# GitHub Actions工作流 - 代码质量检查
# 2025年最新最佳实践，适用于Axum企业级项目

name: 代码质量检查

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天UTC时间02:00运行（北京时间10:00）
    - cron: '0 2 * * *'

env:
  CARGO_TERM_COLOR: always
  RUSTFLAGS: "-Dwarnings"

jobs:
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        rust-version: [stable, beta]
        
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 安装Rust工具链
      uses: dtolnay/rust-toolchain@master
      with:
        toolchain: ${{ matrix.rust-version }}
        components: rustfmt, clippy
        
    - name: 配置Rust缓存
      uses: Swatinem/rust-cache@v2
      with:
        key: ${{ matrix.rust-version }}
        
    - name: 安装代码质量工具
      run: |
        cargo install cargo-deny --locked
        cargo install cargo-audit --locked
        cargo install cargo-outdated --locked
        cargo install cargo-machete --locked
        
    - name: 检查Cargo.toml格式
      run: |
        if ! command -v taplo &> /dev/null; then
          cargo install taplo-cli --locked
        fi
        taplo format --check
        
    - name: 编译检查
      run: cargo check --all-targets --all-features --workspace
      
    - name: 代码格式化检查
      run: cargo fmt --all -- --check
      
    - name: Clippy静态分析（严格模式）
      run: |
        cargo clippy --all-targets --all-features --workspace -- \
          -D warnings \
          -D clippy::all \
          -D clippy::pedantic \
          -W clippy::nursery \
          -D clippy::cognitive_complexity \
          -D clippy::too_many_arguments \
          -D clippy::type_complexity \
          -D clippy::unwrap_used \
          -D clippy::expect_used \
          -D clippy::panic
          
    - name: 安全漏洞检查
      run: cargo deny check --all-features
      
    - name: 漏洞审计
      run: cargo audit --deny warnings
      
    - name: 检查未使用的依赖
      run: cargo machete
      continue-on-error: true
      
    - name: 检查过时的依赖
      run: cargo outdated --workspace
      continue-on-error: true
      
    - name: 单元测试
      run: cargo test --workspace --lib
      
    - name: 集成测试
      run: cargo test --workspace --test '*'
      continue-on-error: true
      
    - name: 文档测试
      run: cargo test --workspace --doc
      continue-on-error: true
      
    - name: 生成文档
      run: cargo doc --workspace --all-features --no-deps
      
    - name: 检查文档链接
      run: |
        cargo install cargo-deadlinks --locked
        cargo deadlinks --check-http
      continue-on-error: true

  security-audit:
    name: 安全审计
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 安装Rust工具链
      uses: dtolnay/rust-toolchain@stable
      
    - name: 配置Rust缓存
      uses: Swatinem/rust-cache@v2
      
    - name: 安装安全工具
      run: |
        cargo install cargo-audit --locked
        cargo install cargo-deny --locked
        
    - name: 更新安全数据库
      run: cargo audit --update
      
    - name: 安全漏洞扫描
      run: cargo audit --deny warnings
      
    - name: 许可证检查
      run: cargo deny check licenses
      
    - name: 依赖源检查
      run: cargo deny check sources
      
    - name: 禁用依赖检查
      run: cargo deny check bans

  performance-check:
    name: 性能检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 安装Rust工具链
      uses: dtolnay/rust-toolchain@stable
      
    - name: 配置Rust缓存
      uses: Swatinem/rust-cache@v2
      
    - name: 安装性能工具
      run: |
        cargo install cargo-bloat --locked
        cargo install cargo-udeps --locked
        
    - name: 检查二进制大小
      run: cargo bloat --release --crates
      continue-on-error: true
      
    - name: 检查未使用的依赖
      run: cargo +nightly udeps --all-targets
      continue-on-error: true
      
    - name: 编译时间分析
      run: |
        cargo clean
        cargo build --timings --release
      continue-on-error: true

  code-coverage:
    name: 代码覆盖率
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      
    - name: 安装Rust工具链
      uses: dtolnay/rust-toolchain@stable
      components: llvm-tools-preview
      
    - name: 配置Rust缓存
      uses: Swatinem/rust-cache@v2
      
    - name: 安装覆盖率工具
      run: cargo install cargo-llvm-cov --locked
      
    - name: 生成覆盖率报告
      run: |
        cargo llvm-cov --all-features --workspace --lcov --output-path lcov.info
        
    - name: 上传覆盖率到Codecov
      uses: codecov/codecov-action@v3
      with:
        files: lcov.info
        fail_ci_if_error: false
        
    - name: 覆盖率摘要
      run: cargo llvm-cov --all-features --workspace --summary-only

  dependency-review:
    name: 依赖审查
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    
    steps:
    - name: 依赖审查
      uses: actions/dependency-review-action@v3
      with:
        fail-on-severity: moderate
        
  notify-on-failure:
    name: 失败通知
    runs-on: ubuntu-latest
    needs: [code-quality, security-audit, performance-check, code-coverage]
    if: failure()
    
    steps:
    - name: 发送失败通知
      run: |
        echo "🚨 代码质量检查失败！"
        echo "请检查以上步骤的错误信息并修复问题。"
        echo "📋 常见问题解决方案："
        echo "1. 运行 'cargo fmt --all' 修复格式问题"
        echo "2. 运行 'cargo clippy --fix' 修复Clippy警告"
        echo "3. 检查 deny.toml 配置解决安全问题"
        echo "4. 更新依赖版本解决漏洞问题"
