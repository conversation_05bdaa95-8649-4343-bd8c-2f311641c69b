# Axum企业级后端数据库问题排查与解决方案 PRD

## 项目概述
本项目旨在系统性地排查和解决Axum企业级后端应用中的数据库操作失败问题，确保用户注册API的稳定性和可靠性。项目采用模块化领域驱动设计(Modular DDD)结合整洁架构的企业级架构模式。

## 问题背景
- **技术栈**: Axum 0.8.4 + Tokio 1.45.1 + SeaORM + PostgreSQL 17 + DragonflyDB
- **架构**: 模块化领域驱动设计(Modular DDD) + 整洁架构
- **环境**: Windows 10本机 + WSL2容器化部署
- **版本**: Rust 2024 Edition

## 核心问题
用户注册API调用时返回500错误，前端显示"数据库操作失败"。需要进行系统性排查和解决。

## 排查目标
1. **数据库连接状态验证** - 确保PostgreSQL容器正常运行
2. **SeaORM配置检查** - 验证数据库连接字符串和配置
3. **数据库迁移状态** - 确认表结构已正确创建
4. **权限问题排查** - 验证数据库用户权限
5. **网络连接测试** - 确保容器间网络通信正常
6. **日志分析** - 详细分析后端错误日志
7. **API功能验证** - 确保用户注册流程完整工作

## 技术要求
- 使用WSL2 + Podman容器管理PostgreSQL和DragonflyDB
- 遵循rust_axum_Rules.md编码规范
- 保持API向后兼容性
- 确保每步可编译：`cargo check --workspace`
- 遵循DRY、SOLID原则、TDD开发模式

## 成功指标
1. **用户注册API正常工作** - 返回201状态码和正确的JWT令牌
2. **数据库连接稳定** - 连接池健康检查通过
3. **数据持久化正确** - 用户数据正确保存到PostgreSQL
4. **错误处理完善** - 重复用户名返回409冲突错误
5. **日志记录完整** - 所有操作都有详细的中文日志记录
6. **容器环境稳定** - PostgreSQL和DragonflyDB容器正常运行

## 排查步骤详细说明

### 第一阶段：环境状态检查
1. **容器状态验证**
   - 检查PostgreSQL 17容器运行状态
   - 检查DragonflyDB容器运行状态
   - 验证容器健康检查状态

2. **数据库连接测试**
   - 测试从容器内部连接PostgreSQL
   - 验证数据库用户权限
   - 检查数据库表结构完整性

3. **迁移状态检查**
   - 运行`cargo run -p migration -- status`
   - 验证所有迁移已正确应用
   - 检查表结构与迁移文件一致性

### 第二阶段：应用层排查
1. **服务器启动测试**
   - 使用正确的启动命令`cargo run -p axum-server`
   - 检查数据库连接池初始化
   - 验证依赖注入容器配置

2. **API端点测试**
   - 测试用户注册API功能
   - 验证请求/响应格式
   - 检查错误处理机制

3. **日志分析**
   - 分析详细的服务器日志
   - 跟踪请求处理流程
   - 识别具体错误点

### 第三阶段：问题解决验证
1. **功能验证**
   - 成功注册新用户
   - 验证数据库数据持久化
   - 测试重复用户名错误处理

2. **性能验证**
   - 检查连接池配置
   - 验证数据库查询性能
   - 确认无内存泄漏

## 预期结果
通过系统性的排查和解决，确保：
- 用户注册API完全正常工作
- 数据库连接稳定可靠
- 错误处理机制完善
- 日志记录详细准确
- 系统架构保持整洁和可维护性
