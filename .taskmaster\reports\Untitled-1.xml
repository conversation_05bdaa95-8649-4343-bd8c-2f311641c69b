<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2298.21484375 2009" style="max-width: 2298.21484375px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15"><style>#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .error-icon{fill:#a44141;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edge-thickness-normal{stroke-width:1px;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .marker.cross{stroke:lightgrey;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 p{margin:0;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .cluster-label text{fill:#F9FFFE;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .cluster-label span{color:#F9FFFE;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .cluster-label span p{background-color:transparent;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .label text,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 span{fill:#ccc;color:#ccc;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .node rect,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .node circle,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .node ellipse,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .node polygon,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .rough-node .label text,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .node .label text,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .image-shape .label,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .icon-shape .label{text-anchor:middle;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .rough-node .label,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .node .label,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .image-shape .label,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .icon-shape .label{text-align:center;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .node.clickable{cursor:pointer;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .arrowheadPath{fill:lightgrey;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .cluster text{fill:#F9FFFE;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .cluster span{color:#F9FFFE;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 rect.text{fill:none;stroke-width:0;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .icon-shape,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .icon-shape p,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .icon-shape rect,#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .devPhase&gt;*{fill:#e3f2fd!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .devPhase span{fill:#e3f2fd!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .techStack&gt;*{fill:#f1f8e9!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .techStack span{fill:#f1f8e9!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .architecture&gt;*{fill:#fce4ec!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .architecture span{fill:#fce4ec!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .coreModule&gt;*{fill:#fff3e0!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .coreModule span{fill:#fff3e0!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .testSuite&gt;*{fill:#e8f5e8!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .testSuite span{fill:#e8f5e8!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .deployment&gt;*{fill:#f3e5f5!important;}#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15 .deployment span{fill:#f3e5f5!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="部署运维体系" class="cluster"><rect height="724" width="232.37109375" y="915" x="8" style=""></rect><g transform="translate(76.185546875, 915)" class="cluster-label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>部署运维体系</p></span></div></foreignObject></g></g><g data-look="classic" id="测试验证体系" class="cluster"><rect height="724" width="210" y="1044" x="1837.29296875" style=""></rect><g transform="translate(1894.29296875, 1044)" class="cluster-label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>测试验证体系</p></span></div></foreignObject></g></g><g data-look="classic" id="核心功能模块" class="cluster"><rect height="724" width="194" y="786" x="534.27734375" style=""></rect><g transform="translate(583.27734375, 786)" class="cluster-label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>核心功能模块</p></span></div></foreignObject></g></g><g data-look="classic" id="架构设计层" class="cluster"><rect height="595" width="222.921875" y="1406" x="2067.29296875" style=""></rect><g transform="translate(2138.75390625, 1406)" class="cluster-label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>架构设计层</p></span></div></foreignObject></g></g><g data-look="classic" id="核心技术栈" class="cluster"><rect height="803" width="253.90625" y="345" x="260.37109375" style=""></rect><g transform="translate(347.32421875, 345)" class="cluster-label"><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>核心技术栈</p></span></div></foreignObject></g></g><g data-look="classic" id="开发实施阶段" class="cluster"><rect height="1502" width="1069.015625" y="8" x="748.27734375" style=""></rect><g transform="translate(1234.78515625, 8)" class="cluster-label"><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>开发实施阶段</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M1219.086,87L1225.2,91.167C1231.313,95.333,1243.539,103.667,1249.652,111.333C1255.766,119,1255.766,126,1255.766,129.5L1255.766,133"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C_1" d="M1163.305,187.171L1144.132,191.976C1124.958,196.781,1086.612,206.39,1067.439,214.695C1048.266,223,1048.266,230,1048.266,233.5L1048.266,237"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_D_2" d="M1084.766,295L1090.399,299.167C1096.031,303.333,1107.297,311.667,1112.93,320C1118.563,328.333,1118.563,336.667,1118.563,344.333C1118.563,352,1118.563,359,1118.563,362.5L1118.563,366"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_E_3" d="M1149.976,424L1154.824,428.167C1159.671,432.333,1169.367,440.667,1174.215,448.333C1179.063,456,1179.063,463,1179.063,466.5L1179.063,470"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_F_4" d="M1210.476,528L1215.324,532.167C1220.171,536.333,1229.867,544.667,1234.715,552.333C1239.563,560,1239.563,567,1239.563,570.5L1239.563,574"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_G_5" d="M1208.149,632L1203.301,636.167C1198.454,640.333,1188.758,648.667,1183.91,656.333C1179.063,664,1179.063,671,1179.063,674.5L1179.063,678"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_H_6" d="M1265.063,724.185L1299.813,730.321C1334.563,736.457,1404.063,748.728,1438.813,759.031C1473.563,769.333,1473.563,777.667,1473.563,785.333C1473.563,793,1473.563,800,1473.563,803.5L1473.563,807"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_I_7" d="M1444.226,865L1439.699,869.167C1435.171,873.333,1426.117,881.667,1421.59,890C1417.063,898.333,1417.063,906.667,1417.063,914.333C1417.063,922,1417.063,929,1417.063,932.5L1417.063,936"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_J_8" d="M1495.063,982.481L1525.729,988.567C1556.396,994.654,1617.729,1006.827,1648.396,1017.08C1679.063,1027.333,1679.063,1035.667,1679.063,1043.333C1679.063,1051,1679.063,1058,1679.063,1061.5L1679.063,1065"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_K_9" d="M1673.87,1123L1673.069,1127.167C1672.268,1131.333,1670.665,1139.667,1669.864,1148C1669.063,1156.333,1669.063,1164.667,1669.063,1172.333C1669.063,1180,1669.063,1187,1669.063,1190.5L1669.063,1194"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_L_10" d="M1663.87,1252L1663.069,1256.167C1662.268,1260.333,1660.665,1268.667,1659.864,1276.333C1659.063,1284,1659.063,1291,1659.063,1294.5L1659.063,1298"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_M_11" d="M1630.764,1356L1626.397,1360.167C1622.03,1364.333,1613.296,1372.667,1608.929,1381C1604.563,1389.333,1604.563,1397.667,1604.563,1405.333C1604.563,1413,1604.563,1420,1604.563,1423.5L1604.563,1427"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_N_12" d="M1109.082,69.933L1059.397,76.944C1009.712,83.955,910.342,97.978,860.658,113.656C810.973,129.333,810.973,146.667,810.973,164C810.973,181.333,810.973,198.667,810.973,216C810.973,233.333,810.973,250.667,810.973,268C810.973,285.333,810.973,302.667,810.973,315.5C810.973,328.333,810.973,336.667,756.352,347.538C701.731,358.409,592.489,371.817,537.868,378.522L483.248,385.226"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_FF_13" d="M1348.227,173.856L1414.116,180.88C1480.005,187.904,1611.784,201.952,1677.673,217.643C1743.563,233.333,1743.563,250.667,1743.563,268C1743.563,285.333,1743.563,302.667,1743.563,315.5C1743.563,328.333,1743.563,336.667,1743.563,349.5C1743.563,362.333,1743.563,379.667,1743.563,397C1743.563,414.333,1743.563,431.667,1743.563,449C1743.563,466.333,1743.563,483.667,1743.563,501C1743.563,518.333,1743.563,535.667,1743.563,553C1743.563,570.333,1743.563,587.667,1743.563,605C1743.563,622.333,1743.563,639.667,1743.563,657C1743.563,674.333,1743.563,691.667,1743.563,709C1743.563,726.333,1743.563,743.667,1743.563,756.5C1743.563,769.333,1743.563,777.667,1743.563,790.5C1743.563,803.333,1743.563,820.667,1743.563,838C1743.563,855.333,1743.563,872.667,1743.563,885.5C1743.563,898.333,1743.563,906.667,1743.563,919.5C1743.563,932.333,1743.563,949.667,1743.563,967C1743.563,984.333,1743.563,1001.667,1743.563,1014.5C1743.563,1027.333,1743.563,1035.667,1765.706,1045.627C1787.849,1055.588,1832.136,1067.176,1854.28,1072.97L1876.423,1078.764"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C_R_14" d="M969.862,295L957.762,299.167C945.663,303.333,921.464,311.667,909.365,320C897.266,328.333,897.266,336.667,897.266,349.5C897.266,362.333,897.266,379.667,897.266,397C897.266,414.333,897.266,431.667,897.266,449C897.266,466.333,897.266,483.667,897.266,501C897.266,518.333,897.266,535.667,897.266,553C897.266,570.333,897.266,587.667,897.266,605C897.266,622.333,897.266,639.667,897.266,657C897.266,674.333,897.266,691.667,897.266,709C897.266,726.333,897.266,743.667,897.266,756.5C897.266,769.333,897.266,777.667,826.286,789.071C755.307,800.476,613.348,814.952,542.369,822.19L471.39,829.428"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_S_15" d="M1050.543,424L1040.046,428.167C1029.55,432.333,1008.556,440.667,998.059,453.5C987.563,466.333,987.563,483.667,987.563,501C987.563,518.333,987.563,535.667,987.563,553C987.563,570.333,987.563,587.667,987.563,605C987.563,622.333,987.563,639.667,987.563,657C987.563,674.333,987.563,691.667,987.563,709C987.563,726.333,987.563,743.667,987.563,756.5C987.563,769.333,987.563,777.667,987.563,790.5C987.563,803.333,987.563,820.667,987.563,838C987.563,855.333,987.563,872.667,987.563,885.5C987.563,898.333,987.563,906.667,900.453,918.38C813.343,930.093,639.123,945.186,552.013,952.733L464.903,960.279"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_AA_16" d="M1116.236,528L1106.54,532.167C1096.845,536.333,1077.454,544.667,1067.758,557.5C1058.063,570.333,1058.063,587.667,1058.063,605C1058.063,622.333,1058.063,639.667,1058.063,657C1058.063,674.333,1058.063,691.667,1058.063,709C1058.063,726.333,1058.063,743.667,1058.063,756.5C1058.063,769.333,1058.063,777.667,1058.063,790.5C1058.063,803.333,1058.063,820.667,1058.063,838C1058.063,855.333,1058.063,872.667,1058.063,885.5C1058.063,898.333,1058.063,906.667,997.927,918.16C937.791,929.654,817.519,944.308,757.384,951.635L697.248,958.962"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_BB_17" d="M1270.976,632L1275.824,636.167C1280.671,640.333,1290.367,648.667,1295.215,661.5C1300.063,674.333,1300.063,691.667,1300.063,709C1300.063,726.333,1300.063,743.667,1300.063,756.5C1300.063,769.333,1300.063,777.667,1300.063,790.5C1300.063,803.333,1300.063,820.667,1300.063,838C1300.063,855.333,1300.063,872.667,1300.063,885.5C1300.063,898.333,1300.063,906.667,1300.063,919.5C1300.063,932.333,1300.063,949.667,1300.063,967C1300.063,984.333,1300.063,1001.667,1300.063,1014.5C1300.063,1027.333,1300.063,1035.667,1199.596,1047.645C1099.13,1059.623,898.198,1075.246,797.732,1083.058L697.265,1090.869"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_G_Z_18" d="M1093.063,730.144L1072.146,735.287C1051.229,740.429,1009.396,750.715,988.479,760.024C967.563,769.333,967.563,777.667,922.507,788.8C877.452,799.934,787.341,813.868,742.286,820.835L697.23,827.802"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_EE_19" d="M1502.899,865L1507.426,869.167C1511.954,873.333,1521.008,881.667,1525.535,890C1530.063,898.333,1530.063,906.667,1530.063,919.5C1530.063,932.333,1530.063,949.667,1530.063,967C1530.063,984.333,1530.063,1001.667,1530.063,1014.5C1530.063,1027.333,1530.063,1035.667,1530.063,1048.5C1530.063,1061.333,1530.063,1078.667,1530.063,1096C1530.063,1113.333,1530.063,1130.667,1530.063,1143.5C1530.063,1156.333,1530.063,1164.667,1530.063,1177.5C1530.063,1190.333,1530.063,1207.667,1530.063,1225C1530.063,1242.333,1530.063,1259.667,1530.063,1277C1530.063,1294.333,1530.063,1311.667,1530.063,1329C1530.063,1346.333,1530.063,1363.667,1530.063,1376.5C1530.063,1389.333,1530.063,1397.667,1391.264,1409.864C1252.465,1422.061,974.868,1438.121,836.069,1446.152L697.271,1454.182"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_T_20" d="M1339.063,981.083L1304.063,987.403C1269.063,993.722,1199.063,1006.361,1164.063,1016.847C1129.063,1027.333,1129.063,1035.667,1018.106,1047.612C907.149,1059.557,685.236,1075.115,574.279,1082.893L463.322,1090.672"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_J_GG_21" d="M1707.361,1123L1711.728,1127.167C1716.095,1131.333,1724.829,1139.667,1729.196,1148C1733.563,1156.333,1733.563,1164.667,1757.371,1174.765C1781.179,1184.862,1828.795,1196.725,1852.603,1202.656L1876.412,1208.587"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_K_HH_22" d="M1697.361,1252L1701.728,1256.167C1706.095,1260.333,1714.829,1268.667,1743.335,1278.572C1771.842,1288.478,1820.122,1299.956,1844.262,1305.694L1868.401,1311.433"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_U_23" d="M1687.361,1356L1691.728,1360.167C1696.095,1364.333,1704.829,1372.667,1709.196,1381C1713.563,1389.333,1713.563,1397.667,1778.979,1409.146C1844.395,1420.625,1975.227,1435.249,2040.644,1442.562L2106.06,1449.874"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_QQ_24" d="M1604.563,1485L1604.563,1489.167C1604.563,1493.333,1604.563,1501.667,1367.447,1510C1130.332,1518.333,656.102,1526.667,415.596,1534.51C175.09,1542.353,168.308,1549.706,164.917,1553.383L161.527,1557.06"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_O_25" d="M387.324,424L387.324,428.167C387.324,432.333,387.324,440.667,387.324,448.333C387.324,456,387.324,463,387.324,466.5L387.324,470"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_P_26" d="M387.324,528L387.324,532.167C387.324,536.333,387.324,544.667,387.324,552.333C387.324,560,387.324,567,387.324,570.5L387.324,574"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_P_Q_27" d="M387.324,632L387.324,636.167C387.324,640.333,387.324,648.667,387.324,656.333C387.324,664,387.324,671,387.324,674.5L387.324,678"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_R_28" d="M387.324,736L387.324,740.167C387.324,744.333,387.324,752.667,387.324,761C387.324,769.333,387.324,777.667,387.324,785.333C387.324,793,387.324,800,387.324,803.5L387.324,807"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_R_S_29" d="M387.324,865L387.324,869.167C387.324,873.333,387.324,881.667,387.324,890C387.324,898.333,387.324,906.667,387.324,914.333C387.324,922,387.324,929,387.324,932.5L387.324,936"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_T_30" d="M387.324,994L387.324,998.167C387.324,1002.333,387.324,1010.667,387.324,1019C387.324,1027.333,387.324,1035.667,387.324,1043.333C387.324,1051,387.324,1058,387.324,1061.5L387.324,1065"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_U_V_31" d="M2178.754,1485L2178.754,1489.167C2178.754,1493.333,2178.754,1501.667,2178.754,1510C2178.754,1518.333,2178.754,1526.667,2178.754,1534.333C2178.754,1542,2178.754,1549,2178.754,1552.5L2178.754,1556"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_W_32" d="M2178.754,1614L2178.754,1618.167C2178.754,1622.333,2178.754,1630.667,2178.754,1639C2178.754,1647.333,2178.754,1655.667,2178.754,1663.333C2178.754,1671,2178.754,1678,2178.754,1681.5L2178.754,1685"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_W_X_33" d="M2178.754,1743L2178.754,1747.167C2178.754,1751.333,2178.754,1759.667,2178.754,1768C2178.754,1776.333,2178.754,1784.667,2178.754,1792.333C2178.754,1800,2178.754,1807,2178.754,1810.5L2178.754,1814"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_Y_34" d="M2178.754,1872L2178.754,1876.167C2178.754,1880.333,2178.754,1888.667,2178.754,1896.333C2178.754,1904,2178.754,1911,2178.754,1914.5L2178.754,1918"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_AA_35" d="M631.277,865L631.277,869.167C631.277,873.333,631.277,881.667,631.277,890C631.277,898.333,631.277,906.667,631.277,914.333C631.277,922,631.277,929,631.277,932.5L631.277,936"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AA_BB_36" d="M631.277,994L631.277,998.167C631.277,1002.333,631.277,1010.667,631.277,1019C631.277,1027.333,631.277,1035.667,631.277,1043.333C631.277,1051,631.277,1058,631.277,1061.5L631.277,1065"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_BB_CC_37" d="M631.277,1123L631.277,1127.167C631.277,1131.333,631.277,1139.667,631.277,1148C631.277,1156.333,631.277,1164.667,631.277,1172.333C631.277,1180,631.277,1187,631.277,1190.5L631.277,1194"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CC_DD_38" d="M631.277,1252L631.277,1256.167C631.277,1260.333,631.277,1268.667,631.277,1276.333C631.277,1284,631.277,1291,631.277,1294.5L631.277,1298"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DD_EE_39" d="M631.277,1356L631.277,1360.167C631.277,1364.333,631.277,1372.667,631.277,1381C631.277,1389.333,631.277,1397.667,631.277,1405.333C631.277,1413,631.277,1420,631.277,1423.5L631.277,1427"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_FF_GG_40" d="M1942.293,1123L1942.293,1127.167C1942.293,1131.333,1942.293,1139.667,1942.293,1148C1942.293,1156.333,1942.293,1164.667,1942.293,1172.333C1942.293,1180,1942.293,1187,1942.293,1190.5L1942.293,1194"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_GG_HH_41" d="M1942.293,1252L1942.293,1256.167C1942.293,1260.333,1942.293,1268.667,1942.293,1276.333C1942.293,1284,1942.293,1291,1942.293,1294.5L1942.293,1298"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HH_II_42" d="M1942.293,1356L1942.293,1360.167C1942.293,1364.333,1942.293,1372.667,1942.293,1381C1942.293,1389.333,1942.293,1397.667,1942.293,1405.333C1942.293,1413,1942.293,1420,1942.293,1423.5L1942.293,1427"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_II_JJ_43" d="M1942.293,1485L1942.293,1489.167C1942.293,1493.333,1942.293,1501.667,1942.293,1510C1942.293,1518.333,1942.293,1526.667,1942.293,1534.333C1942.293,1542,1942.293,1549,1942.293,1552.5L1942.293,1556"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_JJ_KK_44" d="M1942.293,1614L1942.293,1618.167C1942.293,1622.333,1942.293,1630.667,1942.293,1639C1942.293,1647.333,1942.293,1655.667,1942.293,1663.333C1942.293,1671,1942.293,1678,1942.293,1681.5L1942.293,1685"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LL_MM_45" d="M123.914,994L123.914,998.167C123.914,1002.333,123.914,1010.667,123.914,1019C123.914,1027.333,123.914,1035.667,123.914,1043.333C123.914,1051,123.914,1058,123.914,1061.5L123.914,1065"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_MM_NN_46" d="M123.914,1123L123.914,1127.167C123.914,1131.333,123.914,1139.667,123.914,1148C123.914,1156.333,123.914,1164.667,123.914,1172.333C123.914,1180,123.914,1187,123.914,1190.5L123.914,1194"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NN_OO_47" d="M123.914,1252L123.914,1256.167C123.914,1260.333,123.914,1268.667,123.914,1276.333C123.914,1284,123.914,1291,123.914,1294.5L123.914,1298"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OO_PP_48" d="M123.914,1356L123.914,1360.167C123.914,1364.333,123.914,1372.667,123.914,1381C123.914,1389.333,123.914,1397.667,123.914,1405.333C123.914,1413,123.914,1420,123.914,1423.5L123.914,1427"></path><path marker-end="url(#mermaid-e075523a-cac1-4fa7-8a26-131e629a4c15_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PP_QQ_49" d="M123.914,1485L123.914,1489.167C123.914,1493.333,123.914,1501.667,123.914,1510C123.914,1518.333,123.914,1526.667,124.589,1534.345C125.265,1542.024,126.616,1549.048,127.291,1552.56L127.966,1556.072"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1179.47265625, 60)" id="flowchart-A-126" class="node default devPhase"><rect height="54" width="140.78125" y="-27" x="-70.390625" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-40.390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80.78125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>任务52启动</p></span></div></foreignObject></g></g><g transform="translate(1255.765625, 164)" id="flowchart-B-127" class="node default devPhase"><rect height="54" width="184.921875" y="-27" x="-92.4609375" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-62.4609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="124.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TDD测试框架搭建</p></span></div></foreignObject></g></g><g transform="translate(1048.265625, 268)" id="flowchart-C-129" class="node default devPhase"><rect height="54" width="235.171875" y="-27" x="-117.5859375" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-87.5859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="175.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL全文搜索优化</p></span></div></foreignObject></g></g><g transform="translate(1118.5625, 397)" id="flowchart-D-131" class="node default devPhase"><rect height="54" width="211.1875" y="-27" x="-105.59375" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-75.59375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="151.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>DragonflyDB多级缓存</p></span></div></foreignObject></g></g><g transform="translate(1179.0625, 501)" id="flowchart-E-133" class="node default devPhase"><rect height="54" width="172" y="-27" x="-86" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>缓存防雪崩机制</p></span></div></foreignObject></g></g><g transform="translate(1239.5625, 605)" id="flowchart-F-135" class="node default devPhase"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>异步搜索队列</p></span></div></foreignObject></g></g><g transform="translate(1179.0625, 709)" id="flowchart-G-137" class="node default devPhase"><rect height="54" width="172" y="-27" x="-86" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>搜索结果预计算</p></span></div></foreignObject></g></g><g transform="translate(1473.5625, 838)" id="flowchart-H-139" class="node default devPhase"><rect height="54" width="172" y="-27" x="-86" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-56, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="112"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>熔断器和限流器</p></span></div></foreignObject></g></g><g transform="translate(1417.0625, 967)" id="flowchart-I-141" class="node default devPhase"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>性能监控模块</p></span></div></foreignObject></g></g><g transform="translate(1679.0625, 1096)" id="flowchart-J-143" class="node default devPhase"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>单元测试实现</p></span></div></foreignObject></g></g><g transform="translate(1669.0625, 1225)" id="flowchart-K-145" class="node default devPhase"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>集成测试实现</p></span></div></foreignObject></g></g><g transform="translate(1659.0625, 1329)" id="flowchart-L-147" class="node default devPhase"><rect height="54" width="188" y="-27" x="-94" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-64, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="128"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>代码可扩展性优化</p></span></div></foreignObject></g></g><g transform="translate(1604.5625, 1458)" id="flowchart-M-149" class="node default devPhase"><rect height="54" width="156" y="-27" x="-78" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>完整技术文档</p></span></div></foreignObject></g></g><g transform="translate(387.32421875, 397)" id="flowchart-N-150" class="node default techStack"><rect height="54" width="183.90625" y="-27" x="-91.953125" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-61.953125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="123.90625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Rust 2024 Edition</p></span></div></foreignObject></g></g><g transform="translate(387.32421875, 501)" id="flowchart-O-151" class="node default techStack"><rect height="54" width="141.21875" y="-27" x="-70.609375" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-40.609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="81.21875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Axum 0.8.4</p></span></div></foreignObject></g></g><g transform="translate(387.32421875, 605)" id="flowchart-P-152" class="node default techStack"><rect height="54" width="147.234375" y="-27" x="-73.6171875" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-43.6171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="87.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Tokio 1.45.1</p></span></div></foreignObject></g></g><g transform="translate(387.32421875, 709)" id="flowchart-Q-153" class="node default techStack"><rect height="54" width="166.40625" y="-27" x="-83.203125" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-53.203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="106.40625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>SeaORM 1.1.12</p></span></div></foreignObject></g></g><g transform="translate(387.32421875, 838)" id="flowchart-R-154" class="node default techStack"><rect height="54" width="160.171875" y="-27" x="-80.0859375" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-50.0859375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="100.171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL 17</p></span></div></foreignObject></g></g><g transform="translate(387.32421875, 967)" id="flowchart-S-155" class="node default techStack"><rect height="54" width="147.1875" y="-27" x="-73.59375" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-43.59375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="87.1875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>DragonflyDB</p></span></div></foreignObject></g></g><g transform="translate(387.32421875, 1096)" id="flowchart-T-156" class="node default techStack"><rect height="54" width="144.015625" y="-27" x="-72.0078125" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-42.0078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="84.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Prometheus</p></span></div></foreignObject></g></g><g transform="translate(2178.75390625, 1458)" id="flowchart-U-157" class="node default architecture"><rect height="54" width="137.4375" y="-27" x="-68.71875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-38.71875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="77.4375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>模块化DDD</p></span></div></foreignObject></g></g><g transform="translate(2178.75390625, 1587)" id="flowchart-V-158" class="node default architecture"><rect height="54" width="124" y="-27" x="-62" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>整洁架构</p></span></div></foreignObject></g></g><g transform="translate(2178.75390625, 1716)" id="flowchart-W-159" class="node default architecture"><rect height="54" width="152.921875" y="-27" x="-76.4609375" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-46.4609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="92.921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>TDD开发模式</p></span></div></foreignObject></g></g><g transform="translate(2178.75390625, 1845)" id="flowchart-X-160" class="node default architecture"><rect height="54" width="140" y="-27" x="-70" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>微服务准备</p></span></div></foreignObject></g></g><g transform="translate(2178.75390625, 1949)" id="flowchart-Y-161" class="node default architecture"><rect height="54" width="140" y="-27" x="-70" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>容器化部署</p></span></div></foreignObject></g></g><g transform="translate(631.27734375, 838)" id="flowchart-Z-162" class="node default coreModule"><rect height="54" width="124" y="-27" x="-62" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>搜索服务</p></span></div></foreignObject></g></g><g transform="translate(631.27734375, 967)" id="flowchart-AA-163" class="node default coreModule"><rect height="54" width="124" y="-27" x="-62" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>缓存服务</p></span></div></foreignObject></g></g><g transform="translate(631.27734375, 1096)" id="flowchart-BB-164" class="node default coreModule"><rect height="54" width="124" y="-27" x="-62" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>队列服务</p></span></div></foreignObject></g></g><g transform="translate(631.27734375, 1225)" id="flowchart-CC-165" class="node default coreModule"><rect height="54" width="124" y="-27" x="-62" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>监控服务</p></span></div></foreignObject></g></g><g transform="translate(631.27734375, 1329)" id="flowchart-DD-166" class="node default coreModule"><rect height="54" width="124" y="-27" x="-62" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>认证服务</p></span></div></foreignObject></g></g><g transform="translate(631.27734375, 1458)" id="flowchart-EE-167" class="node default coreModule"><rect height="54" width="124" y="-27" x="-62" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>限流服务</p></span></div></foreignObject></g></g><g transform="translate(1942.29296875, 1096)" id="flowchart-FF-168" class="node default testSuite"><rect height="54" width="124" y="-27" x="-62" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>单元测试</p></span></div></foreignObject></g></g><g transform="translate(1942.29296875, 1225)" id="flowchart-GG-169" class="node default testSuite"><rect height="54" width="124" y="-27" x="-62" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>集成测试</p></span></div></foreignObject></g></g><g transform="translate(1942.29296875, 1329)" id="flowchart-HH-170" class="node default testSuite"><rect height="54" width="140" y="-27" x="-70" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>端到端测试</p></span></div></foreignObject></g></g><g transform="translate(1942.29296875, 1458)" id="flowchart-II-171" class="node default testSuite"><rect height="54" width="124" y="-27" x="-62" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>性能测试</p></span></div></foreignObject></g></g><g transform="translate(1942.29296875, 1587)" id="flowchart-JJ-172" class="node default testSuite"><rect height="54" width="124" y="-27" x="-62" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>压力测试</p></span></div></foreignObject></g></g><g transform="translate(1942.29296875, 1716)" id="flowchart-KK-173" class="node default testSuite"><rect height="54" width="124" y="-27" x="-62" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>安全测试</p></span></div></foreignObject></g></g><g transform="translate(123.9140625, 967)" id="flowchart-LL-174" class="node default deployment"><rect height="54" width="156" y="-27" x="-78" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-48, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="96"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>本地开发环境</p></span></div></foreignObject></g></g><g transform="translate(123.9140625, 1096)" id="flowchart-MM-175" class="node default deployment"><rect height="54" width="161.828125" y="-27" x="-80.9140625" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-50.9140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="101.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>WSL2容器环境</p></span></div></foreignObject></g></g><g transform="translate(123.9140625, 1225)" id="flowchart-NN-176" class="node default deployment"><rect height="54" width="140" y="-27" x="-70" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-40, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="80"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>数据库集群</p></span></div></foreignObject></g></g><g transform="translate(123.9140625, 1329)" id="flowchart-OO-177" class="node default deployment"><rect height="54" width="124" y="-27" x="-62" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>缓存集群</p></span></div></foreignObject></g></g><g transform="translate(123.9140625, 1458)" id="flowchart-PP-178" class="node default deployment"><rect height="54" width="124" y="-27" x="-62" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>监控告警</p></span></div></foreignObject></g></g><g transform="translate(133.9140625, 1587)" id="flowchart-QQ-179" class="node default deployment"><rect height="54" width="124" y="-27" x="-62" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-32, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="64"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>日志收集</p></span></div></foreignObject></g></g></g></g></g></svg>