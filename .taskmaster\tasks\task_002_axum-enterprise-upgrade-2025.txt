# Task ID: 2
# Title: 创建podman-compose.yml配置文件以管理PostgreSQL 17和DragonflyDB容器
# Status: pending
# Dependencies: 1
# Priority: high
# Description: 设计并实现一个podman-compose.yml配置文件，用于编排和管理PostgreSQL 17与DragonflyDB容器化服务。
# Details:
1. 研究PostgreSQL 17和DragonflyDB的容器化部署需求，包括端口映射、持久化存储和环境变量配置。
2. 编写podman-compose.yml文件，定义两个服务：一个用于PostgreSQL 17，另一个用于DragonflyDB，确保服务名称、镜像版本、端口映射、卷挂载和环境变量正确无误。
3. 配置服务之间的依赖关系，确保PostgreSQL在DragonflyDB之前启动（如果存在依赖）。
4. 验证网络设置，确保容器间可以互相通信（如DragonflyDB需要连接PostgreSQL）。
5. 提供启动、停止和重建服务的命令说明，确保配置文件可维护性高。
6. 添加健康检查配置，确保容器健康状态可监控。
7. 提供文档说明，指导团队成员如何使用该配置文件进行部署和调试。

# Test Strategy:
1. 使用`podman-compose up`命令启动服务，验证容器是否成功运行。
2. 检查容器日志，确保没有启动错误或配置问题。
3. 通过访问对应端口（如PostgreSQL的5432、DragonflyDB的6379）验证服务是否正常响应。
4. 测试服务重启、重建和停止功能，确保配置文件具备良好的可操作性。
5. 验证数据持久化是否正常，确保容器重启后数据未丢失。
6. 如果DragonflyDB依赖PostgreSQL，测试服务启动顺序是否符合预期。
7. 使用`podman-compose ps`命令检查服务状态，确保所有服务处于运行状态。
