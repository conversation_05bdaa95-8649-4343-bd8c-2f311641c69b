# Task ID: 10
# Title: 实现HTTP消息发送接口
# Status: pending
# Dependencies: 1, 4, 5, 6, 8
# Priority: medium
# Description: 开发HTTP消息发送功能，包括消息验证、队列管理、重试机制和速率限制，确保消息发送的可靠性和稳定性。
# Details:
1. 设计并实现HTTP消息发送接口的路由和控制器逻辑，支持POST请求发送消息。
2. 实现消息验证逻辑，确保消息内容、目标地址等参数合法（如非空、格式校验、大小限制等）。
3. 集成消息队列机制（如RabbitMQ或Kafka），将消息暂存队列中，确保消息在高并发或失败时不会丢失。
4. 实现重试机制，当消息发送失败时自动重试指定次数（如3次），并支持指数退避策略。
5. 添加速率限制功能，防止滥用或攻击，例如限制每个用户每分钟最多发送100条消息。
6. 在接口中集成JWT权限控制，确保只有授权用户可以发送消息。
7. 在APIClient类中添加对HTTP消息发送接口的封装，保持API请求的统一管理。
8. 记录接口文档，包括请求参数、响应格式、错误码和使用示例。
9. 确保接口与用户认证模块、权限控制系统、消息队列模块协同工作，提供安全可靠的消息发送功能。

# Test Strategy:
1. 使用单元测试验证参数验证逻辑是否正确处理合法和非法输入。
2. 测试消息队列是否正常工作，验证消息是否能正确入队和出队。
3. 模拟消息发送失败场景，验证重试机制是否按预期工作。
4. 测试速率限制功能是否正常工作，验证超过限制时是否返回适当的错误码（如429 Too Many Requests）。
5. 模拟权限不足的请求，验证系统是否正确拒绝访问并返回适当的错误码（如401未授权或403禁止访问）。
6. 进行端到端测试，验证接口是否能正确发送消息并返回预期的响应。
7. 使用Postman或curl测试接口的响应格式和错误处理逻辑。
8. 通过代码审查和静态分析工具确保代码符合最佳实践和项目标准。
