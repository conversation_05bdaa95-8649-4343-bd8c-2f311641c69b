// API性能基准测试 - 任务17实现
// 使用Criterion.rs测量API响应时间和吞吐量，支持SQLite与PostgreSQL+DragonflyDB性能对比

use criterion::{BenchmarkId, Criterion, Throughput, criterion_group, criterion_main};
use reqwest::Client;
use serde_json::json;
use std::collections::HashMap;
use std::hint::black_box;
use std::time::Duration;
use tokio::runtime::Runtime;

/// 基准测试配置常量
const BASE_URL: &str = "http://127.0.0.1:3000";
const TEST_USER_EMAIL: &str = "<EMAIL>";
const TEST_USER_PASSWORD: &str = "password123";

/// 性能基准测试配置
const WARMUP_TIME: Duration = Duration::from_secs(3);
const MEASUREMENT_TIME: Duration = Duration::from_secs(10);
const SAMPLE_SIZE: usize = 100;

/// 测试用户认证信息结构
#[derive(Clone)]
struct TestAuth {
    token: String,
    client: Client,
}

/// 性能测试结果统计
#[derive(Debug, Clone)]
struct PerformanceStats {
    test_name: String,
    avg_response_time_ms: f64,
    min_response_time_ms: f64,
    max_response_time_ms: f64,
    throughput_rps: f64,
    success_rate: f64,
}

/// 初始化测试环境和认证
async fn setup_test_auth() -> Result<TestAuth, Box<dyn std::error::Error>> {
    let client = Client::builder()
        .timeout(Duration::from_secs(30))
        .pool_max_idle_per_host(10)
        .build()?;

    // 用户登录获取JWT token
    let login_response = client
        .post(&format!("{}/api/auth/login", BASE_URL))
        .json(&json!({
            "username": "testuser456",
            "password": TEST_USER_PASSWORD
        }))
        .send()
        .await?;

    if !login_response.status().is_success() {
        return Err(format!("登录失败: {}", login_response.status()).into());
    }

    let login_data: serde_json::Value = login_response.json().await?;
    let token = login_data["data"]["access_token"]
        .as_str()
        .ok_or("无法获取认证token")?
        .to_string();

    Ok(TestAuth { token, client })
}

/// 检查服务器健康状态
async fn check_server_health() -> Result<bool, Box<dyn std::error::Error>> {
    let client = Client::builder().timeout(Duration::from_secs(10)).build()?;

    // 尝试多个端点来确保服务器运行
    let endpoints = vec![
        format!("{}/api/performance/health", BASE_URL),
        format!("{}/metrics", BASE_URL),
        format!("{}/api/health/database", BASE_URL),
    ];

    for endpoint in endpoints {
        match client.get(&endpoint).send().await {
            Ok(response) if response.status().is_success() => {
                println!("✅ 服务器健康检查通过: {}", endpoint);
                return Ok(true);
            }
            Ok(response) => {
                println!(
                    "⚠️ 端点响应异常: {} - 状态码: {}",
                    endpoint,
                    response.status()
                );
            }
            Err(e) => {
                println!("❌ 端点连接失败: {} - 错误: {}", endpoint, e);
            }
        }
    }

    Ok(false)
}

/// 基准测试：GET /api/tasks API响应时间
fn benchmark_get_tasks_api(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    // 检查服务器健康状态
    rt.block_on(async {
        if !check_server_health().await.unwrap_or(false) {
            panic!("服务器未运行或不健康，请先启动服务器");
        }
    });

    // 设置测试环境
    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("API响应时间测试");

    // 配置基准测试参数
    group.sample_size(SAMPLE_SIZE);
    group.measurement_time(MEASUREMENT_TIME);
    group.warm_up_time(WARMUP_TIME);

    group.bench_function("GET /api/tasks", |b| {
        b.iter(|| {
            rt.block_on(async {
                let response = auth
                    .client
                    .get(&format!("{}/api/tasks", BASE_URL))
                    .header("Authorization", format!("Bearer {}", auth.token))
                    .send()
                    .await
                    .expect("API请求失败");

                black_box(response.status().is_success());
            })
        });
    });

    group.finish();
}

/// 基准测试：POST /api/tasks API响应时间
fn benchmark_post_tasks_api(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("任务创建API性能");

    group.sample_size(50);
    group.measurement_time(Duration::from_secs(8));

    let mut counter = 0;

    group.bench_function("POST /api/tasks", |b| {
        b.iter(|| {
            counter += 1;
            let task_data = json!({
                "title": format!("基准测试任务 {}", counter),
                "description": "这是一个用于性能基准测试的任务",
                "priority": "medium"
            });

            rt.block_on(async {
                let response = auth
                    .client
                    .post(&format!("{}/api/tasks", BASE_URL))
                    .header("Authorization", format!("Bearer {}", auth.token))
                    .json(&task_data)
                    .send()
                    .await
                    .expect("创建任务API请求失败");

                black_box(response.status().is_success());
            })
        });
    });

    group.finish();
}

/// 基准测试：用户认证API响应时间
fn benchmark_auth_api(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let client = Client::new();

    let mut group = c.benchmark_group("用户认证API性能");

    group.sample_size(30);
    group.measurement_time(Duration::from_secs(6));

    group.bench_function("POST /api/auth/login", |b| {
        b.iter(|| {
            rt.block_on(async {
                let response = client
                    .post(&format!("{}/api/auth/login", BASE_URL))
                    .json(&json!({
                        "email": TEST_USER_EMAIL,
                        "password": TEST_USER_PASSWORD
                    }))
                    .send()
                    .await
                    .expect("登录API请求失败");

                black_box(response.status().is_success());
            })
        });
    });

    group.finish();
}

/// 基准测试：API吞吐量测试（并发请求）
fn benchmark_api_throughput(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("API吞吐量测试");

    // 测试不同并发级别的吞吐量
    for concurrent_requests in [1, 5, 10, 20].iter() {
        group.throughput(Throughput::Elements(*concurrent_requests as u64));

        group.bench_with_input(
            BenchmarkId::new("并发GET请求", concurrent_requests),
            concurrent_requests,
            |b, &concurrent_requests| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();

                        for _ in 0..concurrent_requests {
                            let client = auth.client.clone();
                            let token = auth.token.clone();

                            let handle = tokio::spawn(async move {
                                client
                                    .get(&format!("{}/api/tasks", BASE_URL))
                                    .header("Authorization", format!("Bearer {}", token))
                                    .send()
                                    .await
                                    .expect("并发API请求失败")
                            });

                            handles.push(handle);
                        }

                        // 等待所有请求完成
                        for handle in handles {
                            let response = handle.await.expect("任务执行失败");
                            black_box(response.status().is_success());
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：高并发负载测试（任务11.4专用）
fn benchmark_concurrent_load_stress(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("高并发负载压力测试");

    // 配置更严格的基准测试参数以模拟真实负载
    group.sample_size(20);
    group.measurement_time(Duration::from_secs(15));
    group.warm_up_time(Duration::from_secs(5));

    // 测试极高并发级别：50, 100, 200, 500个并发请求
    for concurrent_requests in [50, 100, 200, 500].iter() {
        group.throughput(Throughput::Elements(*concurrent_requests as u64));

        group.bench_with_input(
            BenchmarkId::new("极高并发负载", concurrent_requests),
            concurrent_requests,
            |b, &concurrent_requests| {
                b.iter(|| {
                    rt.block_on(async {
                        let mut handles = Vec::new();

                        // 创建信号量来控制并发数量，避免资源耗尽
                        let semaphore =
                            std::sync::Arc::new(tokio::sync::Semaphore::new(concurrent_requests));

                        for i in 0..concurrent_requests {
                            let client = auth.client.clone();
                            let token = auth.token.clone();
                            let semaphore = semaphore.clone();

                            let handle = tokio::spawn(async move {
                                let _permit = semaphore.acquire().await.expect("获取信号量失败");

                                // 混合不同类型的API请求以模拟真实场景
                                let response = if i % 3 == 0 {
                                    // GET 请求
                                    client
                                        .get(&format!("{}/api/tasks", BASE_URL))
                                        .header("Authorization", format!("Bearer {}", token))
                                        .send()
                                        .await
                                } else if i % 3 == 1 {
                                    // POST 请求
                                    client
                                        .post(&format!("{}/api/tasks", BASE_URL))
                                        .header("Authorization", format!("Bearer {}", token))
                                        .json(&json!({
                                            "title": format!("负载测试任务 {}", i),
                                            "description": "高并发负载测试生成的任务",
                                            "priority": "low"
                                        }))
                                        .send()
                                        .await
                                } else {
                                    // 认证请求
                                    client
                                        .post(&format!("{}/api/auth/login", BASE_URL))
                                        .json(&json!({
                                            "email": TEST_USER_EMAIL,
                                            "password": TEST_USER_PASSWORD
                                        }))
                                        .send()
                                        .await
                                };

                                response.expect("高并发API请求失败")
                            });

                            handles.push(handle);
                        }

                        // 等待所有请求完成并收集结果
                        let mut success_count = 0;
                        let mut error_count = 0;

                        for handle in handles {
                            match handle.await {
                                Ok(response) => {
                                    if response.status().is_success() {
                                        success_count += 1;
                                    } else {
                                        error_count += 1;
                                    }
                                }
                                Err(_) => {
                                    error_count += 1;
                                }
                            }
                        }

                        // 使用black_box防止编译器优化
                        black_box((success_count, error_count));
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：数据库操作性能（通过API间接测试）
fn benchmark_database_operations(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("数据库操作性能");

    group.sample_size(30);

    // 测试不同数据量的查询性能
    for task_count in [10, 50, 100].iter() {
        group.bench_with_input(
            BenchmarkId::new("查询任务列表", task_count),
            task_count,
            |b, _task_count| {
                b.iter(|| {
                    rt.block_on(async {
                        let response = auth
                            .client
                            .get(&format!("{}/api/tasks", BASE_URL))
                            .header("Authorization", format!("Bearer {}", auth.token))
                            .send()
                            .await
                            .expect("查询任务列表失败");

                        let tasks: serde_json::Value = response.json().await.expect("解析响应失败");
                        black_box(tasks);
                    })
                });
            },
        );
    }

    group.finish();
}

// 定义基准测试组
criterion_group!(
    api_benchmarks,
    benchmark_get_tasks_api,
    benchmark_post_tasks_api,
    benchmark_auth_api,
    benchmark_api_throughput,
    benchmark_concurrent_load_stress,
    benchmark_database_operations
);

// 主入口点
criterion_main!(api_benchmarks);
