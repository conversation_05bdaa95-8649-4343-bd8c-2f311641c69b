// 综合性能基准测试 - 任务21专用高并发压力测试
// 模拟1万并发WebSocket连接，验证系统稳定性和性能
// 使用Criterion.rs进行基准测试，记录API响应时间、吞吐量等指标

use criterion::{BenchmarkId, Criterion, Throughput, criterion_group, criterion_main};
use futures_util::{SinkExt, StreamExt};
use reqwest::Client;
use serde_json::json;
use std::hint::black_box;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::Duration;
use tokio::runtime::Runtime;
use tokio::sync::Semaphore;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

/// 基准测试配置常量
const BASE_URL: &str = "http://127.0.0.1:3000";
const TEST_USER_EMAIL: &str = "<EMAIL>";
const TEST_USER_PASSWORD: &str = "password123";

/// 性能基准测试配置
const WARMUP_TIME: Duration = Duration::from_secs(5);
const MEASUREMENT_TIME: Duration = Duration::from_secs(15);
const SAMPLE_SIZE: usize = 50;

/// 测试用户认证信息结构
#[derive(Clone)]
struct TestAuth {
    token: String,
    client: Client,
}

/// 性能测试结果统计
#[derive(Debug, Clone)]
struct PerformanceMetrics {
    test_name: String,
    avg_response_time_ms: f64,
    p95_response_time_ms: f64,
    p99_response_time_ms: f64,
    throughput_rps: f64,
    success_rate: f64,
    error_count: u64,
    total_requests: u64,
}

/// 初始化测试环境和认证
async fn setup_test_auth() -> Result<TestAuth, Box<dyn std::error::Error>> {
    let client = Client::builder()
        .timeout(Duration::from_secs(30))
        .pool_max_idle_per_host(20)
        .pool_idle_timeout(Duration::from_secs(90))
        .build()?;

    // 用户登录获取JWT token
    let login_response = client
        .post(&format!("{}/api/auth/login", BASE_URL))
        .json(&json!({
            "username": "testuser456",
            "password": TEST_USER_PASSWORD
        }))
        .send()
        .await?;

    if !login_response.status().is_success() {
        return Err(format!("登录失败: {}", login_response.status()).into());
    }

    let login_data: serde_json::Value = login_response.json().await?;
    let token = login_data["data"]["access_token"]
        .as_str()
        .ok_or("无法获取认证token")?
        .to_string();

    Ok(TestAuth { token, client })
}

/// 检查服务器健康状态
async fn check_server_health() -> Result<bool, Box<dyn std::error::Error>> {
    let client = Client::builder().timeout(Duration::from_secs(10)).build()?;

    // 尝试多个端点来确保服务器运行
    let endpoints = vec![
        format!("{}/api/performance/health", BASE_URL),
        format!("{}/metrics", BASE_URL),
        format!("{}/api/health/database", BASE_URL),
    ];

    for endpoint in endpoints {
        match client.get(&endpoint).send().await {
            Ok(response) if response.status().is_success() => {
                println!("✅ 服务器健康检查通过: {}", endpoint);
                return Ok(true);
            }
            Ok(response) => {
                println!(
                    "⚠️ 端点响应异常: {} - 状态码: {}",
                    endpoint,
                    response.status()
                );
            }
            Err(e) => {
                println!("❌ 端点连接失败: {} - 错误: {}", endpoint, e);
            }
        }
    }

    Ok(false)
}

/// 基准测试：API响应时间对比（SQLite vs PostgreSQL+DragonflyDB）
fn benchmark_api_response_time_comparison(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    // 检查服务器健康状态
    rt.block_on(async {
        if !check_server_health().await.unwrap_or(false) {
            panic!("服务器未运行或不健康，请先启动服务器");
        }
    });

    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("API响应时间对比");
    group.sample_size(SAMPLE_SIZE);
    group.measurement_time(MEASUREMENT_TIME);
    group.warm_up_time(WARMUP_TIME);

    // 测试不同的API端点
    let endpoints = vec![
        ("GET /api/tasks", "GET", "/api/tasks", None),
        (
            "POST /api/tasks",
            "POST",
            "/api/tasks",
            Some(json!({
                "title": "基准测试任务",
                "description": "用于性能基准测试的任务",
                "priority": "medium"
            })),
        ),
        ("GET /api/auth/profile", "GET", "/api/auth/profile", None),
    ];

    for (name, method, path, body) in endpoints {
        group.bench_function(name, |b| {
            b.iter(|| {
                rt.block_on(async {
                    let client = auth.client.clone();
                    let token = auth.token.clone();
                    let url = format!("{}{}", BASE_URL, path);
                    let request_body = body.clone();

                    let mut request = match method {
                        "GET" => client.get(&url),
                        "POST" => client.post(&url),
                        "PUT" => client.put(&url),
                        "DELETE" => client.delete(&url),
                        _ => client.get(&url),
                    };

                    request = request.header("Authorization", format!("Bearer {}", token));

                    if let Some(body) = request_body {
                        request = request.json(&body);
                    }

                    let response = request.send().await.expect("API请求失败");
                    black_box(response.status().is_success());
                })
            });
        });
    }

    group.finish();
}

/// 基准测试：高并发负载测试
fn benchmark_concurrent_load_test(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("高并发负载测试");
    group.sample_size(20);
    group.measurement_time(Duration::from_secs(20));
    group.warm_up_time(Duration::from_secs(5));

    // 测试不同并发级别
    for concurrent_requests in [10, 50, 100, 200].iter() {
        group.throughput(Throughput::Elements(*concurrent_requests as u64));

        group.bench_with_input(
            BenchmarkId::new("并发GET请求", concurrent_requests),
            concurrent_requests,
            |b, &concurrent_requests| {
                b.iter(|| {
                    rt.block_on(async {
                        let auth = auth.clone();
                        let semaphore = Arc::new(Semaphore::new(concurrent_requests));
                        let mut handles = Vec::new();

                        for i in 0..concurrent_requests {
                            let client = auth.client.clone();
                            let token = auth.token.clone();
                            let semaphore = semaphore.clone();

                            let handle = tokio::spawn(async move {
                                let _permit = semaphore.acquire().await.expect("获取信号量失败");

                                // 混合不同类型的请求
                                let response = if i % 3 == 0 {
                                    // GET 任务列表
                                    client
                                        .get(&format!("{}/api/tasks", BASE_URL))
                                        .header("Authorization", format!("Bearer {}", token))
                                        .send()
                                        .await
                                } else if i % 3 == 1 {
                                    // GET 用户资料
                                    client
                                        .get(&format!("{}/api/auth/profile", BASE_URL))
                                        .header("Authorization", format!("Bearer {}", token))
                                        .send()
                                        .await
                                } else {
                                    // POST 创建任务
                                    client
                                        .post(&format!("{}/api/tasks", BASE_URL))
                                        .header("Authorization", format!("Bearer {}", token))
                                        .json(&json!({
                                            "title": format!("并发测试任务 {}", i),
                                            "description": "高并发负载测试生成的任务",
                                            "priority": "low"
                                        }))
                                        .send()
                                        .await
                                };

                                response.expect("并发API请求失败")
                            });

                            handles.push(handle);
                        }

                        // 等待所有请求完成
                        let mut success_count = 0;
                        for handle in handles {
                            match handle.await {
                                Ok(response) => {
                                    if response.status().is_success() {
                                        success_count += 1;
                                    }
                                }
                                Err(_) => {}
                            }
                        }

                        black_box(success_count);
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：数据库操作性能对比
fn benchmark_database_operations_comparison(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("数据库操作性能对比");
    group.sample_size(30);
    group.measurement_time(Duration::from_secs(12));

    // 测试不同数据量的操作
    for operation_count in [1, 10, 50, 100].iter() {
        group.bench_with_input(
            BenchmarkId::new("批量任务创建", operation_count),
            operation_count,
            |b, &operation_count| {
                b.iter(|| {
                    rt.block_on(async {
                        let auth = auth.clone();
                        let mut handles = Vec::new();

                        for i in 0..operation_count {
                            let client = auth.client.clone();
                            let token = auth.token.clone();

                            let handle = tokio::spawn(async move {
                                client
                                    .post(&format!("{}/api/tasks", BASE_URL))
                                    .header("Authorization", format!("Bearer {}", token))
                                    .json(&json!({
                                        "title": format!("批量测试任务 {}", i),
                                        "description": "数据库性能测试任务",
                                        "priority": "medium"
                                    }))
                                    .send()
                                    .await
                                    .expect("批量创建任务失败")
                            });

                            handles.push(handle);
                        }

                        // 等待所有操作完成
                        for handle in handles {
                            let response = handle.await.expect("任务执行失败");
                            black_box(response.status().is_success());
                        }
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：高并发WebSocket连接压力测试 - 任务21核心功能
fn benchmark_websocket_stress_test(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();
    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("WebSocket高并发压力测试");
    group.sample_size(10); // 减少样本数量，因为WebSocket测试比较耗时
    group.measurement_time(Duration::from_secs(30));
    group.warm_up_time(Duration::from_secs(10));

    // 测试不同并发级别的WebSocket连接
    for concurrent_connections in [100, 500, 1000, 2000].iter() {
        group.throughput(Throughput::Elements(*concurrent_connections as u64));

        group.bench_with_input(
            BenchmarkId::new("并发WebSocket连接", concurrent_connections),
            concurrent_connections,
            |b, &concurrent_connections| {
                b.iter(|| {
                    rt.block_on(async {
                        let auth = auth.clone();
                        let semaphore = Arc::new(Semaphore::new(concurrent_connections));
                        let success_count = Arc::new(AtomicU64::new(0));
                        let error_count = Arc::new(AtomicU64::new(0));
                        let mut handles = Vec::new();

                        for i in 0..concurrent_connections {
                            let token = auth.token.clone();
                            let semaphore = semaphore.clone();
                            let success_count = success_count.clone();
                            let error_count = error_count.clone();

                            let handle = tokio::spawn(async move {
                                let _permit = semaphore.acquire().await.expect("获取信号量失败");

                                // 建立WebSocket连接
                                let ws_url = format!("ws://127.0.0.1:3000/ws?token={}", token);

                                match connect_async(&ws_url).await {
                                    Ok((mut ws_stream, _)) => {
                                        // 发送测试消息
                                        let test_message = json!({
                                            "type": "chat_message",
                                            "room_id": 1,
                                            "content": format!("压力测试消息 {}", i)
                                        });

                                        if let Err(_) = ws_stream
                                            .send(Message::Text(test_message.to_string()))
                                            .await
                                        {
                                            error_count.fetch_add(1, Ordering::Relaxed);
                                            return;
                                        }

                                        // 等待响应
                                        match tokio::time::timeout(
                                            Duration::from_secs(5),
                                            ws_stream.next(),
                                        )
                                        .await
                                        {
                                            Ok(Some(Ok(_))) => {
                                                success_count.fetch_add(1, Ordering::Relaxed);
                                            }
                                            _ => {
                                                error_count.fetch_add(1, Ordering::Relaxed);
                                            }
                                        }

                                        // 关闭连接
                                        let _ = ws_stream.close(None).await;
                                    }
                                    Err(_) => {
                                        error_count.fetch_add(1, Ordering::Relaxed);
                                    }
                                }
                            });

                            handles.push(handle);
                        }

                        // 等待所有连接完成
                        for handle in handles {
                            let _ = handle.await;
                        }

                        let final_success = success_count.load(Ordering::Relaxed);
                        let final_errors = error_count.load(Ordering::Relaxed);

                        // 记录性能指标
                        black_box((final_success, final_errors));
                    })
                });
            },
        );
    }

    group.finish();
}

// 定义基准测试组
criterion_group!(
    comprehensive_benchmarks,
    benchmark_api_response_time_comparison,
    benchmark_concurrent_load_test,
    benchmark_database_operations_comparison,
    benchmark_websocket_stress_test
);

// 主入口点
criterion_main!(comprehensive_benchmarks);
