// 任务11.4：并发负载基准测试
// 专门用于测试高并发场景下的系统性能和稳定性
// 结合Prometheus监控系统观察资源使用情况

use criterion::{BenchmarkId, Criterion, Throughput, criterion_group, criterion_main};
use reqwest::Client;
use serde_json::json;
use std::hint::black_box;
use std::sync::Arc;
use std::time::Duration;
use tokio::runtime::Runtime;
use tokio::sync::Semaphore;

/// 基准测试配置常量
const BASE_URL: &str = "http://127.0.0.1:3000";
const TEST_USER_EMAIL: &str = "<EMAIL>";
const TEST_USER_PASSWORD: &str = "password123";

/// 测试用户认证信息结构
#[derive(Clone)]
struct TestAuth {
    token: String,
    client: Client,
}

/// 负载测试统计信息
#[derive(Debug, Clone)]
struct LoadTestStats {
    success_count: usize,
    error_count: usize,
    total_requests: usize,
    avg_response_time_ms: f64,
}

/// 初始化测试环境和认证
async fn setup_test_auth() -> Result<TestAuth, Box<dyn std::error::Error>> {
    let client = Client::builder()
        .timeout(Duration::from_secs(30))
        .pool_max_idle_per_host(100)
        .build()?;

    // 用户登录获取JWT token
    let login_response = client
        .post(&format!("{}/api/auth/login", BASE_URL))
        .json(&json!({
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }))
        .send()
        .await?;

    if !login_response.status().is_success() {
        return Err(format!("登录失败: {}", login_response.status()).into());
    }

    let login_data: serde_json::Value = login_response.json().await?;
    let token = login_data["token"]
        .as_str()
        .ok_or("无法获取认证token")?
        .to_string();

    Ok(TestAuth { token, client })
}

/// 基准测试：极高并发GET请求负载测试
fn benchmark_extreme_concurrent_get(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("极高并发GET负载测试");

    // 配置基准测试参数
    group.sample_size(10);
    group.measurement_time(Duration::from_secs(20));
    group.warm_up_time(Duration::from_secs(5));

    // 测试不同并发级别：100, 500, 1000, 2000个并发请求
    for concurrent_requests in [100, 500, 1000, 2000].iter() {
        group.throughput(Throughput::Elements(*concurrent_requests as u64));

        group.bench_with_input(
            BenchmarkId::new("极高并发GET", concurrent_requests),
            concurrent_requests,
            |b, &concurrent_requests| {
                b.iter(|| {
                    rt.block_on(async {
                        let semaphore = Arc::new(Semaphore::new(concurrent_requests));
                        let mut handles = Vec::with_capacity(concurrent_requests);

                        for _ in 0..concurrent_requests {
                            let client = auth.client.clone();
                            let token = auth.token.clone();
                            let semaphore = semaphore.clone();

                            let handle = tokio::spawn(async move {
                                let _permit = semaphore.acquire().await.expect("获取信号量失败");

                                let start_time = std::time::Instant::now();
                                let result = client
                                    .get(&format!("{}/api/tasks", BASE_URL))
                                    .header("Authorization", format!("Bearer {}", token))
                                    .send()
                                    .await;
                                let elapsed = start_time.elapsed();

                                match result {
                                    Ok(response) => (response.status().is_success(), elapsed),
                                    Err(_) => (false, elapsed),
                                }
                            });

                            handles.push(handle);
                        }

                        // 收集所有结果
                        let mut success_count = 0;
                        let mut total_time = Duration::new(0, 0);

                        for handle in handles {
                            if let Ok((success, elapsed)) = handle.await {
                                if success {
                                    success_count += 1;
                                }
                                total_time += elapsed;
                            }
                        }

                        let stats = LoadTestStats {
                            success_count,
                            error_count: concurrent_requests - success_count,
                            total_requests: concurrent_requests,
                            avg_response_time_ms: (total_time.as_millis() as f64)
                                / (concurrent_requests as f64),
                        };

                        black_box(stats);
                    })
                });
            },
        );
    }

    group.finish();
}

/// 基准测试：混合API操作并发负载测试
fn benchmark_mixed_api_concurrent_load(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("混合API并发负载测试");

    // 配置基准测试参数
    group.sample_size(8);
    group.measurement_time(Duration::from_secs(25));
    group.warm_up_time(Duration::from_secs(8));

    // 测试不同并发级别的混合操作
    for concurrent_requests in [200, 500, 1000].iter() {
        group.throughput(Throughput::Elements(*concurrent_requests as u64));

        group.bench_with_input(
            BenchmarkId::new("混合API并发", concurrent_requests),
            concurrent_requests,
            |b, &concurrent_requests| {
                b.iter(|| {
                    rt.block_on(async {
                        let semaphore = Arc::new(Semaphore::new(concurrent_requests.min(500))); // 限制最大并发
                        let mut handles = Vec::with_capacity(concurrent_requests);

                        for i in 0..concurrent_requests {
                            let client = auth.client.clone();
                            let token = auth.token.clone();
                            let semaphore = semaphore.clone();

                            let handle = tokio::spawn(async move {
                                let _permit = semaphore.acquire().await.expect("获取信号量失败");

                                let start_time = std::time::Instant::now();

                                // 根据索引选择不同的API操作类型
                                let result = match i % 4 {
                                    0 => {
                                        // GET 任务列表 (40%)
                                        client
                                            .get(&format!("{}/api/tasks", BASE_URL))
                                            .header("Authorization", format!("Bearer {}", token))
                                            .send()
                                            .await
                                    }
                                    1 => {
                                        // POST 创建任务 (30%)
                                        client
                                            .post(&format!("{}/api/tasks", BASE_URL))
                                            .header("Authorization", format!("Bearer {}", token))
                                            .json(&json!({
                                                "title": format!("并发测试任务 {}", i),
                                                "description": "混合API并发负载测试",
                                                "priority": "low"
                                            }))
                                            .send()
                                            .await
                                    }
                                    2 => {
                                        // 用户认证 (20%)
                                        client
                                            .post(&format!("{}/api/auth/login", BASE_URL))
                                            .json(&json!({
                                                "email": TEST_USER_EMAIL,
                                                "password": TEST_USER_PASSWORD
                                            }))
                                            .send()
                                            .await
                                    }
                                    _ => {
                                        // GET 特定任务 (10%)
                                        client
                                            .get(&format!("{}/api/tasks/1", BASE_URL))
                                            .header("Authorization", format!("Bearer {}", token))
                                            .send()
                                            .await
                                    }
                                };

                                let elapsed = start_time.elapsed();

                                match result {
                                    Ok(response) => (response.status().is_success(), elapsed),
                                    Err(_) => (false, elapsed),
                                }
                            });

                            handles.push(handle);
                        }

                        // 收集统计信息
                        let mut success_count = 0;
                        let mut error_count = 0;
                        let mut total_time = Duration::new(0, 0);

                        for handle in handles {
                            match handle.await {
                                Ok((success, elapsed)) => {
                                    if success {
                                        success_count += 1;
                                    } else {
                                        error_count += 1;
                                    }
                                    total_time += elapsed;
                                }
                                Err(_) => {
                                    error_count += 1;
                                }
                            }
                        }

                        let stats = LoadTestStats {
                            success_count,
                            error_count,
                            total_requests: concurrent_requests,
                            avg_response_time_ms: (total_time.as_millis() as f64)
                                / (concurrent_requests as f64),
                        };

                        black_box(stats);
                    })
                });
            },
        );
    }

    group.finish();
}

// 定义基准测试组
criterion_group!(
    concurrent_load_benchmarks,
    benchmark_extreme_concurrent_get,
    benchmark_mixed_api_concurrent_load
);

// 主入口点
criterion_main!(concurrent_load_benchmarks);
