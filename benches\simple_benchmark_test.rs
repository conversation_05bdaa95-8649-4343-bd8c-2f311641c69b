// 简单的基准测试验证
// 用于验证Criterion.rs配置是否正确

use criterion::{Criterion, criterion_group, criterion_main};
use std::{hint::black_box, time::Duration};

/// 简单的数学运算基准测试
fn benchmark_simple_math(c: &mut Criterion) {
    let mut group = c.benchmark_group("简单数学运算");

    group.sample_size(1000);
    group.measurement_time(Duration::from_secs(5));

    group.bench_function("加法运算", |b| {
        b.iter(|| {
            let a = black_box(42);
            let b = black_box(24);
            black_box(a + b)
        });
    });

    group.bench_function("乘法运算", |b| {
        b.iter(|| {
            let a = black_box(42);
            let b = black_box(24);
            black_box(a * b)
        });
    });

    group.bench_function("斐波那契数列(n=20)", |b| {
        b.iter(|| {
            fn fibonacci(n: u64) -> u64 {
                match n {
                    0 => 0,
                    1 => 1,
                    n => fibonacci(n - 1) + fi<PERSON><PERSON><PERSON>(n - 2),
                }
            }
            black_box(fibon<PERSON>ci(black_box(20)))
        });
    });

    group.finish();
}

/// 字符串操作基准测试
fn benchmark_string_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("字符串操作");

    group.sample_size(500);

    group.bench_function("字符串连接", |b| {
        b.iter(|| {
            let mut result = String::new();
            for i in 0..100 {
                result.push_str(&format!("测试字符串{}", i));
            }
            black_box(result)
        });
    });

    group.bench_function("字符串格式化", |b| {
        b.iter(|| {
            let name = black_box("Axum");
            let version = black_box("0.8.4");
            black_box(format!("框架: {}, 版本: {}", name, version))
        });
    });

    group.finish();
}

/// 向量操作基准测试
fn benchmark_vector_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("向量操作");

    group.sample_size(200);

    group.bench_function("向量创建和填充", |b| {
        b.iter(|| {
            let mut vec = Vec::new();
            for i in 0..1000 {
                vec.push(i);
            }
            black_box(vec)
        });
    });

    group.bench_function("向量排序", |b| {
        let mut vec: Vec<i32> = (0..1000).rev().collect();
        b.iter(|| {
            let mut v = vec.clone();
            v.sort();
            black_box(v)
        });
    });

    group.finish();
}

/// JSON序列化基准测试
fn benchmark_json_operations(c: &mut Criterion) {
    use serde_json::{Value, json};

    let mut group = c.benchmark_group("JSON操作");

    group.sample_size(300);

    let test_data = json!({
        "id": 1,
        "title": "测试任务",
        "description": "这是一个用于基准测试的任务描述",
        "priority": "high",
        "completed": false,
        "tags": ["测试", "基准", "性能"],
        "metadata": {
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T12:00:00Z"
        }
    });

    group.bench_function("JSON序列化", |b| {
        b.iter(|| black_box(serde_json::to_string(&test_data).unwrap()));
    });

    group.bench_function("JSON反序列化", |b| {
        let json_str = serde_json::to_string(&test_data).unwrap();
        b.iter(|| {
            let _: Value = black_box(serde_json::from_str(&json_str).unwrap());
        });
    });

    group.finish();
}

/// 异步操作基准测试
fn benchmark_async_operations(c: &mut Criterion) {
    let mut group = c.benchmark_group("异步操作");

    group.sample_size(100);

    group.bench_function("同步睡眠", |b| {
        b.iter(|| {
            std::thread::sleep(Duration::from_millis(1));
            black_box(())
        });
    });

    group.bench_function("简单计算", |b| {
        b.iter(|| {
            let result = (0..100).sum::<i32>();
            black_box(result)
        });
    });

    group.finish();
}

// 定义基准测试组
criterion_group!(
    simple_benchmarks,
    benchmark_simple_math,
    benchmark_string_operations,
    benchmark_vector_operations,
    benchmark_json_operations,
    benchmark_async_operations
);

// 主入口点
criterion_main!(simple_benchmarks);
