// 高并发压力测试基准 - 任务21专用
// 模拟1万并发WebSocket连接，验证系统稳定性和性能
// 使用Criterion.rs进行基准测试，记录详细性能指标

use criterion::{BenchmarkId, Criterion, Throughput, criterion_group, criterion_main};
use futures_util::{SinkExt, StreamExt};
use reqwest::Client;
use serde_json::json;
use std::hint::black_box;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
use std::time::{Duration, Instant};
use tokio::runtime::Runtime;
use tokio::sync::Semaphore;
use tokio_tungstenite::{connect_async, tungstenite::protocol::Message};

/// 基准测试配置常量
const BASE_URL: &str = "http://127.0.0.1:3000";
const TEST_USER_EMAIL: &str = "<EMAIL>";
const TEST_USER_PASSWORD: &str = "password123";

/// 性能测试结果统计
#[derive(Debug, Clone)]
struct StressTestMetrics {
    total_connections: u64,
    successful_connections: u64,
    failed_connections: u64,
    total_messages_sent: u64,
    total_messages_received: u64,
    avg_connection_time_ms: f64,
    avg_message_latency_ms: f64,
    peak_concurrent_connections: u64,
    test_duration_ms: u64,
}

/// 测试用户认证信息结构
#[derive(Clone)]
struct TestAuth {
    token: String,
    client: Client,
}

/// 初始化测试环境和认证
async fn setup_test_auth() -> Result<TestAuth, Box<dyn std::error::Error>> {
    let client = Client::builder()
        .timeout(Duration::from_secs(30))
        .pool_max_idle_per_host(50)
        .pool_idle_timeout(Duration::from_secs(90))
        .build()?;

    // 用户登录获取JWT token
    let login_response = client
        .post(&format!("{}/api/auth/login", BASE_URL))
        .json(&json!({
            "username": "testuser456",
            "password": TEST_USER_PASSWORD
        }))
        .send()
        .await?;

    if !login_response.status().is_success() {
        return Err(format!("登录失败: {}", login_response.status()).into());
    }

    let login_data: serde_json::Value = login_response.json().await?;
    let token = login_data["data"]["access_token"]
        .as_str()
        .ok_or("无法获取认证token")?
        .to_string();

    Ok(TestAuth { token, client })
}

/// 检查服务器健康状态
async fn check_server_health() -> Result<bool, Box<dyn std::error::Error>> {
    let client = Client::builder().timeout(Duration::from_secs(10)).build()?;

    // 尝试多个端点来确保服务器运行
    let endpoints = vec![
        format!("{}/api/performance/health", BASE_URL),
        format!("{}/metrics", BASE_URL),
        format!("{}/api/health", BASE_URL),
    ];

    for endpoint in endpoints {
        match client.get(&endpoint).send().await {
            Ok(response) if response.status().is_success() => {
                println!("✅ 服务器健康检查通过: {}", endpoint);
                return Ok(true);
            }
            Ok(response) => {
                println!(
                    "⚠️ 端点响应异常: {} - 状态码: {}",
                    endpoint,
                    response.status()
                );
            }
            Err(e) => {
                println!("❌ 端点连接失败: {} - 错误: {}", endpoint, e);
            }
        }
    }

    Ok(false)
}

/// 基准测试：极限WebSocket并发连接测试
fn benchmark_extreme_websocket_concurrency(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    // 检查服务器健康状态
    rt.block_on(async {
        if !check_server_health().await.unwrap_or(false) {
            panic!("服务器未运行或不健康，请先启动服务器");
        }
    });

    let auth = rt.block_on(async { setup_test_auth().await.expect("设置测试认证失败") });

    let mut group = c.benchmark_group("极限WebSocket并发连接测试");
    group.sample_size(5); // 极限测试样本数量较少
    group.measurement_time(Duration::from_secs(60)); // 更长的测试时间
    group.warm_up_time(Duration::from_secs(15));

    // 测试不同并发级别，逐步接近1万连接
    for concurrent_connections in [1000, 2500, 5000, 7500, 10000].iter() {
        group.throughput(Throughput::Elements(*concurrent_connections as u64));

        group.bench_with_input(
            BenchmarkId::new("极限并发WebSocket", concurrent_connections),
            concurrent_connections,
            |b, &concurrent_connections| {
                b.iter(|| {
                    rt.block_on(async {
                        let auth = auth.clone();
                        let start_time = Instant::now();
                        // 性能指标收集
                        let total_connections = Arc::new(AtomicU64::new(0));
                        let successful_connections = Arc::new(AtomicU64::new(0));
                        let failed_connections = Arc::new(AtomicU64::new(0));
                        let messages_sent = Arc::new(AtomicU64::new(0));
                        let messages_received = Arc::new(AtomicU64::new(0));
                        let connection_times = Arc::new(tokio::sync::Mutex::new(Vec::new()));

                        // 控制并发连接数
                        let semaphore = Arc::new(Semaphore::new(concurrent_connections.min(1000)));
                        let mut handles = Vec::new();

                        for i in 0..concurrent_connections {
                            let token = auth.token.clone();
                            let semaphore = semaphore.clone();
                            let total_connections = total_connections.clone();
                            let successful_connections = successful_connections.clone();
                            let failed_connections = failed_connections.clone();
                            let messages_sent = messages_sent.clone();
                            let messages_received = messages_received.clone();
                            let connection_times = connection_times.clone();

                            let handle = tokio::spawn(async move {
                                let _permit = semaphore.acquire().await.expect("获取信号量失败");
                                let connection_start = Instant::now();

                                total_connections.fetch_add(1, Ordering::Relaxed);

                                // 建立WebSocket连接
                                let ws_url = format!("ws://127.0.0.1:3000/ws?token={}", token);

                                match connect_async(&ws_url).await {
                                    Ok((mut ws_stream, _)) => {
                                        let connection_time = connection_start.elapsed();
                                        connection_times
                                            .lock().await
                                            .push(connection_time.as_millis() as f64);
                                        successful_connections.fetch_add(1, Ordering::Relaxed);

                                        // 发送多条测试消息
                                        for msg_id in 0..5 {
                                            let test_message =
                                                json!({
                                                "type": "chat_message",
                                                "room_id": 1,
                                                "content": format!("压力测试消息 {} - {}", i, msg_id)
                                            });

                                            if
                                                ws_stream
                                                    .send(
                                                        Message::Text(test_message.to_string())
                                                    ).await
                                                    .is_ok()
                                            {
                                                messages_sent.fetch_add(1, Ordering::Relaxed);

                                                // 等待响应
                                                match
                                                    tokio::time::timeout(
                                                        Duration::from_secs(3),
                                                        ws_stream.next()
                                                    ).await
                                                {
                                                    Ok(Some(Ok(_))) => {
                                                        messages_received.fetch_add(
                                                            1,
                                                            Ordering::Relaxed
                                                        );
                                                    }
                                                    _ => {
                                                        break;
                                                    }
                                                }
                                            } else {
                                                break;
                                            }
                                        }

                                        // 保持连接一段时间以测试稳定性
                                        tokio::time::sleep(Duration::from_millis(100)).await;

                                        // 关闭连接
                                        let _ = ws_stream.close(None).await;
                                    }
                                    Err(_) => {
                                        failed_connections.fetch_add(1, Ordering::Relaxed);
                                    }
                                }
                            });

                            handles.push(handle);
                        }

                        // 等待所有连接完成
                        for handle in handles {
                            let _ = handle.await;
                        }

                        let test_duration = start_time.elapsed();
                        let connection_times_vec = connection_times.lock().await;
                        let avg_connection_time = if connection_times_vec.is_empty() {
                            0.0
                        } else {
                            connection_times_vec.iter().sum::<f64>() /
                                (connection_times_vec.len() as f64)
                        };

                        // 构建性能指标
                        let metrics = StressTestMetrics {
                            total_connections: total_connections.load(Ordering::Relaxed),
                            successful_connections: successful_connections.load(Ordering::Relaxed),
                            failed_connections: failed_connections.load(Ordering::Relaxed),
                            total_messages_sent: messages_sent.load(Ordering::Relaxed),
                            total_messages_received: messages_received.load(Ordering::Relaxed),
                            avg_connection_time_ms: avg_connection_time,
                            avg_message_latency_ms: 0.0, // 简化版本，不计算消息延迟
                            peak_concurrent_connections: concurrent_connections as u64,
                            test_duration_ms: test_duration.as_millis() as u64,
                        };

                        // 输出性能指标
                        println!("压力测试指标 - 并发连接数: {}", concurrent_connections);
                        println!(
                            "  成功连接: {}/{}",
                            metrics.successful_connections,
                            metrics.total_connections
                        );
                        println!("  失败连接: {}", metrics.failed_connections);
                        println!(
                            "  消息发送/接收: {}/{}",
                            metrics.total_messages_sent,
                            metrics.total_messages_received
                        );
                        println!("  平均连接时间: {:.2}ms", metrics.avg_connection_time_ms);
                        println!("  测试总时长: {}ms", metrics.test_duration_ms);

                        // 记录性能指标供Criterion分析
                        black_box(metrics);
                    })
                });
            }
        );
    }

    group.finish();
}

// 定义基准测试组
criterion_group!(
    stress_test_benchmarks,
    benchmark_extreme_websocket_concurrency
);

// 主入口点
criterion_main!(stress_test_benchmarks);
