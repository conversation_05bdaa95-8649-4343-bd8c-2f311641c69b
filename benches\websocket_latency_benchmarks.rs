// WebSocket延迟基准测试
// 测量WebSocket连接建立、消息发送和接收的延迟

use criterion::{BenchmarkId, Criterion, Throughput, criterion_group, criterion_main};
use serde_json::json;
use std::{
    hint::black_box,
    time::{Duration, Instant},
};
use tokio::runtime::Runtime;

/// WebSocket测试配置常量
const WS_URL: &str = "ws://127.0.0.1:3000/ws";
const TEST_USER_TOKEN: &str = "test_token_for_benchmarks";

/// WebSocket连接延迟测试
fn benchmark_websocket_connection_latency(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("WebSocket连接延迟");

    group.sample_size(50);
    group.measurement_time(Duration::from_secs(10));
    group.warm_up_time(Duration::from_secs(2));

    group.bench_function("WebSocket连接建立", |b| {
        b.to_async(&rt).iter(|| async {
            let start_time = Instant::now();

            // 构建带认证参数的WebSocket URL
            let ws_url = format!("{}?token={}", WS_URL, TEST_USER_TOKEN);
            let url = Url::parse(&ws_url).expect("无效的WebSocket URL");

            // 建立WebSocket连接
            let (ws_stream, _response) = connect_async(url).await.expect("WebSocket连接失败");

            let connection_time = start_time.elapsed();

            // 关闭连接
            drop(ws_stream);

            black_box(connection_time);
        });
    });

    group.finish();
}

/// WebSocket消息发送延迟测试
fn benchmark_websocket_message_send_latency(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("WebSocket消息发送延迟");

    group.sample_size(100);
    group.measurement_time(Duration::from_secs(15));

    group.bench_function("单条消息发送", |b| {
        b.to_async(&rt).iter(|| async {
            let ws_url = format!("{}?token={}", WS_URL, TEST_USER_TOKEN);
            let url = Url::parse(&ws_url).expect("无效的WebSocket URL");

            let (ws_stream, _) = connect_async(url).await.expect("WebSocket连接失败");

            let (mut ws_sender, _ws_receiver) = ws_stream.split();

            let test_message = json!({
                "type": "chat_message",
                "content": "基准测试消息",
                "timestamp": chrono::Utc::now().to_rfc3339()
            });

            let start_time = Instant::now();

            // 发送消息
            ws_sender
                .send(Message::Text(test_message.to_string()))
                .await
                .expect("发送WebSocket消息失败");

            let send_time = start_time.elapsed();

            black_box(send_time);
        });
    });

    group.finish();
}

/// WebSocket消息往返延迟测试（RTT - Round Trip Time）
fn benchmark_websocket_round_trip_latency(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("WebSocket往返延迟");

    group.sample_size(50);
    group.measurement_time(Duration::from_secs(20));

    group.bench_function("消息往返时间", |b| {
        b.to_async(&rt).iter(|| async {
            let ws_url = format!("{}?token={}", WS_URL, TEST_USER_TOKEN);
            let url = Url::parse(&ws_url).expect("无效的WebSocket URL");

            let (ws_stream, _) = connect_async(url).await.expect("WebSocket连接失败");

            let (mut ws_sender, mut ws_receiver) = ws_stream.split();

            let ping_message = json!({
                "type": "ping",
                "timestamp": chrono::Utc::now().to_rfc3339(),
                "id": uuid::Uuid::new_v4().to_string()
            });

            let start_time = Instant::now();

            // 发送ping消息
            ws_sender
                .send(Message::Text(ping_message.to_string()))
                .await
                .expect("发送ping消息失败");

            // 等待响应
            if let Some(response) = ws_receiver.next().await {
                match response {
                    Ok(_message) => {
                        let rtt = start_time.elapsed();
                        black_box(rtt);
                    }
                    Err(e) => {
                        eprintln!("接收WebSocket响应失败: {}", e);
                    }
                }
            }
        });
    });

    group.finish();
}

/// WebSocket并发连接性能测试
fn benchmark_websocket_concurrent_connections(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("WebSocket并发连接性能");

    // 测试不同并发连接数的性能
    for connection_count in [1, 5, 10, 20, 50].iter() {
        group.throughput(Throughput::Elements(*connection_count as u64));

        group.bench_with_input(
            BenchmarkId::new("并发连接", connection_count),
            connection_count,
            |b, &connection_count| {
                b.to_async(&rt).iter(|| async {
                    let start_time = Instant::now();
                    let mut handles = Vec::new();

                    // 创建多个并发WebSocket连接
                    for i in 0..connection_count {
                        let handle = tokio::spawn(async move {
                            let ws_url =
                                format!("{}?token={}&client_id={}", WS_URL, TEST_USER_TOKEN, i);
                            let url = Url::parse(&ws_url).expect("无效的WebSocket URL");

                            let (ws_stream, _) =
                                connect_async(url).await.expect("WebSocket连接失败");

                            // 保持连接一小段时间
                            tokio::time::sleep(Duration::from_millis(100)).await;

                            drop(ws_stream);
                        });

                        handles.push(handle);
                    }

                    // 等待所有连接完成
                    for handle in handles {
                        handle.await.expect("WebSocket连接任务失败");
                    }

                    let total_time = start_time.elapsed();
                    black_box(total_time);
                });
            },
        );
    }

    group.finish();
}

/// WebSocket消息吞吐量测试
fn benchmark_websocket_message_throughput(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("WebSocket消息吞吐量");

    // 测试不同消息数量的吞吐量
    for message_count in [10, 50, 100, 200].iter() {
        group.throughput(Throughput::Elements(*message_count as u64));

        group.bench_with_input(
            BenchmarkId::new("批量消息发送", message_count),
            message_count,
            |b, &message_count| {
                b.to_async(&rt).iter(|| async {
                    let ws_url = format!("{}?token={}", WS_URL, TEST_USER_TOKEN);
                    let url = Url::parse(&ws_url).expect("无效的WebSocket URL");

                    let (ws_stream, _) = connect_async(url).await.expect("WebSocket连接失败");

                    let (mut ws_sender, _ws_receiver) = ws_stream.split();

                    let start_time = Instant::now();

                    // 批量发送消息
                    for i in 0..message_count {
                        let message = json!({
                            "type": "chat_message",
                            "content": format!("批量测试消息 {}", i),
                            "timestamp": chrono::Utc::now().to_rfc3339()
                        });

                        ws_sender
                            .send(Message::Text(message.to_string()))
                            .await
                            .expect("发送批量消息失败");
                    }

                    let throughput_time = start_time.elapsed();
                    black_box(throughput_time);
                });
            },
        );
    }

    group.finish();
}

/// WebSocket连接稳定性测试
fn benchmark_websocket_connection_stability(c: &mut Criterion) {
    let rt = Runtime::new().unwrap();

    let mut group = c.benchmark_group("WebSocket连接稳定性");

    group.sample_size(20);
    group.measurement_time(Duration::from_secs(30));

    group.bench_function("长时间连接保持", |b| {
        b.to_async(&rt).iter(|| async {
            let ws_url = format!("{}?token={}", WS_URL, TEST_USER_TOKEN);
            let url = Url::parse(&ws_url).expect("无效的WebSocket URL");

            let (ws_stream, _) = connect_async(url).await.expect("WebSocket连接失败");

            let (mut ws_sender, mut ws_receiver) = ws_stream.split();

            let start_time = Instant::now();

            // 在连接期间定期发送心跳消息
            let heartbeat_task = tokio::spawn(async move {
                for i in 0..10 {
                    let heartbeat = json!({
                        "type": "heartbeat",
                        "sequence": i,
                        "timestamp": chrono::Utc::now().to_rfc3339()
                    });

                    if ws_sender
                        .send(Message::Text(heartbeat.to_string()))
                        .await
                        .is_err()
                    {
                        break;
                    }

                    tokio::time::sleep(Duration::from_millis(500)).await;
                }
            });

            // 接收消息任务
            let receive_task = tokio::spawn(async move {
                let mut message_count = 0;
                while let Some(message) = ws_receiver.next().await {
                    if message.is_ok() {
                        message_count += 1;
                        if message_count >= 10 {
                            break;
                        }
                    }
                }
                message_count
            });

            // 等待任务完成
            let (_, received_count) = tokio::join!(heartbeat_task, receive_task);
            let stability_time = start_time.elapsed();

            black_box((stability_time, received_count.unwrap_or(0)));
        });
    });

    group.finish();
}

// 定义基准测试组
criterion_group!(
    websocket_benchmarks,
    benchmark_websocket_connection_latency,
    benchmark_websocket_message_send_latency,
    benchmark_websocket_round_trip_latency,
    benchmark_websocket_concurrent_connections,
    benchmark_websocket_message_throughput,
    benchmark_websocket_connection_stability
);

// 主入口点
criterion_main!(websocket_benchmarks);
