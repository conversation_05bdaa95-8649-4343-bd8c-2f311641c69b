# DragonflyDB 企业级配置文件
# 针对百万并发聊天室应用优化
# 适用于高性能、高可用性的生产环境

# ============================================================================
# 基础网络配置
# ============================================================================

# 监听端口 (默认6379，与Redis兼容)
--port=6379

# 绑定地址 (容器内部监听所有接口)
--bind=0.0.0.0

# 启用TCP_NODELAY以减少延迟
--tcp_nodelay=true

# TCP保活时间 (5分钟)
--tcp_keepalive=300

# 最大客户端连接数 (支持百万并发)
--maxclients=100000

# ============================================================================
# 内存管理配置
# ============================================================================

# 最大内存限制 (2GB，为百万并发预留充足内存)
--maxmemory=2gb

# 启用缓存模式 (当接近内存限制时自动驱逐数据)
--cache_mode=true

# 内存碎片整理阈值
--mem_defrag_threshold=0.7
--mem_defrag_waste_threshold=0.2
--mem_defrag_page_utilization_threshold=0.8

# 启用心跳驱逐机制
--enable_heartbeat_eviction=true
--max_eviction_per_heartbeat=200
--max_segment_to_consider=8

# OOM拒绝比率 (已退役的标志，移除)
# --oom_deny_ratio=1.05

# ============================================================================
# 性能优化配置
# ============================================================================

# 集群模式 (模拟模式，提供更好的性能)
--cluster_mode=emulated

# 分片数量 (自动检测CPU核心数)
--num_shards=0

# Proactor线程数 (I/O处理线程，自动检测)
--proactor_threads=0

# 连接I/O线程数 (自动检测)
--conn_io_threads=0

# 启用连接迁移优化
--migrate_connections=true

# 管道压缩阈值 (优化批量操作)
--pipeline_squash=20

# 请求缓存限制 (每个I/O线程128MB)
--request_cache_limit=134217728

# 订阅者线程限制 (未知标志，移除)
# --subscriber_thread_limit=268435456

# ============================================================================
# 数据结构优化
# ============================================================================

# 使用优化的Set数据结构 (未知标志，移除)
# --use_set2=true

# 使用B+树实现ZSet (未知标志，移除)
# --use_zset_tree=true

# 列表压缩深度 (启用压缩以节省内存)
--list_compress_depth=2

# 列表最大listpack大小
--list_max_listpack_size=-2

# ============================================================================
# 持久化配置
# ============================================================================

# 使用DragonflyDB原生快照格式
--df_snapshot_format=true

# 数据库文件名 (包含时间戳)
--dbfilename=dump-dragonflydb-{timestamp}

# 工作目录
--dir=/data

# 压缩模式 (LZ4压缩，平衡性能和存储)
--compression_mode=3
--compression_level=2

# 快照定时任务 (每小时保存一次快照，移除引号)
--snapshot_cron=0 * * * *

# 序列化最大块大小 (启用流式序列化以处理大对象)
--serialization_max_chunk_size=16777216

# 启用直接I/O (提高大文件写入性能)
--backing_file_direct=false

# ============================================================================
# 安全配置
# ============================================================================

# 数据库数量
--dbnum=16

# 密码认证 (生产环境必须设置)
--requirepass=dragonfly_secure_password_2025

# 管理端口配置 (仅限本地访问)
--admin_port=6380
--admin_bind=127.0.0.1
--admin_nopass=false

# 禁用危险命令重命名 (简化格式)
--rename_command=FLUSHDB=""
--rename_command=FLUSHALL=""
--rename_command=KEYS=""
--rename_command=CONFIG=""
--rename_command=SHUTDOWN=SHUTDOWN_ENTERPRISE_ONLY
--rename_command=DEBUG=""
--rename_command=EVAL=EVAL_RESTRICTED

# 限制管理命令仅在管理端口可用
--restricted_commands=CONFIG,DEBUG,FLUSHDB,FLUSHALL,SHUTDOWN

# ACL配置文件路径
--aclfile=/etc/dragonfly/users.acl

# ACL日志最大长度
--acllog_max_len=64

# ============================================================================
# 监控和调试配置
# ============================================================================

# 慢查询日志阈值 (10毫秒)
--slowlog_log_slower_than=10000
--slowlog_max_len=100

# 启用热点键跟踪 (未知标志，移除)
# --enable_top_keys_tracking=true

# 心跳频率
--hz=100

# 键输出限制
--keys_output_limit=10000

# ============================================================================
# Lua脚本配置
# ============================================================================

# 每线程Lua解释器数量
--interpreter_per_thread=20

# 默认Lua标志 (允许未声明的全局变量)
--default_lua_flags=allow-undeclared-globals

# 启用Lua自动异步
--lua_auto_async=true

# 多重评估压缩缓冲区
--multi_eval_squash_buffer=8192

# ============================================================================
# 多执行事务配置
# ============================================================================

# 多执行模式 (已退役的标志，移除)
# --multi_exec_mode=2

# 启用多执行压缩
--multi_exec_squash=true

# ============================================================================
# 高级性能调优
# ============================================================================

# 启用单跳阻塞优化 (未知标志，移除)
# --singlehop_blocking=true

# 序列化最大块大小 (流式序列化，0=禁用)
--serialization_max_chunk_size=0

# 分层存储最大待写入数 (未知标志，移除)
# --tiered_storage_max_pending_writes=64

# ============================================================================
# 日志配置
# ============================================================================

# 日志输出到stderr
--logtostderr=true

# 最小日志级别 (0=INFO, 1=WARNING, 2=ERROR, 3=FATAL)
--minloglevel=0

# 最大日志文件大小 (MB)
--max_log_size=100

# ============================================================================
# 版本检查
# ============================================================================

# 启用版本检查 (每日检查新版本)
--version_check=true
