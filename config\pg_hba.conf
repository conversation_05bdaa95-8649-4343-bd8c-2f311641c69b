# PostgreSQL 17 客户端认证配置文件
# 企业级安全设置，适用于Axum聊天室应用

# TYPE  DATABASE        USER            ADDRESS                 METHOD

# "local" 用于Unix域套接字连接
local   all             all                                     trust

# IPv4 本地连接 - 开发环境使用trust认证
host    all             all             127.0.0.1/32            trust
host    all             all             localhost               trust

# IPv6 本地连接
host    all             all             ::1/128                 trust

# 容器网络连接 (<PERSON><PERSON>/Podman网络) - 开发环境使用trust认证
host    all             all             **********/16           trust

# 开发环境 - 允许本地网络访问
host    all             all             ***********/16          trust
host    all             all             10.0.0.0/8              trust

# 应用专用数据库连接 - 开发环境使用trust认证
host    axum_tutorial   axum_user       **********/16           trust
host    axum_tutorial   axum_user       127.0.0.1/32            trust

# 复制连接 (为将来的主从复制准备)
host    replication     all             **********/16           trust

# 拒绝所有其他连接
host    all             all             0.0.0.0/0               reject

# 配置说明:
# TYPE: 连接类型 (local, host, hostssl, hostnossl)
# DATABASE: 数据库名称 (all 表示所有数据库)
# USER: 用户名 (all 表示所有用户)
# ADDRESS: 客户端IP地址或网络
# METHOD: 认证方法
#   - trust: 无条件信任 (仅用于开发)
#   - md5: MD5密码认证
#   - scram-sha-256: SCRAM-SHA-256认证 (推荐)
#   - reject: 拒绝连接

# 生产环境建议:
# 1. 将 trust 改为 scram-sha-256
# 2. 限制具体的IP地址范围
# 3. 启用SSL连接 (hostssl)
# 4. 定期审查和更新访问规则
