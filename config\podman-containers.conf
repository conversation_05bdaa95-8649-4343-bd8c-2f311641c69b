# Podman容器配置文件
# 此文件应放置在WSL2 Ubuntu中: ~/.config/containers/containers.conf
# 优化Podman在WSL2环境中的性能和网络配置

[containers]
# 默认容器用户
default_user = "user"

# 网络命名空间配置
netns = "bridge"

# 用户命名空间配置  
userns = "host"

# IPC命名空间配置
ipcns = "private"

# UTS命名空间配置
utsns = "private"

# PID命名空间配置
pidns = "private"

# Cgroup命名空间配置
cgroupns = "private"

# 默认Cgroup管理器
cgroups = "enabled"

# 日志驱动
log_driver = "journald"

# 日志大小限制
log_size_max = "10MB"

# 默认日志标签
log_tag = "{{.ImageName}}"

# 容器标签
label = true

# 默认容器运行时
runtime = "crun"

# 容器存储驱动
storage_driver = "overlay"

# 默认拉取策略
pull_policy = "missing"

# 环境变量
env = [
    "PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin",
    "TERM=xterm"
]

# 默认挂载
volumes = []

# 默认设备
devices = []

# 默认DNS服务器
dns_servers = [
    "*******",
    "*******"
]

# DNS搜索域
dns_searches = []

# DNS选项
dns_options = []

# 主机名
hostname = ""

# 默认网络
default_network = "podman"

# 网络配置目录
network_config_dir = "/etc/containers/networks"

# 容器初始化
init = false

# 初始化路径
init_path = "/usr/libexec/podman/catatonit"

# 默认ulimit
default_ulimits = [
    "nofile=65536:65536"
]

# 默认PID限制
pids_limit = 2048

# 日志级别
log_level = "warn"

# 事件日志记录器
events_logger = "journald"

# 事件日志文件路径
events_logfile_path = "/tmp/events.log"

# 镜像默认传输
image_default_transport = "docker://"

# 镜像并行作业数
image_parallel_copies = 0

# 添加压缩
add_compression = []

# 默认Mounts文件
default_mounts_file = ""

# 钩子目录
hooks_dir = [
    "/usr/share/containers/oci/hooks.d"
]

# 准备钩子
prepare_volume_on_create = false

# 容器退出命令
container_exit_command = "/usr/bin/podman"

# 容器退出命令延迟
container_exit_command_delay = 5

# TZ环境变量
tz = ""

# umask
umask = "0022"

[engine]
# 活动服务
active_service = ""

# Cgroup管理器
cgroup_manager = "systemd"

# 卷插件目录
volume_plugin_dir = "/usr/libexec/podman"

# 运行时
runtime = "crun"

# 运行时支持JSON
runtime_supports_json = ["crun", "runc", "kata", "runsc"]

# 运行时支持KVM
runtime_supports_kvm = ["kata"]

# 运行时支持nocgroups
runtime_supports_nocgroups = ["crun", "runc"]

# 静态目录
static_dir = "/var/lib/containers/storage/libpod"

# 临时目录
tmp_dir = "/run/libpod"

# 卷路径
volume_path = "/var/lib/containers/storage/volumes"

# Chown复制的卷
chown_copied_files = true

# 数据库后端
database_backend = "sqlite"

# 事件日志记录器
events_logger = "journald"

# 镜像构建格式
image_build_format = "oci"

# 镜像默认传输
image_default_transport = "docker://"

# 镜像并行作业数
image_parallel_copies = 0

# 基础设施镜像
infra_image = "k8s.gcr.io/pause:3.5"

# 锁类型
lock_type = "shm"

# 机器启用
machine_enabled = false

# 多镜像存档
multi_image_archive = false

# 命名空间
namespace = ""

# 网络命令路径
network_cmd_path = ""

# 网络命令选项
network_cmd_options = []

# 无透视图
no_pivot_root = false

# 运行时路径
runtime_path = []

# 服务超时
service_timeout = 5

# 停止超时
stop_timeout = 10

# 远程URI
remote_uri = ""

# 远程身份
remote_identity = ""

[network]
# 网络后端
network_backend = "netavark"

# CNI插件目录
cni_plugin_dirs = [
    "/usr/local/libexec/cni",
    "/usr/libexec/cni",
    "/usr/local/lib/cni",
    "/usr/lib/cni",
    "/opt/cni/bin"
]

# 默认网络
default_network = "podman"

# 默认子网
default_subnet = "10.88.0.0/16"

# 默认子网池
default_subnet_pools = [
    {"base" = "10.89.0.0/16", "size" = 24},
    {"base" = "10.90.0.0/15", "size" = 24},
    {"base" = "10.92.0.0/14", "size" = 24},
    {"base" = "10.96.0.0/11", "size" = 24},
    {"base" = "10.128.0.0/9", "size" = 24}
]

# DNS绑定端口
dns_bind_port = 53

# 网络配置目录
network_config_dir = "/etc/containers/networks"

[secrets]
# 驱动
driver = "file"

# 选项
opts = {}

[configmaps]
# 驱动
driver = "file"

# 选项  
opts = {}
