# PostgreSQL 17 企业级性能优化配置
# 适用于百万吞吐量百万并发的Axum聊天室应用

# 连接设置
listen_addresses = '*'
port = 5432
max_connections = 200
superuser_reserved_connections = 3

# 内存设置
shared_buffers = 256MB                  # 共享缓冲区
effective_cache_size = 1GB              # 有效缓存大小
work_mem = 4MB                          # 工作内存
maintenance_work_mem = 64MB             # 维护工作内存
dynamic_shared_memory_type = posix

# WAL (Write-Ahead Logging) 设置
wal_level = replica
wal_buffers = 16MB
min_wal_size = 1GB
max_wal_size = 4GB
checkpoint_completion_target = 0.9
checkpoint_timeout = 5min

# 查询规划器设置
random_page_cost = 1.1                  # SSD优化
effective_io_concurrency = 200          # 并发IO
seq_page_cost = 1.0
cpu_tuple_cost = 0.01
cpu_index_tuple_cost = 0.005
cpu_operator_cost = 0.0025

# 并行查询设置
max_worker_processes = 8
max_parallel_workers_per_gather = 2
max_parallel_workers = 8
max_parallel_maintenance_workers = 2
parallel_tuple_cost = 0.1
parallel_setup_cost = 1000.0

# 统计信息设置
default_statistics_target = 100
track_activities = on
track_counts = on
track_io_timing = on
track_functions = all

# 日志设置
logging_collector = on
log_destination = 'stderr'
log_directory = 'log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_rotation_age = 1d
log_rotation_size = 100MB
log_min_duration_statement = 1000       # 记录超过1秒的查询
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
log_checkpoints = on
log_connections = on
log_disconnections = on
log_lock_waits = on
log_temp_files = 10MB

# 自动清理设置
autovacuum = on
autovacuum_max_workers = 3
autovacuum_naptime = 1min
autovacuum_vacuum_threshold = 50
autovacuum_analyze_threshold = 50
autovacuum_vacuum_scale_factor = 0.2
autovacuum_analyze_scale_factor = 0.1
autovacuum_freeze_max_age = 200000000
autovacuum_multixact_freeze_max_age = 400000000

# 锁设置
deadlock_timeout = 1s
max_locks_per_transaction = 64
max_pred_locks_per_transaction = 64

# 其他性能设置
shared_preload_libraries = 'pg_stat_statements'
max_prepared_transactions = 0
temp_buffers = 8MB
max_files_per_process = 1000
effective_io_concurrency = 200

# 时区设置
timezone = 'Asia/Shanghai'
log_timezone = 'Asia/Shanghai'

# 字符集设置
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'

# SSL设置 (生产环境建议启用)
ssl = off
#ssl_cert_file = 'server.crt'
#ssl_key_file = 'server.key'
#ssl_ca_file = 'ca.crt'

# 复制设置 (为将来的主从复制准备)
max_wal_senders = 3
wal_keep_size = 1GB
hot_standby = on
hot_standby_feedback = on

# JIT编译 (PostgreSQL 11+)
jit = on
jit_above_cost = 100000
jit_inline_above_cost = 500000
jit_optimize_above_cost = 500000
