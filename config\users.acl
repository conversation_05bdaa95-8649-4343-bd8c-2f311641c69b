# DragonflyDB ACL用户配置文件
# 定义不同权限级别的用户，确保企业级安全性

# ============================================================================
# 默认用户 (禁用，强制使用命名用户)
# ============================================================================
user default off

# ============================================================================
# 管理员用户 (完全权限)
# ============================================================================
user admin on >admin_secure_password_2025 ~* &* +@all

# ============================================================================
# 应用程序用户 (读写权限，限制危险命令)
# ============================================================================
user axum_app on >dragonfly_secure_password_2025 ~* &* +@all -@dangerous -flushdb -flushall -shutdown -debug -config -eval -script

# ============================================================================
# 只读用户 (仅读取权限)
# ============================================================================
user readonly on >readonly_password_2025 ~* &* +@read -@write -@admin -@dangerous

# ============================================================================
# 监控用户 (仅监控命令权限)
# ============================================================================
user monitor on >monitor_password_2025 ~* &* +ping +info +client +config|get +slowlog +memory +latency

# ============================================================================
# 缓存用户 (专用于缓存操作)
# ============================================================================
user cache_user on >cache_password_2025 ~cache:* &* +@read +@write +@string +@hash +@list +@set +@sortedset +expire +ttl +exists +del -@admin -@dangerous

# ============================================================================
# 会话用户 (专用于会话管理)
# ============================================================================
user session_user on >session_password_2025 ~session:* ~user:* &* +@read +@write +@string +@hash +expire +ttl +exists +del -@admin -@dangerous

# ============================================================================
# 聊天用户 (专用于聊天功能)
# ============================================================================
user chat_user on >chat_password_2025 ~chat:* ~message:* ~room:* &* +@read +@write +@string +@hash +@list +@set +@sortedset +@pubsub +expire +ttl +exists +del -@admin -@dangerous

# ============================================================================
# 备份用户 (仅备份相关权限)
# ============================================================================
user backup_user on >backup_password_2025 ~* &* +@read +save +bgsave +lastsave +info -@write -@admin -@dangerous
