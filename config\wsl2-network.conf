# WSL2网络配置文件
# 用于优化WSL2与Windows主机之间的网络连接
# 解决localhost端口映射问题

# 此文件应放置在Windows用户目录下: %USERPROFILE%\.wslconfig
# 例如: C:\Users\<USER>\.wslconfig

[wsl2]
# 内存限制 (建议设置为系统内存的50-75%)
memory=8GB

# CPU核心数限制
processors=4

# 启用localhost端口转发 (关键配置)
localhostForwarding=true

# 网络模式设置
networkingMode=NAT

# 启用DNS隧道
dnsTunneling=true

# 启用防火墙
firewall=true

# 启用自动代理
autoProxy=true

# 内核参数
kernelCommandLine=cgroup_no_v1=all systemd.unified_cgroup_hierarchy=1

# 交换文件大小
swap=2GB

# 交换文件路径
swapFile=%USERPROFILE%\\AppData\\Local\\Temp\\swap.vhdx

# 启用嵌套虚拟化
nestedVirtualization=true

# 页面报告
pageReporting=true

# 启用GUI应用支持
guiApplications=true

# 调试控制台
debugConsole=false

# 实验性功能
[experimental]
# 启用镜像网络模式 (Windows 11 22H2+)
networkingMode=mirrored

# 启用DNS代理
dnsTunneling=true

# 启用防火墙
firewall=true

# 启用自动内存回收
autoMemoryReclaim=gradual
