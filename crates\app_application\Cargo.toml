[package]
name = "app_application"
version = "0.1.0"
edition = "2024"
license = "MIT OR Apache-2.0"
authors = ["Rust学习者"]
description = "应用层：业务用例实现"

[dependencies]
# 内部依赖
app_domain = { workspace = true }
app_common = { workspace = true }
app_interfaces = { workspace = true }
app_infrastructure = { workspace = true }

# 异步支持
async-trait = { workspace = true }
tokio = { workspace = true }

# UUID 支持
uuid = { workspace = true }

# 密码哈希
argon2 = { version = "0.5", features = ["password-hash"] }
rand_core = "0.6"

# JWT
jsonwebtoken = "9.3"

# 日志
tracing = { workspace = true }

# 时间处理
chrono = { workspace = true }

# 序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 数据验证
validator = { workspace = true }

# 数据库ORM
sea-orm = { workspace = true }

# 正则表达式
regex = "1.10"
lazy_static = "1.4"

# 错误恢复机制 - 替换未维护的backoff包
tokio-retry = "0.3"  # 替代backoff
failsafe = "1.0"

# Web框架
axum = { workspace = true }

# 字节处理
bytes = "1.5"

# HTTP客户端（用于回调通知）
reqwest = { version = "0.12", features = ["json"] }

# CPU核心数检测
num_cpus = "1.16"
