//! # 认证应用服务
//!
//! 认证相关的业务用例实现，包括：
//! - 用户注册和登录
//! - JWT令牌管理
//! - 密码验证和哈希
//! - 认证业务流程编排

use app_common::error::{AppError, Result};
use app_domain::entities::user::User;
use app_domain::repositories::user_repository::UserRepositoryContract;
use app_domain::services::user_service::UserDomainService;
use app_interfaces::{AuthResponse, Claims, LoginRequest, RegisterRequest, UserInfo};
use argon2::{
    Argon2,
    password_hash::{PasswordHash, PasswordHasher, PasswordVerifier, SaltString},
};
use async_trait::async_trait;
use chrono::{Duration, Utc};
use jsonwebtoken::{EncodingKey, Header, encode};
use rand_core::OsRng;
use sea_orm::prelude::Uuid;
// 注意：Serialize和Deserialize已移除，因为Claims已迁移到app_interfaces crate
use std::sync::Arc;
use validator::Validate;

// 注意：Claims已迁移到app_interfaces crate

/// 认证应用服务接口
#[async_trait]
pub trait AuthApplicationService: Send + Sync {
    /// 用户注册
    async fn register(&self, request: RegisterRequest) -> Result<AuthResponse>;

    /// 用户登录
    async fn login(&self, request: LoginRequest) -> Result<AuthResponse>;

    /// 验证JWT令牌
    async fn verify_token(&self, token: &str) -> Result<Claims>;

    /// 刷新JWT令牌
    async fn refresh_token(&self, token: &str) -> Result<AuthResponse>;
}

/// 认证应用服务实现
pub struct AuthApplicationServiceImpl {
    user_repository: Arc<dyn UserRepositoryContract>,
    user_domain_service: Arc<dyn UserDomainService>,
    jwt_secret: String,
}

impl AuthApplicationServiceImpl {
    /// 创建新的认证应用服务实例
    pub fn new(
        user_repository: Arc<dyn UserRepositoryContract>,
        user_domain_service: Arc<dyn UserDomainService>,
        jwt_secret: String,
    ) -> Self {
        Self {
            user_repository,
            user_domain_service,
            jwt_secret,
        }
    }

    /// 哈希密码
    async fn hash_password(&self, password: &str) -> Result<String> {
        let salt = SaltString::generate(&mut OsRng);
        let argon2 = Argon2::default();

        let password_hash = argon2
            .hash_password(password.as_bytes(), &salt)
            .map_err(|e| AppError::InternalServerError(format!("密码哈希失败: {e}")))?;

        Ok(password_hash.to_string())
    }

    /// 验证密码
    async fn verify_password(&self, password: &str, hash: &str) -> Result<bool> {
        let parsed_hash = PasswordHash::new(hash)
            .map_err(|e| AppError::InternalServerError(format!("密码哈希解析失败: {e}")))?;

        let argon2 = Argon2::default();
        Ok(argon2
            .verify_password(password.as_bytes(), &parsed_hash)
            .is_ok())
    }

    /// 生成JWT令牌
    async fn generate_token(&self, user: &User) -> Result<String> {
        let now = Utc::now();
        let exp = now + Duration::hours(24); // 24小时过期

        let claims = Claims {
            sub: user.id.to_string(),
            username: user.username.clone(),
            exp: exp.timestamp(),
            iat: now.timestamp(),
        };

        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.jwt_secret.as_ref()),
        )
        .map_err(|e| AppError::InternalServerError(format!("JWT令牌生成失败: {e}")))?;

        Ok(token)
    }

    /// 创建认证响应
    async fn create_auth_response(&self, user: &User) -> Result<AuthResponse> {
        let token = self.generate_token(user).await?;

        Ok(AuthResponse {
            access_token: token,
            token_type: "Bearer".to_string(),
            expires_in: 24 * 60 * 60, // 24小时（秒）
            user: UserInfo {
                id: user.id,
                username: user.username.clone(),
                email: user.email.clone().unwrap_or_default(),
                created_at: user.created_at,
            },
        })
    }
}

#[async_trait]
impl AuthApplicationService for AuthApplicationServiceImpl {
    /// 用户注册
    async fn register(&self, request: RegisterRequest) -> Result<AuthResponse> {
        // 验证输入
        request
            .validate()
            .map_err(|e| AppError::ValidationError(format!("输入验证失败: {e}")))?;

        // 验证密码确认
        if request.password != request.confirm_password {
            return Err(AppError::ValidationError("密码确认不匹配".to_string()));
        }

        // 检查用户名是否已存在
        if self
            .user_repository
            .find_by_username(&request.username)
            .await?
            .is_some()
        {
            return Err(AppError::UserAlreadyExists("用户名已存在".to_string()));
        }

        // 哈希密码
        let password_hash = self.hash_password(&request.password).await?;

        // 创建用户实体（不使用邮箱，符合学习项目规范）
        let user = User::new(request.username, None, password_hash)
            .map_err(|e| AppError::ValidationError(format!("用户实体创建失败: {e}")))?;

        // 通过领域服务创建用户
        let user = self.user_domain_service.create_user(user).await?;

        // 保存用户
        let saved_user = self.user_repository.create(user).await?;

        // 生成认证响应
        self.create_auth_response(&saved_user).await
    }

    /// 用户登录
    async fn login(&self, request: LoginRequest) -> Result<AuthResponse> {
        // 验证输入
        request
            .validate()
            .map_err(|e| AppError::ValidationError(format!("输入验证失败: {e}")))?;

        // 查找用户
        let user = self
            .user_repository
            .find_by_username(&request.username)
            .await?
            .ok_or_else(|| AppError::InvalidCredentials)?;

        // 验证密码
        let is_valid = self
            .verify_password(&request.password, &user.password_hash)
            .await?;
        if !is_valid {
            return Err(AppError::InvalidCredentials);
        }

        // 生成认证响应
        self.create_auth_response(&user).await
    }

    /// 验证JWT令牌
    async fn verify_token(&self, token: &str) -> Result<Claims> {
        use jsonwebtoken::{DecodingKey, Validation, decode};

        let token_data = decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.jwt_secret.as_ref()),
            &Validation::default(),
        )
        .map_err(|e| AppError::InvalidToken(format!("令牌验证失败: {e}")))?;

        Ok(token_data.claims)
    }

    /// 刷新JWT令牌
    async fn refresh_token(&self, token: &str) -> Result<AuthResponse> {
        // 验证当前令牌
        let claims = self.verify_token(token).await?;

        // 查找用户
        let user_id = Uuid::parse_str(&claims.sub)
            .map_err(|e| AppError::InternalServerError(format!("用户ID解析失败: {e}")))?;

        let user = self
            .user_repository
            .find_by_id(user_id)
            .await?
            .ok_or_else(|| AppError::NotFound("用户不存在".to_string()))?;

        // 生成新的认证响应
        self.create_auth_response(&user).await
    }
}
