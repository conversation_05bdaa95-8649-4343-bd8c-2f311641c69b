//! # 聊天应用服务
//!
//! 聊天相关的业务用例实现，包括：
//! - 聊天室管理
//! - 消息发送和接收
//! - 用户会话管理
//! - 实时通信编排

use app_common::error::{AppError, Result};
use app_domain::entities::{
    chat::{GetGlobalChatRoomHistoryRequest, SearchGlobalChatRoomMessagesRequest},
    message::{Message, MessageCreateParams, MessageType},
    user_session::UserSession,
};
use app_domain::repositories::chat_repository::ChatRepositoryContract;
use app_domain::services::chat_service::ChatDomainService;
use app_interfaces::UserSessionResponse;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sea_orm::prelude::Uuid;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use validator::Validate;

/// 发送消息到全局聊天室的请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct SendMessageToGlobalRoomRequest {
    /// 消息内容
    #[validate(length(min = 1, max = 10000, message = "消息内容长度必须在1-10000个字符之间"))]
    pub content: String,

    /// 消息类型
    pub message_type: MessageType,

    /// 回复的消息ID（可选）
    pub reply_to_id: Option<Uuid>,

    /// 消息元数据（可选）
    pub metadata: Option<String>,

    /// 消息优先级（可选，0-9）
    #[validate(range(min = 0, max = 9, message = "消息优先级必须在0-9之间"))]
    pub priority: Option<i32>,

    /// 消息过期时间（可选）
    pub expires_at: Option<DateTime<Utc>>,
}

/// 消息响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct MessageResponse {
    pub id: Uuid,
    pub content: String,
    pub message_type: MessageType,
    pub sender_id: Uuid,
    pub chat_room_id: Uuid,
    pub reply_to_id: Option<Uuid>,
    pub metadata: Option<String>,
    pub priority: i32,
    pub is_pinned: bool,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// 聊天应用服务接口
///
/// 定义聊天相关的业务用例操作
#[async_trait]
pub trait ChatApplicationService: Send + Sync {
    /// 获取聊天室在线用户
    async fn get_online_users(
        &self,
        room_id: Uuid,
        user_id: Uuid,
    ) -> Result<Vec<UserSessionResponse>>;

    /// 发送消息到全局聊天室
    ///
    /// # 参数
    /// - `user_id`: 发送者用户ID
    /// - `request`: 发送消息请求
    ///
    /// # 返回
    /// - `Ok(MessageResponse)`: 发送成功，返回消息响应
    /// - `Err(...)`: 发送失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户必须已认证
    /// - 消息内容必须通过验证
    /// - 全局聊天室必须启用
    async fn send_message_to_global_room(
        &self,
        user_id: Uuid,
        request: SendMessageToGlobalRoomRequest,
    ) -> Result<MessageResponse>;

    /// 在全局聊天室中搜索消息
    ///
    /// # 参数
    /// - `user_id`: 请求用户ID
    /// - `request`: 搜索消息请求
    ///
    /// # 返回
    /// - `Ok(Vec<MessageResponse>)`: 搜索成功，返回匹配的消息列表
    /// - `Err(...)`: 搜索失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户必须已认证
    /// - 搜索关键词必须通过验证
    /// - 支持按时间范围和发送者过滤
    async fn search_messages_in_global_room(
        &self,
        user_id: Uuid,
        request: SearchGlobalChatRoomMessagesRequest,
    ) -> Result<Vec<MessageResponse>>;

    /// 获取全局聊天室历史消息
    ///
    /// # 参数
    /// - `user_id`: 请求用户ID
    /// - `request`: 获取历史消息请求
    ///
    /// # 返回
    /// - `Ok(Vec<MessageResponse>)`: 获取成功，返回历史消息列表
    /// - `Err(...)`: 获取失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户必须已认证
    /// - 支持分页查询
    /// - 按时间倒序返回消息
    async fn get_global_room_history(
        &self,
        user_id: Uuid,
        request: GetGlobalChatRoomHistoryRequest,
    ) -> Result<Vec<MessageResponse>>;
}

/// 聊天应用服务实现
pub struct ChatApplicationServiceImpl {
    /// 聊天仓库
    chat_repository: Arc<dyn ChatRepositoryContract>,
    /// 聊天领域服务
    chat_domain_service: Arc<dyn ChatDomainService>,
    /// 用户仓库
    user_repository: Arc<dyn app_domain::repositories::UserRepositoryContract>,
}

impl ChatApplicationServiceImpl {
    /// 创建新的聊天应用服务实例
    pub fn new(
        chat_repository: Arc<dyn ChatRepositoryContract>,
        chat_domain_service: Arc<dyn ChatDomainService>,
        user_repository: Arc<dyn app_domain::repositories::UserRepositoryContract>,
    ) -> Self {
        Self {
            chat_repository,
            chat_domain_service,
            user_repository,
        }
    }

    /// 将用户会话实体转换为响应DTO
    fn user_session_to_response(&self, session: UserSession) -> UserSessionResponse {
        UserSessionResponse {
            id: session.id,
            user_id: session.user_id,
            username: "Unknown".to_string(), // 需要从数据库查询
            room_id: session.current_chat_room_id.unwrap_or_default(),
            connected_at: session.created_at,
            last_activity: session.last_activity_at,
            is_online: session.is_online(),
            device_type: Some("web".to_string()), // 默认设备类型
            ip_address: None,
        }
    }

    /// 将消息实体转换为响应DTO
    fn message_to_response(&self, message: Message) -> MessageResponse {
        MessageResponse {
            id: message.id,
            content: message.content,
            message_type: message.message_type,
            sender_id: message.sender_id,
            chat_room_id: message.chat_room_id,
            reply_to_id: message.reply_to_id,
            metadata: message.metadata,
            priority: message.priority,
            is_pinned: message.is_pinned,
            expires_at: message.expires_at,
            created_at: message.created_at,
            updated_at: message.updated_at,
        }
    }

    /// 获取全局聊天室ID（如果不存在则自动创建）
    ///
    /// # 返回
    /// - `Result<Uuid>`: 成功返回全局聊天室ID，失败返回错误信息
    ///
    /// # 设计说明
    /// 实现优雅的回退机制：如果全局聊天室不存在，自动创建一个，
    /// 避免因为数据库初始化问题导致的服务不可用
    async fn get_global_chat_room_id(&self) -> Result<Uuid> {
        // 尝试从仓库获取全局聊天室
        match self.chat_repository.find_global_chat_room().await? {
            Some(global_room) => {
                tracing::debug!("找到现有全局聊天室: {}", global_room.id);
                Ok(global_room.id)
            }
            None => {
                // 全局聊天室不存在，自动创建一个
                tracing::info!("全局聊天室不存在，正在自动创建...");

                // 创建或获取系统用户ID
                let system_user_id = self.get_or_create_system_user().await?;

                // 创建全局聊天室实体
                let global_room = app_domain::entities::chat_room::ChatRoom::new(
                    "全局聊天室".to_string(),
                    Some("系统自动创建的全局聊天室，所有用户都可以参与".to_string()),
                    app_domain::entities::chat_room::ChatRoomType::Public,
                    system_user_id,
                    Some(10000), // 设置较大的成员上限
                    None,
                )
                .map_err(|e| {
                    tracing::error!("创建全局聊天室实体失败: {:?}", e);
                    AppError::ValidationError(format!("创建全局聊天室实体失败: {e:?}"))
                })?;

                // 保存全局聊天室到数据库
                let created_room = self.chat_repository.create_chat_room(global_room).await?;

                tracing::info!("全局聊天室创建成功: {}", created_room.id);
                Ok(created_room.id)
            }
        }
    }

    /// 验证用户是否已认证
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Result<()>`: 成功返回空，失败返回错误信息
    async fn validate_user(&self, user_id: Uuid) -> Result<()> {
        // 检查用户是否存在
        let user_exists = self.chat_repository.user_exists(user_id).await?;
        if !user_exists {
            return Err(AppError::InvalidToken("用户未认证".to_string()));
        }
        Ok(())
    }

    /// 获取或创建系统用户
    ///
    /// # 返回
    /// - `Result<Uuid>`: 成功返回系统用户ID，失败返回错误信息
    ///
    /// # 设计说明
    /// 系统用户用于创建全局聊天室等系统级资源，避免外键约束问题
    async fn get_or_create_system_user(&self) -> Result<Uuid> {
        const SYSTEM_USERNAME: &str = "system";
        const SYSTEM_USER_ID: &str = "00000000-0000-0000-0000-000000000001"; // 使用非零UUID避免混淆

        // 尝试查找现有的系统用户
        if let Some(existing_user) = self
            .user_repository
            .find_by_username(SYSTEM_USERNAME)
            .await
            .map_err(|e| AppError::DatabaseError(format!("查询系统用户失败: {e}")))?
        {
            tracing::debug!("找到现有系统用户: {}", existing_user.id);
            return Ok(existing_user.id);
        }

        // 系统用户不存在，创建一个
        tracing::info!("系统用户不存在，正在创建...");

        let system_user_id = Uuid::parse_str(SYSTEM_USER_ID).expect("系统用户ID格式错误");

        // 创建系统用户实体
        let mut system_user = app_domain::entities::user::User::new(
            SYSTEM_USERNAME.to_string(),
            Some("system@localhost".to_string()),
            "$2b$12$system.user.password.hash".to_string(), // 系统用户使用固定的密码哈希
        )
        .map_err(|e| {
            tracing::error!("创建系统用户实体失败: {:?}", e);
            AppError::ValidationError(format!("创建系统用户实体失败: {e:?}"))
        })?;

        // 设置固定的系统用户ID
        system_user.id = system_user_id;

        // 保存系统用户到数据库
        let created_user = self
            .user_repository
            .create(system_user)
            .await
            .map_err(|e| AppError::DatabaseError(format!("创建系统用户失败: {e}")))?;

        tracing::info!("系统用户创建成功: {}", created_user.id);
        Ok(created_user.id)
    }
}

#[async_trait]
impl ChatApplicationService for ChatApplicationServiceImpl {
    /// 获取聊天室在线用户业务用例
    async fn get_online_users(
        &self,
        room_id: Uuid,
        user_id: Uuid,
    ) -> Result<Vec<UserSessionResponse>> {
        // 1. 验证用户是否在聊天室中
        let _session = self
            .chat_repository
            .find_user_session(room_id, user_id)
            .await?
            .ok_or_else(|| AppError::Forbidden("用户不在聊天室中".to_string()))?;

        // 2. 获取在线用户列表
        let sessions = self.chat_repository.find_room_online_users(room_id).await?;
        Ok(sessions
            .into_iter()
            .map(|s| self.user_session_to_response(s))
            .collect())
    }

    /// 发送消息到全局聊天室业务用例
    async fn send_message_to_global_room(
        &self,
        user_id: Uuid,
        request: SendMessageToGlobalRoomRequest,
    ) -> Result<MessageResponse> {
        // 1. 验证请求数据
        request
            .validate()
            .map_err(|e| AppError::ValidationError(format!("请求验证失败: {e}")))?;

        // 2. 验证用户是否已认证
        self.validate_user(user_id).await?;

        // 3. 获取全局聊天室ID
        let global_room_id = self.get_global_chat_room_id().await?;

        // 4. 创建消息实体
        let message_params = MessageCreateParams {
            content: request.content,
            message_type: request.message_type,
            sender_id: user_id,
            chat_room_id: global_room_id,
            reply_to_id: request.reply_to_id,
            metadata: request.metadata,
            priority: request.priority,
            expires_at: request.expires_at,
        };

        let message = Message::new(message_params)
            .map_err(|e| AppError::ValidationError(format!("消息创建失败: {e}")))?;

        // 5. 通过领域服务发送消息
        let sent_message = self
            .chat_domain_service
            .send_message(message, user_id)
            .await?;

        // 6. 保存消息到仓库
        let saved_message = self.chat_repository.create_message(sent_message).await?;

        // 7. 转换为响应DTO
        Ok(self.message_to_response(saved_message))
    }

    /// 在全局聊天室中搜索消息业务用例
    async fn search_messages_in_global_room(
        &self,
        user_id: Uuid,
        request: SearchGlobalChatRoomMessagesRequest,
    ) -> Result<Vec<MessageResponse>> {
        // 1. 验证请求数据
        request
            .validate()
            .map_err(|e| AppError::ValidationError(format!("请求验证失败: {e}")))?;

        // 2. 验证用户是否已认证
        self.validate_user(user_id).await?;

        // 3. 获取全局聊天室ID
        let global_room_id = self.get_global_chat_room_id().await?;

        // 4. 从仓库搜索消息
        let messages = self
            .chat_repository
            .search_messages_in_room(
                global_room_id,
                request.query,
                request.limit.unwrap_or(50),
                request.start_time,
                request.end_time,
                request.sender_id,
            )
            .await?;

        // 5. 转换为响应DTO列表
        Ok(messages
            .into_iter()
            .map(|m| self.message_to_response(m))
            .collect())
    }

    /// 获取全局聊天室历史消息业务用例
    async fn get_global_room_history(
        &self,
        user_id: Uuid,
        request: GetGlobalChatRoomHistoryRequest,
    ) -> Result<Vec<MessageResponse>> {
        // 1. 验证请求数据
        request
            .validate()
            .map_err(|e| AppError::ValidationError(format!("请求验证失败: {e}")))?;

        // 2. 验证用户是否已认证
        self.validate_user(user_id).await?;

        // 3. 获取全局聊天室ID
        let global_room_id = self.get_global_chat_room_id().await?;

        // 4. 从仓库获取历史消息
        let messages = self
            .chat_repository
            .get_room_message_history(
                global_room_id,
                request.limit.unwrap_or(50),
                request.before,
                request.after,
            )
            .await?;

        // 5. 转换为响应DTO列表
        Ok(messages
            .into_iter()
            .map(|m| self.message_to_response(m))
            .collect())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use app_common::error::{AppError, Result};
    use app_domain::entities::{
        ChatRoom, User,
        chat::GlobalChatRoom,
        message::{Message, MessageType},
        user_session::UserSession,
    };
    use app_domain::repositories::{
        chat_repository::ChatRepositoryContract, message_repository::MessageRepositoryContract,
        user_repository::UserRepositoryContract,
    };
    use async_trait::async_trait;
    use sea_orm::DbErr;
    use std::sync::Arc;
    use uuid::Uuid;

    /// 模拟聊天仓库实现
    struct MockChatRepository {
        pub global_room: Option<GlobalChatRoom>,
        pub user_exists: bool,
        pub messages: Vec<Message>,
    }

    impl MockChatRepository {
        fn new() -> Self {
            let global_room = GlobalChatRoom::new(
                "全局聊天室".to_string(),
                Some("测试全局聊天室".to_string()),
                Some(1000),
                Some(30),
                None,
            )
            .unwrap();

            Self {
                global_room: Some(global_room),
                user_exists: true,
                messages: Vec::new(),
            }
        }
    }

    #[async_trait]
    impl ChatRepositoryContract for MockChatRepository {
        async fn create_chat_room(&self, chat_room: ChatRoom) -> Result<ChatRoom> {
            Ok(chat_room)
        }

        async fn find_chat_room_by_id(&self, _room_id: Uuid) -> Result<Option<ChatRoom>> {
            Ok(None)
        }

        async fn find_chat_room_by_name(&self, _name: &str) -> Result<Option<ChatRoom>> {
            Ok(None)
        }

        async fn find_user_chat_rooms(&self, _user_id: Uuid) -> Result<Vec<ChatRoom>> {
            Ok(Vec::new())
        }

        async fn update_chat_room(&self, chat_room: ChatRoom) -> Result<ChatRoom> {
            Ok(chat_room)
        }

        async fn delete_chat_room(&self, _room_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn create_message(&self, message: Message) -> Result<Message> {
            Ok(message)
        }

        async fn find_message_by_id(&self, _message_id: Uuid) -> Result<Option<Message>> {
            Ok(None)
        }

        async fn find_room_messages(
            &self,
            _room_id: Uuid,
            _limit: u32,
            _before: Option<DateTime<Utc>>,
        ) -> Result<Vec<Message>> {
            Ok(self.messages.clone())
        }

        async fn delete_message(&self, _message_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn create_user_session(&self, session: UserSession) -> Result<UserSession> {
            Ok(session)
        }

        async fn find_user_session(
            &self,
            _room_id: Uuid,
            _user_id: Uuid,
        ) -> Result<Option<UserSession>> {
            Ok(None)
        }

        async fn find_room_online_users(&self, _room_id: Uuid) -> Result<Vec<UserSession>> {
            Ok(Vec::new())
        }

        async fn update_user_session(&self, session: UserSession) -> Result<UserSession> {
            Ok(session)
        }

        async fn delete_user_session(&self, _session_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn set_user_offline(&self, _room_id: Uuid, _user_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn find_global_chat_room(&self) -> Result<Option<GlobalChatRoom>> {
            Ok(self.global_room.clone())
        }

        async fn create_global_chat_room(
            &self,
            global_room: GlobalChatRoom,
        ) -> Result<GlobalChatRoom> {
            Ok(global_room)
        }

        async fn update_global_chat_room(
            &self,
            global_room: GlobalChatRoom,
        ) -> Result<GlobalChatRoom> {
            Ok(global_room)
        }

        async fn search_messages_in_room(
            &self,
            _room_id: Uuid,
            _query: String,
            _limit: u32,
            _start_time: Option<DateTime<Utc>>,
            _end_time: Option<DateTime<Utc>>,
            _sender_id: Option<Uuid>,
        ) -> Result<Vec<Message>> {
            Ok(self.messages.clone())
        }

        async fn get_room_message_history(
            &self,
            _room_id: Uuid,
            _limit: u32,
            _before: Option<DateTime<Utc>>,
            _after: Option<DateTime<Utc>>,
        ) -> Result<Vec<Message>> {
            Ok(self.messages.clone())
        }

        async fn user_exists(&self, _user_id: Uuid) -> Result<bool> {
            Ok(self.user_exists)
        }
    }

    /// 模拟用户仓库实现
    struct MockUserRepository;

    impl MockUserRepository {
        fn new() -> Self {
            Self
        }
    }

    #[async_trait]
    impl UserRepositoryContract for MockUserRepository {
        async fn create(&self, user: User) -> std::result::Result<User, DbErr> {
            Ok(user)
        }

        async fn find_by_id(&self, _id: Uuid) -> std::result::Result<Option<User>, DbErr> {
            Ok(None)
        }

        async fn find_by_username(
            &self,
            username: &str,
        ) -> std::result::Result<Option<User>, DbErr> {
            if username == "system" {
                // 返回一个模拟的系统用户
                let system_user = User::new(
                    "system".to_string(),
                    Some("system@localhost".to_string()),
                    "$2b$12$system.user.password.hash".to_string(),
                )
                .unwrap();
                Ok(Some(system_user))
            } else {
                Ok(None)
            }
        }

        async fn update(&self, user: User) -> std::result::Result<User, DbErr> {
            Ok(user)
        }

        async fn delete(&self, _id: Uuid) -> std::result::Result<u64, DbErr> {
            Ok(1)
        }

        async fn count_all(&self) -> std::result::Result<u64, DbErr> {
            Ok(0)
        }
    }

    /// 模拟聊天领域服务实现
    struct MockChatDomainService;

    #[async_trait]
    impl ChatDomainService for MockChatDomainService {
        async fn create_chat_room(
            &self,
            chat_room: ChatRoom,
            _creator_id: Uuid,
        ) -> Result<ChatRoom> {
            Ok(chat_room)
        }

        async fn join_chat_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn send_message(&self, message: Message, _sender_id: Uuid) -> Result<Message> {
            Ok(message)
        }

        async fn get_message_history(
            &self,
            _room_id: Uuid,
            _user_id: Uuid,
            _limit: u32,
            _before: Option<DateTime<Utc>>,
        ) -> Result<Vec<Message>> {
            Ok(Vec::new())
        }

        async fn get_online_users(
            &self,
            _room_id: Uuid,
            _user_id: Uuid,
        ) -> Result<Vec<UserSession>> {
            Ok(Vec::new())
        }

        async fn create_user_session(&self, session: UserSession) -> Result<UserSession> {
            Ok(session)
        }

        async fn leave_chat_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn update_session_status(&self, _session_id: Uuid, _is_active: bool) -> Result<()> {
            Ok(())
        }

        async fn delete_user_session(&self, _session_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn is_user_in_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<bool> {
            Ok(true)
        }

        async fn get_chat_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<Option<ChatRoom>> {
            Ok(None)
        }
    }

    /// 创建测试用的聊天应用服务
    fn create_test_service() -> ChatApplicationServiceImpl {
        let repository = Arc::new(MockChatRepository::new());
        let domain_service = Arc::new(MockChatDomainService);
        let user_repository = Arc::new(MockUserRepository::new());
        ChatApplicationServiceImpl::new(repository, domain_service, user_repository)
    }

    /// 测试发送消息到全局聊天室 - 成功场景
    #[tokio::test]
    async fn test_send_message_to_global_room_success() {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let request = SendMessageToGlobalRoomRequest {
            content: "测试消息".to_string(),
            message_type: MessageType::Text,
            reply_to_id: None,
            metadata: None,
            priority: Some(5),
            expires_at: None,
        };

        let result = service.send_message_to_global_room(user_id, request).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        assert_eq!(response.content, "测试消息");
        assert_eq!(response.message_type, MessageType::Text);
        assert_eq!(response.sender_id, user_id);
        assert_eq!(response.priority, 5);
    }

    /// 测试发送消息到全局聊天室 - 验证失败场景
    #[tokio::test]
    async fn test_send_message_to_global_room_validation_error() {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let request = SendMessageToGlobalRoomRequest {
            content: "".to_string(), // 空内容应该验证失败
            message_type: MessageType::Text,
            reply_to_id: None,
            metadata: None,
            priority: Some(5),
            expires_at: None,
        };

        let result = service.send_message_to_global_room(user_id, request).await;
        assert!(result.is_err());

        if let Err(AppError::ValidationError(_)) = result {
            // 验证错误是预期的
        } else {
            panic!("期望验证错误");
        }
    }

    /// 测试在全局聊天室中搜索消息 - 成功场景
    #[tokio::test]
    async fn test_search_messages_in_global_room_success() {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let request = SearchGlobalChatRoomMessagesRequest {
            query: "测试".to_string(),
            limit: Some(10),
            start_time: None,
            end_time: None,
            sender_id: None,
        };

        let result = service
            .search_messages_in_global_room(user_id, request)
            .await;
        assert!(result.is_ok());

        let messages = result.unwrap();
        assert!(messages.is_empty()); // 模拟仓库返回空列表
    }

    /// 测试获取全局聊天室历史消息 - 成功场景
    #[tokio::test]
    async fn test_get_global_room_history_success() {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let request = GetGlobalChatRoomHistoryRequest {
            limit: Some(20),
            before: None,
            after: None,
        };

        let result = service.get_global_room_history(user_id, request).await;
        assert!(result.is_ok());

        let messages = result.unwrap();
        assert!(messages.is_empty()); // 模拟仓库返回空列表
    }
}
