//! # 聊天应用服务测试
//!
//! 测试ChatApplicationService的业务逻辑和三个核心方法

#[cfg(test)]
mod tests {
    use super::*;
    use app_common::error::AppError;
    use app_domain::entities::{
        chat::GlobalChatRoom,
        message::{Message, MessageType, MessageStatus},
        user_session::UserSession,
    };
    use app_domain::repositories::chat_repository::ChatRepositoryContract;
    use app_domain::services::chat_service::ChatDomainService;
    use async_trait::async_trait;
    use chrono::Utc;
    use std::sync::Arc;
    use uuid::Uuid;

    /// 模拟聊天仓库实现
    struct MockChatRepository {
        pub global_room: Option<GlobalChatRoom>,
        pub user_exists: bool,
        pub messages: Vec<Message>,
    }

    impl MockChatRepository {
        fn new() -> Self {
            let global_room = GlobalChatRoom::new(
                "全局聊天室".to_string(),
                Some("测试全局聊天室".to_string()),
                Some(1000),
                Some(30),
                None,
            ).unwrap();

            Self {
                global_room: Some(global_room),
                user_exists: true,
                messages: Vec::new(),
            }
        }
    }

    #[async_trait]
    impl ChatRepositoryContract for MockChatRepository {
        async fn create_chat_room(&self, chat_room: ChatRoom) -> Result<ChatRoom> {
            Ok(chat_room)
        }

        async fn find_chat_room_by_id(&self, _room_id: Uuid) -> Result<Option<ChatRoom>> {
            Ok(None)
        }

        async fn find_chat_room_by_name(&self, _name: &str) -> Result<Option<ChatRoom>> {
            Ok(None)
        }

        async fn find_user_chat_rooms(&self, _user_id: Uuid) -> Result<Vec<ChatRoom>> {
            Ok(Vec::new())
        }

        async fn update_chat_room(&self, chat_room: ChatRoom) -> Result<ChatRoom> {
            Ok(chat_room)
        }

        async fn delete_chat_room(&self, _room_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn create_message(&self, message: Message) -> Result<Message> {
            Ok(message)
        }

        async fn find_message_by_id(&self, _message_id: Uuid) -> Result<Option<Message>> {
            Ok(None)
        }

        async fn find_room_messages(
            &self,
            _room_id: Uuid,
            _limit: u32,
            _before: Option<DateTime<Utc>>,
        ) -> Result<Vec<Message>> {
            Ok(self.messages.clone())
        }

        async fn delete_message(&self, _message_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn create_user_session(&self, session: UserSession) -> Result<UserSession> {
            Ok(session)
        }

        async fn find_user_session(&self, _room_id: Uuid, _user_id: Uuid) -> Result<Option<UserSession>> {
            Ok(None)
        }

        async fn find_room_online_users(&self, _room_id: Uuid) -> Result<Vec<UserSession>> {
            Ok(Vec::new())
        }

        async fn update_user_session(&self, session: UserSession) -> Result<UserSession> {
            Ok(session)
        }

        async fn delete_user_session(&self, _session_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn set_user_offline(&self, _room_id: Uuid, _user_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn find_global_chat_room(&self) -> Result<Option<GlobalChatRoom>> {
            Ok(self.global_room.clone())
        }

        async fn create_global_chat_room(&self, global_room: GlobalChatRoom) -> Result<GlobalChatRoom> {
            Ok(global_room)
        }

        async fn update_global_chat_room(&self, global_room: GlobalChatRoom) -> Result<GlobalChatRoom> {
            Ok(global_room)
        }

        async fn search_messages_in_room(
            &self,
            _room_id: Uuid,
            _query: String,
            _limit: u32,
            _start_time: Option<DateTime<Utc>>,
            _end_time: Option<DateTime<Utc>>,
            _sender_id: Option<Uuid>,
        ) -> Result<Vec<Message>> {
            Ok(self.messages.clone())
        }

        async fn get_room_message_history(
            &self,
            _room_id: Uuid,
            _limit: u32,
            _before: Option<DateTime<Utc>>,
            _after: Option<DateTime<Utc>>,
        ) -> Result<Vec<Message>> {
            Ok(self.messages.clone())
        }

        async fn user_exists(&self, _user_id: Uuid) -> Result<bool> {
            Ok(self.user_exists)
        }
    }

    /// 模拟聊天领域服务实现
    struct MockChatDomainService;

    #[async_trait]
    impl ChatDomainService for MockChatDomainService {
        async fn create_chat_room(&self, chat_room: ChatRoom, _creator_id: Uuid) -> Result<ChatRoom> {
            Ok(chat_room)
        }

        async fn join_chat_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<()> {
            Ok(())
        }

        async fn send_message(&self, message: Message, _sender_id: Uuid) -> Result<Message> {
            Ok(message)
        }

        async fn get_message_history(
            &self,
            _room_id: Uuid,
            _user_id: Uuid,
            _limit: u32,
            _before: Option<DateTime<Utc>>,
        ) -> Result<Vec<Message>> {
            Ok(Vec::new())
        }

        async fn get_online_users(&self, _room_id: Uuid, _user_id: Uuid) -> Result<Vec<UserSession>> {
            Ok(Vec::new())
        }

        async fn create_user_session(&self, session: UserSession) -> Result<UserSession> {
            Ok(session)
        }

        async fn get_chat_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<Option<ChatRoom>> {
            Ok(None)
        }
    }

    /// 创建测试用的聊天应用服务
    fn create_test_service() -> ChatApplicationServiceImpl {
        let repository = Arc::new(MockChatRepository::new());
        let domain_service = Arc::new(MockChatDomainService);
        ChatApplicationServiceImpl::new(repository, domain_service)
    }

    /// 测试发送消息到全局聊天室 - 成功场景
    #[tokio::test]
    async fn test_send_message_to_global_room_success() {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let request = SendMessageToGlobalRoomRequest {
            content: "测试消息".to_string(),
            message_type: MessageType::Text,
            reply_to_id: None,
            metadata: None,
            priority: Some(5),
            expires_at: None,
        };

        let result = service.send_message_to_global_room(user_id, request).await;
        assert!(result.is_ok());

        let response = result.unwrap();
        assert_eq!(response.content, "测试消息");
        assert_eq!(response.message_type, MessageType::Text);
        assert_eq!(response.sender_id, user_id);
        assert_eq!(response.priority, 5);
    }

    /// 测试发送消息到全局聊天室 - 验证失败场景
    #[tokio::test]
    async fn test_send_message_to_global_room_validation_error() {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let request = SendMessageToGlobalRoomRequest {
            content: "".to_string(), // 空内容应该验证失败
            message_type: MessageType::Text,
            reply_to_id: None,
            metadata: None,
            priority: Some(5),
            expires_at: None,
        };

        let result = service.send_message_to_global_room(user_id, request).await;
        assert!(result.is_err());
        
        if let Err(AppError::ValidationError(_)) = result {
            // 验证错误是预期的
        } else {
            panic!("期望验证错误");
        }
    }

    /// 测试在全局聊天室中搜索消息 - 成功场景
    #[tokio::test]
    async fn test_search_messages_in_global_room_success() {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let request = SearchGlobalChatRoomMessagesRequest {
            query: "测试".to_string(),
            limit: Some(10),
            start_time: None,
            end_time: None,
            sender_id: None,
        };

        let result = service.search_messages_in_global_room(user_id, request).await;
        assert!(result.is_ok());

        let messages = result.unwrap();
        assert!(messages.is_empty()); // 模拟仓库返回空列表
    }

    /// 测试获取全局聊天室历史消息 - 成功场景
    #[tokio::test]
    async fn test_get_global_room_history_success() {
        let service = create_test_service();
        let user_id = Uuid::new_v4();
        let request = GetGlobalChatRoomHistoryRequest {
            limit: Some(20),
            before: None,
            after: None,
        };

        let result = service.get_global_room_history(user_id, request).await;
        assert!(result.is_ok());

        let messages = result.unwrap();
        assert!(messages.is_empty()); // 模拟仓库返回空列表
    }
}
