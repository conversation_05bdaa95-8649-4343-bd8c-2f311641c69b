//! # WebSocket连接管理器应用服务
//!
//! WebSocket连接管理相关的业务用例实现，包括：
//! - WebSocket连接生命周期管理
//! - 用户连接状态跟踪
//! - 连接统计和监控
//! - 消息广播协调

use app_common::error::Result;
use axum::extract::ws::Message;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use tokio::sync::{RwLock, mpsc};
use uuid::Uuid;

/// 连接唯一标识符
pub type ConnectionId = Uuid;

/// WebSocket连接统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketStats {
    /// 当前活跃连接数
    pub active_connections: u64,
    /// 历史总连接数
    pub total_connections: u64,
    /// 当前在线用户数（去重）
    pub unique_users: u64,
    /// 总发送消息数
    pub total_messages_sent: u64,
    /// 总接收消息数
    pub total_messages_received: u64,
    /// 平均连接持续时间（秒）
    pub average_connection_duration: f64,
    /// 最长连接持续时间（秒）
    pub max_connection_duration: f64,
    /// 断线重连次数
    pub reconnection_count: u64,
    /// 消息吞吐量（每分钟）
    pub messages_per_minute: f64,
    /// 连接成功率（百分比）
    pub connection_success_rate: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

/// 连接质量指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionQuality {
    /// 连接稳定性评分（0-100）
    pub stability_score: f64,
    /// 平均响应时间（毫秒）
    pub average_response_time: f64,
    /// 错误率（百分比）
    pub error_rate: f64,
    /// 心跳丢失率（百分比）
    pub heartbeat_loss_rate: f64,
}

/// 实时消息统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageThroughput {
    /// 当前每秒消息数
    pub messages_per_second: f64,
    /// 当前每分钟消息数
    pub messages_per_minute: f64,
    /// 峰值每秒消息数
    pub peak_messages_per_second: f64,
    /// 平均消息大小（字节）
    pub average_message_size: f64,
    /// 总传输字节数
    pub total_bytes_transferred: u64,
}

/// 用户连接信息结构体
#[derive(Debug)]
pub struct UserConnection {
    /// 连接ID
    pub connection_id: ConnectionId,
    /// 用户ID
    pub user_id: Uuid,
    /// 用户名
    pub username: String,
    /// 连接建立时间
    pub connected_at: DateTime<Utc>,
    /// 最后活跃时间
    pub last_activity: DateTime<Utc>,
    /// 消息发送器
    pub sender: mpsc::UnboundedSender<Message>,
    /// 发送消息计数
    pub messages_sent: AtomicU64,
    /// 接收消息计数
    pub messages_received: AtomicU64,
    /// 连接质量指标
    pub quality: Arc<RwLock<ConnectionQuality>>,
}

impl UserConnection {
    /// 创建新的用户连接
    pub fn new(user_id: Uuid, username: String, sender: mpsc::UnboundedSender<Message>) -> Self {
        let now = Utc::now();
        Self {
            connection_id: Uuid::new_v4(),
            user_id,
            username,
            connected_at: now,
            last_activity: now,
            sender,
            messages_sent: AtomicU64::new(0),
            messages_received: AtomicU64::new(0),
            quality: Arc::new(RwLock::new(ConnectionQuality {
                stability_score: 100.0,
                average_response_time: 0.0,
                error_rate: 0.0,
                heartbeat_loss_rate: 0.0,
            })),
        }
    }

    /// 更新最后活跃时间
    pub fn update_activity(&mut self) {
        self.last_activity = Utc::now();
    }

    /// 增加发送消息计数
    pub fn increment_sent(&self) {
        self.messages_sent.fetch_add(1, Ordering::Relaxed);
    }

    /// 增加接收消息计数
    pub fn increment_received(&self) {
        self.messages_received.fetch_add(1, Ordering::Relaxed);
    }

    /// 获取连接持续时间（秒）
    pub fn connection_duration(&self) -> f64 {
        (Utc::now() - self.connected_at).num_seconds() as f64
    }
}

/// WebSocket连接管理器
pub struct ConnectionManager {
    /// 活跃连接映射 (连接ID -> 用户连接)
    connections: Arc<RwLock<HashMap<ConnectionId, UserConnection>>>,
    /// 用户连接映射 (用户ID -> 连接ID列表)
    user_connections: Arc<RwLock<HashMap<Uuid, Vec<ConnectionId>>>>,
    /// 统计计数器
    stats: Arc<RwLock<WebSocketStats>>,
    /// 总连接计数器
    total_connections: AtomicU64,
    /// 重连计数器
    reconnection_count: AtomicU64,
}

impl ConnectionManager {
    /// 创建新的连接管理器
    pub fn new() -> Self {
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            user_connections: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(WebSocketStats {
                active_connections: 0,
                total_connections: 0,
                unique_users: 0,
                total_messages_sent: 0,
                total_messages_received: 0,
                average_connection_duration: 0.0,
                max_connection_duration: 0.0,
                reconnection_count: 0,
                messages_per_minute: 0.0,
                connection_success_rate: 100.0,
                last_updated: Utc::now(),
            })),
            total_connections: AtomicU64::new(0),
            reconnection_count: AtomicU64::new(0),
        }
    }

    /// 添加新连接
    pub async fn add_connection(
        &self,
        user_id: Uuid,
        username: String,
        sender: mpsc::UnboundedSender<Message>,
    ) -> Result<ConnectionId> {
        let connection = UserConnection::new(user_id, username, sender);
        let connection_id = connection.connection_id;

        // 添加到连接映射
        {
            let mut connections = self.connections.write().await;
            connections.insert(connection_id, connection);
        }

        // 添加到用户连接映射
        {
            let mut user_connections = self.user_connections.write().await;
            user_connections
                .entry(user_id)
                .or_insert_with(Vec::new)
                .push(connection_id);
        }

        // 更新统计
        self.total_connections.fetch_add(1, Ordering::Relaxed);
        self.update_stats().await;

        tracing::info!(
            "新WebSocket连接已建立: connection_id={}, user_id={}",
            connection_id,
            user_id
        );

        Ok(connection_id)
    }

    /// 移除连接
    pub async fn remove_connection(&self, connection_id: ConnectionId) -> Result<()> {
        let user_id = {
            let mut connections = self.connections.write().await;
            if let Some(connection) = connections.remove(&connection_id) {
                connection.user_id
            } else {
                return Ok(()); // 连接不存在，直接返回
            }
        };

        // 从用户连接映射中移除
        {
            let mut user_connections = self.user_connections.write().await;
            if let Some(connection_ids) = user_connections.get_mut(&user_id) {
                connection_ids.retain(|&id| id != connection_id);
                if connection_ids.is_empty() {
                    user_connections.remove(&user_id);
                }
            }
        }

        // 更新统计
        self.update_stats().await;

        tracing::info!(
            "WebSocket连接已断开: connection_id={}, user_id={}",
            connection_id,
            user_id
        );

        Ok(())
    }

    /// 获取连接统计
    pub async fn get_stats(&self) -> WebSocketStats {
        self.stats.read().await.clone()
    }

    /// 获取在线用户列表
    pub async fn get_online_users(&self) -> Vec<(Uuid, String)> {
        let connections = self.connections.read().await;
        let mut users = HashMap::new();

        for connection in connections.values() {
            users.insert(connection.user_id, connection.username.clone());
        }

        users.into_iter().collect()
    }

    /// 广播消息给所有连接
    pub async fn broadcast_to_all(&self, message: Message) -> Result<u64> {
        let connections = self.connections.read().await;
        let mut sent_count = 0u64;

        for connection in connections.values() {
            if connection.sender.send(message.clone()).is_ok() {
                connection.increment_sent();
                sent_count += 1;
            }
        }

        // 更新统计
        {
            let mut stats = self.stats.write().await;
            stats.total_messages_sent += sent_count;
            stats.last_updated = Utc::now();
        }

        Ok(sent_count)
    }

    /// 发送消息给特定用户的所有连接
    pub async fn send_to_user(&self, user_id: Uuid, message: Message) -> Result<u64> {
        let user_connections = self.user_connections.read().await;
        let connections = self.connections.read().await;
        let mut sent_count = 0u64;

        if let Some(connection_ids) = user_connections.get(&user_id) {
            for &connection_id in connection_ids {
                if let Some(connection) = connections.get(&connection_id) {
                    if connection.sender.send(message.clone()).is_ok() {
                        connection.increment_sent();
                        sent_count += 1;
                    }
                }
            }
        }

        // 更新统计
        {
            let mut stats = self.stats.write().await;
            stats.total_messages_sent += sent_count;
            stats.last_updated = Utc::now();
        }

        Ok(sent_count)
    }

    /// 更新统计信息
    async fn update_stats(&self) {
        let connections = self.connections.read().await;
        let user_connections = self.user_connections.read().await;

        let active_connections = connections.len() as u64;
        let unique_users = user_connections.len() as u64;
        let total_connections = self.total_connections.load(Ordering::Relaxed);

        // 计算平均连接持续时间
        let total_duration: f64 = connections
            .values()
            .map(|conn| conn.connection_duration())
            .sum();
        let average_duration = if active_connections > 0 {
            total_duration / (active_connections as f64)
        } else {
            0.0
        };

        // 计算最长连接持续时间
        let max_duration = connections
            .values()
            .map(|conn| conn.connection_duration())
            .fold(0.0, f64::max);

        let mut stats = self.stats.write().await;
        stats.active_connections = active_connections;
        stats.total_connections = total_connections;
        stats.unique_users = unique_users;
        stats.average_connection_duration = average_duration;
        stats.max_connection_duration = max_duration;
        stats.reconnection_count = self.reconnection_count.load(Ordering::Relaxed);
        stats.last_updated = Utc::now();
    }
}

impl Default for ConnectionManager {
    fn default() -> Self {
        Self::new()
    }
}
