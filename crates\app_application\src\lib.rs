//! # App Application
//!
//! 应用层，实现业务用例：
//! - 业务用例编排
//! - 应用服务实现
//! - 领域对象协调
//! - 事务管理

// ============================================================================
// 模块声明 - 按业务领域分组
// ============================================================================

// 核心业务服务
pub mod auth_service;
pub mod chat_service;
pub mod resilient_chat_service;
pub mod task_service;
pub mod user_service;

// WebSocket相关服务
pub mod connection_manager;
pub mod message_distributor;
pub mod websocket_service;

// 辅助服务
pub mod notification_service;
pub mod performance_service;
pub mod status_sync_service;

// 异步搜索队列系统
pub mod async_search_queue;
pub mod precompute_scheduler;
pub mod search_result_notifier;
pub mod search_task_scheduler;

// ============================================================================
// 核心应用服务重新导出 - 按业务重要性排序
// ============================================================================

// 认证服务
pub use auth_service::{AuthApplicationService, AuthApplicationServiceImpl};

// 用户服务
pub use user_service::{UserApplicationService, UserApplicationServiceImpl};

// 任务服务
pub use task_service::{TaskApplicationService, TaskApplicationServiceImpl};

// 聊天服务
pub use chat_service::{ChatApplicationService, ChatApplicationServiceImpl};

// 弹性聊天服务
pub use resilient_chat_service::ResilientChatApplicationService;

// WebSocket服务
pub use websocket_service::{WebSocketApplicationService, WebSocketApplicationServiceImpl};

// 连接管理服务
pub use connection_manager::{
    ConnectionId, ConnectionManager, ConnectionQuality, MessageThroughput, UserConnection,
    WebSocketStats,
};

// 消息分发服务
pub use message_distributor::{
    BroadcastStrategy, DistributionStats, MessageDistributor, MessagePriority,
};

// 通知服务
pub use notification_service::{
    NotificationApplicationService, NotificationApplicationServiceImpl, NotificationRequest,
    NotificationResponse, NotificationService,
};

// 性能监控服务
pub use performance_service::{
    PerformanceApplicationService, PerformanceApplicationServiceImpl, PerformanceMetrics,
    PerformanceStatsResponse,
};

// 状态同步服务
pub use status_sync_service::{
    MessageStatus, StatusSyncApplicationService, StatusSyncApplicationServiceImpl,
    StatusSyncService, UserStatus,
};

// 异步搜索队列系统
pub use async_search_queue::{AsyncSearchQueue, QueueConfig, QueueMetrics};
pub use search_result_notifier::{
    NotificationRecord, NotificationStatus, NotificationType, NotifierConfig, SearchResultNotifier,
    WebSocketManager,
};
pub use search_task_scheduler::{
    SchedulerConfig, SchedulerMetrics, SearchTaskExecutor, SearchTaskScheduler, TaskResultNotifier,
    WorkerMetrics,
};

// 搜索结果预计算系统
pub use precompute_scheduler::{
    HotQueryStats, PrecomputeScheduler, PrecomputeSchedulerConfig, PrecomputeSchedulerStats,
};

// ============================================================================
// 外部依赖重新导出 - 仅导出应用层需要的依赖
// ============================================================================

// 内部依赖
pub use app_common;
pub use app_domain;

// 异步trait支持
pub use async_trait;

// 日志支持
pub use tracing;
