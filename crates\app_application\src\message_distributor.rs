//! # 消息分发器应用服务
//!
//! 消息分发相关的业务用例实现，包括：
//! - 智能消息分发和路由
//! - 多种广播策略支持
//! - 消息队列和批量处理
//! - 消息优先级和限流机制

use app_common::error::Result;
use axum::extract::ws::Message;

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use tokio::sync::{Mutex, RwLock};
use uuid::Uuid;

use super::connection_manager::ConnectionManager;

/// 消息分发策略枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum BroadcastStrategy {
    /// 广播给所有连接（排除发送者）
    BroadcastAll { exclude_sender: bool },
    /// 广播给特定用户列表
    BroadcastToUsers { user_ids: Vec<Uuid> },
    /// 广播给特定聊天室
    BroadcastToRoom { room_id: Uuid },
    /// 私聊消息
    DirectMessage { target_user_id: Uuid },
    /// 系统公告
    SystemAnnouncement,
    /// 基于用户角色的广播
    RoleBased { roles: Vec<String> },
}

/// 消息优先级枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum MessagePriority {
    /// 低优先级
    Low = 1,
    /// 普通优先级
    Normal = 2,
    /// 高优先级
    High = 3,
    /// 紧急优先级
    Critical = 4,
}

/// 分发消息结构体
#[derive(Debug, Clone)]
pub struct DistributionMessage {
    /// 消息ID
    pub id: Uuid,
    /// 消息内容
    pub content: Message,
    /// 分发策略
    pub strategy: BroadcastStrategy,
    /// 消息优先级
    pub priority: MessagePriority,
    /// 发送者用户ID（可选）
    pub sender_user_id: Option<Uuid>,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 重试次数
    pub retry_count: u32,
    /// 最大重试次数
    pub max_retries: u32,
}

impl DistributionMessage {
    /// 创建新的分发消息
    pub fn new(
        content: Message,
        strategy: BroadcastStrategy,
        priority: MessagePriority,
        sender_user_id: Option<Uuid>,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            content,
            strategy,
            priority,
            sender_user_id,
            created_at: Utc::now(),
            retry_count: 0,
            max_retries: 3,
        }
    }

    /// 是否可以重试
    pub fn can_retry(&self) -> bool {
        self.retry_count < self.max_retries
    }

    /// 增加重试次数
    pub fn increment_retry(&mut self) {
        self.retry_count += 1;
    }
}

/// 分发统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DistributionStats {
    /// 总分发消息数
    pub total_messages_distributed: u64,
    /// 成功分发数
    pub successful_distributions: u64,
    /// 失败分发数
    pub failed_distributions: u64,
    /// 重试次数
    pub retry_count: u64,
    /// 平均分发延迟（毫秒）
    pub average_distribution_latency: f64,
    /// 队列中待处理消息数
    pub pending_messages: usize,
    /// 每秒分发消息数
    pub messages_per_second: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

/// 批处理配置
#[derive(Debug, Clone)]
pub struct BatchConfig {
    /// 批处理大小
    pub batch_size: usize,
    /// 批处理超时时间（毫秒）
    pub batch_timeout_ms: u64,
    /// 是否启用压缩
    pub enable_compression: bool,
    /// 压缩阈值（字节）
    pub compression_threshold: usize,
}

impl Default for BatchConfig {
    fn default() -> Self {
        Self {
            batch_size: 100,
            batch_timeout_ms: 50,
            enable_compression: true,
            compression_threshold: 1024,
        }
    }
}

/// 消息分发器
pub struct MessageDistributor {
    /// 连接管理器
    connection_manager: Arc<ConnectionManager>,
    /// 消息队列（按优先级排序）
    message_queue: Arc<Mutex<VecDeque<DistributionMessage>>>,
    /// 分发统计
    stats: Arc<RwLock<DistributionStats>>,
    /// 批处理配置
    #[allow(dead_code)]
    batch_config: Arc<RwLock<BatchConfig>>,
    /// 用户角色映射
    user_roles: Arc<RwLock<HashMap<Uuid, Vec<String>>>>,
    /// 聊天室成员映射
    room_members: Arc<RwLock<HashMap<Uuid, Vec<Uuid>>>>,
    /// 分发计数器
    distribution_counter: AtomicU64,
    /// 成功计数器
    success_counter: AtomicU64,
    /// 失败计数器
    failure_counter: AtomicU64,
    /// 重试计数器
    retry_counter: AtomicU64,
}

impl MessageDistributor {
    /// 创建新的消息分发器
    pub fn new(connection_manager: Arc<ConnectionManager>) -> Self {
        Self {
            connection_manager,
            message_queue: Arc::new(Mutex::new(VecDeque::new())),
            stats: Arc::new(RwLock::new(DistributionStats {
                total_messages_distributed: 0,
                successful_distributions: 0,
                failed_distributions: 0,
                retry_count: 0,
                average_distribution_latency: 0.0,
                pending_messages: 0,
                messages_per_second: 0.0,
                last_updated: Utc::now(),
            })),
            batch_config: Arc::new(RwLock::new(BatchConfig::default())),
            user_roles: Arc::new(RwLock::new(HashMap::new())),
            room_members: Arc::new(RwLock::new(HashMap::new())),
            distribution_counter: AtomicU64::new(0),
            success_counter: AtomicU64::new(0),
            failure_counter: AtomicU64::new(0),
            retry_counter: AtomicU64::new(0),
        }
    }

    /// 分发消息
    pub async fn distribute_message(
        &self,
        content: Message,
        strategy: BroadcastStrategy,
        priority: MessagePriority,
        sender_user_id: Option<Uuid>,
    ) -> Result<u64> {
        let message = DistributionMessage::new(content, strategy, priority, sender_user_id);

        // 添加到队列
        {
            let mut queue = self.message_queue.lock().await;
            // 按优先级插入（高优先级在前）
            let insert_pos = queue
                .iter()
                .position(|m| m.priority < message.priority)
                .unwrap_or(queue.len());
            queue.insert(insert_pos, message.clone());
        }

        // 立即处理消息
        self.process_message(message).await
    }

    /// 处理单个消息
    async fn process_message(&self, mut message: DistributionMessage) -> Result<u64> {
        let start_time = std::time::Instant::now();

        let result = match &message.strategy {
            BroadcastStrategy::BroadcastAll { exclude_sender } => {
                self.handle_broadcast_all(&message, *exclude_sender).await
            }
            BroadcastStrategy::BroadcastToUsers { user_ids } => {
                self.handle_broadcast_to_users(&message, user_ids).await
            }
            BroadcastStrategy::BroadcastToRoom { room_id } => {
                self.handle_broadcast_to_room(&message, *room_id).await
            }
            BroadcastStrategy::DirectMessage { target_user_id } => {
                self.handle_direct_message(&message, *target_user_id).await
            }
            BroadcastStrategy::SystemAnnouncement => {
                self.handle_system_announcement(&message).await
            }
            BroadcastStrategy::RoleBased { roles } => {
                self.handle_role_based_broadcast(&message, roles).await
            }
        };

        let latency = start_time.elapsed().as_millis() as f64;

        match result {
            Ok(sent_count) => {
                self.success_counter.fetch_add(1, Ordering::Relaxed);
                self.update_stats(latency, true).await;
                Ok(sent_count)
            }
            Err(e) => {
                if message.can_retry() {
                    message.increment_retry();
                    self.retry_counter.fetch_add(1, Ordering::Relaxed);

                    // 重新加入队列
                    let mut queue = self.message_queue.lock().await;
                    queue.push_back(message);

                    tracing::warn!("消息分发失败，将重试: {}", e);
                    Err(e)
                } else {
                    self.failure_counter.fetch_add(1, Ordering::Relaxed);
                    self.update_stats(latency, false).await;
                    tracing::error!("消息分发最终失败: {}", e);
                    Err(e)
                }
            }
        }
    }

    /// 处理广播给所有用户
    async fn handle_broadcast_all(
        &self,
        message: &DistributionMessage,
        exclude_sender: bool,
    ) -> Result<u64> {
        if exclude_sender && message.sender_user_id.is_some() {
            // TODO: 实现排除发送者的广播逻辑
            self.connection_manager
                .broadcast_to_all(message.content.clone())
                .await
        } else {
            self.connection_manager
                .broadcast_to_all(message.content.clone())
                .await
        }
    }

    /// 处理广播给特定用户列表
    async fn handle_broadcast_to_users(
        &self,
        message: &DistributionMessage,
        user_ids: &[Uuid],
    ) -> Result<u64> {
        let mut total_sent = 0u64;

        for &user_id in user_ids {
            match self
                .connection_manager
                .send_to_user(user_id, message.content.clone())
                .await
            {
                Ok(sent) => {
                    total_sent += sent;
                }
                Err(e) => tracing::warn!("发送消息给用户 {} 失败: {}", user_id, e),
            }
        }

        Ok(total_sent)
    }

    /// 处理广播给聊天室
    async fn handle_broadcast_to_room(
        &self,
        message: &DistributionMessage,
        room_id: Uuid,
    ) -> Result<u64> {
        let room_members = self.room_members.read().await;

        if let Some(members) = room_members.get(&room_id) {
            self.handle_broadcast_to_users(message, members).await
        } else {
            Ok(0)
        }
    }

    /// 处理私聊消息
    async fn handle_direct_message(
        &self,
        message: &DistributionMessage,
        target_user_id: Uuid,
    ) -> Result<u64> {
        self.connection_manager
            .send_to_user(target_user_id, message.content.clone())
            .await
    }

    /// 处理系统公告
    async fn handle_system_announcement(&self, message: &DistributionMessage) -> Result<u64> {
        self.connection_manager
            .broadcast_to_all(message.content.clone())
            .await
    }

    /// 处理基于角色的广播
    async fn handle_role_based_broadcast(
        &self,
        message: &DistributionMessage,
        roles: &[String],
    ) -> Result<u64> {
        let user_roles = self.user_roles.read().await;
        let mut target_users = Vec::new();

        for (user_id, user_role_list) in user_roles.iter() {
            if roles.iter().any(|role| user_role_list.contains(role)) {
                target_users.push(*user_id);
            }
        }

        self.handle_broadcast_to_users(message, &target_users).await
    }

    /// 获取分发统计
    pub async fn get_stats(&self) -> DistributionStats {
        self.stats.read().await.clone()
    }

    /// 更新统计信息
    async fn update_stats(&self, latency: f64, _success: bool) {
        let mut stats = self.stats.write().await;

        stats.total_messages_distributed =
            self.distribution_counter.fetch_add(1, Ordering::Relaxed) + 1;
        stats.successful_distributions = self.success_counter.load(Ordering::Relaxed);
        stats.failed_distributions = self.failure_counter.load(Ordering::Relaxed);
        stats.retry_count = self.retry_counter.load(Ordering::Relaxed);

        // 更新平均延迟
        let total_latency = stats.average_distribution_latency
            * ((stats.total_messages_distributed - 1) as f64)
            + latency;
        stats.average_distribution_latency =
            total_latency / (stats.total_messages_distributed as f64);

        // 更新队列大小
        stats.pending_messages = self.message_queue.lock().await.len();

        stats.last_updated = Utc::now();
    }

    /// 设置用户角色
    pub async fn set_user_roles(&self, user_id: Uuid, roles: Vec<String>) {
        let mut user_roles = self.user_roles.write().await;
        user_roles.insert(user_id, roles);
    }

    /// 添加用户到聊天室
    pub async fn add_user_to_room(&self, room_id: Uuid, user_id: Uuid) {
        let mut room_members = self.room_members.write().await;
        room_members
            .entry(room_id)
            .or_insert_with(Vec::new)
            .push(user_id);
    }

    /// 从聊天室移除用户
    pub async fn remove_user_from_room(&self, room_id: Uuid, user_id: Uuid) {
        let mut room_members = self.room_members.write().await;
        if let Some(members) = room_members.get_mut(&room_id) {
            members.retain(|&id| id != user_id);
            if members.is_empty() {
                room_members.remove(&room_id);
            }
        }
    }
}
