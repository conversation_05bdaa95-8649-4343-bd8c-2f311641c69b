//! # 通知应用服务
//!
//! 通知相关的业务用例实现，包括：
//! - 用户通知管理
//! - 系统公告发送
//! - 通知偏好设置
//! - 实时通知推送

use app_common::error::Result;
use async_trait::async_trait;
use chrono::{DateTime, Timelike, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

use super::connection_manager::ConnectionManager;
use super::message_distributor::{BroadcastStrategy, MessageDistributor, MessagePriority};

/// 通知类型枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum NotificationType {
    /// 用户加入通知
    UserJoined,
    /// 用户离开通知
    UserLeft,
    /// 系统公告
    SystemAnnouncement,
    /// 在线人数变化
    OnlineCountChanged,
    /// 新消息通知
    NewMessage,
    /// 任务状态变更
    TaskStatusChanged,
    /// 系统维护通知
    SystemMaintenance,
    /// 安全警告
    SecurityAlert,
}

/// 通知优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum NotificationPriority {
    /// 低优先级
    Low = 1,
    /// 普通优先级
    Normal = 2,
    /// 高优先级
    High = 3,
    /// 紧急优先级
    Critical = 4,
}

impl From<NotificationPriority> for MessagePriority {
    fn from(priority: NotificationPriority) -> Self {
        match priority {
            NotificationPriority::Low => MessagePriority::Low,
            NotificationPriority::Normal => MessagePriority::Normal,
            NotificationPriority::High => MessagePriority::High,
            NotificationPriority::Critical => MessagePriority::Critical,
        }
    }
}

/// 通知请求DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationRequest {
    /// 通知类型
    pub notification_type: NotificationType,
    /// 通知标题
    pub title: String,
    /// 通知内容
    pub message: String,
    /// 通知优先级
    pub priority: NotificationPriority,
    /// 目标用户ID列表（为空则广播给所有用户）
    pub target_users: Option<Vec<Uuid>>,
    /// 发送者用户ID（可选）
    pub sender_user_id: Option<Uuid>,
    /// 通知数据（可选的额外数据）
    pub data: Option<serde_json::Value>,
}

/// 通知响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationResponse {
    /// 通知ID
    pub id: Uuid,
    /// 通知类型
    pub notification_type: NotificationType,
    /// 通知标题
    pub title: String,
    /// 通知内容
    pub message: String,
    /// 通知优先级
    pub priority: NotificationPriority,
    /// 发送时间
    pub sent_at: DateTime<Utc>,
    /// 发送给的用户数量
    pub recipients_count: u64,
    /// 发送状态
    pub status: String,
}

/// 用户通知偏好设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationPreferences {
    /// 用户ID
    pub user_id: Uuid,
    /// 启用的通知类型
    pub enabled_types: Vec<NotificationType>,
    /// 免打扰模式
    pub do_not_disturb: bool,
    /// 免打扰开始时间
    pub dnd_start_time: Option<String>, // HH:MM 格式
    /// 免打扰结束时间
    pub dnd_end_time: Option<String>, // HH:MM 格式
    /// 最后更新时间
    pub updated_at: DateTime<Utc>,
}

impl Default for NotificationPreferences {
    fn default() -> Self {
        Self {
            user_id: Uuid::new_v4(),
            enabled_types: vec![
                NotificationType::UserJoined,
                NotificationType::UserLeft,
                NotificationType::SystemAnnouncement,
                NotificationType::OnlineCountChanged,
                NotificationType::NewMessage,
                NotificationType::TaskStatusChanged,
            ],
            do_not_disturb: false,
            dnd_start_time: None,
            dnd_end_time: None,
            updated_at: Utc::now(),
        }
    }
}

/// 通知服务
pub struct NotificationService {
    /// 连接管理器
    connection_manager: Arc<ConnectionManager>,
    /// 消息分发器
    message_distributor: Arc<MessageDistributor>,
    /// 用户通知偏好设置
    user_preferences: Arc<RwLock<HashMap<Uuid, NotificationPreferences>>>,
}

impl NotificationService {
    /// 创建新的通知服务
    pub fn new(
        connection_manager: Arc<ConnectionManager>,
        message_distributor: Arc<MessageDistributor>,
    ) -> Self {
        Self {
            connection_manager,
            message_distributor,
            user_preferences: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// 发送通知
    pub async fn send_notification(
        &self,
        request: NotificationRequest,
    ) -> Result<NotificationResponse> {
        let notification_id = Uuid::new_v4();
        let sent_at = Utc::now();

        // 创建通知消息
        let notification_message = serde_json::json!({
            "id": notification_id,
            "type": "notification",
            "notification_type": request.notification_type,
            "title": request.title,
            "message": request.message,
            "priority": request.priority,
            "sent_at": sent_at,
            "sender_user_id": request.sender_user_id,
            "data": request.data
        });

        let message = axum::extract::ws::Message::Text(notification_message.to_string().into());

        // 确定分发策略
        let strategy = if let Some(target_users) = &request.target_users {
            // 过滤用户偏好设置
            let filtered_users = self
                .filter_users_by_preferences(target_users, &request.notification_type)
                .await;

            BroadcastStrategy::BroadcastToUsers {
                user_ids: filtered_users,
            }
        } else {
            // 广播给所有用户，但需要过滤偏好设置
            let all_users = self
                .get_all_users_with_notification_enabled(&request.notification_type)
                .await;

            BroadcastStrategy::BroadcastToUsers {
                user_ids: all_users,
            }
        };

        // 分发通知
        let priority_clone = request.priority.clone();
        let recipients_count = self
            .message_distributor
            .distribute_message(
                message,
                strategy,
                priority_clone.into(),
                request.sender_user_id,
            )
            .await?;

        tracing::info!(
            "通知已发送: id={}, type={:?}, recipients={}",
            notification_id,
            request.notification_type,
            recipients_count
        );

        Ok(NotificationResponse {
            id: notification_id,
            notification_type: request.notification_type,
            title: request.title,
            message: request.message,
            priority: request.priority,
            sent_at,
            recipients_count,
            status: "sent".to_string(),
        })
    }

    /// 发送用户加入通知
    pub async fn notify_user_joined(
        &self,
        username: &str,
        user_id: Uuid,
    ) -> Result<NotificationResponse> {
        let request = NotificationRequest {
            notification_type: NotificationType::UserJoined,
            title: "用户加入".to_string(),
            message: format!("用户 {username} 加入了聊天室"),
            priority: NotificationPriority::Normal,
            target_users: None, // 广播给所有用户
            sender_user_id: Some(user_id),
            data: Some(serde_json::json!({
                "user_id": user_id,
                "username": username
            })),
        };

        self.send_notification(request).await
    }

    /// 发送用户离开通知
    pub async fn notify_user_left(
        &self,
        username: &str,
        user_id: Uuid,
    ) -> Result<NotificationResponse> {
        let request = NotificationRequest {
            notification_type: NotificationType::UserLeft,
            title: "用户离开".to_string(),
            message: format!("用户 {username} 离开了聊天室"),
            priority: NotificationPriority::Normal,
            target_users: None, // 广播给所有用户
            sender_user_id: Some(user_id),
            data: Some(serde_json::json!({
                "user_id": user_id,
                "username": username
            })),
        };

        self.send_notification(request).await
    }

    /// 发送在线人数变化通知
    pub async fn notify_online_count_changed(&self, count: u64) -> Result<NotificationResponse> {
        let request = NotificationRequest {
            notification_type: NotificationType::OnlineCountChanged,
            title: "在线人数更新".to_string(),
            message: format!("当前在线人数: {count}"),
            priority: NotificationPriority::Low,
            target_users: None, // 广播给所有用户
            sender_user_id: None,
            data: Some(serde_json::json!({
                "online_count": count
            })),
        };

        self.send_notification(request).await
    }

    /// 发送系统公告
    pub async fn send_system_announcement(
        &self,
        title: &str,
        message: &str,
        priority: NotificationPriority,
    ) -> Result<NotificationResponse> {
        let request = NotificationRequest {
            notification_type: NotificationType::SystemAnnouncement,
            title: title.to_string(),
            message: message.to_string(),
            priority,
            target_users: None, // 广播给所有用户
            sender_user_id: None,
            data: None,
        };

        self.send_notification(request).await
    }

    /// 获取用户通知偏好设置
    pub async fn get_user_preferences(&self, user_id: Uuid) -> NotificationPreferences {
        let preferences = self.user_preferences.read().await;
        preferences
            .get(&user_id)
            .cloned()
            .unwrap_or_else(|| NotificationPreferences {
                user_id,
                ..Default::default()
            })
    }

    /// 更新用户通知偏好设置
    pub async fn update_user_preferences(
        &self,
        user_id: Uuid,
        preferences: NotificationPreferences,
    ) -> Result<()> {
        let mut user_preferences = self.user_preferences.write().await;
        let mut updated_prefs = preferences;
        updated_prefs.user_id = user_id;
        updated_prefs.updated_at = Utc::now();

        user_preferences.insert(user_id, updated_prefs);

        tracing::info!("用户通知偏好设置已更新: user_id={}", user_id);
        Ok(())
    }

    /// 根据用户偏好设置过滤用户列表
    async fn filter_users_by_preferences(
        &self,
        user_ids: &[Uuid],
        notification_type: &NotificationType,
    ) -> Vec<Uuid> {
        let preferences = self.user_preferences.read().await;
        let mut filtered_users = Vec::new();

        for &user_id in user_ids {
            if let Some(user_prefs) = preferences.get(&user_id) {
                if self.should_send_notification(user_prefs, notification_type) {
                    filtered_users.push(user_id);
                }
            } else {
                // 如果没有设置偏好，默认发送
                filtered_users.push(user_id);
            }
        }

        filtered_users
    }

    /// 获取所有启用了特定通知类型的用户
    async fn get_all_users_with_notification_enabled(
        &self,
        notification_type: &NotificationType,
    ) -> Vec<Uuid> {
        let online_users = self.connection_manager.get_online_users().await;
        let preferences = self.user_preferences.read().await;
        let mut enabled_users = Vec::new();

        for (user_id, _username) in online_users {
            if let Some(user_prefs) = preferences.get(&user_id) {
                if self.should_send_notification(user_prefs, notification_type) {
                    enabled_users.push(user_id);
                }
            } else {
                // 如果没有设置偏好，默认发送
                enabled_users.push(user_id);
            }
        }

        enabled_users
    }

    /// 判断是否应该发送通知
    fn should_send_notification(
        &self,
        preferences: &NotificationPreferences,
        notification_type: &NotificationType,
    ) -> bool {
        // 检查通知类型是否启用
        if !preferences.enabled_types.contains(notification_type) {
            return false;
        }

        // 检查免打扰模式
        if preferences.do_not_disturb {
            // 如果是紧急通知，即使在免打扰模式下也要发送
            if matches!(
                notification_type,
                NotificationType::SecurityAlert | NotificationType::SystemMaintenance
            ) {
                return true;
            }

            // 检查免打扰时间段
            if let (Some(start_time), Some(end_time)) =
                (&preferences.dnd_start_time, &preferences.dnd_end_time)
            {
                let now = Utc::now().time();
                let current_time = format!("{:02}:{:02}", now.hour(), now.minute());

                // 简单的时间段检查（不考虑跨天情况）
                if start_time <= &current_time && &current_time <= end_time {
                    return false;
                }
            } else {
                // 如果设置了免打扰但没有指定时间段，则全天免打扰
                return false;
            }
        }

        true
    }
}

/// 通知应用服务接口
#[async_trait]
pub trait NotificationApplicationService: Send + Sync {
    /// 发送通知
    async fn send_notification(&self, request: NotificationRequest)
    -> Result<NotificationResponse>;

    /// 获取用户通知偏好设置
    async fn get_user_preferences(&self, user_id: Uuid) -> NotificationPreferences;

    /// 更新用户通知偏好设置
    async fn update_user_preferences(
        &self,
        user_id: Uuid,
        preferences: NotificationPreferences,
    ) -> Result<()>;
}

/// 通知应用服务实现
pub struct NotificationApplicationServiceImpl {
    notification_service: Arc<NotificationService>,
}

impl NotificationApplicationServiceImpl {
    /// 创建新的通知应用服务实例
    pub fn new(notification_service: Arc<NotificationService>) -> Self {
        Self {
            notification_service,
        }
    }
}

#[async_trait]
impl NotificationApplicationService for NotificationApplicationServiceImpl {
    async fn send_notification(
        &self,
        request: NotificationRequest,
    ) -> Result<NotificationResponse> {
        self.notification_service.send_notification(request).await
    }

    async fn get_user_preferences(&self, user_id: Uuid) -> NotificationPreferences {
        self.notification_service
            .get_user_preferences(user_id)
            .await
    }

    async fn update_user_preferences(
        &self,
        user_id: Uuid,
        preferences: NotificationPreferences,
    ) -> Result<()> {
        self.notification_service
            .update_user_preferences(user_id, preferences)
            .await
    }
}
