//! # 性能监控应用服务
//!
//! 性能监控相关的业务用例实现，包括：
//! - 系统性能指标收集
//! - 性能统计和分析
//! - 性能告警和通知
//! - 性能优化建议

use app_common::error::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use uuid::Uuid;

/// 性能指标结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// 请求总数
    pub total_requests: u64,
    /// 成功请求数
    pub successful_requests: u64,
    /// 失败请求数
    pub failed_requests: u64,
    /// 平均响应时间（毫秒）
    pub average_response_time: f64,
    /// P95响应时间（毫秒）
    pub p95_response_time: f64,
    /// P99响应时间（毫秒）
    pub p99_response_time: f64,
    /// 最大响应时间（毫秒）
    pub max_response_time: f64,
    /// 最小响应时间（毫秒）
    pub min_response_time: f64,
    /// 当前并发连接数
    pub concurrent_connections: u64,
    /// 峰值并发连接数
    pub peak_concurrent_connections: u64,
    /// 内存使用量（字节）
    pub memory_usage_bytes: u64,
    /// CPU使用率（百分比）
    pub cpu_usage_percent: f64,
    /// 错误率（百分比）
    pub error_rate: f64,
    /// 吞吐量（每秒请求数）
    pub throughput_rps: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            total_requests: 0,
            successful_requests: 0,
            failed_requests: 0,
            average_response_time: 0.0,
            p95_response_time: 0.0,
            p99_response_time: 0.0,
            max_response_time: 0.0,
            min_response_time: f64::MAX,
            concurrent_connections: 0,
            peak_concurrent_connections: 0,
            memory_usage_bytes: 0,
            cpu_usage_percent: 0.0,
            error_rate: 0.0,
            throughput_rps: 0.0,
            last_updated: Utc::now(),
        }
    }
}

/// 性能统计响应DTO
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceStatsResponse {
    /// 基础性能指标
    pub metrics: PerformanceMetrics,
    /// 端点性能统计
    pub endpoint_stats: HashMap<String, EndpointStats>,
    /// 系统健康状态
    pub health_status: HealthStatus,
    /// 性能趋势
    pub trends: PerformanceTrends,
    /// 告警信息
    pub alerts: Vec<PerformanceAlert>,
}

/// 端点性能统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EndpointStats {
    /// 端点路径
    pub path: String,
    /// 请求总数
    pub total_requests: u64,
    /// 平均响应时间（毫秒）
    pub average_response_time: f64,
    /// 错误率（百分比）
    pub error_rate: f64,
    /// 最后请求时间
    pub last_request_at: DateTime<Utc>,
}

/// 系统健康状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthStatus {
    /// 整体健康评分（0-100）
    pub overall_score: f64,
    /// 响应时间健康状态
    pub response_time_health: String,
    /// 错误率健康状态
    pub error_rate_health: String,
    /// 资源使用健康状态
    pub resource_usage_health: String,
    /// 连接数健康状态
    pub connection_health: String,
}

/// 性能趋势
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceTrends {
    /// 响应时间趋势
    pub response_time_trend: String, // "improving", "stable", "degrading"
    /// 吞吐量趋势
    pub throughput_trend: String,
    /// 错误率趋势
    pub error_rate_trend: String,
    /// 内存使用趋势
    pub memory_usage_trend: String,
}

/// 性能告警
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceAlert {
    /// 告警ID
    pub id: Uuid,
    /// 告警类型
    pub alert_type: String,
    /// 告警级别
    pub severity: String, // "low", "medium", "high", "critical"
    /// 告警消息
    pub message: String,
    /// 告警时间
    pub timestamp: DateTime<Utc>,
    /// 相关指标值
    pub metric_value: f64,
    /// 阈值
    pub threshold: f64,
}

/// 响应时间记录
#[derive(Debug, Clone)]
struct ResponseTimeRecord {
    duration: f64,
    #[allow(dead_code)]
    timestamp: Instant,
}

/// 性能监控器
pub struct PerformanceMonitor {
    /// 基础指标
    metrics: Arc<RwLock<PerformanceMetrics>>,
    /// 端点统计
    endpoint_stats: Arc<RwLock<HashMap<String, EndpointStats>>>,
    /// 响应时间记录（用于计算百分位数）
    response_times: Arc<RwLock<Vec<ResponseTimeRecord>>>,
    /// 活跃告警
    active_alerts: Arc<RwLock<Vec<PerformanceAlert>>>,
    /// 原子计数器
    request_counter: AtomicU64,
    success_counter: AtomicU64,
    failure_counter: AtomicU64,
    connection_counter: AtomicUsize,
    peak_connections: AtomicUsize,
}

impl PerformanceMonitor {
    /// 创建新的性能监控器
    pub fn new() -> Self {
        Self {
            metrics: Arc::new(RwLock::new(PerformanceMetrics::default())),
            endpoint_stats: Arc::new(RwLock::new(HashMap::new())),
            response_times: Arc::new(RwLock::new(Vec::new())),
            active_alerts: Arc::new(RwLock::new(Vec::new())),
            request_counter: AtomicU64::new(0),
            success_counter: AtomicU64::new(0),
            failure_counter: AtomicU64::new(0),
            connection_counter: AtomicUsize::new(0),
            peak_connections: AtomicUsize::new(0),
        }
    }

    /// 记录请求
    pub async fn record_request(&self, endpoint: &str, response_time: Duration, success: bool) {
        let response_time_ms = response_time.as_millis() as f64;

        // 更新原子计数器
        self.request_counter.fetch_add(1, Ordering::Relaxed);
        if success {
            self.success_counter.fetch_add(1, Ordering::Relaxed);
        } else {
            self.failure_counter.fetch_add(1, Ordering::Relaxed);
        }

        // 记录响应时间
        {
            let mut response_times = self.response_times.write().await;
            response_times.push(ResponseTimeRecord {
                duration: response_time_ms,
                timestamp: Instant::now(),
            });

            // 保持最近1000条记录
            if response_times.len() > 1000 {
                let len = response_times.len();
                response_times.drain(0..len - 1000);
            }
        }

        // 更新端点统计
        {
            let mut endpoint_stats = self.endpoint_stats.write().await;
            let stats = endpoint_stats
                .entry(endpoint.to_string())
                .or_insert_with(|| EndpointStats {
                    path: endpoint.to_string(),
                    total_requests: 0,
                    average_response_time: 0.0,
                    error_rate: 0.0,
                    last_request_at: Utc::now(),
                });

            stats.total_requests += 1;
            stats.average_response_time = (stats.average_response_time
                * ((stats.total_requests - 1) as f64)
                + response_time_ms)
                / (stats.total_requests as f64);
            if !success {
                stats.error_rate = (stats.error_rate * ((stats.total_requests - 1) as f64) + 100.0)
                    / (stats.total_requests as f64);
            } else {
                stats.error_rate = (stats.error_rate * ((stats.total_requests - 1) as f64))
                    / (stats.total_requests as f64);
            }
            stats.last_request_at = Utc::now();
        }

        // 更新整体指标
        self.update_metrics().await;
    }

    /// 记录连接变化
    pub async fn record_connection_change(&self, delta: i32) {
        let current = if delta > 0 {
            self.connection_counter
                .fetch_add(delta as usize, Ordering::Relaxed)
                + (delta as usize)
        } else {
            self.connection_counter
                .fetch_sub(-delta as usize, Ordering::Relaxed)
                - (-delta as usize)
        };

        // 更新峰值连接数
        let peak = self.peak_connections.load(Ordering::Relaxed);
        if current > peak {
            self.peak_connections.store(current, Ordering::Relaxed);
        }

        self.update_metrics().await;
    }

    /// 获取性能统计
    pub async fn get_performance_stats(&self) -> PerformanceStatsResponse {
        let metrics = self.metrics.read().await.clone();
        let endpoint_stats = self.endpoint_stats.read().await.clone();
        let active_alerts = self.active_alerts.read().await.clone();

        PerformanceStatsResponse {
            metrics,
            endpoint_stats,
            health_status: self.calculate_health_status().await,
            trends: self.calculate_trends().await,
            alerts: active_alerts,
        }
    }

    /// 更新指标
    async fn update_metrics(&self) {
        let mut metrics = self.metrics.write().await;

        let total_requests = self.request_counter.load(Ordering::Relaxed);
        let successful_requests = self.success_counter.load(Ordering::Relaxed);
        let failed_requests = self.failure_counter.load(Ordering::Relaxed);
        let concurrent_connections = self.connection_counter.load(Ordering::Relaxed) as u64;
        let peak_concurrent_connections = self.peak_connections.load(Ordering::Relaxed) as u64;

        metrics.total_requests = total_requests;
        metrics.successful_requests = successful_requests;
        metrics.failed_requests = failed_requests;
        metrics.concurrent_connections = concurrent_connections;
        metrics.peak_concurrent_connections = peak_concurrent_connections;

        // 计算错误率
        if total_requests > 0 {
            metrics.error_rate = ((failed_requests as f64) / (total_requests as f64)) * 100.0;
        }

        // 计算响应时间统计
        let response_times = self.response_times.read().await;
        if !response_times.is_empty() {
            let mut times: Vec<f64> = response_times.iter().map(|r| r.duration).collect();
            times.sort_by(|a, b| a.partial_cmp(b).unwrap());

            metrics.average_response_time = times.iter().sum::<f64>() / (times.len() as f64);
            metrics.min_response_time = times[0];
            metrics.max_response_time = times[times.len() - 1];

            // 计算百分位数
            let p95_index = ((times.len() as f64) * 0.95) as usize;
            let p99_index = ((times.len() as f64) * 0.99) as usize;
            metrics.p95_response_time = times.get(p95_index).copied().unwrap_or(0.0);
            metrics.p99_response_time = times.get(p99_index).copied().unwrap_or(0.0);
        }

        // 计算吞吐量（简化版本）
        metrics.throughput_rps = (total_requests as f64) / 60.0; // 假设运行了1分钟

        metrics.last_updated = Utc::now();

        // 检查告警条件
        self.check_alerts(&metrics).await;
    }

    /// 计算健康状态
    async fn calculate_health_status(&self) -> HealthStatus {
        let metrics = self.metrics.read().await;

        let response_time_health = if metrics.average_response_time < 100.0 {
            "excellent"
        } else if metrics.average_response_time < 500.0 {
            "good"
        } else if metrics.average_response_time < 1000.0 {
            "fair"
        } else {
            "poor"
        };

        let error_rate_health = if metrics.error_rate < 1.0 {
            "excellent"
        } else if metrics.error_rate < 5.0 {
            "good"
        } else if metrics.error_rate < 10.0 {
            "fair"
        } else {
            "poor"
        };

        let resource_usage_health = if metrics.cpu_usage_percent < 70.0
            && metrics.memory_usage_bytes < 1_000_000_000
        {
            "excellent"
        } else if metrics.cpu_usage_percent < 85.0 && metrics.memory_usage_bytes < 2_000_000_000 {
            "good"
        } else {
            "poor"
        };

        let connection_health = if metrics.concurrent_connections < 1000 {
            "excellent"
        } else if metrics.concurrent_connections < 5000 {
            "good"
        } else {
            "fair"
        };

        // 计算整体评分
        let scores = [
            match response_time_health {
                "excellent" => 100.0,
                "good" => 80.0,
                "fair" => 60.0,
                _ => 40.0,
            },
            match error_rate_health {
                "excellent" => 100.0,
                "good" => 80.0,
                "fair" => 60.0,
                _ => 40.0,
            },
            match resource_usage_health {
                "excellent" => 100.0,
                "good" => 80.0,
                _ => 40.0,
            },
            match connection_health {
                "excellent" => 100.0,
                "good" => 80.0,
                _ => 60.0,
            },
        ];

        let overall_score = scores.iter().sum::<f64>() / (scores.len() as f64);

        HealthStatus {
            overall_score,
            response_time_health: response_time_health.to_string(),
            error_rate_health: error_rate_health.to_string(),
            resource_usage_health: resource_usage_health.to_string(),
            connection_health: connection_health.to_string(),
        }
    }

    /// 计算性能趋势
    async fn calculate_trends(&self) -> PerformanceTrends {
        // 简化版本，实际应该基于历史数据
        PerformanceTrends {
            response_time_trend: "stable".to_string(),
            throughput_trend: "stable".to_string(),
            error_rate_trend: "stable".to_string(),
            memory_usage_trend: "stable".to_string(),
        }
    }

    /// 检查告警条件
    async fn check_alerts(&self, metrics: &PerformanceMetrics) {
        let mut alerts = self.active_alerts.write().await;
        alerts.clear(); // 清除旧告警

        // 响应时间告警
        if metrics.average_response_time > 1000.0 {
            alerts.push(PerformanceAlert {
                id: Uuid::new_v4(),
                alert_type: "response_time".to_string(),
                severity: "high".to_string(),
                message: format!("平均响应时间过高: {:.2}ms", metrics.average_response_time),
                timestamp: Utc::now(),
                metric_value: metrics.average_response_time,
                threshold: 1000.0,
            });
        }

        // 错误率告警
        if metrics.error_rate > 5.0 {
            alerts.push(PerformanceAlert {
                id: Uuid::new_v4(),
                alert_type: "error_rate".to_string(),
                severity: "medium".to_string(),
                message: format!("错误率过高: {:.2}%", metrics.error_rate),
                timestamp: Utc::now(),
                metric_value: metrics.error_rate,
                threshold: 5.0,
            });
        }

        // 连接数告警
        if metrics.concurrent_connections > 5000 {
            alerts.push(PerformanceAlert {
                id: Uuid::new_v4(),
                alert_type: "connection_count".to_string(),
                severity: "medium".to_string(),
                message: format!("并发连接数过高: {}", metrics.concurrent_connections),
                timestamp: Utc::now(),
                metric_value: metrics.concurrent_connections as f64,
                threshold: 5000.0,
            });
        }
    }
}

impl Default for PerformanceMonitor {
    fn default() -> Self {
        Self::new()
    }
}

/// 性能应用服务接口
#[async_trait]
pub trait PerformanceApplicationService: Send + Sync {
    /// 获取性能统计
    async fn get_performance_stats(&self) -> Result<PerformanceStatsResponse>;

    /// 记录请求性能
    async fn record_request(
        &self,
        endpoint: &str,
        response_time: Duration,
        success: bool,
    ) -> Result<()>;

    /// 记录连接变化
    async fn record_connection_change(&self, delta: i32) -> Result<()>;
}

/// 性能应用服务实现
pub struct PerformanceApplicationServiceImpl {
    performance_monitor: Arc<PerformanceMonitor>,
}

impl PerformanceApplicationServiceImpl {
    /// 创建新的性能应用服务实例
    pub fn new(performance_monitor: Arc<PerformanceMonitor>) -> Self {
        Self {
            performance_monitor,
        }
    }
}

#[async_trait]
impl PerformanceApplicationService for PerformanceApplicationServiceImpl {
    async fn get_performance_stats(&self) -> Result<PerformanceStatsResponse> {
        Ok(self.performance_monitor.get_performance_stats().await)
    }

    async fn record_request(
        &self,
        endpoint: &str,
        response_time: Duration,
        success: bool,
    ) -> Result<()> {
        self.performance_monitor
            .record_request(endpoint, response_time, success)
            .await;
        Ok(())
    }

    async fn record_connection_change(&self, delta: i32) -> Result<()> {
        self.performance_monitor
            .record_connection_change(delta)
            .await;
        Ok(())
    }
}
