//! # 搜索结果预计算调度器模块
//!
//! 基于Tokio实现的高性能预计算任务调度器，负责：
//! - 热门搜索词分析和识别
//! - 预计算任务调度和管理
//! - 搜索结果预生成
//! - 缓存预热和更新
//! - 性能监控和统计

use app_common::{AppError, Result};
use app_domain::entities::search_task::{
    PrecomputeExecutionStats, PrecomputeScheduleStrategy, PrecomputeTask, PrecomputeTaskType,
    SearchTaskPriority,
};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, Semaphore, mpsc};
use tokio::time::{Duration, Instant, interval};
use tracing::{debug, error, info};
use uuid::Uuid;

/// 预计算调度器配置
///
/// 定义预计算调度器的运行参数
#[derive(Debug, Clone)]
pub struct PrecomputeSchedulerConfig {
    /// 最大并发预计算任务数
    pub max_concurrent_tasks: usize,
    /// 热门搜索词分析间隔（秒）
    pub hot_query_analysis_interval: u32,
    /// 预计算结果缓存TTL（秒）
    pub precompute_cache_ttl: u32,
    /// 最小搜索频次阈值（成为热门搜索词的最小搜索次数）
    pub min_search_frequency: u32,
    /// 热门搜索词数量限制
    pub max_hot_queries: usize,
    /// 预计算任务超时时间（秒）
    pub task_timeout_seconds: u32,
    /// 统计数据保留天数
    pub stats_retention_days: u32,
}

impl Default for PrecomputeSchedulerConfig {
    fn default() -> Self {
        Self {
            max_concurrent_tasks: 10,
            hot_query_analysis_interval: 300, // 5分钟
            precompute_cache_ttl: 3600,       // 1小时
            min_search_frequency: 10,
            max_hot_queries: 100,
            task_timeout_seconds: 300, // 5分钟
            stats_retention_days: 30,
        }
    }
}

/// 热门搜索词统计信息
///
/// 记录搜索词的统计数据
#[derive(Debug, Clone)]
pub struct HotQueryStats {
    /// 搜索查询
    pub query: String,
    /// 搜索频次
    pub frequency: u32,
    /// 最后搜索时间
    pub last_searched_at: DateTime<Utc>,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 搜索用户数量
    pub unique_users: u32,
    /// 预计算状态
    pub precomputed: bool,
    /// 预计算更新时间
    pub precomputed_at: Option<DateTime<Utc>>,
}

/// 预计算调度器统计信息
///
/// 记录调度器的运行统计数据
#[derive(Debug, Clone)]
pub struct PrecomputeSchedulerStats {
    /// 总预计算任务数
    pub total_tasks: u32,
    /// 已完成任务数
    pub completed_tasks: u32,
    /// 失败任务数
    pub failed_tasks: u32,
    /// 当前运行任务数
    pub running_tasks: u32,
    /// 热门搜索词数量
    pub hot_queries_count: u32,
    /// 预计算缓存命中率
    pub cache_hit_rate: f64,
    /// 平均任务执行时间（毫秒）
    pub avg_task_execution_time_ms: f64,
    /// 最后更新时间
    pub last_updated: Instant,
}

/// 搜索结果预计算调度器
///
/// 负责管理和调度搜索结果预计算任务
pub struct PrecomputeScheduler {
    /// 调度器配置
    config: PrecomputeSchedulerConfig,
    /// 预计算任务队列
    task_queue: Arc<RwLock<Vec<PrecomputeTask>>>,
    /// 热门搜索词统计
    hot_queries: Arc<RwLock<HashMap<String, HotQueryStats>>>,
    /// 正在运行的任务
    running_tasks: Arc<RwLock<HashMap<Uuid, PrecomputeTask>>>,
    /// 调度器统计信息
    stats: Arc<RwLock<PrecomputeSchedulerStats>>,
    /// 并发控制信号量
    semaphore: Arc<Semaphore>,
    /// 任务通知通道发送端
    task_sender: mpsc::UnboundedSender<PrecomputeTask>,
    /// 任务通知通道接收端
    task_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<PrecomputeTask>>>>,
    /// 调度器关闭标志
    shutdown: Arc<RwLock<bool>>,
}

impl PrecomputeScheduler {
    /// 创建新的预计算调度器
    ///
    /// # 参数
    /// - `config`: 调度器配置
    ///
    /// # 返回
    /// - 新创建的预计算调度器实例
    pub fn new(config: PrecomputeSchedulerConfig) -> Self {
        let (task_sender, task_receiver) = mpsc::unbounded_channel();

        Self {
            semaphore: Arc::new(Semaphore::new(config.max_concurrent_tasks)),
            config,
            task_queue: Arc::new(RwLock::new(Vec::new())),
            hot_queries: Arc::new(RwLock::new(HashMap::new())),
            running_tasks: Arc::new(RwLock::new(HashMap::new())),
            stats: Arc::new(RwLock::new(PrecomputeSchedulerStats {
                total_tasks: 0,
                completed_tasks: 0,
                failed_tasks: 0,
                running_tasks: 0,
                hot_queries_count: 0,
                cache_hit_rate: 0.0,
                avg_task_execution_time_ms: 0.0,
                last_updated: Instant::now(),
            })),
            task_sender,
            task_receiver: Arc::new(RwLock::new(Some(task_receiver))),
            shutdown: Arc::new(RwLock::new(false)),
        }
    }

    /// 启动预计算调度器
    ///
    /// 启动调度器的主要工作循环
    pub async fn start(&self) -> Result<()> {
        info!("启动搜索结果预计算调度器");

        // 启动热门搜索词分析任务
        self.start_hot_query_analysis().await?;

        // 启动任务调度循环
        self.start_task_scheduling().await?;

        // 启动任务执行循环
        self.start_task_execution().await?;

        info!("预计算调度器启动完成");
        Ok(())
    }

    /// 停止预计算调度器
    pub async fn stop(&self) -> Result<()> {
        info!("停止搜索结果预计算调度器");

        // 设置关闭标志
        *self.shutdown.write().await = true;

        // 等待所有运行中的任务完成
        while !self.running_tasks.read().await.is_empty() {
            tokio::time::sleep(Duration::from_millis(100)).await;
        }

        info!("预计算调度器已停止");
        Ok(())
    }

    /// 添加预计算任务
    ///
    /// # 参数
    /// - `task`: 要添加的预计算任务
    ///
    /// # 返回
    /// - Ok(task_id): 成功添加任务，返回任务ID
    /// - Err: 添加任务失败
    pub async fn schedule_task(&self, task: PrecomputeTask) -> Result<Uuid> {
        // 检查调度器是否已关闭
        if *self.shutdown.read().await {
            return Err(AppError::ValidationError("调度器已关闭".to_string()));
        }

        let task_id = task.id;

        // 添加到任务队列
        self.task_queue.write().await.push(task.clone());

        // 发送任务通知
        if let Err(e) = self.task_sender.send(task) {
            error!("发送预计算任务通知失败: {:?}", e);
            return Err(AppError::InternalServerError("任务通知失败".to_string()));
        }

        // 更新统计信息
        let mut stats = self.stats.write().await;
        stats.total_tasks += 1;
        stats.last_updated = Instant::now();

        info!("预计算任务已添加到调度队列: {}", task_id);
        Ok(task_id)
    }

    /// 更新搜索查询统计
    ///
    /// # 参数
    /// - `query`: 搜索查询
    /// - `response_time_ms`: 响应时间（毫秒）
    /// - `user_id`: 用户ID
    pub async fn update_search_stats(
        &self,
        query: &str,
        response_time_ms: u64,
        user_id: Uuid,
    ) -> Result<()> {
        let mut hot_queries = self.hot_queries.write().await;

        let stats = hot_queries
            .entry(query.to_string())
            .or_insert_with(|| HotQueryStats {
                query: query.to_string(),
                frequency: 0,
                last_searched_at: Utc::now(),
                avg_response_time_ms: 0.0,
                unique_users: 0,
                precomputed: false,
                precomputed_at: None,
            });

        // 更新统计信息
        stats.frequency += 1;
        stats.last_searched_at = Utc::now();

        // 更新平均响应时间
        stats.avg_response_time_ms = (stats.avg_response_time_ms * ((stats.frequency - 1) as f64)
            + (response_time_ms as f64))
            / (stats.frequency as f64);

        // 简化的用户计数（实际实现中应该维护用户集合）
        stats.unique_users = ((stats.frequency as f64) * 0.7) as u32; // 估算唯一用户数

        debug!(
            "更新搜索统计: {} (频次: {}, 平均响应时间: {:.2}ms)",
            query, stats.frequency, stats.avg_response_time_ms
        );

        Ok(())
    }

    /// 获取调度器统计信息
    ///
    /// # 返回
    /// - 当前调度器统计信息
    pub async fn get_stats(&self) -> PrecomputeSchedulerStats {
        let mut stats = self.stats.write().await;

        // 更新实时统计
        stats.running_tasks = self.running_tasks.read().await.len() as u32;
        stats.hot_queries_count = self.hot_queries.read().await.len() as u32;
        stats.last_updated = Instant::now();

        stats.clone()
    }

    /// 获取热门搜索词列表
    ///
    /// # 参数
    /// - `limit`: 返回数量限制
    ///
    /// # 返回
    /// - 热门搜索词列表（按频次降序排列）
    pub async fn get_hot_queries(&self, limit: usize) -> Vec<HotQueryStats> {
        let hot_queries = self.hot_queries.read().await;
        let mut queries: Vec<_> = hot_queries.values().cloned().collect();

        // 按频次降序排序
        queries.sort_by(|a, b| b.frequency.cmp(&a.frequency));

        // 限制返回数量
        queries.truncate(limit);

        queries
    }

    /// 启动热门搜索词分析任务
    ///
    /// 定期分析搜索统计数据，识别热门搜索词
    async fn start_hot_query_analysis(&self) -> Result<()> {
        let config = self.config.clone();
        let hot_queries = self.hot_queries.clone();
        let task_sender = self.task_sender.clone();
        let shutdown = self.shutdown.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(
                config.hot_query_analysis_interval as u64,
            ));

            loop {
                interval.tick().await;

                // 检查是否需要关闭
                if *shutdown.read().await {
                    break;
                }

                // 分析热门搜索词
                if let Err(e) = Self::analyze_hot_queries(&hot_queries, &task_sender, &config).await
                {
                    error!("热门搜索词分析失败: {}", e);
                }
            }

            info!("热门搜索词分析任务已停止");
        });

        Ok(())
    }

    /// 分析热门搜索词并创建预计算任务
    ///
    /// # 参数
    /// - `hot_queries`: 热门搜索词统计
    /// - `task_sender`: 任务发送通道
    /// - `config`: 调度器配置
    async fn analyze_hot_queries(
        hot_queries: &Arc<RwLock<HashMap<String, HotQueryStats>>>,
        task_sender: &mpsc::UnboundedSender<PrecomputeTask>,
        config: &PrecomputeSchedulerConfig,
    ) -> Result<()> {
        let mut queries = hot_queries.write().await;
        let mut new_hot_queries = Vec::new();

        // 识别需要预计算的热门搜索词
        for (query, stats) in queries.iter_mut() {
            if stats.frequency >= config.min_search_frequency && !stats.precomputed {
                // 创建预计算任务
                let precompute_task = PrecomputeTask::new(
                    PrecomputeTaskType::ResultPregeneration,
                    PrecomputeScheduleStrategy::EventDriven,
                    Utc::now(),
                )
                .with_target_query(query.clone())
                .with_priority(SearchTaskPriority::High)
                .with_param("frequency", stats.frequency)
                .with_param("avg_response_time_ms", stats.avg_response_time_ms);

                // 发送预计算任务
                if let Err(e) = task_sender.send(precompute_task) {
                    error!("发送预计算任务失败: {}", e);
                } else {
                    stats.precomputed = true;
                    stats.precomputed_at = Some(Utc::now());
                    new_hot_queries.push(query.clone());
                }
            }
        }

        if !new_hot_queries.is_empty() {
            info!(
                "识别到 {} 个新的热门搜索词，已创建预计算任务",
                new_hot_queries.len()
            );
            debug!("新热门搜索词: {:?}", new_hot_queries);
        }

        Ok(())
    }

    /// 启动任务调度循环
    ///
    /// 处理任务队列中的预计算任务
    async fn start_task_scheduling(&self) -> Result<()> {
        let task_queue = self.task_queue.clone();
        let running_tasks = self.running_tasks.clone();
        let semaphore = self.semaphore.clone();
        let shutdown = self.shutdown.clone();
        let stats = self.stats.clone();

        tokio::spawn(async move {
            let mut interval = interval(Duration::from_secs(1)); // 每秒检查一次

            loop {
                interval.tick().await;

                // 检查是否需要关闭
                if *shutdown.read().await {
                    break;
                }

                // 获取可执行的任务
                let mut queue = task_queue.write().await;
                let mut tasks_to_execute = Vec::new();

                // 查找应该执行的任务
                queue.retain(|task| {
                    if task.should_execute() {
                        tasks_to_execute.push(task.clone());
                        false // 从队列中移除
                    } else {
                        true // 保留在队列中
                    }
                });

                drop(queue); // 释放队列锁

                // 执行任务
                for mut task in tasks_to_execute {
                    // 获取信号量许可
                    if let Ok(permit) = semaphore.clone().try_acquire_owned() {
                        task.start_execution();
                        let task_id = task.id;

                        // 添加到运行任务列表
                        running_tasks.write().await.insert(task_id, task.clone());

                        // 更新统计信息
                        stats.write().await.running_tasks += 1;

                        // 启动任务执行
                        let running_tasks_clone = running_tasks.clone();
                        let stats_clone = stats.clone();

                        tokio::spawn(async move {
                            let _permit = permit; // 持有许可直到任务完成

                            // 执行预计算任务
                            let result = Self::execute_precompute_task(&task).await;

                            // 更新任务状态
                            let mut running_tasks = running_tasks_clone.write().await;
                            if let Some(mut completed_task) = running_tasks.remove(&task_id) {
                                match &result {
                                    Ok(stats) => {
                                        completed_task.complete_execution(stats.clone());
                                        info!("预计算任务完成: {}", task_id);
                                    }
                                    Err(e) => {
                                        completed_task.fail_execution(e.to_string());
                                        error!("预计算任务失败: {} - {}", task_id, e);
                                    }
                                }
                            }

                            // 更新统计信息
                            let mut stats = stats_clone.write().await;
                            stats.running_tasks = stats.running_tasks.saturating_sub(1);
                            match result {
                                Ok(_) => {
                                    stats.completed_tasks += 1;
                                }
                                Err(_) => {
                                    stats.failed_tasks += 1;
                                }
                            }
                        });
                    } else {
                        // 没有可用的信号量许可，将任务放回队列
                        task_queue.write().await.push(task);
                        break;
                    }
                }
            }

            info!("任务调度循环已停止");
        });

        Ok(())
    }

    /// 启动任务执行循环
    ///
    /// 处理任务通知通道中的任务
    async fn start_task_execution(&self) -> Result<()> {
        let mut receiver = self
            .task_receiver
            .write()
            .await
            .take()
            .ok_or_else(|| AppError::InternalServerError("任务接收器已被使用".to_string()))?;

        let task_queue = self.task_queue.clone();
        let shutdown = self.shutdown.clone();

        tokio::spawn(async move {
            while let Some(task) = receiver.recv().await {
                // 检查是否需要关闭
                if *shutdown.read().await {
                    break;
                }

                // 将任务添加到队列中等待调度
                task_queue.write().await.push(task);
            }

            info!("任务执行循环已停止");
        });

        Ok(())
    }

    /// 执行预计算任务
    ///
    /// # 参数
    /// - `task`: 要执行的预计算任务
    ///
    /// # 返回
    /// - Ok(stats): 执行成功，返回统计信息
    /// - Err: 执行失败
    async fn execute_precompute_task(task: &PrecomputeTask) -> Result<PrecomputeExecutionStats> {
        let start_time = Instant::now();

        debug!(
            "开始执行预计算任务: {} (类型: {:?})",
            task.id, task.task_type
        );

        let stats = match task.task_type {
            PrecomputeTaskType::HotQueryAnalysis => Self::execute_hot_query_analysis(task).await?,
            PrecomputeTaskType::ResultPregeneration => {
                Self::execute_result_pregeneration(task).await?
            }
            PrecomputeTaskType::CacheWarmup => Self::execute_cache_warmup(task).await?,
            PrecomputeTaskType::StatisticsUpdate => Self::execute_statistics_update(task).await?,
            PrecomputeTaskType::ExpiredDataCleanup => {
                Self::execute_expired_data_cleanup(task).await?
            }
        };

        let execution_time = start_time.elapsed();
        debug!(
            "预计算任务执行完成: {} (耗时: {:?})",
            task.id, execution_time
        );

        Ok(PrecomputeExecutionStats {
            processed_queries: stats.processed_queries,
            generated_results: stats.generated_results,
            updated_cache_entries: stats.updated_cache_entries,
            execution_time_ms: execution_time.as_millis() as u64,
            peak_memory_usage: stats.peak_memory_usage,
            database_queries: stats.database_queries,
            cache_hits: stats.cache_hits,
            cache_misses: stats.cache_misses,
        })
    }

    /// 执行热门搜索词分析任务
    ///
    /// # 参数
    /// - `task`: 预计算任务
    ///
    /// # 返回
    /// - 执行统计信息
    async fn execute_hot_query_analysis(task: &PrecomputeTask) -> Result<PrecomputeExecutionStats> {
        info!("执行热门搜索词分析任务: {}", task.id);

        // 模拟热门搜索词分析过程
        // 实际实现中会查询数据库，分析搜索日志
        tokio::time::sleep(Duration::from_millis(100)).await;

        Ok(PrecomputeExecutionStats {
            processed_queries: 50,
            generated_results: 0,
            updated_cache_entries: 0,
            execution_time_ms: 100,
            peak_memory_usage: 1024 * 1024, // 1MB
            database_queries: 5,
            cache_hits: 0,
            cache_misses: 0,
        })
    }

    /// 执行搜索结果预生成任务
    ///
    /// # 参数
    /// - `task`: 预计算任务
    ///
    /// # 返回
    /// - 执行统计信息
    async fn execute_result_pregeneration(
        task: &PrecomputeTask,
    ) -> Result<PrecomputeExecutionStats> {
        info!("执行搜索结果预生成任务: {}", task.id);

        let target_query = task
            .target_query
            .as_ref()
            .ok_or_else(|| AppError::ValidationError("预生成任务缺少目标查询".to_string()))?;

        debug!("为查询 '{}' 预生成搜索结果", target_query);

        // 模拟搜索结果预生成过程
        // 实际实现中会执行搜索查询，生成结果并缓存
        tokio::time::sleep(Duration::from_millis(500)).await;

        Ok(PrecomputeExecutionStats {
            processed_queries: 1,
            generated_results: 25,
            updated_cache_entries: 1,
            execution_time_ms: 500,
            peak_memory_usage: 2 * 1024 * 1024, // 2MB
            database_queries: 3,
            cache_hits: 0,
            cache_misses: 1,
        })
    }

    /// 执行缓存预热任务
    ///
    /// # 参数
    /// - `task`: 预计算任务
    ///
    /// # 返回
    /// - 执行统计信息
    async fn execute_cache_warmup(task: &PrecomputeTask) -> Result<PrecomputeExecutionStats> {
        info!("执行缓存预热任务: {}", task.id);

        // 模拟缓存预热过程
        // 实际实现中会将预计算结果加载到缓存中
        tokio::time::sleep(Duration::from_millis(200)).await;

        Ok(PrecomputeExecutionStats {
            processed_queries: 10,
            generated_results: 0,
            updated_cache_entries: 10,
            execution_time_ms: 200,
            peak_memory_usage: 5 * 1024 * 1024, // 5MB
            database_queries: 1,
            cache_hits: 0,
            cache_misses: 10,
        })
    }

    /// 执行统计数据更新任务
    ///
    /// # 参数
    /// - `task`: 预计算任务
    ///
    /// # 返回
    /// - 执行统计信息
    async fn execute_statistics_update(task: &PrecomputeTask) -> Result<PrecomputeExecutionStats> {
        info!("执行统计数据更新任务: {}", task.id);

        // 模拟统计数据更新过程
        // 实际实现中会更新搜索统计和分析数据
        tokio::time::sleep(Duration::from_millis(150)).await;

        Ok(PrecomputeExecutionStats {
            processed_queries: 100,
            generated_results: 0,
            updated_cache_entries: 5,
            execution_time_ms: 150,
            peak_memory_usage: 1024 * 1024, // 1MB
            database_queries: 2,
            cache_hits: 3,
            cache_misses: 2,
        })
    }

    /// 执行过期数据清理任务
    ///
    /// # 参数
    /// - `task`: 预计算任务
    ///
    /// # 返回
    /// - 执行统计信息
    async fn execute_expired_data_cleanup(
        task: &PrecomputeTask,
    ) -> Result<PrecomputeExecutionStats> {
        info!("执行过期数据清理任务: {}", task.id);

        // 模拟过期数据清理过程
        // 实际实现中会清理过期的预计算结果和缓存
        tokio::time::sleep(Duration::from_millis(300)).await;

        Ok(PrecomputeExecutionStats {
            processed_queries: 0,
            generated_results: 0,
            updated_cache_entries: 0,
            execution_time_ms: 300,
            peak_memory_usage: 512 * 1024, // 512KB
            database_queries: 1,
            cache_hits: 0,
            cache_misses: 0,
        })
    }
}
