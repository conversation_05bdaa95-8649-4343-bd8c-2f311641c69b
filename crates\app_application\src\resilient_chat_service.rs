//! # 弹性聊天应用服务
//!
//! 集成熔断器、限流器和降级策略的企业级聊天应用服务
//! 为消息搜索功能提供高可用性和稳定性保障

use crate::chat_service::{ ChatApplicationService, MessageResponse };
use app_common::error::{ AppError, Result };
use app_domain::entities::chat::SearchGlobalChatRoomMessagesRequest;
use app_domain::repositories::chat_repository::ChatRepositoryContract;
use app_infrastructure::{
    RateLimiterType,
    ResilienceConfig,
    ResilienceManager,
    ResilientMessageSearchCacheService,
    SearchFallbackResult,
    cache::SearchQueryResult,
};
use sea_orm::prelude::Uuid;
use std::sync::Arc;
use tracing::{ debug, error, info, warn };

/// 弹性聊天应用服务
///
/// 【目的】: 为聊天功能提供具备弹性能力的应用服务
/// 【特性】: 集成熔断器、限流器、降级策略，防止服务级联故障
pub struct ResilientChatApplicationService {
    /// 底层聊天应用服务
    chat_service: Arc<dyn ChatApplicationService>,
    /// 弹性消息搜索缓存服务
    resilient_cache_service: Option<Arc<ResilientMessageSearchCacheService>>,
    /// 弹性管理器
    resilience_manager: Arc<ResilienceManager>,
}

impl ResilientChatApplicationService {
    /// 创建新的弹性聊天应用服务
    ///
    /// 【参数】:
    /// - chat_repository: 聊天仓库实现
    /// - chat_domain_service: 聊天领域服务
    /// - user_repository: 用户仓库实现
    /// - resilient_cache_service: 弹性缓存服务（可选）
    /// - resilience_config: 弹性配置
    ///
    /// 【返回】: 弹性聊天应用服务实例
    pub async fn new(
        chat_repository: Arc<dyn ChatRepositoryContract>,
        chat_domain_service: Arc<dyn app_domain::services::chat_service::ChatDomainService>,
        user_repository: Arc<dyn app_domain::repositories::UserRepositoryContract>,
        resilient_cache_service: Option<Arc<ResilientMessageSearchCacheService>>,
        resilience_config: ResilienceConfig
    ) -> Result<Self> {
        info!("🛡️ 创建弹性聊天应用服务");

        // 创建底层聊天应用服务
        let chat_service: Arc<dyn ChatApplicationService> = Arc::new(
            crate::chat_service::ChatApplicationServiceImpl::new(
                chat_repository,
                chat_domain_service,
                user_repository
            )
        );

        // 创建弹性管理器
        let resilience_manager = Arc::new(
            ResilienceManager::new(resilience_config).await.map_err(|e|
                AppError::InternalServerError(format!("创建弹性管理器失败: {e}"))
            )?
        );

        // 启动监控任务
        resilience_manager.start_monitoring().await;

        let service = Self {
            chat_service,
            resilient_cache_service,
            resilience_manager,
        };

        // 执行缓存兼容性检查和清理
        service.perform_cache_compatibility_check().await;

        Ok(service)
    }

    /// 弹性搜索消息（带缓存和降级策略）
    ///
    /// 【参数】:
    /// - user_id: 用户ID
    /// - request: 搜索请求
    ///
    /// 【返回】: 搜索结果或降级结果
    async fn resilient_search_messages_with_cache(
        &self,
        user_id: Uuid,
        request: SearchGlobalChatRoomMessagesRequest
    ) -> Result<Vec<MessageResponse>> {
        let user_id_str = user_id.to_string();

        // 1. 检查用户级限流
        if
            !self.resilience_manager.check_rate_limit(
                RateLimiterType::UserBased,
                &user_id_str,
                1.0
            ).await
        {
            warn!("用户 {} 搜索被限流", user_id);
            return self.handle_rate_limit_fallback(&request, "用户级限流").await;
        }

        // 2. 检查搜索端点限流
        if
            !self.resilience_manager.check_rate_limit(
                RateLimiterType::EndpointBased,
                "search_messages",
                1.0
            ).await
        {
            warn!("搜索端点被限流");
            return self.handle_rate_limit_fallback(&request, "端点级限流").await;
        }

        // 3. 尝试从弹性缓存获取结果
        if let Some(cache_service) = &self.resilient_cache_service {
            match
                cache_service.resilient_get_cached_search_result(
                    &request.query,
                    &user_id_str,
                    None // 全局聊天室，room_id为None
                ).await
            {
                Ok(Some(cached_result)) => {
                    info!("从弹性缓存命中搜索结果: {}", request.query);
                    return Ok(self.convert_cached_result_to_messages(cached_result));
                }
                Ok(None) => {
                    debug!("弹性缓存未命中: {}", request.query);
                }
                Err(e) => {
                    warn!("弹性缓存查询失败: {}", e);
                    // 继续执行数据库查询
                }
            }
        }

        // 4. 使用熔断器保护数据库查询
        if
            let Some(circuit_breaker) =
                self.resilience_manager.get_circuit_breaker("database").await
        {
            match
                circuit_breaker.execute(async {
                    self.chat_service.search_messages_in_global_room(user_id, request.clone()).await
                }).await
            {
                Ok(messages) => {
                    self.resilience_manager.record_success().await;

                    // 异步缓存搜索结果
                    if let Some(cache_service) = &self.resilient_cache_service {
                        let cache_result = self.convert_messages_to_cache_result(
                            &messages,
                            &request,
                            &user_id_str
                        );
                        tokio::spawn({
                            let cache_service = cache_service.clone();
                            let user_id_str = user_id_str.clone();
                            async move {
                                if
                                    let Err(e) = cache_service.resilient_cache_search_result(
                                        &cache_result,
                                        &user_id_str
                                    ).await
                                {
                                    warn!("异步缓存搜索结果失败: {}", e);
                                }
                            }
                        });
                    }

                    info!("数据库搜索成功: {} 条结果", messages.len());
                    Ok(messages)
                }
                Err(app_infrastructure::CircuitBreakerError::CircuitOpen) => {
                    warn!("数据库熔断器开启，执行降级策略");
                    self.handle_database_failure_fallback(&request, "数据库熔断器开启").await
                }
                Err(app_infrastructure::CircuitBreakerError::OperationFailed(e)) => {
                    error!("数据库查询失败: {}", e);
                    self.resilience_manager.record_failure().await;
                    self.handle_database_failure_fallback(
                        &request,
                        &format!("数据库查询失败: {e}")
                    ).await
                }
                Err(e) => {
                    error!("数据库熔断器错误: {}", e);
                    self.resilience_manager.record_failure().await;
                    Err(AppError::InternalServerError(format!("数据库服务错误: {e}")))
                }
            }
        } else {
            // 如果没有熔断器，直接执行查询
            match self.chat_service.search_messages_in_global_room(user_id, request.clone()).await {
                Ok(messages) => {
                    self.resilience_manager.record_success().await;
                    Ok(messages)
                }
                Err(e) => {
                    self.resilience_manager.record_failure().await;
                    self.handle_database_failure_fallback(
                        &request,
                        &format!("数据库查询失败: {e}")
                    ).await
                }
            }
        }
    }

    /// 处理限流降级
    async fn handle_rate_limit_fallback(
        &self,
        request: &SearchGlobalChatRoomMessagesRequest,
        reason: &str
    ) -> Result<Vec<MessageResponse>> {
        match self.resilience_manager.execute_fallback("rate_limit", request, reason).await {
            Ok(fallback_result) => {
                info!("执行限流降级策略成功");
                Ok(self.convert_fallback_to_messages(fallback_result))
            }
            Err(e) => {
                error!("限流降级策略失败: {}", e);
                Err(AppError::BadRequest("请求频率过高，请稍后重试".to_string()))
            }
        }
    }

    /// 处理数据库故障降级
    async fn handle_database_failure_fallback(
        &self,
        request: &SearchGlobalChatRoomMessagesRequest,
        reason: &str
    ) -> Result<Vec<MessageResponse>> {
        match self.resilience_manager.execute_fallback("database_failure", request, reason).await {
            Ok(fallback_result) => {
                info!("执行数据库故障降级策略成功");
                Ok(self.convert_fallback_to_messages(fallback_result))
            }
            Err(e) => {
                error!("数据库故障降级策略失败: {}", e);
                Err(AppError::InternalServerError("搜索服务暂时不可用，请稍后重试".to_string()))
            }
        }
    }

    /// 将缓存结果转换为消息响应
    fn convert_cached_result_to_messages(
        &self,
        cached_result: SearchQueryResult
    ) -> Vec<MessageResponse> {
        // 这里需要根据实际的缓存结果结构进行转换
        // 暂时返回空结果，实际项目中需要完善转换逻辑
        vec![]
    }

    /// 将消息响应转换为缓存结果
    fn convert_messages_to_cache_result(
        &self,
        messages: &[MessageResponse],
        request: &SearchGlobalChatRoomMessagesRequest,
        user_id: &str
    ) -> SearchQueryResult {
        SearchQueryResult {
            query: request.query.clone(),
            user_id: user_id.to_string(),
            room_id: None,
            message_ids: messages
                .iter()
                .map(|msg| format!("{}:{}", msg.sender_id, msg.content))
                .collect(),
            total_count: messages.len() as u64,
            timestamp: chrono::Utc::now(),
        }
    }

    /// 将降级结果转换为消息响应
    fn convert_fallback_to_messages(
        &self,
        fallback_result: SearchFallbackResult
    ) -> Vec<MessageResponse> {
        fallback_result.messages
            .into_iter()
            .map(|msg| {
                MessageResponse {
                    id: msg.id,
                    content: msg.content,
                    sender_id: msg.sender_id,
                    chat_room_id: Uuid::nil(), // 使用默认值
                    message_type: app_domain::entities::message::MessageType::System,
                    reply_to_id: None,
                    metadata: None,
                    priority: 1, // 使用数字表示优先级
                    is_pinned: false,
                    expires_at: None,
                    created_at: msg.created_at,
                    updated_at: msg.created_at,
                }
            })
            .collect()
    }

    /// 获取弹性统计信息
    pub async fn get_resilience_stats(&self) -> app_infrastructure::ResilienceStats {
        self.resilience_manager.get_stats().await
    }
}

impl ResilientChatApplicationService {
    /// 弹性搜索消息的公共接口
    pub async fn search_messages_with_resilience(
        &self,
        user_id: Uuid,
        request: SearchGlobalChatRoomMessagesRequest
    ) -> Result<Vec<MessageResponse>> {
        // 使用弹性搜索方法
        self.resilient_search_messages_with_cache(user_id, request).await
    }

    /// 获取弹性统计信息的公共接口
    pub async fn get_resilience_statistics(&self) -> app_infrastructure::ResilienceStats {
        self.resilience_manager.get_stats().await
    }
    /// 执行缓存兼容性检查和清理
    ///
    /// 【目的】: 在服务启动时检查并清理不兼容的缓存数据
    /// 【说明】: 这是一个后台任务，不会阻塞服务启动
    async fn perform_cache_compatibility_check(&self) {
        if let Some(cache_service) = &self.resilient_cache_service {
            info!("🔍 开始执行缓存兼容性检查...");

            // 异步执行缓存兼容性检查，不阻塞服务启动
            let cache_service_clone = Arc::clone(cache_service);
            tokio::spawn(async move {
                match cache_service_clone.perform_compatibility_check().await {
                    Ok(stats) => {
                        info!(
                            "✅ 缓存兼容性检查完成 - 检查键数: {}, 清理键数: {}",
                            stats.checked_keys,
                            stats.cleaned_keys
                        );
                    }
                    Err(e) => {
                        warn!("⚠️  缓存兼容性检查失败: {}", e);
                    }
                }
            });
        } else {
            debug!("跳过缓存兼容性检查（未配置缓存服务）");
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use app_infrastructure::ResilienceConfig;

    // 注意：这些测试需要mock依赖，在实际项目中需要完善

    #[tokio::test]
    async fn test_resilient_chat_service_creation() {
        // 这个测试需要mock依赖，暂时跳过具体实现
        // 在实际项目中，应该使用mock或测试容器

        // let mock_repository = Arc::new(MockChatRepository::new());
        // let mock_domain_service = Arc::new(ChatDomainService::new());
        // let resilience_config = ResilienceConfig::default();

        // let service = ResilientChatApplicationService::new(
        //     mock_repository,
        //     mock_domain_service,
        //     None,
        //     resilience_config,
        // ).await;

        // assert!(service.is_ok());
    }
}
