//! # 搜索结果通知器模块
//!
//! 实现搜索任务完成后的结果通知机制，支持：
//! - WebSocket实时通知
//! - HTTP回调通知
//! - 结果缓存管理
//! - 通知重试机制
//! - 通知状态跟踪

use crate::search_task_scheduler::TaskResultNotifier;
use app_common::{AppError, Result};
use app_domain::entities::search_task::{SearchTask, SearchTaskResult};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{RwLock, mpsc};
use tokio::time::{Duration, Instant, timeout};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// 通知类型枚举
///
/// 定义不同的通知方式
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum NotificationType {
    /// WebSocket实时通知
    WebSocket,
    /// HTTP回调通知
    HttpCallback,
    /// 内部事件通知
    InternalEvent,
}

/// 通知状态枚举
///
/// 跟踪通知的发送状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum NotificationStatus {
    /// 待发送
    Pending,
    /// 发送中
    Sending,
    /// 发送成功
    Sent,
    /// 发送失败
    Failed,
    /// 已重试
    Retried,
}

/// 通知配置
///
/// 定义通知器的配置参数
#[derive(Debug, Clone)]
pub struct NotifierConfig {
    /// HTTP客户端超时时间（秒）
    pub http_timeout_seconds: u64,
    /// 最大重试次数
    pub max_retries: u32,
    /// 重试间隔（毫秒）
    pub retry_delay_ms: u64,
    /// 通知缓存TTL（秒）
    pub cache_ttl_seconds: u64,
    /// 是否启用结果缓存
    pub enable_result_cache: bool,
    /// WebSocket连接池大小
    pub websocket_pool_size: usize,
}

impl Default for NotifierConfig {
    fn default() -> Self {
        Self {
            http_timeout_seconds: 10,
            max_retries: 3,
            retry_delay_ms: 1000,
            cache_ttl_seconds: 3600, // 1小时
            enable_result_cache: true,
            websocket_pool_size: 1000,
        }
    }
}

/// 通知记录
///
/// 记录单个通知的详细信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationRecord {
    /// 通知ID
    pub id: Uuid,
    /// 关联的任务ID
    pub task_id: Uuid,
    /// 用户ID
    pub user_id: Uuid,
    /// 通知类型
    pub notification_type: NotificationType,
    /// 通知状态
    pub status: NotificationStatus,
    /// 目标地址（WebSocket连接ID或HTTP URL）
    pub target: String,
    /// 通知内容
    pub content: serde_json::Value,
    /// 重试次数
    pub retry_count: u32,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
    /// 最后尝试时间
    pub last_attempt_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 完成时间
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 错误信息
    pub error_message: Option<String>,
}

impl NotificationRecord {
    /// 创建新的通知记录
    pub fn new(
        task_id: Uuid,
        user_id: Uuid,
        notification_type: NotificationType,
        target: String,
        content: serde_json::Value,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            task_id,
            user_id,
            notification_type,
            status: NotificationStatus::Pending,
            target,
            content,
            retry_count: 0,
            created_at: chrono::Utc::now(),
            last_attempt_at: None,
            completed_at: None,
            error_message: None,
        }
    }
}

/// WebSocket连接管理器trait
///
/// 定义WebSocket连接管理接口
#[async_trait::async_trait]
pub trait WebSocketManager: Send + Sync {
    /// 发送消息到指定用户的WebSocket连接
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    /// - `message`: 要发送的消息
    ///
    /// # 返回
    /// - Ok(count): 成功发送的连接数
    /// - Err: 发送失败
    async fn send_to_user(&self, user_id: Uuid, message: serde_json::Value) -> Result<usize>;

    /// 检查用户是否有活跃的WebSocket连接
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - true: 用户有活跃连接
    /// - false: 用户无活跃连接
    async fn has_active_connection(&self, user_id: Uuid) -> bool;
}

/// 搜索结果通知器
///
/// 负责处理搜索任务完成后的结果通知
pub struct SearchResultNotifier {
    /// 通知器配置
    config: NotifierConfig,
    /// HTTP客户端
    http_client: Client,
    /// WebSocket连接管理器
    websocket_manager: Option<Arc<dyn WebSocketManager>>,
    /// 通知记录缓存
    notification_records: Arc<RwLock<HashMap<Uuid, NotificationRecord>>>,
    /// 结果缓存
    result_cache: Arc<RwLock<HashMap<Uuid, (SearchTaskResult, Instant)>>>,
    /// 重试队列发送端
    retry_sender: mpsc::UnboundedSender<NotificationRecord>,
    /// 重试队列接收端
    retry_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<NotificationRecord>>>>,
}

impl SearchResultNotifier {
    /// 创建新的搜索结果通知器
    ///
    /// # 参数
    /// - `config`: 通知器配置
    /// - `websocket_manager`: WebSocket连接管理器（可选）
    ///
    /// # 返回
    /// - 新创建的通知器实例
    pub fn new(
        config: NotifierConfig,
        websocket_manager: Option<Arc<dyn WebSocketManager>>,
    ) -> Self {
        let http_client = Client::builder()
            .timeout(Duration::from_secs(config.http_timeout_seconds))
            .build()
            .expect("创建HTTP客户端失败");

        let (retry_sender, retry_receiver) = mpsc::unbounded_channel();

        Self {
            config,
            http_client,
            websocket_manager,
            notification_records: Arc::new(RwLock::new(HashMap::new())),
            result_cache: Arc::new(RwLock::new(HashMap::new())),
            retry_sender,
            retry_receiver: Arc::new(RwLock::new(Some(retry_receiver))),
        }
    }

    /// 启动通知器
    ///
    /// 启动重试处理任务和缓存清理任务
    pub async fn start(&self) -> Result<()> {
        info!("启动搜索结果通知器");

        // 启动重试处理任务
        self.spawn_retry_handler().await;

        // 启动缓存清理任务
        self.spawn_cache_cleaner().await;

        info!("搜索结果通知器启动完成");
        Ok(())
    }

    /// 缓存搜索结果
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    /// - `result`: 搜索结果
    pub async fn cache_result(&self, task_id: Uuid, result: SearchTaskResult) {
        if self.config.enable_result_cache {
            let mut cache = self.result_cache.write().await;
            cache.insert(task_id, (result, Instant::now()));
            debug!("缓存搜索结果: {}", task_id);
        }
    }

    /// 获取缓存的搜索结果
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    ///
    /// # 返回
    /// - Some(result): 缓存的结果
    /// - None: 结果未缓存或已过期
    pub async fn get_cached_result(&self, task_id: Uuid) -> Option<SearchTaskResult> {
        if !self.config.enable_result_cache {
            return None;
        }

        let cache = self.result_cache.read().await;
        if let Some((result, cached_at)) = cache.get(&task_id) {
            let ttl = Duration::from_secs(self.config.cache_ttl_seconds);
            if cached_at.elapsed() < ttl {
                debug!("命中缓存结果: {}", task_id);
                return Some(result.clone());
            }
        }

        None
    }

    /// 发送WebSocket通知
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    /// - `content`: 通知内容
    ///
    /// # 返回
    /// - Ok(count): 成功发送的连接数
    /// - Err: 发送失败
    async fn send_websocket_notification(
        &self,
        user_id: Uuid,
        content: serde_json::Value,
    ) -> Result<usize> {
        if let Some(ws_manager) = &self.websocket_manager {
            ws_manager.send_to_user(user_id, content).await
        } else {
            Err(AppError::InternalServerError(
                "WebSocket管理器未配置".to_string(),
            ))
        }
    }

    /// 发送HTTP回调通知
    ///
    /// # 参数
    /// - `callback_url`: 回调URL
    /// - `content`: 通知内容
    ///
    /// # 返回
    /// - Ok(()): 发送成功
    /// - Err: 发送失败
    async fn send_http_callback(
        &self,
        callback_url: &str,
        content: serde_json::Value,
    ) -> Result<()> {
        let response = timeout(
            Duration::from_secs(self.config.http_timeout_seconds),
            self.http_client.post(callback_url).json(&content).send(),
        )
        .await;

        match response {
            Ok(Ok(resp)) => {
                if resp.status().is_success() {
                    debug!("HTTP回调发送成功: {}", callback_url);
                    Ok(())
                } else {
                    let error_msg = format!("HTTP回调失败，状态码: {}", resp.status());
                    warn!("{}", error_msg);
                    Err(AppError::InternalServerError(error_msg))
                }
            }
            Ok(Err(e)) => {
                let error_msg = format!("HTTP回调请求失败: {e}");
                warn!("{}", error_msg);
                Err(AppError::InternalServerError(error_msg))
            }
            Err(_) => {
                let error_msg = "HTTP回调超时".to_string();
                warn!("{}", error_msg);
                Err(AppError::InternalServerError(error_msg))
            }
        }
    }

    /// 处理通知重试
    ///
    /// # 参数
    /// - `record`: 通知记录
    async fn handle_retry(&self, mut record: NotificationRecord) {
        if record.retry_count >= self.config.max_retries {
            warn!("通知重试次数已达上限: {}", record.id);
            record.status = NotificationStatus::Failed;
            record.completed_at = Some(chrono::Utc::now());

            // 更新记录
            self.notification_records
                .write()
                .await
                .insert(record.id, record);
            return;
        }

        // 等待重试间隔
        tokio::time::sleep(Duration::from_millis(self.config.retry_delay_ms)).await;

        record.retry_count += 1;
        record.status = NotificationStatus::Retried;
        record.last_attempt_at = Some(chrono::Utc::now());

        debug!("重试通知: {} (第{}次)", record.id, record.retry_count);

        // 根据通知类型重新发送
        let result = match record.notification_type {
            NotificationType::WebSocket => self
                .send_websocket_notification(record.user_id, record.content.clone())
                .await
                .map(|_| ()),
            NotificationType::HttpCallback => {
                self.send_http_callback(&record.target, record.content.clone())
                    .await
            }
            NotificationType::InternalEvent => {
                // 内部事件通知暂时不实现重试
                Ok(())
            }
        };

        match result {
            Ok(_) => {
                record.status = NotificationStatus::Sent;
                record.completed_at = Some(chrono::Utc::now());
                info!("通知重试成功: {}", record.id);
            }
            Err(e) => {
                record.error_message = Some(e.to_string());
                warn!("通知重试失败: {} - {}", record.id, e);

                // 如果还能重试，加入重试队列
                if record.retry_count < self.config.max_retries {
                    if let Err(e) = self.retry_sender.send(record.clone()) {
                        error!("加入重试队列失败: {:?}", e);
                    }
                } else {
                    record.status = NotificationStatus::Failed;
                    record.completed_at = Some(chrono::Utc::now());
                }
            }
        }

        // 更新记录
        self.notification_records
            .write()
            .await
            .insert(record.id, record);
    }

    /// 生成重试处理任务
    async fn spawn_retry_handler(&self) {
        let retry_receiver = self.retry_receiver.clone();
        let notifier = Arc::new(self.clone());

        tokio::spawn(async move {
            let mut receiver_guard = retry_receiver.write().await;
            if let Some(mut receiver) = receiver_guard.take() {
                drop(receiver_guard);

                info!("重试处理任务启动");

                while let Some(record) = receiver.recv().await {
                    let notifier_clone = notifier.clone();
                    tokio::spawn(async move {
                        notifier_clone.handle_retry(record).await;
                    });
                }

                info!("重试处理任务停止");
            }
        });
    }

    /// 生成缓存清理任务
    async fn spawn_cache_cleaner(&self) {
        let result_cache = self.result_cache.clone();
        let notification_records = self.notification_records.clone();
        let cache_ttl = self.config.cache_ttl_seconds;

        tokio::spawn(async move {
            info!("缓存清理任务启动");

            loop {
                tokio::time::sleep(Duration::from_secs(cache_ttl / 2)).await;

                let now = Instant::now();
                let ttl_duration = Duration::from_secs(cache_ttl);

                // 清理过期的结果缓存
                {
                    let mut cache = result_cache.write().await;
                    let before_count = cache.len();
                    cache.retain(|_, (_, cached_at)| now.duration_since(*cached_at) < ttl_duration);
                    let after_count = cache.len();

                    if before_count > after_count {
                        debug!("清理过期结果缓存: {} -> {}", before_count, after_count);
                    }
                }

                // 清理过期的通知记录
                {
                    let mut records = notification_records.write().await;
                    let before_count = records.len();
                    let cutoff_time =
                        chrono::Utc::now() - chrono::Duration::seconds(cache_ttl as i64);

                    records.retain(|_, record| {
                        record
                            .completed_at
                            .is_none_or(|completed| completed > cutoff_time)
                    });

                    let after_count = records.len();
                    if before_count > after_count {
                        debug!("清理过期通知记录: {} -> {}", before_count, after_count);
                    }
                }
            }
        });
    }

    /// 获取通知统计信息
    ///
    /// # 返回
    /// - 通知统计信息
    pub async fn get_notification_stats(&self) -> HashMap<String, u64> {
        let records = self.notification_records.read().await;
        let mut stats = HashMap::new();

        let mut pending = 0u64;
        let mut sending = 0u64;
        let mut sent = 0u64;
        let mut failed = 0u64;
        let mut retried = 0u64;

        for record in records.values() {
            match record.status {
                NotificationStatus::Pending => {
                    pending += 1;
                }
                NotificationStatus::Sending => {
                    sending += 1;
                }
                NotificationStatus::Sent => {
                    sent += 1;
                }
                NotificationStatus::Failed => {
                    failed += 1;
                }
                NotificationStatus::Retried => {
                    retried += 1;
                }
            }
        }

        stats.insert("pending".to_string(), pending);
        stats.insert("sending".to_string(), sending);
        stats.insert("sent".to_string(), sent);
        stats.insert("failed".to_string(), failed);
        stats.insert("retried".to_string(), retried);
        stats.insert("total".to_string(), records.len() as u64);

        stats
    }
}

// 为了支持clone，需要实现Clone trait
impl Clone for SearchResultNotifier {
    fn clone(&self) -> Self {
        let (retry_sender, retry_receiver) = mpsc::unbounded_channel();

        Self {
            config: self.config.clone(),
            http_client: self.http_client.clone(),
            websocket_manager: self.websocket_manager.clone(),
            notification_records: self.notification_records.clone(),
            result_cache: self.result_cache.clone(),
            retry_sender,
            retry_receiver: Arc::new(RwLock::new(Some(retry_receiver))),
        }
    }
}

#[async_trait::async_trait]
impl TaskResultNotifier for SearchResultNotifier {
    /// 通知任务完成
    ///
    /// # 参数
    /// - `task`: 完成的任务
    /// - `result`: 任务结果（成功时）
    /// - `error`: 错误信息（失败时）
    async fn notify_completion(
        &self,
        task: &SearchTask,
        result: Option<SearchTaskResult>,
        error: Option<String>,
    ) -> Result<()> {
        debug!("开始通知任务完成: {}", task.id);

        // 缓存结果（如果成功）
        if let Some(ref result) = result {
            self.cache_result(task.id, result.clone()).await;
        }

        // 构建通知内容
        let notification_content = serde_json::json!({
            "task_id": task.id,
            "user_id": task.user_id,
            "query": task.query,
            "status": if result.is_some() { "completed" } else { "failed" },
            "result": result,
            "error": error,
            "timestamp": chrono::Utc::now()
        });

        // 发送WebSocket通知（如果用户在线）
        if let Some(ws_manager) = &self.websocket_manager {
            if ws_manager.has_active_connection(task.user_id).await {
                let ws_record = NotificationRecord::new(
                    task.id,
                    task.user_id,
                    NotificationType::WebSocket,
                    "websocket".to_string(),
                    notification_content.clone(),
                );

                match self
                    .send_websocket_notification(task.user_id, notification_content.clone())
                    .await
                {
                    Ok(count) => {
                        let mut record = ws_record;
                        record.status = NotificationStatus::Sent;
                        record.completed_at = Some(chrono::Utc::now());
                        self.notification_records
                            .write()
                            .await
                            .insert(record.id, record);
                        info!("WebSocket通知发送成功: {} (连接数: {})", task.id, count);
                    }
                    Err(e) => {
                        let mut record = ws_record;
                        record.status = NotificationStatus::Failed;
                        record.error_message = Some(e.to_string());

                        // 加入重试队列
                        if let Err(e) = self.retry_sender.send(record.clone()) {
                            error!("加入重试队列失败: {:?}", e);
                        }

                        self.notification_records
                            .write()
                            .await
                            .insert(record.id, record);
                        warn!("WebSocket通知发送失败: {} - {}", task.id, e);
                    }
                }
            }
        }

        // 发送HTTP回调通知（如果配置了回调URL）
        if let Some(callback_url) = &task.callback_url {
            let callback_record = NotificationRecord::new(
                task.id,
                task.user_id,
                NotificationType::HttpCallback,
                callback_url.clone(),
                notification_content.clone(),
            );

            match self
                .send_http_callback(callback_url, notification_content)
                .await
            {
                Ok(()) => {
                    let mut record = callback_record;
                    record.status = NotificationStatus::Sent;
                    record.completed_at = Some(chrono::Utc::now());
                    self.notification_records
                        .write()
                        .await
                        .insert(record.id, record);
                    info!("HTTP回调通知发送成功: {}", task.id);
                }
                Err(e) => {
                    let mut record = callback_record;
                    record.status = NotificationStatus::Failed;
                    record.error_message = Some(e.to_string());

                    // 加入重试队列
                    if let Err(e) = self.retry_sender.send(record.clone()) {
                        error!("加入重试队列失败: {:?}", e);
                    }

                    self.notification_records
                        .write()
                        .await
                        .insert(record.id, record);
                    warn!("HTTP回调通知发送失败: {} - {}", task.id, e);
                }
            }
        }

        Ok(())
    }
}
