//! # 搜索任务调度器模块
//!
//! 基于Tokio实现的高性能搜索任务调度器，负责：
//! - 任务分发策略
//! - 工作线程池管理
//! - 负载均衡
//! - 任务超时处理
//! - 错误恢复机制

use crate::async_search_queue::{AsyncSearchQueue, QueueConfig};
use app_common::{AppError, Result};
use app_domain::entities::search_task::{SearchTask, SearchTaskResult};
use std::sync::Arc;
use tokio::sync::{RwLock, mpsc};
use tokio::time::{Duration, Instant, timeout};
use tracing::{debug, info, warn};
use uuid::Uuid;

/// 任务执行器trait
///
/// 定义搜索任务的执行接口
#[async_trait::async_trait]
pub trait SearchTaskExecutor: Send + Sync {
    /// 执行搜索任务
    ///
    /// # 参数
    /// - `task`: 要执行的搜索任务
    ///
    /// # 返回
    /// - Ok(result): 执行成功，返回搜索结果
    /// - Err: 执行失败
    async fn execute(&self, task: &SearchTask) -> Result<SearchTaskResult>;
}

/// 任务结果通知器trait
///
/// 定义任务结果通知接口
#[async_trait::async_trait]
pub trait TaskResultNotifier: Send + Sync {
    /// 通知任务完成
    ///
    /// # 参数
    /// - `task`: 完成的任务
    /// - `result`: 任务结果（成功时）
    /// - `error`: 错误信息（失败时）
    async fn notify_completion(
        &self,
        task: &SearchTask,
        result: Option<SearchTaskResult>,
        error: Option<String>,
    ) -> Result<()>;
}

/// 调度器配置
///
/// 定义搜索任务调度器的配置参数
#[derive(Debug, Clone)]
pub struct SchedulerConfig {
    /// 工作线程数量
    pub worker_count: usize,
    /// 任务批处理大小
    pub batch_size: usize,
    /// 工作线程空闲等待时间（毫秒）
    pub worker_idle_timeout_ms: u64,
    /// 任务重试间隔（毫秒）
    pub retry_delay_ms: u64,
    /// 调度器监控间隔（秒）
    pub monitoring_interval_seconds: u64,
    /// 是否启用负载均衡
    pub enable_load_balancing: bool,
}

impl Default for SchedulerConfig {
    fn default() -> Self {
        Self {
            worker_count: num_cpus::get(),
            batch_size: 10,
            worker_idle_timeout_ms: 1000,
            retry_delay_ms: 1000,
            monitoring_interval_seconds: 30,
            enable_load_balancing: true,
        }
    }
}

/// 工作线程统计信息
///
/// 记录单个工作线程的统计数据
#[derive(Debug, Clone)]
pub struct WorkerMetrics {
    /// 工作线程ID
    pub worker_id: usize,
    /// 处理的任务总数
    pub processed_tasks: u64,
    /// 成功任务数
    pub successful_tasks: u64,
    /// 失败任务数
    pub failed_tasks: u64,
    /// 平均处理时间（毫秒）
    pub avg_processing_time_ms: f64,
    /// 当前状态（空闲/忙碌）
    pub is_busy: bool,
    /// 最后活动时间
    pub last_activity: Instant,
}

impl Default for WorkerMetrics {
    fn default() -> Self {
        Self {
            worker_id: 0,
            processed_tasks: 0,
            successful_tasks: 0,
            failed_tasks: 0,
            avg_processing_time_ms: 0.0,
            is_busy: false,
            last_activity: Instant::now(),
        }
    }
}

/// 调度器统计信息
///
/// 记录整个调度器的统计数据
#[derive(Debug, Clone)]
pub struct SchedulerMetrics {
    /// 活跃工作线程数
    pub active_workers: usize,
    /// 空闲工作线程数
    pub idle_workers: usize,
    /// 总处理任务数
    pub total_processed: u64,
    /// 总成功任务数
    pub total_successful: u64,
    /// 总失败任务数
    pub total_failed: u64,
    /// 系统吞吐量（任务/秒）
    pub throughput_per_second: f64,
    /// 工作线程统计
    pub worker_metrics: Vec<WorkerMetrics>,
    /// 最后更新时间
    pub last_updated: Instant,
}

impl Default for SchedulerMetrics {
    fn default() -> Self {
        Self {
            active_workers: 0,
            idle_workers: 0,
            total_processed: 0,
            total_successful: 0,
            total_failed: 0,
            throughput_per_second: 0.0,
            worker_metrics: Vec::new(),
            last_updated: Instant::now(),
        }
    }
}

/// 搜索任务调度器
///
/// 负责管理和调度搜索任务的执行
pub struct SearchTaskScheduler {
    /// 调度器配置
    config: SchedulerConfig,
    /// 任务队列
    queue: Arc<AsyncSearchQueue>,
    /// 任务执行器
    executor: Arc<dyn SearchTaskExecutor>,
    /// 结果通知器
    notifier: Arc<dyn TaskResultNotifier>,
    /// 工作线程统计
    worker_metrics: Arc<RwLock<Vec<WorkerMetrics>>>,
    /// 调度器统计
    scheduler_metrics: Arc<RwLock<SchedulerMetrics>>,
    /// 关闭信号发送端
    shutdown_sender: mpsc::UnboundedSender<()>,
    /// 关闭信号接收端
    shutdown_receiver: Arc<RwLock<Option<mpsc::UnboundedReceiver<()>>>>,
    /// 调度器运行状态
    is_running: Arc<RwLock<bool>>,
}

impl SearchTaskScheduler {
    /// 创建新的搜索任务调度器
    ///
    /// # 参数
    /// - `config`: 调度器配置
    /// - `queue_config`: 队列配置
    /// - `executor`: 任务执行器
    /// - `notifier`: 结果通知器
    ///
    /// # 返回
    /// - 新创建的调度器实例
    pub fn new(
        config: SchedulerConfig,
        queue_config: QueueConfig,
        executor: Arc<dyn SearchTaskExecutor>,
        notifier: Arc<dyn TaskResultNotifier>,
    ) -> Self {
        let queue = Arc::new(AsyncSearchQueue::new(queue_config));
        let (shutdown_sender, shutdown_receiver) = mpsc::unbounded_channel();

        // 初始化工作线程统计
        let worker_metrics = (0..config.worker_count)
            .map(|i| WorkerMetrics {
                worker_id: i,
                last_activity: Instant::now(),
                ..Default::default()
            })
            .collect();

        Self {
            config,
            queue,
            executor,
            notifier,
            worker_metrics: Arc::new(RwLock::new(worker_metrics)),
            scheduler_metrics: Arc::new(RwLock::new(SchedulerMetrics::default())),
            shutdown_sender,
            shutdown_receiver: Arc::new(RwLock::new(Some(shutdown_receiver))),
            is_running: Arc::new(RwLock::new(false)),
        }
    }

    /// 启动调度器
    ///
    /// 启动工作线程池和监控任务
    pub async fn start(&self) -> Result<()> {
        if *self.is_running.read().await {
            return Err(AppError::ValidationError("调度器已在运行".to_string()));
        }

        info!(
            "启动搜索任务调度器，工作线程数: {}",
            self.config.worker_count
        );
        *self.is_running.write().await = true;

        // 启动工作线程
        for worker_id in 0..self.config.worker_count {
            self.spawn_worker(worker_id).await;
        }

        // 启动监控任务
        self.spawn_monitor().await;

        info!("搜索任务调度器启动完成");
        Ok(())
    }

    /// 停止调度器
    ///
    /// 优雅关闭所有工作线程
    pub async fn stop(&self) -> Result<()> {
        if !*self.is_running.read().await {
            return Ok(());
        }

        info!("开始停止搜索任务调度器...");
        *self.is_running.write().await = false;

        // 发送关闭信号
        if let Err(e) = self.shutdown_sender.send(()) {
            warn!("发送关闭信号失败: {:?}", e);
        }

        // 关闭队列
        self.queue.shutdown().await;

        info!("搜索任务调度器已停止");
        Ok(())
    }

    /// 提交搜索任务
    ///
    /// # 参数
    /// - `task`: 要提交的搜索任务
    ///
    /// # 返回
    /// - Ok(task_id): 成功提交，返回任务ID
    /// - Err: 提交失败
    pub async fn submit_task(&self, task: SearchTask) -> Result<Uuid> {
        if !*self.is_running.read().await {
            return Err(AppError::ValidationError("调度器未运行".to_string()));
        }

        self.queue.enqueue(task).await
    }

    /// 取消任务
    ///
    /// # 参数
    /// - `task_id`: 要取消的任务ID
    ///
    /// # 返回
    /// - true: 任务成功取消
    /// - false: 任务未找到或无法取消
    pub async fn cancel_task(&self, task_id: Uuid) -> bool {
        self.queue.cancel_task(task_id).await
    }

    /// 获取任务状态
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    ///
    /// # 返回
    /// - Some(task): 任务信息
    /// - None: 任务未找到
    pub async fn get_task_status(&self, task_id: Uuid) -> Option<SearchTask> {
        self.queue.get_task_status(task_id).await
    }

    /// 获取调度器统计信息
    ///
    /// # 返回
    /// - 当前调度器统计信息
    pub async fn get_metrics(&self) -> SchedulerMetrics {
        let mut metrics = self.scheduler_metrics.write().await;
        let worker_metrics = self.worker_metrics.read().await;

        // 更新统计信息
        metrics.active_workers = worker_metrics.iter().filter(|w| w.is_busy).count();
        metrics.idle_workers = worker_metrics.len() - metrics.active_workers;
        metrics.worker_metrics = worker_metrics.clone();

        // 计算总统计
        metrics.total_processed = worker_metrics.iter().map(|w| w.processed_tasks).sum();
        metrics.total_successful = worker_metrics.iter().map(|w| w.successful_tasks).sum();
        metrics.total_failed = worker_metrics.iter().map(|w| w.failed_tasks).sum();

        // 计算吞吐量
        let elapsed = metrics.last_updated.elapsed().as_secs_f64();
        if elapsed > 0.0 {
            metrics.throughput_per_second = (metrics.total_processed as f64) / elapsed;
        }

        metrics.last_updated = Instant::now();
        metrics.clone()
    }

    /// 生成工作线程
    ///
    /// # 参数
    /// - `worker_id`: 工作线程ID
    async fn spawn_worker(&self, worker_id: usize) {
        let queue = self.queue.clone();
        let executor = self.executor.clone();
        let notifier = self.notifier.clone();
        let worker_metrics = self.worker_metrics.clone();
        let is_running = self.is_running.clone();
        let config = self.config.clone();

        tokio::spawn(async move {
            info!("工作线程 {} 启动", worker_id);

            while *is_running.read().await {
                // 获取并发控制许可
                let semaphore = queue.get_semaphore();
                let permit = semaphore.acquire().await;
                if permit.is_err() {
                    break;
                }
                let _permit = permit.unwrap();

                // 从队列获取任务
                if let Some(task) = queue.dequeue().await {
                    // 更新工作线程状态
                    {
                        let mut metrics = worker_metrics.write().await;
                        if let Some(worker_metric) = metrics.get_mut(worker_id) {
                            worker_metric.is_busy = true;
                            worker_metric.last_activity = Instant::now();
                        }
                    }

                    let task_id = task.id;
                    let start_time = Instant::now();

                    debug!("工作线程 {} 开始处理任务: {}", worker_id, task_id);

                    // 执行任务（带超时）
                    let execution_result = timeout(
                        Duration::from_secs(task.timeout_seconds as u64),
                        executor.execute(&task),
                    )
                    .await;

                    let execution_time = start_time.elapsed();
                    let execution_time_ms = execution_time.as_millis() as u64;

                    // 处理执行结果
                    match execution_result {
                        Ok(Ok(result)) => {
                            // 任务执行成功
                            queue.complete_task(task_id, true, None).await;

                            if let Err(e) =
                                notifier.notify_completion(&task, Some(result), None).await
                            {
                                warn!("通知任务完成失败: {:?}", e);
                            }

                            // 更新工作线程统计
                            {
                                let mut metrics = worker_metrics.write().await;
                                if let Some(worker_metric) = metrics.get_mut(worker_id) {
                                    worker_metric.processed_tasks += 1;
                                    worker_metric.successful_tasks += 1;
                                    worker_metric.avg_processing_time_ms = (worker_metric
                                        .avg_processing_time_ms
                                        * ((worker_metric.processed_tasks - 1) as f64)
                                        + (execution_time_ms as f64))
                                        / (worker_metric.processed_tasks as f64);
                                }
                            }

                            info!(
                                "工作线程 {} 完成任务: {} (耗时: {}ms)",
                                worker_id, task_id, execution_time_ms
                            );
                        }
                        Ok(Err(e)) => {
                            // 任务执行失败
                            let error_msg = e.to_string();
                            queue
                                .complete_task(task_id, false, Some(error_msg.clone()))
                                .await;

                            if let Err(e) = notifier
                                .notify_completion(&task, None, Some(error_msg))
                                .await
                            {
                                warn!("通知任务失败失败: {:?}", e);
                            }

                            // 更新工作线程统计
                            {
                                let mut metrics = worker_metrics.write().await;
                                if let Some(worker_metric) = metrics.get_mut(worker_id) {
                                    worker_metric.processed_tasks += 1;
                                    worker_metric.failed_tasks += 1;
                                }
                            }

                            warn!(
                                "工作线程 {} 任务失败: {} (耗时: {}ms, 错误: {})",
                                worker_id, task_id, execution_time_ms, e
                            );
                        }
                        Err(_) => {
                            // 任务超时
                            let error_msg = format!("任务执行超时 ({}秒)", task.timeout_seconds);
                            queue
                                .complete_task(task_id, false, Some(error_msg.clone()))
                                .await;

                            if let Err(e) = notifier
                                .notify_completion(&task, None, Some(error_msg))
                                .await
                            {
                                warn!("通知任务超时失败: {:?}", e);
                            }

                            // 更新工作线程统计
                            {
                                let mut metrics = worker_metrics.write().await;
                                if let Some(worker_metric) = metrics.get_mut(worker_id) {
                                    worker_metric.processed_tasks += 1;
                                    worker_metric.failed_tasks += 1;
                                }
                            }

                            warn!(
                                "工作线程 {} 任务超时: {} (超时时间: {}秒)",
                                worker_id, task_id, task.timeout_seconds
                            );
                        }
                    }

                    // 更新工作线程状态为空闲
                    {
                        let mut metrics = worker_metrics.write().await;
                        if let Some(worker_metric) = metrics.get_mut(worker_id) {
                            worker_metric.is_busy = false;
                            worker_metric.last_activity = Instant::now();
                        }
                    }
                } else {
                    // 队列为空，等待一段时间
                    tokio::time::sleep(Duration::from_millis(config.worker_idle_timeout_ms)).await;
                }
            }

            info!("工作线程 {} 停止", worker_id);
        });
    }

    /// 生成监控任务
    async fn spawn_monitor(&self) {
        let scheduler_metrics = self.scheduler_metrics.clone();
        let queue = self.queue.clone();
        let is_running = self.is_running.clone();
        let monitoring_interval = self.config.monitoring_interval_seconds;

        tokio::spawn(async move {
            info!("调度器监控任务启动");

            while *is_running.read().await {
                tokio::time::sleep(Duration::from_secs(monitoring_interval)).await;

                // 获取队列统计
                let queue_metrics = queue.get_metrics().await;

                // 更新调度器统计
                {
                    let mut metrics = scheduler_metrics.write().await;
                    metrics.last_updated = Instant::now();
                }

                debug!(
                    "队列统计 - 待处理: {}, 处理中: {}, 已完成: {}, 失败: {}, 吞吐量: {:.2}/s",
                    queue_metrics.pending_tasks,
                    queue_metrics.processing_tasks,
                    queue_metrics.completed_tasks,
                    queue_metrics.failed_tasks,
                    queue_metrics.throughput_per_second
                );
            }

            info!("调度器监控任务停止");
        });
    }
}
