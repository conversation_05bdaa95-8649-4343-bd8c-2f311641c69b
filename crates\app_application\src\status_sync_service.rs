//! # 状态同步应用服务
//!
//! 状态同步相关的业务用例实现，包括：
//! - 用户在线状态同步
//! - 消息已读状态同步
//! - 实时状态变更广播
//! - 状态持久化管理

use app_common::error::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use uuid::Uuid;

use super::connection_manager::ConnectionManager;
use super::message_distributor::{BroadcastStrategy, MessageDistributor, MessagePriority};

/// 用户状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Default)]
pub enum UserStatus {
    /// 在线
    Online,
    /// 离线
    #[default]
    Offline,
    /// 忙碌
    Busy,
    /// 离开
    Away,
    /// 隐身
    Invisible,
}

/// 消息状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Default)]
pub enum MessageStatus {
    /// 未读
    #[default]
    Unread,
    /// 已读
    Read,
    /// 已送达
    Delivered,
    /// 发送失败
    Failed,
}

/// 用户状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserStatusInfo {
    /// 用户ID
    pub user_id: Uuid,
    /// 用户名
    pub username: String,
    /// 用户状态
    pub status: UserStatus,
    /// 状态消息
    pub status_message: Option<String>,
    /// 最后活跃时间
    pub last_active_at: DateTime<Utc>,
    /// 状态更新时间
    pub updated_at: DateTime<Utc>,
}

/// 消息状态信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageStatusInfo {
    /// 消息ID
    pub message_id: Uuid,
    /// 用户ID
    pub user_id: Uuid,
    /// 消息状态
    pub status: MessageStatus,
    /// 状态更新时间
    pub updated_at: DateTime<Utc>,
}

/// 状态变更事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatusChangeEvent {
    /// 事件ID
    pub event_id: Uuid,
    /// 事件类型
    pub event_type: StatusEventType,
    /// 用户ID
    pub user_id: Uuid,
    /// 事件数据
    pub data: serde_json::Value,
    /// 事件时间
    pub timestamp: DateTime<Utc>,
}

/// 状态事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StatusEventType {
    /// 用户状态变更
    UserStatusChanged,
    /// 消息状态变更
    MessageStatusChanged,
    /// 用户上线
    UserOnline,
    /// 用户下线
    UserOffline,
    /// 消息已读
    MessageRead,
    /// 消息已送达
    MessageDelivered,
}

/// 状态同步服务
pub struct StatusSyncService {
    /// 连接管理器
    #[allow(dead_code)]
    connection_manager: Arc<ConnectionManager>,
    /// 消息分发器
    message_distributor: Arc<MessageDistributor>,
    /// 用户状态映射
    user_statuses: Arc<RwLock<HashMap<Uuid, UserStatusInfo>>>,
    /// 消息状态映射
    message_statuses: Arc<RwLock<HashMap<Uuid, HashMap<Uuid, MessageStatusInfo>>>>, // message_id -> user_id -> status
    /// 状态变更事件历史
    status_events: Arc<RwLock<Vec<StatusChangeEvent>>>,
}

impl StatusSyncService {
    /// 创建新的状态同步服务
    pub fn new(
        connection_manager: Arc<ConnectionManager>,
        message_distributor: Arc<MessageDistributor>,
    ) -> Self {
        Self {
            connection_manager,
            message_distributor,
            user_statuses: Arc::new(RwLock::new(HashMap::new())),
            message_statuses: Arc::new(RwLock::new(HashMap::new())),
            status_events: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// 更新用户状态
    pub async fn update_user_status(
        &self,
        user_id: Uuid,
        username: String,
        status: UserStatus,
        status_message: Option<String>,
    ) -> Result<()> {
        let now = Utc::now();

        // 更新用户状态
        {
            let mut user_statuses = self.user_statuses.write().await;
            let user_status_info = UserStatusInfo {
                user_id,
                username: username.clone(),
                status: status.clone(),
                status_message: status_message.clone(),
                last_active_at: now,
                updated_at: now,
            };
            user_statuses.insert(user_id, user_status_info);
        }

        // 创建状态变更事件
        let event = StatusChangeEvent {
            event_id: Uuid::new_v4(),
            event_type: StatusEventType::UserStatusChanged,
            user_id,
            data: serde_json::json!({
                "username": username,
                "status": status,
                "status_message": status_message,
                "timestamp": now
            }),
            timestamp: now,
        };

        // 记录事件
        self.record_status_event(event.clone()).await;

        // 广播状态变更
        self.broadcast_status_change(event).await?;

        tracing::info!(
            "用户状态已更新: user_id={}, username={}, status={:?}",
            user_id,
            username,
            status
        );

        Ok(())
    }

    /// 用户上线
    pub async fn user_online(&self, user_id: Uuid, username: String) -> Result<()> {
        self.update_user_status(user_id, username.clone(), UserStatus::Online, None)
            .await?;

        // 创建用户上线事件
        let event = StatusChangeEvent {
            event_id: Uuid::new_v4(),
            event_type: StatusEventType::UserOnline,
            user_id,
            data: serde_json::json!({
                "username": username,
                "timestamp": Utc::now()
            }),
            timestamp: Utc::now(),
        };

        self.record_status_event(event.clone()).await;
        self.broadcast_status_change(event).await?;

        Ok(())
    }

    /// 用户下线
    pub async fn user_offline(&self, user_id: Uuid) -> Result<()> {
        let username = {
            let user_statuses = self.user_statuses.read().await;
            user_statuses
                .get(&user_id)
                .map(|info| info.username.clone())
                .unwrap_or_else(|| "Unknown".to_string())
        };

        self.update_user_status(user_id, username.clone(), UserStatus::Offline, None)
            .await?;

        // 创建用户下线事件
        let event = StatusChangeEvent {
            event_id: Uuid::new_v4(),
            event_type: StatusEventType::UserOffline,
            user_id,
            data: serde_json::json!({
                "username": username,
                "timestamp": Utc::now()
            }),
            timestamp: Utc::now(),
        };

        self.record_status_event(event.clone()).await;
        self.broadcast_status_change(event).await?;

        Ok(())
    }

    /// 更新消息状态
    pub async fn update_message_status(
        &self,
        message_id: Uuid,
        user_id: Uuid,
        status: MessageStatus,
    ) -> Result<()> {
        let now = Utc::now();

        // 更新消息状态
        {
            let mut message_statuses = self.message_statuses.write().await;
            let user_statuses = message_statuses
                .entry(message_id)
                .or_insert_with(HashMap::new);

            let message_status_info = MessageStatusInfo {
                message_id,
                user_id,
                status: status.clone(),
                updated_at: now,
            };

            user_statuses.insert(user_id, message_status_info);
        }

        // 创建消息状态变更事件
        let event_type = match status {
            MessageStatus::Read => StatusEventType::MessageRead,
            MessageStatus::Delivered => StatusEventType::MessageDelivered,
            _ => StatusEventType::MessageStatusChanged,
        };

        let event = StatusChangeEvent {
            event_id: Uuid::new_v4(),
            event_type,
            user_id,
            data: serde_json::json!({
                "message_id": message_id,
                "status": status,
                "timestamp": now
            }),
            timestamp: now,
        };

        // 记录事件
        self.record_status_event(event.clone()).await;

        // 广播状态变更（只发送给相关用户）
        self.broadcast_message_status_change(event, message_id)
            .await?;

        tracing::debug!(
            "消息状态已更新: message_id={}, user_id={}, status={:?}",
            message_id,
            user_id,
            status
        );

        Ok(())
    }

    /// 标记消息为已读
    pub async fn mark_message_as_read(&self, message_id: Uuid, user_id: Uuid) -> Result<()> {
        self.update_message_status(message_id, user_id, MessageStatus::Read)
            .await
    }

    /// 标记消息为已送达
    pub async fn mark_message_as_delivered(&self, message_id: Uuid, user_id: Uuid) -> Result<()> {
        self.update_message_status(message_id, user_id, MessageStatus::Delivered)
            .await
    }

    /// 获取用户状态
    pub async fn get_user_status(&self, user_id: Uuid) -> Option<UserStatusInfo> {
        let user_statuses = self.user_statuses.read().await;
        user_statuses.get(&user_id).cloned()
    }

    /// 获取所有在线用户状态
    pub async fn get_online_users(&self) -> Vec<UserStatusInfo> {
        let user_statuses = self.user_statuses.read().await;
        user_statuses
            .values()
            .filter(|info| info.status == UserStatus::Online)
            .cloned()
            .collect()
    }

    /// 获取消息状态
    pub async fn get_message_status(&self, message_id: Uuid) -> HashMap<Uuid, MessageStatusInfo> {
        let message_statuses = self.message_statuses.read().await;
        message_statuses
            .get(&message_id)
            .cloned()
            .unwrap_or_default()
    }

    /// 获取用户的未读消息数量
    pub async fn get_unread_count(&self, user_id: Uuid) -> u64 {
        let message_statuses = self.message_statuses.read().await;
        let mut unread_count = 0u64;

        for user_statuses in message_statuses.values() {
            if let Some(status_info) = user_statuses.get(&user_id) {
                if status_info.status == MessageStatus::Unread {
                    unread_count += 1;
                }
            }
        }

        unread_count
    }

    /// 记录状态事件
    async fn record_status_event(&self, event: StatusChangeEvent) {
        let mut status_events = self.status_events.write().await;
        status_events.push(event);

        // 保持最近1000条事件记录
        if status_events.len() > 1000 {
            let len = status_events.len();
            status_events.drain(0..len - 1000);
        }
    }

    /// 广播状态变更
    async fn broadcast_status_change(&self, event: StatusChangeEvent) -> Result<()> {
        let message_content = serde_json::json!({
            "type": "status_change",
            "event": event
        });

        let message = axum::extract::ws::Message::Text(message_content.to_string().into());

        self.message_distributor
            .distribute_message(
                message,
                BroadcastStrategy::BroadcastAll {
                    exclude_sender: false,
                },
                MessagePriority::Normal,
                Some(event.user_id),
            )
            .await?;

        Ok(())
    }

    /// 广播消息状态变更
    async fn broadcast_message_status_change(
        &self,
        event: StatusChangeEvent,
        _message_id: Uuid,
    ) -> Result<()> {
        let message_content = serde_json::json!({
            "type": "message_status_change",
            "event": event
        });

        let message = axum::extract::ws::Message::Text(message_content.to_string().into());

        // 只发送给消息的发送者和接收者
        // 这里简化处理，实际应该根据消息的发送者和接收者来确定
        self.message_distributor
            .distribute_message(
                message,
                BroadcastStrategy::BroadcastAll {
                    exclude_sender: false,
                },
                MessagePriority::Low,
                Some(event.user_id),
            )
            .await?;

        Ok(())
    }

    /// 清理离线用户状态
    pub async fn cleanup_offline_users(&self, offline_threshold_minutes: i64) -> Result<u64> {
        let threshold = Utc::now() - chrono::Duration::minutes(offline_threshold_minutes);
        let mut cleaned_count = 0u64;

        {
            let mut user_statuses = self.user_statuses.write().await;
            let offline_users: Vec<Uuid> = user_statuses
                .iter()
                .filter(|(_, info)| {
                    info.last_active_at < threshold && info.status != UserStatus::Offline
                })
                .map(|(&user_id, _)| user_id)
                .collect();

            for user_id in offline_users {
                if let Some(user_info) = user_statuses.get_mut(&user_id) {
                    user_info.status = UserStatus::Offline;
                    user_info.updated_at = Utc::now();
                    cleaned_count += 1;
                }
            }
        }

        if cleaned_count > 0 {
            tracing::info!("清理了 {} 个离线用户状态", cleaned_count);
        }

        Ok(cleaned_count)
    }
}

/// 状态同步应用服务接口
#[async_trait]
pub trait StatusSyncApplicationService: Send + Sync {
    /// 更新用户状态
    async fn update_user_status(
        &self,
        user_id: Uuid,
        username: String,
        status: UserStatus,
        status_message: Option<String>,
    ) -> Result<()>;

    /// 用户上线
    async fn user_online(&self, user_id: Uuid, username: String) -> Result<()>;

    /// 用户下线
    async fn user_offline(&self, user_id: Uuid) -> Result<()>;

    /// 标记消息为已读
    async fn mark_message_as_read(&self, message_id: Uuid, user_id: Uuid) -> Result<()>;

    /// 获取用户状态
    async fn get_user_status(&self, user_id: Uuid) -> Option<UserStatusInfo>;

    /// 获取所有在线用户状态
    async fn get_online_users(&self) -> Vec<UserStatusInfo>;

    /// 获取用户的未读消息数量
    async fn get_unread_count(&self, user_id: Uuid) -> u64;
}

/// 状态同步应用服务实现
pub struct StatusSyncApplicationServiceImpl {
    status_sync_service: Arc<StatusSyncService>,
}

impl StatusSyncApplicationServiceImpl {
    /// 创建新的状态同步应用服务实例
    pub fn new(status_sync_service: Arc<StatusSyncService>) -> Self {
        Self {
            status_sync_service,
        }
    }
}

#[async_trait]
impl StatusSyncApplicationService for StatusSyncApplicationServiceImpl {
    async fn update_user_status(
        &self,
        user_id: Uuid,
        username: String,
        status: UserStatus,
        status_message: Option<String>,
    ) -> Result<()> {
        self.status_sync_service
            .update_user_status(user_id, username, status, status_message)
            .await
    }

    async fn user_online(&self, user_id: Uuid, username: String) -> Result<()> {
        self.status_sync_service
            .user_online(user_id, username)
            .await
    }

    async fn user_offline(&self, user_id: Uuid) -> Result<()> {
        self.status_sync_service.user_offline(user_id).await
    }

    async fn mark_message_as_read(&self, message_id: Uuid, user_id: Uuid) -> Result<()> {
        self.status_sync_service
            .mark_message_as_read(message_id, user_id)
            .await
    }

    async fn get_user_status(&self, user_id: Uuid) -> Option<UserStatusInfo> {
        self.status_sync_service.get_user_status(user_id).await
    }

    async fn get_online_users(&self) -> Vec<UserStatusInfo> {
        self.status_sync_service.get_online_users().await
    }

    async fn get_unread_count(&self, user_id: Uuid) -> u64 {
        self.status_sync_service.get_unread_count(user_id).await
    }
}
