//! # 任务应用服务
//!
//! 任务相关的业务用例实现，包括：
//! - 任务创建和管理
//! - 任务状态更新
//! - 任务查询和列表
//! - 任务业务流程编排

use app_common::error::{AppError, Result};
use app_domain::entities::task::Task;
use app_domain::repositories::task_repository::TaskRepositoryContract;
use app_domain::services::task_service::TaskDomainService;
use app_interfaces::{CreateTaskRequest, TaskResponse, UpdateTaskRequest};
use async_trait::async_trait;
use sea_orm::prelude::Uuid;
// 移除未使用的serde导入
use std::sync::Arc;
use validator::Validate;

/// 任务应用服务接口
///
/// 定义任务相关的业务用例操作
#[async_trait]
pub trait TaskApplicationService: Send + Sync {
    /// 创建任务
    async fn create_task(&self, request: CreateTaskRequest, user_id: Uuid) -> Result<TaskResponse>;

    /// 更新任务
    async fn update_task(
        &self,
        task_id: Uuid,
        request: UpdateTaskRequest,
        user_id: Uuid,
    ) -> Result<TaskResponse>;

    /// 删除任务
    async fn delete_task(&self, task_id: Uuid, user_id: Uuid) -> Result<()>;

    /// 根据ID查找任务
    async fn find_task_by_id(&self, task_id: Uuid, user_id: Uuid) -> Result<Option<TaskResponse>>;

    /// 获取用户的所有任务
    async fn get_user_tasks(&self, user_id: Uuid) -> Result<Vec<TaskResponse>>;

    /// 标记任务为完成
    async fn mark_task_completed(&self, task_id: Uuid, user_id: Uuid) -> Result<TaskResponse>;

    /// 标记任务为未完成
    async fn mark_task_incomplete(&self, task_id: Uuid, user_id: Uuid) -> Result<TaskResponse>;
}

/// 任务应用服务实现
pub struct TaskApplicationServiceImpl {
    /// 任务仓库
    task_repository: Arc<dyn TaskRepositoryContract>,
    /// 任务领域服务
    task_domain_service: Arc<dyn TaskDomainService>,
}

impl TaskApplicationServiceImpl {
    /// 创建新的任务应用服务实例
    pub fn new(
        task_repository: Arc<dyn TaskRepositoryContract>,
        task_domain_service: Arc<dyn TaskDomainService>,
    ) -> Self {
        Self {
            task_repository,
            task_domain_service,
        }
    }

    /// 验证任务权限
    async fn verify_task_ownership(&self, task_id: Uuid, user_id: Uuid) -> Result<Task> {
        let task = self
            .task_repository
            .find_by_id(task_id)
            .await?
            .ok_or_else(|| AppError::NotFound("任务不存在".to_string()))?;

        if task.user_id != Some(user_id) {
            return Err(AppError::Forbidden("无权限访问此任务".to_string()));
        }

        Ok(task)
    }

    /// 将任务实体转换为响应DTO
    fn task_to_response(&self, task: Task) -> TaskResponse {
        use app_interfaces::TaskStatus;
        TaskResponse {
            id: task.id,
            title: task.title,
            description: task.description,
            status: if task.completed {
                TaskStatus::Completed
            } else {
                TaskStatus::Pending
            },
            priority: app_interfaces::TaskPriority::Medium, // 默认优先级
            user_id: task.user_id.unwrap_or_default(),
            created_at: task.created_at,
            updated_at: task.updated_at,
            due_date: None,
            completed_at: if task.completed {
                Some(task.updated_at)
            } else {
                None
            },
            completed: task.completed, // 添加兼容前端的completed字段
        }
    }
}

#[async_trait]
impl TaskApplicationService for TaskApplicationServiceImpl {
    /// 创建任务业务用例
    async fn create_task(&self, request: CreateTaskRequest, user_id: Uuid) -> Result<TaskResponse> {
        tracing::info!(user_id = %user_id, title = %request.title, "开始处理创建任务请求");

        // 1. 验证输入
        request
            .validate()
            .map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 2. 创建任务实体
        let now = chrono::Utc::now();
        let task = Task {
            id: Uuid::new_v4(),
            title: request.title.clone(),
            description: request.description,
            completed: false, // 新创建的任务默认未完成
            user_id: Some(user_id),
            created_at: now,
            updated_at: now,
        };

        // 3. 验证任务业务规则
        self.task_domain_service.validate_task(&task).await?;

        // 4. 保存任务到数据库
        let created_task = self.task_repository.create(task).await?;

        tracing::info!(user_id = %user_id, task_id = %created_task.id, "任务创建成功");

        Ok(self.task_to_response(created_task))
    }

    /// 更新任务业务用例
    async fn update_task(
        &self,
        task_id: Uuid,
        request: UpdateTaskRequest,
        user_id: Uuid,
    ) -> Result<TaskResponse> {
        tracing::info!(user_id = %user_id, task_id = %task_id, "开始处理更新任务请求");

        // 1. 验证输入
        request
            .validate()
            .map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 2. 验证任务权限
        let mut task = self.verify_task_ownership(task_id, user_id).await?;

        // 3. 更新任务字段
        if let Some(title) = request.title {
            task.title = title;
        }
        if let Some(description) = request.description {
            task.description = Some(description);
        }
        if let Some(status) = request.status {
            use app_interfaces::TaskStatus;
            task.completed = matches!(status, TaskStatus::Completed);
        }
        task.updated_at = chrono::Utc::now();

        // 4. 验证更新后的任务业务规则
        self.task_domain_service.validate_task(&task).await?;

        // 5. 保存更新到数据库
        let updated_task = self.task_repository.update(task).await?;

        tracing::info!(user_id = %user_id, task_id = %task_id, "任务更新成功");

        Ok(self.task_to_response(updated_task))
    }

    /// 删除任务业务用例
    async fn delete_task(&self, task_id: Uuid, user_id: Uuid) -> Result<()> {
        tracing::info!(user_id = %user_id, task_id = %task_id, "开始处理删除任务请求");

        // 1. 验证任务权限
        self.verify_task_ownership(task_id, user_id).await?;

        // 2. 删除任务
        self.task_repository.delete(task_id).await?;

        tracing::info!(user_id = %user_id, task_id = %task_id, "任务删除成功");

        Ok(())
    }

    /// 根据ID查找任务业务用例
    async fn find_task_by_id(&self, task_id: Uuid, user_id: Uuid) -> Result<Option<TaskResponse>> {
        let task = self.verify_task_ownership(task_id, user_id).await;
        match task {
            Ok(task) => Ok(Some(self.task_to_response(task))),
            Err(AppError::NotFound(_)) => Ok(None),
            Err(e) => Err(e),
        }
    }

    /// 获取用户的所有任务业务用例
    async fn get_user_tasks(&self, user_id: Uuid) -> Result<Vec<TaskResponse>> {
        let tasks = self.task_repository.find_by_user_id(user_id).await?;
        Ok(tasks
            .into_iter()
            .map(|task| self.task_to_response(task))
            .collect())
    }

    /// 标记任务为完成业务用例
    async fn mark_task_completed(&self, task_id: Uuid, user_id: Uuid) -> Result<TaskResponse> {
        tracing::info!(user_id = %user_id, task_id = %task_id, "开始处理标记任务完成请求");

        // 1. 验证任务权限
        let mut task = self.verify_task_ownership(task_id, user_id).await?;

        // 2. 更新完成状态
        task.completed = true;
        task.updated_at = chrono::Utc::now();

        // 3. 验证业务规则
        self.task_domain_service.validate_task(&task).await?;

        // 4. 保存更新
        let updated_task = self.task_repository.update(task).await?;

        tracing::info!(user_id = %user_id, task_id = %task_id, "任务标记为完成");

        Ok(self.task_to_response(updated_task))
    }

    /// 标记任务为未完成业务用例
    async fn mark_task_incomplete(&self, task_id: Uuid, user_id: Uuid) -> Result<TaskResponse> {
        tracing::info!(user_id = %user_id, task_id = %task_id, "开始处理标记任务未完成请求");

        // 1. 验证任务权限
        let mut task = self.verify_task_ownership(task_id, user_id).await?;

        // 2. 更新完成状态
        task.completed = false;
        task.updated_at = chrono::Utc::now();

        // 3. 验证业务规则
        self.task_domain_service.validate_task(&task).await?;

        // 4. 保存更新
        let updated_task = self.task_repository.update(task).await?;

        tracing::info!(user_id = %user_id, task_id = %task_id, "任务标记为未完成");

        Ok(self.task_to_response(updated_task))
    }
}
