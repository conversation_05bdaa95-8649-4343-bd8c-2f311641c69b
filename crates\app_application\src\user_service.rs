//! # 用户应用服务
//!
//! 用户相关的业务用例实现，包括：
//! - 用户注册和登录
//! - 用户信息管理
//! - 认证和授权
//! - 用户业务流程编排

use app_common::error::{AppError, Result};
use app_domain::entities::user::User;
use app_domain::repositories::user_repository::UserRepositoryContract;
use app_domain::services::user_service::UserDomainService;
use app_interfaces::{AuthResponse, Claims, LoginRequest, RegisterRequest, UserInfo, UserResponse};
use argon2::{
    Argon2,
    password_hash::{PasswordHash, PasswordHasher, PasswordVerifier, SaltString},
};
use async_trait::async_trait;
use chrono::{Duration, Utc};
use jsonwebtoken::{EncodingKey, Header, encode};
use rand_core::OsRng;
use sea_orm::prelude::Uuid;
// 移除未使用的serde导入
use std::sync::Arc;
use validator::Validate;

// 用户名验证正则表达式
lazy_static::lazy_static! {
    static ref USERNAME_REGEX: regex::Regex = regex::Regex::new(r"^[a-zA-Z0-9_]+$").unwrap();
}

/// 用户应用服务接口
///
/// 定义用户相关的业务用例操作
#[async_trait]
pub trait UserApplicationService: Send + Sync {
    /// 用户注册
    async fn register_user(
        &self,
        request: RegisterRequest,
        jwt_secret: &str,
    ) -> Result<AuthResponse>;

    /// 用户登录
    async fn login_user(&self, request: LoginRequest, jwt_secret: &str) -> Result<AuthResponse>;

    /// 根据用户名查找用户
    async fn find_user_by_username(&self, username: &str) -> Result<Option<UserResponse>>;

    /// 根据用户ID查找用户
    async fn find_user_by_id(&self, user_id: Uuid) -> Result<Option<UserResponse>>;

    /// 验证用户名是否可用
    async fn is_username_available(&self, username: &str) -> Result<bool>;

    /// 获取用户总数（用于健康检查）
    async fn fetch_user_count(&self) -> Result<u64>;
}

/// 用户应用服务实现
pub struct UserApplicationServiceImpl {
    /// 用户仓库
    user_repository: Arc<dyn UserRepositoryContract>,
    /// 用户领域服务
    user_domain_service: Arc<dyn UserDomainService>,
}

impl UserApplicationServiceImpl {
    /// 创建新的用户应用服务实例
    pub fn new(
        user_repository: Arc<dyn UserRepositoryContract>,
        user_domain_service: Arc<dyn UserDomainService>,
    ) -> Self {
        Self {
            user_repository,
            user_domain_service,
        }
    }

    /// 验证注册请求
    async fn validate_register_request(&self, request: &RegisterRequest) -> Result<()> {
        // 1. 验证输入格式
        request
            .validate()
            .map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 2. 验证密码确认
        if request.password != request.confirm_password {
            return Err(AppError::ValidationError(
                "密码和确认密码不匹配".to_string(),
            ));
        }

        // 3. 验证用户名是否可用
        if !self
            .user_domain_service
            .is_username_available(&request.username)
            .await?
        {
            return Err(AppError::UserAlreadyExists(request.username.clone()));
        }

        Ok(())
    }

    /// 哈希密码
    fn hash_password(&self, password: &str) -> Result<String> {
        let argon2 = Argon2::default();
        let salt = SaltString::generate(&mut OsRng);
        let password_hash = argon2
            .hash_password(password.as_bytes(), &salt)
            .map_err(|e| AppError::PasswordHashError(e.to_string()))?
            .to_string();
        Ok(password_hash)
    }

    /// 验证密码
    fn verify_password(&self, password: &str, hash: &str) -> Result<bool> {
        let argon2 = Argon2::default();
        let parsed_hash =
            PasswordHash::new(hash).map_err(|e| AppError::PasswordHashError(e.to_string()))?;

        Ok(argon2
            .verify_password(password.as_bytes(), &parsed_hash)
            .is_ok())
    }

    /// 生成JWT令牌
    fn generate_jwt_token(&self, user: &User, jwt_secret: &str) -> Result<(String, i64)> {
        let now = Utc::now();
        let expires_in = 24 * 60 * 60; // 24小时，单位：秒
        let exp = now + Duration::seconds(expires_in);

        let claims = Claims {
            sub: user.id.to_string(),
            username: user.username.clone(),
            exp: exp.timestamp(),
            iat: now.timestamp(),
        };

        let token = encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(jwt_secret.as_ref()),
        )
        .map_err(|e| AppError::TokenGenerationError(e.to_string()))?;

        Ok((token, expires_in))
    }
}

#[async_trait]
impl UserApplicationService for UserApplicationServiceImpl {
    /// 用户注册业务用例
    async fn register_user(
        &self,
        request: RegisterRequest,
        jwt_secret: &str,
    ) -> Result<AuthResponse> {
        tracing::info!(username = %request.username, "开始处理用户注册请求");

        // 1. 验证注册请求
        self.validate_register_request(&request).await?;

        // 2. 哈希密码
        let password_hash = self.hash_password(&request.password)?;

        // 3. 创建用户实体（支持可选邮箱）
        let user = User::new(
            request.username.clone(),
            request.email.clone(), // 使用请求中的可选邮箱
            password_hash,
        )
        .map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 4. 验证用户实体业务规则
        self.user_domain_service.validate_user(&user).await?;

        // 5. 保存用户到数据库
        let created_user = self.user_repository.create(user).await?;

        tracing::info!(username = %request.username, user_id = %created_user.id, "用户注册成功");

        // 6. 生成JWT令牌（注册后自动登录）
        let (token, expires_in) = self.generate_jwt_token(&created_user, jwt_secret)?;

        // 7. 转换为认证响应DTO
        Ok(AuthResponse {
            access_token: token,
            token_type: "Bearer".to_string(),
            expires_in,
            user: UserInfo {
                id: created_user.id,
                username: created_user.username,
                email: created_user.email.unwrap_or_default(),
                created_at: created_user.created_at,
            },
        })
    }

    /// 用户登录业务用例
    async fn login_user(&self, request: LoginRequest, jwt_secret: &str) -> Result<AuthResponse> {
        tracing::info!(username = %request.username, "开始处理用户登录请求");

        // 1. 验证输入
        request
            .validate()
            .map_err(|e| AppError::ValidationError(e.to_string()))?;

        // 2. 根据用户名查找用户
        let user = self
            .user_repository
            .find_by_username(&request.username)
            .await?
            .ok_or_else(|| AppError::InvalidCredentials)?;

        // 3. 验证密码
        if !self.verify_password(&request.password, &user.password_hash)? {
            tracing::warn!(username = %request.username, "用户登录失败，密码验证失败");
            return Err(AppError::InvalidCredentials);
        }

        // 4. 转换为领域实体
        let user_entity = user;

        // 5. 生成JWT令牌
        let (token, expires_in) = self.generate_jwt_token(&user_entity, jwt_secret)?;

        tracing::info!(username = %request.username, user_id = %user_entity.id, "用户登录成功");

        Ok(AuthResponse {
            access_token: token,
            token_type: "Bearer".to_string(),
            expires_in,
            user: UserInfo {
                id: user_entity.id,
                username: user_entity.username,
                email: user_entity.email.unwrap_or_default(),
                created_at: user_entity.created_at,
            },
        })
    }

    /// 根据用户名查找用户
    async fn find_user_by_username(&self, username: &str) -> Result<Option<UserResponse>> {
        let user = self.user_repository.find_by_username(username).await?;
        Ok(user.map(|u| UserResponse {
            id: u.id,
            username: u.username,
            email: u.email.unwrap_or_default(),
            display_name: u.display_name,
            avatar_url: u.avatar_url,
            bio: u.bio,
            location: u.location,
            website: u.website,
            last_login_at: u.last_login_at,
            status: u.status,
            created_at: u.created_at,
            updated_at: u.updated_at,
        }))
    }

    /// 根据用户ID查找用户
    async fn find_user_by_id(&self, user_id: Uuid) -> Result<Option<UserResponse>> {
        let user = self.user_repository.find_by_id(user_id).await?;
        Ok(user.map(|u| UserResponse {
            id: u.id,
            username: u.username,
            email: u.email.unwrap_or_default(),
            display_name: u.display_name,
            avatar_url: u.avatar_url,
            bio: u.bio,
            location: u.location,
            website: u.website,
            last_login_at: u.last_login_at,
            status: u.status,
            created_at: u.created_at,
            updated_at: u.updated_at,
        }))
    }

    /// 验证用户名是否可用
    async fn is_username_available(&self, username: &str) -> Result<bool> {
        self.user_domain_service
            .is_username_available(username)
            .await
    }

    /// 获取用户总数（用于健康检查）
    async fn fetch_user_count(&self) -> Result<u64> {
        self.user_repository
            .count_all()
            .await
            .map_err(|e| AppError::DatabaseError(e.to_string()))
    }
}
