//! # WebSocket 应用服务
//!
//! 实现WebSocket相关的业务用例和应用逻辑

use app_domain::websocket::*;
use app_interfaces::{
    BroadcastRequest, WebSocketConnectionRequest, WebSocketMessageRequest, WebSocketStatsResponse,
};
use async_trait::async_trait;
// 移除未使用的serde导入
use std::sync::Arc;
use tracing::{error, info, warn};
use uuid::Uuid;

/// WebSocket应用服务接口
///
/// 【功能】: 定义WebSocket相关的应用层业务用例
#[async_trait]
pub trait WebSocketApplicationService: Send + Sync {
    /// 建立WebSocket连接
    ///
    /// 【参数】:
    /// * `request` - 连接请求
    ///
    /// 【返回值】: Result<ConnectionId, String> - 连接ID或错误信息
    async fn establish_connection(
        &self,
        request: WebSocketConnectionRequest,
    ) -> Result<ConnectionId, String>;

    /// 断开WebSocket连接
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn disconnect(&self, connection_id: &ConnectionId) -> Result<(), String>;

    /// 发送消息
    ///
    /// 【参数】:
    /// * `request` - 消息请求
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn send_message(&self, request: WebSocketMessageRequest) -> Result<(), String>;

    /// 广播消息
    ///
    /// 【参数】:
    /// * `request` - 广播请求
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_message(&self, request: BroadcastRequest) -> Result<usize, String>;

    /// 获取用户连接
    ///
    /// 【参数】:
    /// * `user_id` - 用户ID
    ///
    /// 【返回值】: Vec<WsSession> - 用户的所有连接会话
    async fn get_user_connections(&self, user_id: &Uuid) -> Vec<WsSession>;

    /// 获取统计信息
    ///
    /// 【返回值】: WebSocketStatsResponse - 统计信息
    async fn get_stats(&self) -> WebSocketStatsResponse;

    /// 获取活跃连接列表
    ///
    /// 【返回值】: Result<Vec<WsSession>, String> - 活跃连接列表或错误信息
    async fn get_active_connections(&self) -> Result<Vec<WsSession>, String>;

    /// 更新连接活跃状态
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn update_activity(&self, connection_id: &ConnectionId) -> Result<(), String>;

    /// 清理空闲连接
    ///
    /// 【参数】:
    /// * `idle_timeout_seconds` - 空闲超时时间（秒）
    ///
    /// 【返回值】: usize - 清理的连接数量
    async fn cleanup_idle_connections(&self, idle_timeout_seconds: i64) -> usize;

    /// 注册连接的消息发送通道
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    /// * `sender` - 消息发送通道
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn register_connection_sender(
        &self,
        connection_id: &ConnectionId,
        sender_id: String,
    ) -> Result<(), String>;

    /// 更新连接的消息发送通道
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    /// * `sender` - 新的消息发送通道
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn update_connection_sender(
        &self,
        connection_id: &ConnectionId,
        sender: tokio::sync::mpsc::UnboundedSender<axum::extract::ws::Message>,
    ) -> Result<(), String>;

    /// 获取在线用户列表
    ///
    /// 【返回值】: Result<Vec<WsSession>, app_common::error::AppError> - 在线用户连接会话列表
    async fn get_online_users(&self) -> Result<Vec<WsSession>, app_common::error::AppError>;

    /// 获取连接服务实例
    ///
    /// 【返回值】: Arc<dyn WebSocketConnectionService> - 连接服务实例
    fn get_connection_service(&self) -> Arc<dyn WebSocketConnectionService>;

    /// 广播任务创建消息
    ///
    /// 【参数】:
    /// * `task_data` - 任务数据（JSON格式）
    /// * `sender_id` - 发送者用户ID
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_task_created(
        &self,
        task_data: serde_json::Value,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String>;

    /// 广播任务更新消息
    ///
    /// 【参数】:
    /// * `task_data` - 任务数据（JSON格式）
    /// * `sender_id` - 发送者用户ID
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_task_updated(
        &self,
        task_data: serde_json::Value,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String>;

    /// 广播任务删除消息
    ///
    /// 【参数】:
    /// * `task_id` - 任务ID
    /// * `sender_id` - 发送者用户ID
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_task_deleted(
        &self,
        task_id: u64,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String>;

    /// 发送消息给指定用户
    ///
    /// 【参数】:
    /// * `request` - 发送消息请求
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn send_message_to_user(
        &self,
        request: app_interfaces::SendMessageRequest,
    ) -> Result<(), String>;
}

/// WebSocket应用服务实现
///
/// 【功能】: 实现WebSocket应用服务接口，协调领域服务完成业务用例
pub struct WebSocketApplicationServiceImpl {
    /// WebSocket连接服务
    connection_service: Arc<dyn WebSocketConnectionService>,
    /// WebSocket消息服务
    message_service: Arc<dyn WebSocketMessageService>,
    /// WebSocket统计服务
    stats_service: Arc<dyn WebSocketStatsService>,
    /// WebSocket实时消息服务
    realtime_service: Option<Arc<dyn WebSocketRealtimeService>>,
}

impl WebSocketApplicationServiceImpl {
    /// 创建新的WebSocket应用服务实例
    ///
    /// 【参数】:
    /// * `connection_service` - 连接管理服务
    /// * `message_service` - 消息分发服务
    /// * `stats_service` - 统计服务
    pub fn new(
        connection_service: Arc<dyn WebSocketConnectionService>,
        message_service: Arc<dyn WebSocketMessageService>,
        stats_service: Arc<dyn WebSocketStatsService>,
    ) -> Self {
        Self {
            connection_service,
            message_service,
            stats_service,
            realtime_service: None,
        }
    }

    /// 设置实时消息服务
    ///
    /// 【参数】:
    /// * `realtime_service` - 实时消息服务
    pub fn with_realtime_service(
        mut self,
        realtime_service: Arc<dyn WebSocketRealtimeService>,
    ) -> Self {
        self.realtime_service = Some(realtime_service);
        self
    }

    /// 计算消息吞吐量（消息/秒）
    ///
    /// 【功能】: 基于历史消息数据计算当前的消息吞吐量
    async fn calculate_message_throughput(&self, message_stats: &MessageStats) -> f64 {
        // 简化实现：基于总消息数和假设的时间窗口
        // 实际实现中应该使用时间窗口内的消息数量
        let total_messages = message_stats.total_sent + message_stats.total_received;
        if total_messages > 0 {
            // 假设系统运行了1小时，计算每秒消息数
            (total_messages as f64) / 3600.0
        } else {
            0.0
        }
    }

    /// 计算平均延迟（毫秒）
    ///
    /// 【功能】: 计算WebSocket消息的平均延迟
    async fn calculate_avg_latency(&self) -> f64 {
        // TODO: 实现实际的延迟计算逻辑
        // 这里返回模拟值，实际应该从性能监控数据中获取
        25.0
    }

    /// 计算P95延迟（毫秒）
    ///
    /// 【功能】: 计算95%的消息延迟在此值以下
    async fn calculate_p95_latency(&self) -> f64 {
        // TODO: 实现实际的P95延迟计算
        45.0
    }

    /// 计算P99延迟（毫秒）
    ///
    /// 【功能】: 计算99%的消息延迟在此值以下
    async fn calculate_p99_latency(&self) -> f64 {
        // TODO: 实现实际的P99延迟计算
        80.0
    }

    /// 获取CPU使用率
    ///
    /// 【功能】: 获取当前进程的CPU使用率
    async fn get_cpu_usage(&self) -> f64 {
        // TODO: 实现实际的CPU使用率获取
        // 可以使用sysinfo crate
        15.0
    }

    /// 获取内存使用量（MB）
    ///
    /// 【功能】: 获取当前进程的内存使用量
    async fn get_memory_usage(&self) -> f64 {
        // TODO: 实现实际的内存使用量获取
        // 可以使用sysinfo crate
        128.0
    }

    /// 计算网络带宽使用（Mbps）
    ///
    /// 【功能】: 计算WebSocket连接的网络带宽使用
    async fn calculate_network_bandwidth(&self) -> f64 {
        // TODO: 实现实际的网络带宽计算
        5.2
    }

    /// 计算错误率
    ///
    /// 【功能】: 计算WebSocket连接和消息的错误率
    async fn calculate_error_rate(&self) -> f64 {
        // TODO: 实现实际的错误率计算
        // 基于连接失败数和消息失败数
        0.01
    }
}

#[async_trait]
impl WebSocketApplicationService for WebSocketApplicationServiceImpl {
    async fn establish_connection(
        &self,
        request: WebSocketConnectionRequest,
    ) -> Result<ConnectionId, String> {
        info!(
            "建立WebSocket连接: 用户ID={}, 用户名={}, 会话类型={:?}",
            request.user_id, request.username, request.session_type
        );

        // 转换会话类型
        let session_type = match request.session_type.as_str() {
            "chat" => SessionType::Chat,
            "notification" => SessionType::Notification,
            "monitoring" => SessionType::Monitoring,
            "admin" => SessionType::Admin,
            _ => SessionType::Chat, // 默认为聊天会话
        };

        // 创建新的WebSocket会话
        let mut session = WsSession::new(request.user_id, request.username.clone(), session_type);

        // 标记会话为已连接状态（关键修复！）
        session.mark_connected();

        // 添加元数据
        if let Some(metadata) = request.metadata {
            for (key, value) in metadata {
                session.add_metadata(key, value);
            }
        }

        let connection_id = session.id;

        // 添加连接到连接管理器
        self.connection_service.add_connection(session).await?;

        // 记录连接事件
        self.stats_service
            .record_connection_event(&ConnectionEventType::Connected)
            .await;

        info!("WebSocket连接建立成功: 连接ID={}", connection_id);
        Ok(connection_id)
    }

    async fn disconnect(&self, connection_id: &ConnectionId) -> Result<(), String> {
        info!("断开WebSocket连接: 连接ID={}", connection_id);

        // 移除连接
        self.connection_service
            .remove_connection(connection_id)
            .await?;

        // 记录断开连接事件
        self.stats_service
            .record_connection_event(&ConnectionEventType::Disconnected)
            .await;

        info!("WebSocket连接断开成功: 连接ID={}", connection_id);
        Ok(())
    }

    async fn send_message(&self, request: WebSocketMessageRequest) -> Result<(), String> {
        // 转换发送者ID
        let sender_id = request.sender_id.map(|id| {
            ConnectionId::from_uuid(Uuid::parse_str(&id).unwrap_or_else(|_| Uuid::new_v4()))
        });

        // 创建消息
        let mut message = WsMessage::new_text(request.content, sender_id);

        // 设置优先级
        if let Some(priority_str) = request.priority {
            let priority = match priority_str.as_str() {
                "low" => MessagePriority::Low,
                "normal" => MessagePriority::Normal,
                "high" => MessagePriority::High,
                "critical" => MessagePriority::Critical,
                _ => MessagePriority::Normal,
            };
            message = message.with_priority(priority);
        }

        // 发送消息
        if let Some(receiver_id_str) = request.receiver_id {
            // 转换接收者ID
            let receiver_id = ConnectionId::from_uuid(
                Uuid::parse_str(&receiver_id_str).unwrap_or_else(|_| Uuid::new_v4()),
            );

            // 发送给指定连接
            message = message.with_receiver(receiver_id);
            let message_size = message.content.len();

            self.message_service
                .send_to_connection(&receiver_id, message)
                .await?;

            // 记录消息发送
            self.stats_service
                .record_message_sent(&MessageType::Text, message_size)
                .await;
        } else {
            return Err("必须指定接收者连接ID".to_string());
        }

        Ok(())
    }

    async fn broadcast_message(&self, request: BroadcastRequest) -> Result<usize, String> {
        info!("广播WebSocket消息: 内容长度={}", request.content.len());

        // 转换发送者ID
        let sender_id = request.sender_id.map(|id| {
            ConnectionId::from_uuid(Uuid::parse_str(&id).unwrap_or_else(|_| Uuid::new_v4()))
        });

        // 创建消息
        let mut message = WsMessage::new_text(request.content, sender_id);

        // 设置优先级
        if let Some(priority_str) = request.priority {
            let priority = match priority_str.as_str() {
                "low" => MessagePriority::Low,
                "normal" => MessagePriority::Normal,
                "high" => MessagePriority::High,
                "critical" => MessagePriority::Critical,
                _ => MessagePriority::Normal,
            };
            message = message.with_priority(priority);
        }

        let message_size = message.content.len();

        let sent_count = if let Some(ref target_users) = request.target_users {
            // 广播给指定用户
            self.message_service
                .broadcast_to_users(target_users, message)
                .await?
        } else if let Some(session_type_str) = request.target_session_type {
            // 转换会话类型
            let session_type = match session_type_str.as_str() {
                "chat" => SessionType::Chat,
                "notification" => SessionType::Notification,
                "monitoring" => SessionType::Monitoring,
                "admin" => SessionType::Admin,
                _ => SessionType::Chat,
            };
            // 广播给指定会话类型
            self.message_service
                .broadcast_to_session_type(&session_type, message)
                .await?
        } else {
            // 广播给所有连接
            self.message_service
                .broadcast_to_all(message, request.exclude_sender)
                .await?
        };

        // 记录消息发送
        for _ in 0..sent_count {
            self.stats_service
                .record_message_sent(&MessageType::Text, message_size)
                .await;
        }

        info!("WebSocket消息广播完成: 发送给{}个连接", sent_count);
        Ok(sent_count)
    }

    async fn get_user_connections(&self, user_id: &Uuid) -> Vec<WsSession> {
        self.connection_service.get_user_connections(user_id).await
    }

    async fn get_stats(&self) -> WebSocketStatsResponse {
        let domain_connection_stats = self.stats_service.get_connection_stats().await;
        let domain_message_stats = self.stats_service.get_message_stats().await;

        // 转换为接口层的统计类型
        let connection_stats = app_interfaces::WebSocketStats {
            total_connections: domain_connection_stats.total_connections,
            active_connections: domain_connection_stats.active_connections as u64,
            unique_active_users: domain_connection_stats.unique_users as u64,
            connection_success_rate: domain_connection_stats.connection_success_rate,
            avg_connection_duration: domain_connection_stats.avg_connection_duration,
            failed_connections: domain_connection_stats.failed_connections,
            connections_by_type: domain_connection_stats
                .connections_by_type
                .into_iter()
                .map(|(k, v)| (k.to_string(), v as u64))
                .collect(),
        };

        let message_stats = app_interfaces::MessageStats {
            total_sent: domain_message_stats.total_sent,
            total_received: domain_message_stats.total_received,
            total_messages: domain_message_stats.total_sent + domain_message_stats.total_received,
            message_throughput: self
                .calculate_message_throughput(&domain_message_stats)
                .await,
            avg_message_size: domain_message_stats.avg_message_size,
            total_bytes: domain_message_stats.total_bytes,
            message_success_rate: domain_message_stats.message_success_rate / 100.0, // 转换为0.0-1.0范围
            messages_by_type: domain_message_stats
                .messages_by_type
                .into_iter()
                .map(|(k, v)| (format!("{k:?}"), v))
                .collect(),
        };

        let performance_metrics = app_interfaces::PerformanceMetrics {
            avg_latency_ms: self.calculate_avg_latency().await,
            p95_latency_ms: self.calculate_p95_latency().await,
            p99_latency_ms: self.calculate_p99_latency().await,
            cpu_usage_percent: self.get_cpu_usage().await,
            memory_usage_mb: self.get_memory_usage().await,
            network_bandwidth_mbps: self.calculate_network_bandwidth().await,
            error_rate: self.calculate_error_rate().await,
        };

        WebSocketStatsResponse {
            connection_stats,
            message_stats,
            performance_metrics,
            timestamp: chrono::Utc::now(),
        }
    }

    async fn get_active_connections(&self) -> Result<Vec<WsSession>, String> {
        info!("获取活跃WebSocket连接列表");

        // 从连接服务获取活跃连接
        let active_connections = self.connection_service.get_active_connections().await;

        info!("成功获取活跃连接: 连接数={}", active_connections.len());

        Ok(active_connections)
    }

    async fn update_activity(&self, connection_id: &ConnectionId) -> Result<(), String> {
        self.connection_service.update_activity(connection_id).await
    }

    async fn cleanup_idle_connections(&self, idle_timeout_seconds: i64) -> usize {
        let cleaned_count = self
            .connection_service
            .cleanup_idle_connections(idle_timeout_seconds)
            .await;

        if cleaned_count > 0 {
            info!("清理了{}个空闲WebSocket连接", cleaned_count);
        }

        cleaned_count
    }

    async fn register_connection_sender(
        &self,
        connection_id: &ConnectionId,
        sender_id: String,
    ) -> Result<(), String> {
        // 通过连接服务注册发送通道
        self.connection_service
            .register_sender(connection_id, sender_id)
            .await
    }

    async fn update_connection_sender(
        &self,
        connection_id: &ConnectionId,
        _sender: tokio::sync::mpsc::UnboundedSender<axum::extract::ws::Message>,
    ) -> Result<(), String> {
        // 通过接口方法更新发送通道
        // 注意：实际的发送通道更新需要在基础设施层实现
        info!("应用层：更新连接发送通道请求 - 连接ID={}", connection_id);
        self.connection_service
            .update_connection_sender(connection_id, "updated".to_string())
            .await
    }

    async fn get_online_users(&self) -> Result<Vec<WsSession>, app_common::error::AppError> {
        // 获取所有活跃连接
        let active_connections = self.connection_service.get_active_connections().await;

        info!(
            "获取在线用户列表: 当前活跃连接数={}",
            active_connections.len()
        );

        Ok(active_connections)
    }

    fn get_connection_service(&self) -> Arc<dyn WebSocketConnectionService> {
        self.connection_service.clone()
    }

    async fn broadcast_task_created(
        &self,
        task_data: serde_json::Value,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String> {
        info!("应用层：广播任务创建消息 - 发送者={:?}", sender_id);

        match &self.realtime_service {
            Some(service) => service.broadcast_task_created(task_data, sender_id).await,
            None => {
                warn!("实时消息服务未配置，无法广播任务创建消息");
                Ok(0)
            }
        }
    }

    async fn broadcast_task_updated(
        &self,
        task_data: serde_json::Value,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String> {
        info!("应用层：广播任务更新消息 - 发送者={:?}", sender_id);

        match &self.realtime_service {
            Some(service) => service.broadcast_task_updated(task_data, sender_id).await,
            None => {
                warn!("实时消息服务未配置，无法广播任务更新消息");
                Ok(0)
            }
        }
    }

    async fn broadcast_task_deleted(
        &self,
        task_id: u64,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String> {
        info!(
            "应用层：广播任务删除消息 - 任务ID={}, 发送者={:?}",
            task_id, sender_id
        );

        match &self.realtime_service {
            Some(service) => service.broadcast_task_deleted(task_id, sender_id).await,
            None => {
                warn!("实时消息服务未配置，无法广播任务删除消息");
                Ok(0)
            }
        }
    }

    async fn send_message_to_user(
        &self,
        request: app_interfaces::SendMessageRequest,
    ) -> Result<(), String> {
        info!(
            "应用层：发送消息给用户 - 用户ID={}, 内容长度={}字节",
            request.user_id,
            request.content.len()
        );

        // 创建广播请求，只发送给指定用户
        let broadcast_request = app_interfaces::BroadcastRequest {
            content: request.content,
            target_users: Some(vec![request.user_id]),
            target_session_type: None,
            exclude_sender: false,
            sender_id: None,
            priority: request.priority.map(|p| p.to_string()),
        };

        // 使用广播功能发送给指定用户
        match self.broadcast_message(broadcast_request).await {
            Ok(sent_count) => {
                if sent_count > 0 {
                    info!(
                        "消息已发送给用户: {}, 发送连接数: {}",
                        request.user_id, sent_count
                    );
                    Ok(())
                } else {
                    warn!("用户 {} 没有活跃连接，消息发送失败", request.user_id);
                    Err(format!("用户 {} 没有活跃连接", request.user_id))
                }
            }
            Err(e) => {
                error!("发送消息给用户 {} 失败: {}", request.user_id, e);
                Err(e)
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_websocket_connection_request() {
        let user_id = Uuid::new_v4();
        let username = "test_user".to_string();
        let session_type = "chat".to_string(); // 使用String类型

        let request = WebSocketConnectionRequest {
            user_id,
            username: username.clone(),
            session_type: session_type.clone(),
            metadata: None,
        };

        assert_eq!(request.user_id, user_id);
        assert_eq!(request.username, username);
        assert_eq!(request.session_type, session_type);
        assert!(request.metadata.is_none());
    }

    #[test]
    fn test_websocket_message_request() {
        let sender_id = "sender_123".to_string(); // 使用String类型
        let receiver_id = "receiver_456".to_string(); // 使用String类型
        let content = "Hello, World!".to_string();

        let request = WebSocketMessageRequest {
            sender_id: Some(sender_id.clone()),
            receiver_id: Some(receiver_id.clone()),
            content: content.clone(),
            priority: Some("high".to_string()), // 使用String类型
        };

        assert_eq!(request.sender_id, Some(sender_id));
        assert_eq!(request.receiver_id, Some(receiver_id));
        assert_eq!(request.content, content);
        assert_eq!(request.priority, Some("high".to_string()));
    }

    #[test]
    fn test_broadcast_request() {
        let content = "Broadcast message".to_string();
        let target_users = vec![Uuid::new_v4(), Uuid::new_v4()];

        let request = BroadcastRequest {
            content: content.clone(),
            target_users: Some(target_users.clone()),
            target_session_type: None,
            exclude_sender: true,
            sender_id: None,
            priority: Some("normal".to_string()), // 使用String类型
        };

        assert_eq!(request.content, content);
        assert_eq!(request.target_users, Some(target_users));
        assert!(request.exclude_sender);
        assert_eq!(request.priority, Some("normal".to_string()));
    }
}
