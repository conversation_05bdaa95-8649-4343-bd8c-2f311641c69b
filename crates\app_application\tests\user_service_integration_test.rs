//! # 用户应用服务集成测试
//!
//! 验证用户应用服务在DDD架构迁移后的功能完整性

use std::sync::Arc;
use uuid::Uuid;

use app_application::user_service::{UserApplicationService, UserApplicationServiceImpl};
use app_common::error::Result;
use app_domain::{
    entities::User, repositories::user_repository::UserRepositoryContract,
    services::user_service::UserDomainService,
};
use app_interfaces::auth::{LoginRequest, RegisterRequest};
use async_trait::async_trait;
use sea_orm::DbErr;

/// 模拟用户仓储实现（用于测试）
#[derive(Clone)]
struct MockUserRepository {
    users: Vec<User>,
    user_count: u64,
}

impl MockUserRepository {
    fn new() -> Self {
        Self {
            users: Vec::new(),
            user_count: 0,
        }
    }

    fn with_user_count(mut self, count: u64) -> Self {
        self.user_count = count;
        self
    }
}

#[async_trait]
impl UserRepositoryContract for MockUserRepository {
    async fn find_by_username(&self, username: &str) -> Result<Option<User>, DbErr> {
        Ok(self.users.iter().find(|u| u.username == username).cloned())
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<User>, DbErr> {
        Ok(self.users.iter().find(|u| u.id == id).cloned())
    }

    async fn create(&self, user: User) -> Result<User, DbErr> {
        Ok(user)
    }

    async fn update(&self, user: User) -> Result<User, DbErr> {
        Ok(user)
    }

    async fn delete(&self, _user_id: Uuid) -> Result<u64, DbErr> {
        Ok(1)
    }

    async fn count_all(&self) -> Result<u64, DbErr> {
        Ok(self.user_count)
    }
}

/// 模拟用户领域服务实现（用于测试）
#[derive(Clone)]
struct MockUserDomainService;

#[async_trait]
impl UserDomainService for MockUserDomainService {
    async fn is_username_available(&self, _username: &str) -> Result<bool> {
        Ok(true)
    }

    async fn validate_password_strength(&self, _password: &str) -> Result<bool> {
        Ok(true)
    }

    async fn hash_password(&self, password: &str) -> Result<String> {
        Ok(format!("hashed_{}", password))
    }

    async fn verify_password(&self, _password: &str, _hash: &str) -> Result<bool> {
        Ok(true)
    }
}

/// 创建测试用的用户应用服务
fn create_test_user_service(user_count: u64) -> UserApplicationServiceImpl {
    let user_repository = Arc::new(MockUserRepository::new().with_user_count(user_count));
    let user_domain_service = Arc::new(MockUserDomainService);

    UserApplicationServiceImpl::new(user_repository, user_domain_service)
}

#[tokio::test]
async fn test_fetch_user_count_success() {
    // 准备测试数据
    let expected_count = 42;
    let user_service = create_test_user_service(expected_count);

    // 执行测试
    let result = user_service.fetch_user_count().await;

    // 验证结果
    assert!(result.is_ok());
    assert_eq!(result.unwrap(), expected_count);
}

#[tokio::test]
async fn test_fetch_user_count_zero() {
    // 准备测试数据
    let user_service = create_test_user_service(0);

    // 执行测试
    let result = user_service.fetch_user_count().await;

    // 验证结果
    assert!(result.is_ok());
    assert_eq!(result.unwrap(), 0);
}

#[tokio::test]
async fn test_user_service_integration_with_ddd_architecture() {
    // 准备测试数据
    let user_service = create_test_user_service(10);

    // 测试用户名可用性检查
    let username_available = user_service.is_username_available("testuser").await;
    assert!(username_available.is_ok());
    assert!(username_available.unwrap());

    // 测试用户计数功能
    let user_count = user_service.fetch_user_count().await;
    assert!(user_count.is_ok());
    assert_eq!(user_count.unwrap(), 10);
}

#[tokio::test]
async fn test_user_service_ddd_layer_separation() {
    // 验证应用服务正确使用仓储接口和领域服务
    let user_service = create_test_user_service(5);

    // 测试通过仓储接口的数据访问
    let user_count = user_service.fetch_user_count().await;
    assert!(user_count.is_ok());

    // 测试通过领域服务的业务逻辑
    let username_check = user_service.is_username_available("newuser").await;
    assert!(username_check.is_ok());

    // 验证层级分离：应用服务不直接访问数据库
    // 这个测试通过编译即证明了架构的正确性
}
