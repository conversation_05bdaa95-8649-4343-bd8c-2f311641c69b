[package]
name = "app_common"
version = "0.1.0"
edition = "2024"
license = "MIT OR Apache-2.0"
authors = ["Rust学习者"]
description = "公共模块：错误处理、共享DTO、工具函数等"

[dependencies]
# 基础依赖
anyhow = { workspace = true }
thiserror = { workspace = true }

# Axum 相关
axum = { workspace = true }
http = "1.1"
hyper = "1.4"

# 序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 日志和跟踪
tracing = { workspace = true }
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "time", "local-time"] }
tracing-error = "0.2"
tracing-appender = "0.2"
tower-http = { version = "0.6.6", features = ["trace"] }

# JWT 和认证
jsonwebtoken = "9.3"

# 时间处理
chrono = { workspace = true }

# UUID 支持
uuid = { workspace = true }

# 数据库 ORM
sea-orm = { workspace = true }

# URL 编码
urlencoding = "2.1"

# 输入验证 - 升级到0.20修复RUSTSEC-2024-0421 (idna漏洞)
validator = { version = "0.20", features = ["derive"] }

# 配置管理
dotenvy = "0.15"

# 异步支持
async-trait = { workspace = true }
futures-util = "0.3"

# Tower 中间件支持
tower = { version = "0.5.2", features = ["timeout"] }

# 性能监控和指标收集
metrics = "0.23"
sysinfo = "0.32"
tokio = { workspace = true }
parking_lot = "0.12"

# 内部依赖
app_interfaces = { path = "../app_interfaces" }

[features]
default = []
testing = []

[dev-dependencies]
# 测试依赖
axum-test = "17.3"
tokio = { workspace = true, features = ["test-util"] }
tokio-test = "0.4"
tempfile = "3.8"
tower = { version = "0.5.2", features = ["util"] }
serial_test = "3.0"

[[example]]
name = "utils_demo"
path = "examples/utils_demo.rs"
