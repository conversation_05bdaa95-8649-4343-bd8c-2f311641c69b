//! # 异步Trait优化演示
//!
//! 展示如何在企业级Axum项目中正确使用async-trait优化
//!
//! 运行命令：
//! ```bash
//! cargo run --example async_trait_optimization_demo -p app_common
//! ```

use app_common::{
    AsyncTraitOptimizer, AsyncTraitOptimizerFactory, AsyncTraitPerformanceTester,
    DefaultAsyncTraitOptimizer,
};
use std::sync::Arc;
use tokio::time::Instant;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化日志
    tracing_subscriber::fmt::init();

    println!("🚀 异步Trait优化演示开始");
    println!("{}", "=".repeat(50));

    // 1. 基本异步trait使用演示
    demo_basic_async_trait_usage().await?;

    // 2. trait对象动态分发演示
    demo_trait_object_dynamic_dispatch().await?;

    // 3. 并发性能测试演示
    demo_concurrent_performance().await?;

    // 4. 线程安全性验证演示
    demo_thread_safety().await?;

    // 5. 企业级最佳实践演示
    demo_enterprise_best_practices().await?;

    println!("{}", "=".repeat(50));
    println!("✅ 异步Trait优化演示完成");

    Ok(())
}

/// 演示基本的异步trait使用
async fn demo_basic_async_trait_usage() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n📋 1. 基本异步trait使用演示");
    println!("{}", "-".repeat(30));

    // 创建优化器实例
    let optimizer = DefaultAsyncTraitOptimizer::new("demo_optimizer".to_string(), true);

    // 验证异步调用
    println!("🔍 验证异步调用能力...");
    let result = optimizer.verify_async_call("demo_operation").await?;
    println!("   结果: {}", if result { "✅ 成功" } else { "❌ 失败" });

    // 执行批处理操作
    println!("📦 执行批处理操作...");
    let operations = vec![
        "batch_op_1".to_string(),
        "batch_op_2".to_string(),
        "batch_op_3".to_string(),
        "batch_op_4".to_string(),
        "batch_op_5".to_string(),
    ];
    let processed_count = optimizer.execute_batch_operations(operations).await?;
    println!("   处理操作数量: {processed_count}");

    // 性能基准测试
    println!("⏱️  执行性能基准测试...");
    let avg_micros = optimizer.benchmark_dynamic_dispatch(100).await?;
    println!("   平均执行时间: {avg_micros} 微秒");

    Ok(())
}

/// 演示trait对象的动态分发
async fn demo_trait_object_dynamic_dispatch() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🎯 2. Trait对象动态分发演示");
    println!("{}", "-".repeat(30));

    // 使用工厂创建trait对象
    let optimizer: Arc<dyn AsyncTraitOptimizer> =
        AsyncTraitOptimizerFactory::create_default("trait_object_demo".to_string(), true);

    println!("🏭 使用工厂创建trait对象");
    println!("   类型: Arc<dyn AsyncTraitOptimizer>");

    // 验证trait对象可以正常调用异步方法
    println!("🔄 测试动态分发调用...");
    let start_time = Instant::now();

    for i in 0..10 {
        let operation_id = format!("dynamic_dispatch_{i}");
        let result = optimizer.verify_async_call(&operation_id).await?;
        if !result {
            println!("   ⚠️  操作 {operation_id} 验证失败");
        }
    }

    let elapsed = start_time.elapsed();
    println!("   ✅ 10次动态分发调用完成，耗时: {elapsed:?}");

    // 测试线程安全性
    println!("🔒 验证线程安全性...");
    let is_safe = optimizer.verify_thread_safety().await?;
    println!(
        "   结果: {}",
        if is_safe {
            "✅ 线程安全"
        } else {
            "❌ 非线程安全"
        }
    );

    Ok(())
}

/// 演示并发性能测试
async fn demo_concurrent_performance() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n⚡ 3. 并发性能测试演示");
    println!("{}", "-".repeat(30));

    let optimizer = AsyncTraitOptimizerFactory::create_default(
        "concurrent_demo".to_string(),
        false, // 关闭监控以减少噪音
    );

    // 测试不同并发级别的性能
    let concurrent_levels = vec![1, 5, 10, 20, 50];

    for &level in &concurrent_levels {
        println!("🔄 测试并发级别: {level}");

        let start_time = Instant::now();
        let (successful_calls, total_time) =
            AsyncTraitPerformanceTester::test_concurrent_calls(optimizer.clone(), level).await?;
        let actual_elapsed = start_time.elapsed();

        println!("   成功调用: {successful_calls}/{level}");
        println!("   报告时间: {total_time} ms");
        println!("   实际时间: {actual_elapsed:?}");
        println!(
            "   平均延迟: {:.2} ms",
            (total_time as f64) / (level as f64)
        );
        println!();
    }

    Ok(())
}

/// 演示线程安全性验证
async fn demo_thread_safety() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🛡️  4. 线程安全性验证演示");
    println!("{}", "-".repeat(30));

    let optimizer =
        AsyncTraitOptimizerFactory::create_default("thread_safety_demo".to_string(), true);

    // 测试不同线程数量的安全性
    let thread_counts = vec![1, 2, 4, 8];

    for &count in &thread_counts {
        println!("🧵 测试线程数量: {count}");

        let start_time = Instant::now();
        let is_safe =
            AsyncTraitPerformanceTester::test_thread_safety(optimizer.clone(), count).await?;
        let elapsed = start_time.elapsed();

        println!(
            "   结果: {}",
            if is_safe {
                "✅ 线程安全"
            } else {
                "❌ 非线程安全"
            }
        );
        println!("   耗时: {elapsed:?}");
        println!();
    }

    Ok(())
}

/// 演示企业级最佳实践
async fn demo_enterprise_best_practices() -> Result<(), Box<dyn std::error::Error>> {
    println!("\n🏢 5. 企业级最佳实践演示");
    println!("{}", "-".repeat(30));

    // 1. 创建多个优化器实例模拟微服务架构
    println!("🏗️  创建微服务架构模拟...");
    let optimizers = AsyncTraitOptimizerFactory::create_multiple(3, "microservice");

    println!("   创建了 {} 个微服务优化器实例", optimizers.len());

    // 2. 模拟服务间异步通信
    println!("📡 模拟服务间异步通信...");
    let mut tasks = Vec::new();

    for (i, optimizer) in optimizers.iter().enumerate() {
        let opt = optimizer.clone();
        let service_id = i;

        let task = tokio::spawn(async move {
            let operation_id = format!("service_{service_id}_operation");
            opt.verify_async_call(&operation_id).await
        });

        tasks.push(task);
    }

    // 等待所有服务响应
    let mut successful_services = 0;
    for (i, task) in tasks.into_iter().enumerate() {
        match task.await {
            Ok(Ok(true)) => {
                println!("   ✅ 服务 {i} 响应成功");
                successful_services += 1;
            }
            Ok(Ok(false)) => {
                println!("   ⚠️  服务 {i} 响应失败");
            }
            Ok(Err(e)) => {
                println!("   ❌ 服务 {i} 发生错误: {e}");
            }
            Err(e) => {
                println!("   💥 服务 {i} 任务失败: {e}");
            }
        }
    }

    println!(
        "   总计: {}/{} 服务响应成功",
        successful_services,
        optimizers.len()
    );

    // 3. 模拟负载均衡和故障恢复
    println!("⚖️  模拟负载均衡测试...");
    let primary_optimizer = &optimizers[0];
    let backup_optimizer = &optimizers[1];

    // 尝试主服务
    let primary_result = primary_optimizer
        .verify_async_call("load_balance_test")
        .await;
    match primary_result {
        Ok(true) => {
            println!("   ✅ 主服务处理成功");
        }
        _ => {
            println!("   ⚠️  主服务失败，切换到备用服务");
            let backup_result = backup_optimizer
                .verify_async_call("load_balance_test")
                .await?;
            if backup_result {
                println!("   ✅ 备用服务处理成功");
            } else {
                println!("   ❌ 备用服务也失败");
            }
        }
    }

    // 4. 性能监控和指标收集
    println!("📊 性能监控和指标收集...");
    let monitor_optimizer = &optimizers[2];

    let benchmark_result = monitor_optimizer.benchmark_dynamic_dispatch(50).await?;
    println!("   平均响应时间: {benchmark_result} 微秒");

    if benchmark_result < 1000 {
        println!("   ✅ 性能良好 (< 1ms)");
    } else if benchmark_result < 5000 {
        println!("   ⚠️  性能一般 (1-5ms)");
    } else {
        println!("   ❌ 性能较差 (> 5ms)");
    }

    Ok(())
}
