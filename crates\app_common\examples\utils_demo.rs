//! 工具函数使用示例
//!
//! 演示如何使用迁移到app_common的工具函数

use app_common::error::Result;
use app_common::utils::*;

fn main() -> Result<()> {
    println!("=== App Common 工具函数演示 ===\n");

    // 1. UUID 工具函数演示
    println!("1. UUID 工具函数:");

    // 生成新的UUID
    let new_uuid = generate_uuid();
    println!("   生成的UUID: {new_uuid}");

    let new_uuid_str = generate_uuid_string();
    println!("   生成的UUID字符串: {new_uuid_str}");

    // 验证UUID格式
    let valid_uuid = "550e8400-e29b-41d4-a716-************";
    println!(
        "   UUID '{}' 是否有效: {}",
        valid_uuid,
        is_valid_uuid(valid_uuid)
    );

    let invalid_uuid = "invalid-uuid";
    println!(
        "   UUID '{}' 是否有效: {}",
        invalid_uuid,
        is_valid_uuid(invalid_uuid)
    );

    // 解析UUID
    match parse_uuid_string(valid_uuid) {
        Ok(uuid) => println!("   解析成功: {uuid}"),
        Err(e) => println!("   解析失败: {e}"),
    }

    println!();

    // 2. 验证工具函数演示
    println!("2. 验证工具函数:");

    // 验证用户名
    let usernames = vec!["valid_user", "ab", "invalid user", "user@domain"];
    for username in usernames {
        match validate_username(username) {
            Ok(_) => println!("   用户名 '{username}': 有效"),
            Err(e) => println!("   用户名 '{username}': 无效 - {e}"),
        }
    }

    // 验证密码强度
    let passwords = vec!["password123", "short", "12345678", "password"];
    for password in passwords {
        match validate_password_strength(password) {
            Ok(_) => println!("   密码 '{password}': 强度足够"),
            Err(e) => println!("   密码 '{password}': 强度不足 - {e}"),
        }
    }

    // 验证邮箱
    let emails = vec!["<EMAIL>", "invalid-email", "@example.com"];
    for email in emails {
        match validate_email(email) {
            Ok(_) => println!("   邮箱 '{email}': 有效"),
            Err(e) => println!("   邮箱 '{email}': 无效 - {e}"),
        }
    }

    println!();

    // 3. JWT 工具函数演示
    println!("3. JWT 工具函数:");

    let jwt_utils = JwtUtils::new("demo-secret-key".to_string());

    // 创建JWT token
    match jwt_utils.create_token("user123", "demo_user", 1) {
        Ok(token) => {
            println!("   创建的JWT token: {}", &token[..50]); // 只显示前50个字符

            // 验证token
            match jwt_utils.validate_token(&token) {
                Ok(claims) => {
                    println!("   验证成功:");
                    println!("     用户ID: {}", claims.sub);
                    println!("     用户名: {}", claims.username);
                    println!("     签发时间: {}", claims.iat);
                    println!("     过期时间: {}", claims.exp);
                }
                Err(e) => println!("   验证失败: {e}"),
            }

            // 测试Bearer token提取
            let bearer_token = format!("Bearer {token}");
            match jwt_utils.extract_token_from_bearer(&bearer_token) {
                Ok(extracted) => println!("   从Bearer token提取成功: {}...", &extracted[..20]),
                Err(e) => println!("   从Bearer token提取失败: {e}"),
            }
        }
        Err(e) => println!("   创建JWT token失败: {e}"),
    }

    println!();

    // 4. 身份验证服务演示
    println!("4. 身份验证服务:");

    let auth_service = AuthService::new("demo-auth-secret".to_string());

    // 创建测试token
    match auth_service.create_token("user456", "auth_demo_user", 2) {
        Ok(token) => {
            println!("   创建的认证token: {}...", &token[..30]);

            // 模拟HTTP请求头验证
            use axum::http::{HeaderMap, HeaderValue, header::AUTHORIZATION};
            let mut headers = HeaderMap::new();
            headers.insert(
                AUTHORIZATION,
                HeaderValue::from_str(&format!("Bearer {token}")).unwrap(),
            );

            match auth_service.authenticate_http_request(&headers) {
                Ok(claims) => {
                    println!("   HTTP认证成功:");
                    println!("     用户ID: {}", claims.sub);
                    println!("     用户名: {}", claims.username);
                }
                Err(e) => println!("   HTTP认证失败: {e}"),
            }

            // 模拟WebSocket查询参数验证
            use axum::http::Uri;
            let ws_uri: Uri = format!("ws://localhost:3000/ws?token={token}")
                .parse()
                .unwrap();

            match auth_service.authenticate_websocket_request(&ws_uri, &HeaderMap::new()) {
                Ok((claims, method)) => {
                    println!("   WebSocket认证成功:");
                    println!("     用户ID: {}", claims.sub);
                    println!("     用户名: {}", claims.username);
                    println!("     提取方法: {method:?}");
                }
                Err(e) => println!("   WebSocket认证失败: {e}"),
            }
        }
        Err(e) => println!("   创建认证token失败: {e}"),
    }

    println!("\n=== 演示完成 ===");
    Ok(())
}
