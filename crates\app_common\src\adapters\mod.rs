//! # API适配层模块
//!
//! 提供不同API版本之间的适配和转换功能
//!
//! ## 核心功能
//! - 请求格式适配
//! - 响应格式转换
//! - 字段映射和转换
//! - 向后兼容性保障
//!
//! ## 设计原则
//! - 透明适配：客户端无感知的版本适配
//! - 性能优化：最小化适配开销
//! - 可扩展性：支持多版本并存

pub mod request_adapter;
pub mod response_adapter;
pub mod version_adapter;

// 重新导出常用类型
pub use request_adapter::{RequestAdapter, RequestAdapterConfig};
pub use response_adapter::{ResponseAdapter, ResponseAdapterConfig};
pub use version_adapter::{AdapterRegistry, VersionAdapter};
