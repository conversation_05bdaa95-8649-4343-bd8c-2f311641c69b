//! # 版本适配器
//!
//! 核心版本适配逻辑，支持不同API版本之间的数据转换

use app_interfaces::versioning::ApiVersion;
use serde_json::Value;
use std::collections::HashMap;
use tracing::{info, instrument, warn};

/// 版本适配器特征
pub trait VersionAdapter: Send + Sync {
    /// 适配请求数据
    fn adapt_request(
        &self,
        data: Value,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> Result<Value, AdapterError>;

    /// 适配响应数据
    fn adapt_response(
        &self,
        data: Value,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> Result<Value, AdapterError>;

    /// 检查是否支持指定版本适配
    fn supports_adaptation(&self, from_version: &ApiVersion, to_version: &ApiVersion) -> bool;
}

/// 适配器错误类型
#[derive(Debug, Clone)]
pub enum AdapterError {
    /// 不支持的版本适配
    UnsupportedVersions { from: ApiVersion, to: ApiVersion },
    /// 数据格式错误
    InvalidDataFormat(String),
    /// 字段映射失败
    FieldMappingError(String),
    /// 类型转换错误
    TypeConversionError(String),
}

impl std::fmt::Display for AdapterError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AdapterError::UnsupportedVersions { from, to } => {
                write!(f, "不支持从版本 {from} 到版本 {to} 的适配")
            }
            AdapterError::InvalidDataFormat(msg) => {
                write!(f, "数据格式错误: {msg}")
            }
            AdapterError::FieldMappingError(msg) => {
                write!(f, "字段映射错误: {msg}")
            }
            AdapterError::TypeConversionError(msg) => {
                write!(f, "类型转换错误: {msg}")
            }
        }
    }
}

impl std::error::Error for AdapterError {}

/// 适配器注册表
pub struct AdapterRegistry {
    /// 注册的适配器
    adapters: HashMap<String, Box<dyn VersionAdapter>>,
}

impl AdapterRegistry {
    /// 创建新的适配器注册表
    pub fn new() -> Self {
        Self {
            adapters: HashMap::new(),
        }
    }

    /// 注册适配器
    pub fn register<T: VersionAdapter + 'static>(&mut self, name: String, adapter: T) {
        self.adapters.insert(name, Box::new(adapter));
    }

    /// 获取适配器
    pub fn get_adapter(&self, name: &str) -> Option<&dyn VersionAdapter> {
        self.adapters.get(name).map(|adapter| adapter.as_ref())
    }

    /// 执行版本适配
    #[instrument(skip(self, data), fields(adapter_name = %adapter_name, from_version = %from_version, to_version = %to_version))]
    pub fn adapt_data(
        &self,
        adapter_name: &str,
        data: Value,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
        is_request: bool,
    ) -> Result<Value, AdapterError> {
        let adapter = self.get_adapter(adapter_name).ok_or_else(|| {
            AdapterError::InvalidDataFormat(format!("适配器 {adapter_name} 未找到"))
        })?;

        if !adapter.supports_adaptation(from_version, to_version) {
            return Err(AdapterError::UnsupportedVersions {
                from: from_version.clone(),
                to: to_version.clone(),
            });
        }

        let result = if is_request {
            adapter.adapt_request(data, from_version, to_version)
        } else {
            adapter.adapt_response(data, from_version, to_version)
        };

        match &result {
            Ok(_) => info!("版本适配成功"),
            Err(e) => warn!("版本适配失败: {}", e),
        }

        result
    }
}

impl Default for AdapterRegistry {
    fn default() -> Self {
        let mut registry = Self::new();

        // 注册默认适配器
        registry.register("user".to_string(), UserAdapter::new());
        registry.register("task".to_string(), TaskAdapter::new());
        registry.register("chat".to_string(), ChatAdapter::new());

        registry
    }
}

/// 用户API适配器
#[derive(Debug)]
pub struct UserAdapter;

impl Default for UserAdapter {
    fn default() -> Self {
        Self::new()
    }
}

impl UserAdapter {
    pub fn new() -> Self {
        Self
    }
}

impl VersionAdapter for UserAdapter {
    fn adapt_request(
        &self,
        mut data: Value,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> Result<Value, AdapterError> {
        // 示例：从v1.0.0到v1.1.0的适配
        if from_version == &ApiVersion::new(1, 0, 0) && to_version == &ApiVersion::new(1, 1, 0) {
            // v1.1.0添加了可选的display_name字段
            if let Some(obj) = data.as_object_mut() {
                if !obj.contains_key("display_name") {
                    // 如果没有display_name，使用username作为默认值
                    if let Some(username) = obj.get("username").and_then(|v| v.as_str()) {
                        obj.insert(
                            "display_name".to_string(),
                            Value::String(username.to_string()),
                        );
                    }
                }
            }
        }

        Ok(data)
    }

    fn adapt_response(
        &self,
        mut data: Value,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> Result<Value, AdapterError> {
        // 示例：从v1.1.0到v1.0.0的适配（向后兼容）
        if from_version == &ApiVersion::new(1, 1, 0) && to_version == &ApiVersion::new(1, 0, 0) {
            // 移除v1.1.0中新增的字段
            if let Some(obj) = data.as_object_mut() {
                obj.remove("display_name");
                obj.remove("avatar_url");
                obj.remove("bio");
                obj.remove("location");
                obj.remove("website");
            }
        }

        Ok(data)
    }

    fn supports_adaptation(&self, from_version: &ApiVersion, to_version: &ApiVersion) -> bool {
        // 支持v1.x.x版本之间的适配
        from_version.major == 1 && to_version.major == 1
    }
}

/// 任务API适配器
#[derive(Debug)]
pub struct TaskAdapter;

impl Default for TaskAdapter {
    fn default() -> Self {
        Self::new()
    }
}

impl TaskAdapter {
    pub fn new() -> Self {
        Self
    }
}

impl VersionAdapter for TaskAdapter {
    fn adapt_request(
        &self,
        mut data: Value,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> Result<Value, AdapterError> {
        // 示例：处理任务状态字段的变更
        if from_version == &ApiVersion::new(1, 0, 0) && to_version == &ApiVersion::new(1, 1, 0) {
            if let Some(obj) = data.as_object_mut() {
                // v1.1.0将completed字段改为status字段
                if let Some(completed) = obj.remove("completed") {
                    let status = if completed.as_bool().unwrap_or(false) {
                        "completed"
                    } else {
                        "pending"
                    };
                    obj.insert("status".to_string(), Value::String(status.to_string()));
                }
            }
        }

        Ok(data)
    }

    fn adapt_response(
        &self,
        mut data: Value,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> Result<Value, AdapterError> {
        // 示例：向后兼容的响应适配
        if from_version == &ApiVersion::new(1, 1, 0) && to_version == &ApiVersion::new(1, 0, 0) {
            if let Some(obj) = data.as_object_mut() {
                // 将status字段转换回completed字段
                if let Some(status) = obj.remove("status") {
                    let completed = status.as_str() == Some("completed");
                    obj.insert("completed".to_string(), Value::Bool(completed));
                }

                // 移除v1.1.0中新增的字段
                obj.remove("priority");
                obj.remove("due_date");
            }
        }

        Ok(data)
    }

    fn supports_adaptation(&self, from_version: &ApiVersion, to_version: &ApiVersion) -> bool {
        from_version.major == 1 && to_version.major == 1
    }
}

/// 聊天API适配器
#[derive(Debug)]
pub struct ChatAdapter;

impl Default for ChatAdapter {
    fn default() -> Self {
        Self::new()
    }
}

impl ChatAdapter {
    pub fn new() -> Self {
        Self
    }
}

impl VersionAdapter for ChatAdapter {
    fn adapt_request(
        &self,
        data: Value,
        _from_version: &ApiVersion,
        _to_version: &ApiVersion,
    ) -> Result<Value, AdapterError> {
        // 聊天API目前版本稳定，无需特殊适配
        Ok(data)
    }

    fn adapt_response(
        &self,
        mut data: Value,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> Result<Value, AdapterError> {
        // 示例：处理消息格式的变更
        if from_version == &ApiVersion::new(1, 1, 0) && to_version == &ApiVersion::new(1, 0, 0) {
            if let Some(obj) = data.as_object_mut() {
                // 移除v1.1.0中新增的消息元数据
                obj.remove("metadata");
                obj.remove("reactions");
                obj.remove("thread_id");
            }
        }

        Ok(data)
    }

    fn supports_adaptation(&self, from_version: &ApiVersion, to_version: &ApiVersion) -> bool {
        from_version.major == 1 && to_version.major == 1
    }
}

/// 通用字段映射工具
pub struct FieldMapper;

impl FieldMapper {
    /// 映射字段名称
    pub fn map_field_name(
        field_name: &str,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> String {
        // 示例映射规则
        match (from_version, to_version, field_name) {
            // v1.0.0 -> v1.1.0: completed -> status
            (
                &ApiVersion {
                    major: 1,
                    minor: 0,
                    patch: 0,
                },
                &ApiVersion {
                    major: 1,
                    minor: 1,
                    patch: 0,
                },
                "completed",
            ) => "status".to_string(),
            // v1.1.0 -> v1.0.0: status -> completed
            (
                &ApiVersion {
                    major: 1,
                    minor: 1,
                    patch: 0,
                },
                &ApiVersion {
                    major: 1,
                    minor: 0,
                    patch: 0,
                },
                "status",
            ) => "completed".to_string(),
            _ => field_name.to_string(),
        }
    }

    /// 转换字段值
    pub fn convert_field_value(
        field_name: &str,
        value: &Value,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> Result<Value, AdapterError> {
        match (from_version, to_version, field_name) {
            // completed (bool) -> status (string)
            (
                &ApiVersion {
                    major: 1,
                    minor: 0,
                    patch: 0,
                },
                &ApiVersion {
                    major: 1,
                    minor: 1,
                    patch: 0,
                },
                "completed",
            ) => {
                if let Some(completed) = value.as_bool() {
                    let status = if completed { "completed" } else { "pending" };
                    Ok(Value::String(status.to_string()))
                } else {
                    Err(AdapterError::TypeConversionError(
                        "completed字段必须是布尔值".to_string(),
                    ))
                }
            }
            // status (string) -> completed (bool)
            (
                &ApiVersion {
                    major: 1,
                    minor: 1,
                    patch: 0,
                },
                &ApiVersion {
                    major: 1,
                    minor: 0,
                    patch: 0,
                },
                "status",
            ) => {
                if let Some(status) = value.as_str() {
                    let completed = status == "completed";
                    Ok(Value::Bool(completed))
                } else {
                    Err(AdapterError::TypeConversionError(
                        "status字段必须是字符串".to_string(),
                    ))
                }
            }
            _ => Ok(value.clone()),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_user_adapter_request() {
        let adapter = UserAdapter::new();
        let data = json!({
            "username": "testuser",
            "email": "<EMAIL>"
        });

        let result = adapter
            .adapt_request(data, &ApiVersion::new(1, 0, 0), &ApiVersion::new(1, 1, 0))
            .unwrap();

        assert_eq!(result["display_name"], "testuser");
    }

    #[test]
    fn test_task_adapter_response() {
        let adapter = TaskAdapter::new();
        let data = json!({
            "id": 1,
            "title": "测试任务",
            "status": "completed",
            "priority": "high"
        });

        let result = adapter
            .adapt_response(data, &ApiVersion::new(1, 1, 0), &ApiVersion::new(1, 0, 0))
            .unwrap();

        assert_eq!(result["completed"], true);
        assert!(result.get("priority").is_none());
    }

    #[test]
    fn test_field_mapper() {
        let mapped_name = FieldMapper::map_field_name(
            "completed",
            &ApiVersion::new(1, 0, 0),
            &ApiVersion::new(1, 1, 0),
        );
        assert_eq!(mapped_name, "status");

        let converted_value = FieldMapper::convert_field_value(
            "completed",
            &Value::Bool(true),
            &ApiVersion::new(1, 0, 0),
            &ApiVersion::new(1, 1, 0),
        )
        .unwrap();
        assert_eq!(converted_value, "completed");
    }
}
