//! # 异步Trait优化测试模块
//!
//! 提供全面的异步trait优化功能测试，验证：
//! - async-trait的正确使用
//! - trait对象的异步调用能力
//! - 线程安全性验证
//! - 性能基准测试

use super::*;
use std::sync::Arc;

/// 测试异步trait优化器的基本功能
#[tokio::test]
async fn test_async_trait_optimizer_basic_functionality() {
    // 创建优化器实例
    let optimizer = DefaultAsyncTraitOptimizer::new("test_optimizer".to_string(), true);

    // 测试验证异步调用
    let result = optimizer.verify_async_call("valid_operation").await;
    assert!(result.is_ok());
    assert!(result.unwrap());

    // 测试无效操作
    let result = optimizer.verify_async_call("invalid_operation").await;
    assert!(result.is_ok());
    assert!(!result.unwrap());

    // 测试空操作ID
    let result = optimizer.verify_async_call("").await;
    assert!(result.is_err());
}

/// 测试异步批处理操作
#[tokio::test]
async fn test_batch_operations() {
    let optimizer = DefaultAsyncTraitOptimizer::new("batch_test".to_string(), true);

    // 测试空操作列表
    let result = optimizer.execute_batch_operations(vec![]).await;
    assert!(result.is_ok());
    assert_eq!(result.unwrap(), 0);

    // 测试正常批处理
    let operations = vec![
        "operation_1".to_string(),
        "operation_2".to_string(),
        "operation_3".to_string(),
    ];
    let result = optimizer.execute_batch_operations(operations).await;
    assert!(result.is_ok());
    assert_eq!(result.unwrap(), 3);
}

/// 测试动态分发性能基准
#[tokio::test]
async fn test_dynamic_dispatch_benchmark() {
    let optimizer = DefaultAsyncTraitOptimizer::new(
        "benchmark_test".to_string(),
        false, // 关闭监控以减少测试噪音
    );

    // 测试无效迭代次数
    let result = optimizer.benchmark_dynamic_dispatch(0).await;
    assert!(result.is_err());

    // 测试正常基准测试
    let result = optimizer.benchmark_dynamic_dispatch(10).await;
    assert!(result.is_ok());
    let avg_micros = result.unwrap();
    assert!(avg_micros > 0); // 应该有一些执行时间
}

/// 测试线程安全性验证
#[tokio::test]
async fn test_thread_safety_verification() {
    let optimizer = DefaultAsyncTraitOptimizer::new("thread_safety_test".to_string(), true);

    let result = optimizer.verify_thread_safety().await;
    assert!(result.is_ok());
    assert!(result.unwrap());
}

/// 测试trait对象的使用
#[tokio::test]
async fn test_trait_object_usage() {
    // 使用工厂创建trait对象
    let optimizer: Arc<dyn AsyncTraitOptimizer> =
        AsyncTraitOptimizerFactory::create_default("trait_object_test".to_string(), true);

    // 验证trait对象可以正常调用异步方法
    let result = optimizer.verify_async_call("trait_object_operation").await;
    assert!(result.is_ok());
    assert!(result.unwrap());

    // 验证批处理操作
    let operations = vec!["op1".to_string(), "op2".to_string()];
    let result = optimizer.execute_batch_operations(operations).await;
    assert!(result.is_ok());
    assert_eq!(result.unwrap(), 2);
}

/// 测试多个优化器实例的创建
#[tokio::test]
async fn test_multiple_optimizers() {
    let optimizers = AsyncTraitOptimizerFactory::create_multiple(3, "multi_test");

    assert_eq!(optimizers.len(), 3);

    // 验证每个优化器都能正常工作
    for (i, optimizer) in optimizers.iter().enumerate() {
        let operation_id = format!("multi_operation_{i}");
        let result = optimizer.verify_async_call(&operation_id).await;
        assert!(result.is_ok());
        assert!(result.unwrap());
    }
}

/// 测试并发调用性能
#[tokio::test]
async fn test_concurrent_calls_performance() {
    let optimizer =
        AsyncTraitOptimizerFactory::create_default("concurrent_test".to_string(), false);

    let (successful_calls, total_time) = AsyncTraitPerformanceTester::test_concurrent_calls(
        optimizer, 5, // 5个并发调用
    )
    .await
    .unwrap();

    assert_eq!(successful_calls, 5);
    assert!(total_time > 0);
}

/// 测试多线程环境下的线程安全性
#[tokio::test]
async fn test_multi_thread_safety() {
    let optimizer =
        AsyncTraitOptimizerFactory::create_default("multi_thread_test".to_string(), true);

    let is_safe = AsyncTraitPerformanceTester::test_thread_safety(
        optimizer, 3, // 3个线程
    )
    .await
    .unwrap();

    assert!(is_safe);
}

/// 测试Send + Sync约束的编译时验证
#[tokio::test]
async fn test_send_sync_constraints() {
    // 这个测试主要验证编译时约束
    // 如果代码能编译通过，说明Send + Sync约束正确

    let optimizer: Arc<dyn AsyncTraitOptimizer> =
        AsyncTraitOptimizerFactory::create_default("send_sync_test".to_string(), true);

    // 在异步任务中使用trait对象
    let handle =
        tokio::spawn(async move { optimizer.verify_async_call("send_sync_operation").await });

    let result = handle.await.unwrap();
    assert!(result.is_ok());
    assert!(result.unwrap());
}

/// 测试错误处理
#[tokio::test]
async fn test_error_handling() {
    let optimizer = DefaultAsyncTraitOptimizer::new("error_test".to_string(), true);

    // 测试验证错误
    let result = optimizer.verify_async_call("").await;
    assert!(result.is_err());

    if let Err(AppError::ValidationError(msg)) = result {
        assert_eq!(msg, "操作ID不能为空");
    } else {
        panic!("期望ValidationError");
    }

    // 测试基准测试错误
    let result = optimizer.benchmark_dynamic_dispatch(0).await;
    assert!(result.is_err());

    if let Err(AppError::ValidationError(msg)) = result {
        assert_eq!(msg, "迭代次数必须大于0");
    } else {
        panic!("期望ValidationError");
    }
}

/// 测试性能监控开关
#[tokio::test]
async fn test_monitoring_toggle() {
    // 测试启用监控
    let optimizer_with_monitoring =
        DefaultAsyncTraitOptimizer::new("monitoring_enabled".to_string(), true);

    let result = optimizer_with_monitoring.verify_async_call("test_op").await;
    assert!(result.is_ok());

    // 测试禁用监控
    let optimizer_without_monitoring =
        DefaultAsyncTraitOptimizer::new("monitoring_disabled".to_string(), false);

    let result = optimizer_without_monitoring
        .verify_async_call("test_op")
        .await;
    assert!(result.is_ok());
}

/// 集成测试：完整的异步trait优化流程
#[tokio::test]
async fn test_complete_async_trait_optimization_flow() {
    // 1. 创建优化器
    let optimizer =
        AsyncTraitOptimizerFactory::create_default("integration_test".to_string(), true);

    // 2. 验证基本功能
    let verify_result = optimizer.verify_async_call("integration_operation").await;
    assert!(verify_result.is_ok());
    assert!(verify_result.unwrap());

    // 3. 执行批处理
    let batch_operations = vec![
        "batch_op_1".to_string(),
        "batch_op_2".to_string(),
        "batch_op_3".to_string(),
    ];
    let batch_result = optimizer.execute_batch_operations(batch_operations).await;
    assert!(batch_result.is_ok());
    assert_eq!(batch_result.unwrap(), 3);

    // 4. 性能基准测试
    let benchmark_result = optimizer.benchmark_dynamic_dispatch(5).await;
    assert!(benchmark_result.is_ok());
    assert!(benchmark_result.unwrap() > 0);

    // 5. 线程安全验证
    let safety_result = optimizer.verify_thread_safety().await;
    assert!(safety_result.is_ok());
    assert!(safety_result.unwrap());

    // 6. 并发性能测试
    let (successful_calls, _) =
        AsyncTraitPerformanceTester::test_concurrent_calls(optimizer.clone(), 3)
            .await
            .unwrap();
    assert_eq!(successful_calls, 3);

    // 7. 多线程安全测试
    let thread_safety = AsyncTraitPerformanceTester::test_thread_safety(optimizer, 2)
        .await
        .unwrap();
    assert!(thread_safety);
}
