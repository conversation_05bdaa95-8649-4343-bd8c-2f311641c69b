//! # 认证中间件模块
//!
//! 提供Axum认证中间件，包括：
//! - JWT token验证中间件
//! - 基于角色的权限检查中间件
//! - 资源访问控制中间件
//! - 自动token刷新中间件

use super::{
    jwt_manager::{ExtendedClaims, JwtManager},
    permission_checker::{Permission, PermissionChecker},
    role_manager::UserRole,
};
use axum::{
    extract::{Request, State},
    http::{StatusCode, header::AUTHORIZATION},
    middleware::Next,
    response::Response,
};
use std::sync::Arc;

/// 认证中间件
#[derive(Debug, Clone)]
pub struct AuthMiddleware {
    jwt_manager: Arc<JwtManager>,
}

impl AuthMiddleware {
    /// 创建新的认证中间件
    pub fn new(jwt_manager: JwtManager) -> Self {
        Self {
            jwt_manager: Arc::new(jwt_manager),
        }
    }

    /// JWT认证中间件函数
    pub async fn jwt_auth_middleware(
        State(auth_middleware): State<AuthMiddleware>,
        mut req: Request,
        next: Next,
    ) -> Result<Response, StatusCode> {
        // 从请求头中提取JWT token
        let auth_header = req
            .headers()
            .get(AUTHORIZATION)
            .and_then(|header| header.to_str().ok())
            .and_then(|header| header.strip_prefix("Bearer "));

        let token = match auth_header {
            Some(token) => token,
            None => {
                tracing::warn!("请求缺少Authorization头");
                return Err(StatusCode::UNAUTHORIZED);
            }
        };

        // 验证token
        let claims = match auth_middleware.jwt_manager.validate_token_with_role(token) {
            Ok(claims) => claims,
            Err(err) => {
                tracing::warn!("JWT token验证失败: {:?}", err);
                return Err(StatusCode::UNAUTHORIZED);
            }
        };

        // 将用户信息注入到请求扩展中
        req.extensions_mut().insert(claims);

        Ok(next.run(req).await)
    }

    /// 可选的JWT认证中间件（允许未认证的请求通过）
    pub async fn optional_jwt_auth_middleware(
        State(auth_middleware): State<AuthMiddleware>,
        mut req: Request,
        next: Next,
    ) -> Response {
        // 尝试从请求头中提取JWT token
        if let Some(auth_header) = req
            .headers()
            .get(AUTHORIZATION)
            .and_then(|header| header.to_str().ok())
            .and_then(|header| header.strip_prefix("Bearer "))
        {
            // 尝试验证token
            if let Ok(claims) = auth_middleware
                .jwt_manager
                .validate_token_with_role(auth_header)
            {
                req.extensions_mut().insert(claims);
            }
        }

        next.run(req).await
    }
}

/// 基于角色的认证中间件
#[derive(Debug, Clone)]
pub struct RoleBasedAuthMiddleware {
    jwt_manager: Arc<JwtManager>,
    permission_checker: Arc<PermissionChecker>,
}

impl RoleBasedAuthMiddleware {
    /// 创建新的基于角色的认证中间件
    pub fn new(jwt_manager: JwtManager, permission_checker: PermissionChecker) -> Self {
        Self {
            jwt_manager: Arc::new(jwt_manager),
            permission_checker: Arc::new(permission_checker),
        }
    }

    /// 需要特定权限的中间件
    pub fn require_permission(
        permission: Permission,
    ) -> impl (Fn(
        Request,
        Next,
    ) -> std::pin::Pin<
        Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
    >) + Clone {
        move |req: Request, next: Next| {
            let permission = permission.clone();
            Box::pin(async move {
                // 从请求扩展中获取用户信息
                let claims = req
                    .extensions()
                    .get::<ExtendedClaims>()
                    .ok_or(StatusCode::UNAUTHORIZED)?;

                // 获取用户角色
                let role = claims
                    .get_role()
                    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

                // 检查权限
                let permission_checker = PermissionChecker::new();
                if !permission_checker.has_permission(&role, &permission) {
                    tracing::warn!(
                        "用户 {} (角色: {:?}) 尝试访问需要 {:?} 权限的资源",
                        claims.username,
                        role,
                        permission
                    );
                    return Err(StatusCode::FORBIDDEN);
                }

                Ok(next.run(req).await)
            })
        }
    }

    /// 需要特定角色的中间件
    pub fn require_role(
        required_role: UserRole,
    ) -> impl (Fn(
        Request,
        Next,
    ) -> std::pin::Pin<
        Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
    >) + Clone {
        move |req: Request, next: Next| {
            let required_role = required_role.clone();
            Box::pin(async move {
                // 从请求扩展中获取用户信息
                let claims = req
                    .extensions()
                    .get::<ExtendedClaims>()
                    .ok_or(StatusCode::UNAUTHORIZED)?;

                // 获取用户角色
                let user_role = claims
                    .get_role()
                    .map_err(|_| StatusCode::INTERNAL_SERVER_ERROR)?;

                // 检查角色权限级别
                if user_role.permission_level() < required_role.permission_level() {
                    tracing::warn!(
                        "用户 {} (角色: {:?}) 尝试访问需要 {:?} 角色的资源",
                        claims.username,
                        user_role,
                        required_role
                    );
                    return Err(StatusCode::FORBIDDEN);
                }

                Ok(next.run(req).await)
            })
        }
    }

    /// 管理员权限中间件
    pub fn require_admin() -> impl (Fn(
        Request,
        Next,
    ) -> std::pin::Pin<
        Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
    >) + Clone {
        Self::require_role(UserRole::Admin)
    }

    /// 管理级别权限中间件（Manager及以上）
    pub fn require_manager() -> impl (Fn(
        Request,
        Next,
    ) -> std::pin::Pin<
        Box<dyn std::future::Future<Output = Result<Response, StatusCode>> + Send>,
    >) + Clone {
        Self::require_role(UserRole::Manager)
    }
}

/// 自动token刷新中间件
#[derive(Debug, Clone)]
pub struct TokenRefreshMiddleware {
    jwt_manager: Arc<JwtManager>,
    refresh_threshold_minutes: i64,
}

impl TokenRefreshMiddleware {
    /// 创建新的token刷新中间件
    pub fn new(jwt_manager: JwtManager, refresh_threshold_minutes: i64) -> Self {
        Self {
            jwt_manager: Arc::new(jwt_manager),
            refresh_threshold_minutes,
        }
    }

    /// Token刷新中间件函数
    pub async fn token_refresh_middleware(
        State(refresh_middleware): State<TokenRefreshMiddleware>,
        req: Request,
        next: Next,
    ) -> Response {
        // 先提取需要的信息，避免借用冲突
        let claims = req.extensions().get::<ExtendedClaims>().cloned();
        let auth_header = req
            .headers()
            .get(AUTHORIZATION)
            .and_then(|header| header.to_str().ok())
            .and_then(|header| header.strip_prefix("Bearer "))
            .map(|s| s.to_string());

        let mut response = next.run(req).await;

        // 检查请求中是否有用户信息
        if let Some(claims) = claims {
            // 检查token是否即将过期
            if claims.is_expiring_soon(refresh_middleware.refresh_threshold_minutes) {
                // 尝试刷新token
                if let Some(auth_header) = auth_header {
                    if let Ok(new_token) =
                        refresh_middleware.jwt_manager.refresh_token(&auth_header)
                    {
                        // 在响应头中返回新token
                        if let Ok(header_value) = new_token.parse() {
                            response.headers_mut().insert("X-New-Token", header_value);
                        }
                        tracing::info!("为用户 {} 自动刷新了token", claims.username);
                    }
                }
            }
        }

        response
    }
}

/// 辅助函数：从请求中提取用户信息
pub fn extract_user_claims(req: &Request) -> Option<&ExtendedClaims> {
    req.extensions().get::<ExtendedClaims>()
}

/// 辅助函数：检查当前用户是否是管理员
pub fn is_current_user_admin(req: &Request) -> bool {
    extract_user_claims(req)
        .and_then(|claims| claims.get_role().ok())
        .map(|role| role.is_admin())
        .unwrap_or(false)
}

/// 辅助函数：获取当前用户ID
pub fn get_current_user_id(req: &Request) -> Option<String> {
    extract_user_claims(req).map(|claims| claims.sub.clone())
}

/// 辅助函数：获取当前用户角色
pub fn get_current_user_role(req: &Request) -> Option<UserRole> {
    extract_user_claims(req).and_then(|claims| claims.get_role().ok())
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{body::Body, http::Request as HttpRequest};

    #[test]
    fn test_auth_middleware_creation() {
        let jwt_manager = JwtManager::new("test-secret".to_string());
        let auth_middleware = AuthMiddleware::new(jwt_manager);
        assert!(auth_middleware.jwt_manager.get_blacklist_stats() == 0);
    }

    #[test]
    fn test_role_based_auth_middleware_creation() {
        let jwt_manager = JwtManager::new("test-secret".to_string());
        let permission_checker = PermissionChecker::new();
        let role_middleware = RoleBasedAuthMiddleware::new(jwt_manager, permission_checker);
        assert!(role_middleware.jwt_manager.get_blacklist_stats() == 0);
    }

    #[test]
    fn test_token_refresh_middleware_creation() {
        let jwt_manager = JwtManager::new("test-secret".to_string());
        let refresh_middleware = TokenRefreshMiddleware::new(jwt_manager, 30);
        assert_eq!(refresh_middleware.refresh_threshold_minutes, 30);
    }

    #[test]
    fn test_helper_functions() {
        let mut req = HttpRequest::builder()
            .uri("/test")
            .body(Body::empty())
            .unwrap();

        // 测试没有用户信息的情况
        assert!(extract_user_claims(&req).is_none());
        assert!(!is_current_user_admin(&req));
        assert!(get_current_user_id(&req).is_none());
        assert!(get_current_user_role(&req).is_none());

        // 添加用户信息
        let claims = ExtendedClaims::new("user123", "testuser", UserRole::Admin, 1);
        req.extensions_mut().insert(claims);

        // 测试有用户信息的情况
        assert!(extract_user_claims(&req).is_some());
        assert!(is_current_user_admin(&req));
        assert_eq!(get_current_user_id(&req).unwrap(), "user123");
        assert_eq!(get_current_user_role(&req).unwrap(), UserRole::Admin);
    }
}
