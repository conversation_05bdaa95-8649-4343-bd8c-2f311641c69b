//! # JWT管理器模块
//!
//! 提供增强的JWT功能，包括：
//! - 支持角色信息的JWT token生成和验证
//! - Token刷新机制
//! - Token黑名单管理
//! - 安全性增强

use super::role_manager::UserRole;
use chrono::{Duration, Utc};
use jsonwebtoken::{Decod<PERSON><PERSON><PERSON>, Enco<PERSON><PERSON><PERSON>, Header, Validation, decode, encode};
use serde::{Deserialize, Serialize};
use std::collections::HashSet;
use std::sync::{Arc, RwLock};

/// 扩展的JWT Claims，包含角色信息
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct ExtendedClaims {
    /// 用户ID (Subject)
    pub sub: String,
    /// 用户名
    pub username: String,
    /// 用户角色
    pub role: String,
    /// 过期时间 (Expiration Time)
    pub exp: i64,
    /// 签发时间 (Issued At)
    pub iat: i64,
    /// 生效时间 (Not Before) - 可选
    pub nbf: Option<i64>,
    /// 签发者 (Issuer) - 可选
    pub iss: Option<String>,
    /// 受众 (Audience) - 可选
    pub aud: Option<String>,
    /// JWT ID - 用于token撤销
    pub jti: Option<String>,
}

impl ExtendedClaims {
    /// 创建新的扩展Claims
    pub fn new(user_id: &str, username: &str, role: UserRole, expires_in_hours: i64) -> Self {
        let now = Utc::now();
        Self {
            sub: user_id.to_string(),
            username: username.to_string(),
            role: role.to_string(),
            iat: now.timestamp(),
            exp: (now + Duration::hours(expires_in_hours)).timestamp(),
            nbf: Some(now.timestamp()),
            iss: Some("axum-tutorial".to_string()),
            aud: Some("axum-tutorial-users".to_string()),
            jti: Some(uuid::Uuid::new_v4().to_string()),
        }
    }

    /// 获取用户角色
    pub fn get_role(&self) -> crate::error::Result<UserRole> {
        match self.role.as_str() {
            "Admin" => Ok(UserRole::Admin),
            "Manager" => Ok(UserRole::Manager),
            "User" => Ok(UserRole::User),
            "Guest" => Ok(UserRole::Guest),
            _ => {
                // 尝试解析自定义角色或使用from_str作为后备
                UserRole::from_str(&self.role.to_lowercase())
            }
        }
    }

    /// 检查token是否即将过期
    pub fn is_expiring_soon(&self, threshold_minutes: i64) -> bool {
        let now = Utc::now().timestamp();
        let threshold = threshold_minutes * 60;
        self.exp - now <= threshold
    }

    /// 检查token是否已过期
    pub fn is_expired(&self) -> bool {
        Utc::now().timestamp() > self.exp
    }

    /// 检查token是否还未生效
    pub fn is_not_yet_valid(&self) -> bool {
        if let Some(nbf) = self.nbf {
            Utc::now().timestamp() < nbf
        } else {
            false
        }
    }

    /// 获取token剩余有效时间（秒）
    pub fn remaining_validity_seconds(&self) -> i64 {
        self.exp - Utc::now().timestamp()
    }
}

/// JWT管理器
#[derive(Debug)]
pub struct JwtManager {
    /// JWT密钥
    secret: String,
    /// Token黑名单
    blacklist: Arc<RwLock<HashSet<String>>>,
    /// 默认过期时间（小时）
    default_expiry_hours: i64,
}

impl JwtManager {
    /// 创建新的JWT管理器
    pub fn new(secret: String) -> Self {
        Self {
            secret,
            blacklist: Arc::new(RwLock::new(HashSet::new())),
            default_expiry_hours: 24,
        }
    }

    /// 设置默认过期时间
    pub fn with_default_expiry(mut self, hours: i64) -> Self {
        self.default_expiry_hours = hours;
        self
    }

    /// 创建包含角色信息的JWT token
    pub fn create_token_with_role(
        &self,
        user_id: &str,
        username: &str,
        role: UserRole,
        expires_in_hours: i64,
    ) -> crate::error::Result<String> {
        let claims = ExtendedClaims::new(user_id, username, role, expires_in_hours);

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.secret.as_ref()),
        )
        .map_err(|e| crate::error::AppError::InternalServerError(format!("JWT token创建失败: {e}")))
    }

    /// 创建默认过期时间的token
    pub fn create_token_default(
        &self,
        user_id: &str,
        username: &str,
        role: UserRole,
    ) -> crate::error::Result<String> {
        self.create_token_with_role(user_id, username, role, self.default_expiry_hours)
    }

    /// 验证JWT token并返回扩展Claims
    pub fn validate_token_with_role(&self, token: &str) -> crate::error::Result<ExtendedClaims> {
        if token.is_empty() {
            return Err(crate::error::AppError::ValidationError(
                "Token不能为空".to_string(),
            ));
        }

        // 检查token是否在黑名单中
        if self.is_token_blacklisted(token) {
            return Err(crate::error::AppError::InvalidToken(
                "Token已被撤销".to_string(),
            ));
        }

        let mut validation = Validation::default();
        validation.validate_aud = false; // 不验证audience字段
        validation.validate_nbf = false; // 不验证not before字段

        match decode::<ExtendedClaims>(
            token,
            &DecodingKey::from_secret(self.secret.as_ref()),
            &validation,
        ) {
            Ok(token_data) => {
                let claims = token_data.claims;

                // 额外的验证
                if claims.is_not_yet_valid() {
                    return Err(crate::error::AppError::InvalidToken(
                        "Token还未生效".to_string(),
                    ));
                }

                Ok(claims)
            }
            Err(err) => match err.kind() {
                jsonwebtoken::errors::ErrorKind::ExpiredSignature => Err(
                    crate::error::AppError::InvalidToken("Token已过期".to_string()),
                ),
                jsonwebtoken::errors::ErrorKind::InvalidToken => Err(
                    crate::error::AppError::InvalidToken("Token格式无效".to_string()),
                ),
                jsonwebtoken::errors::ErrorKind::InvalidSignature => Err(
                    crate::error::AppError::InvalidToken("Token签名无效".to_string()),
                ),
                _ => Err(crate::error::AppError::InvalidToken(format!(
                    "Token验证失败: {err:?}"
                ))),
            },
        }
    }

    /// 刷新token
    pub fn refresh_token(&self, old_token: &str) -> crate::error::Result<String> {
        let claims = self.validate_token_with_role(old_token)?;
        let role = claims.get_role()?;

        // 将旧token加入黑名单
        self.blacklist_token(old_token);

        // 创建新token
        self.create_token_default(&claims.sub, &claims.username, role)
    }

    /// 将token加入黑名单
    pub fn blacklist_token(&self, token: &str) {
        if let Ok(mut blacklist) = self.blacklist.write() {
            blacklist.insert(token.to_string());
        }
    }

    /// 检查token是否在黑名单中
    pub fn is_token_blacklisted(&self, token: &str) -> bool {
        if let Ok(blacklist) = self.blacklist.read() {
            blacklist.contains(token)
        } else {
            false
        }
    }

    /// 清理过期的黑名单token
    pub fn cleanup_expired_blacklist(&self) {
        // 注意：这里需要解析token来检查过期时间
        // 为了简化，我们暂时保留所有黑名单token
        // 在实际应用中，可以定期清理过期的token
    }

    /// 撤销用户的所有token（通过用户ID）
    pub fn revoke_user_tokens(&self, user_id: &str) {
        // 在实际应用中，这里需要维护用户ID到token的映射
        // 或者使用数据库来存储token状态
        tracing::info!("撤销用户 {} 的所有token", user_id);
    }

    /// 获取黑名单统计信息
    pub fn get_blacklist_stats(&self) -> usize {
        if let Ok(blacklist) = self.blacklist.read() {
            blacklist.len()
        } else {
            0
        }
    }

    /// 清空黑名单
    pub fn clear_blacklist(&self) {
        if let Ok(mut blacklist) = self.blacklist.write() {
            blacklist.clear();
        }
    }
}

impl Clone for JwtManager {
    fn clone(&self) -> Self {
        Self {
            secret: self.secret.clone(),
            blacklist: Arc::clone(&self.blacklist),
            default_expiry_hours: self.default_expiry_hours,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    const TEST_SECRET: &str = "test-jwt-manager-secret-key-for-testing-purposes";

    #[test]
    fn test_extended_claims_creation() {
        let claims = ExtendedClaims::new("user123", "testuser", UserRole::User, 1);

        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.username, "testuser");
        assert_eq!(claims.role, "User");
        assert!(claims.jti.is_some());
        assert!(claims.iss.is_some());
        assert!(claims.aud.is_some());
    }

    #[test]
    fn test_jwt_manager_token_operations() {
        let manager = JwtManager::new(TEST_SECRET.to_string());

        // 创建token
        let token = manager.create_token_with_role("user123", "testuser", UserRole::User, 1);
        assert!(token.is_ok());

        // 验证token
        let token = token.unwrap();
        let claims = manager.validate_token_with_role(&token);
        if let Err(ref e) = claims {
            println!("Token validation error: {e:?}");
        }
        assert!(claims.is_ok());

        let claims = claims.unwrap();
        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.username, "testuser");
        assert_eq!(claims.get_role().unwrap(), UserRole::User);
    }

    #[test]
    fn test_token_blacklist() {
        let manager = JwtManager::new(TEST_SECRET.to_string());
        let token = manager
            .create_token_default("user123", "testuser", UserRole::User)
            .unwrap();

        // 验证token有效
        assert!(manager.validate_token_with_role(&token).is_ok());

        // 将token加入黑名单
        manager.blacklist_token(&token);
        assert!(manager.is_token_blacklisted(&token));

        // 验证黑名单token被拒绝
        assert!(manager.validate_token_with_role(&token).is_err());
    }

    #[test]
    fn test_token_refresh() {
        let manager = JwtManager::new(TEST_SECRET.to_string());
        let old_token = manager
            .create_token_default("user123", "testuser", UserRole::User)
            .unwrap();

        // 刷新token
        let new_token = manager.refresh_token(&old_token);
        assert!(new_token.is_ok());

        let new_token = new_token.unwrap();
        assert_ne!(old_token, new_token);

        // 旧token应该被加入黑名单
        assert!(manager.is_token_blacklisted(&old_token));

        // 新token应该有效
        assert!(manager.validate_token_with_role(&new_token).is_ok());
    }

    #[test]
    fn test_claims_expiration_check() {
        let claims = ExtendedClaims::new("user123", "testuser", UserRole::User, 1);

        // 1小时后过期，30分钟阈值，不应该即将过期
        assert!(!claims.is_expiring_soon(30));

        // 1小时后过期，2小时阈值，应该即将过期
        assert!(claims.is_expiring_soon(120));

        // 应该还未过期
        assert!(!claims.is_expired());

        // 应该已经生效
        assert!(!claims.is_not_yet_valid());
    }
}
