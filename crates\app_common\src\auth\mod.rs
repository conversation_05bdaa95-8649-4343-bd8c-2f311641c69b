//! # 认证和权限管理模块
//!
//! 本模块提供完整的JWT权限控制系统，包括：
//! - 用户角色定义和权限级别
//! - JWT token生成和验证（支持角色信息）
//! - 权限检查器
//! - 认证中间件
//! - 权限变更事件处理
//!
//! ## 设计原则
//! - **基于角色的访问控制 (RBAC)**：支持Admin、Manager、User、Guest四种角色
//! - **细粒度权限控制**：支持Read、Write、Delete、Admin四种操作权限
//! - **JWT标准兼容**：遵循JWT标准，支持过期时间、签发时间等标准字段
//! - **安全性优先**：token验证、权限检查、资源访问控制
//! - **可扩展性**：易于添加新角色和权限

pub mod auth_middleware;
pub mod jwt_manager;
pub mod permission_checker;
pub mod role_manager;

// 重新导出核心类型
pub use auth_middleware::{
    AuthMiddleware, RoleBasedAuthMiddleware, TokenRefreshMiddleware, extract_user_claims,
    get_current_user_id, get_current_user_role, is_current_user_admin,
};
pub use jwt_manager::{ExtendedClaims, JwtManager};
pub use permission_checker::{Permission, PermissionChecker};
pub use role_manager::{RoleManager, UserRole};

/// 权限控制系统的核心配置
#[derive(Debug, Clone)]
pub struct AuthConfig {
    /// JWT密钥
    pub jwt_secret: String,
    /// Token默认过期时间（小时）
    pub default_token_expiry_hours: i64,
    /// Token刷新阈值（分钟）
    pub token_refresh_threshold_minutes: i64,
    /// 是否启用权限缓存
    pub enable_permission_cache: bool,
    /// 权限缓存过期时间（秒）
    pub permission_cache_ttl_seconds: u64,
}

impl Default for AuthConfig {
    fn default() -> Self {
        Self {
            jwt_secret: "default-jwt-secret-change-in-production".to_string(),
            default_token_expiry_hours: 24,
            token_refresh_threshold_minutes: 30,
            enable_permission_cache: true,
            permission_cache_ttl_seconds: 300, // 5分钟
        }
    }
}

impl AuthConfig {
    /// 创建新的认证配置
    pub fn new(jwt_secret: String) -> Self {
        Self {
            jwt_secret,
            ..Default::default()
        }
    }

    /// 设置token过期时间
    pub fn with_token_expiry(mut self, hours: i64) -> Self {
        self.default_token_expiry_hours = hours;
        self
    }

    /// 设置token刷新阈值
    pub fn with_refresh_threshold(mut self, minutes: i64) -> Self {
        self.token_refresh_threshold_minutes = minutes;
        self
    }

    /// 启用或禁用权限缓存
    pub fn with_permission_cache(mut self, enabled: bool) -> Self {
        self.enable_permission_cache = enabled;
        self
    }

    /// 设置权限缓存TTL
    pub fn with_cache_ttl(mut self, seconds: u64) -> Self {
        self.permission_cache_ttl_seconds = seconds;
        self
    }

    /// 验证配置的有效性
    pub fn validate(&self) -> crate::error::Result<()> {
        if self.jwt_secret.is_empty() {
            return Err(crate::error::AppError::ValidationError(
                "JWT密钥不能为空".to_string(),
            ));
        }

        if self.jwt_secret.len() < 32 {
            return Err(crate::error::AppError::ValidationError(
                "JWT密钥长度至少需要32个字符".to_string(),
            ));
        }

        if self.default_token_expiry_hours <= 0 {
            return Err(crate::error::AppError::ValidationError(
                "Token过期时间必须大于0".to_string(),
            ));
        }

        if self.token_refresh_threshold_minutes < 0 {
            return Err(crate::error::AppError::ValidationError(
                "Token刷新阈值不能为负数".to_string(),
            ));
        }

        Ok(())
    }
}

/// 权限控制系统的主要入口点
#[derive(Debug, Clone)]
pub struct AuthSystem {
    config: AuthConfig,
    jwt_manager: JwtManager,
    role_manager: RoleManager,
    permission_checker: PermissionChecker,
}

impl AuthSystem {
    /// 创建新的权限控制系统
    pub fn new(config: AuthConfig) -> crate::error::Result<Self> {
        config.validate()?;

        let jwt_manager = JwtManager::new(config.jwt_secret.clone());
        let role_manager = RoleManager::new();
        let permission_checker = PermissionChecker::new();

        Ok(Self {
            config,
            jwt_manager,
            role_manager,
            permission_checker,
        })
    }

    /// 获取JWT管理器
    pub fn jwt_manager(&self) -> &JwtManager {
        &self.jwt_manager
    }

    /// 获取角色管理器
    pub fn role_manager(&self) -> &RoleManager {
        &self.role_manager
    }

    /// 获取权限检查器
    pub fn permission_checker(&self) -> &PermissionChecker {
        &self.permission_checker
    }

    /// 获取配置
    pub fn config(&self) -> &AuthConfig {
        &self.config
    }

    /// 创建用户token
    pub fn create_user_token(
        &self,
        user_id: &str,
        username: &str,
        role: UserRole,
    ) -> crate::error::Result<String> {
        self.jwt_manager.create_token_with_role(
            user_id,
            username,
            role,
            self.config.default_token_expiry_hours,
        )
    }

    /// 验证用户token
    pub fn validate_user_token(&self, token: &str) -> crate::error::Result<ExtendedClaims> {
        self.jwt_manager.validate_token_with_role(token)
    }

    /// 检查用户权限
    pub fn check_permission(&self, role: &UserRole, permission: &Permission) -> bool {
        self.permission_checker.has_permission(role, permission)
    }

    /// 检查资源访问权限
    pub fn check_resource_access(
        &self,
        role: &UserRole,
        resource_owner_id: &str,
        current_user_id: &str,
    ) -> bool {
        self.permission_checker
            .can_access_resource(role, resource_owner_id, current_user_id)
    }

    /// 检查token是否需要刷新
    pub fn should_refresh_token(&self, claims: &ExtendedClaims) -> bool {
        let now = chrono::Utc::now().timestamp();
        let threshold = self.config.token_refresh_threshold_minutes * 60;
        claims.exp - now <= threshold
    }

    /// 刷新用户token
    pub fn refresh_user_token(&self, old_token: &str) -> crate::error::Result<String> {
        let claims = self.validate_user_token(old_token)?;
        let role = claims.get_role()?;

        self.create_user_token(&claims.sub, &claims.username, role)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_auth_config_validation() {
        // 测试有效配置
        let config = AuthConfig::new("a-very-long-jwt-secret-key-for-testing-purposes".to_string());
        assert!(config.validate().is_ok());

        // 测试空密钥
        let config = AuthConfig::new("".to_string());
        assert!(config.validate().is_err());

        // 测试短密钥
        let config = AuthConfig::new("short".to_string());
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_auth_system_creation() {
        let config = AuthConfig::new("a-very-long-jwt-secret-key-for-testing-purposes".to_string());
        let auth_system = AuthSystem::new(config);
        assert!(auth_system.is_ok());
    }

    #[test]
    fn test_auth_system_token_operations() {
        let config = AuthConfig::new("a-very-long-jwt-secret-key-for-testing-purposes".to_string());
        let auth_system = AuthSystem::new(config).unwrap();

        // 创建token
        let token = auth_system.create_user_token("user123", "testuser", UserRole::User);
        assert!(token.is_ok());

        // 验证token
        let token = token.unwrap();
        let claims = auth_system.validate_user_token(&token);
        assert!(claims.is_ok());

        let claims = claims.unwrap();
        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.username, "testuser");
        assert_eq!(claims.get_role().unwrap(), UserRole::User);
    }

    #[test]
    fn test_auth_system_permission_checks() {
        let config = AuthConfig::new("a-very-long-jwt-secret-key-for-testing-purposes".to_string());
        let auth_system = AuthSystem::new(config).unwrap();

        // 测试权限检查
        assert!(auth_system.check_permission(&UserRole::Admin, &Permission::Admin));
        assert!(!auth_system.check_permission(&UserRole::User, &Permission::Admin));

        // 测试资源访问
        assert!(auth_system.check_resource_access(&UserRole::Admin, "other_user", "current_user"));
        assert!(!auth_system.check_resource_access(&UserRole::User, "other_user", "current_user"));
        assert!(auth_system.check_resource_access(&UserRole::User, "current_user", "current_user"));
    }
}
