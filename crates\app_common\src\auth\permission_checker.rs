//! # 权限检查器模块
//!
//! 提供细粒度的权限检查功能，支持：
//! - 基于角色的权限验证
//! - 资源访问控制
//! - 权限级别比较
//! - 权限缓存（可选）

use super::role_manager::UserRole;
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};

/// 权限操作枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum Permission {
    /// 读取权限
    Read,
    /// 写入权限
    Write,
    /// 删除权限
    Delete,
    /// 管理员权限
    Admin,
    /// 自定义权限
    Custom(String),
}

impl Permission {
    /// 获取操作所需的最低权限级别
    pub fn required_level(&self) -> u8 {
        match self {
            Permission::Read => 25,
            Permission::Write => 50,
            Permission::Delete => 75,
            Permission::Admin => 100,
            Permission::Custom(_) => 50, // 自定义权限默认需要写权限级别
        }
    }

    /// 从字符串创建权限
    pub fn from_str(s: &str) -> Self {
        match s.to_lowercase().as_str() {
            "read" => Permission::Read,
            "write" => Permission::Write,
            "delete" => Permission::Delete,
            "admin" => Permission::Admin,
            _ => Permission::Custom(s.to_string()),
        }
    }

    /// 转换为字符串
    pub fn to_string(&self) -> String {
        match self {
            Permission::Read => "read".to_string(),
            Permission::Write => "write".to_string(),
            Permission::Delete => "delete".to_string(),
            Permission::Admin => "admin".to_string(),
            Permission::Custom(name) => name.clone(),
        }
    }
}

/// 权限缓存条目
#[derive(Debug, Clone)]
struct PermissionCacheEntry {
    result: bool,
    expires_at: DateTime<Utc>,
}

impl PermissionCacheEntry {
    fn new(result: bool, ttl_seconds: u64) -> Self {
        Self {
            result,
            expires_at: Utc::now() + chrono::Duration::seconds(ttl_seconds as i64),
        }
    }

    fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }
}

/// 权限检查器
#[derive(Debug)]
pub struct PermissionChecker {
    /// 权限缓存
    cache: Arc<RwLock<HashMap<String, PermissionCacheEntry>>>,
    /// 缓存TTL（秒）
    cache_ttl: u64,
    /// 是否启用缓存
    cache_enabled: bool,
}

impl Default for PermissionChecker {
    fn default() -> Self {
        Self::new()
    }
}

impl PermissionChecker {
    /// 创建新的权限检查器
    pub fn new() -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            cache_ttl: 300, // 5分钟
            cache_enabled: true,
        }
    }

    /// 创建带配置的权限检查器
    pub fn with_config(cache_enabled: bool, cache_ttl: u64) -> Self {
        Self {
            cache: Arc::new(RwLock::new(HashMap::new())),
            cache_ttl,
            cache_enabled,
        }
    }

    /// 检查用户是否有执行指定操作的权限
    pub fn has_permission(&self, user_role: &UserRole, permission: &Permission) -> bool {
        // 如果启用缓存，先检查缓存
        if self.cache_enabled {
            let cache_key = format!("{}:{}", user_role.to_string(), permission.to_string());

            if let Ok(cache) = self.cache.read() {
                if let Some(entry) = cache.get(&cache_key) {
                    if !entry.is_expired() {
                        return entry.result;
                    }
                }
            }
        }

        // 执行实际的权限检查
        let result = user_role.permission_level() >= permission.required_level();

        // 如果启用缓存，存储结果
        if self.cache_enabled {
            let cache_key = format!("{}:{}", user_role.to_string(), permission.to_string());
            let entry = PermissionCacheEntry::new(result, self.cache_ttl);

            if let Ok(mut cache) = self.cache.write() {
                cache.insert(cache_key, entry);
            }
        }

        result
    }

    /// 检查用户是否可以访问指定资源
    pub fn can_access_resource(
        &self,
        user_role: &UserRole,
        resource_owner_id: &str,
        current_user_id: &str,
    ) -> bool {
        // 管理员可以访问所有资源
        if matches!(user_role, UserRole::Admin) {
            return true;
        }

        // 用户只能访问自己的资源
        resource_owner_id == current_user_id
    }

    /// 检查用户是否可以访问指定资源（带权限要求）
    pub fn can_access_resource_with_permission(
        &self,
        user_role: &UserRole,
        permission: &Permission,
        resource_owner_id: &str,
        current_user_id: &str,
    ) -> bool {
        // 首先检查基本权限
        if !self.has_permission(user_role, permission) {
            return false;
        }

        // 然后检查资源访问权限
        self.can_access_resource(user_role, resource_owner_id, current_user_id)
    }

    /// 批量检查权限
    pub fn check_multiple_permissions(
        &self,
        user_role: &UserRole,
        permissions: &[Permission],
    ) -> Vec<bool> {
        permissions
            .iter()
            .map(|permission| self.has_permission(user_role, permission))
            .collect()
    }

    /// 获取用户拥有的所有权限
    pub fn get_user_permissions(&self, user_role: &UserRole) -> Vec<Permission> {
        let all_permissions = vec![
            Permission::Read,
            Permission::Write,
            Permission::Delete,
            Permission::Admin,
        ];

        all_permissions
            .into_iter()
            .filter(|permission| self.has_permission(user_role, permission))
            .collect()
    }

    /// 清理过期的缓存条目
    pub fn cleanup_expired_cache(&self) {
        if !self.cache_enabled {
            return;
        }

        if let Ok(mut cache) = self.cache.write() {
            cache.retain(|_, entry| !entry.is_expired());
        }
    }

    /// 清空权限缓存
    pub fn clear_cache(&self) {
        if let Ok(mut cache) = self.cache.write() {
            cache.clear();
        }
    }

    /// 获取缓存统计信息
    pub fn get_cache_stats(&self) -> (usize, usize) {
        if let Ok(cache) = self.cache.read() {
            let total = cache.len();
            let expired = cache.values().filter(|entry| entry.is_expired()).count();
            (total, expired)
        } else {
            (0, 0)
        }
    }
}

impl Clone for PermissionChecker {
    fn clone(&self) -> Self {
        Self {
            cache: Arc::clone(&self.cache),
            cache_ttl: self.cache_ttl,
            cache_enabled: self.cache_enabled,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_permission_required_levels() {
        assert_eq!(Permission::Read.required_level(), 25);
        assert_eq!(Permission::Write.required_level(), 50);
        assert_eq!(Permission::Delete.required_level(), 75);
        assert_eq!(Permission::Admin.required_level(), 100);
        assert_eq!(Permission::Custom("test".to_string()).required_level(), 50);
    }

    #[test]
    fn test_permission_from_string() {
        assert_eq!(Permission::from_str("read"), Permission::Read);
        assert_eq!(Permission::from_str("WRITE"), Permission::Write);
        assert_eq!(Permission::from_str("Delete"), Permission::Delete);
        assert_eq!(Permission::from_str("admin"), Permission::Admin);
        assert_eq!(
            Permission::from_str("custom"),
            Permission::Custom("custom".to_string())
        );
    }

    #[test]
    fn test_permission_checker_basic() {
        let checker = PermissionChecker::new();

        // 测试管理员权限
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Read));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Write));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Delete));
        assert!(checker.has_permission(&UserRole::Admin, &Permission::Admin));

        // 测试普通用户权限
        assert!(checker.has_permission(&UserRole::User, &Permission::Read));
        assert!(checker.has_permission(&UserRole::User, &Permission::Write));
        assert!(!checker.has_permission(&UserRole::User, &Permission::Delete));
        assert!(!checker.has_permission(&UserRole::User, &Permission::Admin));
    }

    #[test]
    fn test_resource_access() {
        let checker = PermissionChecker::new();
        let user_id = "user123";
        let other_user_id = "user456";

        // 管理员可以访问所有资源
        assert!(checker.can_access_resource(&UserRole::Admin, other_user_id, user_id));

        // 普通用户只能访问自己的资源
        assert!(checker.can_access_resource(&UserRole::User, user_id, user_id));
        assert!(!checker.can_access_resource(&UserRole::User, other_user_id, user_id));
    }

    #[test]
    fn test_multiple_permissions() {
        let checker = PermissionChecker::new();
        let permissions = vec![Permission::Read, Permission::Write, Permission::Delete];

        let admin_results = checker.check_multiple_permissions(&UserRole::Admin, &permissions);
        assert_eq!(admin_results, vec![true, true, true]);

        let user_results = checker.check_multiple_permissions(&UserRole::User, &permissions);
        assert_eq!(user_results, vec![true, true, false]);
    }

    #[test]
    fn test_get_user_permissions() {
        let checker = PermissionChecker::new();

        let admin_permissions = checker.get_user_permissions(&UserRole::Admin);
        assert_eq!(admin_permissions.len(), 4);

        let user_permissions = checker.get_user_permissions(&UserRole::User);
        assert_eq!(user_permissions.len(), 2);
        assert!(user_permissions.contains(&Permission::Read));
        assert!(user_permissions.contains(&Permission::Write));
    }
}
