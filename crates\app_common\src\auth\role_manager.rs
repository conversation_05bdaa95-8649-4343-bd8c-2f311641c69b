//! # 角色管理器模块
//!
//! 提供用户角色管理功能，包括：
//! - 角色定义和权限级别
//! - 角色层次结构
//! - 角色转换和验证
//! - 角色权限映射

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 用户角色枚举
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum UserRole {
    /// 系统管理员 - 最高权限
    Admin,
    /// 部门经理 - 高级权限
    Manager,
    /// 普通用户 - 基础权限
    User,
    /// 访客用户 - 只读权限
    Guest,
    /// 自定义角色
    Custom { name: String, level: u8 },
}

impl UserRole {
    /// 获取角色权限级别（数字越大权限越高）
    pub fn permission_level(&self) -> u8 {
        match self {
            UserRole::Admin => 100,
            UserRole::Manager => 75,
            UserRole::User => 50,
            UserRole::Guest => 25,
            UserRole::Custom { level, .. } => *level,
        }
    }

    /// 获取角色名称
    pub fn name(&self) -> String {
        match self {
            UserRole::Admin => "Admin".to_string(),
            UserRole::Manager => "Manager".to_string(),
            UserRole::User => "User".to_string(),
            UserRole::Guest => "Guest".to_string(),
            UserRole::Custom { name, .. } => name.clone(),
        }
    }

    /// 获取角色描述
    pub fn description(&self) -> String {
        match self {
            UserRole::Admin => "系统管理员，拥有所有权限".to_string(),
            UserRole::Manager => "部门经理，拥有管理权限".to_string(),
            UserRole::User => "普通用户，拥有基础操作权限".to_string(),
            UserRole::Guest => "访客用户，只有只读权限".to_string(),
            UserRole::Custom { name, level } => {
                format!("自定义角色：{name}，权限级别：{level}")
            }
        }
    }

    /// 从字符串创建角色
    pub fn from_str(s: &str) -> crate::error::Result<Self> {
        match s.to_lowercase().as_str() {
            "admin" => Ok(UserRole::Admin),
            "manager" => Ok(UserRole::Manager),
            "user" => Ok(UserRole::User),
            "guest" => Ok(UserRole::Guest),
            _ => Err(crate::error::AppError::ValidationError(format!(
                "无效的用户角色: {s}"
            ))),
        }
    }

    /// 转换为字符串
    pub fn to_string(&self) -> String {
        match self {
            UserRole::Admin => "Admin".to_string(),
            UserRole::Manager => "Manager".to_string(),
            UserRole::User => "User".to_string(),
            UserRole::Guest => "Guest".to_string(),
            UserRole::Custom { name, .. } => name.clone(),
        }
    }

    /// 检查是否可以升级到指定角色
    pub fn can_upgrade_to(&self, target_role: &UserRole) -> bool {
        self.permission_level() < target_role.permission_level()
    }

    /// 检查是否可以降级到指定角色
    pub fn can_downgrade_to(&self, target_role: &UserRole) -> bool {
        self.permission_level() > target_role.permission_level()
    }

    /// 检查是否是管理员角色
    pub fn is_admin(&self) -> bool {
        matches!(self, UserRole::Admin) || self.permission_level() >= 100
    }

    /// 检查是否是管理级别角色（Manager及以上）
    pub fn is_manager_or_above(&self) -> bool {
        self.permission_level() >= 75
    }

    /// 获取所有标准角色
    pub fn all_standard_roles() -> Vec<UserRole> {
        vec![
            UserRole::Admin,
            UserRole::Manager,
            UserRole::User,
            UserRole::Guest,
        ]
    }
}

/// 角色管理器
#[derive(Debug, Clone)]
pub struct RoleManager {
    /// 自定义角色映射
    custom_roles: HashMap<String, UserRole>,
    /// 角色层次结构
    role_hierarchy: HashMap<UserRole, Vec<UserRole>>,
}

impl Default for RoleManager {
    fn default() -> Self {
        Self::new()
    }
}

impl RoleManager {
    /// 创建新的角色管理器
    pub fn new() -> Self {
        let mut manager = Self {
            custom_roles: HashMap::new(),
            role_hierarchy: HashMap::new(),
        };

        // 初始化标准角色层次结构
        manager.init_standard_hierarchy();
        manager
    }

    /// 初始化标准角色层次结构
    fn init_standard_hierarchy(&mut self) {
        // Admin 可以管理所有角色
        self.role_hierarchy.insert(
            UserRole::Admin,
            vec![UserRole::Manager, UserRole::User, UserRole::Guest],
        );

        // Manager 可以管理 User 和 Guest
        self.role_hierarchy
            .insert(UserRole::Manager, vec![UserRole::User, UserRole::Guest]);

        // User 可以管理 Guest
        self.role_hierarchy
            .insert(UserRole::User, vec![UserRole::Guest]);

        // Guest 不能管理任何角色
        self.role_hierarchy.insert(UserRole::Guest, vec![]);
    }

    /// 添加自定义角色
    pub fn add_custom_role(&mut self, name: String, level: u8) -> crate::error::Result<()> {
        if level > 100 {
            return Err(crate::error::AppError::ValidationError(
                "角色权限级别不能超过100".to_string(),
            ));
        }

        let custom_role = UserRole::Custom {
            name: name.clone(),
            level,
        };
        self.custom_roles.insert(name, custom_role);
        Ok(())
    }

    /// 获取自定义角色
    pub fn get_custom_role(&self, name: &str) -> Option<&UserRole> {
        self.custom_roles.get(name)
    }

    /// 移除自定义角色
    pub fn remove_custom_role(&mut self, name: &str) -> bool {
        self.custom_roles.remove(name).is_some()
    }

    /// 获取所有角色
    pub fn get_all_roles(&self) -> Vec<UserRole> {
        let mut roles = UserRole::all_standard_roles();
        roles.extend(self.custom_roles.values().cloned());
        roles
    }

    /// 检查角色是否可以管理另一个角色
    pub fn can_manage_role(&self, manager_role: &UserRole, target_role: &UserRole) -> bool {
        if let Some(manageable_roles) = self.role_hierarchy.get(manager_role) {
            manageable_roles.contains(target_role)
        } else {
            // 对于自定义角色，基于权限级别判断
            manager_role.permission_level() > target_role.permission_level()
        }
    }

    /// 获取角色可以管理的所有角色
    pub fn get_manageable_roles(&self, role: &UserRole) -> Vec<UserRole> {
        if let Some(manageable_roles) = self.role_hierarchy.get(role) {
            manageable_roles.clone()
        } else {
            // 对于自定义角色，返回权限级别更低的所有角色
            self.get_all_roles()
                .into_iter()
                .filter(|r| role.permission_level() > r.permission_level())
                .collect()
        }
    }

    /// 验证角色升级请求
    pub fn validate_role_upgrade(
        &self,
        current_role: &UserRole,
        target_role: &UserRole,
        requester_role: &UserRole,
    ) -> crate::error::Result<()> {
        // 检查请求者是否有权限进行角色变更
        if !self.can_manage_role(requester_role, current_role) {
            return Err(crate::error::AppError::ValidationError(
                "没有权限变更此用户的角色".to_string(),
            ));
        }

        // 检查请求者是否有权限分配目标角色
        if !self.can_manage_role(requester_role, target_role) {
            return Err(crate::error::AppError::ValidationError(
                "没有权限分配此角色".to_string(),
            ));
        }

        // 检查角色升级是否合理
        if !current_role.can_upgrade_to(target_role) {
            return Err(crate::error::AppError::ValidationError(
                "无法升级到更低权限的角色".to_string(),
            ));
        }

        Ok(())
    }

    /// 获取角色统计信息
    pub fn get_role_stats(&self) -> HashMap<String, usize> {
        let mut stats = HashMap::new();
        stats.insert("standard_roles".to_string(), 4);
        stats.insert("custom_roles".to_string(), self.custom_roles.len());
        stats.insert("total_roles".to_string(), 4 + self.custom_roles.len());
        stats
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_role_permission_levels() {
        assert_eq!(UserRole::Admin.permission_level(), 100);
        assert_eq!(UserRole::Manager.permission_level(), 75);
        assert_eq!(UserRole::User.permission_level(), 50);
        assert_eq!(UserRole::Guest.permission_level(), 25);
    }

    #[test]
    fn test_user_role_from_string() {
        assert_eq!(UserRole::from_str("admin").unwrap(), UserRole::Admin);
        assert_eq!(UserRole::from_str("MANAGER").unwrap(), UserRole::Manager);
        assert_eq!(UserRole::from_str("User").unwrap(), UserRole::User);
        assert_eq!(UserRole::from_str("guest").unwrap(), UserRole::Guest);
        assert!(UserRole::from_str("invalid").is_err());
    }

    #[test]
    fn test_role_upgrade_downgrade() {
        assert!(UserRole::User.can_upgrade_to(&UserRole::Admin));
        assert!(!UserRole::Admin.can_upgrade_to(&UserRole::User));
        assert!(UserRole::Admin.can_downgrade_to(&UserRole::User));
        assert!(!UserRole::User.can_downgrade_to(&UserRole::Admin));
    }

    #[test]
    fn test_role_manager_custom_roles() {
        let mut manager = RoleManager::new();

        // 添加自定义角色
        assert!(manager.add_custom_role("Moderator".to_string(), 60).is_ok());
        assert!(manager.get_custom_role("Moderator").is_some());

        // 测试无效权限级别
        assert!(
            manager
                .add_custom_role("SuperAdmin".to_string(), 150)
                .is_err()
        );
    }

    #[test]
    fn test_role_management_hierarchy() {
        let manager = RoleManager::new();

        // Admin 可以管理所有角色
        assert!(manager.can_manage_role(&UserRole::Admin, &UserRole::Manager));
        assert!(manager.can_manage_role(&UserRole::Admin, &UserRole::User));
        assert!(manager.can_manage_role(&UserRole::Admin, &UserRole::Guest));

        // Manager 可以管理 User 和 Guest
        assert!(manager.can_manage_role(&UserRole::Manager, &UserRole::User));
        assert!(manager.can_manage_role(&UserRole::Manager, &UserRole::Guest));
        assert!(!manager.can_manage_role(&UserRole::Manager, &UserRole::Admin));

        // User 只能管理 Guest
        assert!(manager.can_manage_role(&UserRole::User, &UserRole::Guest));
        assert!(!manager.can_manage_role(&UserRole::User, &UserRole::Manager));

        // Guest 不能管理任何角色
        assert!(!manager.can_manage_role(&UserRole::Guest, &UserRole::User));
    }

    #[test]
    fn test_role_upgrade_validation() {
        let manager = RoleManager::new();

        // 有效的角色升级
        let result =
            manager.validate_role_upgrade(&UserRole::User, &UserRole::Manager, &UserRole::Admin);
        assert!(result.is_ok());

        // 无权限的角色升级
        let result =
            manager.validate_role_upgrade(&UserRole::Manager, &UserRole::Admin, &UserRole::User);
        assert!(result.is_err());
    }
}
