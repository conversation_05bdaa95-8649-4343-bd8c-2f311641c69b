//! # 兼容性检查器
//!
//! 核心兼容性检查逻辑实现

use app_interfaces::versioning::{ApiChange, ApiVersion, ChangeType, CompatibilityCheck};
use serde::{Deserialize, Serialize};
use tracing::{info, instrument, warn};

/// 兼容性检查器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompatibilityConfig {
    /// 是否允许添加新字段
    pub allow_new_fields: bool,
    /// 是否允许添加新端点
    pub allow_new_endpoints: bool,
    /// 是否允许修改可选字段
    pub allow_optional_field_changes: bool,
    /// 严格模式（更严格的兼容性检查）
    pub strict_mode: bool,
    /// 忽略的端点列表
    pub ignored_endpoints: Vec<String>,
    /// 忽略的字段列表
    pub ignored_fields: Vec<String>,
}

impl Default for CompatibilityConfig {
    fn default() -> Self {
        Self {
            allow_new_fields: true,
            allow_new_endpoints: true,
            allow_optional_field_changes: true,
            strict_mode: false,
            ignored_endpoints: Vec::new(),
            ignored_fields: Vec::new(),
        }
    }
}

/// 兼容性检查器
#[derive(Debug)]
pub struct CompatibilityChecker {
    /// 检查配置
    config: CompatibilityConfig,
}

impl CompatibilityChecker {
    /// 创建新的兼容性检查器
    pub fn new(config: CompatibilityConfig) -> Self {
        Self { config }
    }

    /// 创建默认的兼容性检查器
    pub fn default() -> Self {
        Self::new(CompatibilityConfig::default())
    }

    /// 检查API变更的兼容性
    #[instrument(skip(self, changes), fields(change_count = changes.len()))]
    pub fn check_compatibility(&self, changes: &[ApiChange]) -> CompatibilityCheck {
        info!("开始兼容性检查，变更数量: {}", changes.len());

        let mut breaking_changes = Vec::new();
        let mut warnings = Vec::new();
        let mut migration_suggestions = Vec::new();

        for change in changes {
            if self.should_ignore_change(change) {
                continue;
            }

            match self.analyze_change(change) {
                ChangeAnalysis::Breaking(reason) => {
                    warn!("发现破坏性变更: {} - {}", change.description, reason);
                    breaking_changes.push(change.clone());

                    if let Some(suggestion) = self.generate_migration_suggestion(change) {
                        migration_suggestions.push(suggestion);
                    }
                }
                ChangeAnalysis::Warning(reason) => {
                    warn!("发现兼容性警告: {} - {}", change.description, reason);
                    warnings.push(reason);
                }
                ChangeAnalysis::Safe => {
                    info!("安全变更: {}", change.description);
                }
            }
        }

        let is_compatible = breaking_changes.is_empty();

        info!(
            "兼容性检查完成: 兼容={}, 破坏性变更={}, 警告={}",
            is_compatible,
            breaking_changes.len(),
            warnings.len()
        );

        CompatibilityCheck {
            is_compatible,
            breaking_changes,
            warnings,
            migration_suggestions,
        }
    }

    /// 分析单个变更
    fn analyze_change(&self, change: &ApiChange) -> ChangeAnalysis {
        match &change.change_type {
            ChangeType::AddField => {
                if self.config.allow_new_fields {
                    ChangeAnalysis::Safe
                } else {
                    ChangeAnalysis::Warning("添加新字段可能影响严格的客户端".to_string())
                }
            }
            ChangeType::AddEndpoint => {
                if self.config.allow_new_endpoints {
                    ChangeAnalysis::Safe
                } else {
                    ChangeAnalysis::Warning("添加新端点".to_string())
                }
            }
            ChangeType::ModifyFieldType => {
                ChangeAnalysis::Breaking("修改字段类型会破坏现有客户端".to_string())
            }
            ChangeType::RemoveField => {
                ChangeAnalysis::Breaking("删除字段会导致客户端错误".to_string())
            }
            ChangeType::RemoveEndpoint => {
                ChangeAnalysis::Breaking("删除端点会导致客户端请求失败".to_string())
            }
            ChangeType::ModifyEndpointBehavior => {
                if self.config.strict_mode {
                    ChangeAnalysis::Breaking("修改端点行为可能破坏客户端预期".to_string())
                } else {
                    ChangeAnalysis::Warning("端点行为变更需要仔细验证".to_string())
                }
            }
            ChangeType::ModifyErrorFormat => {
                ChangeAnalysis::Breaking("修改错误响应格式会影响错误处理逻辑".to_string())
            }
        }
    }

    /// 检查是否应该忽略某个变更
    fn should_ignore_change(&self, change: &ApiChange) -> bool {
        // 检查是否在忽略的端点列表中
        for ignored_endpoint in &self.config.ignored_endpoints {
            if change
                .affected_endpoints
                .iter()
                .any(|ep| ep.contains(ignored_endpoint))
            {
                return true;
            }
        }

        // 检查是否在忽略的字段列表中
        for ignored_field in &self.config.ignored_fields {
            if change.description.contains(ignored_field) {
                return true;
            }
        }

        false
    }

    /// 生成迁移建议
    fn generate_migration_suggestion(&self, change: &ApiChange) -> Option<String> {
        match &change.change_type {
            ChangeType::RemoveField => Some(format!(
                "在删除字段前，先将其标记为弃用，并在文档中说明替代方案。建议的迁移步骤：\n\
                1. 在版本 {} 中标记字段为弃用\n\
                2. 提供至少6个月的过渡期\n\
                3. 在主版本升级时删除字段",
                change.version
            )),
            ChangeType::RemoveEndpoint => Some(format!(
                "在删除端点前，先返回410 Gone状态码，并在响应中提供替代端点信息。建议的迁移步骤：\n\
                1. 在版本 {} 中标记端点为弃用\n\
                2. 返回Deprecation警告头\n\
                3. 在主版本升级时删除端点",
                change.version
            )),
            ChangeType::ModifyFieldType => Some(format!(
                "字段类型变更需要谨慎处理。建议的迁移步骤：\n\
                1. 创建新字段，保留旧字段\n\
                2. 在版本 {} 中同时支持两个字段\n\
                3. 标记旧字段为弃用\n\
                4. 在主版本升级时删除旧字段",
                change.version
            )),
            ChangeType::ModifyErrorFormat => Some(
                "错误格式变更影响客户端错误处理。建议：\n\
                1. 保持向后兼容的错误格式\n\
                2. 通过版本协商提供新格式\n\
                3. 在文档中详细说明变更"
                    .to_string(),
            ),
            _ => None,
        }
    }

    /// 检查两个API版本之间的兼容性
    pub fn check_version_compatibility(
        &self,
        from_version: &ApiVersion,
        to_version: &ApiVersion,
    ) -> bool {
        // 主版本号相同才可能兼容
        if from_version.major != to_version.major {
            return false;
        }

        // 次版本号向后兼容
        to_version.minor >= from_version.minor
    }

    /// 生成兼容性摘要
    pub fn generate_compatibility_summary(&self, check: &CompatibilityCheck) -> String {
        let mut summary = String::new();

        if check.is_compatible {
            summary.push_str("✅ API变更向后兼容\n");
        } else {
            summary.push_str("❌ 发现破坏性变更\n");
        }

        if !check.breaking_changes.is_empty() {
            summary.push_str(&format!(
                "\n🚨 破坏性变更 ({}):\n",
                check.breaking_changes.len()
            ));
            for change in &check.breaking_changes {
                summary.push_str(&format!("  - {}\n", change.description));
            }
        }

        if !check.warnings.is_empty() {
            summary.push_str(&format!("\n⚠️ 兼容性警告 ({}):\n", check.warnings.len()));
            for warning in &check.warnings {
                summary.push_str(&format!("  - {warning}\n"));
            }
        }

        if !check.migration_suggestions.is_empty() {
            summary.push_str(&format!(
                "\n💡 迁移建议 ({}):\n",
                check.migration_suggestions.len()
            ));
            for suggestion in &check.migration_suggestions {
                summary.push_str(&format!("  - {suggestion}\n"));
            }
        }

        summary
    }
}

/// 变更分析结果
#[derive(Debug, Clone)]
enum ChangeAnalysis {
    /// 安全变更
    Safe,
    /// 警告（可能影响兼容性）
    Warning(String),
    /// 破坏性变更
    Breaking(String),
}

/// 兼容性检查结果统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompatibilityStats {
    /// 总变更数
    pub total_changes: usize,
    /// 安全变更数
    pub safe_changes: usize,
    /// 警告数
    pub warnings: usize,
    /// 破坏性变更数
    pub breaking_changes: usize,
    /// 兼容性得分 (0-100)
    pub compatibility_score: f64,
}

impl CompatibilityStats {
    /// 从兼容性检查结果计算统计信息
    pub fn from_check(check: &CompatibilityCheck, total_changes: usize) -> Self {
        let breaking_changes = check.breaking_changes.len();
        let warnings = check.warnings.len();
        let safe_changes = total_changes.saturating_sub(breaking_changes + warnings);

        // 计算兼容性得分
        let compatibility_score = if total_changes == 0 {
            100.0
        } else {
            let penalty = (breaking_changes as f64 * 10.0) + (warnings as f64 * 2.0);
            let max_score = total_changes as f64 * 10.0;
            ((max_score - penalty) / max_score * 100.0).max(0.0)
        };

        Self {
            total_changes,
            safe_changes,
            warnings,
            breaking_changes,
            compatibility_score,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;

    #[test]
    fn test_safe_change_analysis() {
        let checker = CompatibilityChecker::default();
        let change = ApiChange {
            change_type: ChangeType::AddField,
            description: "添加新的可选字段".to_string(),
            affected_endpoints: vec!["/api/users".to_string()],
            version: ApiVersion::new(1, 1, 0),
            timestamp: Utc::now(),
            migration_guide: None,
        };

        let result = checker.analyze_change(&change);
        matches!(result, ChangeAnalysis::Safe);
    }

    #[test]
    fn test_breaking_change_analysis() {
        let checker = CompatibilityChecker::default();
        let change = ApiChange {
            change_type: ChangeType::RemoveField,
            description: "删除用户ID字段".to_string(),
            affected_endpoints: vec!["/api/users".to_string()],
            version: ApiVersion::new(2, 0, 0),
            timestamp: Utc::now(),
            migration_guide: None,
        };

        let result = checker.analyze_change(&change);
        matches!(result, ChangeAnalysis::Breaking(_));
    }

    #[test]
    fn test_version_compatibility() {
        let checker = CompatibilityChecker::default();

        let v1_0_0 = ApiVersion::new(1, 0, 0);
        let v1_1_0 = ApiVersion::new(1, 1, 0);
        let v2_0_0 = ApiVersion::new(2, 0, 0);

        assert!(checker.check_version_compatibility(&v1_0_0, &v1_1_0));
        assert!(!checker.check_version_compatibility(&v1_1_0, &v1_0_0));
        assert!(!checker.check_version_compatibility(&v1_0_0, &v2_0_0));
    }
}
