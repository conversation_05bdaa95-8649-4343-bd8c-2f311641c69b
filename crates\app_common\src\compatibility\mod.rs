//! # API兼容性检查模块
//!
//! 提供API向后兼容性检查和验证工具
//!
//! ## 核心功能
//! - OpenAPI规范兼容性检查
//! - 数据结构变更检测
//! - 破坏性变更识别
//! - 兼容性报告生成
//! - CI/CD集成支持

pub mod checker;
pub mod reporter;
pub mod schema_diff;

// 重新导出常用类型
pub use checker::{CompatibilityChecker, CompatibilityConfig};
pub use reporter::{CompatibilityReport, CompatibilityReporter, ReportFormat};
pub use schema_diff::{ChangeImpact, SchemaChange, SchemaDiff};
