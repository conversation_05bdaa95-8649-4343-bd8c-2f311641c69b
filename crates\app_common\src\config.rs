//! # 配置管理模块
//!
//! 提供统一的配置加载和管理机制
//!
//! 这个模块实现了企业级的配置管理系统，包括：
//! - 环境变量配置加载
//! - 配置文件支持
//! - 配置验证和错误处理
//! - 多环境配置支持

use serde::{Deserialize, Serialize};
use std::{net::SocketAddr, time::Duration};
use thiserror::Error;

/// 配置错误类型
#[derive(Error, Debug)]
pub enum ConfigError {
    /// 环境变量解析错误
    #[error(
        "环境变量 '{var_name}' 解析失败: 值 '{value}' 无法解析为 {expected_type}. 建议: {suggestion}"
    )]
    EnvVarParseError {
        var_name: String,
        value: String,
        expected_type: String,
        suggestion: String,
    },

    /// 配置验证错误
    #[error(
        "配置字段 '{field}' 验证失败: 值 '{value}' 不符合要求. 原因: {reason}. 建议: {suggestion}"
    )]
    ValidationError {
        field: String,
        value: String,
        reason: String,
        suggestion: String,
    },

    /// 配置文件错误
    #[error("配置文件错误: {message}")]
    ConfigFileError { message: String },

    /// 生产环境安全检查错误
    #[error("生产环境安全检查失败: {message}")]
    ProductionSecurityError { message: String },
}

/// 配置结果类型别名
pub type ConfigResult<T> = Result<T, ConfigError>;

/// 数据库连接池配置结构体
///
/// 【目的】: 专门用于配置SeaORM数据库连接池的参数，支持百万并发连接优化
/// 【设计】: 基于SeaORM ConnectOptions的最佳实践配置
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct DatabasePoolConfig {
    /// 最大连接数 - 支持百万并发的关键参数
    pub max_connections: u32,
    /// 最小连接数 - 保持基础连接池大小
    pub min_connections: u32,
    /// 连接超时时间 - 防止连接建立过慢
    #[serde(with = "duration_serde")]
    pub connect_timeout: Duration,
    /// 空闲超时时间 - 自动清理空闲连接
    #[serde(with = "duration_serde")]
    pub idle_timeout: Duration,
    /// 连接最大生命周期 - 防止连接过期
    #[serde(with = "option_duration_serde")]
    pub max_lifetime: Option<Duration>,
    /// 获取连接超时时间 - 防止应用阻塞
    #[serde(with = "duration_serde")]
    pub acquire_timeout: Duration,
    /// 启用TCP_NODELAY - 减少网络延迟
    pub tcp_nodelay: bool,
    /// 启用连接保活 - 检测断开的连接
    pub tcp_keepalive: bool,
}

impl DatabasePoolConfig {
    /// 创建生产环境优化的数据库连接池配置
    ///
    /// 【功能】: 为百万并发场景优化的数据库连接池配置
    /// 【参数】: 基于SeaORM最佳实践和企业级应用需求
    pub fn production() -> Self {
        Self {
            max_connections: 100,                          // 支持高并发的连接数
            min_connections: 10,                           // 保持基础连接池
            connect_timeout: Duration::from_secs(30),      // 连接建立超时
            idle_timeout: Duration::from_secs(600),        // 10分钟空闲超时
            max_lifetime: Some(Duration::from_secs(3600)), // 1小时最大生命周期
            acquire_timeout: Duration::from_secs(10),      // 获取连接超时
            tcp_nodelay: true,                             // 启用TCP_NODELAY减少延迟
            tcp_keepalive: true,                           // 启用TCP保活检测
        }
    }

    /// 创建开发环境的数据库连接池配置
    ///
    /// 【功能】: 为开发环境优化的数据库连接池配置
    /// 【特点】: 较小的连接池，适合本地开发和测试
    pub fn development() -> Self {
        Self {
            max_connections: 20,                           // 开发环境较小连接数
            min_connections: 2,                            // 最小连接数
            connect_timeout: Duration::from_secs(10),      // 较短连接超时
            idle_timeout: Duration::from_secs(300),        // 5分钟空闲超时
            max_lifetime: Some(Duration::from_secs(1800)), // 30分钟最大生命周期
            acquire_timeout: Duration::from_secs(5),       // 获取连接超时
            tcp_nodelay: true,                             // 启用TCP_NODELAY
            tcp_keepalive: false,                          // 开发环境关闭保活
        }
    }

    /// 验证配置参数的有效性
    ///
    /// 【功能】: 检查配置参数是否合理，防止配置错误
    /// 【返回】: 配置有效返回Ok(())，否则返回错误信息
    pub fn validate(&self) -> ConfigResult<()> {
        if self.max_connections == 0 {
            return Err(ConfigError::ValidationError {
                field: "max_connections".to_string(),
                value: self.max_connections.to_string(),
                reason: "最大连接数不能为0".to_string(),
                suggestion: "设置一个大于0的值，建议生产环境使用100，开发环境使用20".to_string(),
            });
        }

        if self.min_connections > self.max_connections {
            return Err(ConfigError::ValidationError {
                field: "min_connections".to_string(),
                value: format!(
                    "min: {}, max: {}",
                    self.min_connections, self.max_connections
                ),
                reason: "最小连接数不能大于最大连接数".to_string(),
                suggestion: "确保最小连接数小于或等于最大连接数".to_string(),
            });
        }

        if self.connect_timeout.as_secs() == 0 {
            return Err(ConfigError::ValidationError {
                field: "connect_timeout".to_string(),
                value: format!("{}s", self.connect_timeout.as_secs()),
                reason: "连接超时时间不能为0".to_string(),
                suggestion: "设置合理的超时时间，建议5-30秒".to_string(),
            });
        }

        if self.acquire_timeout.as_secs() == 0 {
            return Err(ConfigError::ValidationError {
                field: "acquire_timeout".to_string(),
                value: format!("{}s", self.acquire_timeout.as_secs()),
                reason: "获取连接超时时间不能为0".to_string(),
                suggestion: "设置合理的超时时间，建议5-10秒".to_string(),
            });
        }

        Ok(())
    }
}

/// 服务器配置结构体
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct ServerConfig {
    /// HTTP 服务器监听地址
    pub http_addr: SocketAddr,
    /// 请求超时时间
    #[serde(with = "duration_serde")]
    pub request_timeout: Duration,
    /// 最大请求体大小（字节）
    pub max_request_size: usize,
    /// 启用CORS
    pub enable_cors: bool,
}

impl ServerConfig {
    /// 创建生产环境服务器配置
    pub fn production() -> Self {
        Self {
            http_addr: "0.0.0.0:3000".parse().unwrap(),
            request_timeout: Duration::from_secs(30),
            max_request_size: 1024 * 1024, // 1MB
            enable_cors: false,
        }
    }

    /// 创建开发环境服务器配置
    pub fn development() -> Self {
        Self {
            http_addr: "127.0.0.1:3000".parse().unwrap(),
            request_timeout: Duration::from_secs(60),
            max_request_size: 10 * 1024 * 1024, // 10MB
            enable_cors: true,
        }
    }
}

/// 应用程序配置结构体
///
/// 【目的】: 集中存储应用程序运行所需的所有配置参数
/// 【设计】: 结构体的字段是公开的，允许其他模块直接访问
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct AppConfig {
    /// 服务器配置
    pub server: ServerConfig,
    /// 数据库连接 URL
    pub database_url: String,
    /// 数据库连接池配置
    pub database_pool: DatabasePoolConfig,
    /// JWT 签名密钥
    pub jwt_secret: String,
    /// 运行环境
    pub environment: String,
}

impl AppConfig {
    /// 从环境变量加载配置，提供默认值
    ///
    /// 【功能】: 这是创建 `AppConfig` 实例的主要方式。
    ///          它尝试从预定义的环境变量中读取配置值。
    ///          如果某个环境变量未设置，则使用硬编码的默认值。
    /// 【健壮性】: 使用Result类型进行优雅的错误处理，避免panic。
    pub fn from_env() -> ConfigResult<Self> {
        Self::from_env_with_dotenv(true)
    }

    /// 从环境变量加载配置，可选择是否加载.env文件
    ///
    /// 【功能】: 内部方法，允许控制是否加载.env文件
    /// 【参数】: load_dotenv - 是否加载.env文件
    pub fn from_env_with_dotenv(load_dotenv: bool) -> ConfigResult<Self> {
        println!("CONFIG: 正在从环境变量加载配置...");

        // 可选择性加载环境变量文件
        if load_dotenv {
            dotenvy::dotenv().ok();
        }

        // 检测运行环境
        let environment =
            std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string());

        let is_production = environment.to_lowercase() == "production";
        println!("  - 运行环境: {environment}");

        // 加载服务器配置
        let server = if is_production {
            ServerConfig::production()
        } else {
            ServerConfig::development()
        };

        // 如果设置了HTTP_ADDR环境变量，覆盖默认地址
        let mut server = server;
        if let Ok(http_addr_str) = std::env::var("HTTP_ADDR") {
            let http_addr: SocketAddr =
                http_addr_str
                    .parse()
                    .map_err(|_| ConfigError::EnvVarParseError {
                        var_name: "HTTP_ADDR".to_string(),
                        value: http_addr_str.clone(),
                        expected_type: "SocketAddr (例如: 127.0.0.1:3000)".to_string(),
                        suggestion:
                            "请确保格式为 'IP地址:端口号'，例如 '127.0.0.1:3000' 或 '0.0.0.0:8080'"
                                .to_string(),
                    })?;
            server.http_addr = http_addr;
            println!("  - HTTP 地址: {http_addr} (从环境变量)");
        } else {
            println!("  - HTTP 地址: {} (默认)", server.http_addr);
        }

        // 加载数据库连接 URL
        let database_url = std::env::var("DATABASE_URL")
            .unwrap_or_else(|_| "sqlite:task_manager.db?mode=rwc".to_string());

        // 验证数据库URL格式
        if database_url.is_empty() {
            return Err(ConfigError::ValidationError {
                field: "database_url".to_string(),
                value: database_url,
                reason: "数据库URL不能为空".to_string(),
                suggestion: "设置有效的数据库URL，例如 'sqlite:app.db' 或 'postgresql://user:pass@localhost/db'".to_string(),
            });
        }
        println!("  - 数据库 URL: {database_url}");

        // 加载 JWT 密钥
        let jwt_secret = std::env::var("JWT_SECRET")
            .unwrap_or_else(|_| "your-secret-key-change-in-production".to_string());
        println!("  - JWT 密钥: [已设置]"); // 不打印实际密钥以保证安全

        // 加载连接池配置
        let database_pool = if is_production {
            println!("  - 数据库连接池: 生产环境配置 (最大连接数: 100)");
            DatabasePoolConfig::production()
        } else {
            println!("  - 数据库连接池: 开发环境配置 (最大连接数: 20)");
            DatabasePoolConfig::development()
        };

        // 验证配置
        database_pool.validate()?;

        // 生产环境安全检查
        if is_production {
            Self::validate_production_security(&jwt_secret, &database_url)?;
        }

        println!("CONFIG: 配置加载完成。");

        Ok(Self {
            server,
            database_url,
            database_pool,
            jwt_secret,
            environment,
        })
    }

    /// 生产环境安全检查
    fn validate_production_security(jwt_secret: &str, database_url: &str) -> ConfigResult<()> {
        // 检查JWT密钥强度
        if jwt_secret == "your-secret-key-change-in-production" || jwt_secret.len() < 32 {
            return Err(ConfigError::ProductionSecurityError {
                message: "生产环境必须使用强JWT密钥（至少32字符）".to_string(),
            });
        }

        // 检查数据库URL安全性
        if database_url.starts_with("sqlite:") {
            return Err(ConfigError::ProductionSecurityError {
                message: "生产环境不建议使用SQLite数据库".to_string(),
            });
        }

        Ok(())
    }
}

// Duration序列化辅助模块
mod duration_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use std::time::Duration;

    pub fn serialize<S>(duration: &Duration, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        duration.as_secs().serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Duration, D::Error>
    where
        D: Deserializer<'de>,
    {
        let secs = u64::deserialize(deserializer)?;
        Ok(Duration::from_secs(secs))
    }
}

// Option<Duration>序列化辅助模块
mod option_duration_serde {
    use serde::{Deserialize, Deserializer, Serialize, Serializer};
    use std::time::Duration;

    pub fn serialize<S>(duration: &Option<Duration>, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        match duration {
            Some(d) => Some(d.as_secs()).serialize(serializer),
            None => None::<u64>.serialize(serializer),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<Duration>, D::Error>
    where
        D: Deserializer<'de>,
    {
        let secs = Option::<u64>::deserialize(deserializer)?;
        Ok(secs.map(Duration::from_secs))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::env;

    #[test]
    fn test_database_pool_config_production() {
        let config = DatabasePoolConfig::production();
        assert_eq!(config.max_connections, 100);
        assert_eq!(config.min_connections, 10);
        assert!(config.tcp_nodelay);
        assert!(config.tcp_keepalive);
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_database_pool_config_development() {
        let config = DatabasePoolConfig::development();
        assert_eq!(config.max_connections, 20);
        assert_eq!(config.min_connections, 2);
        assert!(config.tcp_nodelay);
        assert!(!config.tcp_keepalive);
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_database_pool_config_validation() {
        let mut config = DatabasePoolConfig::development();

        // 测试最大连接数为0的情况
        config.max_connections = 0;
        assert!(config.validate().is_err());

        // 测试最小连接数大于最大连接数的情况
        config.max_connections = 10;
        config.min_connections = 20;
        assert!(config.validate().is_err());

        // 测试连接超时为0的情况
        config.min_connections = 5;
        config.connect_timeout = Duration::from_secs(0);
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_server_config_production() {
        let config = ServerConfig::production();
        assert_eq!(config.http_addr.to_string(), "0.0.0.0:3000");
        assert!(!config.enable_cors);
        assert_eq!(config.max_request_size, 1024 * 1024);
    }

    #[test]
    fn test_server_config_development() {
        let config = ServerConfig::development();
        assert_eq!(config.http_addr.to_string(), "127.0.0.1:3000");
        assert!(config.enable_cors);
        assert_eq!(config.max_request_size, 10 * 1024 * 1024);
    }

    #[test]
    #[serial_test::serial]
    fn test_app_config_from_env_development() {
        // 保存原始环境变量
        let original_env = env::var("ENVIRONMENT").ok();
        let original_http = env::var("HTTP_ADDR").ok();
        let original_db = env::var("DATABASE_URL").ok();
        let original_jwt = env::var("JWT_SECRET").ok();

        // 清理并设置测试环境变量
        unsafe {
            env::remove_var("HTTP_ADDR");
            env::remove_var("DATABASE_URL");
            env::remove_var("JWT_SECRET");
            env::set_var("ENVIRONMENT", "development");
        }

        let config = AppConfig::from_env().unwrap();
        assert_eq!(config.environment, "development");
        assert_eq!(config.server.http_addr.to_string(), "127.0.0.1:3000");
        assert_eq!(config.database_pool.max_connections, 20);
        assert!(config.server.enable_cors);

        // 恢复原始环境变量
        unsafe {
            if let Some(env_val) = original_env {
                env::set_var("ENVIRONMENT", env_val);
            } else {
                env::remove_var("ENVIRONMENT");
            }
            if let Some(http_val) = original_http {
                env::set_var("HTTP_ADDR", http_val);
            }
            if let Some(db_val) = original_db {
                env::set_var("DATABASE_URL", db_val);
            }
            if let Some(jwt_val) = original_jwt {
                env::set_var("JWT_SECRET", jwt_val);
            }
        }
    }

    #[test]
    #[serial_test::serial]
    fn test_app_config_with_env_vars() {
        // 保存原始环境变量
        let original_env = env::var("ENVIRONMENT").ok();
        let original_http = env::var("HTTP_ADDR").ok();
        let original_db = env::var("DATABASE_URL").ok();
        let original_jwt = env::var("JWT_SECRET").ok();

        // 设置测试环境变量
        unsafe {
            env::set_var("ENVIRONMENT", "development");
            env::set_var("HTTP_ADDR", "0.0.0.0:8080");
            env::set_var("DATABASE_URL", "postgresql://user:pass@localhost/testdb");
            env::set_var("JWT_SECRET", "test-secret-key-for-development-only");
        }

        let config = AppConfig::from_env().unwrap();
        assert_eq!(
            config.database_url,
            "postgresql://user:pass@localhost/testdb"
        );
        assert_eq!(config.jwt_secret, "test-secret-key-for-development-only");
        assert_eq!(config.server.http_addr.to_string(), "0.0.0.0:8080");

        // 恢复原始环境变量
        unsafe {
            if let Some(env_val) = original_env {
                env::set_var("ENVIRONMENT", env_val);
            } else {
                env::remove_var("ENVIRONMENT");
            }
            if let Some(http_val) = original_http {
                env::set_var("HTTP_ADDR", http_val);
            } else {
                env::remove_var("HTTP_ADDR");
            }
            if let Some(db_val) = original_db {
                env::set_var("DATABASE_URL", db_val);
            } else {
                env::remove_var("DATABASE_URL");
            }
            if let Some(jwt_val) = original_jwt {
                env::set_var("JWT_SECRET", jwt_val);
            } else {
                env::remove_var("JWT_SECRET");
            }
        }
    }

    #[test]
    fn test_config_serialization() {
        let config = DatabasePoolConfig::development();
        let json = serde_json::to_string(&config).unwrap();
        let deserialized: DatabasePoolConfig = serde_json::from_str(&json).unwrap();

        assert_eq!(config.max_connections, deserialized.max_connections);
        assert_eq!(config.min_connections, deserialized.min_connections);
        assert_eq!(config.tcp_nodelay, deserialized.tcp_nodelay);
    }
}
