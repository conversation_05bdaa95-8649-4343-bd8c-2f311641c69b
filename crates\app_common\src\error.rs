//! # 错误处理模块
//!
//! 提供统一的错误类型和处理机制
//!
//! 这个模块是应用程序的【错误处理中心】。
//! 它定义了一个统一的错误枚举 `AppError`，封装了应用中可能出现的各种预期错误。
//! 最关键的是，它为 `AppError` 实现了 Axum 的 `IntoResponse` trait，
//! 这使得任何返回 `Result<_, AppError>` 的 Axum Handler 都能在出错时，
//! 【自动地】将 `AppError` 转换为格式良好、带有合适状态码的 HTTP 错误响应。
//!
//! ## 错误处理最佳实践
//!
//! 基于 Context7 MCP 最佳实践，本模块实现了：
//! - 使用 `thiserror` 进行结构化错误定义
//! - 使用 `anyhow` 进行错误上下文传播
//! - 统一的错误响应格式
//! - 详细的错误日志记录
//! - 错误链追踪和调试支持

use crate::utils::error_response::ErrorHandler;
use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
};
use thiserror::Error;
use tracing_error::{ExtractSpanTrace, SpanTrace};
use validator::ValidationErrors;

/// 应用程序统一错误枚举 (`AppError`)
///
/// 【用途】: 定义了应用程序中所有【可预期的】业务逻辑错误或请求处理错误。
///          每种错误变体 (variant) 代表一种特定的失败场景。
/// 【设计】: 使用 `thiserror` 进行结构化错误定义，提供清晰的错误消息和自动的错误链支持。
/// 【目标】: 提供比标准库 `Error` trait 更具体的错误类型，并方便地映射到 HTTP 状态码。
#[derive(Error, Debug)]
pub enum AppError {
    /// 404 Not Found - 表示请求的任务资源未能找到
    #[error("未找到ID为 {0} 的任务")]
    TaskNotFound(uuid::Uuid),

    /// 404 Not Found - 通用资源未找到错误
    #[error("资源未找到: {0}")]
    NotFound(String),

    /// 403 Forbidden - 权限不足错误
    #[error("权限不足: {0}")]
    Forbidden(String),

    /// 400 Bad Request - 表示客户端发送的请求无效
    #[error("请求错误: {0}")]
    BadRequest(String),

    /// 500 Internal Server Error - 数据库错误
    #[error("数据库错误: {0}")]
    DatabaseError(String),

    /// 409 Conflict - 用户名已存在（注册时）
    #[error("用户名 '{0}' 已存在")]
    UserAlreadyExists(String),

    /// 401 Unauthorized - 无效的登录凭据（用户名或密码错误）
    #[error("用户名或密码错误")]
    InvalidCredentials,

    /// 500 Internal Server Error - 密码哈希处理错误
    #[error("密码哈希错误: {0}")]
    PasswordHashError(String),

    /// 500 Internal Server Error - JWT 令牌生成错误
    #[error("令牌生成错误: {0}")]
    TokenGenerationError(String),

    /// 401 Unauthorized - JWT 令牌无效或过期
    #[error("令牌无效: {0}")]
    InvalidToken(String),

    /// 400 Bad Request - 输入验证错误
    #[error("输入验证失败: {0}")]
    ValidationError(String),

    /// 500 Internal Server Error - 内部服务器错误
    #[error("内部服务器错误: {0}")]
    InternalServerError(String),

    /// 501 Not Implemented - 功能尚未实现
    #[error("功能尚未实现: {0}")]
    NotImplemented(String),

    /// 带有 SpanTrace 上下文的错误包装器
    /// 用于捕获错误发生时的 tracing span 上下文信息
    #[error("跟踪错误: {message}")]
    TracedError {
        /// 原始错误信息
        message: String,
        /// 错误发生时的 span 跟踪信息（不作为错误源）
        span_trace: SpanTrace,
        /// HTTP 状态码
        status_code: StatusCode,
    },

    /// 外部错误包装器 - 使用 anyhow 进行错误上下文传播
    #[error(transparent)]
    External(#[from] anyhow::Error),

    /// 数据库连接错误
    #[error("数据库连接失败")]
    DatabaseConnection(#[from] sea_orm::DbErr),

    /// 输入验证错误（来自 validator crate）
    #[error("输入验证失败")]
    InputValidation(#[from] ValidationErrors),

    /// JSON 序列化/反序列化错误
    #[error("JSON 处理错误: {0}")]
    JsonError(#[from] serde_json::Error),

    /// IO 错误
    #[error("IO 错误")]
    IoError(#[from] std::io::Error),

    /// 超时错误
    #[error("操作超时")]
    Timeout,

    /// 服务不可用错误
    #[error("服务暂时不可用，请稍后重试")]
    ServiceUnavailable,

    /// 限流错误
    #[error("请求过于频繁，请稍后重试")]
    RateLimited,
}

// thiserror 自动生成 Display 和 std::error::Error 实现
// 以及 From 实现（通过 #[from] 属性）
// 因此我们不需要手动实现这些 trait

/// 为 `AppError` 实现 `IntoResponse` trait [[Axum 核心特性: IntoResponse]]
///
/// 【目的】: 这是 Axum 错误处理的核心机制。
///          实现了 `IntoResponse` 的类型可以被 Axum Handler 直接返回（通常在 `Result::Err` 中）。
///          Axum 会自动调用此 `into_response` 方法将错误转换为标准的 `axum::response::Response`。
impl IntoResponse for AppError {
    /// 将 `AppError` 实例转换为 HTTP 响应 (`Response`)。
    ///
    /// 【重构说明】：使用统一的错误响应构建器，消除重复的错误处理逻辑
    fn into_response(self) -> Response {
        // 使用统一的错误处理器
        ErrorHandler::handle_app_error(&self).build()
    }
}

/// 应用程序结果类型别名
///
/// 【类型】: `Result<T, AppError>` 的别名。
/// 【目的】: 简化代码书写。
///          在函数签名中，可以用 `Result<T>` 代替冗长的 `std::result::Result<T, AppError>`。
pub type Result<T> = std::result::Result<T, AppError>;

/// 应用程序结果类型别名（AppResult）
///
/// 【类型】: `Result<T, AppError>` 的别名。
/// 【目的】: 为了与其他模块的命名约定保持一致。
pub type AppResult<T> = std::result::Result<T, AppError>;

// --- 辅助函数 ---

/// 辅助函数：创建"无效 UUID"错误 (`AppError::BadRequest`)
///
/// 【目的】: 标准化创建 UUID 解析失败时的错误。
///
/// # 参数
/// * `id: &str` - 尝试解析但失败的字符串。
/// # 返回值
/// * `AppError` - 一个配置好的 `AppError::BadRequest` 实例。
pub fn invalid_uuid(id: &str) -> AppError {
    AppError::BadRequest(format!("无效的UUID格式: {id}"))
}

// --- SpanTrace 错误处理扩展 ---

impl AppError {
    /// 创建一个带有当前 span 跟踪信息的错误
    ///
    /// 【功能】：捕获当前 tracing span 的上下文信息，并创建一个 TracedError
    ///
    /// # 参数
    /// * `message` - 错误消息
    /// * `status_code` - HTTP 状态码
    ///
    /// # 返回值
    /// * `AppError::TracedError` - 包含 span 跟踪信息的错误
    pub fn with_span_trace(message: String, status_code: StatusCode) -> Self {
        Self::TracedError {
            message,
            span_trace: SpanTrace::capture(),
            status_code,
        }
    }

    /// 将现有错误包装为带有 span 跟踪信息的错误
    ///
    /// 【功能】：将任何实现了 std::error::Error 的错误类型包装为 TracedError
    ///
    /// # 参数
    /// * `error` - 原始错误
    /// * `status_code` - HTTP 状态码
    ///
    /// # 返回值
    /// * `AppError::TracedError` - 包含 span 跟踪信息的错误
    pub fn wrap_with_span_trace<E: std::error::Error>(error: E, status_code: StatusCode) -> Self {
        Self::TracedError {
            message: error.to_string(),
            span_trace: SpanTrace::capture(),
            status_code,
        }
    }
}

/// 为 AppError 实现 ExtractSpanTrace trait
///
/// 【功能】：允许从 AppError 中提取 SpanTrace 信息
impl ExtractSpanTrace for AppError {
    fn span_trace(&self) -> Option<&SpanTrace> {
        match self {
            AppError::TracedError { span_trace, .. } => Some(span_trace),
            _ => None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::StatusCode;

    #[test]
    fn test_app_error_display() {
        let task_id = uuid::Uuid::new_v4();
        let error = AppError::TaskNotFound(task_id);
        assert_eq!(format!("{error}"), format!("未找到ID为 {} 的任务", task_id));

        let error = AppError::BadRequest("测试错误".to_string());
        assert_eq!(format!("{error}"), "请求错误: 测试错误");

        let error = AppError::InvalidCredentials;
        assert_eq!(format!("{error}"), "用户名或密码错误");
    }

    #[test]
    fn test_invalid_uuid_helper() {
        let error = invalid_uuid("invalid-uuid");
        match error {
            AppError::BadRequest(msg) => {
                assert_eq!(msg, "无效的UUID格式: invalid-uuid");
            }
            _ => panic!("Expected BadRequest error"),
        }
    }

    #[test]
    fn test_span_trace_error() {
        let error =
            AppError::with_span_trace("测试错误".to_string(), StatusCode::INTERNAL_SERVER_ERROR);

        match error {
            AppError::TracedError {
                message,
                status_code,
                ..
            } => {
                assert_eq!(message, "测试错误");
                assert_eq!(status_code, StatusCode::INTERNAL_SERVER_ERROR);
            }
            _ => panic!("Expected TracedError"),
        }
    }

    #[test]
    fn test_error_conversion() {
        let error = AppError::UserAlreadyExists("testuser".to_string());
        let response = error.into_response();
        // 这里我们只测试转换不会panic，具体的响应内容测试需要在集成测试中进行
        assert!(response.status().is_client_error());
    }
}
