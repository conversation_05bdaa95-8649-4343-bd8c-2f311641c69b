//! # 日志配置模块
//!
//! 定义日志系统的配置选项和轮转策略

use std::path::PathBuf;
use tracing_appender::rolling::Rotation;

/// 日志轮转策略枚举
///
/// 定义日志文件的轮转策略
#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub enum LogRotation {
    /// 每分钟轮转（用于测试）
    Minutely,
    /// 每小时轮转
    Hourly,
    /// 每天轮转（默认）
    Daily,
    /// 从不轮转
    Never,
}

impl From<LogRotation> for Rotation {
    fn from(rotation: LogRotation) -> Self {
        match rotation {
            LogRotation::Minutely => Rotation::MINUTELY,
            LogRotation::Hourly => Rotation::HOURLY,
            LogRotation::Daily => Rotation::DAILY,
            LogRotation::Never => Rotation::NEVER,
        }
    }
}

impl Default for LogRotation {
    fn default() -> Self {
        Self::Daily
    }
}

/// 日志配置选项
///
/// 提供灵活的日志配置选项，支持不同的输出格式和目标
#[derive(Debug, Clone)]
pub struct LoggerConfig {
    /// 是否启用JSON格式输出
    pub json_format: bool,
    /// 是否启用文件日志
    pub file_logging: bool,
    /// 日志文件目录
    pub log_directory: PathBuf,
    /// 是否启用错误跟踪
    pub error_tracing: bool,
    /// 是否显示span事件
    pub show_spans: bool,
    /// 自定义环境过滤器
    pub custom_filter: Option<String>,
    /// 日志轮转策略
    pub rotation: LogRotation,
    /// 日志文件名前缀
    pub file_name_prefix: String,
    /// 是否启用非阻塞写入
    pub non_blocking: bool,
    /// 保留的日志文件数量（0表示不限制）
    pub max_log_files: usize,
    /// 单个日志文件最大大小（字节，0表示不限制）
    pub max_file_size: u64,
    /// 是否启用控制台输出
    pub console_output: bool,
    /// 是否在控制台输出中显示颜色
    pub console_colors: bool,
}

impl Default for LoggerConfig {
    fn default() -> Self {
        Self {
            json_format: false,
            file_logging: true,
            log_directory: PathBuf::from("logs"),
            error_tracing: true,
            show_spans: true,
            custom_filter: None,
            rotation: LogRotation::Daily,
            file_name_prefix: "app".to_string(),
            non_blocking: true,
            max_log_files: 30,                // 保留30天的日志文件
            max_file_size: 100 * 1024 * 1024, // 100MB
            console_output: true,
            console_colors: true,
        }
    }
}

impl LoggerConfig {
    /// 创建一个用于开发环境的配置
    pub fn development() -> Self {
        Self {
            json_format: false,
            file_logging: false,
            console_output: true,
            console_colors: true,
            error_tracing: true,
            show_spans: true,
            ..Default::default()
        }
    }

    /// 创建一个用于生产环境的配置
    pub fn production() -> Self {
        Self {
            json_format: true,
            file_logging: true,
            console_output: true,
            console_colors: false,
            error_tracing: true,
            show_spans: false,
            non_blocking: true,
            ..Default::default()
        }
    }

    /// 创建一个用于测试环境的配置
    pub fn testing() -> Self {
        Self {
            json_format: false,
            file_logging: false,
            console_output: false,
            error_tracing: false,
            show_spans: false,
            ..Default::default()
        }
    }

    /// 设置日志目录
    pub fn with_log_directory<P: Into<PathBuf>>(mut self, directory: P) -> Self {
        self.log_directory = directory.into();
        self
    }

    /// 设置文件名前缀
    pub fn with_file_prefix<S: Into<String>>(mut self, prefix: S) -> Self {
        self.file_name_prefix = prefix.into();
        self
    }

    /// 设置自定义过滤器
    pub fn with_filter<S: Into<String>>(mut self, filter: S) -> Self {
        self.custom_filter = Some(filter.into());
        self
    }

    /// 启用或禁用JSON格式
    pub fn with_json_format(mut self, enabled: bool) -> Self {
        self.json_format = enabled;
        self
    }

    /// 启用或禁用文件日志
    pub fn with_file_logging(mut self, enabled: bool) -> Self {
        self.file_logging = enabled;
        self
    }

    /// 设置轮转策略
    pub fn with_rotation(mut self, rotation: LogRotation) -> Self {
        self.rotation = rotation;
        self
    }

    /// 启用或禁用控制台输出
    pub fn with_console_output(mut self, enabled: bool) -> Self {
        self.console_output = enabled;
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_log_rotation_conversion() {
        assert_eq!(Rotation::DAILY, LogRotation::Daily.into());
        assert_eq!(Rotation::HOURLY, LogRotation::Hourly.into());
        assert_eq!(Rotation::MINUTELY, LogRotation::Minutely.into());
        assert_eq!(Rotation::NEVER, LogRotation::Never.into());
    }

    #[test]
    fn test_logger_config_default() {
        let config = LoggerConfig::default();
        assert!(!config.json_format);
        assert!(config.file_logging);
        assert!(config.error_tracing);
        assert!(config.show_spans);
        assert_eq!(config.log_directory, PathBuf::from("logs"));
        assert_eq!(config.file_name_prefix, "app");
        assert_eq!(config.rotation, LogRotation::Daily);
        assert!(config.non_blocking);
        assert_eq!(config.max_log_files, 30);
        assert_eq!(config.max_file_size, 100 * 1024 * 1024);
    }

    #[test]
    fn test_logger_config_development() {
        let config = LoggerConfig::development();
        assert!(!config.json_format);
        assert!(!config.file_logging);
        assert!(config.console_output);
        assert!(config.console_colors);
        assert!(config.error_tracing);
        assert!(config.show_spans);
    }

    #[test]
    fn test_logger_config_production() {
        let config = LoggerConfig::production();
        assert!(config.json_format);
        assert!(config.file_logging);
        assert!(config.console_output);
        assert!(!config.console_colors);
        assert!(config.error_tracing);
        assert!(!config.show_spans);
        assert!(config.non_blocking);
    }

    #[test]
    fn test_logger_config_testing() {
        let config = LoggerConfig::testing();
        assert!(!config.json_format);
        assert!(!config.file_logging);
        assert!(!config.console_output);
        assert!(!config.error_tracing);
        assert!(!config.show_spans);
    }

    #[test]
    fn test_logger_config_builder_methods() {
        let config = LoggerConfig::default()
            .with_log_directory("/tmp/logs")
            .with_file_prefix("test")
            .with_filter("debug")
            .with_json_format(true)
            .with_file_logging(false)
            .with_rotation(LogRotation::Hourly);

        assert_eq!(config.log_directory, PathBuf::from("/tmp/logs"));
        assert_eq!(config.file_name_prefix, "test");
        assert_eq!(config.custom_filter, Some("debug".to_string()));
        assert!(config.json_format);
        assert!(!config.file_logging);
        assert_eq!(config.rotation, LogRotation::Hourly);
    }
}
