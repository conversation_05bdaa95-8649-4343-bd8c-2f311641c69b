//! # 日志中间件模块
//!
//! 提供HTTP请求日志记录中间件

use axum::{body::Body, extract::Request, middleware::Next, response::Response};
use std::time::Instant;
use tower_http::{
    classify::ServerErrorsAsFailures,
    trace::{self, TraceLayer},
};
use tracing::{Level, info, warn};
use uuid::Uuid;

/// 创建并返回一个用于 HTTP 请求跟踪的 `TraceLayer` 中间件
///
/// 提供一个配置好的 `TraceLayer`，可以作为 Axum 中间件使用。
/// 它会自动记录关于传入 HTTP 请求和传出响应的关键信息，如方法、路径、状态码、延迟等。
///
/// # 返回值
/// * `TraceLayer` - 返回一个配置好的 TraceLayer 实例
///
/// # 示例
/// ```rust,no_run
/// use axum::Router;
/// use app_common::logging::trace_layer;
/// use tower::ServiceBuilder;
///
/// let app = Router::new()
///     .layer(ServiceBuilder::new().layer(trace_layer()));
/// ```
pub fn trace_layer() -> TraceLayer<tower_http::classify::SharedClassifier<ServerErrorsAsFailures>> {
    TraceLayer::new_for_http()
        .on_request(trace::DefaultOnRequest::new().level(Level::INFO))
        .on_response(
            trace::DefaultOnResponse::new()
                .level(Level::INFO)
                .latency_unit(tower_http::LatencyUnit::Millis),
        )
        .on_body_chunk(trace::DefaultOnBodyChunk::new())
        .on_failure(
            trace::DefaultOnFailure::new()
                .level(Level::ERROR)
                .latency_unit(tower_http::LatencyUnit::Millis),
        )
}

/// 请求日志中间件
///
/// 记录每个HTTP请求的详细信息，包括：
/// - 请求ID（UUID）
/// - HTTP方法和路径
/// - 请求开始和结束时间
/// - 响应状态码
/// - 请求处理时长
/// - 客户端IP地址（如果可用）
///
/// # 参数
/// * `request` - HTTP请求
/// * `next` - 下一个中间件或处理器
///
/// # 返回值
/// * `Response<Body>` - HTTP响应
///
/// # 示例
/// ```rust,no_run
/// use axum::{Router, middleware};
/// use app_common::logging::request_logging_middleware;
///
/// let app = Router::new()
///     .layer(middleware::from_fn(request_logging_middleware));
/// ```
pub async fn request_logging_middleware(request: Request<Body>, next: Next) -> Response<Body> {
    let start_time = Instant::now();
    let request_id = Uuid::new_v4();

    // 提取请求信息
    let method = request.method().clone();
    let uri = request.uri().clone();
    let path = uri.path();
    let query = uri.query().unwrap_or("");

    // 提取客户端IP（如果可用）
    let client_ip = request
        .headers()
        .get("x-forwarded-for")
        .and_then(|hv| hv.to_str().ok())
        .or_else(|| {
            request
                .headers()
                .get("x-real-ip")
                .and_then(|hv| hv.to_str().ok())
        })
        .unwrap_or("unknown");

    // 记录请求开始
    info!(
        request_id = %request_id,
        method = %method,
        path = %path,
        query = %query,
        client_ip = %client_ip,
        "HTTP请求开始"
    );

    // 创建span用于跟踪整个请求
    let span = tracing::info_span!(
        "http_request",
        request_id = %request_id,
        method = %method,
        path = %path,
        client_ip = %client_ip
    );

    let _guard = span.enter();

    // 处理请求
    let response = next.run(request).await;

    // 计算处理时长
    let duration = start_time.elapsed();
    let status = response.status();

    // 根据状态码选择日志级别
    match status.as_u16() {
        200..=299 => {
            info!(
                request_id = %request_id,
                status = %status,
                duration_ms = duration.as_millis(),
                "HTTP请求完成"
            );
        }
        300..=399 => {
            info!(
                request_id = %request_id,
                status = %status,
                duration_ms = duration.as_millis(),
                "HTTP请求重定向"
            );
        }
        400..=499 => {
            warn!(
                request_id = %request_id,
                status = %status,
                duration_ms = duration.as_millis(),
                "HTTP请求客户端错误"
            );
        }
        500..=599 => {
            tracing::error!(
                request_id = %request_id,
                status = %status,
                duration_ms = duration.as_millis(),
                "HTTP请求服务器错误"
            );
        }
        _ => {
            warn!(
                request_id = %request_id,
                status = %status,
                duration_ms = duration.as_millis(),
                "HTTP请求未知状态"
            );
        }
    }

    response
}

/// 增强的请求日志中间件
///
/// 提供更详细的请求日志记录，包括：
/// - 请求头信息
/// - 用户代理
/// - 内容类型和长度
/// - 性能指标
///
/// # 参数
/// * `request` - HTTP请求
/// * `next` - 下一个中间件或处理器
///
/// # 返回值
/// * `Response<Body>` - HTTP响应
pub async fn enhanced_request_logging_middleware(
    request: Request<Body>,
    next: Next,
) -> Response<Body> {
    let start_time = Instant::now();
    let request_id = Uuid::new_v4();

    // 提取请求信息
    let method = request.method().clone();
    let uri = request.uri().clone();
    let path = uri.path();
    let query = uri.query().unwrap_or("");
    let version = format!("{:?}", request.version());

    // 提取请求头信息
    let user_agent = request
        .headers()
        .get("user-agent")
        .and_then(|hv| hv.to_str().ok())
        .unwrap_or("unknown");

    let content_type = request
        .headers()
        .get("content-type")
        .and_then(|hv| hv.to_str().ok())
        .unwrap_or("unknown");

    let content_length = request
        .headers()
        .get("content-length")
        .and_then(|hv| hv.to_str().ok())
        .and_then(|s| s.parse::<u64>().ok())
        .unwrap_or(0);

    let client_ip = request
        .headers()
        .get("x-forwarded-for")
        .and_then(|hv| hv.to_str().ok())
        .or_else(|| {
            request
                .headers()
                .get("x-real-ip")
                .and_then(|hv| hv.to_str().ok())
        })
        .unwrap_or("unknown");

    // 记录详细的请求开始信息
    info!(
        request_id = %request_id,
        method = %method,
        path = %path,
        query = %query,
        version = %version,
        client_ip = %client_ip,
        user_agent = %user_agent,
        content_type = %content_type,
        content_length = content_length,
        "详细HTTP请求开始"
    );

    // 创建详细的span
    let span = tracing::info_span!(
        "enhanced_http_request",
        request_id = %request_id,
        method = %method,
        path = %path,
        client_ip = %client_ip,
        user_agent = %user_agent
    );

    let _guard = span.enter();

    // 处理请求
    let response = next.run(request).await;

    // 计算处理时长
    let duration = start_time.elapsed();
    let status = response.status();

    // 提取响应头信息
    let response_content_type = response
        .headers()
        .get("content-type")
        .and_then(|hv| hv.to_str().ok())
        .unwrap_or("unknown");

    let response_content_length = response
        .headers()
        .get("content-length")
        .and_then(|hv| hv.to_str().ok())
        .and_then(|s| s.parse::<u64>().ok())
        .unwrap_or(0);

    // 记录详细的响应信息
    match status.as_u16() {
        200..=299 => {
            info!(
                request_id = %request_id,
                status = %status,
                duration_ms = duration.as_millis(),
                duration_micros = duration.as_micros(),
                response_content_type = %response_content_type,
                response_content_length = response_content_length,
                "详细HTTP请求完成"
            );
        }
        400..=499 => {
            warn!(
                request_id = %request_id,
                status = %status,
                duration_ms = duration.as_millis(),
                response_content_type = %response_content_type,
                response_content_length = response_content_length,
                "详细HTTP请求客户端错误"
            );
        }
        500..=599 => {
            tracing::error!(
                request_id = %request_id,
                status = %status,
                duration_ms = duration.as_millis(),
                response_content_type = %response_content_type,
                response_content_length = response_content_length,
                "详细HTTP请求服务器错误"
            );
        }
        _ => {
            info!(
                request_id = %request_id,
                status = %status,
                duration_ms = duration.as_millis(),
                response_content_type = %response_content_type,
                response_content_length = response_content_length,
                "详细HTTP请求完成"
            );
        }
    }

    response
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_trace_layer_creation() {
        let _layer = trace_layer();
        // 只是验证能够创建，具体功能需要集成测试
        // 如果能创建layer而不panic，测试就通过了
    }
}
