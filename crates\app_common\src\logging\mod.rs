//! # 日志记录模块
//!
//! 提供统一的日志记录功能，包括：
//! - 日志系统初始化和配置
//! - 请求日志中间件
//! - 结构化日志记录
//! - 文件轮转和管理
//!
//! 基于 tracing 框架实现，支持多种输出格式和目标。

pub mod config;
pub mod middleware;
pub mod setup;

pub use config::*;
pub use middleware::*;
pub use setup::*;

/// 重新导出常用的 tracing 宏和类型
pub use tracing::{Level, debug, error, info, trace, warn};

/// 重新导出 tracing 的 instrument 宏用于函数跟踪
pub use tracing::instrument;

/// 重新导出 UUID 用于请求ID生成
pub use uuid::Uuid;
