//! # 日志系统设置模块
//!
//! 负责初始化和配置日志系统

use super::config::LoggerConfig;
use anyhow::Result;
use std::fs;
use std::sync::atomic::{AtomicBool, Ordering};
use tracing_appender::non_blocking::WorkerGuard;
use tracing_subscriber::{EnvFilter, fmt, layer::SubscriberExt, util::SubscriberInitExt};

/// 静态标记，用于确保日志系统只初始化一次
static LOGGER_INITIALIZED: AtomicBool = AtomicBool::new(false);

/// 日志系统初始化结果
pub struct LoggerGuard {
    /// 非阻塞写入的工作线程守护者（可选）
    pub worker_guard: Option<WorkerGuard>,
}

impl LoggerGuard {
    /// 创建一个新的日志守护者
    pub fn new(worker_guard: Option<WorkerGuard>) -> Self {
        Self { worker_guard }
    }

    /// 创建一个空的日志守护者（用于控制台输出）
    pub fn empty() -> Self {
        Self { worker_guard: None }
    }
}

/// 设置基础日志系统
///
/// 使用默认配置初始化日志系统，适用于简单场景
pub fn setup_logger() -> Result<LoggerGuard> {
    setup_logger_with_config(LoggerConfig::default())
}

/// 使用指定配置设置日志系统
///
/// 根据配置选项初始化日志系统，支持：
/// - 控制台和文件输出
/// - JSON格式输出
/// - 文件日志轮转
/// - 错误上下文跟踪
/// - 结构化字段记录
/// - 非阻塞写入
///
/// # 参数
/// * `config` - 日志配置选项
///
/// # 返回值
/// * `Ok(LoggerGuard)` - 成功时返回日志守护者
/// * `Err(anyhow::Error)` - 初始化失败时返回错误
///
/// # 示例
/// ```rust,no_run
/// use app_common::logging::{LoggerConfig, setup_logger_with_config};
///
/// let config = LoggerConfig::development();
/// let _guard = setup_logger_with_config(config)?;
/// # Ok::<(), anyhow::Error>(())
/// ```
pub fn setup_logger_with_config(config: LoggerConfig) -> Result<LoggerGuard> {
    // 检查日志系统是否已经被初始化
    if LOGGER_INITIALIZED
        .compare_exchange(false, true, Ordering::SeqCst, Ordering::SeqCst)
        .is_err()
    {
        tracing::warn!("日志系统已经初始化，跳过重复初始化");
        return Ok(LoggerGuard::empty());
    }

    // 创建环境过滤器
    let env_filter = if let Some(custom_filter) = &config.custom_filter {
        EnvFilter::new(custom_filter)
    } else {
        EnvFilter::try_from_default_env().unwrap_or_else(|_| EnvFilter::new("info"))
    };

    let mut worker_guard = None;

    // 简化的日志设置
    if config.file_logging {
        // 确保日志目录存在
        fs::create_dir_all(&config.log_directory)?;

        let file_appender =
            tracing_appender::rolling::daily(&config.log_directory, &config.file_name_prefix);

        if config.non_blocking {
            let (non_blocking, guard) = tracing_appender::non_blocking(file_appender);
            worker_guard = Some(guard);

            tracing_subscriber::registry()
                .with(env_filter)
                .with(fmt::layer().with_writer(non_blocking).with_ansi(false))
                .init();
        } else {
            tracing_subscriber::registry()
                .with(env_filter)
                .with(fmt::layer().with_writer(file_appender).with_ansi(false))
                .init();
        }
    } else if config.console_output {
        // 只输出到控制台
        if config.json_format {
            tracing_subscriber::registry()
                .with(env_filter)
                .with(fmt::layer().json())
                .init();
        } else {
            tracing_subscriber::registry()
                .with(env_filter)
                .with(fmt::layer())
                .init();
        }
    } else {
        // 测试模式，禁用输出
        tracing_subscriber::registry().with(env_filter).init();
    }

    // 记录初始化成功信息
    tracing::info!(
        json_format = config.json_format,
        file_logging = config.file_logging,
        console_output = config.console_output,
        log_directory = %config.log_directory.display(),
        rotation = ?config.rotation,
        file_name_prefix = %config.file_name_prefix,
        non_blocking = config.non_blocking,
        max_log_files = config.max_log_files,
        max_file_size = config.max_file_size,
        error_tracing = config.error_tracing,
        show_spans = config.show_spans,
        "日志系统已初始化"
    );

    Ok(LoggerGuard::new(worker_guard))
}

/// 检查日志系统是否已初始化
pub fn is_logger_initialized() -> bool {
    LOGGER_INITIALIZED.load(Ordering::SeqCst)
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[test]
    fn test_logger_guard_creation() {
        let guard = LoggerGuard::empty();
        assert!(guard.worker_guard.is_none());

        let guard_with_worker = LoggerGuard::new(None);
        assert!(guard_with_worker.worker_guard.is_none());
    }

    #[test]
    fn test_duplicate_initialization() {
        // 测试重复初始化的处理
        let config = LoggerConfig::testing();
        let result1 = setup_logger_with_config(config.clone());
        assert!(result1.is_ok());

        // 第二次初始化应该成功但跳过实际初始化
        let result2 = setup_logger_with_config(config);
        assert!(result2.is_ok());
    }

    #[test]
    fn test_file_logging_directory_creation() {
        let temp_dir = TempDir::new().unwrap();
        let config = LoggerConfig::default()
            .with_log_directory(temp_dir.path())
            .with_file_prefix("test")
            .with_file_logging(true)
            .with_console_output(false);

        // 只测试目录创建逻辑，不实际初始化日志系统
        let result = std::fs::create_dir_all(&config.log_directory);
        assert!(result.is_ok());
        assert!(temp_dir.path().exists());
    }

    #[test]
    fn test_logger_config_validation() {
        let config = LoggerConfig::testing();
        assert!(!config.console_output);
        assert!(!config.file_logging);
        assert!(!config.json_format);

        let config = LoggerConfig::development();
        assert!(config.console_output);
        assert!(!config.file_logging);
        assert!(!config.json_format);

        let config = LoggerConfig::production();
        assert!(config.console_output);
        assert!(config.file_logging);
        assert!(config.json_format);
    }
}
