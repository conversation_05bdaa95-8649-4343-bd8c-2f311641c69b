//! # 性能告警管理模块
//!
//! 专门用于管理性能监控的告警机制，包括：
//! - 阈值监控和告警触发
//! - 告警规则配置和管理
//! - 告警通知和升级机制
//! - 告警历史记录和统计
//! - 告警抑制和去重

use parking_lot::Mutex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, SystemTime};
use tracing::{debug, error, info, warn};

/// 告警级别
///
/// 【功能】：定义告警的严重程度级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Hash, Serialize, Deserialize)]
pub enum AlertLevel {
    /// 信息级别 - 仅记录，不需要立即处理
    Info,
    /// 警告级别 - 需要关注，但不紧急
    Warning,
    /// 错误级别 - 需要及时处理
    Error,
    /// 严重级别 - 需要立即处理
    Critical,
}

impl AlertLevel {
    /// 获取告警级别的字符串表示
    pub fn as_str(&self) -> &'static str {
        match self {
            AlertLevel::Info => "info",
            AlertLevel::Warning => "warning",
            AlertLevel::Error => "error",
            AlertLevel::Critical => "critical",
        }
    }
}

/// 告警规则
///
/// 【功能】：定义性能监控的告警规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertRule {
    /// 规则ID
    pub id: String,
    /// 规则名称
    pub name: String,
    /// 规则描述
    pub description: String,
    /// 监控指标名称
    pub metric_name: String,
    /// 告警级别
    pub level: AlertLevel,
    /// 阈值
    pub threshold: f64,
    /// 比较操作符（>, <, >=, <=, ==, !=）
    pub operator: String,
    /// 持续时间（秒）- 指标超过阈值多长时间后触发告警
    pub duration_seconds: u64,
    /// 是否启用
    pub enabled: bool,
    /// 标签过滤器
    pub labels: HashMap<String, String>,
    /// 告警消息模板
    pub message_template: String,
}

/// 告警事件
///
/// 【功能】：表示一个具体的告警事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertEvent {
    /// 事件ID
    pub id: String,
    /// 规则ID
    pub rule_id: String,
    /// 告警级别
    pub level: AlertLevel,
    /// 告警消息
    pub message: String,
    /// 当前指标值
    pub current_value: f64,
    /// 阈值
    pub threshold: f64,
    /// 相关标签
    pub labels: HashMap<String, String>,
    /// 触发时间
    pub triggered_at: SystemTime,
    /// 解决时间（如果已解决）
    pub resolved_at: Option<SystemTime>,
    /// 告警状态
    pub status: AlertStatus,
}

/// 告警状态
///
/// 【功能】：定义告警事件的状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum AlertStatus {
    /// 触发中
    Firing,
    /// 已解决
    Resolved,
    /// 已抑制
    Suppressed,
}

/// 告警管理器配置
///
/// 【功能】：配置告警管理器的行为参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertManagerConfig {
    /// 是否启用告警管理
    pub enable_alerting: bool,
    /// 告警评估间隔（秒）
    pub evaluation_interval_seconds: u64,
    /// 告警历史保留时间（秒）
    pub alert_history_retention_seconds: u64,
    /// 最大活跃告警数量
    pub max_active_alerts: usize,
    /// 是否启用告警去重
    pub enable_deduplication: bool,
    /// 去重时间窗口（秒）
    pub deduplication_window_seconds: u64,
    /// 是否启用告警抑制
    pub enable_suppression: bool,
}

impl Default for AlertManagerConfig {
    fn default() -> Self {
        Self {
            enable_alerting: true,
            evaluation_interval_seconds: 30, // 30秒评估一次
            alert_history_retention_seconds: 7 * 24 * 3600, // 保留7天
            max_active_alerts: 1000,
            enable_deduplication: true,
            deduplication_window_seconds: 300, // 5分钟去重窗口
            enable_suppression: true,
        }
    }
}

/// 告警管理器
///
/// 【功能】：管理所有的告警规则和告警事件
#[derive(Debug)]
pub struct AlertManager {
    /// 配置
    config: AlertManagerConfig,
    /// 告警规则
    rules: Arc<Mutex<HashMap<String, AlertRule>>>,
    /// 活跃告警
    active_alerts: Arc<Mutex<HashMap<String, AlertEvent>>>,
    /// 告警历史
    alert_history: Arc<Mutex<Vec<AlertEvent>>>,
    /// 告警统计
    alert_stats: AlertStats,
}

/// 告警统计信息
///
/// 【功能】：记录告警系统的统计数据
#[derive(Debug)]
pub struct AlertStats {
    /// 总告警数
    total_alerts: AtomicU64,
    /// 信息级告警数
    info_alerts: AtomicU64,
    /// 警告级告警数
    warning_alerts: AtomicU64,
    /// 错误级告警数
    error_alerts: AtomicU64,
    /// 严重级告警数
    critical_alerts: AtomicU64,
    /// 已解决告警数
    resolved_alerts: AtomicU64,
}

impl AlertManager {
    /// 创建新的告警管理器
    ///
    /// 【功能】：初始化告警管理系统
    ///
    /// # 参数
    /// * `config` - 告警管理器配置
    ///
    /// # 返回值
    /// * `Arc<AlertManager>` - 告警管理器实例
    pub fn new(config: AlertManagerConfig) -> Arc<Self> {
        // 注册告警相关的Prometheus指标
        if config.enable_alerting {
            Self::register_alert_prometheus_metrics();
        }

        Arc::new(Self {
            config,
            rules: Arc::new(Mutex::new(HashMap::new())),
            active_alerts: Arc::new(Mutex::new(HashMap::new())),
            alert_history: Arc::new(Mutex::new(Vec::new())),
            alert_stats: AlertStats {
                total_alerts: AtomicU64::new(0),
                info_alerts: AtomicU64::new(0),
                warning_alerts: AtomicU64::new(0),
                error_alerts: AtomicU64::new(0),
                critical_alerts: AtomicU64::new(0),
                resolved_alerts: AtomicU64::new(0),
            },
        })
    }

    /// 注册告警相关的Prometheus指标
    ///
    /// 【功能】：注册所有告警相关的Prometheus指标
    fn register_alert_prometheus_metrics() {
        use metrics::{describe_counter, describe_gauge};

        // 告警事件指标
        describe_counter!("alerts_total", "告警总数，按级别和规则分类");
        describe_counter!("alerts_resolved_total", "已解决告警总数");
        describe_gauge!("alerts_active", "当前活跃告警数，按级别分类");
        describe_gauge!("alert_rules_count", "告警规则总数");

        // 告警处理指标
        describe_counter!("alert_evaluations_total", "告警规则评估总数");
        describe_counter!("alert_notifications_total", "告警通知总数，按类型分类");

        info!("✅ 告警管理Prometheus指标注册完成");
    }

    /// 添加告警规则
    ///
    /// 【功能】：添加新的告警规则
    ///
    /// # 参数
    /// * `rule` - 告警规则
    pub fn add_rule(&self, rule: AlertRule) {
        let rule_id = rule.id.clone();
        self.rules.lock().insert(rule_id.clone(), rule);

        info!(
            rule_id = %rule_id,
            "告警规则已添加"
        );

        // 更新Prometheus指标
        if self.config.enable_alerting {
            use metrics::gauge;
            let rule_count = self.rules.lock().len() as f64;
            gauge!("alert_rules_count").set(rule_count);
        }
    }

    /// 移除告警规则
    ///
    /// 【功能】：移除指定的告警规则
    ///
    /// # 参数
    /// * `rule_id` - 规则ID
    pub fn remove_rule(&self, rule_id: &str) {
        if self.rules.lock().remove(rule_id).is_some() {
            info!(rule_id = rule_id, "告警规则已移除");

            // 更新Prometheus指标
            if self.config.enable_alerting {
                use metrics::gauge;
                let rule_count = self.rules.lock().len() as f64;
                gauge!("alert_rules_count").set(rule_count);
            }
        }
    }

    /// 评估指标值并触发告警
    ///
    /// 【功能】：根据告警规则评估指标值，必要时触发告警
    ///
    /// # 参数
    /// * `metric_name` - 指标名称
    /// * `value` - 指标值
    /// * `labels` - 指标标签
    pub fn evaluate_metric(&self, metric_name: &str, value: f64, labels: &HashMap<String, String>) {
        if !self.config.enable_alerting {
            return;
        }

        let rules = self.rules.lock();
        for rule in rules.values() {
            if rule.enabled && rule.metric_name == metric_name {
                // 检查标签过滤器
                if self.matches_labels(&rule.labels, labels) {
                    // 评估阈值
                    if self.evaluate_threshold(rule, value) {
                        self.trigger_alert(rule, value, labels);
                    } else {
                        self.resolve_alert(rule, labels);
                    }
                }
            }
        }
    }

    /// 检查标签是否匹配
    ///
    /// 【功能】：检查指标标签是否匹配规则的标签过滤器
    fn matches_labels(
        &self,
        rule_labels: &HashMap<String, String>,
        metric_labels: &HashMap<String, String>,
    ) -> bool {
        for (key, value) in rule_labels {
            if metric_labels.get(key) != Some(value) {
                return false;
            }
        }
        true
    }

    /// 评估阈值
    ///
    /// 【功能】：根据规则评估指标值是否超过阈值
    fn evaluate_threshold(&self, rule: &AlertRule, value: f64) -> bool {
        match rule.operator.as_str() {
            ">" => value > rule.threshold,
            "<" => value < rule.threshold,
            ">=" => value >= rule.threshold,
            "<=" => value <= rule.threshold,
            "==" => (value - rule.threshold).abs() < f64::EPSILON,
            "!=" => (value - rule.threshold).abs() >= f64::EPSILON,
            _ => {
                warn!(
                    operator = %rule.operator,
                    "不支持的比较操作符"
                );
                false
            }
        }
    }

    /// 触发告警
    ///
    /// 【功能】：触发新的告警事件
    fn trigger_alert(&self, rule: &AlertRule, value: f64, labels: &HashMap<String, String>) {
        let alert_id = format!("{}:{}", rule.id, self.generate_label_hash(labels));

        // 检查是否已存在相同的活跃告警（去重）
        if self.config.enable_deduplication {
            let active_alerts = self.active_alerts.lock();
            if let Some(existing_alert) = active_alerts.get(&alert_id) {
                if existing_alert.status == AlertStatus::Firing {
                    // 告警已存在且仍在触发中，跳过
                    return;
                }
            }
        }

        // 创建新的告警事件
        let alert_event = AlertEvent {
            id: alert_id.clone(),
            rule_id: rule.id.clone(),
            level: rule.level,
            message: self.format_alert_message(rule, value, labels),
            current_value: value,
            threshold: rule.threshold,
            labels: labels.clone(),
            triggered_at: SystemTime::now(),
            resolved_at: None,
            status: AlertStatus::Firing,
        };

        // 添加到活跃告警
        self.active_alerts
            .lock()
            .insert(alert_id.clone(), alert_event.clone());

        // 添加到历史记录
        self.alert_history.lock().push(alert_event.clone());

        // 更新统计
        self.update_alert_stats(&alert_event, true);

        // 记录日志
        match rule.level {
            AlertLevel::Info => info!(
                alert_id = %alert_id,
                rule_id = %rule.id,
                message = %alert_event.message,
                "告警触发 - 信息级别"
            ),
            AlertLevel::Warning => warn!(
                alert_id = %alert_id,
                rule_id = %rule.id,
                message = %alert_event.message,
                "告警触发 - 警告级别"
            ),
            AlertLevel::Error => error!(
                alert_id = %alert_id,
                rule_id = %rule.id,
                message = %alert_event.message,
                "告警触发 - 错误级别"
            ),
            AlertLevel::Critical => error!(
                alert_id = %alert_id,
                rule_id = %rule.id,
                message = %alert_event.message,
                "告警触发 - 严重级别 ⚠️"
            ),
        }

        // 更新Prometheus指标
        if self.config.enable_alerting {
            use metrics::counter;
            counter!("alerts_total",
                "level" => rule.level.as_str(),
                "rule" => rule.id.clone()
            )
            .increment(1);

            // 更新活跃告警数
            self.update_active_alerts_gauge();
        }
    }

    /// 解决告警
    ///
    /// 【功能】：解决现有的告警事件
    fn resolve_alert(&self, rule: &AlertRule, labels: &HashMap<String, String>) {
        let alert_id = format!("{}:{}", rule.id, self.generate_label_hash(labels));

        let mut active_alerts = self.active_alerts.lock();
        if let Some(mut alert) = active_alerts.remove(&alert_id) {
            alert.status = AlertStatus::Resolved;
            alert.resolved_at = Some(SystemTime::now());

            // 更新历史记录
            self.alert_history.lock().push(alert.clone());

            // 更新统计
            self.alert_stats
                .resolved_alerts
                .fetch_add(1, Ordering::Relaxed);

            info!(
                alert_id = %alert_id,
                rule_id = %rule.id,
                "告警已解决"
            );

            // 更新Prometheus指标
            if self.config.enable_alerting {
                use metrics::counter;
                counter!("alerts_resolved_total").increment(1);
                self.update_active_alerts_gauge();
            }
        }
    }

    /// 生成标签哈希
    ///
    /// 【功能】：为标签集合生成唯一的哈希值
    fn generate_label_hash(&self, labels: &HashMap<String, String>) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        let mut sorted_labels: Vec<_> = labels.iter().collect();
        sorted_labels.sort_by_key(|(k, _)| *k);
        sorted_labels.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    /// 格式化告警消息
    ///
    /// 【功能】：根据模板格式化告警消息
    fn format_alert_message(
        &self,
        rule: &AlertRule,
        value: f64,
        labels: &HashMap<String, String>,
    ) -> String {
        let mut message = rule.message_template.clone();

        // 替换变量
        message = message.replace("{metric_name}", &rule.metric_name);
        message = message.replace("{current_value}", &value.to_string());
        message = message.replace("{threshold}", &rule.threshold.to_string());
        message = message.replace("{operator}", &rule.operator);

        // 替换标签变量
        for (key, value) in labels {
            let placeholder = format!("{{{key}}}");
            message = message.replace(&placeholder, value);
        }

        message
    }

    /// 更新告警统计
    ///
    /// 【功能】：更新告警统计计数器
    fn update_alert_stats(&self, alert: &AlertEvent, is_new: bool) {
        if is_new {
            self.alert_stats
                .total_alerts
                .fetch_add(1, Ordering::Relaxed);

            match alert.level {
                AlertLevel::Info => self.alert_stats.info_alerts.fetch_add(1, Ordering::Relaxed),
                AlertLevel::Warning => self
                    .alert_stats
                    .warning_alerts
                    .fetch_add(1, Ordering::Relaxed),
                AlertLevel::Error => self
                    .alert_stats
                    .error_alerts
                    .fetch_add(1, Ordering::Relaxed),
                AlertLevel::Critical => self
                    .alert_stats
                    .critical_alerts
                    .fetch_add(1, Ordering::Relaxed),
            };
        }
    }

    /// 更新活跃告警数量的Prometheus指标
    ///
    /// 【功能】：更新各级别活跃告警数量的Prometheus指标
    fn update_active_alerts_gauge(&self) {
        use metrics::gauge;

        let active_alerts = self.active_alerts.lock();
        let mut level_counts = HashMap::new();

        for alert in active_alerts.values() {
            *level_counts.entry(alert.level).or_insert(0) += 1;
        }

        // 更新各级别的活跃告警数
        gauge!("alerts_active", "level" => "info")
            .set(*level_counts.get(&AlertLevel::Info).unwrap_or(&0) as f64);
        gauge!("alerts_active", "level" => "warning")
            .set(*level_counts.get(&AlertLevel::Warning).unwrap_or(&0) as f64);
        gauge!("alerts_active", "level" => "error")
            .set(*level_counts.get(&AlertLevel::Error).unwrap_or(&0) as f64);
        gauge!("alerts_active", "level" => "critical")
            .set(*level_counts.get(&AlertLevel::Critical).unwrap_or(&0) as f64);
    }

    /// 获取活跃告警列表
    ///
    /// 【功能】：获取当前所有活跃的告警事件
    ///
    /// # 返回值
    /// * `Vec<AlertEvent>` - 活跃告警列表
    pub fn get_active_alerts(&self) -> Vec<AlertEvent> {
        self.active_alerts.lock().values().cloned().collect()
    }

    /// 获取告警历史
    ///
    /// 【功能】：获取告警历史记录
    ///
    /// # 参数
    /// * `limit` - 返回的最大数量
    ///
    /// # 返回值
    /// * `Vec<AlertEvent>` - 告警历史列表
    pub fn get_alert_history(&self, limit: usize) -> Vec<AlertEvent> {
        let history = self.alert_history.lock();
        let start = if history.len() > limit {
            history.len() - limit
        } else {
            0
        };
        history[start..].to_vec()
    }

    /// 获取告警统计信息
    ///
    /// 【功能】：获取告警系统的统计信息
    ///
    /// # 返回值
    /// * `AlertStatsSummary` - 告警统计摘要
    pub fn get_alert_stats(&self) -> AlertStatsSummary {
        AlertStatsSummary {
            total_alerts: self.alert_stats.total_alerts.load(Ordering::Relaxed),
            info_alerts: self.alert_stats.info_alerts.load(Ordering::Relaxed),
            warning_alerts: self.alert_stats.warning_alerts.load(Ordering::Relaxed),
            error_alerts: self.alert_stats.error_alerts.load(Ordering::Relaxed),
            critical_alerts: self.alert_stats.critical_alerts.load(Ordering::Relaxed),
            resolved_alerts: self.alert_stats.resolved_alerts.load(Ordering::Relaxed),
            active_alerts_count: self.active_alerts.lock().len(),
            rules_count: self.rules.lock().len(),
        }
    }

    /// 清理过期的告警历史
    ///
    /// 【功能】：清理超过保留时间的告警历史记录
    pub fn cleanup_expired_alerts(&self) {
        let retention_duration = Duration::from_secs(self.config.alert_history_retention_seconds);
        let cutoff_time = SystemTime::now() - retention_duration;

        let mut history = self.alert_history.lock();
        let original_len = history.len();

        history.retain(|alert| alert.triggered_at > cutoff_time);

        let removed_count = original_len - history.len();
        if removed_count > 0 {
            debug!(
                removed_count = removed_count,
                remaining_count = history.len(),
                "告警历史清理完成"
            );
        }
    }
}

/// 告警统计摘要
///
/// 【功能】：包含告警系统的统计摘要信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertStatsSummary {
    /// 总告警数
    pub total_alerts: u64,
    /// 信息级告警数
    pub info_alerts: u64,
    /// 警告级告警数
    pub warning_alerts: u64,
    /// 错误级告警数
    pub error_alerts: u64,
    /// 严重级告警数
    pub critical_alerts: u64,
    /// 已解决告警数
    pub resolved_alerts: u64,
    /// 当前活跃告警数
    pub active_alerts_count: usize,
    /// 告警规则数
    pub rules_count: usize,
}

/// 创建告警管理器
///
/// 【功能】：创建一个告警管理器实例
///
/// # 参数
/// * `config` - 告警管理器配置
///
/// # 返回值
/// * `Arc<AlertManager>` - 告警管理器实例
pub fn create_alert_manager(config: AlertManagerConfig) -> Arc<AlertManager> {
    AlertManager::new(config)
}

/// 创建默认的性能告警规则
///
/// 【功能】：创建一组默认的性能监控告警规则
///
/// # 返回值
/// * `Vec<AlertRule>` - 默认告警规则列表
pub fn create_default_performance_alert_rules() -> Vec<AlertRule> {
    vec![
        // HTTP响应时间告警
        AlertRule {
            id: "http_response_time_high".to_string(),
            name: "HTTP响应时间过高".to_string(),
            description: "HTTP请求响应时间超过阈值".to_string(),
            metric_name: "http_request_duration_seconds".to_string(),
            level: AlertLevel::Warning,
            threshold: 2.0, // 2秒
            operator: ">".to_string(),
            duration_seconds: 60,
            enabled: true,
            labels: HashMap::new(),
            message_template: "HTTP响应时间过高: {current_value}s > {threshold}s".to_string(),
        },
        // 数据库连接池使用率告警
        AlertRule {
            id: "database_connection_pool_high".to_string(),
            name: "数据库连接池使用率过高".to_string(),
            description: "数据库连接池使用率超过阈值".to_string(),
            metric_name: "database_connection_pool_usage_ratio".to_string(),
            level: AlertLevel::Warning,
            threshold: 0.8, // 80%
            operator: ">".to_string(),
            duration_seconds: 120,
            enabled: true,
            labels: HashMap::new(),
            message_template: "数据库连接池使用率过高: {current_value} > {threshold}".to_string(),
        },
        // 搜索缓存命中率低告警
        AlertRule {
            id: "search_cache_hit_ratio_low".to_string(),
            name: "搜索缓存命中率过低".to_string(),
            description: "搜索缓存命中率低于阈值".to_string(),
            metric_name: "search_cache_hit_ratio".to_string(),
            level: AlertLevel::Warning,
            threshold: 0.7, // 70%
            operator: "<".to_string(),
            duration_seconds: 300,
            enabled: true,
            labels: HashMap::new(),
            message_template: "搜索缓存命中率过低: {current_value} < {threshold}".to_string(),
        },
        // 队列长度过长告警
        AlertRule {
            id: "queue_length_high".to_string(),
            name: "队列长度过长".to_string(),
            description: "队列长度超过阈值".to_string(),
            metric_name: "queue_length".to_string(),
            level: AlertLevel::Error,
            threshold: 100.0,
            operator: ">".to_string(),
            duration_seconds: 60,
            enabled: true,
            labels: HashMap::new(),
            message_template: "队列长度过长: {current_value} > {threshold}".to_string(),
        },
    ]
}
