//! # JWT认证中间件
//!
//! 提供基于JWT的认证中间件和用户提取器，适配新的模块化DDD架构。
//!
//! ## 核心功能
//! - **AuthenticatedUser提取器**: 从请求中提取认证用户信息
//! - **JWT验证**: 验证JWT token的有效性
//! - **错误处理**: 统一的认证错误处理
//!
//! ## 设计原则
//! - **单一职责**: 专注于JWT认证逻辑
//! - **类型安全**: 使用Uuid类型确保类型安全
//! - **可测试性**: 便于单元测试和集成测试

use crate::utils::{AuthService, Claims, JwtError, error_response::ErrorResponseBuilder};
use axum::{
    extract::{FromRequestParts, Request},
    http::{StatusCode, request::Parts},
    middleware::Next,
    response::Response,
};
use sea_orm::prelude::Uuid;
use serde::{Deserialize, Serialize};
use std::str::FromStr;

/// 认证用户信息结构体
///
/// 用于在请求处理过程中传递认证后的用户信息
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct AuthenticatedUser {
    /// 用户ID (使用Uuid类型确保类型安全)
    pub user_id: Uuid,
    /// 用户名
    pub username: String,
}

impl AuthenticatedUser {
    /// 创建新的认证用户实例
    pub fn new(user_id: Uuid, username: String) -> Self {
        Self { user_id, username }
    }

    /// 从JWT Claims创建认证用户
    pub fn from_claims(claims: Claims) -> Result<Self, JwtAuthError> {
        let user_id = Uuid::from_str(&claims.sub)
            .map_err(|_| JwtAuthError::InvalidUserId(claims.sub.clone()))?;

        Ok(Self {
            user_id,
            username: claims.username,
        })
    }
}

/// JWT认证错误类型
#[derive(Debug, thiserror::Error, PartialEq)]
pub enum JwtAuthError {
    #[error("JWT token 缺失")]
    TokenMissing,
    #[error("JWT token 无效")]
    TokenInvalid,
    #[error("JWT token 已过期")]
    TokenExpired,
    #[error("用户ID格式无效: {0}")]
    InvalidUserId(String),
    #[error("认证失败: {0}")]
    AuthenticationFailed(String),
}

impl From<JwtError> for JwtAuthError {
    fn from(jwt_error: JwtError) -> Self {
        match jwt_error {
            JwtError::TokenMissing => JwtAuthError::TokenMissing,
            JwtError::TokenInvalid => JwtAuthError::TokenInvalid,
            JwtError::TokenExpired => JwtAuthError::TokenExpired,
            JwtError::TokenCreationFailed(msg) => JwtAuthError::AuthenticationFailed(msg),
        }
    }
}

impl From<JwtAuthError> for StatusCode {
    fn from(error: JwtAuthError) -> Self {
        match error {
            JwtAuthError::TokenMissing
            | JwtAuthError::TokenInvalid
            | JwtAuthError::TokenExpired
            | JwtAuthError::InvalidUserId(_)
            | JwtAuthError::AuthenticationFailed(_) => StatusCode::UNAUTHORIZED,
        }
    }
}

/// JWT认证状态
///
/// 用于在应用状态中存储JWT密钥
#[derive(Debug, Clone)]
pub struct JwtAuthState {
    pub jwt_secret: String,
}

impl JwtAuthState {
    /// 创建新的JWT认证状态
    pub fn new(jwt_secret: String) -> Self {
        Self { jwt_secret }
    }
}

/// JWT认证中间件函数
///
/// 用于在请求处理过程中注入JWT密钥，使AuthenticatedUser提取器能够正常工作
pub async fn jwt_auth_middleware(mut req: Request, next: Next) -> Result<Response, StatusCode> {
    // 从应用状态中获取JWT密钥
    let jwt_secret = req
        .extensions()
        .get::<JwtAuthState>()
        .ok_or(StatusCode::INTERNAL_SERVER_ERROR)?
        .jwt_secret
        .clone();

    // 将JWT密钥注入到请求扩展中，供AuthenticatedUser提取器使用
    req.extensions_mut().insert(jwt_secret);

    // 继续处理请求
    Ok(next.run(req).await)
}

/// AuthenticatedUser的FromRequestParts实现
///
/// 这个实现允许在Axum处理器中直接使用AuthenticatedUser作为参数，
/// 自动从请求中提取和验证JWT token
impl<S> FromRequestParts<S> for AuthenticatedUser
where
    S: Send + Sync,
{
    type Rejection = Response;

    async fn from_request_parts(parts: &mut Parts, _state: &S) -> Result<Self, Self::Rejection> {
        // 从请求扩展中获取JWT密钥
        // 注意：这需要在中间件中预先设置
        let jwt_secret = parts
            .extensions
            .get::<String>()
            .ok_or_else(|| {
                tracing::error!("JWT密钥未在请求扩展中找到");
                ErrorResponseBuilder::internal_server_error()
                    .with_details("JWT配置错误")
                    .build()
            })?
            .clone();

        // 创建认证服务
        let auth_service = AuthService::new(jwt_secret);

        // 从请求头中提取并验证JWT token
        let claims = auth_service
            .authenticate_http_request(&parts.headers)
            .map_err(|e| {
                tracing::warn!("JWT认证失败: {:?}", e);
                ErrorResponseBuilder::authentication_error()
                    .with_details("无效的认证凭据")
                    .build()
            })?;

        // 从Claims创建AuthenticatedUser
        AuthenticatedUser::from_claims(claims).map_err(|e| {
            tracing::warn!("创建认证用户失败: {:?}", e);
            ErrorResponseBuilder::authentication_error()
                .with_details("用户信息解析失败")
                .build()
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::JwtUtils;

    #[allow(dead_code)]
    const TEST_SECRET: &str = "test-jwt-auth-middleware-secret";

    #[allow(dead_code)]
    fn create_test_jwt_token(user_id: &str, username: &str) -> String {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());
        jwt_utils.create_token(user_id, username, 1).unwrap()
    }

    #[test]
    fn test_authenticated_user_creation() {
        let user_id = Uuid::new_v4();
        let username = "testuser".to_string();

        let auth_user = AuthenticatedUser::new(user_id, username.clone());

        assert_eq!(auth_user.user_id, user_id);
        assert_eq!(auth_user.username, username);
    }

    #[test]
    fn test_authenticated_user_from_claims() {
        let user_id = Uuid::new_v4();
        let claims = Claims {
            sub: user_id.to_string(),
            username: "testuser".to_string(),
            exp: 9999999999,
            iat: 1000000000,
        };

        let auth_user = AuthenticatedUser::from_claims(claims).unwrap();

        assert_eq!(auth_user.user_id, user_id);
        assert_eq!(auth_user.username, "testuser");
    }

    #[test]
    fn test_authenticated_user_from_invalid_claims() {
        let claims = Claims {
            sub: "invalid-uuid".to_string(),
            username: "testuser".to_string(),
            exp: 9999999999,
            iat: 1000000000,
        };

        let result = AuthenticatedUser::from_claims(claims);

        assert!(matches!(result, Err(JwtAuthError::InvalidUserId(_))));
    }

    #[test]
    fn test_jwt_auth_error_from_jwt_error() {
        assert_eq!(
            JwtAuthError::from(JwtError::TokenMissing),
            JwtAuthError::TokenMissing
        );
        assert_eq!(
            JwtAuthError::from(JwtError::TokenInvalid),
            JwtAuthError::TokenInvalid
        );
        assert_eq!(
            JwtAuthError::from(JwtError::TokenExpired),
            JwtAuthError::TokenExpired
        );
    }

    #[test]
    fn test_jwt_auth_error_to_status_code() {
        assert_eq!(
            StatusCode::from(JwtAuthError::TokenMissing),
            StatusCode::UNAUTHORIZED
        );
        assert_eq!(
            StatusCode::from(JwtAuthError::TokenInvalid),
            StatusCode::UNAUTHORIZED
        );
        assert_eq!(
            StatusCode::from(JwtAuthError::TokenExpired),
            StatusCode::UNAUTHORIZED
        );
    }

    #[test]
    fn test_jwt_auth_state_creation() {
        let secret = "test-secret".to_string();
        let auth_state = JwtAuthState::new(secret.clone());

        assert_eq!(auth_state.jwt_secret, secret);
    }
}
