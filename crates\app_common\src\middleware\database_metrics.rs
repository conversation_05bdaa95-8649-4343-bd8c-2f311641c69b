//! # 数据库性能监控模块
//!
//! 专门用于监控数据库操作的性能指标，包括：
//! - 数据库连接池监控
//! - 查询执行时间统计
//! - 慢查询检测和记录
//! - 数据库错误率监控
//! - 连接泄漏检测

use parking_lot::Mutex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::Instant;
use tracing::{debug, error, info, warn};

/// 数据库性能监控配置
///
/// 【功能】：配置数据库性能监控的各项参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseMetricsConfig {
    /// 是否启用数据库性能监控
    pub enable_database_metrics: bool,
    /// 是否启用连接池监控
    pub enable_connection_pool_monitoring: bool,
    /// 是否启用慢查询检测
    pub enable_slow_query_detection: bool,
    /// 慢查询阈值（毫秒）
    pub slow_query_threshold_ms: u64,
    /// 连接池告警阈值（百分比）
    pub connection_pool_warning_threshold: f64,
    /// 查询统计缓存大小
    pub query_stats_cache_size: usize,
    /// 是否记录查询参数（注意隐私）
    pub log_query_parameters: bool,
}

impl Default for DatabaseMetricsConfig {
    fn default() -> Self {
        Self {
            enable_database_metrics: true,
            enable_connection_pool_monitoring: true,
            enable_slow_query_detection: true,
            slow_query_threshold_ms: 1000,          // 1秒
            connection_pool_warning_threshold: 0.8, // 80%
            query_stats_cache_size: 500,
            log_query_parameters: false, // 默认不记录参数以保护隐私
        }
    }
}

/// 数据库查询指标
///
/// 【功能】：记录单次数据库查询的详细指标
#[derive(Debug, Clone)]
pub struct DatabaseQueryMetrics {
    /// 查询类型（SELECT, INSERT, UPDATE, DELETE等）
    pub query_type: String,
    /// 查询表名
    pub table_name: Option<String>,
    /// 查询开始时间
    pub start_time: Instant,
    /// 查询SQL（可选，用于调试）
    pub sql: Option<String>,
    /// 影响的行数
    pub affected_rows: Option<u64>,
    /// 是否使用了索引
    pub used_index: Option<bool>,
    /// 连接池ID
    pub connection_id: Option<String>,
}

/// 查询统计信息
///
/// 【功能】：统计特定查询的性能数据
#[derive(Debug, Clone)]
pub struct QueryStats {
    /// 执行次数
    pub execution_count: u64,
    /// 平均执行时间（毫秒）
    pub avg_execution_time_ms: f64,
    /// 最小执行时间（毫秒）
    pub min_execution_time_ms: u64,
    /// 最大执行时间（毫秒）
    pub max_execution_time_ms: u64,
    /// 错误次数
    pub error_count: u64,
    /// 最后执行时间
    pub last_execution_time: std::time::SystemTime,
}

/// 数据库性能指标收集器
///
/// 【功能】：集中管理数据库操作的所有性能指标
#[derive(Debug)]
pub struct DatabaseMetricsCollector {
    /// 配置
    config: DatabaseMetricsConfig,
    /// 查询总数
    total_queries: AtomicU64,
    /// 慢查询总数
    slow_queries: AtomicU64,
    /// 数据库错误总数
    database_errors: AtomicU64,
    /// 连接池使用统计
    connection_pool_stats: Arc<Mutex<ConnectionPoolStats>>,
    /// 查询统计信息
    query_stats: Arc<Mutex<HashMap<String, QueryStats>>>,
    /// 表级别统计
    table_stats: Arc<Mutex<HashMap<String, TableStats>>>,
}

/// 连接池统计信息
///
/// 【功能】：记录数据库连接池的使用情况
#[derive(Debug, Clone)]
pub struct ConnectionPoolStats {
    /// 活跃连接数
    pub active_connections: u32,
    /// 空闲连接数
    pub idle_connections: u32,
    /// 最大连接数
    pub max_connections: u32,
    /// 连接获取等待次数
    pub connection_waits: u64,
    /// 连接超时次数
    pub connection_timeouts: u64,
    /// 连接泄漏次数
    pub connection_leaks: u64,
}

/// 表级别统计信息
///
/// 【功能】：记录特定表的操作统计
#[derive(Debug, Clone)]
pub struct TableStats {
    /// SELECT操作次数
    pub select_count: u64,
    /// INSERT操作次数
    pub insert_count: u64,
    /// UPDATE操作次数
    pub update_count: u64,
    /// DELETE操作次数
    pub delete_count: u64,
    /// 平均查询时间（毫秒）
    pub avg_query_time_ms: f64,
    /// 最后访问时间
    pub last_access_time: std::time::SystemTime,
}

impl DatabaseMetricsCollector {
    /// 创建新的数据库性能指标收集器
    ///
    /// 【功能】：初始化数据库性能监控系统
    ///
    /// # 参数
    /// * `config` - 数据库性能监控配置
    ///
    /// # 返回值
    /// * `Arc<DatabaseMetricsCollector>` - 数据库性能指标收集器实例
    pub fn new(config: DatabaseMetricsConfig) -> Arc<Self> {
        // 注册数据库相关的Prometheus指标
        if config.enable_database_metrics {
            Self::register_database_prometheus_metrics();
        }

        Arc::new(Self {
            config,
            total_queries: AtomicU64::new(0),
            slow_queries: AtomicU64::new(0),
            database_errors: AtomicU64::new(0),
            connection_pool_stats: Arc::new(Mutex::new(ConnectionPoolStats {
                active_connections: 0,
                idle_connections: 0,
                max_connections: 0,
                connection_waits: 0,
                connection_timeouts: 0,
                connection_leaks: 0,
            })),
            query_stats: Arc::new(Mutex::new(HashMap::new())),
            table_stats: Arc::new(Mutex::new(HashMap::new())),
        })
    }

    /// 注册数据库相关的Prometheus指标
    ///
    /// 【功能】：注册所有数据库相关的Prometheus指标
    fn register_database_prometheus_metrics() {
        use metrics::{describe_counter, describe_gauge, describe_histogram};

        // 数据库查询指标
        describe_counter!(
            "database_queries_total",
            "数据库查询总数，按操作类型和表名分类"
        );
        describe_histogram!(
            "database_query_duration_seconds",
            "数据库查询时间分布（秒）"
        );
        describe_counter!("database_slow_queries_total", "慢查询总数，按表名分类");
        describe_counter!("database_errors_total", "数据库错误总数，按错误类型分类");

        // 连接池指标
        describe_gauge!("database_connections_active", "数据库活跃连接数");
        describe_gauge!("database_connections_idle", "数据库空闲连接数");
        describe_gauge!("database_connections_max", "数据库最大连接数");
        describe_gauge!("database_connection_pool_usage_ratio", "数据库连接池使用率");
        describe_counter!("database_connection_waits_total", "数据库连接等待总数");
        describe_counter!("database_connection_timeouts_total", "数据库连接超时总数");
        describe_counter!("database_connection_leaks_total", "数据库连接泄漏总数");

        // 表级别指标
        describe_counter!(
            "database_table_operations_total",
            "数据库表操作总数，按表名和操作类型分类"
        );
        describe_histogram!(
            "database_table_query_duration_seconds",
            "数据库表查询时间分布（秒）"
        );

        info!("✅ 数据库性能Prometheus指标注册完成");
    }

    /// 记录数据库查询开始
    ///
    /// 【功能】：在数据库查询开始时记录基础指标
    ///
    /// # 参数
    /// * `query_type` - 查询类型
    /// * `table_name` - 表名（可选）
    /// * `sql` - SQL语句（可选）
    ///
    /// # 返回值
    /// * `DatabaseQueryMetrics` - 数据库查询指标对象
    pub fn start_database_query(
        &self,
        query_type: String,
        table_name: Option<String>,
        sql: Option<String>,
    ) -> DatabaseQueryMetrics {
        self.total_queries.fetch_add(1, Ordering::Relaxed);

        // 更新Prometheus指标
        if self.config.enable_database_metrics {
            use metrics::counter;
            counter!("database_queries_total",
                "query_type" => query_type.clone(),
                "table" => table_name.as_deref().unwrap_or("unknown").to_string()
            )
            .increment(1);
        }

        DatabaseQueryMetrics {
            query_type: query_type.clone(),
            table_name: table_name.clone(),
            start_time: Instant::now(),
            sql: if self.config.log_query_parameters {
                sql.clone()
            } else {
                None
            },
            affected_rows: None,
            used_index: None,
            connection_id: None,
        }
    }

    /// 记录数据库查询完成
    ///
    /// 【功能】：在数据库查询完成时记录详细指标
    ///
    /// # 参数
    /// * `metrics` - 数据库查询指标对象
    pub fn finish_database_query(&self, metrics: DatabaseQueryMetrics) {
        let duration = metrics.start_time.elapsed();
        let duration_ms = duration.as_millis() as u64;
        let duration_seconds = duration.as_secs_f64();

        // 检查是否为慢查询
        if self.config.enable_slow_query_detection
            && duration_ms > self.config.slow_query_threshold_ms
        {
            self.slow_queries.fetch_add(1, Ordering::Relaxed);
            warn!(
                query_type = %metrics.query_type,
                table_name = ?metrics.table_name,
                duration_ms = duration_ms,
                threshold_ms = self.config.slow_query_threshold_ms,
                sql = ?metrics.sql,
                "慢查询检测到 - 需要优化数据库查询"
            );

            // 更新慢查询Prometheus指标
            if self.config.enable_database_metrics {
                use metrics::counter;
                let table_name = metrics
                    .table_name
                    .as_deref()
                    .unwrap_or("unknown")
                    .to_string();
                counter!("database_slow_queries_total",
                    "table" => table_name
                )
                .increment(1);
            }
        }

        // 更新查询统计
        self.update_query_stats(&metrics, duration_ms);

        // 更新表级别统计
        if let Some(table_name) = &metrics.table_name {
            self.update_table_stats(table_name, &metrics.query_type, duration_ms);
        }

        // 更新Prometheus指标
        if self.config.enable_database_metrics {
            self.update_database_prometheus_metrics(&metrics, duration_seconds);
        }

        debug!(
            query_type = %metrics.query_type,
            table_name = ?metrics.table_name,
            duration_ms = duration_ms,
            affected_rows = ?metrics.affected_rows,
            used_index = ?metrics.used_index,
            "数据库查询完成"
        );
    }

    /// 更新查询统计信息
    ///
    /// 【功能】：更新特定查询的统计信息
    fn update_query_stats(&self, metrics: &DatabaseQueryMetrics, duration_ms: u64) {
        let query_key = format!(
            "{}:{}",
            metrics.query_type,
            metrics.table_name.as_deref().unwrap_or("unknown")
        );

        let mut query_stats = self.query_stats.lock();
        let stats = query_stats.entry(query_key).or_insert_with(|| QueryStats {
            execution_count: 0,
            avg_execution_time_ms: 0.0,
            min_execution_time_ms: u64::MAX,
            max_execution_time_ms: 0,
            error_count: 0,
            last_execution_time: std::time::SystemTime::now(),
        });

        // 更新统计信息
        let old_count = stats.execution_count;
        stats.execution_count += 1;

        // 计算移动平均值
        stats.avg_execution_time_ms = (stats.avg_execution_time_ms * (old_count as f64)
            + (duration_ms as f64))
            / (stats.execution_count as f64);

        // 更新最小/最大值
        stats.min_execution_time_ms = stats.min_execution_time_ms.min(duration_ms);
        stats.max_execution_time_ms = stats.max_execution_time_ms.max(duration_ms);

        stats.last_execution_time = std::time::SystemTime::now();

        // 限制缓存大小
        if query_stats.len() > self.config.query_stats_cache_size {
            // 移除最旧的条目
            if let Some((oldest_key, _)) = query_stats
                .iter()
                .min_by_key(|(_, stats)| stats.last_execution_time)
            {
                let oldest_key = oldest_key.clone();
                query_stats.remove(&oldest_key);
            }
        }
    }

    /// 更新表级别统计
    ///
    /// 【功能】：更新特定表的操作统计
    fn update_table_stats(&self, table_name: &str, query_type: &str, duration_ms: u64) {
        let mut table_stats = self.table_stats.lock();
        let stats = table_stats
            .entry(table_name.to_string())
            .or_insert_with(|| TableStats {
                select_count: 0,
                insert_count: 0,
                update_count: 0,
                delete_count: 0,
                avg_query_time_ms: 0.0,
                last_access_time: std::time::SystemTime::now(),
            });

        // 更新操作计数
        match query_type.to_uppercase().as_str() {
            "SELECT" => {
                stats.select_count += 1;
            }
            "INSERT" => {
                stats.insert_count += 1;
            }
            "UPDATE" => {
                stats.update_count += 1;
            }
            "DELETE" => {
                stats.delete_count += 1;
            }
            _ => {} // 其他操作类型
        }

        // 更新平均查询时间
        let total_operations =
            stats.select_count + stats.insert_count + stats.update_count + stats.delete_count;
        if total_operations > 1 {
            stats.avg_query_time_ms = (stats.avg_query_time_ms * ((total_operations - 1) as f64)
                + (duration_ms as f64))
                / (total_operations as f64);
        } else {
            stats.avg_query_time_ms = duration_ms as f64;
        }

        stats.last_access_time = std::time::SystemTime::now();
    }

    /// 更新数据库Prometheus指标
    ///
    /// 【功能】：更新所有相关的数据库Prometheus指标
    fn update_database_prometheus_metrics(
        &self,
        metrics: &DatabaseQueryMetrics,
        duration_seconds: f64,
    ) {
        use metrics::{counter, histogram};

        // 记录查询执行时间
        let table_name = metrics
            .table_name
            .as_deref()
            .unwrap_or("unknown")
            .to_string();
        histogram!("database_query_duration_seconds",
            "query_type" => metrics.query_type.clone(),
            "table" => table_name.clone()
        )
        .record(duration_seconds);

        // 记录表级别操作
        if let Some(table_name_ref) = &metrics.table_name {
            counter!("database_table_operations_total",
                "table" => table_name_ref.clone(),
                "operation" => metrics.query_type.clone()
            )
            .increment(1);

            histogram!("database_table_query_duration_seconds",
                "table" => table_name_ref.clone()
            )
            .record(duration_seconds);
        }
    }

    /// 记录数据库错误
    ///
    /// 【功能】：记录数据库操作过程中发生的错误
    ///
    /// # 参数
    /// * `error_type` - 错误类型
    /// * `query_type` - 查询类型
    /// * `table_name` - 表名（可选）
    /// * `error_message` - 错误消息
    pub fn record_database_error(
        &self,
        error_type: String,
        query_type: String,
        table_name: Option<String>,
        error_message: String,
    ) {
        self.database_errors.fetch_add(1, Ordering::Relaxed);

        // 更新查询统计中的错误计数
        let query_key = format!(
            "{}:{}",
            query_type,
            table_name.as_deref().unwrap_or("unknown")
        );
        if let Some(stats) = self.query_stats.lock().get_mut(&query_key) {
            stats.error_count += 1;
        }

        if self.config.enable_database_metrics {
            use metrics::counter;
            let table_name_str = table_name.as_deref().unwrap_or("unknown").to_string();
            counter!("database_errors_total",
                "error_type" => error_type.clone(),
                "query_type" => query_type.clone(),
                "table" => table_name_str
            )
            .increment(1);
        }

        error!(
            error_type = %error_type,
            query_type = %query_type,
            table_name = ?table_name,
            error_message = %error_message,
            "数据库错误记录"
        );
    }

    /// 更新连接池统计
    ///
    /// 【功能】：更新数据库连接池的统计信息
    ///
    /// # 参数
    /// * `active` - 活跃连接数
    /// * `idle` - 空闲连接数
    /// * `max` - 最大连接数
    pub fn update_connection_pool_stats(&self, active: u32, idle: u32, max: u32) {
        if !self.config.enable_connection_pool_monitoring {
            return;
        }

        {
            let mut stats = self.connection_pool_stats.lock();
            stats.active_connections = active;
            stats.idle_connections = idle;
            stats.max_connections = max;
        }

        // 计算连接池使用率
        let usage_ratio = if max > 0 {
            (active as f64) / (max as f64)
        } else {
            0.0
        };

        // 检查连接池使用率告警
        if usage_ratio > self.config.connection_pool_warning_threshold {
            warn!(
                active_connections = active,
                max_connections = max,
                usage_ratio = usage_ratio,
                threshold = self.config.connection_pool_warning_threshold,
                "数据库连接池使用率过高 - 可能需要调整连接池配置"
            );
        }

        // 更新Prometheus指标
        if self.config.enable_database_metrics {
            use metrics::gauge;
            gauge!("database_connections_active").set(active as f64);
            gauge!("database_connections_idle").set(idle as f64);
            gauge!("database_connections_max").set(max as f64);
            gauge!("database_connection_pool_usage_ratio").set(usage_ratio);
        }

        debug!(
            active_connections = active,
            idle_connections = idle,
            max_connections = max,
            usage_ratio = usage_ratio,
            "连接池统计更新"
        );
    }
}
