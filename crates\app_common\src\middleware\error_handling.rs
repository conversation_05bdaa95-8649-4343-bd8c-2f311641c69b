//! # 错误处理中间件
//!
//! 提供全局错误处理、超时处理和错误恢复机制
//!
//! 这个模块实现了企业级的错误处理中间件，包括：
//! - 全局错误捕获和格式化
//! - 请求超时处理
//! - 错误上下文跟踪
//! - 结构化错误响应

use axum::{extract::Request, middleware::Next, response::Response};
use std::time::Duration;
use tower::timeout::TimeoutLayer;
use tracing::{info, instrument, warn};

use crate::error::AppError;
use crate::utils::error_response::{ErrorHandler, ErrorResponseBuilder};

/// 创建超时中间件层
pub fn timeout_layer() -> TimeoutLayer {
    TimeoutLayer::new(Duration::from_secs(30))
}

/// 全局错误处理函数
///
/// 【功能】：处理超时错误，将其转换为适当的HTTP响应
///
/// # 参数
/// * `error` - 捕获到的错误
///
/// # 返回值
/// * `Response` - 格式化的错误响应
#[instrument(skip(error), fields(error_type = %error))]
pub async fn handle_timeout_error(
    error: tower::BoxError,
) -> Result<Response, std::convert::Infallible> {
    // 使用统一的错误处理器
    ErrorHandler::handle_box_error(error, ErrorResponseBuilder::internal_server_error()).await
}

/// 验证错误处理器
///
/// 【功能】：专门处理验证相关的错误
///
/// # 参数
/// * `error` - 验证错误
///
/// # 返回值
/// * `Response` - 格式化的验证错误响应
#[instrument(skip(error))]
pub async fn handle_validation_error(
    error: tower::BoxError,
) -> Result<Response, std::convert::Infallible> {
    // 使用统一的错误响应构建器
    let response = ErrorResponseBuilder::validation_error(error.to_string()).build();
    Ok(response)
}

/// 认证错误处理器
///
/// 【功能】：专门处理认证相关的错误
///
/// # 参数
/// * `error` - 认证错误
///
/// # 返回值
/// * `Response` - 格式化的认证错误响应
#[instrument(skip(_error))]
pub async fn handle_auth_error(
    _error: tower::BoxError,
) -> Result<Response, std::convert::Infallible> {
    // 使用统一的错误响应构建器
    let response = ErrorResponseBuilder::authentication_error().build();
    Ok(response)
}

/// 错误恢复中间件
///
/// 【功能】：为HTTP请求提供错误恢复能力
///
/// # 参数
/// * `request` - HTTP请求
/// * `next` - 下一个中间件或处理器
///
/// # 返回值
/// * `Result<Response>` - HTTP响应
#[instrument(skip(request, next))]
pub async fn error_recovery_middleware(request: Request, next: Next) -> Result<Response, AppError> {
    let uri = request.uri().clone();
    let method = request.method().clone();
    let service_name = format!("{}:{}", method, uri.path());

    info!(
        method = %method,
        uri = %uri,
        service_name = %service_name,
        "Processing request with error recovery"
    );

    // 执行请求并记录错误恢复统计
    let start_time = std::time::Instant::now();
    let response = next.run(request).await;
    let elapsed = start_time.elapsed();

    let status = response.status();
    if status.is_server_error() {
        // 记录服务器错误，用于错误恢复统计
        warn!(
            method = %method,
            uri = %uri,
            status = %status,
            elapsed_ms = elapsed.as_millis(),
            "Server error detected, updating recovery stats"
        );
    } else {
        info!(
            method = %method,
            uri = %uri,
            status = %status,
            elapsed_ms = elapsed.as_millis(),
            "Request processed successfully"
        );
    }

    Ok(response)
}

/// 增强的错误处理中间件层
///
/// 【功能】：创建一个包含超时处理的中间件层
pub fn enhanced_error_handling_layer() -> TimeoutLayer {
    timeout_layer()
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{
        Router,
        body::Body,
        http::{Method, Request, StatusCode},
        middleware,
        routing::get,
    };
    use serde_json::Value;
    use tower::ServiceExt;

    // 测试辅助函数：创建测试路由
    fn create_test_router() -> Router {
        Router::new()
            .route("/success", get(|| async { "success" }))
            .route(
                "/error",
                get(|| async {
                    Err::<String, AppError>(AppError::BadRequest("测试错误".to_string()))
                }),
            )
            .layer(middleware::from_fn(error_recovery_middleware))
    }

    #[tokio::test]
    async fn test_successful_request() {
        let app = create_test_router();

        let request = Request::builder()
            .method(Method::GET)
            .uri("/success")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_app_error_handling() {
        let app = create_test_router();

        let request = Request::builder()
            .method(Method::GET)
            .uri("/error")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::BAD_REQUEST);
    }

    #[tokio::test]
    async fn test_timeout_error_handler() {
        let timeout_error = tower::timeout::error::Elapsed::new();
        let boxed_error: tower::BoxError = Box::new(timeout_error);

        let response = handle_timeout_error(boxed_error).await.unwrap();
        assert_eq!(response.status(), StatusCode::REQUEST_TIMEOUT);

        // 验证响应体格式
        let body = axum::body::to_bytes(response.into_body(), usize::MAX)
            .await
            .unwrap();
        let json: Value = serde_json::from_slice(&body).unwrap();

        assert_eq!(json["error"]["code"], "REQUEST_TIMEOUT");
        assert_eq!(json["error"]["status"], 408);
    }

    #[tokio::test]
    async fn test_validation_error_handler() {
        let validation_error =
            std::io::Error::new(std::io::ErrorKind::InvalidInput, "测试验证错误");
        let boxed_error: tower::BoxError = Box::new(validation_error);

        let response = handle_validation_error(boxed_error).await.unwrap();
        assert_eq!(response.status(), StatusCode::BAD_REQUEST);

        // 验证响应体格式
        let body = axum::body::to_bytes(response.into_body(), usize::MAX)
            .await
            .unwrap();
        let json: Value = serde_json::from_slice(&body).unwrap();

        assert_eq!(json["error"]["code"], "VALIDATION_ERROR");
        assert_eq!(json["error"]["status"], 400);
    }

    #[tokio::test]
    async fn test_auth_error_handler() {
        let auth_error = std::io::Error::new(std::io::ErrorKind::PermissionDenied, "测试认证错误");
        let boxed_error: tower::BoxError = Box::new(auth_error);

        let response = handle_auth_error(boxed_error).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);

        // 验证响应体格式
        let body = axum::body::to_bytes(response.into_body(), usize::MAX)
            .await
            .unwrap();
        let json: Value = serde_json::from_slice(&body).unwrap();

        assert_eq!(json["error"]["code"], "AUTHENTICATION_ERROR");
        assert_eq!(json["error"]["status"], 401);
    }
}
