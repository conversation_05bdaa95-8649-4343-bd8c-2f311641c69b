//! # 集成性能监控系统
//!
//! 将所有性能监控模块整合在一起，提供统一的监控接口：
//! - HTTP请求性能监控
//! - 搜索功能性能监控
//! - 数据库操作性能监控
//! - 队列任务性能监控
//! - 告警管理系统
//! - Prometheus指标导出

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{info, warn};

use super::{
    AlertManager, AlertManagerConfig, AlertRule, DatabaseMetricsCollector, DatabaseMetricsConfig,
    PerformanceConfig, PerformanceMetrics, QueueMetricsCollector, QueueMetricsConfig,
    SearchMetricsCollector, SearchMetricsConfig, create_default_performance_alert_rules,
};

/// 集成监控系统配置
///
/// 【功能】：配置整个监控系统的参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegratedMonitoringConfig {
    /// 是否启用监控系统
    pub enable_monitoring: bool,
    /// HTTP性能监控配置
    pub performance_config: PerformanceConfig,
    /// 搜索性能监控配置
    pub search_metrics_config: SearchMetricsConfig,
    /// 数据库性能监控配置
    pub database_metrics_config: DatabaseMetricsConfig,
    /// 队列性能监控配置
    pub queue_metrics_config: QueueMetricsConfig,
    /// 告警管理配置
    pub alert_manager_config: AlertManagerConfig,
    /// Prometheus导出器绑定地址
    pub prometheus_bind_address: String,
}

impl Default for IntegratedMonitoringConfig {
    fn default() -> Self {
        Self {
            enable_monitoring: true,
            performance_config: PerformanceConfig::default(),
            search_metrics_config: SearchMetricsConfig::default(),
            database_metrics_config: DatabaseMetricsConfig::default(),
            queue_metrics_config: QueueMetricsConfig::default(),
            alert_manager_config: AlertManagerConfig::default(),
            prometheus_bind_address: "0.0.0.0:9090".to_string(),
        }
    }
}

/// 集成性能监控系统
///
/// 【功能】：统一管理所有性能监控组件
#[derive(Debug)]
pub struct IntegratedMonitoringSystem {
    /// 配置
    config: IntegratedMonitoringConfig,
    /// HTTP性能监控
    performance_metrics: Arc<PerformanceMetrics>,
    /// 搜索性能监控
    search_metrics: Arc<SearchMetricsCollector>,
    /// 数据库性能监控
    database_metrics: Arc<DatabaseMetricsCollector>,
    /// 队列性能监控
    queue_metrics: Arc<QueueMetricsCollector>,
    /// 告警管理器
    alert_manager: Arc<AlertManager>,
}

impl IntegratedMonitoringSystem {
    /// 创建新的集成监控系统
    ///
    /// 【功能】：初始化整个监控系统
    ///
    /// # 参数
    /// * `config` - 集成监控系统配置
    ///
    /// # 返回值
    /// * `Result<Arc<IntegratedMonitoringSystem>, Box<dyn std::error::Error>>` - 监控系统实例
    pub fn new(
        config: IntegratedMonitoringConfig,
    ) -> Result<Arc<Self>, Box<dyn std::error::Error>> {
        if !config.enable_monitoring {
            info!("监控系统已禁用");
            // 返回一个禁用的监控系统实例
        }

        info!("🚀 初始化集成性能监控系统...");

        // 初始化各个监控组件
        let performance_metrics = PerformanceMetrics::new(config.performance_config.clone());
        let search_metrics = SearchMetricsCollector::new(config.search_metrics_config.clone());
        let database_metrics =
            DatabaseMetricsCollector::new(config.database_metrics_config.clone());
        let queue_metrics = QueueMetricsCollector::new(config.queue_metrics_config.clone());
        let alert_manager = AlertManager::new(config.alert_manager_config.clone());

        // 设置默认告警规则
        let default_rules = create_default_performance_alert_rules();
        for rule in default_rules {
            alert_manager.add_rule(rule);
        }

        // 初始化Prometheus导出器
        if let Err(e) = super::init_prometheus_exporter(&config.prometheus_bind_address) {
            warn!(
                error = %e,
                bind_address = %config.prometheus_bind_address,
                "Prometheus导出器初始化失败，继续使用内部指标"
            );
        }

        let system = Arc::new(Self {
            config,
            performance_metrics,
            search_metrics,
            database_metrics,
            queue_metrics,
            alert_manager,
        });

        info!("✅ 集成性能监控系统初始化完成");
        Ok(system)
    }

    /// 获取HTTP性能监控器
    ///
    /// 【功能】：获取HTTP请求性能监控组件
    ///
    /// # 返回值
    /// * `Arc<PerformanceMetrics>` - HTTP性能监控器
    pub fn performance_metrics(&self) -> Arc<PerformanceMetrics> {
        self.performance_metrics.clone()
    }

    /// 获取搜索性能监控器
    ///
    /// 【功能】：获取搜索功能性能监控组件
    ///
    /// # 返回值
    /// * `Arc<SearchMetricsCollector>` - 搜索性能监控器
    pub fn search_metrics(&self) -> Arc<SearchMetricsCollector> {
        self.search_metrics.clone()
    }

    /// 获取数据库性能监控器
    ///
    /// 【功能】：获取数据库操作性能监控组件
    ///
    /// # 返回值
    /// * `Arc<DatabaseMetricsCollector>` - 数据库性能监控器
    pub fn database_metrics(&self) -> Arc<DatabaseMetricsCollector> {
        self.database_metrics.clone()
    }

    /// 获取队列性能监控器
    ///
    /// 【功能】：获取队列任务性能监控组件
    ///
    /// # 返回值
    /// * `Arc<QueueMetricsCollector>` - 队列性能监控器
    pub fn queue_metrics(&self) -> Arc<QueueMetricsCollector> {
        self.queue_metrics.clone()
    }

    /// 获取告警管理器
    ///
    /// 【功能】：获取告警管理组件
    ///
    /// # 返回值
    /// * `Arc<AlertManager>` - 告警管理器
    pub fn alert_manager(&self) -> Arc<AlertManager> {
        self.alert_manager.clone()
    }

    /// 获取监控系统健康状态
    ///
    /// 【功能】：获取整个监控系统的健康状态报告
    ///
    /// # 返回值
    /// * `MonitoringHealthReport` - 健康状态报告
    pub fn get_health_report(&self) -> MonitoringHealthReport {
        let performance_stats = self.performance_metrics.get_stats();
        let search_stats = self.search_metrics.get_performance_stats();
        let queue_stats = self.queue_metrics.get_performance_stats();
        let alert_stats = self.alert_manager.get_alert_stats();

        MonitoringHealthReport {
            system_enabled: self.config.enable_monitoring,
            total_http_requests: performance_stats.total_requests,
            total_search_requests: search_stats.total_searches,
            total_queue_tasks: queue_stats.total_tasks,
            active_alerts: alert_stats.active_alerts_count,
            critical_alerts: alert_stats.critical_alerts,
            search_cache_hit_ratio: search_stats.cache_hit_ratio,
            queue_success_rate: queue_stats.overall_success_rate,
            prometheus_enabled: !self.config.prometheus_bind_address.is_empty(),
        }
    }

    /// 执行监控系统维护任务
    ///
    /// 【功能】：执行定期维护任务，如清理过期数据、更新统计等
    pub async fn perform_maintenance(&self) {
        info!("🔧 执行监控系统维护任务...");

        // 清理过期的搜索词统计
        self.search_metrics
            .cleanup_expired_terms(std::time::Duration::from_secs(7 * 24 * 3600)); // 7天

        // 清理过期的告警历史
        self.alert_manager.cleanup_expired_alerts();

        // 可以添加更多维护任务...

        info!("✅ 监控系统维护任务完成");
    }

    /// 添加自定义告警规则
    ///
    /// 【功能】：向告警管理器添加自定义告警规则
    ///
    /// # 参数
    /// * `rule` - 告警规则
    pub fn add_alert_rule(&self, rule: AlertRule) {
        self.alert_manager.add_rule(rule);
    }

    /// 评估指标并触发告警
    ///
    /// 【功能】：评估指标值并根据规则触发告警
    ///
    /// # 参数
    /// * `metric_name` - 指标名称
    /// * `value` - 指标值
    /// * `labels` - 指标标签
    pub fn evaluate_metric(
        &self,
        metric_name: &str,
        value: f64,
        labels: &std::collections::HashMap<String, String>,
    ) {
        self.alert_manager
            .evaluate_metric(metric_name, value, labels);
    }

    /// 创建企业级监控配置
    ///
    /// 【功能】：创建适合企业级应用的监控配置
    ///
    /// # 返回值
    /// * `IntegratedMonitoringConfig` - 企业级监控配置
    pub fn create_enterprise_config() -> IntegratedMonitoringConfig {
        IntegratedMonitoringConfig {
            enable_monitoring: true,
            performance_config: PerformanceConfig {
                enable_prometheus_metrics: true,
                enable_detailed_logging: true,
                enable_geo_stats: true,
                enable_user_agent_stats: true,
                enable_error_classification: true,
                slow_request_threshold_ms: 1000, // 1秒
                enable_system_monitoring: true,
                system_monitoring_interval: 30,
                log_request_headers: false, // 生产环境不记录请求头
                max_concurrent_connections_warning: 1000,
                enable_size_monitoring: true,
                max_user_agent_cache_size: 1000,
                max_geo_cache_size: 500,
            },
            search_metrics_config: SearchMetricsConfig {
                enable_search_metrics: true,
                enable_cache_monitoring: true,
                enable_popular_terms_tracking: true,
                slow_search_threshold_ms: 500, // 500毫秒
                popular_terms_cache_size: 1000,
                quality_score_threshold: 0.8,
            },
            database_metrics_config: DatabaseMetricsConfig {
                enable_database_metrics: true,
                enable_connection_pool_monitoring: true,
                enable_slow_query_detection: true,
                slow_query_threshold_ms: 1000,          // 1秒
                connection_pool_warning_threshold: 0.8, // 80%
                query_stats_cache_size: 500,
                log_query_parameters: false, // 生产环境不记录参数
            },
            queue_metrics_config: QueueMetricsConfig {
                enable_queue_metrics: true,
                enable_queue_length_alerts: true,
                enable_processing_time_monitoring: true,
                queue_length_warning_threshold: 100,
                queue_length_critical_threshold: 500,
                slow_task_threshold_ms: 5000,         // 5秒
                failure_rate_warning_threshold: 0.05, // 5%
                queue_stats_cache_size: 100,
            },
            alert_manager_config: AlertManagerConfig {
                enable_alerting: true,
                evaluation_interval_seconds: 30,
                alert_history_retention_seconds: 7 * 24 * 3600, // 7天
                max_active_alerts: 1000,
                enable_deduplication: true,
                deduplication_window_seconds: 300, // 5分钟
                enable_suppression: true,
            },
            prometheus_bind_address: "0.0.0.0:9090".to_string(),
        }
    }
}

/// 监控系统健康状态报告
///
/// 【功能】：包含监控系统整体健康状态的报告
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringHealthReport {
    /// 监控系统是否启用
    pub system_enabled: bool,
    /// HTTP请求总数
    pub total_http_requests: u64,
    /// 搜索请求总数
    pub total_search_requests: u64,
    /// 队列任务总数
    pub total_queue_tasks: u64,
    /// 活跃告警数
    pub active_alerts: usize,
    /// 严重告警数
    pub critical_alerts: u64,
    /// 搜索缓存命中率
    pub search_cache_hit_ratio: f64,
    /// 队列成功率
    pub queue_success_rate: f64,
    /// Prometheus是否启用
    pub prometheus_enabled: bool,
}

/// 创建集成监控系统
///
/// 【功能】：创建一个完整的集成监控系统实例
///
/// # 参数
/// * `config` - 集成监控系统配置
///
/// # 返回值
/// * `Result<Arc<IntegratedMonitoringSystem>, Box<dyn std::error::Error>>` - 监控系统实例
pub fn create_integrated_monitoring_system(
    config: IntegratedMonitoringConfig,
) -> Result<Arc<IntegratedMonitoringSystem>, Box<dyn std::error::Error>> {
    IntegratedMonitoringSystem::new(config)
}

/// 创建默认的集成监控系统
///
/// 【功能】：使用默认配置创建集成监控系统
///
/// # 返回值
/// * `Result<Arc<IntegratedMonitoringSystem>, Box<dyn std::error::Error>>` - 监控系统实例
pub fn create_default_monitoring_system()
-> Result<Arc<IntegratedMonitoringSystem>, Box<dyn std::error::Error>> {
    let config = IntegratedMonitoringConfig::default();
    IntegratedMonitoringSystem::new(config)
}

/// 创建企业级监控系统
///
/// 【功能】：使用企业级配置创建集成监控系统
///
/// # 返回值
/// * `Result<Arc<IntegratedMonitoringSystem>, Box<dyn std::error::Error>>` - 监控系统实例
pub fn create_enterprise_monitoring_system()
-> Result<Arc<IntegratedMonitoringSystem>, Box<dyn std::error::Error>> {
    let config = IntegratedMonitoringSystem::create_enterprise_config();
    IntegratedMonitoringSystem::new(config)
}
