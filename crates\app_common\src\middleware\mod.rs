//! # 中间件模块
//!
//! 提供应用程序的中间件组件，包括：
//! - JWT认证中间件
//! - 性能监控中间件
//! - 错误处理中间件
//! - 请求提取器
//! - 安全验证组件

pub mod alert_manager;
pub mod auth_middleware;
pub mod database_metrics;
pub mod error_handling;
pub mod integrated_monitoring;
pub mod performance_monitor;
pub mod queue_metrics;
pub mod search_metrics;
pub mod versioning_middleware;

// 重新导出常用类型
pub use alert_manager::{
    AlertEvent, AlertLevel, AlertManager, AlertManagerConfig, AlertRule, AlertStatsSummary,
    AlertStatus, create_alert_manager, create_default_performance_alert_rules,
};
pub use auth_middleware::{AuthenticatedUser, JwtAuthError, JwtAuthState, jwt_auth_middleware};
pub use database_metrics::{
    ConnectionPoolStats, DatabaseMetricsCollector, DatabaseMetricsConfig, DatabaseQueryMetrics,
    QueryStats, TableStats,
};
pub use error_handling::{
    enhanced_error_handling_layer, error_recovery_middleware, handle_auth_error,
    handle_timeout_error, handle_validation_error, timeout_layer,
};
pub use integrated_monitoring::{
    IntegratedMonitoringConfig, IntegratedMonitoringSystem, MonitoringHealthReport,
    create_default_monitoring_system, create_enterprise_monitoring_system,
    create_integrated_monitoring_system,
};
pub use performance_monitor::{
    ErrorClassification, GeoStats, PerformanceConfig, PerformanceMetrics, PerformanceStats,
    RequestEndMetrics, RequestMetrics, UserAgentStats, create_performance_monitoring_layer,
    init_prometheus_exporter, performance_monitoring_middleware,
};
pub use queue_metrics::{
    QueueMetricsCollector, QueueMetricsConfig, QueuePerformanceStats, QueueStats, QueueTaskMetrics,
    TaskTypeStats, create_queue_metrics_collector,
};
pub use search_metrics::{
    PopularTermStats, SearchMetricsCollector, SearchMetricsConfig, SearchPerformanceStats,
    SearchRequestMetrics, create_search_metrics_collector,
};
pub use versioning_middleware::{ApiVersionExtractor, VersioningMiddleware, versioning_middleware};
