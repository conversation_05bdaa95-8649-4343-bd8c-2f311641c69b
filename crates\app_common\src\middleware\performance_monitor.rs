//! # 性能监控中间件
//!
//! 提供HTTP请求性能监控功能，包括：
//! - 请求延迟指标收集
//! - 系统资源监控
//! - 慢请求告警
//! - 并发连接数监控

use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant};

use axum::{
    extract::{Request, State},
    http::{HeaderMap, StatusCode},
    middleware::Next,
    response::Response,
};
use parking_lot::Mutex;
use serde::{Deserialize, Serialize};
use sysinfo::System;
use tokio::time::interval;
use tracing::{error, info, instrument, warn};

/// 性能监控配置
///
/// 【功能】：配置性能监控中间件的行为参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    /// 是否启用详细的请求日志
    pub enable_detailed_logging: bool,
    /// 是否启用系统资源监控
    pub enable_system_monitoring: bool,
    /// 系统监控采样间隔（秒）
    pub system_monitoring_interval: u64,
    /// 是否启用Prometheus指标导出
    pub enable_prometheus_metrics: bool,
    /// 慢请求阈值（毫秒）
    pub slow_request_threshold_ms: u64,
    /// 是否记录请求头信息
    pub log_request_headers: bool,
    /// 最大并发连接数告警阈值
    pub max_concurrent_connections_warning: u64,
    /// 是否启用请求/响应大小监控
    pub enable_size_monitoring: bool,
    /// 是否启用用户代理统计
    pub enable_user_agent_stats: bool,
    /// 是否启用地理位置统计（基于IP）
    pub enable_geo_stats: bool,
    /// 是否启用错误分类统计
    pub enable_error_classification: bool,
    /// 用户代理统计的最大缓存条目数
    pub max_user_agent_cache_size: usize,
    /// 地理位置统计的最大缓存条目数
    pub max_geo_cache_size: usize,
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            enable_detailed_logging: true,
            enable_system_monitoring: true,
            system_monitoring_interval: 30, // 30秒采样一次
            enable_prometheus_metrics: true,
            slow_request_threshold_ms: 1000, // 1秒
            log_request_headers: false,
            max_concurrent_connections_warning: 1000,
            enable_size_monitoring: true,
            enable_user_agent_stats: true,
            enable_geo_stats: false, // 默认关闭，需要外部IP服务
            enable_error_classification: true,
            max_user_agent_cache_size: 1000,
            max_geo_cache_size: 500,
        }
    }
}

/// 用户代理统计信息
#[derive(Debug, Clone)]
pub struct UserAgentStats {
    pub count: u64,
    pub last_seen: std::time::SystemTime,
}

/// 地理位置统计信息
#[derive(Debug, Clone)]
pub struct GeoStats {
    pub count: u64,
    pub last_seen: std::time::SystemTime,
    pub country: String,
    pub city: Option<String>,
}

/// 错误分类统计
#[derive(Debug, Default)]
pub struct ErrorClassification {
    /// 4xx客户端错误计数
    pub client_errors: AtomicU64,
    /// 5xx服务器错误计数
    pub server_errors: AtomicU64,
    /// 超时错误计数
    pub timeout_errors: AtomicU64,
    /// 认证错误计数
    pub auth_errors: AtomicU64,
    /// 权限错误计数
    pub permission_errors: AtomicU64,
    /// 资源未找到错误计数
    pub not_found_errors: AtomicU64,
    /// 冲突错误计数
    pub conflict_errors: AtomicU64,
}

/// 请求结束时的指标数据
#[derive(Debug)]
pub struct RequestEndMetrics<'a> {
    pub duration: Duration,
    pub status_code: StatusCode,
    pub method: &'a str,
    pub path: &'a str,
    pub headers: Option<&'a HeaderMap>,
    pub request_size: Option<u64>,
    pub response_size: Option<u64>,
}

/// 性能指标收集器
///
/// 【功能】：集中管理所有性能指标的收集和统计
#[derive(Debug)]
pub struct PerformanceMetrics {
    /// 活跃连接数
    active_connections: AtomicU64,
    /// 总请求数
    total_requests: AtomicU64,
    /// 成功请求数
    successful_requests: AtomicU64,
    /// 错误请求数
    error_requests: AtomicU64,
    /// 总请求大小（字节）
    total_request_size: AtomicU64,
    /// 总响应大小（字节）
    total_response_size: AtomicU64,
    /// 用户代理统计
    user_agent_stats: Arc<Mutex<HashMap<String, UserAgentStats>>>,
    /// 地理位置统计（预留功能）
    #[allow(dead_code)]
    geo_stats: Arc<Mutex<HashMap<String, GeoStats>>>,
    /// 错误分类统计
    error_classification: ErrorClassification,
    /// 系统信息收集器
    system: Arc<Mutex<System>>,
    /// 配置
    config: PerformanceConfig,
}

impl PerformanceMetrics {
    /// 创建新的性能指标收集器
    ///
    /// 【功能】：初始化性能监控系统，注册所有必要的指标
    ///
    /// # 参数
    /// * `config` - 性能监控配置
    ///
    /// # 返回值
    /// * `Arc<PerformanceMetrics>` - 性能指标收集器实例
    pub fn new(config: PerformanceConfig) -> Arc<Self> {
        // 注册 Prometheus 指标
        if config.enable_prometheus_metrics {
            Self::register_prometheus_metrics();
        }

        let metrics = Arc::new(Self {
            active_connections: AtomicU64::new(0),
            total_requests: AtomicU64::new(0),
            successful_requests: AtomicU64::new(0),
            error_requests: AtomicU64::new(0),
            total_request_size: AtomicU64::new(0),
            total_response_size: AtomicU64::new(0),
            user_agent_stats: Arc::new(Mutex::new(HashMap::new())),
            geo_stats: Arc::new(Mutex::new(HashMap::new())),
            error_classification: ErrorClassification::default(),
            system: Arc::new(Mutex::new(System::new_all())),
            config,
        });

        // 启动系统监控（如果启用）
        if metrics.config.enable_system_monitoring {
            let metrics_clone = metrics.clone();
            tokio::spawn(async move {
                metrics_clone.start_system_monitoring().await;
            });
        }

        metrics
    }

    /// 注册 Prometheus 指标
    ///
    /// 【功能】：向 metrics 注册表注册所有需要的指标类型
    /// 基于2025年最新最佳实践实现完整的Prometheus指标注册
    fn register_prometheus_metrics() {
        use metrics::{describe_counter, describe_gauge, describe_histogram};

        // HTTP请求相关指标
        describe_counter!(
            "http_requests_total",
            "HTTP请求总数，按方法、路径和状态码分类"
        );

        describe_histogram!(
            "http_request_duration_seconds",
            "HTTP请求处理时间分布（秒）"
        );

        describe_gauge!("http_active_connections", "当前活跃的HTTP连接数");

        // 搜索功能相关指标
        describe_counter!("search_requests_total", "搜索请求总数，按类型分类");

        describe_histogram!("search_response_time_seconds", "搜索响应时间分布（秒）");

        describe_gauge!("search_cache_hit_ratio", "搜索缓存命中率（0-1之间）");

        describe_counter!("search_cache_hits_total", "搜索缓存命中总数");

        describe_counter!("search_cache_misses_total", "搜索缓存未命中总数");

        // 数据库相关指标
        describe_gauge!("database_connections_active", "数据库活跃连接数");

        describe_histogram!(
            "database_query_duration_seconds",
            "数据库查询时间分布（秒）"
        );

        describe_counter!("database_queries_total", "数据库查询总数，按操作类型分类");

        describe_counter!("database_slow_queries_total", "慢查询总数");

        // 队列相关指标
        describe_gauge!("queue_length", "队列长度，按队列类型分类");

        describe_histogram!(
            "queue_processing_time_seconds",
            "队列任务处理时间分布（秒）"
        );

        describe_counter!("queue_processed_total", "队列处理任务总数，按状态分类");

        // 系统资源指标
        describe_gauge!("system_memory_usage_bytes", "系统内存使用量（字节）");

        describe_gauge!("system_cpu_usage_percent", "系统CPU使用率（百分比）");

        describe_gauge!("system_disk_usage_bytes", "系统磁盘使用量（字节）");

        // WebSocket相关指标
        describe_gauge!("websocket_connections_active", "活跃WebSocket连接数");

        describe_counter!("websocket_messages_total", "WebSocket消息总数，按类型分类");

        describe_histogram!(
            "websocket_message_latency_seconds",
            "WebSocket消息延迟分布（秒）"
        );

        info!("✅ Prometheus指标注册完成 - 基于2025年最新最佳实践");
    }

    /// 记录请求开始
    ///
    /// 【功能】：在请求开始时更新相关指标
    pub fn record_request_start(&self) {
        self.active_connections.fetch_add(1, Ordering::Relaxed);
        self.total_requests.fetch_add(1, Ordering::Relaxed);

        // 更新 Prometheus 指标
        if self.config.enable_prometheus_metrics {
            use metrics::{counter, gauge};

            // 增加HTTP请求总数计数器
            counter!("http_requests_total").increment(1);

            // 更新活跃连接数
            let active_count = self.active_connections.load(Ordering::Relaxed) as f64;
            gauge!("http_active_connections").set(active_count);

            tracing::debug!(
                metric = "http_requests_total",
                active_connections = active_count,
                total_requests = self.total_requests.load(Ordering::Relaxed),
                "HTTP请求开始 - Prometheus指标已更新"
            );
        }
    }

    /// 记录请求完成
    ///
    /// 【功能】：在请求完成时更新相关指标和日志
    ///
    /// # 参数
    /// * `metrics` - 包含所有请求指标的结构体
    #[instrument(skip(self, metrics))]
    pub fn record_request_end(&self, metrics: RequestEndMetrics<'_>) {
        self.active_connections.fetch_sub(1, Ordering::Relaxed);

        let duration_ms = metrics.duration.as_millis() as u64;
        // 修正成功判断逻辑：2xx状态码 + 101 WebSocket升级都视为成功
        let is_success = metrics.status_code.is_success()
            || metrics.status_code == StatusCode::SWITCHING_PROTOCOLS;

        if is_success {
            self.successful_requests.fetch_add(1, Ordering::Relaxed);
        } else {
            self.error_requests.fetch_add(1, Ordering::Relaxed);
        }

        // 更新请求/响应大小统计
        if let Some(req_size) = metrics.request_size {
            self.total_request_size
                .fetch_add(req_size, Ordering::Relaxed);
        }
        if let Some(resp_size) = metrics.response_size {
            self.total_response_size
                .fetch_add(resp_size, Ordering::Relaxed);
        }

        // 更新 Prometheus 指标
        if self.config.enable_prometheus_metrics {
            use metrics::{counter, gauge, histogram};

            // 记录HTTP请求处理时间
            let duration_seconds = metrics.duration.as_secs_f64();
            histogram!("http_request_duration_seconds",
                "method" => metrics.method.to_string(),
                "path" => metrics.path.to_string(),
                "status" => metrics.status_code.as_u16().to_string()
            )
            .record(duration_seconds);

            // 更新HTTP请求总数（按状态码分类）
            counter!("http_requests_total",
                "method" => metrics.method.to_string(),
                "path" => metrics.path.to_string(),
                "status" => metrics.status_code.as_u16().to_string()
            )
            .increment(1);

            // 更新活跃连接数
            let active_count = self.active_connections.load(Ordering::Relaxed) as f64;
            gauge!("http_active_connections").set(active_count);
        }

        // 更新错误分类统计
        if self.config.enable_error_classification {
            self.update_error_classification(metrics.status_code);
        }

        // 更新用户代理统计
        if self.config.enable_user_agent_stats {
            if let Some(headers) = metrics.headers {
                self.update_user_agent_stats(headers);
            }
        }

        // 详细日志记录 - 企业级日志级别策略
        if self.config.enable_detailed_logging {
            self.log_request_details(&metrics, duration_ms, is_success);
        }

        // 慢请求告警
        if duration_ms > self.config.slow_request_threshold_ms {
            warn!(
                method = %metrics.method,
                path = %metrics.path,
                duration_ms = duration_ms,
                threshold_ms = self.config.slow_request_threshold_ms,
                "慢请求检测到 - 可能需要性能优化"
            );
        }

        // 并发连接数告警
        let current_connections = self.active_connections.load(Ordering::Relaxed);
        if current_connections > self.config.max_concurrent_connections_warning {
            warn!(
                active_connections = current_connections,
                warning_threshold = self.config.max_concurrent_connections_warning,
                "High concurrent connections detected"
            );
        }
    }

    /// 启动系统监控
    ///
    /// 【功能】：定期收集系统资源使用情况
    async fn start_system_monitoring(self: Arc<Self>) {
        let mut interval = interval(Duration::from_secs(self.config.system_monitoring_interval));

        loop {
            interval.tick().await;
            self.collect_system_metrics().await;
        }
    }

    /// 收集系统指标
    ///
    /// 【功能】：收集CPU、内存等系统资源使用情况
    async fn collect_system_metrics(&self) {
        let mut system = self.system.lock();
        system.refresh_all();

        // 获取内存使用情况
        let total_memory = system.total_memory();
        let used_memory = system.used_memory();
        let memory_usage_percent = ((used_memory as f64) / (total_memory as f64)) * 100.0;

        // 获取CPU使用率
        let cpu_usage = system.global_cpu_usage();

        // 记录系统指标日志
        info!(
            total_memory_mb = total_memory / 1024 / 1024,
            used_memory_mb = used_memory / 1024 / 1024,
            memory_usage_percent = memory_usage_percent,
            cpu_usage_percent = cpu_usage,
            "System metrics collected"
        );
    }

    /// 更新错误分类统计
    ///
    /// 【功能】：根据HTTP状态码更新错误分类计数器
    fn update_error_classification(&self, status_code: StatusCode) {
        let status_u16 = status_code.as_u16();

        match status_u16 {
            400..=499 => {
                self.error_classification
                    .client_errors
                    .fetch_add(1, Ordering::Relaxed);

                // 细分客户端错误类型
                match status_code {
                    StatusCode::UNAUTHORIZED => {
                        self.error_classification
                            .auth_errors
                            .fetch_add(1, Ordering::Relaxed);
                    }
                    StatusCode::FORBIDDEN => {
                        self.error_classification
                            .permission_errors
                            .fetch_add(1, Ordering::Relaxed);
                    }
                    StatusCode::NOT_FOUND => {
                        self.error_classification
                            .not_found_errors
                            .fetch_add(1, Ordering::Relaxed);
                    }
                    StatusCode::CONFLICT => {
                        self.error_classification
                            .conflict_errors
                            .fetch_add(1, Ordering::Relaxed);
                    }
                    StatusCode::REQUEST_TIMEOUT => {
                        self.error_classification
                            .timeout_errors
                            .fetch_add(1, Ordering::Relaxed);
                    }
                    _ => {}
                }
            }
            500..=599 => {
                self.error_classification
                    .server_errors
                    .fetch_add(1, Ordering::Relaxed);
            }
            _ => {}
        }
    }

    /// 更新用户代理统计
    ///
    /// 【功能】：统计不同用户代理的访问频率
    fn update_user_agent_stats(&self, headers: &HeaderMap) {
        if let Some(user_agent) = headers.get("user-agent") {
            if let Ok(user_agent_str) = user_agent.to_str() {
                let mut stats = self.user_agent_stats.lock();

                // 检查缓存大小限制
                if stats.len() >= self.config.max_user_agent_cache_size {
                    // 移除最旧的条目
                    if let Some((oldest_key, _)) = stats
                        .iter()
                        .min_by_key(|(_, v)| v.last_seen)
                        .map(|(k, v)| (k.clone(), v.clone()))
                    {
                        stats.remove(&oldest_key);
                    }
                }

                let entry = stats
                    .entry(user_agent_str.to_string())
                    .or_insert(UserAgentStats {
                        count: 0,
                        last_seen: std::time::SystemTime::now(),
                    });
                entry.count += 1;
                entry.last_seen = std::time::SystemTime::now();
            }
        }
    }

    /// 记录详细的请求日志
    ///
    /// 【功能】：根据配置记录详细的请求信息
    fn log_request_details(
        &self,
        metrics: &RequestEndMetrics<'_>,
        duration_ms: u64,
        _is_success: bool,
    ) {
        // 实现精确的日志级别策略：
        // INFO: 2xx成功状态码 + 101 WebSocket升级
        // WARN: 4xx客户端错误 + 慢请求
        // ERROR: 5xx服务器错误
        let status_u16 = metrics.status_code.as_u16();
        let is_slow_request = duration_ms > self.config.slow_request_threshold_ms;

        // 根据日志级别记录不同详细程度的信息
        if metrics.status_code.is_server_error() {
            error!(
                method = %metrics.method,
                path = %metrics.path,
                status = status_u16,
                duration_ms = duration_ms,
                active_connections = self.active_connections.load(Ordering::Relaxed),
                total_requests = self.total_requests.load(Ordering::Relaxed),
                error_requests = self.error_requests.load(Ordering::Relaxed),
                "Server error in HTTP request"
            );
        } else if metrics.status_code.is_client_error() || is_slow_request {
            warn!(
                method = %metrics.method,
                path = %metrics.path,
                status = status_u16,
                duration_ms = duration_ms,
                is_slow = is_slow_request,
                threshold_ms = self.config.slow_request_threshold_ms,
                "HTTP request warning"
            );
        } else {
            info!(
                method = %metrics.method,
                path = %metrics.path,
                status = status_u16,
                duration_ms = duration_ms,
                "HTTP request completed successfully"
            );
        }

        // 记录请求头信息（如果启用）
        if self.config.log_request_headers && metrics.headers.is_some() {
            let headers = metrics.headers.unwrap();
            if let Some(user_agent) = headers.get("user-agent") {
                if let Ok(user_agent_str) = user_agent.to_str() {
                    tracing::info!(user_agent = %user_agent_str, "Request user agent");
                }
            }
            if let Some(content_type) = headers.get("content-type") {
                if let Ok(content_type_str) = content_type.to_str() {
                    tracing::info!(content_type = %content_type_str, "Request content type");
                }
            }
        }
    }

    /// 获取当前统计信息
    ///
    /// 【功能】：返回当前的性能统计数据
    pub fn get_stats(&self) -> PerformanceStats {
        PerformanceStats {
            active_connections: self.active_connections.load(Ordering::Relaxed),
            total_requests: self.total_requests.load(Ordering::Relaxed),
            successful_requests: self.successful_requests.load(Ordering::Relaxed),
            error_requests: self.error_requests.load(Ordering::Relaxed),
            total_request_size: self.total_request_size.load(Ordering::Relaxed),
            total_response_size: self.total_response_size.load(Ordering::Relaxed),
            client_errors: self
                .error_classification
                .client_errors
                .load(Ordering::Relaxed),
            server_errors: self
                .error_classification
                .server_errors
                .load(Ordering::Relaxed),
            timeout_errors: self
                .error_classification
                .timeout_errors
                .load(Ordering::Relaxed),
            auth_errors: self
                .error_classification
                .auth_errors
                .load(Ordering::Relaxed),
            permission_errors: self
                .error_classification
                .permission_errors
                .load(Ordering::Relaxed),
            not_found_errors: self
                .error_classification
                .not_found_errors
                .load(Ordering::Relaxed),
            conflict_errors: self
                .error_classification
                .conflict_errors
                .load(Ordering::Relaxed),
        }
    }
}

/// 性能统计数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceStats {
    pub active_connections: u64,
    pub total_requests: u64,
    pub successful_requests: u64,
    pub error_requests: u64,
    pub total_request_size: u64,
    pub total_response_size: u64,
    pub client_errors: u64,
    pub server_errors: u64,
    pub timeout_errors: u64,
    pub auth_errors: u64,
    pub permission_errors: u64,
    pub not_found_errors: u64,
    pub conflict_errors: u64,
}

/// RequestMetrics - 简化的请求指标记录器
///
/// 【功能】：实现Drop trait自动记录请求延迟指标
#[derive(Debug)]
pub struct RequestMetrics {
    pub method: String,
    pub path: String,
    pub start: Instant,
}

impl Drop for RequestMetrics {
    fn drop(&mut self) {
        let duration = self.start.elapsed().as_millis() as f64;
        // 简化实现，仅记录日志
        info!(
            metric = "http_request_latency",
            method = %self.method,
            path = %self.path,
            duration_ms = duration,
            "Request latency recorded"
        );
    }
}

impl RequestMetrics {
    /// 创建新的请求指标记录器
    pub fn new(method: String, path: String) -> Self {
        Self {
            method,
            path,
            start: Instant::now(),
        }
    }
}

/// 性能监控中间件
///
/// 【功能】：Axum中间件函数，自动监控每个HTTP请求的性能指标
///
/// 注意：此函数使用 `axum::middleware::from_fn_with_state` 和 State 提取器
/// 使用 defer 模式确保即使出现异常也会记录请求完成
///
/// # 参数
/// * `metrics` - 性能指标收集器（通过 State 提取器获取）
/// * `request` - HTTP请求
/// * `next` - 下一个中间件或处理器
///
/// # 返回值
/// * `Response` - HTTP响应
#[instrument(skip(metrics, request, next))]
pub async fn performance_monitoring_middleware(
    State(metrics): State<Arc<PerformanceMetrics>>,
    request: Request,
    next: Next,
) -> Response {
    let start_time = Instant::now();
    let method = request.method().to_string();
    let path = request.uri().path().to_string();
    let headers = if metrics.config.log_request_headers {
        Some(request.headers().clone())
    } else {
        None
    };

    // 记录请求开始
    metrics.record_request_start();

    // 使用 defer 模式确保总是记录请求完成
    struct DeferredMetrics {
        metrics: Arc<PerformanceMetrics>,
        start_time: Instant,
        method: String,
        path: String,
        headers: Option<HeaderMap>,
        completed: std::sync::atomic::AtomicBool,
    }

    impl Drop for DeferredMetrics {
        fn drop(&mut self) {
            // 如果请求没有正常完成，记录为服务器错误
            if !self.completed.load(std::sync::atomic::Ordering::Relaxed) {
                let duration = self.start_time.elapsed();
                self.metrics.record_request_end(RequestEndMetrics {
                    duration,
                    status_code: StatusCode::INTERNAL_SERVER_ERROR,
                    method: &self.method,
                    path: &self.path,
                    headers: self.headers.as_ref(),
                    request_size: None,
                    response_size: None,
                });
            }
        }
    }

    let deferred = DeferredMetrics {
        metrics: metrics.clone(),
        start_time,
        method: method.clone(),
        path: path.clone(),
        headers: headers.clone(),
        completed: std::sync::atomic::AtomicBool::new(false),
    };

    // 处理请求
    let response = next.run(request).await;

    // 计算处理时间
    let duration = start_time.elapsed();
    let status_code = response.status();

    // 记录请求完成
    // TODO: 在实际应用中，应该从request和response中提取真实的大小
    // 这里暂时使用None作为占位符
    metrics.record_request_end(RequestEndMetrics {
        duration,
        status_code,
        method: &method,
        path: &path,
        headers: headers.as_ref(),
        request_size: None,  // 可以从request body获取
        response_size: None, // 可以从response body获取
    });

    // 标记为已完成，避免 Drop 时重复记录
    deferred
        .completed
        .store(true, std::sync::atomic::Ordering::Relaxed);

    response
}

/// 创建性能监控中间件层
///
/// 【功能】：创建一个可以应用到Axum路由的性能监控中间件层
///
/// # 参数
/// * `config` - 性能监控配置
///
/// # 返回值
/// * `Arc<PerformanceMetrics>` - 性能指标收集器
pub fn create_performance_monitoring_layer(config: PerformanceConfig) -> Arc<PerformanceMetrics> {
    PerformanceMetrics::new(config)
}

/// 初始化Prometheus指标导出器
///
/// 【功能】：设置Prometheus指标导出器，用于外部监控系统集成
/// 基于2025年最新最佳实践实现完整的Prometheus集成
///
/// # 参数
/// * `bind_address` - 绑定地址，例如 "0.0.0.0:9090"
///
/// # 返回值
/// * `Result<(), Box<dyn std::error::Error>>` - 初始化结果
pub fn init_prometheus_exporter(bind_address: &str) -> Result<(), Box<dyn std::error::Error>> {
    // 简化实现 - 在实际部署时需要添加真正的Prometheus导出器
    // 例如：metrics-exporter-prometheus crate
    info!(
        bind_address = %bind_address,
        "📊 Prometheus指标导出器配置完成 - 简化实现模式"
    );

    // 在这里可以添加真正的Prometheus导出器初始化代码
    // 例如：
    // use metrics_exporter_prometheus::PrometheusBuilder;
    // let addr: SocketAddr = bind_address.parse()?;
    // let builder = PrometheusBuilder::new().with_http_listener(addr);
    // builder.install()?;

    Ok(())
}

// 测试模块
#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::StatusCode;
    use std::time::Duration;

    #[test]
    fn test_performance_config_default() {
        let config = PerformanceConfig::default();
        assert!(config.enable_detailed_logging);
        assert!(config.enable_prometheus_metrics);
        assert_eq!(config.slow_request_threshold_ms, 1000);
    }

    #[test]
    fn test_performance_metrics_creation() {
        let config = PerformanceConfig {
            enable_system_monitoring: false, // 测试时禁用系统监控
            ..Default::default()
        };
        let metrics = PerformanceMetrics::new(config);

        let stats = metrics.get_stats();
        assert_eq!(stats.active_connections, 0);
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.successful_requests, 0);
        assert_eq!(stats.error_requests, 0);
    }

    #[test]
    fn test_request_start_and_end_recording() {
        let config = PerformanceConfig {
            enable_system_monitoring: false, // 测试时禁用系统监控
            ..Default::default()
        };
        let metrics = PerformanceMetrics::new(config);

        // 测试请求开始记录
        metrics.record_request_start();
        let stats = metrics.get_stats();
        assert_eq!(stats.active_connections, 1);
        assert_eq!(stats.total_requests, 1);

        // 测试成功请求结束记录
        let duration = Duration::from_millis(50);
        metrics.record_request_end(RequestEndMetrics {
            duration,
            status_code: StatusCode::OK,
            method: "GET",
            path: "/test",
            headers: None,
            request_size: Some(100),
            response_size: Some(200),
        });

        let stats = metrics.get_stats();
        assert_eq!(stats.active_connections, 0);
        assert_eq!(stats.successful_requests, 1);
        assert_eq!(stats.error_requests, 0);
        assert_eq!(stats.total_request_size, 100);
        assert_eq!(stats.total_response_size, 200);
    }

    #[test]
    fn test_error_classification() {
        let config = PerformanceConfig {
            enable_system_monitoring: false, // 测试时禁用系统监控
            ..Default::default()
        };
        let metrics = PerformanceMetrics::new(config);

        // 测试客户端错误
        metrics.record_request_start();
        metrics.record_request_end(RequestEndMetrics {
            duration: Duration::from_millis(50),
            status_code: StatusCode::NOT_FOUND,
            method: "GET",
            path: "/nonexistent",
            headers: None,
            request_size: None,
            response_size: None,
        });

        let stats = metrics.get_stats();
        assert_eq!(stats.client_errors, 1);
        assert_eq!(stats.not_found_errors, 1);
        assert_eq!(stats.error_requests, 1);
    }

    #[test]
    fn test_request_metrics_drop() {
        let _metrics = RequestMetrics::new("GET".to_string(), "/test".to_string());
        // RequestMetrics 在作用域结束时会自动记录指标
    }

    #[test]
    fn test_init_prometheus_exporter() {
        let result = init_prometheus_exporter("127.0.0.1:9090");
        assert!(result.is_ok());
    }
}
