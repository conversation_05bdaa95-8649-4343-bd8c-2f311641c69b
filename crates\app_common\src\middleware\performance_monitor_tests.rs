//! # 性能监控中间件测试
//!
//! 测试性能监控组件的各项功能，包括：
//! - 指标收集功能
//! - 中间件集成
//! - 错误分类统计
//! - 用户代理统计
//! - 系统资源监控

use super::*;
use axum::{
    body::Body,
    extract::State,
    http::{HeaderMap, Method, Request, StatusCode},
    middleware::Next,
    response::Response,
    routing::get,
    Router,
};
use std::sync::Arc;
use std::time::{Duration, Instant};

/// 创建测试用的性能监控配置
fn create_test_config() -> PerformanceConfig {
        PerformanceConfig {
            enable_detailed_logging: true,
            enable_system_monitoring: false, // 测试时关闭系统监控
            system_monitoring_interval: 1,
            enable_prometheus_metrics: true,
            slow_request_threshold_ms: 100, // 降低阈值便于测试
            log_request_headers: true,
            max_concurrent_connections_warning: 10,
            enable_size_monitoring: true,
            enable_user_agent_stats: true,
            enable_geo_stats: false,
            enable_error_classification: true,
            max_user_agent_cache_size: 5,
            max_geo_cache_size: 5,
        }
    }

/// 创建测试用的HTTP请求
fn create_test_request(method: Method, path: &str, user_agent: Option<&str>) -> Request<Body> {
        let mut builder = Request::builder().method(method).uri(path);

        if let Some(ua) = user_agent {
            builder = builder.header("user-agent", ua);
        }

        builder.body(Body::empty()).unwrap()
    }

#[test]
fn test_performance_config_default() {
        let config = PerformanceConfig::default();

        assert!(config.enable_detailed_logging);
        assert!(config.enable_system_monitoring);
        assert_eq!(config.system_monitoring_interval, 30);
        assert!(config.enable_prometheus_metrics);
        assert_eq!(config.slow_request_threshold_ms, 1000);
        assert!(!config.log_request_headers);
        assert_eq!(config.max_concurrent_connections_warning, 1000);
        assert!(config.enable_size_monitoring);
        assert!(config.enable_user_agent_stats);
        assert!(!config.enable_geo_stats);
        assert!(config.enable_error_classification);
        assert_eq!(config.max_user_agent_cache_size, 1000);
        assert_eq!(config.max_geo_cache_size, 500);
    }

    #[test]
    fn test_performance_metrics_creation() {
        let config = create_test_config();
        let metrics = PerformanceMetrics::new(config.clone());

        let stats = metrics.get_stats();
        assert_eq!(stats.active_connections, 0);
        assert_eq!(stats.total_requests, 0);
        assert_eq!(stats.successful_requests, 0);
        assert_eq!(stats.error_requests, 0);
        assert_eq!(stats.total_request_size, 0);
        assert_eq!(stats.total_response_size, 0);
        assert_eq!(stats.client_errors, 0);
        assert_eq!(stats.server_errors, 0);
    }

    #[test]
    fn test_request_metrics_drop() {
        let start = Instant::now();
        {
            let _metrics = RequestMetrics::new("GET".to_string(), "/test".to_string());
            // 模拟一些处理时间
            std::thread::sleep(Duration::from_millis(10));
        } // RequestMetrics 在这里被 drop，应该记录指标

        let elapsed = start.elapsed();
        assert!(elapsed >= Duration::from_millis(10));
    }

    #[test]
    fn test_request_start_and_end_recording() {
        let config = create_test_config();
        let metrics = PerformanceMetrics::new(config);

        // 测试请求开始记录
        metrics.record_request_start();
        let stats = metrics.get_stats();
        assert_eq!(stats.active_connections, 1);
        assert_eq!(stats.total_requests, 1);

        // 测试成功请求结束记录
        let duration = Duration::from_millis(50);
        metrics.record_request_end(RequestEndMetrics {
            duration,
            status_code: StatusCode::OK,
            method: "GET",
            path: "/test",
            headers: None,
            request_size: Some(100),
            response_size: Some(200),
        });

        let stats = metrics.get_stats();
        assert_eq!(stats.active_connections, 0);
        assert_eq!(stats.successful_requests, 1);
        assert_eq!(stats.error_requests, 0);
        assert_eq!(stats.total_request_size, 100);
        assert_eq!(stats.total_response_size, 200);
    }

    #[test]
    fn test_error_classification() {
        let config = create_test_config();
        let metrics = PerformanceMetrics::new(config);

        // 测试客户端错误
        metrics.record_request_start();
        metrics.record_request_end(RequestEndMetrics {
            duration: Duration::from_millis(50),
            status_code: StatusCode::NOT_FOUND,
            method: "GET",
            path: "/nonexistent",
            headers: None,
            request_size: None,
            response_size: None,
        });

        let stats = metrics.get_stats();
        assert_eq!(stats.client_errors, 1);
        assert_eq!(stats.not_found_errors, 1);
        assert_eq!(stats.error_requests, 1);

        // 测试服务器错误
        metrics.record_request_start();
        metrics.record_request_end(RequestEndMetrics {
            duration: Duration::from_millis(50),
            status_code: StatusCode::INTERNAL_SERVER_ERROR,
            method: "POST",
            path: "/api/error",
            headers: None,
            request_size: None,
            response_size: None,
        });

        let stats = metrics.get_stats();
        assert_eq!(stats.server_errors, 1);
        assert_eq!(stats.error_requests, 2);

        // 测试认证错误
        metrics.record_request_start();
        metrics.record_request_end(RequestEndMetrics {
            duration: Duration::from_millis(50),
            status_code: StatusCode::UNAUTHORIZED,
            method: "GET",
            path: "/protected",
            headers: None,
            request_size: None,
            response_size: None,
        });

        let stats = metrics.get_stats();
        assert_eq!(stats.auth_errors, 1);
        assert_eq!(stats.client_errors, 2);
    }

    #[test]
    fn test_user_agent_stats() {
        let config = create_test_config();
        let metrics = PerformanceMetrics::new(config);

        let mut headers = HeaderMap::new();
        headers.insert("user-agent", "Mozilla/5.0 (Test Browser)".parse().unwrap());

        metrics.record_request_start();
        metrics.record_request_end(RequestEndMetrics {
            duration: Duration::from_millis(50),
            status_code: StatusCode::OK,
            method: "GET",
            path: "/test",
            headers: Some(&headers),
            request_size: None,
            response_size: None,
        });

        // 验证用户代理统计被更新
        let user_agent_stats = metrics.user_agent_stats.lock();
        assert_eq!(user_agent_stats.len(), 1);
        assert!(user_agent_stats.contains_key("Mozilla/5.0 (Test Browser)"));

        let stats = user_agent_stats.get("Mozilla/5.0 (Test Browser)").unwrap();
        assert_eq!(stats.count, 1);
    }

    #[test]
    fn test_user_agent_cache_limit() {
        let config = create_test_config(); // max_user_agent_cache_size = 5
        let metrics = PerformanceMetrics::new(config);

        // 添加超过缓存限制的用户代理
        for i in 0..7 {
            let mut headers = HeaderMap::new();
            headers.insert("user-agent", format!("Browser-{}", i).parse().unwrap());

            metrics.record_request_start();
            metrics.record_request_end(RequestEndMetrics {
                duration: Duration::from_millis(50),
                status_code: StatusCode::OK,
                method: "GET",
                path: "/test",
                headers: Some(&headers),
                request_size: None,
                response_size: None,
            });
        }

        // 验证缓存大小不超过限制
        let user_agent_stats = metrics.user_agent_stats.lock();
        assert!(user_agent_stats.len() <= 5);
    }

    #[test]
    fn test_websocket_upgrade_success() {
        let config = create_test_config();
        let metrics = PerformanceMetrics::new(config);

        // 测试WebSocket升级请求（101状态码）应该被视为成功
        metrics.record_request_start();
        metrics.record_request_end(RequestEndMetrics {
            duration: Duration::from_millis(50),
            status_code: StatusCode::SWITCHING_PROTOCOLS, // 101
            method: "GET",
            path: "/ws",
            headers: None,
            request_size: None,
            response_size: None,
        });

        let stats = metrics.get_stats();
        assert_eq!(stats.successful_requests, 1);
        assert_eq!(stats.error_requests, 0);
    }

    #[tokio::test]
    async fn test_performance_monitoring_middleware_integration() {
        let config = create_test_config();
        let metrics = PerformanceMetrics::new(config);

        // 创建一个简单的处理器
        async fn test_handler() -> &'static str {
            tokio::time::sleep(Duration::from_millis(10)).await;
            "Hello, World!"
        }

        // 创建路由
        let app = Router::new()
            .route("/test", get(test_handler))
            .with_state(metrics.clone())
            .layer(
                axum::middleware::from_fn_with_state(
                    metrics.clone(),
                    performance_monitoring_middleware
                )
            );

        // 创建测试请求
        let request = create_test_request(Method::GET, "/test", Some("Test-Agent"));

        // 发送请求
        let response = app.oneshot(request).await.unwrap();

        // 验证响应
        assert_eq!(response.status(), StatusCode::OK);

        // 验证指标被记录
        let stats = metrics.get_stats();
        assert_eq!(stats.total_requests, 1);
        assert_eq!(stats.successful_requests, 1);
        assert_eq!(stats.active_connections, 0); // 请求完成后应该为0
    }

    #[test]
    fn test_prometheus_metrics_registration() {
        let config = create_test_config();
        let _metrics = PerformanceMetrics::new(config);

        // 这个测试主要验证指标注册不会panic
        // 在实际应用中，可以通过metrics registry验证指标是否正确注册
    }

    #[test]
    fn test_init_prometheus_exporter() {
        let result = init_prometheus_exporter("127.0.0.1:9090");
        assert!(result.is_ok());
    ，}
