//! # 队列性能监控模块
//!
//! 专门用于监控队列系统的性能指标，包括：
//! - 队列长度监控
//! - 任务处理时间统计
//! - 队列吞吐量监控
//! - 任务失败率统计
//! - 队列积压告警

use parking_lot::Mutex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::Instant;
use tracing::{debug, error, info, warn};

/// 队列性能监控配置
///
/// 【功能】：配置队列性能监控的各项参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueueMetricsConfig {
    /// 是否启用队列性能监控
    pub enable_queue_metrics: bool,
    /// 是否启用队列长度告警
    pub enable_queue_length_alerts: bool,
    /// 是否启用处理时间监控
    pub enable_processing_time_monitoring: bool,
    /// 队列长度告警阈值
    pub queue_length_warning_threshold: u64,
    /// 队列长度严重告警阈值
    pub queue_length_critical_threshold: u64,
    /// 慢任务处理阈值（毫秒）
    pub slow_task_threshold_ms: u64,
    /// 任务失败率告警阈值（百分比）
    pub failure_rate_warning_threshold: f64,
    /// 队列统计缓存大小
    pub queue_stats_cache_size: usize,
}

impl Default for QueueMetricsConfig {
    fn default() -> Self {
        Self {
            enable_queue_metrics: true,
            enable_queue_length_alerts: true,
            enable_processing_time_monitoring: true,
            queue_length_warning_threshold: 100,
            queue_length_critical_threshold: 500,
            slow_task_threshold_ms: 5000,         // 5秒
            failure_rate_warning_threshold: 0.05, // 5%
            queue_stats_cache_size: 100,
        }
    }
}

/// 队列任务指标
///
/// 【功能】：记录单个队列任务的详细指标
#[derive(Debug, Clone)]
pub struct QueueTaskMetrics {
    /// 队列名称
    pub queue_name: String,
    /// 任务类型
    pub task_type: String,
    /// 任务ID
    pub task_id: String,
    /// 任务开始时间
    pub start_time: Instant,
    /// 任务优先级
    pub priority: Option<u8>,
    /// 任务重试次数
    pub retry_count: u32,
    /// 任务大小（字节）
    pub task_size_bytes: Option<u64>,
}

/// 队列统计信息
///
/// 【功能】：统计特定队列的性能数据
#[derive(Debug, Clone)]
pub struct QueueStats {
    /// 队列名称
    pub queue_name: String,
    /// 当前队列长度
    pub current_length: u64,
    /// 处理任务总数
    pub processed_tasks: u64,
    /// 失败任务总数
    pub failed_tasks: u64,
    /// 平均处理时间（毫秒）
    pub avg_processing_time_ms: f64,
    /// 最小处理时间（毫秒）
    pub min_processing_time_ms: u64,
    /// 最大处理时间（毫秒）
    pub max_processing_time_ms: u64,
    /// 吞吐量（任务/秒）
    pub throughput_per_second: f64,
    /// 最后更新时间
    pub last_update_time: std::time::SystemTime,
}

/// 队列性能指标收集器
///
/// 【功能】：集中管理队列系统的所有性能指标
#[derive(Debug)]
pub struct QueueMetricsCollector {
    /// 配置
    config: QueueMetricsConfig,
    /// 总任务数
    total_tasks: AtomicU64,
    /// 成功任务数
    successful_tasks: AtomicU64,
    /// 失败任务数
    failed_tasks: AtomicU64,
    /// 慢任务数
    slow_tasks: AtomicU64,
    /// 队列统计信息
    queue_stats: Arc<Mutex<HashMap<String, QueueStats>>>,
    /// 任务类型统计
    task_type_stats: Arc<Mutex<HashMap<String, TaskTypeStats>>>,
}

/// 任务类型统计信息
///
/// 【功能】：统计特定任务类型的性能数据
#[derive(Debug, Clone)]
pub struct TaskTypeStats {
    /// 执行次数
    pub execution_count: u64,
    /// 成功次数
    pub success_count: u64,
    /// 失败次数
    pub failure_count: u64,
    /// 平均执行时间（毫秒）
    pub avg_execution_time_ms: f64,
    /// 最后执行时间
    pub last_execution_time: std::time::SystemTime,
}

impl QueueMetricsCollector {
    /// 创建新的队列性能指标收集器
    ///
    /// 【功能】：初始化队列性能监控系统
    ///
    /// # 参数
    /// * `config` - 队列性能监控配置
    ///
    /// # 返回值
    /// * `Arc<QueueMetricsCollector>` - 队列性能指标收集器实例
    pub fn new(config: QueueMetricsConfig) -> Arc<Self> {
        // 注册队列相关的Prometheus指标
        if config.enable_queue_metrics {
            Self::register_queue_prometheus_metrics();
        }

        Arc::new(Self {
            config,
            total_tasks: AtomicU64::new(0),
            successful_tasks: AtomicU64::new(0),
            failed_tasks: AtomicU64::new(0),
            slow_tasks: AtomicU64::new(0),
            queue_stats: Arc::new(Mutex::new(HashMap::new())),
            task_type_stats: Arc::new(Mutex::new(HashMap::new())),
        })
    }

    /// 注册队列相关的Prometheus指标
    ///
    /// 【功能】：注册所有队列相关的Prometheus指标
    fn register_queue_prometheus_metrics() {
        use metrics::{describe_counter, describe_gauge, describe_histogram};

        // 队列任务指标
        describe_counter!(
            "queue_tasks_total",
            "队列任务总数，按队列名称和任务类型分类"
        );
        describe_counter!(
            "queue_tasks_processed_total",
            "队列处理任务总数，按状态分类"
        );
        describe_histogram!(
            "queue_task_processing_time_seconds",
            "队列任务处理时间分布（秒）"
        );
        describe_counter!(
            "queue_tasks_failed_total",
            "队列失败任务总数，按错误类型分类"
        );

        // 队列长度和状态指标
        describe_gauge!("queue_length", "队列长度，按队列名称分类");
        describe_gauge!("queue_throughput_per_second", "队列吞吐量（任务/秒）");
        describe_gauge!("queue_failure_rate", "队列失败率（0-1）");

        // 任务类型指标
        describe_counter!("queue_task_type_executions_total", "任务类型执行总数");
        describe_histogram!(
            "queue_task_type_duration_seconds",
            "任务类型执行时间分布（秒）"
        );

        // 队列健康指标
        describe_counter!("queue_slow_tasks_total", "慢任务总数，按队列名称分类");
        describe_gauge!("queue_active_workers", "队列活跃工作者数量");

        info!("✅ 队列性能Prometheus指标注册完成");
    }

    /// 记录队列任务开始
    ///
    /// 【功能】：在队列任务开始处理时记录基础指标
    ///
    /// # 参数
    /// * `queue_name` - 队列名称
    /// * `task_type` - 任务类型
    /// * `task_id` - 任务ID
    ///
    /// # 返回值
    /// * `QueueTaskMetrics` - 队列任务指标对象
    pub fn start_queue_task(
        &self,
        queue_name: String,
        task_type: String,
        task_id: String,
    ) -> QueueTaskMetrics {
        self.total_tasks.fetch_add(1, Ordering::Relaxed);

        // 更新Prometheus指标
        if self.config.enable_queue_metrics {
            use metrics::counter;
            counter!("queue_tasks_total",
                "queue" => queue_name.clone(),
                "task_type" => task_type.clone()
            )
            .increment(1);
        }

        QueueTaskMetrics {
            queue_name: queue_name.clone(),
            task_type: task_type.clone(),
            task_id: task_id.clone(),
            start_time: Instant::now(),
            priority: None,
            retry_count: 0,
            task_size_bytes: None,
        }
    }

    /// 记录队列任务完成
    ///
    /// 【功能】：在队列任务完成时记录详细指标
    ///
    /// # 参数
    /// * `metrics` - 队列任务指标对象
    /// * `success` - 任务是否成功
    /// * `error_type` - 错误类型（如果失败）
    pub fn finish_queue_task(
        &self,
        metrics: QueueTaskMetrics,
        success: bool,
        error_type: Option<&str>,
    ) {
        let duration = metrics.start_time.elapsed();
        let duration_ms = duration.as_millis() as u64;
        let duration_seconds = duration.as_secs_f64();

        // 更新成功/失败计数
        if success {
            self.successful_tasks.fetch_add(1, Ordering::Relaxed);
        } else {
            self.failed_tasks.fetch_add(1, Ordering::Relaxed);
        }

        // 检查是否为慢任务
        if self.config.enable_processing_time_monitoring
            && duration_ms > self.config.slow_task_threshold_ms
        {
            self.slow_tasks.fetch_add(1, Ordering::Relaxed);
            warn!(
                queue_name = %metrics.queue_name,
                task_type = %metrics.task_type,
                task_id = %metrics.task_id,
                duration_ms = duration_ms,
                threshold_ms = self.config.slow_task_threshold_ms,
                "慢任务检测到 - 需要优化任务处理性能"
            );
        }

        // 更新队列统计
        self.update_queue_stats(&metrics, duration_ms, success);

        // 更新任务类型统计
        self.update_task_type_stats(&metrics.task_type, duration_ms, success);

        // 更新Prometheus指标
        if self.config.enable_queue_metrics {
            self.update_queue_prometheus_metrics(&metrics, duration_seconds, success, error_type);
        }

        let status = if success { "成功" } else { "失败" };
        debug!(
            queue_name = %metrics.queue_name,
            task_type = %metrics.task_type,
            task_id = %metrics.task_id,
            duration_ms = duration_ms,
            status = status,
            retry_count = metrics.retry_count,
            "队列任务完成"
        );
    }

    /// 更新队列长度
    ///
    /// 【功能】：更新指定队列的当前长度
    ///
    /// # 参数
    /// * `queue_name` - 队列名称
    /// * `length` - 当前队列长度
    pub fn update_queue_length(&self, queue_name: String, length: u64) {
        // 检查队列长度告警
        if self.config.enable_queue_length_alerts {
            if length >= self.config.queue_length_critical_threshold {
                error!(
                    queue_name = queue_name,
                    current_length = length,
                    critical_threshold = self.config.queue_length_critical_threshold,
                    "队列长度达到严重告警阈值 - 需要立即处理"
                );
            } else if length >= self.config.queue_length_warning_threshold {
                warn!(
                    queue_name = queue_name,
                    current_length = length,
                    warning_threshold = self.config.queue_length_warning_threshold,
                    "队列长度达到告警阈值 - 建议增加处理能力"
                );
            }
        }

        // 更新队列统计
        {
            let mut queue_stats = self.queue_stats.lock();
            let stats = queue_stats
                .entry(queue_name.clone())
                .or_insert_with(|| QueueStats {
                    queue_name: queue_name.clone(),
                    current_length: 0,
                    processed_tasks: 0,
                    failed_tasks: 0,
                    avg_processing_time_ms: 0.0,
                    min_processing_time_ms: u64::MAX,
                    max_processing_time_ms: 0,
                    throughput_per_second: 0.0,
                    last_update_time: std::time::SystemTime::now(),
                });

            stats.current_length = length;
            stats.last_update_time = std::time::SystemTime::now();
        }

        // 更新Prometheus指标
        if self.config.enable_queue_metrics {
            use metrics::gauge;
            gauge!("queue_length", "queue" => queue_name.clone()).set(length as f64);
        }

        debug!(queue_name = queue_name, length = length, "队列长度更新");
    }

    /// 更新队列统计信息
    ///
    /// 【功能】：更新特定队列的统计信息
    fn update_queue_stats(&self, metrics: &QueueTaskMetrics, duration_ms: u64, success: bool) {
        let mut queue_stats = self.queue_stats.lock();
        let stats = queue_stats
            .entry(metrics.queue_name.clone())
            .or_insert_with(|| QueueStats {
                queue_name: metrics.queue_name.clone(),
                current_length: 0,
                processed_tasks: 0,
                failed_tasks: 0,
                avg_processing_time_ms: 0.0,
                min_processing_time_ms: u64::MAX,
                max_processing_time_ms: 0,
                throughput_per_second: 0.0,
                last_update_time: std::time::SystemTime::now(),
            });

        // 更新任务计数
        stats.processed_tasks += 1;
        if !success {
            stats.failed_tasks += 1;
        }

        // 更新处理时间统计
        if stats.processed_tasks == 1 {
            stats.avg_processing_time_ms = duration_ms as f64;
            stats.min_processing_time_ms = duration_ms;
            stats.max_processing_time_ms = duration_ms;
        } else {
            // 计算移动平均值
            stats.avg_processing_time_ms = (stats.avg_processing_time_ms
                * ((stats.processed_tasks - 1) as f64)
                + (duration_ms as f64))
                / (stats.processed_tasks as f64);
            stats.min_processing_time_ms = stats.min_processing_time_ms.min(duration_ms);
            stats.max_processing_time_ms = stats.max_processing_time_ms.max(duration_ms);
        }

        // 计算吞吐量（简化实现，基于最近的处理时间）
        if stats.avg_processing_time_ms > 0.0 {
            stats.throughput_per_second = 1000.0 / stats.avg_processing_time_ms;
        }

        stats.last_update_time = std::time::SystemTime::now();
    }

    /// 更新任务类型统计
    ///
    /// 【功能】：更新特定任务类型的统计信息
    fn update_task_type_stats(&self, task_type: &str, duration_ms: u64, success: bool) {
        let mut task_type_stats = self.task_type_stats.lock();
        let stats = task_type_stats
            .entry(task_type.to_string())
            .or_insert_with(|| TaskTypeStats {
                execution_count: 0,
                success_count: 0,
                failure_count: 0,
                avg_execution_time_ms: 0.0,
                last_execution_time: std::time::SystemTime::now(),
            });

        // 更新计数
        stats.execution_count += 1;
        if success {
            stats.success_count += 1;
        } else {
            stats.failure_count += 1;
        }

        // 更新平均执行时间
        stats.avg_execution_time_ms = (stats.avg_execution_time_ms
            * ((stats.execution_count - 1) as f64)
            + (duration_ms as f64))
            / (stats.execution_count as f64);
        stats.last_execution_time = std::time::SystemTime::now();
    }

    /// 更新队列Prometheus指标
    ///
    /// 【功能】：更新所有相关的队列Prometheus指标
    fn update_queue_prometheus_metrics(
        &self,
        metrics: &QueueTaskMetrics,
        duration_seconds: f64,
        success: bool,
        error_type: Option<&str>,
    ) {
        use metrics::{counter, gauge, histogram};

        // 记录任务处理时间
        histogram!("queue_task_processing_time_seconds",
            "queue" => metrics.queue_name.clone(),
            "task_type" => metrics.task_type.clone()
        )
        .record(duration_seconds);

        // 记录任务处理结果
        let status = if success { "success" } else { "failure" };
        counter!("queue_tasks_processed_total",
            "queue" => metrics.queue_name.clone(),
            "task_type" => metrics.task_type.clone(),
            "status" => status
        )
        .increment(1);

        // 记录失败任务
        if !success {
            let error_type_str = error_type.unwrap_or("unknown").to_string();
            counter!("queue_tasks_failed_total",
                "queue" => metrics.queue_name.clone(),
                "task_type" => metrics.task_type.clone(),
                "error_type" => error_type_str
            )
            .increment(1);
        }

        // 记录任务类型执行
        counter!("queue_task_type_executions_total",
            "task_type" => metrics.task_type.clone()
        )
        .increment(1);

        histogram!("queue_task_type_duration_seconds",
            "task_type" => metrics.task_type.clone()
        )
        .record(duration_seconds);

        // 检查慢任务
        if duration_seconds * 1000.0 > (self.config.slow_task_threshold_ms as f64) {
            counter!("queue_slow_tasks_total",
                "queue" => metrics.queue_name.clone()
            )
            .increment(1);
        }

        // 更新失败率
        let queue_stats = self.queue_stats.lock();
        if let Some(stats) = queue_stats.get(&metrics.queue_name) {
            if stats.processed_tasks > 0 {
                let failure_rate = (stats.failed_tasks as f64) / (stats.processed_tasks as f64);
                gauge!("queue_failure_rate", "queue" => metrics.queue_name.clone())
                    .set(failure_rate);

                // 更新吞吐量
                gauge!("queue_throughput_per_second", "queue" => metrics.queue_name.clone())
                    .set(stats.throughput_per_second);
            }
        }
    }

    /// 获取队列性能统计信息
    ///
    /// 【功能】：获取所有队列的性能统计数据
    ///
    /// # 返回值
    /// * `QueuePerformanceStats` - 队列性能统计信息
    pub fn get_performance_stats(&self) -> QueuePerformanceStats {
        let total_tasks = self.total_tasks.load(Ordering::Relaxed);
        let successful_tasks = self.successful_tasks.load(Ordering::Relaxed);
        let failed_tasks = self.failed_tasks.load(Ordering::Relaxed);
        let slow_tasks = self.slow_tasks.load(Ordering::Relaxed);

        let overall_success_rate = if total_tasks > 0 {
            (successful_tasks as f64) / (total_tasks as f64)
        } else {
            0.0
        };

        let queue_count = self.queue_stats.lock().len();
        let task_type_count = self.task_type_stats.lock().len();

        QueuePerformanceStats {
            total_tasks,
            successful_tasks,
            failed_tasks,
            slow_tasks,
            overall_success_rate,
            queue_count,
            task_type_count,
        }
    }

    /// 获取特定队列的统计信息
    ///
    /// 【功能】：获取指定队列的详细统计信息
    ///
    /// # 参数
    /// * `queue_name` - 队列名称
    ///
    /// # 返回值
    /// * `Option<QueueStats>` - 队列统计信息
    pub fn get_queue_stats(&self, queue_name: &str) -> Option<QueueStats> {
        self.queue_stats.lock().get(queue_name).cloned()
    }

    /// 获取所有队列的统计信息
    ///
    /// 【功能】：获取所有队列的统计信息列表
    ///
    /// # 返回值
    /// * `Vec<QueueStats>` - 所有队列的统计信息
    pub fn get_all_queue_stats(&self) -> Vec<QueueStats> {
        self.queue_stats.lock().values().cloned().collect()
    }
}

/// 队列性能统计信息
///
/// 【功能】：包含队列系统的整体性能统计数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueuePerformanceStats {
    /// 总任务数
    pub total_tasks: u64,
    /// 成功任务数
    pub successful_tasks: u64,
    /// 失败任务数
    pub failed_tasks: u64,
    /// 慢任务数
    pub slow_tasks: u64,
    /// 整体成功率
    pub overall_success_rate: f64,
    /// 队列数量
    pub queue_count: usize,
    /// 任务类型数量
    pub task_type_count: usize,
}

/// 创建队列性能监控收集器
///
/// 【功能】：创建一个队列性能监控收集器实例
///
/// # 参数
/// * `config` - 队列性能监控配置
///
/// # 返回值
/// * `Arc<QueueMetricsCollector>` - 队列性能指标收集器
pub fn create_queue_metrics_collector(config: QueueMetricsConfig) -> Arc<QueueMetricsCollector> {
    QueueMetricsCollector::new(config)
}
