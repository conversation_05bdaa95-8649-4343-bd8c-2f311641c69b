//! # 搜索性能监控模块
//!
//! 专门用于监控搜索功能的性能指标，包括：
//! - 搜索请求响应时间
//! - 缓存命中率统计
//! - 搜索结果质量指标
//! - 热门搜索词统计
//! - 搜索错误率监控

use parking_lot::Mutex;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant};
use tracing::{debug, info, warn};

/// 搜索性能监控配置
///
/// 【功能】：配置搜索性能监控的各项参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchMetricsConfig {
    /// 是否启用搜索性能监控
    pub enable_search_metrics: bool,
    /// 是否启用缓存命中率监控
    pub enable_cache_monitoring: bool,
    /// 是否启用热门搜索词统计
    pub enable_popular_terms_tracking: bool,
    /// 慢搜索阈值（毫秒）
    pub slow_search_threshold_ms: u64,
    /// 热门搜索词缓存大小
    pub popular_terms_cache_size: usize,
    /// 搜索结果质量评分阈值
    pub quality_score_threshold: f64,
}

impl Default for SearchMetricsConfig {
    fn default() -> Self {
        Self {
            enable_search_metrics: true,
            enable_cache_monitoring: true,
            enable_popular_terms_tracking: true,
            slow_search_threshold_ms: 500, // 500毫秒
            popular_terms_cache_size: 1000,
            quality_score_threshold: 0.8, // 80%质量分
        }
    }
}

/// 搜索请求指标
///
/// 【功能】：记录单次搜索请求的详细指标
#[derive(Debug, Clone)]
pub struct SearchRequestMetrics {
    /// 搜索查询词
    pub query: String,
    /// 搜索类型（全文搜索、精确匹配等）
    pub search_type: String,
    /// 搜索开始时间
    pub start_time: Instant,
    /// 搜索结果数量
    pub result_count: usize,
    /// 是否命中缓存
    pub cache_hit: bool,
    /// 搜索结果质量评分（0-1）
    pub quality_score: Option<f64>,
    /// 用户ID（可选）
    pub user_id: Option<String>,
}

/// 热门搜索词统计
///
/// 【功能】：统计热门搜索词的使用频率和性能
#[derive(Debug, Clone)]
pub struct PopularTermStats {
    /// 搜索次数
    pub search_count: u64,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 平均结果数量
    pub avg_result_count: f64,
    /// 缓存命中率
    pub cache_hit_ratio: f64,
    /// 最后搜索时间
    pub last_search_time: std::time::SystemTime,
}

/// 搜索性能指标收集器
///
/// 【功能】：集中管理搜索功能的所有性能指标
#[derive(Debug)]
pub struct SearchMetricsCollector {
    /// 配置
    config: SearchMetricsConfig,
    /// 搜索请求总数
    total_searches: AtomicU64,
    /// 缓存命中总数
    cache_hits: AtomicU64,
    /// 缓存未命中总数
    cache_misses: AtomicU64,
    /// 慢搜索总数
    slow_searches: AtomicU64,
    /// 搜索错误总数
    search_errors: AtomicU64,
    /// 热门搜索词统计
    popular_terms: Arc<Mutex<HashMap<String, PopularTermStats>>>,
    /// 搜索类型统计
    search_type_stats: Arc<Mutex<HashMap<String, AtomicU64>>>,
}

impl SearchMetricsCollector {
    /// 创建新的搜索性能指标收集器
    ///
    /// 【功能】：初始化搜索性能监控系统
    ///
    /// # 参数
    /// * `config` - 搜索性能监控配置
    ///
    /// # 返回值
    /// * `Arc<SearchMetricsCollector>` - 搜索性能指标收集器实例
    pub fn new(config: SearchMetricsConfig) -> Arc<Self> {
        // 注册搜索相关的Prometheus指标
        if config.enable_search_metrics {
            Self::register_search_prometheus_metrics();
        }

        Arc::new(Self {
            config,
            total_searches: AtomicU64::new(0),
            cache_hits: AtomicU64::new(0),
            cache_misses: AtomicU64::new(0),
            slow_searches: AtomicU64::new(0),
            search_errors: AtomicU64::new(0),
            popular_terms: Arc::new(Mutex::new(HashMap::new())),
            search_type_stats: Arc::new(Mutex::new(HashMap::new())),
        })
    }

    /// 注册搜索相关的Prometheus指标
    ///
    /// 【功能】：注册所有搜索相关的Prometheus指标
    fn register_search_prometheus_metrics() {
        use metrics::{describe_counter, describe_gauge, describe_histogram};

        // 搜索请求指标
        describe_counter!("search_requests_total", "搜索请求总数，按类型和状态分类");
        describe_histogram!("search_response_time_seconds", "搜索响应时间分布（秒）");
        describe_counter!("search_results_total", "搜索结果总数");

        // 缓存相关指标
        describe_counter!("search_cache_hits_total", "搜索缓存命中总数");
        describe_counter!("search_cache_misses_total", "搜索缓存未命中总数");
        describe_gauge!("search_cache_hit_ratio", "搜索缓存命中率（0-1）");

        // 质量和性能指标
        describe_counter!("search_slow_queries_total", "慢搜索查询总数");
        describe_counter!("search_errors_total", "搜索错误总数，按错误类型分类");
        describe_gauge!("search_quality_score", "搜索结果质量评分");
        describe_gauge!("search_popular_terms_count", "热门搜索词数量");

        info!("✅ 搜索性能Prometheus指标注册完成");
    }

    /// 记录搜索请求开始
    ///
    /// 【功能】：在搜索请求开始时记录基础指标
    ///
    /// # 参数
    /// * `query` - 搜索查询词
    /// * `search_type` - 搜索类型
    /// * `user_id` - 用户ID（可选）
    ///
    /// # 返回值
    /// * `SearchRequestMetrics` - 搜索请求指标对象
    pub fn start_search_request(
        &self,
        query: String,
        search_type: String,
        user_id: Option<String>,
    ) -> SearchRequestMetrics {
        self.total_searches.fetch_add(1, Ordering::Relaxed);

        // 更新搜索类型统计
        {
            let mut stats = self.search_type_stats.lock();
            stats
                .entry(search_type.clone())
                .or_insert_with(|| AtomicU64::new(0))
                .fetch_add(1, Ordering::Relaxed);
        }

        // 更新Prometheus指标
        if self.config.enable_search_metrics {
            use metrics::counter;
            counter!("search_requests_total", "type" => search_type.clone()).increment(1);
        }

        SearchRequestMetrics {
            query: query.clone(),
            search_type: search_type.clone(),
            start_time: Instant::now(),
            result_count: 0,
            cache_hit: false,
            quality_score: None,
            user_id,
        }
    }

    /// 记录搜索请求完成
    ///
    /// 【功能】：在搜索请求完成时记录详细指标
    ///
    /// # 参数
    /// * `metrics` - 搜索请求指标对象
    pub fn finish_search_request(&self, metrics: SearchRequestMetrics) {
        let duration = metrics.start_time.elapsed();
        let duration_ms = duration.as_millis() as u64;
        let duration_seconds = duration.as_secs_f64();

        // 更新缓存统计
        if self.config.enable_cache_monitoring {
            if metrics.cache_hit {
                self.cache_hits.fetch_add(1, Ordering::Relaxed);
            } else {
                self.cache_misses.fetch_add(1, Ordering::Relaxed);
            }
        }

        // 检查是否为慢搜索
        if duration_ms > self.config.slow_search_threshold_ms {
            self.slow_searches.fetch_add(1, Ordering::Relaxed);
            warn!(
                query = %metrics.query,
                search_type = %metrics.search_type,
                duration_ms = duration_ms,
                threshold_ms = self.config.slow_search_threshold_ms,
                "慢搜索检测到 - 需要优化搜索性能"
            );
        }

        // 更新热门搜索词统计
        if self.config.enable_popular_terms_tracking {
            self.update_popular_terms_stats(&metrics, duration_ms);
        }

        // 更新Prometheus指标
        if self.config.enable_search_metrics {
            self.update_prometheus_metrics(&metrics, duration_seconds);
        }

        debug!(
            query = %metrics.query,
            search_type = %metrics.search_type,
            duration_ms = duration_ms,
            result_count = metrics.result_count,
            cache_hit = metrics.cache_hit,
            quality_score = ?metrics.quality_score,
            "搜索请求完成"
        );
    }

    /// 更新热门搜索词统计
    ///
    /// 【功能】：更新热门搜索词的统计信息
    fn update_popular_terms_stats(&self, metrics: &SearchRequestMetrics, duration_ms: u64) {
        let mut popular_terms = self.popular_terms.lock();

        let stats = popular_terms
            .entry(metrics.query.clone())
            .or_insert_with(|| PopularTermStats {
                search_count: 0,
                avg_response_time_ms: 0.0,
                avg_result_count: 0.0,
                cache_hit_ratio: 0.0,
                last_search_time: std::time::SystemTime::now(),
            });

        // 更新统计信息
        let old_count = stats.search_count;
        stats.search_count += 1;

        // 计算移动平均值
        stats.avg_response_time_ms = (stats.avg_response_time_ms * (old_count as f64)
            + (duration_ms as f64))
            / (stats.search_count as f64);
        stats.avg_result_count = (stats.avg_result_count * (old_count as f64)
            + (metrics.result_count as f64))
            / (stats.search_count as f64);

        // 更新缓存命中率
        let cache_hit_count = if metrics.cache_hit { 1.0 } else { 0.0 };
        stats.cache_hit_ratio = (stats.cache_hit_ratio * (old_count as f64) + cache_hit_count)
            / (stats.search_count as f64);

        stats.last_search_time = std::time::SystemTime::now();

        // 限制缓存大小
        if popular_terms.len() > self.config.popular_terms_cache_size {
            // 移除最旧的条目
            if let Some((oldest_key, _)) = popular_terms
                .iter()
                .min_by_key(|(_, stats)| stats.last_search_time)
            {
                let oldest_key = oldest_key.clone();
                popular_terms.remove(&oldest_key);
            }
        }
    }

    /// 更新Prometheus指标
    ///
    /// 【功能】：更新所有相关的Prometheus指标
    fn update_prometheus_metrics(&self, metrics: &SearchRequestMetrics, duration_seconds: f64) {
        use metrics::{counter, gauge, histogram};

        // 记录搜索响应时间
        histogram!("search_response_time_seconds",
            "type" => metrics.search_type.clone(),
            "cache_hit" => if metrics.cache_hit { "true" } else { "false" }
        )
        .record(duration_seconds);

        // 记录搜索结果数量
        counter!("search_results_total").increment(metrics.result_count as u64);

        // 更新缓存指标
        if metrics.cache_hit {
            counter!("search_cache_hits_total").increment(1);
        } else {
            counter!("search_cache_misses_total").increment(1);
        }

        // 计算并更新缓存命中率
        let total_hits = self.cache_hits.load(Ordering::Relaxed) as f64;
        let total_misses = self.cache_misses.load(Ordering::Relaxed) as f64;
        let total_requests = total_hits + total_misses;

        if total_requests > 0.0 {
            let hit_ratio = total_hits / total_requests;
            gauge!("search_cache_hit_ratio").set(hit_ratio);
        }

        // 更新质量评分
        if let Some(quality_score) = metrics.quality_score {
            gauge!("search_quality_score").set(quality_score);
        }

        // 更新热门搜索词数量
        let popular_terms_count = self.popular_terms.lock().len() as f64;
        gauge!("search_popular_terms_count").set(popular_terms_count);

        // 检查慢搜索
        if duration_seconds * 1000.0 > (self.config.slow_search_threshold_ms as f64) {
            counter!("search_slow_queries_total", "type" => metrics.search_type.clone())
                .increment(1);
        }
    }

    /// 记录搜索错误
    ///
    /// 【功能】：记录搜索过程中发生的错误
    ///
    /// # 参数
    /// * `error_type` - 错误类型
    /// * `query` - 搜索查询词
    /// * `search_type` - 搜索类型
    pub fn record_search_error(&self, error_type: String, query: String, search_type: String) {
        self.search_errors.fetch_add(1, Ordering::Relaxed);

        if self.config.enable_search_metrics {
            use metrics::counter;
            counter!("search_errors_total",
                "error_type" => error_type.clone(),
                "search_type" => search_type.clone()
            )
            .increment(1);
        }

        warn!(
            error_type = error_type,
            query = query,
            search_type = search_type,
            "搜索错误记录"
        );
    }

    /// 获取搜索性能统计信息
    ///
    /// 【功能】：获取当前的搜索性能统计数据
    ///
    /// # 返回值
    /// * `SearchPerformanceStats` - 搜索性能统计信息
    pub fn get_performance_stats(&self) -> SearchPerformanceStats {
        let total_searches = self.total_searches.load(Ordering::Relaxed);
        let cache_hits = self.cache_hits.load(Ordering::Relaxed);
        let cache_misses = self.cache_misses.load(Ordering::Relaxed);
        let slow_searches = self.slow_searches.load(Ordering::Relaxed);
        let search_errors = self.search_errors.load(Ordering::Relaxed);

        let cache_hit_ratio = if total_searches > 0 {
            (cache_hits as f64) / ((cache_hits + cache_misses) as f64)
        } else {
            0.0
        };

        let popular_terms_count = self.popular_terms.lock().len();
        let search_type_count = self.search_type_stats.lock().len();

        SearchPerformanceStats {
            total_searches,
            cache_hits,
            cache_misses,
            cache_hit_ratio,
            slow_searches,
            search_errors,
            popular_terms_count,
            search_type_count,
        }
    }

    /// 获取热门搜索词列表
    ///
    /// 【功能】：获取按搜索频率排序的热门搜索词列表
    ///
    /// # 参数
    /// * `limit` - 返回的最大数量
    ///
    /// # 返回值
    /// * `Vec<(String, PopularTermStats)>` - 热门搜索词列表
    pub fn get_popular_terms(&self, limit: usize) -> Vec<(String, PopularTermStats)> {
        let popular_terms = self.popular_terms.lock();
        let mut terms: Vec<_> = popular_terms
            .iter()
            .map(|(term, stats)| (term.clone(), stats.clone()))
            .collect();

        // 按搜索次数降序排序
        terms.sort_by(|a, b| b.1.search_count.cmp(&a.1.search_count));
        terms.truncate(limit);
        terms
    }

    /// 清理过期的热门搜索词
    ///
    /// 【功能】：清理超过指定时间未使用的热门搜索词
    ///
    /// # 参数
    /// * `max_age` - 最大保留时间
    pub fn cleanup_expired_terms(&self, max_age: Duration) {
        let mut popular_terms = self.popular_terms.lock();
        let now = std::time::SystemTime::now();

        popular_terms.retain(|_, stats| {
            if let Ok(elapsed) = now.duration_since(stats.last_search_time) {
                elapsed < max_age
            } else {
                true // 保留时间异常的条目
            }
        });

        debug!(
            remaining_terms = popular_terms.len(),
            max_age_seconds = max_age.as_secs(),
            "热门搜索词清理完成"
        );
    }
}

/// 搜索性能统计信息
///
/// 【功能】：包含搜索功能的整体性能统计数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchPerformanceStats {
    /// 搜索请求总数
    pub total_searches: u64,
    /// 缓存命中总数
    pub cache_hits: u64,
    /// 缓存未命中总数
    pub cache_misses: u64,
    /// 缓存命中率
    pub cache_hit_ratio: f64,
    /// 慢搜索总数
    pub slow_searches: u64,
    /// 搜索错误总数
    pub search_errors: u64,
    /// 热门搜索词数量
    pub popular_terms_count: usize,
    /// 搜索类型数量
    pub search_type_count: usize,
}

/// 创建搜索性能监控中间件
///
/// 【功能】：创建一个可以应用到搜索相关路由的性能监控中间件
///
/// # 参数
/// * `config` - 搜索性能监控配置
///
/// # 返回值
/// * `Arc<SearchMetricsCollector>` - 搜索性能指标收集器
pub fn create_search_metrics_collector(config: SearchMetricsConfig) -> Arc<SearchMetricsCollector> {
    SearchMetricsCollector::new(config)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_search_metrics_config_default() {
        let config = SearchMetricsConfig::default();
        assert!(config.enable_search_metrics);
        assert!(config.enable_cache_monitoring);
        assert_eq!(config.slow_search_threshold_ms, 500);
    }

    #[test]
    fn test_search_metrics_collector_creation() {
        let config = SearchMetricsConfig {
            enable_search_metrics: false, // 测试时禁用Prometheus
            ..Default::default()
        };
        let collector = SearchMetricsCollector::new(config);
        let stats = collector.get_performance_stats();

        assert_eq!(stats.total_searches, 0);
        assert_eq!(stats.cache_hits, 0);
        assert_eq!(stats.cache_misses, 0);
        assert_eq!(stats.slow_searches, 0);
    }

    #[test]
    fn test_search_request_lifecycle() {
        let config = SearchMetricsConfig {
            enable_search_metrics: false, // 测试时禁用Prometheus
            ..Default::default()
        };
        let collector = SearchMetricsCollector::new(config);

        // 开始搜索请求
        let mut metrics =
            collector.start_search_request("test query".to_string(), "full_text".to_string(), None);
        assert_eq!(metrics.query, "test query");
        assert_eq!(metrics.search_type, "full_text");

        // 模拟搜索完成
        metrics.result_count = 10;
        metrics.cache_hit = true;
        metrics.quality_score = Some(0.9);

        collector.finish_search_request(metrics);

        let stats = collector.get_performance_stats();
        assert_eq!(stats.total_searches, 1);
        assert_eq!(stats.cache_hits, 1);
        assert_eq!(stats.cache_misses, 0);
    }

    #[test]
    fn test_popular_terms_tracking() {
        let config = SearchMetricsConfig {
            enable_search_metrics: false,
            enable_popular_terms_tracking: true,
            popular_terms_cache_size: 5,
            ..Default::default()
        };
        let collector = SearchMetricsCollector::new(config);

        // 添加多个搜索词
        for i in 0..3 {
            let mut metrics =
                collector.start_search_request(format!("query{i}"), "full_text".to_string(), None);
            metrics.result_count = 5;
            metrics.cache_hit = i % 2 == 0;
            collector.finish_search_request(metrics);
        }

        let popular_terms = collector.get_popular_terms(10);
        assert_eq!(popular_terms.len(), 3);

        // 验证统计信息
        for (term, stats) in popular_terms {
            assert!(term.starts_with("query"));
            assert_eq!(stats.search_count, 1);
            assert_eq!(stats.avg_result_count, 5.0);
        }
    }
}
