//! # API版本控制中间件
//!
//! 基于2025年最佳实践的API版本控制中间件实现
//!
//! ## 核心功能
//! - 版本协商和验证
//! - 请求路由到正确的版本处理器
//! - 兼容性检查和警告
//! - 版本使用统计和监控
//!
//! ## 使用方式
//! ```rust
//! use app_common::middleware::versioning_middleware::VersioningMiddleware;
//!
//! let versioning = VersioningMiddleware::new(config);
//! let app = Router::new()
//!     .layer(versioning.layer());
//! ```

use app_interfaces::versioning::{
    ApiVersion, ApiVersionError, VersionNegotiation, VersioningConfig, VersioningState,
};
use axum::{
    extract::Request,
    http::{HeaderValue, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
};
use serde_json::json;
use std::sync::Arc;
use tracing::{Span, info, instrument, warn};

/// API版本控制中间件
#[derive(Debug, Clone)]
pub struct VersioningMiddleware {
    /// 版本控制状态
    state: Arc<VersioningState>,
}

impl VersioningMiddleware {
    /// 创建新的版本控制中间件
    pub fn new(config: VersioningConfig) -> Self {
        Self {
            state: Arc::new(VersioningState::new(config)),
        }
    }

    /// 创建默认的版本控制中间件
    pub fn default() -> Self {
        Self::new(VersioningConfig::default())
    }

    /// 获取中间件层
    pub fn into_layer(
        self,
    ) -> axum::middleware::FromFnLayer<
        impl (Fn(
            Request,
            Next,
        )
            -> std::pin::Pin<Box<dyn std::future::Future<Output = Response> + Send + 'static>>)
        + Clone
        + Send
        + 'static,
        (),
        Response,
    > {
        let state = self.state;
        axum::middleware::from_fn(move |req, next| {
            let state = state.clone();
            Box::pin(async move {
                match versioning_middleware(req, next, state).await {
                    Ok(response) => response,
                    Err(status) => (status, "Internal Server Error").into_response(),
                }
            })
                as std::pin::Pin<Box<dyn std::future::Future<Output = Response> + Send + 'static>>
        })
    }
}

/// 版本控制中间件函数
#[instrument(
    skip(req, next, state),
    fields(
        api_version = tracing::field::Empty,
        version_source = tracing::field::Empty,
        requires_adaptation = tracing::field::Empty
    )
)]
pub async fn versioning_middleware(
    mut req: Request,
    next: Next,
    state: Arc<VersioningState>,
) -> Result<Response, StatusCode> {
    let span = Span::current();

    // 从请求中提取版本信息
    let version_info = extract_version_from_request(&req);

    // 协商API版本
    let negotiation = match state.negotiate_version(version_info.as_deref()) {
        Ok(negotiation) => {
            span.record("api_version", negotiation.resolved_version.to_string());
            span.record("requires_adaptation", negotiation.requires_adaptation);
            span.record(
                "version_source",
                version_info.as_deref().unwrap_or("default"),
            );

            info!(
                "API版本协商成功: 请求版本={}, 解析版本={}, 需要适配={}",
                negotiation.requested_version,
                negotiation.resolved_version,
                negotiation.requires_adaptation
            );

            negotiation
        }
        Err(err) => {
            warn!("API版本协商失败: {:?}", err);
            return Ok(create_version_error_response(err));
        }
    };

    // 将版本信息注入到请求扩展中
    req.extensions_mut().insert(negotiation.clone());

    // 记录版本使用统计
    record_version_usage(&negotiation);

    // 继续处理请求
    let mut response = next.run(req).await;

    // 在响应头中添加版本信息
    add_version_headers(&mut response, &negotiation);

    Ok(response)
}

/// 从请求中提取版本信息
///
/// 支持多种版本指定方式：
/// 1. HTTP头: `API-Version: 1.0.0`
/// 2. 查询参数: `?version=1.0.0`
/// 3. Accept头: `Accept: application/vnd.api+json;version=1.0.0`
fn extract_version_from_request(req: &Request) -> Option<String> {
    let headers = req.headers();

    // 1. 检查API-Version头
    if let Some(version) = headers
        .get("api-version")
        .and_then(|v| v.to_str().ok())
        .map(|s| s.to_string())
    {
        return Some(version);
    }

    // 2. 检查X-API-Version头（备用）
    if let Some(version) = headers
        .get("x-api-version")
        .and_then(|v| v.to_str().ok())
        .map(|s| s.to_string())
    {
        return Some(version);
    }

    // 3. 检查查询参数
    if let Some(query) = req.uri().query() {
        for param in query.split('&') {
            if let Some((key, value)) = param.split_once('=') {
                if key == "version" || key == "api_version" {
                    return Some(value.to_string());
                }
            }
        }
    }

    // 4. 检查Accept头中的版本信息
    if let Some(accept) = headers.get("accept").and_then(|v| v.to_str().ok()) {
        if let Some(version) = extract_version_from_accept_header(accept) {
            return Some(version);
        }
    }

    None
}

/// 从Accept头中提取版本信息
///
/// 支持格式：`application/vnd.api+json;version=1.0.0`
fn extract_version_from_accept_header(accept: &str) -> Option<String> {
    for media_type in accept.split(',') {
        let media_type = media_type.trim();
        if let Some((_, params)) = media_type.split_once(';') {
            for param in params.split(';') {
                let param = param.trim();
                if let Some((key, value)) = param.split_once('=') {
                    if key.trim() == "version" {
                        return Some(value.trim().to_string());
                    }
                }
            }
        }
    }
    None
}

/// 创建版本错误响应
fn create_version_error_response(error: ApiVersionError) -> Response {
    let (status_code, error_code, message) = match &error {
        ApiVersionError::MissingVersion => (
            StatusCode::BAD_REQUEST,
            "MISSING_API_VERSION",
            "请在请求头中指定API版本 (API-Version: 1.0.0)",
        ),
        ApiVersionError::InvalidFormat(_) => (
            StatusCode::BAD_REQUEST,
            "INVALID_API_VERSION_FORMAT",
            "API版本格式无效，请使用语义化版本格式 (例如: 1.0.0)",
        ),
        ApiVersionError::UnsupportedVersion(_) => (
            StatusCode::BAD_REQUEST,
            "UNSUPPORTED_API_VERSION",
            "请求的API版本不受支持",
        ),
        ApiVersionError::IncompatibleVersion { .. } => (
            StatusCode::BAD_REQUEST,
            "INCOMPATIBLE_API_VERSION",
            "请求的API版本与当前版本不兼容",
        ),
    };

    let error_response = json!({
        "success": false,
        "error": {
            "code": error_code,
            "message": message,
            "details": error.to_string(),
            "supported_versions": [ApiVersion::CURRENT.to_string()],
            "current_version": ApiVersion::CURRENT.to_string(),
            "timestamp": chrono::Utc::now()
        }
    });

    (status_code, axum::Json(error_response)).into_response()
}

/// 在响应头中添加版本信息
fn add_version_headers(response: &mut Response, negotiation: &VersionNegotiation) {
    let headers = response.headers_mut();

    // 添加当前API版本
    if let Ok(version_value) = HeaderValue::from_str(&negotiation.resolved_version.to_string()) {
        headers.insert("api-version", version_value);
    }

    // 添加兼容性警告
    if !negotiation.compatibility_warnings.is_empty() {
        let warnings = negotiation.compatibility_warnings.join("; ");
        if let Ok(warning_value) = HeaderValue::from_str(&warnings) {
            headers.insert("api-compatibility-warning", warning_value);
        }
    }

    // 添加弃用警告（如果适用）
    if negotiation.resolved_version < ApiVersion::CURRENT {
        let deprecation_warning = format!(
            "API版本 {} 将在未来版本中弃用，请升级到 {}",
            negotiation.resolved_version,
            ApiVersion::CURRENT
        );
        if let Ok(deprecation_value) = HeaderValue::from_str(&deprecation_warning) {
            headers.insert("api-deprecation-warning", deprecation_value);
        }
    }
}

/// 记录版本使用统计
fn record_version_usage(negotiation: &VersionNegotiation) {
    // 记录版本使用情况到日志
    info!(
        "API版本使用统计: 版本={}, 需要适配={}, 警告数={}",
        negotiation.resolved_version,
        negotiation.requires_adaptation,
        negotiation.compatibility_warnings.len()
    );

    // TODO: 集成metrics系统记录详细统计
    // metrics::counter!("api_version_usage_total").increment(1);
}

/// 版本控制中间件状态提取器
///
/// 允许在处理器中访问版本协商结果
#[derive(Debug, Clone)]
pub struct ApiVersionExtractor(pub VersionNegotiation);

impl<S> axum::extract::FromRequestParts<S> for ApiVersionExtractor
where
    S: Send + Sync,
{
    type Rejection = (StatusCode, &'static str);

    async fn from_request_parts(
        parts: &mut axum::http::request::Parts,
        _state: &S,
    ) -> Result<Self, Self::Rejection> {
        parts
            .extensions
            .get::<VersionNegotiation>()
            .cloned()
            .map(ApiVersionExtractor)
            .ok_or((
                StatusCode::INTERNAL_SERVER_ERROR,
                "版本信息未找到，请确保版本控制中间件已正确配置",
            ))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::{HeaderMap, HeaderValue};

    #[test]
    fn test_extract_version_from_api_version_header() {
        let mut headers = HeaderMap::new();
        headers.insert("api-version", HeaderValue::from_static("1.2.3"));

        let req = Request::builder()
            .uri("http://example.com")
            .body(())
            .unwrap();

        // 注意：这里需要模拟完整的请求结构来测试
        // 实际测试需要更复杂的设置
    }

    #[test]
    fn test_extract_version_from_accept_header() {
        let accept = "application/vnd.api+json;version=1.0.0";
        let version = extract_version_from_accept_header(accept);
        assert_eq!(version, Some("1.0.0".to_string()));
    }

    #[test]
    fn test_version_error_response() {
        let error = ApiVersionError::MissingVersion;
        let response = create_version_error_response(error);
        assert_eq!(response.status(), StatusCode::BAD_REQUEST);
    }
}
