// 压力测试专用监控模块
// 收集CPU使用率、内存占用、响应时间、吞吐量等关键指标

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
use std::time::{Duration, SystemTime};
use sysinfo::System;
use tokio::sync::Mutex;
use tracing::{error, info, warn};

/// 压力测试监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StressTestMonitorConfig {
    /// 监控采样间隔（毫秒）
    pub sampling_interval_ms: u64,
    /// 是否启用详细的WebSocket监控
    pub enable_websocket_monitoring: bool,
    /// 是否启用系统资源监控
    pub enable_system_monitoring: bool,
    /// 是否启用数据库性能监控
    pub enable_database_monitoring: bool,
    /// 是否启用内存泄漏检测
    pub enable_memory_leak_detection: bool,
    /// 内存使用率告警阈值（百分比）
    pub memory_usage_warning_threshold: f64,
    /// CPU使用率告警阈值（百分比）
    pub cpu_usage_warning_threshold: f64,
    /// 响应时间告警阈值（毫秒）
    pub response_time_warning_threshold: u64,
    /// 最大监控历史记录数
    pub max_history_records: usize,
}

impl Default for StressTestMonitorConfig {
    fn default() -> Self {
        Self {
            sampling_interval_ms: 1000, // 1秒采样
            enable_websocket_monitoring: true,
            enable_system_monitoring: true,
            enable_database_monitoring: true,
            enable_memory_leak_detection: true,
            memory_usage_warning_threshold: 85.0,  // 85%
            cpu_usage_warning_threshold: 80.0,     // 80%
            response_time_warning_threshold: 1000, // 1秒
            max_history_records: 3600,             // 1小时的历史记录
        }
    }
}

/// 系统资源快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemSnapshot {
    pub timestamp: SystemTime,
    pub cpu_usage_percent: f64,
    pub memory_usage_mb: u64,
    pub memory_usage_percent: f64,
    pub total_memory_mb: u64,
    pub available_memory_mb: u64,
    pub disk_usage_percent: f64,
    pub network_rx_bytes: u64,
    pub network_tx_bytes: u64,
    pub process_count: usize,
    pub thread_count: usize,
}

/// WebSocket连接监控快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketSnapshot {
    pub timestamp: SystemTime,
    pub active_connections: u64,
    pub total_connections_established: u64,
    pub total_connections_closed: u64,
    pub total_messages_sent: u64,
    pub total_messages_received: u64,
    pub avg_message_latency_ms: f64,
    pub connection_errors: u64,
    pub message_errors: u64,
}

/// 应用性能快照
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApplicationSnapshot {
    pub timestamp: SystemTime,
    pub http_requests_total: u64,
    pub http_requests_successful: u64,
    pub http_requests_failed: u64,
    pub avg_response_time_ms: f64,
    pub p95_response_time_ms: f64,
    pub p99_response_time_ms: f64,
    pub database_connections_active: u64,
    pub database_query_avg_time_ms: f64,
    pub cache_hit_rate: f64,
}

/// 压力测试监控器
#[derive(Debug)]
pub struct StressTestMonitor {
    config: StressTestMonitorConfig,
    system: Arc<Mutex<System>>,
    is_monitoring: AtomicBool,

    // 系统资源指标
    system_snapshots: Arc<Mutex<Vec<SystemSnapshot>>>,

    // WebSocket指标
    websocket_snapshots: Arc<Mutex<Vec<WebSocketSnapshot>>>,
    websocket_active_connections: AtomicU64,
    websocket_total_connections: AtomicU64,
    websocket_total_messages_sent: AtomicU64,
    websocket_total_messages_received: AtomicU64,
    websocket_connection_errors: AtomicU64,
    websocket_message_errors: AtomicU64,

    // 应用性能指标
    application_snapshots: Arc<Mutex<Vec<ApplicationSnapshot>>>,
    http_requests_total: AtomicU64,
    http_requests_successful: AtomicU64,
    http_requests_failed: AtomicU64,

    // 响应时间统计
    response_times: Arc<Mutex<Vec<f64>>>,
}

impl StressTestMonitor {
    /// 创建新的压力测试监控器
    pub fn new(config: StressTestMonitorConfig) -> Arc<Self> {
        Arc::new(Self {
            config,
            system: Arc::new(Mutex::new(System::new_all())),
            is_monitoring: AtomicBool::new(false),
            system_snapshots: Arc::new(Mutex::new(Vec::new())),
            websocket_snapshots: Arc::new(Mutex::new(Vec::new())),
            websocket_active_connections: AtomicU64::new(0),
            websocket_total_connections: AtomicU64::new(0),
            websocket_total_messages_sent: AtomicU64::new(0),
            websocket_total_messages_received: AtomicU64::new(0),
            websocket_connection_errors: AtomicU64::new(0),
            websocket_message_errors: AtomicU64::new(0),
            application_snapshots: Arc::new(Mutex::new(Vec::new())),
            http_requests_total: AtomicU64::new(0),
            http_requests_successful: AtomicU64::new(0),
            http_requests_failed: AtomicU64::new(0),
            response_times: Arc::new(Mutex::new(Vec::new())),
        })
    }

    /// 开始监控
    pub async fn start_monitoring(self: Arc<Self>) {
        if self.is_monitoring.swap(true, Ordering::Relaxed) {
            warn!("监控已经在运行中");
            return;
        }

        info!(
            "开始压力测试监控，采样间隔: {}ms",
            self.config.sampling_interval_ms
        );

        let monitor = self.clone();
        tokio::spawn(async move {
            monitor.monitoring_loop().await;
        });
    }

    /// 停止监控
    pub fn stop_monitoring(&self) {
        self.is_monitoring.store(false, Ordering::Relaxed);
        info!("压力测试监控已停止");
    }

    /// 监控主循环
    async fn monitoring_loop(&self) {
        let interval = Duration::from_millis(self.config.sampling_interval_ms);

        while self.is_monitoring.load(Ordering::Relaxed) {
            // 收集系统资源快照
            if self.config.enable_system_monitoring {
                if let Err(e) = self.collect_system_snapshot().await {
                    error!("收集系统快照失败: {}", e);
                }
            }

            // 收集WebSocket快照
            if self.config.enable_websocket_monitoring {
                if let Err(e) = self.collect_websocket_snapshot().await {
                    error!("收集WebSocket快照失败: {}", e);
                }
            }

            // 收集应用性能快照
            if let Err(e) = self.collect_application_snapshot().await {
                error!("收集应用快照失败: {}", e);
            }

            // 检查告警条件
            self.check_alerts().await;

            tokio::time::sleep(interval).await;
        }
    }

    /// 收集系统资源快照
    async fn collect_system_snapshot(&self) -> Result<(), Box<dyn std::error::Error>> {
        let mut system = self.system.lock().await;
        system.refresh_all();

        let cpu_usage = system.global_cpu_usage() as f64;
        let total_memory = system.total_memory();
        let used_memory = system.used_memory();
        let available_memory = system.available_memory();
        let memory_usage_percent = ((used_memory as f64) / (total_memory as f64)) * 100.0;

        let snapshot = SystemSnapshot {
            timestamp: SystemTime::now(),
            cpu_usage_percent: cpu_usage,
            memory_usage_mb: used_memory / 1024 / 1024,
            memory_usage_percent,
            total_memory_mb: total_memory / 1024 / 1024,
            available_memory_mb: available_memory / 1024 / 1024,
            disk_usage_percent: 0.0, // 简化实现
            network_rx_bytes: 0,     // 简化实现
            network_tx_bytes: 0,     // 简化实现
            process_count: system.processes().len(),
            thread_count: 0, // 简化实现
        };

        let mut snapshots = self.system_snapshots.lock().await;
        snapshots.push(snapshot);

        // 限制历史记录数量
        if snapshots.len() > self.config.max_history_records {
            snapshots.remove(0);
        }

        Ok(())
    }

    /// 收集WebSocket快照
    async fn collect_websocket_snapshot(&self) -> Result<(), Box<dyn std::error::Error>> {
        let snapshot = WebSocketSnapshot {
            timestamp: SystemTime::now(),
            active_connections: self.websocket_active_connections.load(Ordering::Relaxed),
            total_connections_established: self.websocket_total_connections.load(Ordering::Relaxed),
            total_connections_closed: 0, // 需要额外计算
            total_messages_sent: self.websocket_total_messages_sent.load(Ordering::Relaxed),
            total_messages_received: self
                .websocket_total_messages_received
                .load(Ordering::Relaxed),
            avg_message_latency_ms: 0.0, // 需要额外计算
            connection_errors: self.websocket_connection_errors.load(Ordering::Relaxed),
            message_errors: self.websocket_message_errors.load(Ordering::Relaxed),
        };

        let mut snapshots = self.websocket_snapshots.lock().await;
        snapshots.push(snapshot);

        // 限制历史记录数量
        if snapshots.len() > self.config.max_history_records {
            snapshots.remove(0);
        }

        Ok(())
    }

    /// 收集应用性能快照
    async fn collect_application_snapshot(&self) -> Result<(), Box<dyn std::error::Error>> {
        let response_times = self.response_times.lock().await;
        let avg_response_time = if response_times.is_empty() {
            0.0
        } else {
            response_times.iter().sum::<f64>() / (response_times.len() as f64)
        };

        // 计算P95和P99
        let mut sorted_times = response_times.clone();
        sorted_times.sort_by(|a, b| a.partial_cmp(b).unwrap());

        let p95_index = ((sorted_times.len() as f64) * 0.95) as usize;
        let p99_index = ((sorted_times.len() as f64) * 0.99) as usize;

        let p95_response_time = sorted_times.get(p95_index).copied().unwrap_or(0.0);
        let p99_response_time = sorted_times.get(p99_index).copied().unwrap_or(0.0);

        drop(response_times);

        let snapshot = ApplicationSnapshot {
            timestamp: SystemTime::now(),
            http_requests_total: self.http_requests_total.load(Ordering::Relaxed),
            http_requests_successful: self.http_requests_successful.load(Ordering::Relaxed),
            http_requests_failed: self.http_requests_failed.load(Ordering::Relaxed),
            avg_response_time_ms: avg_response_time,
            p95_response_time_ms: p95_response_time,
            p99_response_time_ms: p99_response_time,
            database_connections_active: 0,  // 需要从连接池获取
            database_query_avg_time_ms: 0.0, // 需要从数据库监控获取
            cache_hit_rate: 0.0,             // 需要从缓存监控获取
        };

        let mut snapshots = self.application_snapshots.lock().await;
        snapshots.push(snapshot);

        // 限制历史记录数量
        if snapshots.len() > self.config.max_history_records {
            snapshots.remove(0);
        }

        Ok(())
    }

    /// 检查告警条件
    async fn check_alerts(&self) {
        // 检查最新的系统快照
        if let Ok(snapshots) = self.system_snapshots.try_lock() {
            if let Some(latest) = snapshots.last() {
                if latest.cpu_usage_percent > self.config.cpu_usage_warning_threshold {
                    warn!("CPU使用率过高: {:.2}%", latest.cpu_usage_percent);
                }

                if latest.memory_usage_percent > self.config.memory_usage_warning_threshold {
                    warn!("内存使用率过高: {:.2}%", latest.memory_usage_percent);
                }
            }
        }

        // 检查响应时间
        if let Ok(snapshots) = self.application_snapshots.try_lock() {
            if let Some(latest) = snapshots.last() {
                if latest.avg_response_time_ms
                    > (self.config.response_time_warning_threshold as f64)
                {
                    warn!("平均响应时间过高: {:.2}ms", latest.avg_response_time_ms);
                }
            }
        }
    }

    /// 记录WebSocket连接建立
    pub fn record_websocket_connection_established(&self) {
        self.websocket_active_connections
            .fetch_add(1, Ordering::Relaxed);
        self.websocket_total_connections
            .fetch_add(1, Ordering::Relaxed);
    }

    /// 记录WebSocket连接关闭
    pub fn record_websocket_connection_closed(&self) {
        self.websocket_active_connections
            .fetch_sub(1, Ordering::Relaxed);
    }

    /// 记录WebSocket消息发送
    pub fn record_websocket_message_sent(&self) {
        self.websocket_total_messages_sent
            .fetch_add(1, Ordering::Relaxed);
    }

    /// 记录WebSocket消息接收
    pub fn record_websocket_message_received(&self) {
        self.websocket_total_messages_received
            .fetch_add(1, Ordering::Relaxed);
    }

    /// 记录WebSocket连接错误
    pub fn record_websocket_connection_error(&self) {
        self.websocket_connection_errors
            .fetch_add(1, Ordering::Relaxed);
    }

    /// 记录HTTP请求
    pub fn record_http_request(&self, response_time_ms: f64, is_successful: bool) {
        self.http_requests_total.fetch_add(1, Ordering::Relaxed);

        if is_successful {
            self.http_requests_successful
                .fetch_add(1, Ordering::Relaxed);
        } else {
            self.http_requests_failed.fetch_add(1, Ordering::Relaxed);
        }

        // 记录响应时间
        if let Ok(mut response_times) = self.response_times.try_lock() {
            response_times.push(response_time_ms);

            // 限制响应时间历史记录
            if response_times.len() > 1000 {
                response_times.remove(0);
            }
        }
    }

    /// 获取当前监控统计
    pub async fn get_current_stats(&self) -> StressTestStats {
        let system_snapshot = self.system_snapshots.lock().await.last().cloned();
        let websocket_snapshot = self.websocket_snapshots.lock().await.last().cloned();
        let application_snapshot = self.application_snapshots.lock().await.last().cloned();

        StressTestStats {
            system_snapshot,
            websocket_snapshot,
            application_snapshot,
            monitoring_duration_seconds: 0, // 需要计算
        }
    }
}

/// 压力测试统计汇总
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StressTestStats {
    pub system_snapshot: Option<SystemSnapshot>,
    pub websocket_snapshot: Option<WebSocketSnapshot>,
    pub application_snapshot: Option<ApplicationSnapshot>,
    pub monitoring_duration_seconds: u64,
}
