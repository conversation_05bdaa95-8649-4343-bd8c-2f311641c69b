//! # 测试配置模块
//!
//! 为整个测试套件提供统一的配置和工具函数
//!
//! ## 功能特性
//! - 测试数据库配置
//! - Mock服务配置
//! - 测试环境设置
//! - 测试工具函数

use sea_orm::{Database, DatabaseConnection, DbErr};
use std::sync::Arc;
use tokio::sync::OnceCell;
use uuid::Uuid;

/// 测试数据库连接单例
static TEST_DB: OnceCell<Arc<DatabaseConnection>> = OnceCell::const_new();

/// 测试配置结构体
#[derive(Debug, Clone)]
pub struct TestConfig {
    /// 数据库URL
    pub database_url: String,
    /// 是否启用日志
    pub enable_logging: bool,
    /// 测试超时时间（秒）
    pub test_timeout: u64,
    /// 并发测试数量
    pub concurrent_tests: usize,
}

impl Default for TestConfig {
    fn default() -> Self {
        Self {
            database_url: "sqlite::memory:".to_string(),
            enable_logging: false,
            test_timeout: 30,
            concurrent_tests: 4,
        }
    }
}

impl TestConfig {
    /// 创建新的测试配置
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置数据库URL
    pub fn with_database_url(mut self, url: String) -> Self {
        self.database_url = url;
        self
    }

    /// 启用测试日志
    pub fn with_logging(mut self) -> Self {
        self.enable_logging = true;
        self
    }

    /// 设置测试超时时间
    pub fn with_timeout(mut self, timeout: u64) -> Self {
        self.test_timeout = timeout;
        self
    }

    /// 设置并发测试数量
    pub fn with_concurrent_tests(mut self, count: usize) -> Self {
        self.concurrent_tests = count;
        self
    }
}

/// 获取测试数据库连接
///
/// 使用单例模式确保所有测试共享同一个内存数据库连接
pub async fn get_test_database() -> Result<Arc<DatabaseConnection>, DbErr> {
    TEST_DB
        .get_or_try_init(|| async {
            let config = TestConfig::default();
            let db = Database::connect(&config.database_url).await?;
            Ok(Arc::new(db))
        })
        .await
        .cloned()
}

/// 创建独立的测试数据库连接
///
/// 用于需要隔离的集成测试
pub async fn create_test_database() -> Result<DatabaseConnection, DbErr> {
    let config = TestConfig::default();
    Database::connect(&config.database_url).await
}

/// 初始化测试环境
///
/// 设置日志、环境变量等测试所需的全局配置
pub fn init_test_environment() {
    use std::sync::Once;
    static INIT: Once = Once::new();

    INIT.call_once(|| {
        // 设置测试环境变量
        unsafe {
            std::env::set_var("RUST_ENV", "test");
            std::env::set_var("RUST_LOG", "debug");
        }

        // 初始化日志（仅在启用时）
        if std::env::var("TEST_LOGGING").is_ok() {
            tracing_subscriber::fmt()
                .with_test_writer()
                .with_env_filter("debug")
                .init();
        }
    });
}

/// 生成测试用的UUID
pub fn generate_test_uuid() -> Uuid {
    Uuid::new_v4()
}

/// 生成测试用的时间戳
pub fn generate_test_timestamp() -> chrono::DateTime<chrono::Utc> {
    chrono::Utc::now()
}

/// 测试数据生成器
pub struct TestDataGenerator;

impl TestDataGenerator {
    /// 生成测试用户名
    pub fn generate_username() -> String {
        format!("test_user_{}", Uuid::new_v4().simple())
    }

    /// 生成测试邮箱
    pub fn generate_email() -> String {
        format!("test_{}@example.com", Uuid::new_v4().simple())
    }

    /// 生成测试密码
    pub fn generate_password() -> String {
        "test_password_123".to_string()
    }

    /// 生成测试任务标题
    pub fn generate_task_title() -> String {
        format!("测试任务_{}", Uuid::new_v4().simple())
    }

    /// 生成测试任务描述
    pub fn generate_task_description() -> Option<String> {
        Some(format!("这是一个测试任务描述_{}", Uuid::new_v4().simple()))
    }

    /// 生成测试聊天室名称
    pub fn generate_chat_room_name() -> String {
        format!("测试聊天室_{}", Uuid::new_v4().simple())
    }

    /// 生成测试消息内容
    pub fn generate_message_content() -> String {
        format!("测试消息内容_{}", Uuid::new_v4().simple())
    }
}

/// 测试断言工具
pub struct TestAssertions;

impl TestAssertions {
    /// 断言结果为成功
    pub fn assert_ok<T, E>(result: &Result<T, E>) -> &T
    where
        E: std::fmt::Debug,
    {
        match result {
            Ok(value) => value,
            Err(err) => panic!("期望成功结果，但得到错误: {err:?}"),
        }
    }

    /// 断言结果为错误
    pub fn assert_err<T, E>(result: &Result<T, E>) -> &E
    where
        T: std::fmt::Debug,
    {
        match result {
            Ok(value) => panic!("期望错误结果，但得到成功: {value:?}"),
            Err(err) => err,
        }
    }

    /// 断言Option为Some
    pub fn assert_some<T>(option: &Option<T>) -> &T {
        match option {
            Some(value) => value,
            None => panic!("期望Some值，但得到None"),
        }
    }

    /// 断言Option为None
    pub fn assert_none<T>(option: &Option<T>)
    where
        T: std::fmt::Debug,
    {
        if let Some(value) = option {
            panic!("期望None，但得到Some: {value:?}")
        }
    }

    /// 断言向量不为空
    pub fn assert_not_empty<T>(vec: &[T]) {
        assert!(!vec.is_empty(), "期望非空向量，但得到空向量");
    }

    /// 断言向量为空
    pub fn assert_empty<T>(vec: &[T]) {
        assert!(
            vec.is_empty(),
            "期望空向量，但得到非空向量，长度: {}",
            vec.len()
        );
    }

    /// 断言向量长度
    pub fn assert_length<T>(vec: &[T], expected_length: usize) {
        assert_eq!(
            vec.len(),
            expected_length,
            "期望向量长度为 {}，但实际长度为 {}",
            expected_length,
            vec.len()
        );
    }
}

/// 测试清理工具
pub struct TestCleanup;

impl TestCleanup {
    /// 清理测试数据库
    pub async fn cleanup_database(_db: &DatabaseConnection) -> Result<(), DbErr> {
        // 这里可以添加清理逻辑，比如删除测试数据
        // 由于使用内存数据库，通常不需要显式清理
        Ok(())
    }

    /// 清理测试文件
    pub fn cleanup_test_files() {
        // 清理测试过程中创建的临时文件
        if let Ok(entries) = std::fs::read_dir("./") {
            for entry in entries.flatten() {
                if let Some(name) = entry.file_name().to_str() {
                    if name.starts_with("test_") && name.ends_with(".tmp") {
                        let _ = std::fs::remove_file(entry.path());
                    }
                }
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_creation() {
        let config = TestConfig::new()
            .with_database_url("sqlite::memory:".to_string())
            .with_logging()
            .with_timeout(60)
            .with_concurrent_tests(8);

        assert_eq!(config.database_url, "sqlite::memory:");
        assert!(config.enable_logging);
        assert_eq!(config.test_timeout, 60);
        assert_eq!(config.concurrent_tests, 8);
    }

    #[test]
    fn test_data_generator() {
        let username = TestDataGenerator::generate_username();
        let email = TestDataGenerator::generate_email();
        let password = TestDataGenerator::generate_password();

        assert!(username.starts_with("test_user_"));
        assert!(email.contains("@example.com"));
        assert_eq!(password, "test_password_123");
    }

    #[test]
    fn test_assertions() {
        let ok_result: Result<i32, String> = Ok(42);
        let err_result: Result<i32, String> = Err("error".to_string());

        let value = TestAssertions::assert_ok(&ok_result);
        assert_eq!(*value, 42);

        let error = TestAssertions::assert_err(&err_result);
        assert_eq!(error, "error");

        let some_value = Some(42);
        let none_value: Option<i32> = None;

        let value = TestAssertions::assert_some(&some_value);
        assert_eq!(*value, 42);

        TestAssertions::assert_none(&none_value);

        let empty_vec: Vec<i32> = vec![];
        let non_empty_vec = vec![1, 2, 3];

        TestAssertions::assert_empty(&empty_vec);
        TestAssertions::assert_not_empty(&non_empty_vec);
        TestAssertions::assert_length(&non_empty_vec, 3);
    }

    #[tokio::test]
    async fn test_database_connection() {
        init_test_environment();
        let db = get_test_database().await;
        assert!(db.is_ok());
    }
}
