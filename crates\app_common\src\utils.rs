//! # 工具函数模块
//!
//! 提供跨层共享的工具函数，包括：
//! - UUID 解析和验证
//! - 输入验证工具
//! - JWT 工具函数
//! - 身份验证服务
//! - 错误恢复机制
//! - 数据库连接池管理
//! - 内存管理工具
//! - WebSocket连接池管理

pub mod auth_service;
pub mod data_conversion;
pub mod database_pool_manager;
pub mod error_handling;
pub mod error_recovery;
pub mod error_response;
pub mod jwt_utils;
pub mod memory_manager;
pub mod uuid_utils;
pub mod validation_utils;
pub mod websocket_pool_manager;

// 重新导出常用的工具函数
pub use auth_service::{AuthService, TokenExtractionMethod};
pub use data_conversion::{
    BatchConverter, ConversionResult, DataConverter, EntityToActiveModel, EnumConverter,
    ModelToEntity,
};
pub use database_pool_manager::{DatabasePoolManager, PoolMetrics};
pub use error_handling::{
    ErrorCategory, ErrorContext, ErrorHandler as ErrorHandlerTool,
    ErrorRecoveryManager as ErrorRecoveryTool, ErrorSeverity, RecoveryStrategy,
};
pub use error_recovery::{
    CircuitBreakerSettings, DegradationConfig, ErrorRecoveryConfig, ErrorRecoveryManager,
    RecoveryStatus, RetryConfig, RetryStats,
};
pub use error_response::{ErrorHandler, ErrorResponseBuilder};
pub use jwt_utils::{Claims, JwtError, JwtUtils};
pub use memory_manager::{MemoryManager, MemoryMetrics, MemoryOptimizationConfig};
pub use uuid_utils::*;
pub use validation_utils::*;
pub use websocket_pool_manager::{ConnectionMetrics, WebSocketPoolManager};
