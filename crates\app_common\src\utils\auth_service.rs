//! 统一身份验证服务
//!
//! 本模块提供统一的身份验证服务，避免 HTTP 和 WebSocket 认证逻辑的重复。
//!
//! ## 核心功能
//! - **统一的 Token 提取**：支持多种 Token 传递方式
//! - **统一的 Token 验证**：HTTP 和 WebSocket 共用验证逻辑
//! - **统一的错误处理**：一致的错误类型和处理
//! - **可扩展设计**：便于添加新的认证方式
//!
//! ## 设计原则
//! - **DRY 原则**：避免重复代码
//! - **单一职责**：专注于身份验证
//! - **策略模式**：支持多种 Token 提取策略

use crate::utils::{Claims, JwtError, JwtUtils};
use axum::http::{HeaderMap, Uri, header::AUTHORIZATION};

/// Token 提取方法枚举
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum TokenExtractionMethod {
    /// HTTP Authorization Bearer 头
    HttpBearer,
    /// WebSocket 查询参数
    WebSocketQuery,
    /// WebSocket Sec-WebSocket-Protocol 头
    WebSocketProtocol,
}

/// 统一身份验证服务
///
/// 提供 HTTP 和 WebSocket 通用的身份验证功能
pub struct AuthService {
    jwt_utils: JwtUtils,
}

impl AuthService {
    /// 创建新的身份验证服务实例
    pub fn new(jwt_secret: String) -> Self {
        Self {
            jwt_utils: JwtUtils::new(jwt_secret),
        }
    }

    /// 从 HTTP 请求中提取并验证 JWT token
    ///
    /// # 参数
    /// - `headers`: HTTP 请求头
    ///
    /// # 返回
    /// 成功时返回解析后的 Claims，失败时返回错误
    pub fn authenticate_http_request(&self, headers: &HeaderMap) -> Result<Claims, JwtError> {
        // 1. 提取 token
        let token = self.extract_token_from_http_headers(headers)?;

        // 2. 验证 token
        self.jwt_utils.validate_token(&token)
    }

    /// 从 WebSocket 连接中提取并验证 JWT token
    ///
    /// # 参数
    /// - `uri`: WebSocket 连接 URI（用于查询参数）
    /// - `headers`: WebSocket 请求头
    ///
    /// # 返回
    /// 成功时返回解析后的 Claims 和提取方法，失败时返回错误
    pub fn authenticate_websocket_request(
        &self,
        uri: &Uri,
        headers: &HeaderMap,
    ) -> Result<(Claims, TokenExtractionMethod), JwtError> {
        // 尝试多种提取方法

        // 1. 尝试从查询参数提取
        if let Ok(token) = self.extract_token_from_query_params(uri) {
            let claims = self.jwt_utils.validate_token(&token)?;
            return Ok((claims, TokenExtractionMethod::WebSocketQuery));
        }

        // 2. 尝试从 Sec-WebSocket-Protocol 头提取
        if let Ok(token) = self.extract_token_from_websocket_protocol(headers) {
            let claims = self.jwt_utils.validate_token(&token)?;
            return Ok((claims, TokenExtractionMethod::WebSocketProtocol));
        }

        // 3. 尝试从 Authorization 头提取（兼容性）
        if let Ok(token) = self.extract_token_from_http_headers(headers) {
            let claims = self.jwt_utils.validate_token(&token)?;
            return Ok((claims, TokenExtractionMethod::HttpBearer));
        }

        Err(JwtError::TokenMissing)
    }

    /// 从 HTTP 请求头中提取 JWT token
    ///
    /// # 参数
    /// - `headers`: HTTP 请求头
    ///
    /// # 返回
    /// 成功时返回提取的 token，失败时返回错误
    pub fn extract_token_from_http_headers(&self, headers: &HeaderMap) -> Result<String, JwtError> {
        let auth_header = headers
            .get(AUTHORIZATION)
            .and_then(|header| header.to_str().ok())
            .ok_or(JwtError::TokenMissing)?;

        self.jwt_utils.extract_token_from_bearer(auth_header)
    }

    /// 从 WebSocket 查询参数中提取 JWT token
    ///
    /// 支持的查询参数名：token, access_token, jwt
    ///
    /// # 参数
    /// - `uri`: WebSocket 连接 URI
    ///
    /// # 返回
    /// 成功时返回提取的 token，失败时返回错误
    pub fn extract_token_from_query_params(&self, uri: &Uri) -> Result<String, JwtError> {
        let query = uri.query().ok_or(JwtError::TokenMissing)?;

        // 解析查询参数
        for param in query.split('&') {
            if let Some((key, value)) = param.split_once('=') {
                match key {
                    "token" | "access_token" | "jwt" => {
                        if !value.is_empty() {
                            return Ok(urlencoding::decode(value)
                                .map_err(|_| JwtError::TokenInvalid)?
                                .to_string());
                        }
                    }
                    _ => continue,
                }
            }
        }

        Err(JwtError::TokenMissing)
    }

    /// 从 WebSocket Sec-WebSocket-Protocol 头中提取 JWT token
    ///
    /// 支持格式：access_token.{token}
    ///
    /// # 参数
    /// - `headers`: WebSocket 请求头
    ///
    /// # 返回
    /// 成功时返回提取的 token，失败时返回错误
    pub fn extract_token_from_websocket_protocol(
        &self,
        headers: &HeaderMap,
    ) -> Result<String, JwtError> {
        let protocol_header = headers
            .get("sec-websocket-protocol")
            .and_then(|header| header.to_str().ok())
            .ok_or(JwtError::TokenMissing)?;

        // 解析协议头，格式：access_token.{token}
        if let Some(token_part) = protocol_header.strip_prefix("access_token.") {
            if !token_part.is_empty() {
                return Ok(token_part.to_string());
            }
        }

        Err(JwtError::TokenMissing)
    }

    /// 创建新的 JWT token
    ///
    /// # 参数
    /// - `user_id`: 用户 ID
    /// - `username`: 用户名
    /// - `expires_in_hours`: 过期时间（小时）
    ///
    /// # 返回
    /// 成功时返回 JWT token 字符串，失败时返回错误
    pub fn create_token(
        &self,
        user_id: &str,
        username: &str,
        expires_in_hours: i64,
    ) -> Result<String, JwtError> {
        self.jwt_utils
            .create_token(user_id, username, expires_in_hours)
    }

    /// 检查 token 是否即将过期
    ///
    /// # 参数
    /// - `claims`: JWT Claims
    /// - `threshold_minutes`: 过期阈值（分钟）
    ///
    /// # 返回
    /// 如果 token 在指定时间内过期则返回 true
    pub fn is_token_expiring_soon(&self, claims: &Claims, threshold_minutes: i64) -> bool {
        self.jwt_utils
            .is_token_expiring_soon(claims, threshold_minutes)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::http::HeaderValue;

    const TEST_SECRET: &str = "test-auth-service-secret";

    fn create_test_auth_service() -> AuthService {
        AuthService::new(TEST_SECRET.to_string())
    }

    #[test]
    fn test_authenticate_http_request() {
        let auth_service = create_test_auth_service();

        // 创建测试 token
        let token = auth_service.create_token("user123", "testuser", 1).unwrap();

        // 创建请求头
        let mut headers = HeaderMap::new();
        headers.insert(
            AUTHORIZATION,
            HeaderValue::from_str(&format!("Bearer {token}")).unwrap(),
        );

        // 验证请求
        let claims = auth_service.authenticate_http_request(&headers).unwrap();
        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.username, "testuser");
    }

    #[test]
    fn test_extract_token_from_query_params() {
        let auth_service = create_test_auth_service();

        // 测试不同的查询参数名
        let uri1: Uri = "ws://localhost:3000/ws?token=abc123".parse().unwrap();
        let token1 = auth_service.extract_token_from_query_params(&uri1).unwrap();
        assert_eq!(token1, "abc123");

        let uri2: Uri = "ws://localhost:3000/ws?access_token=def456"
            .parse()
            .unwrap();
        let token2 = auth_service.extract_token_from_query_params(&uri2).unwrap();
        assert_eq!(token2, "def456");

        let uri3: Uri = "ws://localhost:3000/ws?jwt=ghi789".parse().unwrap();
        let token3 = auth_service.extract_token_from_query_params(&uri3).unwrap();
        assert_eq!(token3, "ghi789");
    }

    #[test]
    fn test_extract_token_from_websocket_protocol() {
        let auth_service = create_test_auth_service();

        let mut headers = HeaderMap::new();
        headers.insert(
            "sec-websocket-protocol",
            HeaderValue::from_str("access_token.abc123").unwrap(),
        );

        let token = auth_service
            .extract_token_from_websocket_protocol(&headers)
            .unwrap();
        assert_eq!(token, "abc123");
    }
}
