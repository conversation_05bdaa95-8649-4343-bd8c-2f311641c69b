//! # 统一数据转换工具
//!
//! 基于Context7 MCP最佳实践，提供统一的数据转换接口，消除重复的转换逻辑。
//!
//! ## 功能特性
//! - 统一的model_to_entity转换接口
//! - 统一的entity_to_active_model转换接口
//! - 支持部分更新的active_model构建
//! - 自动处理时间戳字段
//! - 符合企业级数据转换标准

use crate::error::{AppError, Result};
use chrono::{DateTime, Utc};
use sea_orm::{ActiveValue, NotSet, Set, Value, sea_query::Nullable};
use uuid::Uuid;

/// 模型到实体转换trait
///
/// 【功能】：定义从数据库模型到领域实体的转换接口
/// 【设计模式】：Adapter模式，适配不同的数据表示
pub trait ModelToEntity<Model, Entity> {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// * `model` - 数据库模型
    ///
    /// # 返回值
    /// * `Result<Entity>` - 转换后的领域实体或错误
    fn model_to_entity(model: Model) -> Result<Entity>;
}

/// 实体到活动模型转换trait
///
/// 【功能】：定义从领域实体到数据库活动模型的转换接口
/// 【设计模式】：Builder模式，构建数据库操作所需的活动模型
pub trait EntityToActiveModel<Entity, ActiveModel> {
    /// 将领域实体转换为数据库活动模型（用于插入）
    ///
    /// # 参数
    /// * `entity` - 领域实体
    ///
    /// # 返回值
    /// * `Result<ActiveModel>` - 转换后的活动模型或错误
    fn entity_to_active_model(entity: Entity) -> Result<ActiveModel>;

    /// 将领域实体转换为部分更新的活动模型（用于更新）
    ///
    /// # 参数
    /// * `entity` - 领域实体
    /// * `exclude_fields` - 要排除的字段列表
    ///
    /// # 返回值
    /// * `Result<ActiveModel>` - 转换后的活动模型或错误
    fn entity_to_partial_active_model(
        entity: Entity,
        exclude_fields: &[&str],
    ) -> Result<ActiveModel>;
}

/// 通用数据转换工具
///
/// 【功能】：提供常用的数据转换辅助方法
pub struct DataConverter;

impl DataConverter {
    /// 创建带有当前时间戳的ActiveValue
    ///
    /// 【功能】：为created_at和updated_at字段生成当前时间戳
    pub fn current_timestamp() -> ActiveValue<DateTime<Utc>> {
        Set(Utc::now())
    }

    /// 创建新的UUID ActiveValue
    ///
    /// 【功能】：为ID字段生成新的UUID
    pub fn new_uuid() -> ActiveValue<Uuid> {
        Set(Uuid::new_v4())
    }

    /// 将Option值转换为ActiveValue
    ///
    /// 【功能】：处理可选字段的转换
    pub fn option_to_active_value<T>(value: Option<T>) -> ActiveValue<Option<T>>
    where
        T: Into<Value> + Nullable,
    {
        match value {
            Some(v) => Set(Some(v)),
            None => Set(None),
        }
    }

    /// 将值转换为ActiveValue::Set
    ///
    /// 【功能】：简化ActiveValue::Set的创建
    pub fn set_value<T>(value: T) -> ActiveValue<T>
    where
        T: Into<Value>,
    {
        Set(value)
    }

    /// 创建未设置的ActiveValue
    ///
    /// 【功能】：用于部分更新时排除某些字段
    pub fn not_set<T>() -> ActiveValue<T>
    where
        T: Into<Value>,
    {
        NotSet
    }

    /// 验证必需字段是否存在
    ///
    /// 【功能】：在转换前验证必需字段
    pub fn validate_required_field<T>(field: &Option<T>, field_name: &str) -> Result<()> {
        if field.is_none() {
            return Err(AppError::ValidationError(format!(
                "必需字段 '{field_name}' 不能为空"
            )));
        }
        Ok(())
    }

    /// 验证字符串字段长度
    ///
    /// 【功能】：验证字符串字段的长度限制
    pub fn validate_string_length(
        value: &str,
        field_name: &str,
        min_length: usize,
        max_length: usize,
    ) -> Result<()> {
        let len = value.len();
        if len < min_length || len > max_length {
            return Err(AppError::ValidationError(format!(
                "字段 '{field_name}' 长度必须在 {min_length} 到 {max_length} 之间，当前长度: {len}"
            )));
        }
        Ok(())
    }

    /// 安全地克隆字符串字段
    ///
    /// 【功能】：避免不必要的字符串克隆
    pub fn clone_string_field(value: &str) -> String {
        value.to_string()
    }

    /// 处理时间戳字段的更新
    ///
    /// 【功能】：在更新操作中自动设置updated_at字段
    pub fn update_timestamp() -> ActiveValue<DateTime<Utc>> {
        Set(Utc::now())
    }

    /// 保持现有时间戳不变
    ///
    /// 【功能】：在部分更新中保持created_at字段不变
    pub fn preserve_timestamp() -> ActiveValue<DateTime<Utc>> {
        NotSet
    }
}

/// 枚举转换trait
///
/// 【功能】：定义枚举类型之间的转换接口
pub trait EnumConverter<From, To> {
    /// 将源枚举转换为目标枚举
    ///
    /// # 参数
    /// * `from` - 源枚举值
    ///
    /// # 返回值
    /// * `Result<To>` - 转换后的目标枚举或错误
    fn convert(from: From) -> Result<To>;

    /// 将目标枚举转换回源枚举
    ///
    /// # 参数
    /// * `to` - 目标枚举值
    ///
    /// # 返回值
    /// * `Result<From>` - 转换后的源枚举或错误
    fn convert_back(to: To) -> Result<From>;
}

/// 批量转换工具
///
/// 【功能】：提供批量数据转换的辅助方法
pub struct BatchConverter;

impl BatchConverter {
    /// 批量转换模型到实体
    ///
    /// 【功能】：将多个数据库模型转换为领域实体
    pub fn models_to_entities<M, E, C>(models: Vec<M>) -> Result<Vec<E>>
    where
        C: ModelToEntity<M, E>,
    {
        models
            .into_iter()
            .map(|model| C::model_to_entity(model))
            .collect()
    }

    /// 批量转换实体到活动模型
    ///
    /// 【功能】：将多个领域实体转换为数据库活动模型
    pub fn entities_to_active_models<E, A, C>(entities: Vec<E>) -> Result<Vec<A>>
    where
        C: EntityToActiveModel<E, A>,
    {
        entities
            .into_iter()
            .map(|entity| C::entity_to_active_model(entity))
            .collect()
    }
}

/// 转换结果包装器
///
/// 【功能】：提供转换操作的结果包装和错误处理
#[derive(Debug, Clone)]
pub struct ConversionResult<T> {
    /// 转换结果
    pub data: T,
    /// 转换过程中的警告信息
    pub warnings: Vec<String>,
}

impl<T> ConversionResult<T> {
    /// 创建成功的转换结果
    pub fn success(data: T) -> Self {
        Self {
            data,
            warnings: Vec::new(),
        }
    }

    /// 创建带警告的转换结果
    pub fn with_warnings(data: T, warnings: Vec<String>) -> Self {
        Self { data, warnings }
    }

    /// 添加警告信息
    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }

    /// 检查是否有警告
    pub fn has_warnings(&self) -> bool {
        !self.warnings.is_empty()
    }

    /// 获取数据并忽略警告
    pub fn into_data(self) -> T {
        self.data
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_data_converter_current_timestamp() {
        let timestamp = DataConverter::current_timestamp();
        match timestamp {
            Set(_) => {} // 正确的情况，无需断言
            _ => panic!("Expected Set value"),
        }
    }

    #[test]
    fn test_data_converter_new_uuid() {
        let uuid_value = DataConverter::new_uuid();
        match uuid_value {
            Set(uuid) => {
                assert!(!uuid.to_string().is_empty());
            }
            _ => panic!("Expected Set value"),
        }
    }

    #[test]
    fn test_data_converter_option_to_active_value() {
        let some_value = DataConverter::option_to_active_value(Some("test".to_string()));
        let none_value = DataConverter::option_to_active_value::<String>(None);

        match some_value {
            Set(Some(value)) => assert_eq!(value, "test"),
            _ => panic!("Expected Set(Some(value))"),
        }

        match none_value {
            Set(None) => {} // 正确的情况，无需断言
            _ => panic!("Expected Set(None)"),
        }
    }

    #[test]
    fn test_data_converter_validate_required_field() {
        let some_field = Some("value");
        let none_field: Option<String> = None;

        assert!(DataConverter::validate_required_field(&some_field, "test_field").is_ok());
        assert!(DataConverter::validate_required_field(&none_field, "test_field").is_err());
    }

    #[test]
    fn test_data_converter_validate_string_length() {
        let valid_string = "test";
        let short_string = "a";
        let long_string = "a".repeat(100);

        assert!(DataConverter::validate_string_length(valid_string, "test", 2, 10).is_ok());
        assert!(DataConverter::validate_string_length(short_string, "test", 2, 10).is_err());
        assert!(DataConverter::validate_string_length(&long_string, "test", 2, 10).is_err());
    }

    #[test]
    fn test_conversion_result() {
        let result = ConversionResult::success("test_data");
        assert_eq!(result.data, "test_data");
        assert!(!result.has_warnings());

        let mut result_with_warnings =
            ConversionResult::with_warnings("test_data", vec!["warning1".to_string()]);
        assert!(result_with_warnings.has_warnings());

        result_with_warnings.add_warning("warning2".to_string());
        assert_eq!(result_with_warnings.warnings.len(), 2);

        let data = result_with_warnings.into_data();
        assert_eq!(data, "test_data");
    }
}
