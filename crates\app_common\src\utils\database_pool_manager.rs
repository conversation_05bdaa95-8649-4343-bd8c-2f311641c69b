//! # 数据库连接池管理器模块
//!
//! 本模块实现了企业级数据库连接池管理功能，包括：
//! 1. 优化的SeaORM连接池配置
//! 2. TCP_NODELAY和连接保活机制
//! 3. 连接池监控指标
//! 4. 连接生命周期管理
//! 5. 故障转移和负载均衡

use crate::config::{AppConfig, DatabasePoolConfig};
use anyhow::Result;
use sea_orm::{ConnectOptions, Database, DatabaseConnection};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
use std::time::Instant;
use tokio::sync::RwLock;
use tracing::{error, info};

/// 数据库连接池统计信息
///
/// 【功能】: 收集和监控数据库连接池的性能指标
/// 【用途】: 用于性能监控、故障诊断和容量规划
#[derive(Debug)]
pub struct PoolMetrics {
    /// 总连接数
    pub total_connections: AtomicU64,
    /// 活跃连接数
    pub active_connections: AtomicU64,
    /// 空闲连接数
    pub idle_connections: AtomicU64,
    /// 连接获取总次数
    pub total_acquires: AtomicU64,
    /// 连接获取成功次数
    pub successful_acquires: AtomicU64,
    /// 连接获取失败次数
    pub failed_acquires: AtomicU64,
    /// 连接超时次数
    pub timeout_count: AtomicU64,
    /// 平均连接获取时间（微秒）
    pub avg_acquire_time_us: AtomicU64,
    /// 连接池是否健康
    pub is_healthy: AtomicBool,
    /// 最后健康检查时间
    pub last_health_check: Arc<RwLock<Instant>>,
}

/// 数据库连接池管理器
///
/// 【功能】: 管理数据库连接池的生命周期和性能优化
/// 【特性】:
/// - 自动连接池配置优化
/// - 连接健康检查
/// - 性能指标收集
/// - 故障恢复机制
#[derive(Debug)]
pub struct DatabasePoolManager {
    /// 数据库连接实例
    connection: Arc<DatabaseConnection>,
    /// 连接池配置
    config: DatabasePoolConfig,
    /// 性能指标
    metrics: Arc<PoolMetrics>,
    /// 连接池是否已初始化
    initialized: AtomicBool,
}

impl DatabasePoolManager {
    /// 创建新的数据库连接池管理器
    ///
    /// 【参数】:
    /// - `config`: 应用程序配置，包含数据库连接信息
    ///
    /// 【返回】:
    /// - `Result<Self>`: 成功时返回管理器实例，失败时返回错误
    pub async fn new(config: &AppConfig) -> Result<Self> {
        info!("初始化数据库连接池管理器");

        // 创建优化的连接选项
        let mut connect_options = ConnectOptions::new(&config.database_url);

        // 应用连接池配置
        Self::configure_connection_options(&mut connect_options, &config.database_pool);

        // 建立数据库连接
        let connection = Database::connect(connect_options).await?;

        info!("数据库连接池创建成功");

        let metrics = Arc::new(PoolMetrics {
            total_connections: AtomicU64::new(0),
            active_connections: AtomicU64::new(0),
            idle_connections: AtomicU64::new(0),
            total_acquires: AtomicU64::new(0),
            successful_acquires: AtomicU64::new(0),
            failed_acquires: AtomicU64::new(0),
            timeout_count: AtomicU64::new(0),
            avg_acquire_time_us: AtomicU64::new(0),
            is_healthy: AtomicBool::new(true),
            last_health_check: Arc::new(RwLock::new(Instant::now())),
        });

        Ok(Self {
            connection: Arc::new(connection),
            config: config.database_pool.clone(),
            metrics,
            initialized: AtomicBool::new(true),
        })
    }

    /// 配置数据库连接选项
    ///
    /// 【功能】: 根据配置参数优化数据库连接性能
    /// 【优化项】:
    /// - 连接池大小
    /// - 连接超时设置
    /// - TCP优化选项
    /// - 连接保活机制
    fn configure_connection_options(
        options: &mut ConnectOptions,
        pool_config: &DatabasePoolConfig,
    ) {
        // 设置连接池大小
        options
            .max_connections(pool_config.max_connections)
            .min_connections(pool_config.min_connections);

        // 设置超时配置
        options
            .connect_timeout(pool_config.connect_timeout)
            .acquire_timeout(pool_config.acquire_timeout)
            .idle_timeout(pool_config.idle_timeout);

        // 启用SQL日志记录（开发环境）
        // 注意：根据实际的DatabasePoolConfig字段进行调整
        options.sqlx_logging(true);

        // 设置连接保活
        // 注意：这个功能需要根据具体的SeaORM版本进行调整

        info!(
            max_connections = pool_config.max_connections,
            min_connections = pool_config.min_connections,
            connect_timeout_ms = pool_config.connect_timeout.as_millis(),
            "数据库连接池配置完成"
        );
    }

    /// 获取数据库连接
    ///
    /// 【功能】: 从连接池获取数据库连接，并更新性能指标
    /// 【返回】: Arc包装的数据库连接
    pub fn get_connection(&self) -> Arc<DatabaseConnection> {
        let start_time = Instant::now();

        // 更新获取连接的统计信息
        self.metrics.total_acquires.fetch_add(1, Ordering::Relaxed);

        let connection = Arc::clone(&self.connection);

        // 记录连接获取时间
        let acquire_time_us = start_time.elapsed().as_micros() as u64;
        self.update_avg_acquire_time(acquire_time_us);

        // 更新成功获取计数
        self.metrics
            .successful_acquires
            .fetch_add(1, Ordering::Relaxed);

        connection
    }

    /// 更新平均连接获取时间
    fn update_avg_acquire_time(&self, new_time_us: u64) {
        let current_avg = self.metrics.avg_acquire_time_us.load(Ordering::Relaxed);
        let total_acquires = self.metrics.total_acquires.load(Ordering::Relaxed);

        if total_acquires > 0 {
            let new_avg = (current_avg * (total_acquires - 1) + new_time_us) / total_acquires;
            self.metrics
                .avg_acquire_time_us
                .store(new_avg, Ordering::Relaxed);
        }
    }

    /// 执行健康检查
    ///
    /// 【功能】: 检查数据库连接池的健康状态
    /// 【返回】: 健康检查结果
    pub async fn health_check(&self) -> Result<bool> {
        let start_time = Instant::now();

        // 执行简单的数据库查询来测试连接
        let result = match self.connection.ping().await {
            Ok(_) => {
                self.metrics.is_healthy.store(true, Ordering::Relaxed);
                info!("数据库连接池健康检查通过");
                true
            }
            Err(e) => {
                self.metrics.is_healthy.store(false, Ordering::Relaxed);
                error!("数据库连接池健康检查失败: {}", e);
                false
            }
        };

        // 更新最后健康检查时间
        *self.metrics.last_health_check.write().await = Instant::now();

        let check_duration = start_time.elapsed();
        info!(
            duration_ms = check_duration.as_millis(),
            healthy = result,
            "数据库健康检查完成"
        );

        Ok(result)
    }

    /// 获取连接池指标
    ///
    /// 【功能】: 返回当前连接池的性能指标
    /// 【用途】: 用于监控和诊断
    pub fn get_metrics(&self) -> PoolMetricsSnapshot {
        PoolMetricsSnapshot {
            total_connections: self.metrics.total_connections.load(Ordering::Relaxed),
            active_connections: self.metrics.active_connections.load(Ordering::Relaxed),
            idle_connections: self.metrics.idle_connections.load(Ordering::Relaxed),
            total_acquires: self.metrics.total_acquires.load(Ordering::Relaxed),
            successful_acquires: self.metrics.successful_acquires.load(Ordering::Relaxed),
            failed_acquires: self.metrics.failed_acquires.load(Ordering::Relaxed),
            timeout_count: self.metrics.timeout_count.load(Ordering::Relaxed),
            avg_acquire_time_us: self.metrics.avg_acquire_time_us.load(Ordering::Relaxed),
            is_healthy: self.metrics.is_healthy.load(Ordering::Relaxed),
        }
    }

    /// 检查连接池是否已初始化
    pub fn is_initialized(&self) -> bool {
        self.initialized.load(Ordering::Relaxed)
    }

    /// 获取连接池配置
    pub fn get_config(&self) -> &DatabasePoolConfig {
        &self.config
    }
}

/// 连接池指标快照
///
/// 【功能】: 提供某个时间点的连接池指标快照
/// 【用途】: 用于监控、日志记录和性能分析
#[derive(Debug, Clone)]
pub struct PoolMetricsSnapshot {
    pub total_connections: u64,
    pub active_connections: u64,
    pub idle_connections: u64,
    pub total_acquires: u64,
    pub successful_acquires: u64,
    pub failed_acquires: u64,
    pub timeout_count: u64,
    pub avg_acquire_time_us: u64,
    pub is_healthy: bool,
}

impl PoolMetricsSnapshot {
    /// 计算连接获取成功率
    pub fn success_rate(&self) -> f64 {
        if self.total_acquires == 0 {
            return 1.0;
        }
        (self.successful_acquires as f64) / (self.total_acquires as f64)
    }

    /// 计算连接池利用率
    pub fn utilization_rate(&self) -> f64 {
        if self.total_connections == 0 {
            return 0.0;
        }
        (self.active_connections as f64) / (self.total_connections as f64)
    }
}
