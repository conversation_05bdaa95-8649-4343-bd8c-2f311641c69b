//! # 增强的错误处理工具模块
//!
//! 基于 Context7 MCP 最佳实践，提供企业级错误处理工具，包括：
//! - 错误上下文传播
//! - 结构化错误日志记录
//! - 错误分类和处理策略
//! - 错误恢复机制
//! - 性能监控集成

use crate::error::AppError;
use anyhow::{Context, Result as AnyhowResult};
use axum::http::StatusCode;
use serde::{Deserialize, Serialize};
use std::fmt;
use tracing::{error, info, instrument, warn};
use tracing_error::SpanTrace;

/// 错误严重级别
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ErrorSeverity {
    /// 低级别 - 预期的业务错误
    Low,
    /// 中级别 - 需要关注的错误
    Medium,
    /// 高级别 - 严重的系统错误
    High,
    /// 紧急级别 - 需要立即处理的错误
    Critical,
}

/// 错误分类
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum ErrorCategory {
    /// 用户输入错误
    UserInput,
    /// 业务逻辑错误
    Business,
    /// 系统错误
    System,
    /// 网络错误
    Network,
    /// 数据库错误
    Database,
    /// 认证/授权错误
    Authentication,
    /// 外部服务错误
    ExternalService,
}

/// 错误上下文信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    /// 错误ID（用于跟踪）
    pub error_id: String,
    /// 错误分类
    pub category: ErrorCategory,
    /// 严重级别
    pub severity: ErrorSeverity,
    /// 用户友好的错误消息
    pub user_message: String,
    /// 技术详细信息
    pub technical_details: Option<String>,
    /// 建议的解决方案
    pub suggested_action: Option<String>,
    /// 相关的请求ID
    pub request_id: Option<String>,
    /// 时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl ErrorContext {
    /// 创建新的错误上下文
    pub fn new(
        category: ErrorCategory,
        severity: ErrorSeverity,
        user_message: impl Into<String>,
    ) -> Self {
        Self {
            error_id: uuid::Uuid::new_v4().to_string(),
            category,
            severity,
            user_message: user_message.into(),
            technical_details: None,
            suggested_action: None,
            request_id: None,
            timestamp: chrono::Utc::now(),
        }
    }

    /// 添加技术详细信息
    pub fn with_technical_details(mut self, details: impl Into<String>) -> Self {
        self.technical_details = Some(details.into());
        self
    }

    /// 添加建议的解决方案
    pub fn with_suggested_action(mut self, action: impl Into<String>) -> Self {
        self.suggested_action = Some(action.into());
        self
    }

    /// 添加请求ID
    pub fn with_request_id(mut self, request_id: impl Into<String>) -> Self {
        self.request_id = Some(request_id.into());
        self
    }
}

/// 错误处理工具
pub struct ErrorHandler;

impl ErrorHandler {
    /// 处理并记录错误，返回适当的HTTP状态码
    #[instrument(skip(error), fields(error_id = %uuid::Uuid::new_v4()))]
    pub fn handle_error(error: &AppError) -> (StatusCode, ErrorContext) {
        let (category, severity, user_message, status_code) = match error {
            AppError::TaskNotFound(_) => (
                ErrorCategory::UserInput,
                ErrorSeverity::Low,
                "请求的资源未找到",
                StatusCode::NOT_FOUND,
            ),
            AppError::NotFound(_) => (
                ErrorCategory::UserInput,
                ErrorSeverity::Low,
                "请求的资源未找到",
                StatusCode::NOT_FOUND,
            ),
            AppError::Forbidden(_) => (
                ErrorCategory::Authentication,
                ErrorSeverity::Medium,
                "您没有权限访问此资源",
                StatusCode::FORBIDDEN,
            ),
            AppError::BadRequest(_) => (
                ErrorCategory::UserInput,
                ErrorSeverity::Low,
                "请求格式不正确",
                StatusCode::BAD_REQUEST,
            ),
            AppError::DatabaseError(_) | AppError::DatabaseConnection(_) => (
                ErrorCategory::Database,
                ErrorSeverity::High,
                "数据库服务暂时不可用",
                StatusCode::INTERNAL_SERVER_ERROR,
            ),
            AppError::UserAlreadyExists(_) => (
                ErrorCategory::Business,
                ErrorSeverity::Low,
                "用户名已存在，请选择其他用户名",
                StatusCode::CONFLICT,
            ),
            AppError::InvalidCredentials => (
                ErrorCategory::Authentication,
                ErrorSeverity::Low,
                "用户名或密码错误",
                StatusCode::UNAUTHORIZED,
            ),
            AppError::InvalidToken(_) => (
                ErrorCategory::Authentication,
                ErrorSeverity::Medium,
                "认证令牌无效或已过期",
                StatusCode::UNAUTHORIZED,
            ),
            AppError::ValidationError(_) | AppError::InputValidation(_) => (
                ErrorCategory::UserInput,
                ErrorSeverity::Low,
                "输入数据验证失败",
                StatusCode::BAD_REQUEST,
            ),
            AppError::Timeout => (
                ErrorCategory::System,
                ErrorSeverity::Medium,
                "请求超时，请稍后重试",
                StatusCode::REQUEST_TIMEOUT,
            ),
            AppError::ServiceUnavailable => (
                ErrorCategory::System,
                ErrorSeverity::High,
                "服务暂时不可用，请稍后重试",
                StatusCode::SERVICE_UNAVAILABLE,
            ),
            AppError::RateLimited => (
                ErrorCategory::System,
                ErrorSeverity::Medium,
                "请求过于频繁，请稍后重试",
                StatusCode::TOO_MANY_REQUESTS,
            ),
            AppError::External(_) => (
                ErrorCategory::ExternalService,
                ErrorSeverity::Medium,
                "外部服务错误",
                StatusCode::BAD_GATEWAY,
            ),
            _ => (
                ErrorCategory::System,
                ErrorSeverity::High,
                "服务器内部错误",
                StatusCode::INTERNAL_SERVER_ERROR,
            ),
        };

        let context = ErrorContext::new(category, severity, user_message)
            .with_technical_details(error.to_string());

        // 根据严重级别记录不同级别的日志
        match severity {
            ErrorSeverity::Low => info!(
                error = %error,
                error_id = %context.error_id,
                category = ?category,
                "Low severity error occurred"
            ),
            ErrorSeverity::Medium => warn!(
                error = %error,
                error_id = %context.error_id,
                category = ?category,
                "Medium severity error occurred"
            ),
            ErrorSeverity::High | ErrorSeverity::Critical => error!(
                error = %error,
                error_id = %context.error_id,
                category = ?category,
                severity = ?severity,
                "High/Critical severity error occurred"
            ),
        }

        (status_code, context)
    }

    /// 创建带有上下文的错误
    pub fn with_context<T>(
        result: AnyhowResult<T>,
        context: impl fmt::Display,
    ) -> Result<T, AppError> {
        result
            .with_context(|| context.to_string())
            .map_err(AppError::External)
    }

    /// 创建跟踪错误
    pub fn traced_error(message: impl Into<String>, status_code: StatusCode) -> AppError {
        AppError::TracedError {
            message: message.into(),
            span_trace: SpanTrace::capture(),
            status_code,
        }
    }
}

/// 错误恢复策略
#[derive(Debug, Clone)]
pub enum RecoveryStrategy {
    /// 重试操作
    Retry { max_attempts: u32, delay_ms: u64 },
    /// 降级服务
    Fallback,
    /// 快速失败
    FailFast,
    /// 忽略错误
    Ignore,
}

/// 错误恢复管理器
pub struct ErrorRecoveryManager;

impl ErrorRecoveryManager {
    /// 根据错误类型确定恢复策略
    pub fn get_recovery_strategy(error: &AppError) -> RecoveryStrategy {
        match error {
            AppError::Timeout | AppError::ServiceUnavailable => RecoveryStrategy::Retry {
                max_attempts: 3,
                delay_ms: 1000,
            },
            AppError::RateLimited => RecoveryStrategy::Retry {
                max_attempts: 2,
                delay_ms: 5000,
            },
            AppError::DatabaseConnection(_) => RecoveryStrategy::Fallback,
            AppError::InvalidCredentials | AppError::Forbidden(_) => RecoveryStrategy::FailFast,
            _ => RecoveryStrategy::FailFast,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_context_creation() {
        let context =
            ErrorContext::new(ErrorCategory::UserInput, ErrorSeverity::Low, "测试错误消息");

        assert_eq!(context.category, ErrorCategory::UserInput);
        assert_eq!(context.severity, ErrorSeverity::Low);
        assert_eq!(context.user_message, "测试错误消息");
        assert!(!context.error_id.is_empty());
    }

    #[test]
    fn test_error_handler() {
        let error = AppError::TaskNotFound(uuid::Uuid::new_v4());
        let (status_code, context) = ErrorHandler::handle_error(&error);

        assert_eq!(status_code, StatusCode::NOT_FOUND);
        assert_eq!(context.category, ErrorCategory::UserInput);
        assert_eq!(context.severity, ErrorSeverity::Low);
    }

    #[test]
    fn test_recovery_strategy() {
        let timeout_error = AppError::Timeout;
        let strategy = ErrorRecoveryManager::get_recovery_strategy(&timeout_error);

        match strategy {
            RecoveryStrategy::Retry { max_attempts, .. } => {
                assert_eq!(max_attempts, 3);
            }
            _ => panic!("Expected retry strategy for timeout error"),
        }
    }
}
