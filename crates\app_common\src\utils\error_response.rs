//! # 统一错误响应构建器
//!
//! 基于Context7 MCP最佳实践，提供统一的错误响应构建工具，消除重复的错误处理模式。
//!
//! ## 功能特性
//! - 统一的JSON错误响应格式
//! - 标准化的错误日志记录
//! - 自动生成跟踪ID
//! - 支持结构化错误上下文
//! - 符合企业级错误处理标准

use axum::{
    Json,
    http::StatusCode,
    response::{IntoResponse, Response},
};
use serde_json::json;
use tracing::{error, instrument, warn};
use tracing_error::SpanTrace;

/// 统一错误响应构建器
///
/// 【功能】：提供统一的错误响应构建接口，消除重复的JSON响应构建逻辑
#[derive(Debug, Clone)]
pub struct ErrorResponseBuilder {
    /// 错误代码
    code: String,
    /// 错误消息
    message: String,
    /// 错误详情（可选）
    details: Option<String>,
    /// HTTP状态码
    status: StatusCode,
    /// 跟踪ID（自动生成）
    trace_id: String,
    /// 时间戳（自动生成）
    timestamp: String,
    /// 是否记录错误日志
    log_error: bool,
    /// Span跟踪信息（可选）
    span_trace: Option<SpanTrace>,
}

impl ErrorResponseBuilder {
    /// 创建新的错误响应构建器
    ///
    /// # 参数
    /// * `code` - 错误代码
    /// * `message` - 错误消息
    /// * `status` - HTTP状态码
    pub fn new(code: impl Into<String>, message: impl Into<String>, status: StatusCode) -> Self {
        Self {
            code: code.into(),
            message: message.into(),
            details: None,
            status,
            trace_id: generate_trace_id(),
            timestamp: chrono::Utc::now().to_rfc3339(),
            log_error: true,
            span_trace: None,
        }
    }

    /// 添加错误详情
    pub fn with_details(mut self, details: impl Into<String>) -> Self {
        self.details = Some(details.into());
        self
    }

    /// 设置是否记录错误日志
    pub fn with_logging(mut self, log_error: bool) -> Self {
        self.log_error = log_error;
        self
    }

    /// 添加Span跟踪信息
    pub fn with_span_trace(mut self, span_trace: SpanTrace) -> Self {
        self.span_trace = Some(span_trace);
        self
    }

    /// 构建HTTP响应
    #[instrument(skip(self), fields(
        error_code = %self.code,
        status = %self.status,
        trace_id = %self.trace_id
    ))]
    pub fn build(self) -> Response {
        // 记录错误日志
        if self.log_error {
            if let Some(span_trace) = &self.span_trace {
                error!(
                    error_code = %self.code,
                    error_message = %self.message,
                    details = ?self.details,
                    status = %self.status,
                    trace_id = %self.trace_id,
                    span_trace = %span_trace,
                    "Error response generated with span context"
                );
            } else {
                error!(
                    error_code = %self.code,
                    error_message = %self.message,
                    details = ?self.details,
                    status = %self.status,
                    trace_id = %self.trace_id,
                    "Error response generated"
                );
            }
        }

        // 使用统一的ApiResponse格式
        let response_body = json!({
            "success": false,
            "data": null,
            "message": self.message,
            "error": {
                "code": self.code,
                "message": self.message,
                "status": self.status.as_u16(),
                "timestamp": self.timestamp,
                "trace_id": self.trace_id,
                "details": self.details
            }
        });

        (self.status, Json(response_body)).into_response()
    }
}

/// 预定义的错误响应构建器工厂方法
impl ErrorResponseBuilder {
    /// 创建超时错误响应
    pub fn timeout_error() -> Self {
        Self::new(
            "REQUEST_TIMEOUT",
            "请求超时，请稍后重试",
            StatusCode::REQUEST_TIMEOUT,
        )
    }

    /// 创建验证错误响应
    pub fn validation_error(details: impl Into<String>) -> Self {
        Self::new("VALIDATION_ERROR", "输入验证失败", StatusCode::BAD_REQUEST).with_details(details)
    }

    /// 创建认证错误响应
    pub fn authentication_error() -> Self {
        Self::new("AUTHENTICATION_ERROR", "认证失败", StatusCode::UNAUTHORIZED)
            .with_details("请检查您的认证凭据")
    }

    /// 创建内部服务器错误响应
    pub fn internal_server_error() -> Self {
        Self::new(
            "INTERNAL_SERVER_ERROR",
            "服务器内部错误",
            StatusCode::INTERNAL_SERVER_ERROR,
        )
    }

    /// 创建数据库错误响应
    pub fn database_error() -> Self {
        Self::new(
            "DATABASE_ERROR",
            "数据库操作失败",
            StatusCode::INTERNAL_SERVER_ERROR,
        )
    }

    /// 创建权限不足错误响应
    pub fn forbidden_error(details: impl Into<String>) -> Self {
        Self::new("FORBIDDEN", "权限不足", StatusCode::FORBIDDEN).with_details(details)
    }

    /// 创建资源未找到错误响应
    pub fn not_found_error(resource: impl Into<String>) -> Self {
        Self::new(
            "NOT_FOUND",
            format!("资源未找到: {}", resource.into()),
            StatusCode::NOT_FOUND,
        )
    }

    /// 创建冲突错误响应
    pub fn conflict_error(details: impl Into<String>) -> Self {
        Self::new("CONFLICT", "资源冲突", StatusCode::CONFLICT).with_details(details)
    }
}

/// 生成唯一的跟踪ID
///
/// 【功能】：生成UUID格式的跟踪ID，用于关联错误日志和响应
fn generate_trace_id() -> String {
    uuid::Uuid::new_v4().to_string()
}

/// 统一的错误处理工具函数
pub struct ErrorHandler;

impl ErrorHandler {
    /// 处理tower::BoxError并转换为统一响应
    #[instrument(skip(error), fields(error_type = %error))]
    pub async fn handle_box_error(
        error: tower::BoxError,
        default_builder: ErrorResponseBuilder,
    ) -> Result<Response, std::convert::Infallible> {
        let span_trace = SpanTrace::capture();

        // 根据错误类型选择合适的响应构建器
        let builder = if error.is::<tower::timeout::error::Elapsed>() {
            warn!("Request timeout occurred");
            ErrorResponseBuilder::timeout_error()
        } else {
            warn!(error = %error, "Unhandled error occurred");
            default_builder.with_details(error.to_string())
        };

        Ok(builder.with_span_trace(span_trace).build())
    }

    /// 处理数据库错误
    #[instrument(skip(db_error))]
    pub fn handle_database_error(db_error: &sea_orm::DbErr) -> ErrorResponseBuilder {
        // 记录详细的数据库错误信息（仅用于服务器日志）
        error!(
            db_error = %db_error,
            "Database operation failed"
        );

        // 返回通用的数据库错误响应（不暴露内部细节）
        ErrorResponseBuilder::database_error()
    }

    /// 处理应用程序错误
    #[instrument(skip(app_error))]
    pub fn handle_app_error(app_error: &crate::error::AppError) -> ErrorResponseBuilder {
        use crate::error::AppError;

        match app_error {
            AppError::TaskNotFound(id) => {
                ErrorResponseBuilder::not_found_error(format!("任务 {id}"))
            }
            AppError::NotFound(msg) => ErrorResponseBuilder::not_found_error(msg.clone()),
            AppError::Forbidden(msg) => ErrorResponseBuilder::forbidden_error(msg.clone()),
            AppError::BadRequest(msg) => ErrorResponseBuilder::validation_error(msg.clone()),
            AppError::DatabaseError(_) => ErrorResponseBuilder::database_error(),
            AppError::UserAlreadyExists(username) => {
                ErrorResponseBuilder::conflict_error(format!("用户名 '{username}' 已存在"))
            }
            AppError::InvalidCredentials => {
                ErrorResponseBuilder::authentication_error().with_details("用户名或密码错误")
            }
            AppError::PasswordHashError(_) => {
                ErrorResponseBuilder::internal_server_error().with_details("密码处理失败")
            }
            AppError::TokenGenerationError(_) => {
                ErrorResponseBuilder::internal_server_error().with_details("令牌生成失败")
            }
            AppError::InvalidToken(msg) => {
                ErrorResponseBuilder::new("INVALID_TOKEN", "无效的令牌", StatusCode::UNAUTHORIZED)
                    .with_details(msg.clone())
            }
            AppError::ValidationError(msg) => ErrorResponseBuilder::validation_error(msg.clone()),
            AppError::InternalServerError(_) => ErrorResponseBuilder::internal_server_error(),
            AppError::NotImplemented(msg) => ErrorResponseBuilder::new(
                "NOT_IMPLEMENTED",
                "功能尚未实现",
                StatusCode::NOT_IMPLEMENTED,
            )
            .with_details(msg.clone()),
            AppError::TracedError {
                message,
                span_trace,
                status_code,
            } => ErrorResponseBuilder::new("TRACED_ERROR", message.clone(), *status_code)
                .with_span_trace(span_trace.clone()),
            AppError::External(err) => ErrorResponseBuilder::internal_server_error()
                .with_details(format!("外部错误: {err}")),
            AppError::DatabaseConnection(err) => ErrorResponseBuilder::database_error()
                .with_details(format!("数据库连接错误: {err}")),
            AppError::InputValidation(err) => {
                ErrorResponseBuilder::validation_error(format!("输入验证失败: {err}"))
            }
            AppError::JsonError(err) => {
                ErrorResponseBuilder::validation_error(format!("JSON处理错误: {err}"))
            }
            AppError::IoError(err) => {
                ErrorResponseBuilder::internal_server_error().with_details(format!("IO错误: {err}"))
            }
            AppError::Timeout => ErrorResponseBuilder::timeout_error(),
            AppError::ServiceUnavailable => ErrorResponseBuilder::new(
                "SERVICE_UNAVAILABLE",
                "服务暂时不可用",
                StatusCode::SERVICE_UNAVAILABLE,
            ),
            AppError::RateLimited => ErrorResponseBuilder::new(
                "RATE_LIMITED",
                "请求过于频繁",
                StatusCode::TOO_MANY_REQUESTS,
            ),
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_response_builder_basic() {
        let builder = ErrorResponseBuilder::new("TEST_ERROR", "测试错误", StatusCode::BAD_REQUEST);

        assert_eq!(builder.code, "TEST_ERROR");
        assert_eq!(builder.message, "测试错误");
        assert_eq!(builder.status, StatusCode::BAD_REQUEST);
        assert!(builder.details.is_none());
    }

    #[test]
    fn test_error_response_builder_with_details() {
        let builder = ErrorResponseBuilder::new("TEST_ERROR", "测试错误", StatusCode::BAD_REQUEST)
            .with_details("详细信息");

        assert_eq!(builder.details, Some("详细信息".to_string()));
    }

    #[test]
    fn test_predefined_builders() {
        let timeout = ErrorResponseBuilder::timeout_error();
        assert_eq!(timeout.code, "REQUEST_TIMEOUT");
        assert_eq!(timeout.status, StatusCode::REQUEST_TIMEOUT);

        let validation = ErrorResponseBuilder::validation_error("测试验证错误");
        assert_eq!(validation.code, "VALIDATION_ERROR");
        assert_eq!(validation.status, StatusCode::BAD_REQUEST);
        assert_eq!(validation.details, Some("测试验证错误".to_string()));

        let auth = ErrorResponseBuilder::authentication_error();
        assert_eq!(auth.code, "AUTHENTICATION_ERROR");
        assert_eq!(auth.status, StatusCode::UNAUTHORIZED);
    }

    #[test]
    fn test_generate_trace_id() {
        let trace_id = generate_trace_id();
        assert!(!trace_id.is_empty());
        assert!(uuid::Uuid::parse_str(&trace_id).is_ok());
    }
}
