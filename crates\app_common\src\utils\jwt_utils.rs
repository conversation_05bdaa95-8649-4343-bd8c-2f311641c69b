//! JWT 工具模块
//!
//! 本模块提供统一的 JWT 相关工具函数，避免代码重复。
//! 包含 JWT token 的创建、验证、解析等通用功能。
//!
//! ## 核心功能
//! - **统一的 JWT 验证逻辑**：HTTP 和 WebSocket 共用
//! - **Token 创建工具**：测试和生产环境共用
//! - **错误处理统一**：统一的错误类型和处理
//!
//! ## 设计原则
//! - **DRY 原则**：避免重复代码
//! - **单一职责**：专注于 JWT 相关操作
//! - **可测试性**：便于单元测试

use chrono::{Duration, Utc};
use jsonwebtoken::{DecodingKey, EncodingKey, Header, Validation, decode, encode};
// 移除未使用的导入：Deserialize, Serialize

// JWT Claims 结构体已移至 app_interfaces 模块以避免重复定义
// 这里重新导出以保持向后兼容性
pub use app_interfaces::Claims;

/// JWT 错误类型
#[derive(Debug, PartialEq, thiserror::Error)]
pub enum JwtError {
    #[error("JWT token 缺失")]
    TokenMissing,
    #[error("JWT token 无效")]
    TokenInvalid,
    #[error("JWT token 已过期")]
    TokenExpired,
    #[error("JWT token 创建失败: {0}")]
    TokenCreationFailed(String),
}

/// JWT 工具结构体
///
/// 封装了 JWT 相关的操作，包括创建和验证 token
pub struct JwtUtils {
    secret: String,
}

impl JwtUtils {
    /// 创建新的 JWT 工具实例
    pub fn new(secret: String) -> Self {
        Self { secret }
    }

    /// 创建 JWT token
    ///
    /// # 参数
    /// - `user_id`: 用户 ID
    /// - `username`: 用户名
    /// - `expires_in_hours`: 过期时间（小时）
    ///
    /// # 返回
    /// 成功时返回 JWT token 字符串，失败时返回错误
    pub fn create_token(
        &self,
        user_id: &str,
        username: &str,
        expires_in_hours: i64,
    ) -> Result<String, JwtError> {
        let now = Utc::now();
        let claims = Claims {
            sub: user_id.to_string(),
            username: username.to_string(),
            iat: now.timestamp(),
            exp: (now + Duration::hours(expires_in_hours)).timestamp(),
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.secret.as_ref()),
        )
        .map_err(|e| JwtError::TokenCreationFailed(e.to_string()))
    }

    /// 验证 JWT token
    ///
    /// # 参数
    /// - `token`: JWT token 字符串
    ///
    /// # 返回
    /// 成功时返回解析后的 Claims，失败时返回错误
    pub fn validate_token(&self, token: &str) -> Result<Claims, JwtError> {
        if token.is_empty() {
            return Err(JwtError::TokenMissing);
        }

        match decode::<Claims>(
            token,
            &DecodingKey::from_secret(self.secret.as_ref()),
            &Validation::default(),
        ) {
            Ok(token_data) => Ok(token_data.claims),
            Err(err) => match err.kind() {
                jsonwebtoken::errors::ErrorKind::ExpiredSignature => Err(JwtError::TokenExpired),
                _ => Err(JwtError::TokenInvalid),
            },
        }
    }

    /// 从 Bearer token 中提取 JWT
    ///
    /// # 参数
    /// - `bearer_token`: Bearer token 字符串（格式：Bearer <token>）
    ///
    /// # 返回
    /// 成功时返回提取的 token，失败时返回错误
    pub fn extract_token_from_bearer(&self, bearer_token: &str) -> Result<String, JwtError> {
        if !bearer_token.starts_with("Bearer ") {
            return Err(JwtError::TokenInvalid);
        }

        let token = bearer_token.strip_prefix("Bearer ").unwrap_or("");
        if token.is_empty() {
            return Err(JwtError::TokenMissing);
        }

        Ok(token.to_string())
    }

    /// 验证 Bearer token
    ///
    /// 组合了提取和验证的功能
    ///
    /// # 参数
    /// - `bearer_token`: Bearer token 字符串
    ///
    /// # 返回
    /// 成功时返回解析后的 Claims，失败时返回错误
    pub fn validate_bearer_token(&self, bearer_token: &str) -> Result<Claims, JwtError> {
        let token = self.extract_token_from_bearer(bearer_token)?;
        self.validate_token(&token)
    }

    /// 检查 token 是否即将过期
    ///
    /// # 参数
    /// - `claims`: JWT Claims
    /// - `threshold_minutes`: 过期阈值（分钟）
    ///
    /// # 返回
    /// 如果 token 在指定时间内过期则返回 true
    pub fn is_token_expiring_soon(&self, claims: &Claims, threshold_minutes: i64) -> bool {
        let now = Utc::now().timestamp();
        let threshold = threshold_minutes * 60;
        claims.exp - now <= threshold
    }

    /// 创建用于测试的过期 token
    ///
    /// 仅用于测试目的
    #[cfg(test)]
    pub fn create_expired_test_token(&self, user_id: &str, username: &str) -> String {
        let past_time = Utc::now() - Duration::hours(1);
        let claims = Claims {
            sub: user_id.to_string(),
            username: username.to_string(),
            iat: past_time.timestamp(),
            exp: past_time.timestamp(),
        };

        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.secret.as_ref()),
        )
        .expect("Failed to create test token")
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    const TEST_SECRET: &str = "test-jwt-utils-secret";

    #[test]
    fn test_create_and_validate_token() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        // 创建 token
        let token = jwt_utils.create_token("user123", "testuser", 1).unwrap();
        assert!(!token.is_empty());

        // 验证 token
        let claims = jwt_utils.validate_token(&token).unwrap();
        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.username, "testuser");
    }

    #[test]
    fn test_validate_expired_token() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        let expired_token = jwt_utils.create_expired_test_token("user123", "testuser");
        let result = jwt_utils.validate_token(&expired_token);

        assert_eq!(result, Err(JwtError::TokenExpired));
    }

    #[test]
    fn test_extract_token_from_bearer() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        let bearer_token = "Bearer abc123";
        let token = jwt_utils.extract_token_from_bearer(bearer_token).unwrap();
        assert_eq!(token, "abc123");

        // 测试无效格式
        let invalid_bearer = "Invalid abc123";
        let result = jwt_utils.extract_token_from_bearer(invalid_bearer);
        assert_eq!(result, Err(JwtError::TokenInvalid));
    }

    #[test]
    fn test_validate_bearer_token() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        // 创建有效的 token
        let token = jwt_utils.create_token("user123", "testuser", 1).unwrap();
        let bearer_token = format!("Bearer {token}");

        // 验证 Bearer token
        let claims = jwt_utils.validate_bearer_token(&bearer_token).unwrap();
        assert_eq!(claims.sub, "user123");
        assert_eq!(claims.username, "testuser");
    }

    #[test]
    fn test_is_token_expiring_soon() {
        let jwt_utils = JwtUtils::new(TEST_SECRET.to_string());

        // 创建一个1小时后过期的 token
        let token = jwt_utils.create_token("user123", "testuser", 1).unwrap();
        let claims = jwt_utils.validate_token(&token).unwrap();

        // 检查是否在2小时内过期（应该返回 true）
        assert!(jwt_utils.is_token_expiring_soon(&claims, 120));

        // 检查是否在30分钟内过期（应该返回 false）
        assert!(!jwt_utils.is_token_expiring_soon(&claims, 30));
    }
}
