//! # 内存管理器模块
//!
//! 提供企业级内存管理功能，包括：
//! 1. 内存使用监控
//! 2. 内存泄漏检测
//! 3. 内存优化策略
//! 4. 垃圾回收建议
//! 5. 内存使用统计

use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, warn};

/// 内存优化配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryOptimizationConfig {
    /// 内存监控间隔（秒）
    pub monitoring_interval_secs: u64,
    /// 内存警告阈值（MB）
    pub warning_threshold_mb: u64,
    /// 内存危险阈值（MB）
    pub critical_threshold_mb: u64,
    /// 是否启用自动垃圾回收
    pub enable_auto_gc: bool,
    /// 垃圾回收触发阈值（MB）
    pub gc_trigger_threshold_mb: u64,
    /// 内存历史记录保留时间（分钟）
    pub history_retention_minutes: u64,
}

/// 内存使用指标
#[derive(Debug)]
pub struct MemoryMetrics {
    /// 当前内存使用量（字节）
    pub current_usage_bytes: AtomicU64,
    /// 峰值内存使用量（字节）
    pub peak_usage_bytes: AtomicU64,
    /// 内存分配次数
    pub allocation_count: AtomicU64,
    /// 内存释放次数
    pub deallocation_count: AtomicU64,
    /// 活跃对象数量
    pub active_objects: AtomicUsize,
    /// 最后更新时间
    pub last_updated: Arc<RwLock<Instant>>,
    /// 内存使用历史（最近N个采样点）
    pub usage_history: Arc<RwLock<Vec<MemoryUsageSnapshot>>>,
}

/// 内存使用快照
#[derive(Debug, Clone)]
pub struct MemoryUsageSnapshot {
    /// 时间戳
    pub timestamp: Instant,
    /// 内存使用量（字节）
    pub usage_bytes: u64,
    /// 活跃对象数量
    pub active_objects: usize,
}

/// 内存状态
#[derive(Debug, Clone, PartialEq)]
pub enum MemoryStatus {
    /// 正常状态
    Normal,
    /// 警告状态
    Warning,
    /// 危险状态
    Critical,
    /// 内存泄漏疑似
    LeakSuspected,
}

/// 内存管理器
///
/// 【功能】: 提供内存使用监控和优化建议
pub struct MemoryManager {
    config: MemoryOptimizationConfig,
    metrics: Arc<MemoryMetrics>,
    start_time: Instant,
}

impl MemoryManager {
    /// 创建新的内存管理器
    pub fn new(config: MemoryOptimizationConfig) -> Self {
        let metrics = Arc::new(MemoryMetrics {
            current_usage_bytes: AtomicU64::new(0),
            peak_usage_bytes: AtomicU64::new(0),
            allocation_count: AtomicU64::new(0),
            deallocation_count: AtomicU64::new(0),
            active_objects: AtomicUsize::new(0),
            last_updated: Arc::new(RwLock::new(Instant::now())),
            usage_history: Arc::new(RwLock::new(Vec::new())),
        });

        Self {
            config,
            metrics,
            start_time: Instant::now(),
        }
    }

    /// 记录内存分配
    pub async fn record_allocation(&self, size: u64) {
        let current = self
            .metrics
            .current_usage_bytes
            .fetch_add(size, Ordering::Relaxed)
            + size;

        // 更新峰值使用量
        let mut peak = self.metrics.peak_usage_bytes.load(Ordering::Relaxed);
        while current > peak {
            match self.metrics.peak_usage_bytes.compare_exchange_weak(
                peak,
                current,
                Ordering::Relaxed,
                Ordering::Relaxed,
            ) {
                Ok(_) => {
                    break;
                }
                Err(x) => {
                    peak = x;
                }
            }
        }

        self.metrics
            .allocation_count
            .fetch_add(1, Ordering::Relaxed);
        self.metrics.active_objects.fetch_add(1, Ordering::Relaxed);

        *self.metrics.last_updated.write().await = Instant::now();

        debug!("记录内存分配: {} 字节，当前总使用: {} 字节", size, current);

        // 检查是否需要触发垃圾回收
        if self.config.enable_auto_gc && self.should_trigger_gc(current).await {
            self.suggest_garbage_collection().await;
        }
    }

    /// 记录内存释放
    pub async fn record_deallocation(&self, size: u64) {
        self.metrics
            .current_usage_bytes
            .fetch_sub(size, Ordering::Relaxed);
        self.metrics
            .deallocation_count
            .fetch_add(1, Ordering::Relaxed);
        self.metrics.active_objects.fetch_sub(1, Ordering::Relaxed);

        *self.metrics.last_updated.write().await = Instant::now();

        debug!("记录内存释放: {} 字节", size);
    }

    /// 获取当前内存状态
    pub async fn get_memory_status(&self) -> MemoryStatus {
        let current_mb = self.get_current_usage_mb();

        if current_mb >= self.config.critical_threshold_mb {
            MemoryStatus::Critical
        } else if current_mb >= self.config.warning_threshold_mb {
            MemoryStatus::Warning
        } else if self.is_leak_suspected().await {
            MemoryStatus::LeakSuspected
        } else {
            MemoryStatus::Normal
        }
    }

    /// 获取当前内存使用量（MB）
    pub fn get_current_usage_mb(&self) -> u64 {
        self.metrics.current_usage_bytes.load(Ordering::Relaxed) / (1024 * 1024)
    }

    /// 获取峰值内存使用量（MB）
    pub fn get_peak_usage_mb(&self) -> u64 {
        self.metrics.peak_usage_bytes.load(Ordering::Relaxed) / (1024 * 1024)
    }

    /// 获取内存使用统计
    pub async fn get_memory_stats(&self) -> MemoryStats {
        let current_usage = self.metrics.current_usage_bytes.load(Ordering::Relaxed);
        let peak_usage = self.metrics.peak_usage_bytes.load(Ordering::Relaxed);
        let allocation_count = self.metrics.allocation_count.load(Ordering::Relaxed);
        let deallocation_count = self.metrics.deallocation_count.load(Ordering::Relaxed);
        let active_objects = self.metrics.active_objects.load(Ordering::Relaxed);

        let uptime = self.start_time.elapsed();
        let allocation_rate = if uptime.as_secs() > 0 {
            (allocation_count as f64) / (uptime.as_secs() as f64)
        } else {
            0.0
        };

        MemoryStats {
            current_usage_bytes: current_usage,
            current_usage_mb: current_usage / (1024 * 1024),
            peak_usage_bytes: peak_usage,
            peak_usage_mb: peak_usage / (1024 * 1024),
            allocation_count,
            deallocation_count,
            active_objects,
            allocation_rate,
            uptime_secs: uptime.as_secs(),
            status: self.get_memory_status().await,
        }
    }

    /// 更新内存使用历史
    pub async fn update_usage_history(&self) {
        let current_usage = self.metrics.current_usage_bytes.load(Ordering::Relaxed);
        let active_objects = self.metrics.active_objects.load(Ordering::Relaxed);

        let snapshot = MemoryUsageSnapshot {
            timestamp: Instant::now(),
            usage_bytes: current_usage,
            active_objects,
        };

        let mut history = self.metrics.usage_history.write().await;
        history.push(snapshot);

        // 清理过期的历史记录
        let retention_duration = Duration::from_secs(self.config.history_retention_minutes * 60);
        let cutoff_time = Instant::now() - retention_duration;
        history.retain(|snapshot| snapshot.timestamp > cutoff_time);
    }

    /// 检查是否疑似内存泄漏
    async fn is_leak_suspected(&self) -> bool {
        let history = self.metrics.usage_history.read().await;

        if history.len() < 10 {
            return false; // 数据不足，无法判断
        }

        // 检查最近的内存使用趋势
        let recent_samples = &history[history.len().saturating_sub(10)..];
        let mut increasing_count = 0;

        for window in recent_samples.windows(2) {
            if window[1].usage_bytes > window[0].usage_bytes {
                increasing_count += 1;
            }
        }

        // 如果80%以上的采样点都在增长，可能存在内存泄漏
        (increasing_count as f64) / ((recent_samples.len() - 1) as f64) > 0.8
    }

    /// 检查是否应该触发垃圾回收
    async fn should_trigger_gc(&self, current_usage: u64) -> bool {
        let threshold_bytes = self.config.gc_trigger_threshold_mb * 1024 * 1024;
        current_usage > threshold_bytes
    }

    /// 建议执行垃圾回收
    async fn suggest_garbage_collection(&self) {
        warn!(
            current_usage_mb = self.get_current_usage_mb(),
            threshold_mb = self.config.gc_trigger_threshold_mb,
            "建议执行垃圾回收以释放内存"
        );

        // 这里可以添加实际的垃圾回收逻辑
        // 例如：调用 tokio::task::yield_now() 或其他清理操作
    }

    /// 启动内存监控任务
    pub async fn start_monitoring(&self) -> tokio::task::JoinHandle<()> {
        let manager = self.clone();
        let interval = Duration::from_secs(self.config.monitoring_interval_secs);

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);

            loop {
                interval_timer.tick().await;
                manager.update_usage_history().await;

                let status = manager.get_memory_status().await;
                match status {
                    MemoryStatus::Warning => {
                        warn!(
                            "内存使用量达到警告阈值: {} MB",
                            manager.get_current_usage_mb()
                        );
                    }
                    MemoryStatus::Critical => {
                        warn!(
                            "内存使用量达到危险阈值: {} MB",
                            manager.get_current_usage_mb()
                        );
                    }
                    MemoryStatus::LeakSuspected => {
                        warn!("检测到疑似内存泄漏");
                    }
                    MemoryStatus::Normal => {
                        debug!("内存使用正常: {} MB", manager.get_current_usage_mb());
                    }
                }
            }
        })
    }
}

impl Clone for MemoryManager {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            metrics: Arc::clone(&self.metrics),
            start_time: self.start_time,
        }
    }
}

/// 内存统计信息
#[derive(Debug, Clone)]
pub struct MemoryStats {
    pub current_usage_bytes: u64,
    pub current_usage_mb: u64,
    pub peak_usage_bytes: u64,
    pub peak_usage_mb: u64,
    pub allocation_count: u64,
    pub deallocation_count: u64,
    pub active_objects: usize,
    pub allocation_rate: f64,
    pub uptime_secs: u64,
    pub status: MemoryStatus,
}

impl Default for MemoryOptimizationConfig {
    fn default() -> Self {
        Self {
            monitoring_interval_secs: 30,
            warning_threshold_mb: 512,
            critical_threshold_mb: 1024,
            enable_auto_gc: true,
            gc_trigger_threshold_mb: 256,
            history_retention_minutes: 60,
        }
    }
}
