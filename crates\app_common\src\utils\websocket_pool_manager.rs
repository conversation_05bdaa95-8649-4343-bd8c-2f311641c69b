//! # WebSocket连接池管理器模块
//!
//! 提供WebSocket连接的统一管理功能，包括：
//! 1. 连接生命周期管理
//! 2. 连接池监控和统计
//! 3. 连接健康检查
//! 4. 自动清理无效连接
//! 5. 连接负载均衡

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::RwLock;
use tracing::{debug, info, warn};
use uuid::Uuid;

/// WebSocket连接信息
#[derive(Debug, Clone)]
pub struct WebSocketConnection {
    /// 连接ID
    pub id: Uuid,
    /// 用户ID
    pub user_id: Uuid,
    /// 连接建立时间
    pub connected_at: Instant,
    /// 最后活跃时间
    pub last_activity: Instant,
    /// 是否活跃
    pub is_active: bool,
    /// 连接来源IP
    pub remote_addr: Option<String>,
    /// 用户代理
    pub user_agent: Option<String>,
}

/// 连接池配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketPoolConfig {
    /// 最大连接数
    pub max_connections: usize,
    /// 连接超时时间（秒）
    pub connection_timeout_secs: u64,
    /// 心跳检查间隔（秒）
    pub heartbeat_interval_secs: u64,
    /// 清理无效连接间隔（秒）
    pub cleanup_interval_secs: u64,
    /// 是否启用连接统计
    pub enable_metrics: bool,
}

/// 连接池指标
#[derive(Debug)]
pub struct ConnectionMetrics {
    /// 总连接数
    pub total_connections: AtomicUsize,
    /// 活跃连接数
    pub active_connections: AtomicUsize,
    /// 累计连接次数
    pub total_connects: AtomicU64,
    /// 累计断开次数
    pub total_disconnects: AtomicU64,
    /// 超时断开次数
    pub timeout_disconnects: AtomicU64,
    /// 平均连接持续时间（秒）
    pub avg_connection_duration_secs: AtomicU64,
    /// 最后更新时间
    pub last_updated: Arc<RwLock<Instant>>,
}

/// WebSocket连接池管理器
///
/// 【功能】: 统一管理WebSocket连接的生命周期和性能监控
pub struct WebSocketPoolManager {
    /// 连接池配置
    config: WebSocketPoolConfig,
    /// 活跃连接映射 (连接ID -> 连接信息)
    connections: Arc<RwLock<HashMap<Uuid, WebSocketConnection>>>,
    /// 用户连接映射 (用户ID -> 连接ID集合)
    user_connections: Arc<RwLock<HashMap<Uuid, Vec<Uuid>>>>,
    /// 连接池指标
    metrics: Arc<ConnectionMetrics>,
}

impl WebSocketPoolManager {
    /// 创建新的WebSocket连接池管理器
    pub fn new(config: WebSocketPoolConfig) -> Self {
        let metrics = Arc::new(ConnectionMetrics {
            total_connections: AtomicUsize::new(0),
            active_connections: AtomicUsize::new(0),
            total_connects: AtomicU64::new(0),
            total_disconnects: AtomicU64::new(0),
            timeout_disconnects: AtomicU64::new(0),
            avg_connection_duration_secs: AtomicU64::new(0),
            last_updated: Arc::new(RwLock::new(Instant::now())),
        });

        Self {
            config,
            connections: Arc::new(RwLock::new(HashMap::new())),
            user_connections: Arc::new(RwLock::new(HashMap::new())),
            metrics,
        }
    }

    /// 添加新连接
    pub async fn add_connection(
        &self,
        connection_id: Uuid,
        user_id: Uuid,
        remote_addr: Option<String>,
        user_agent: Option<String>,
    ) -> Result<(), String> {
        // 检查连接数限制
        let current_count = self.metrics.total_connections.load(Ordering::Relaxed);
        if current_count >= self.config.max_connections {
            return Err(format!(
                "连接数已达到最大限制: {}",
                self.config.max_connections
            ));
        }

        let now = Instant::now();
        let connection = WebSocketConnection {
            id: connection_id,
            user_id,
            connected_at: now,
            last_activity: now,
            is_active: true,
            remote_addr,
            user_agent,
        };

        // 添加到连接池
        {
            let mut connections = self.connections.write().await;
            connections.insert(connection_id, connection);
        }

        // 更新用户连接映射
        {
            let mut user_connections = self.user_connections.write().await;
            user_connections
                .entry(user_id)
                .or_insert_with(Vec::new)
                .push(connection_id);
        }

        // 更新指标
        self.metrics
            .total_connections
            .fetch_add(1, Ordering::Relaxed);
        self.metrics
            .active_connections
            .fetch_add(1, Ordering::Relaxed);
        self.metrics.total_connects.fetch_add(1, Ordering::Relaxed);
        *self.metrics.last_updated.write().await = now;

        info!(
            connection_id = %connection_id,
            user_id = %user_id,
            total_connections = current_count + 1,
            "WebSocket连接已添加"
        );

        Ok(())
    }

    /// 移除连接
    pub async fn remove_connection(&self, connection_id: Uuid) -> Option<WebSocketConnection> {
        let connection = {
            let mut connections = self.connections.write().await;
            connections.remove(&connection_id)
        };

        if let Some(ref conn) = connection {
            // 从用户连接映射中移除
            {
                let mut user_connections = self.user_connections.write().await;
                if let Some(user_conn_list) = user_connections.get_mut(&conn.user_id) {
                    user_conn_list.retain(|&id| id != connection_id);
                    if user_conn_list.is_empty() {
                        user_connections.remove(&conn.user_id);
                    }
                }
            }

            // 更新指标
            self.metrics
                .total_connections
                .fetch_sub(1, Ordering::Relaxed);
            if conn.is_active {
                self.metrics
                    .active_connections
                    .fetch_sub(1, Ordering::Relaxed);
            }
            self.metrics
                .total_disconnects
                .fetch_add(1, Ordering::Relaxed);

            // 更新平均连接持续时间
            let duration_secs = conn.connected_at.elapsed().as_secs();
            self.update_avg_duration(duration_secs);

            *self.metrics.last_updated.write().await = Instant::now();

            info!(
                connection_id = %connection_id,
                user_id = %conn.user_id,
                duration_secs = duration_secs,
                "WebSocket连接已移除"
            );
        }

        connection
    }

    /// 更新连接活跃时间
    pub async fn update_activity(&self, connection_id: Uuid) {
        let mut connections = self.connections.write().await;
        if let Some(connection) = connections.get_mut(&connection_id) {
            connection.last_activity = Instant::now();
            debug!(connection_id = %connection_id, "连接活跃时间已更新");
        }
    }

    /// 获取用户的所有连接
    pub async fn get_user_connections(&self, user_id: Uuid) -> Vec<Uuid> {
        let user_connections = self.user_connections.read().await;
        user_connections.get(&user_id).cloned().unwrap_or_default()
    }

    /// 获取连接信息
    pub async fn get_connection(&self, connection_id: Uuid) -> Option<WebSocketConnection> {
        let connections = self.connections.read().await;
        connections.get(&connection_id).cloned()
    }

    /// 获取所有活跃连接
    pub async fn get_active_connections(&self) -> Vec<WebSocketConnection> {
        let connections = self.connections.read().await;
        connections
            .values()
            .filter(|conn| conn.is_active)
            .cloned()
            .collect()
    }

    /// 清理超时连接
    pub async fn cleanup_timeout_connections(&self) -> Vec<Uuid> {
        let timeout_duration = Duration::from_secs(self.config.connection_timeout_secs);
        let now = Instant::now();
        let mut timeout_connections = Vec::new();

        {
            let mut connections = self.connections.write().await;
            let mut to_remove = Vec::new();

            for (id, connection) in connections.iter_mut() {
                if now.duration_since(connection.last_activity) > timeout_duration {
                    connection.is_active = false;
                    to_remove.push(*id);
                    timeout_connections.push(*id);
                }
            }

            for id in to_remove {
                connections.remove(&id);
            }
        }

        // 更新指标
        if !timeout_connections.is_empty() {
            self.metrics
                .timeout_disconnects
                .fetch_add(timeout_connections.len() as u64, Ordering::Relaxed);
            self.metrics
                .total_connections
                .fetch_sub(timeout_connections.len(), Ordering::Relaxed);
            self.metrics
                .active_connections
                .fetch_sub(timeout_connections.len(), Ordering::Relaxed);

            warn!(
                timeout_count = timeout_connections.len(),
                "清理了超时的WebSocket连接"
            );
        }

        timeout_connections
    }

    /// 获取连接池统计信息
    pub async fn get_pool_stats(&self) -> WebSocketPoolStats {
        let _connections = self.connections.read().await;
        let user_connections = self.user_connections.read().await;

        WebSocketPoolStats {
            total_connections: self.metrics.total_connections.load(Ordering::Relaxed),
            active_connections: self.metrics.active_connections.load(Ordering::Relaxed),
            unique_users: user_connections.len(),
            total_connects: self.metrics.total_connects.load(Ordering::Relaxed),
            total_disconnects: self.metrics.total_disconnects.load(Ordering::Relaxed),
            timeout_disconnects: self.metrics.timeout_disconnects.load(Ordering::Relaxed),
            avg_connection_duration_secs: self
                .metrics
                .avg_connection_duration_secs
                .load(Ordering::Relaxed),
            max_connections: self.config.max_connections,
            utilization_rate: self.calculate_utilization_rate(),
        }
    }

    /// 启动清理任务
    pub async fn start_cleanup_task(&self) -> tokio::task::JoinHandle<()> {
        let manager = self.clone();
        let cleanup_interval = Duration::from_secs(self.config.cleanup_interval_secs);

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(cleanup_interval);

            loop {
                interval.tick().await;
                let timeout_connections = manager.cleanup_timeout_connections().await;

                if !timeout_connections.is_empty() {
                    debug!(
                        cleaned_count = timeout_connections.len(),
                        "定期清理任务完成"
                    );
                }
            }
        })
    }

    /// 更新平均连接持续时间
    fn update_avg_duration(&self, new_duration_secs: u64) {
        let total_disconnects = self.metrics.total_disconnects.load(Ordering::Relaxed);
        if total_disconnects > 0 {
            let current_avg = self
                .metrics
                .avg_connection_duration_secs
                .load(Ordering::Relaxed);
            let new_avg =
                (current_avg * (total_disconnects - 1) + new_duration_secs) / total_disconnects;
            self.metrics
                .avg_connection_duration_secs
                .store(new_avg, Ordering::Relaxed);
        }
    }

    /// 计算连接池利用率
    fn calculate_utilization_rate(&self) -> f64 {
        let current = self.metrics.total_connections.load(Ordering::Relaxed) as f64;
        let max = self.config.max_connections as f64;
        if max > 0.0 { current / max } else { 0.0 }
    }
}

impl Clone for WebSocketPoolManager {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            connections: Arc::clone(&self.connections),
            user_connections: Arc::clone(&self.user_connections),
            metrics: Arc::clone(&self.metrics),
        }
    }
}

/// WebSocket连接池统计信息
#[derive(Debug, Clone)]
pub struct WebSocketPoolStats {
    pub total_connections: usize,
    pub active_connections: usize,
    pub unique_users: usize,
    pub total_connects: u64,
    pub total_disconnects: u64,
    pub timeout_disconnects: u64,
    pub avg_connection_duration_secs: u64,
    pub max_connections: usize,
    pub utilization_rate: f64,
}

impl Default for WebSocketPoolConfig {
    fn default() -> Self {
        Self {
            max_connections: 1000,
            connection_timeout_secs: 300, // 5分钟
            heartbeat_interval_secs: 30,
            cleanup_interval_secs: 60,
            enable_metrics: true,
        }
    }
}
