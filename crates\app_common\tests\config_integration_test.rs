//! # 配置管理系统集成测试
//!
//! 测试配置管理系统在实际应用场景中的功能

use app_common::{AppConfig, ConfigError, DatabasePoolConfig, ServerConfig};
use std::env;
use std::time::Duration;

#[test]
#[serial_test::serial]
fn test_config_loading_with_defaults() {
    // 清理环境变量
    unsafe {
        env::remove_var("ENVIRONMENT");
        env::remove_var("HTTP_ADDR");
        env::remove_var("DATABASE_URL");
        env::remove_var("JWT_SECRET");
    }

    let config = AppConfig::from_env_with_dotenv(false).unwrap();

    // 验证默认配置
    assert_eq!(config.environment, "development");
    assert_eq!(config.server.http_addr.to_string(), "127.0.0.1:3000");
    assert_eq!(config.database_url, "sqlite:task_manager.db?mode=rwc");
    assert_eq!(config.jwt_secret, "your-secret-key-change-in-production");
    assert_eq!(config.database_pool.max_connections, 20);
    assert!(config.server.enable_cors);
}

#[test]
#[serial_test::serial]
fn test_production_environment_config() {
    // 保存原始环境变量
    let original_env = env::var("ENVIRONMENT").ok();
    let original_jwt = env::var("JWT_SECRET").ok();
    let original_db = env::var("DATABASE_URL").ok();
    let original_http = env::var("HTTP_ADDR").ok();

    // 设置生产环境配置
    unsafe {
        // 明确清理所有可能影响的环境变量
        env::remove_var("HTTP_ADDR");
        env::remove_var("DATABASE_URL");
        env::remove_var("JWT_SECRET");
        env::remove_var("ENVIRONMENT");

        // 设置生产环境配置
        env::set_var("ENVIRONMENT", "production");
        env::set_var(
            "JWT_SECRET",
            "super-secure-production-jwt-secret-key-with-32-chars",
        );
        env::set_var("DATABASE_URL", "postgresql://user:pass@localhost/proddb");
    }

    let config = AppConfig::from_env_with_dotenv(false).unwrap();

    // 验证生产环境配置
    assert_eq!(config.environment, "production");
    assert_eq!(config.server.http_addr.to_string(), "0.0.0.0:3000");
    assert_eq!(config.database_pool.max_connections, 100);
    assert!(!config.server.enable_cors);
    assert_eq!(config.server.max_request_size, 1024 * 1024); // 1MB

    // 恢复原始环境变量
    unsafe {
        if let Some(env_val) = original_env {
            env::set_var("ENVIRONMENT", env_val);
        } else {
            env::remove_var("ENVIRONMENT");
        }
        if let Some(jwt_val) = original_jwt {
            env::set_var("JWT_SECRET", jwt_val);
        } else {
            env::remove_var("JWT_SECRET");
        }
        if let Some(db_val) = original_db {
            env::set_var("DATABASE_URL", db_val);
        } else {
            env::remove_var("DATABASE_URL");
        }
        if let Some(http_val) = original_http {
            env::set_var("HTTP_ADDR", http_val);
        } else {
            env::remove_var("HTTP_ADDR");
        }
    }
}

#[test]
#[serial_test::serial]
fn test_production_security_validation() {
    // 保存原始环境变量
    let original_env = env::var("ENVIRONMENT").ok();
    let original_jwt = env::var("JWT_SECRET").ok();
    let original_db = env::var("DATABASE_URL").ok();

    // 测试生产环境安全检查 - 弱JWT密钥
    unsafe {
        env::set_var("ENVIRONMENT", "production");
        env::set_var("JWT_SECRET", "weak-key");
        env::set_var("DATABASE_URL", "postgresql://user:pass@localhost/proddb");
    }

    let result = AppConfig::from_env_with_dotenv(false);
    assert!(result.is_err());
    if let Err(ConfigError::ProductionSecurityError { message }) = result {
        assert!(message.contains("强JWT密钥"));
    } else {
        panic!("Expected ProductionSecurityError for weak JWT key");
    }

    // 测试生产环境安全检查 - SQLite数据库
    unsafe {
        env::set_var(
            "JWT_SECRET",
            "super-secure-production-jwt-secret-key-with-32-chars",
        );
        env::set_var("DATABASE_URL", "sqlite:production.db");
    }

    let result = AppConfig::from_env_with_dotenv(false);
    assert!(result.is_err());
    if let Err(ConfigError::ProductionSecurityError { message }) = result {
        assert!(message.contains("SQLite"));
    } else {
        panic!("Expected ProductionSecurityError for SQLite in production");
    }

    // 恢复原始环境变量
    unsafe {
        if let Some(env_val) = original_env {
            env::set_var("ENVIRONMENT", env_val);
        } else {
            env::remove_var("ENVIRONMENT");
        }
        if let Some(jwt_val) = original_jwt {
            env::set_var("JWT_SECRET", jwt_val);
        } else {
            env::remove_var("JWT_SECRET");
        }
        if let Some(db_val) = original_db {
            env::set_var("DATABASE_URL", db_val);
        } else {
            env::remove_var("DATABASE_URL");
        }
    }
}

#[test]
fn test_database_pool_config_validation_scenarios() {
    // 测试无效的最大连接数
    let mut config = DatabasePoolConfig::development();
    config.max_connections = 0;
    assert!(config.validate().is_err());

    // 测试最小连接数大于最大连接数
    config.max_connections = 10;
    config.min_connections = 20;
    assert!(config.validate().is_err());

    // 测试零超时时间
    config.min_connections = 5;
    config.connect_timeout = Duration::from_secs(0);
    assert!(config.validate().is_err());

    // 测试有效配置
    let valid_config = DatabasePoolConfig::production();
    assert!(valid_config.validate().is_ok());
}

#[test]
fn test_server_config_environments() {
    // 测试开发环境配置
    let dev_config = ServerConfig::development();
    assert_eq!(dev_config.http_addr.to_string(), "127.0.0.1:3000");
    assert!(dev_config.enable_cors);
    assert_eq!(dev_config.max_request_size, 10 * 1024 * 1024); // 10MB
    assert_eq!(dev_config.request_timeout, Duration::from_secs(60));

    // 测试生产环境配置
    let prod_config = ServerConfig::production();
    assert_eq!(prod_config.http_addr.to_string(), "0.0.0.0:3000");
    assert!(!prod_config.enable_cors);
    assert_eq!(prod_config.max_request_size, 1024 * 1024); // 1MB
    assert_eq!(prod_config.request_timeout, Duration::from_secs(30));
}

#[test]
#[serial_test::serial]
fn test_config_error_handling() {
    // 保存原始环境变量
    let original_http = env::var("HTTP_ADDR").ok();
    let original_db = env::var("DATABASE_URL").ok();

    // 测试无效的HTTP地址格式
    unsafe {
        env::set_var("HTTP_ADDR", "invalid-address");
        env::remove_var("DATABASE_URL");
    }

    let result = AppConfig::from_env_with_dotenv(false);
    assert!(result.is_err());
    if let Err(ConfigError::EnvVarParseError { var_name, .. }) = result {
        assert_eq!(var_name, "HTTP_ADDR");
    } else {
        panic!("Expected EnvVarParseError for invalid HTTP address");
    }

    // 测试空数据库URL
    unsafe {
        env::remove_var("HTTP_ADDR");
        env::set_var("DATABASE_URL", "");
    }

    let result = AppConfig::from_env_with_dotenv(false);
    assert!(result.is_err());
    if let Err(ConfigError::ValidationError { field, .. }) = result {
        assert_eq!(field, "database_url");
    } else {
        panic!("Expected ValidationError for empty database URL");
    }

    // 恢复原始环境变量
    unsafe {
        if let Some(http_val) = original_http {
            env::set_var("HTTP_ADDR", http_val);
        } else {
            env::remove_var("HTTP_ADDR");
        }
        if let Some(db_val) = original_db {
            env::set_var("DATABASE_URL", db_val);
        } else {
            env::remove_var("DATABASE_URL");
        }
    }
}

#[test]
fn test_config_serialization_roundtrip() {
    let original_config = AppConfig {
        server: ServerConfig::development(),
        database_url: "sqlite:test.db".to_string(),
        database_pool: DatabasePoolConfig::development(),
        jwt_secret: "test-secret".to_string(),
        environment: "test".to_string(),
    };

    // 序列化
    let json = serde_json::to_string(&original_config).unwrap();

    // 反序列化
    let deserialized_config: AppConfig = serde_json::from_str(&json).unwrap();

    // 验证数据一致性
    assert_eq!(
        original_config.database_url,
        deserialized_config.database_url
    );
    assert_eq!(original_config.jwt_secret, deserialized_config.jwt_secret);
    assert_eq!(original_config.environment, deserialized_config.environment);
    assert_eq!(
        original_config.server.http_addr,
        deserialized_config.server.http_addr
    );
    assert_eq!(
        original_config.database_pool.max_connections,
        deserialized_config.database_pool.max_connections
    );
}

#[test]
fn test_database_pool_config_performance_settings() {
    let prod_config = DatabasePoolConfig::production();

    // 验证生产环境性能设置
    assert_eq!(prod_config.max_connections, 100);
    assert_eq!(prod_config.min_connections, 10);
    assert!(prod_config.tcp_nodelay); // 减少网络延迟
    assert!(prod_config.tcp_keepalive); // 检测断开的连接
    assert_eq!(prod_config.connect_timeout, Duration::from_secs(30));
    assert_eq!(prod_config.idle_timeout, Duration::from_secs(600)); // 10分钟
    assert_eq!(prod_config.max_lifetime, Some(Duration::from_secs(3600))); // 1小时
    assert_eq!(prod_config.acquire_timeout, Duration::from_secs(10));

    let dev_config = DatabasePoolConfig::development();

    // 验证开发环境设置
    assert_eq!(dev_config.max_connections, 20);
    assert_eq!(dev_config.min_connections, 2);
    assert!(dev_config.tcp_nodelay);
    assert!(!dev_config.tcp_keepalive); // 开发环境关闭保活
    assert_eq!(dev_config.connect_timeout, Duration::from_secs(10));
    assert_eq!(dev_config.idle_timeout, Duration::from_secs(300)); // 5分钟
    assert_eq!(dev_config.max_lifetime, Some(Duration::from_secs(1800))); // 30分钟
    assert_eq!(dev_config.acquire_timeout, Duration::from_secs(5));
}

#[test]
#[serial_test::serial]
fn test_environment_detection() {
    // 保存原始环境变量
    let original_env = env::var("ENVIRONMENT").ok();

    // 测试不同环境值的检测
    let test_cases = vec![
        ("production", true),
        ("PRODUCTION", true),
        ("Production", true),
        ("development", false),
        ("dev", false),
        ("test", false),
        ("staging", false),
    ];

    for (env_value, is_production) in test_cases {
        unsafe {
            env::set_var("ENVIRONMENT", env_value);
            env::remove_var("HTTP_ADDR");
            env::remove_var("DATABASE_URL");
            if is_production {
                // 生产环境需要强JWT密钥和非SQLite数据库
                env::set_var(
                    "JWT_SECRET",
                    "super-secure-production-jwt-secret-key-with-32-chars",
                );
                env::set_var("DATABASE_URL", "postgresql://user:pass@localhost/proddb");
            } else {
                env::remove_var("JWT_SECRET");
            }
        }

        let config = AppConfig::from_env_with_dotenv(false).unwrap();

        if is_production {
            assert_eq!(config.database_pool.max_connections, 100);
            assert!(!config.server.enable_cors);
        } else {
            assert_eq!(config.database_pool.max_connections, 20);
            assert!(config.server.enable_cors);
        }
    }

    // 恢复原始环境变量
    unsafe {
        if let Some(env_val) = original_env {
            env::set_var("ENVIRONMENT", env_val);
        } else {
            env::remove_var("ENVIRONMENT");
        }
    }
}
