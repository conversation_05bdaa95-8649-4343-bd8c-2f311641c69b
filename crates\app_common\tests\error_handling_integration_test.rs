//! # 错误处理中间件集成测试
//!
//! 测试错误处理中间件在实际应用场景中的功能

use app_common::{
    error::AppError,
    middleware::{
        error_recovery_middleware, handle_auth_error, handle_timeout_error, handle_validation_error,
    },
};
use axum::{
    Json, Router,
    body::Body,
    extract::Request,
    http::{Method, StatusCode},
    middleware,
    routing::{get, post},
};
use serde_json::{Value, json};
use std::time::Duration;
use tower::ServiceExt;
use uuid::Uuid;

/// 创建测试应用
fn create_test_app() -> Router {
    Router::new()
        .route("/health", get(health_check))
        .route("/tasks/{id}", get(get_task))
        .route("/tasks", post(create_task))
        .route("/timeout", get(timeout_endpoint))
        .route("/panic", get(panic_endpoint))
        .layer(middleware::from_fn(error_recovery_middleware))
}

/// 健康检查端点
async fn health_check() -> &'static str {
    "OK"
}

/// 获取任务端点 - 测试各种错误场景
async fn get_task(
    axum::extract::Path(id): axum::extract::Path<String>,
) -> Result<Json<Value>, AppError> {
    match id.as_str() {
        "not-found" => Err(AppError::TaskNotFound(Uuid::new_v4())),
        "bad-request" => Err(AppError::BadRequest("无效的任务ID格式".to_string())),
        "forbidden" => Err(AppError::Forbidden("权限不足".to_string())),
        "internal-error" => Err(AppError::InternalServerError("内部服务器错误".to_string())),
        "valid-id" => Ok(Json(json!({
            "id": id,
            "title": "测试任务",
            "status": "pending"
        }))),
        _ => Err(AppError::BadRequest("未知的任务ID".to_string())),
    }
}

/// 创建任务端点 - 测试验证错误
async fn create_task(Json(payload): Json<Value>) -> Result<Json<Value>, AppError> {
    if payload.get("title").is_none() {
        return Err(AppError::ValidationError(
            "缺少必需的字段: title".to_string(),
        ));
    }

    if payload["title"].as_str().unwrap_or("").is_empty() {
        return Err(AppError::ValidationError("title 字段不能为空".to_string()));
    }

    Ok(Json(json!({
        "id": Uuid::new_v4(),
        "title": payload["title"],
        "status": "created"
    })))
}

/// 超时端点 - 测试超时处理
async fn timeout_endpoint() -> &'static str {
    tokio::time::sleep(Duration::from_secs(35)).await;
    "This should timeout"
}

/// Panic端点 - 测试panic恢复
async fn panic_endpoint() -> &'static str {
    panic!("测试panic恢复");
}

#[tokio::test]
async fn test_health_check() {
    let app = create_test_app();

    let request = Request::builder()
        .method(Method::GET)
        .uri("/health")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::OK);

    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    assert_eq!(&body[..], b"OK");
}

#[tokio::test]
async fn test_task_not_found_error() {
    let app = create_test_app();

    let request = Request::builder()
        .method(Method::GET)
        .uri("/tasks/not-found")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::NOT_FOUND);

    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    let json: Value = serde_json::from_slice(&body).unwrap();

    assert_eq!(json["error"]["code"], "NOT_FOUND");
    assert_eq!(json["error"]["status"], 404);
    assert!(
        json["error"]["message"]
            .as_str()
            .unwrap()
            .contains("未找到")
    );
}

#[tokio::test]
async fn test_bad_request_error() {
    let app = create_test_app();

    let request = Request::builder()
        .method(Method::GET)
        .uri("/tasks/bad-request")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    let json: Value = serde_json::from_slice(&body).unwrap();

    assert_eq!(json["error"]["code"], "VALIDATION_ERROR");
    assert_eq!(json["error"]["status"], 400);
    assert!(
        json["error"]["message"]
            .as_str()
            .unwrap()
            .contains("输入验证失败")
    );
}

#[tokio::test]
async fn test_forbidden_error() {
    let app = create_test_app();

    let request = Request::builder()
        .method(Method::GET)
        .uri("/tasks/forbidden")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::FORBIDDEN);

    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    let json: Value = serde_json::from_slice(&body).unwrap();

    assert_eq!(json["error"]["code"], "FORBIDDEN");
    assert_eq!(json["error"]["status"], 403);
    assert!(
        json["error"]["message"]
            .as_str()
            .unwrap()
            .contains("权限不足")
    );
}

#[tokio::test]
async fn test_internal_server_error() {
    let app = create_test_app();

    let request = Request::builder()
        .method(Method::GET)
        .uri("/tasks/internal-error")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::INTERNAL_SERVER_ERROR);

    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    let json: Value = serde_json::from_slice(&body).unwrap();

    assert_eq!(json["error"]["code"], "INTERNAL_SERVER_ERROR");
    assert_eq!(json["error"]["status"], 500);
    assert!(
        json["error"]["message"]
            .as_str()
            .unwrap()
            .contains("服务器内部错误")
    );
}

#[tokio::test]
async fn test_validation_error() {
    let app = create_test_app();

    let request = Request::builder()
        .method(Method::POST)
        .uri("/tasks")
        .header("content-type", "application/json")
        .body(Body::from(r#"{"description": "测试任务"}"#))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    let json: Value = serde_json::from_slice(&body).unwrap();

    assert_eq!(json["error"]["code"], "VALIDATION_ERROR");
    assert_eq!(json["error"]["status"], 400);
    assert!(
        json["error"]["message"]
            .as_str()
            .unwrap()
            .contains("输入验证失败")
    );
}

#[tokio::test]
async fn test_successful_task_creation() {
    let app = create_test_app();

    let request = Request::builder()
        .method(Method::POST)
        .uri("/tasks")
        .header("content-type", "application/json")
        .body(Body::from(
            r#"{"title": "新任务", "description": "测试任务"}"#,
        ))
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::OK);

    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    let json: Value = serde_json::from_slice(&body).unwrap();

    assert_eq!(json["title"], "新任务");
    assert_eq!(json["status"], "created");
    assert!(json["id"].is_string());
}

#[tokio::test]
async fn test_successful_task_retrieval() {
    let app = create_test_app();

    let request = Request::builder()
        .method(Method::GET)
        .uri("/tasks/valid-id")
        .body(Body::empty())
        .unwrap();

    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::OK);

    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    let json: Value = serde_json::from_slice(&body).unwrap();

    assert_eq!(json["id"], "valid-id");
    assert_eq!(json["title"], "测试任务");
    assert_eq!(json["status"], "pending");
}

#[tokio::test]
async fn test_error_handlers_directly() {
    // 测试超时错误处理器
    let timeout_error = tower::timeout::error::Elapsed::new();
    let boxed_error: tower::BoxError = Box::new(timeout_error);
    let response = handle_timeout_error(boxed_error).await.unwrap();
    assert_eq!(response.status(), StatusCode::REQUEST_TIMEOUT);

    // 测试验证错误处理器
    let validation_error = std::io::Error::new(std::io::ErrorKind::InvalidInput, "验证失败");
    let boxed_error: tower::BoxError = Box::new(validation_error);
    let response = handle_validation_error(boxed_error).await.unwrap();
    assert_eq!(response.status(), StatusCode::BAD_REQUEST);

    // 测试认证错误处理器
    let auth_error = std::io::Error::new(std::io::ErrorKind::PermissionDenied, "认证失败");
    let boxed_error: tower::BoxError = Box::new(auth_error);
    let response = handle_auth_error(boxed_error).await.unwrap();
    assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
}
