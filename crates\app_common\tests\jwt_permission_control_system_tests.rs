//! # JWT权限控制系统集成测试
//!
//! 本测试模块验证JWT权限控制系统的完整功能，包括：
//! - JWT token生成和验证
//! - 用户角色权限检查
//! - 权限中间件功能
//! - token刷新机制
//! - 权限变更事件处理
//! - 安全性验证
//!
//! ## 测试覆盖范围
//! - 单元测试：JWT工具类、权限检查器
//! - 集成测试：中间件集成、API权限验证
//! - 端到端测试：完整的认证流程
//! - 安全测试：token篡改、过期处理
//! - 性能测试：高并发权限验证

use app_common::error::{AppError, Result};
use chrono::{Duration, Utc};

const TEST_JWT_SECRET: &str = "test-jwt-permission-control-secret-key-2025";
const TEST_USER_ID: &str = "550e8400-e29b-41d4-a716-446655440000";
const TEST_USERNAME: &str = "test_user";

/// 用户角色枚举
#[derive(Debug, Clone, PartialEq)]
pub enum UserRole {
    Admin,
    Manager,
    User,
    Guest,
}

impl UserRole {
    /// 获取角色权限级别（数字越大权限越高）
    pub fn permission_level(&self) -> u8 {
        match self {
            UserRole::Admin => 100,
            UserRole::Manager => 75,
            UserRole::User => 50,
            UserRole::Guest => 25,
        }
    }
}

/// 权限操作枚举
#[derive(Debug, Clone, PartialEq)]
pub enum Permission {
    Read,
    Write,
    Delete,
    Admin,
}

impl Permission {
    /// 获取操作所需的最低权限级别
    pub fn required_level(&self) -> u8 {
        match self {
            Permission::Read => 25,
            Permission::Write => 50,
            Permission::Delete => 75,
            Permission::Admin => 100,
        }
    }
}

/// 权限检查器
pub struct PermissionChecker;

impl PermissionChecker {
    /// 检查用户是否有执行指定操作的权限
    pub fn has_permission(user_role: &UserRole, permission: &Permission) -> bool {
        user_role.permission_level() >= permission.required_level()
    }

    /// 检查用户是否可以访问指定资源
    pub fn can_access_resource(
        user_role: &UserRole,
        resource_owner_id: &str,
        current_user_id: &str,
    ) -> bool {
        // 管理员可以访问所有资源
        if matches!(user_role, UserRole::Admin) {
            return true;
        }

        // 用户只能访问自己的资源
        resource_owner_id == current_user_id
    }
}

/// 扩展的JWT Claims，包含角色信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct ExtendedClaims {
    pub sub: String,
    pub username: String,
    pub role: String,
    pub exp: i64,
    pub iat: i64,
}

impl ExtendedClaims {
    /// 创建新的扩展Claims
    pub fn new(user_id: &str, username: &str, role: UserRole, expires_in_hours: i64) -> Self {
        let now = Utc::now();
        Self {
            sub: user_id.to_string(),
            username: username.to_string(),
            role: format!("{role:?}"),
            iat: now.timestamp(),
            exp: (now + Duration::hours(expires_in_hours)).timestamp(),
        }
    }

    /// 获取用户角色
    pub fn get_role(&self) -> Result<UserRole> {
        match self.role.as_str() {
            "Admin" => Ok(UserRole::Admin),
            "Manager" => Ok(UserRole::Manager),
            "User" => Ok(UserRole::User),
            "Guest" => Ok(UserRole::Guest),
            _ => Err(AppError::ValidationError("无效的用户角色".to_string())),
        }
    }

    /// 检查token是否即将过期（30分钟内）
    pub fn is_expiring_soon(&self) -> bool {
        let now = Utc::now().timestamp();
        let threshold = 30 * 60; // 30分钟
        self.exp - now <= threshold
    }
}

/// 扩展的JWT工具类，支持角色权限
pub struct ExtendedJwtUtils {
    secret: String,
}

impl ExtendedJwtUtils {
    /// 创建新的扩展JWT工具实例
    pub fn new(secret: String) -> Self {
        Self { secret }
    }

    /// 创建包含角色信息的JWT token
    pub fn create_token_with_role(
        &self,
        user_id: &str,
        username: &str,
        role: UserRole,
        expires_in_hours: i64,
    ) -> Result<String> {
        let claims = ExtendedClaims::new(user_id, username, role, expires_in_hours);

        use jsonwebtoken::{EncodingKey, Header, encode};
        encode(
            &Header::default(),
            &claims,
            &EncodingKey::from_secret(self.secret.as_ref()),
        )
        .map_err(|e| AppError::InternalServerError(format!("JWT token创建失败: {e}")))
    }

    /// 验证JWT token并返回扩展Claims
    pub fn validate_token_with_role(&self, token: &str) -> Result<ExtendedClaims> {
        if token.is_empty() {
            return Err(AppError::ValidationError("Token不能为空".to_string()));
        }

        use jsonwebtoken::{DecodingKey, Validation, decode};
        match decode::<ExtendedClaims>(
            token,
            &DecodingKey::from_secret(self.secret.as_ref()),
            &Validation::default(),
        ) {
            Ok(token_data) => Ok(token_data.claims),
            Err(err) => match err.kind() {
                jsonwebtoken::errors::ErrorKind::ExpiredSignature => {
                    Err(AppError::InvalidToken("Token已过期".to_string()))
                }
                _ => Err(AppError::InvalidToken("Token无效".to_string())),
            },
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试权限检查器基本功能
    #[test]
    fn test_permission_checker_basic_permissions() {
        // 测试管理员权限
        assert!(PermissionChecker::has_permission(
            &UserRole::Admin,
            &Permission::Read
        ));
        assert!(PermissionChecker::has_permission(
            &UserRole::Admin,
            &Permission::Write
        ));
        assert!(PermissionChecker::has_permission(
            &UserRole::Admin,
            &Permission::Delete
        ));
        assert!(PermissionChecker::has_permission(
            &UserRole::Admin,
            &Permission::Admin
        ));

        // 测试普通用户权限
        assert!(PermissionChecker::has_permission(
            &UserRole::User,
            &Permission::Read
        ));
        assert!(PermissionChecker::has_permission(
            &UserRole::User,
            &Permission::Write
        ));
        assert!(!PermissionChecker::has_permission(
            &UserRole::User,
            &Permission::Delete
        ));
        assert!(!PermissionChecker::has_permission(
            &UserRole::User,
            &Permission::Admin
        ));

        // 测试访客权限
        assert!(PermissionChecker::has_permission(
            &UserRole::Guest,
            &Permission::Read
        ));
        assert!(!PermissionChecker::has_permission(
            &UserRole::Guest,
            &Permission::Write
        ));
        assert!(!PermissionChecker::has_permission(
            &UserRole::Guest,
            &Permission::Delete
        ));
        assert!(!PermissionChecker::has_permission(
            &UserRole::Guest,
            &Permission::Admin
        ));
    }

    /// 测试资源访问权限
    #[test]
    fn test_resource_access_permissions() {
        let user_id = "user123";
        let other_user_id = "user456";

        // 管理员可以访问所有资源
        assert!(PermissionChecker::can_access_resource(
            &UserRole::Admin,
            other_user_id,
            user_id
        ));

        // 普通用户只能访问自己的资源
        assert!(PermissionChecker::can_access_resource(
            &UserRole::User,
            user_id,
            user_id
        ));
        assert!(!PermissionChecker::can_access_resource(
            &UserRole::User,
            other_user_id,
            user_id
        ));
    }

    /// 测试扩展JWT工具类
    #[test]
    fn test_extended_jwt_utils() {
        let jwt_utils = ExtendedJwtUtils::new(TEST_JWT_SECRET.to_string());

        // 创建token
        let token = jwt_utils
            .create_token_with_role(TEST_USER_ID, TEST_USERNAME, UserRole::Admin, 1)
            .unwrap();

        assert!(!token.is_empty());

        // 验证token
        let claims = jwt_utils.validate_token_with_role(&token).unwrap();
        assert_eq!(claims.sub, TEST_USER_ID);
        assert_eq!(claims.username, TEST_USERNAME);
        assert_eq!(claims.get_role().unwrap(), UserRole::Admin);
    }

    /// 测试token过期检查
    #[test]
    fn test_token_expiration_check() {
        let jwt_utils = ExtendedJwtUtils::new(TEST_JWT_SECRET.to_string());

        // 创建即将过期的token（1分钟）
        let short_token = jwt_utils
            .create_token_with_role(TEST_USER_ID, TEST_USERNAME, UserRole::User, 1)
            .unwrap();

        let claims = jwt_utils.validate_token_with_role(&short_token).unwrap();
        // 由于是1小时过期，不应该即将过期
        assert!(!claims.is_expiring_soon());
    }

    /// 测试无效token处理
    #[test]
    fn test_invalid_token_handling() {
        let jwt_utils = ExtendedJwtUtils::new(TEST_JWT_SECRET.to_string());

        // 测试空token
        let result = jwt_utils.validate_token_with_role("");
        assert!(result.is_err());

        // 测试格式错误的token
        let result = jwt_utils.validate_token_with_role("invalid.token.format");
        assert!(result.is_err());

        // 测试使用错误密钥签名的token
        let wrong_jwt_utils = ExtendedJwtUtils::new("wrong-secret".to_string());
        let token = jwt_utils
            .create_token_with_role(TEST_USER_ID, TEST_USERNAME, UserRole::User, 1)
            .unwrap();

        let result = wrong_jwt_utils.validate_token_with_role(&token);
        assert!(result.is_err());
    }
}
