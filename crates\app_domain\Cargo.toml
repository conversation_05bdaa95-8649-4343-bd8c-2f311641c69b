[package]
name = "app_domain"
version = "0.1.0"
edition = "2024"
license = "MIT OR Apache-2.0"
authors = ["Rust学习者"]
description = "领域层：核心业务模型、实体和规则"

[dependencies]
# 公共模块依赖
app_common = { workspace = true, features = ["testing"] }

# 数据库迁移模块（用于实体转换） - 已移除对原项目migration的依赖

# 序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 时间处理
chrono = { workspace = true }

# UUID 支持
uuid = { workspace = true }

# 异步支持
async-trait = { workspace = true }

# 输入验证 - 升级到0.20修复RUSTSEC-2024-0421 (idna漏洞)
validator = { version = "0.20", features = ["derive"] }

# 正则表达式（用于用户名验证）
regex = "1.10"

# 懒加载静态变量
lazy_static = "1.4"

# SeaORM（用于ActiveValue）
sea-orm = { workspace = true }

[dev-dependencies]
# 异步测试运行时
tokio = { workspace = true }
# 测试依赖
tokio-test = "0.4"
tempfile = "3.8"
mockall = "0.12"
