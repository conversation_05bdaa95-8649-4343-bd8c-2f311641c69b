//! # 认证领域实体
//!
//! 定义认证相关的领域实体和值对象，包括：
//! - 注册请求实体
//! - 登录请求实体
//! - 认证令牌实体
//! - 密码相关值对象

use crate::entities::{DateTime, Deserialize, Serialize, Utc, Uuid};
use lazy_static::lazy_static;
use regex::Regex;

// 用户名验证正则表达式
lazy_static! {
    /// 用户名验证规则：只允许字母、数字、下划线和连字符
    static ref USERNAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_-]+$").unwrap();
}

// 注意：RegisterRequest和LoginRequest已迁移到app_interfaces crate
// 这些是API契约层的责任，不应该在领域层定义

/// 认证令牌领域实体
///
/// 设计原则：
/// - 封装JWT令牌的业务逻辑
/// - 包含令牌的完整生命周期信息
/// - 提供令牌验证和刷新机制
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct AuthToken {
    /// 访问令牌
    pub access_token: String,

    /// 刷新令牌（可选）
    pub refresh_token: Option<String>,

    /// 令牌类型（通常为 "Bearer"）
    pub token_type: String,

    /// 令牌过期时间（秒）
    pub expires_in: i64,

    /// 令牌创建时间
    pub created_at: DateTime<Utc>,

    /// 令牌过期时间
    pub expires_at: DateTime<Utc>,

    /// 关联的用户ID
    pub user_id: Uuid,

    /// 令牌作用域（可选）
    pub scope: Option<String>,
}

/// 密码哈希值对象
///
/// 设计原则：
/// - 封装密码哈希的业务逻辑
/// - 确保密码安全存储
/// - 提供密码验证接口
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct PasswordHash {
    /// 哈希值
    pub hash: String,

    /// 盐值（如果使用）
    pub salt: Option<String>,

    /// 哈希算法标识
    pub algorithm: String,

    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 用户认证会话信息值对象
///
/// 设计原则：
/// - 封装用户认证会话的核心信息
/// - 提供会话状态管理
/// - 支持多设备登录场景
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub struct AuthUserSession {
    /// 会话ID
    pub session_id: Uuid,

    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: String,

    /// 设备信息
    pub device_info: Option<String>,

    /// IP地址
    pub ip_address: String,

    /// 用户代理
    pub user_agent: Option<String>,

    /// 登录时间
    pub login_at: DateTime<Utc>,

    /// 最后活跃时间
    pub last_activity_at: DateTime<Utc>,

    /// 会话是否有效
    pub is_active: bool,
}

// RegisterRequest和LoginRequest的实现已迁移到app_interfaces crate

impl AuthToken {
    /// 创建新的认证令牌
    ///
    /// 【功能】: 便捷方法创建认证令牌实体
    /// 【参数】:
    /// * `access_token` - 访问令牌
    /// * `user_id` - 用户ID
    /// * `expires_in` - 过期时间（秒）
    ///
    /// 【返回值】: AuthToken 实例
    pub fn new(access_token: String, user_id: Uuid, expires_in: i64) -> Self {
        let now = Utc::now();
        let expires_at = now + chrono::Duration::seconds(expires_in);

        Self {
            access_token,
            refresh_token: None,
            token_type: "Bearer".to_string(),
            expires_in,
            created_at: now,
            expires_at,
            user_id,
            scope: None,
        }
    }

    /// 检查令牌是否已过期
    ///
    /// 【功能】: 检查令牌是否已过期
    /// 【返回值】: 是否已过期
    pub fn is_expired(&self) -> bool {
        Utc::now() > self.expires_at
    }

    /// 获取剩余有效时间（秒）
    ///
    /// 【功能】: 获取令牌剩余有效时间
    /// 【返回值】: 剩余秒数，如果已过期则返回0
    pub fn remaining_seconds(&self) -> i64 {
        let remaining = self.expires_at - Utc::now();
        remaining.num_seconds().max(0)
    }
}

impl PasswordHash {
    /// 创建新的密码哈希
    ///
    /// 【功能】: 便捷方法创建密码哈希值对象
    /// 【参数】:
    /// * `hash` - 哈希值
    /// * `algorithm` - 哈希算法
    ///
    /// 【返回值】: PasswordHash 实例
    pub fn new(hash: String, algorithm: String) -> Self {
        Self {
            hash,
            salt: None,
            algorithm,
            created_at: Utc::now(),
        }
    }

    /// 创建带盐值的密码哈希
    ///
    /// 【功能】: 便捷方法创建带盐值的密码哈希值对象
    /// 【参数】:
    /// * `hash` - 哈希值
    /// * `salt` - 盐值
    /// * `algorithm` - 哈希算法
    ///
    /// 【返回值】: PasswordHash 实例
    pub fn new_with_salt(hash: String, salt: String, algorithm: String) -> Self {
        Self {
            hash,
            salt: Some(salt),
            algorithm,
            created_at: Utc::now(),
        }
    }
}

impl AuthUserSession {
    /// 创建新的用户认证会话
    ///
    /// 【功能】: 便捷方法创建用户认证会话值对象
    /// 【参数】:
    /// * `user_id` - 用户ID
    /// * `username` - 用户名
    /// * `ip_address` - IP地址
    ///
    /// 【返回值】: AuthUserSession 实例
    pub fn new(user_id: Uuid, username: String, ip_address: String) -> Self {
        let now = Utc::now();
        Self {
            session_id: Uuid::new_v4(),
            user_id,
            username,
            device_info: None,
            ip_address,
            user_agent: None,
            login_at: now,
            last_activity_at: now,
            is_active: true,
        }
    }

    /// 更新最后活跃时间
    ///
    /// 【功能】: 更新会话的最后活跃时间
    pub fn update_activity(&mut self) {
        self.last_activity_at = Utc::now();
    }

    /// 检查会话是否超时
    ///
    /// 【功能】: 检查会话是否因长时间不活跃而超时
    /// 【参数】:
    /// * `timeout_minutes` - 超时时间（分钟）
    ///
    /// 【返回值】: 是否超时
    pub fn is_timeout(&self, timeout_minutes: i64) -> bool {
        let timeout_duration = chrono::Duration::minutes(timeout_minutes);
        Utc::now() - self.last_activity_at > timeout_duration
    }

    /// 终止会话
    ///
    /// 【功能】: 将会话标记为非活跃状态
    pub fn terminate(&mut self) {
        self.is_active = false;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    // 注意：RegisterRequest和LoginRequest的测试已迁移到app_interfaces crate

    #[test]
    fn test_auth_token() {
        let user_id = Uuid::new_v4();
        let token = AuthToken::new("test_token".to_string(), user_id, 3600);

        assert_eq!(token.access_token, "test_token");
        assert_eq!(token.user_id, user_id);
        assert_eq!(token.expires_in, 3600);
        assert!(!token.is_expired());
        assert!(token.remaining_seconds() > 0);
    }

    #[test]
    fn test_auth_user_session() {
        let user_id = Uuid::new_v4();
        let mut session =
            AuthUserSession::new(user_id, "test_user".to_string(), "127.0.0.1".to_string());

        assert_eq!(session.user_id, user_id);
        assert_eq!(session.username, "test_user");
        assert!(session.is_active);
        assert!(!session.is_timeout(60)); // 60分钟超时

        session.update_activity();
        assert!(!session.is_timeout(60));

        session.terminate();
        assert!(!session.is_active);
    }
}
