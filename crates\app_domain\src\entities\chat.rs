//! # 聊天实体
//!
//! 聊天相关的领域实体，包括全局聊天室实体和相关业务逻辑

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::{Validate, ValidationErrors};

/// 全局聊天室实体
///
/// 设计原则：
/// - 封装全局聊天室的核心业务逻辑
/// - 维护全局聊天室数据的不变性约束
/// - 与数据库模型解耦，专注于业务规则
/// - 提供类型安全的全局聊天室操作接口
///
/// 业务特性：
/// - 全局聊天室是系统级别的聊天室，所有用户都可以访问
/// - 支持消息发送、搜索和历史记录获取
/// - 具有消息统计和管理功能
/// - 支持消息过期和清理机制
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Validate)]
pub struct GlobalChatRoom {
    /// 全局聊天室唯一标识符
    pub id: Uuid,

    /// 全局聊天室名称
    #[validate(length(
        min = 1,
        max = 100,
        message = "全局聊天室名称长度必须在1-100个字符之间"
    ))]
    pub name: String,

    /// 全局聊天室描述
    #[validate(length(max = 1000, message = "全局聊天室描述不能超过1000个字符"))]
    pub description: Option<String>,

    /// 是否启用状态
    pub is_enabled: bool,

    /// 最大消息数量限制（0表示无限制）
    #[validate(range(min = 0, max = 1000000, message = "最大消息数量必须在0-1000000之间"))]
    pub max_messages: i32,

    /// 当前消息数量
    #[validate(range(min = 0, message = "当前消息数量不能为负数"))]
    pub current_messages: i32,

    /// 消息保留天数（0表示永久保留）
    #[validate(range(min = 0, max = 365, message = "消息保留天数必须在0-365之间"))]
    pub message_retention_days: i32,

    /// 全局聊天室设置（JSON格式存储扩展配置）
    pub settings: Option<String>,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 创建全局聊天室的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateGlobalChatRoomRequest {
    #[validate(length(
        min = 1,
        max = 100,
        message = "全局聊天室名称长度必须在1-100个字符之间"
    ))]
    pub name: String,

    #[validate(length(max = 1000, message = "全局聊天室描述不能超过1000个字符"))]
    pub description: Option<String>,

    #[validate(range(min = 0, max = 1000000, message = "最大消息数量必须在0-1000000之间"))]
    pub max_messages: Option<i32>,

    #[validate(range(min = 0, max = 365, message = "消息保留天数必须在0-365之间"))]
    pub message_retention_days: Option<i32>,

    pub settings: Option<String>,
}

/// 更新全局聊天室的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct UpdateGlobalChatRoomRequest {
    #[validate(length(
        min = 1,
        max = 100,
        message = "全局聊天室名称长度必须在1-100个字符之间"
    ))]
    pub name: Option<String>,

    #[validate(length(max = 1000, message = "全局聊天室描述不能超过1000个字符"))]
    pub description: Option<String>,

    pub is_enabled: Option<bool>,

    #[validate(range(min = 0, max = 1000000, message = "最大消息数量必须在0-1000000之间"))]
    pub max_messages: Option<i32>,

    #[validate(range(min = 0, max = 365, message = "消息保留天数必须在0-365之间"))]
    pub message_retention_days: Option<i32>,

    pub settings: Option<String>,
}

/// 全局聊天室消息搜索请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct SearchGlobalChatRoomMessagesRequest {
    /// 搜索关键词
    #[validate(length(min = 1, max = 100, message = "搜索关键词长度必须在1-100个字符之间"))]
    pub query: String,

    /// 搜索结果数量限制
    #[validate(range(min = 1, max = 100, message = "搜索结果数量限制必须在1-100之间"))]
    pub limit: Option<u32>,

    /// 搜索起始时间（可选）
    pub start_time: Option<DateTime<Utc>>,

    /// 搜索结束时间（可选）
    pub end_time: Option<DateTime<Utc>>,

    /// 发送者用户ID过滤（可选）
    pub sender_id: Option<Uuid>,
}

/// 获取全局聊天室历史消息请求
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct GetGlobalChatRoomHistoryRequest {
    /// 消息数量限制
    #[validate(range(min = 1, max = 100, message = "消息数量限制必须在1-100之间"))]
    pub limit: Option<u32>,

    /// 获取此时间之前的消息（用于分页）
    pub before: Option<DateTime<Utc>>,

    /// 获取此时间之后的消息（用于分页）
    pub after: Option<DateTime<Utc>>,
}

impl GlobalChatRoom {
    /// 创建新的全局聊天室
    ///
    /// # 参数
    /// - `name`: 全局聊天室名称
    /// - `description`: 全局聊天室描述（可选）
    /// - `max_messages`: 最大消息数量（可选，默认为0表示无限制）
    /// - `message_retention_days`: 消息保留天数（可选，默认为0表示永久保留）
    /// - `settings`: 全局聊天室设置（可选）
    ///
    /// # 返回
    /// - `Result<GlobalChatRoom, ValidationErrors>`: 成功返回全局聊天室实体，失败返回验证错误
    pub fn new(
        name: String,
        description: Option<String>,
        max_messages: Option<i32>,
        message_retention_days: Option<i32>,
        settings: Option<String>,
    ) -> Result<Self, ValidationErrors> {
        let now = Utc::now();
        let global_chat_room = Self {
            id: Uuid::new_v4(),
            name,
            description,
            is_enabled: true,
            max_messages: max_messages.unwrap_or(0),
            current_messages: 0,
            message_retention_days: message_retention_days.unwrap_or(0),
            settings,
            created_at: now,
            updated_at: now,
        };

        // 验证全局聊天室数据
        global_chat_room.validate()?;
        Ok(global_chat_room)
    }

    /// 更新全局聊天室名称
    ///
    /// # 参数
    /// - `new_name`: 新的全局聊天室名称
    ///
    /// # 返回
    /// - `Result<(), ValidationErrors>`: 成功返回空，失败返回验证错误
    pub fn update_name(&mut self, new_name: String) -> Result<(), ValidationErrors> {
        // 创建临时全局聊天室进行验证
        let temp_room = Self {
            name: new_name.clone(),
            ..self.clone()
        };

        // 验证新名称
        temp_room.validate()?;

        // 更新名称和时间戳
        self.name = new_name;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 更新全局聊天室描述
    ///
    /// # 参数
    /// - `new_description`: 新的全局聊天室描述
    ///
    /// # 返回
    /// - `Result<(), ValidationErrors>`: 成功返回空，失败返回验证错误
    pub fn update_description(
        &mut self,
        new_description: Option<String>,
    ) -> Result<(), ValidationErrors> {
        // 创建临时全局聊天室进行验证
        let temp_room = Self {
            description: new_description.clone(),
            ..self.clone()
        };

        // 验证新描述
        temp_room.validate()?;

        // 更新描述和时间戳
        self.description = new_description;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 启用全局聊天室
    pub fn enable(&mut self) {
        self.is_enabled = true;
        self.updated_at = Utc::now();
    }

    /// 禁用全局聊天室
    pub fn disable(&mut self) {
        self.is_enabled = false;
        self.updated_at = Utc::now();
    }

    /// 增加消息数量
    ///
    /// # 返回
    /// - `Result<(), String>`: 成功返回空，失败返回错误信息
    pub fn add_message(&mut self) -> Result<(), String> {
        if self.max_messages > 0 && self.current_messages >= self.max_messages {
            return Err("全局聊天室已达到最大消息数量限制".to_string());
        }

        self.current_messages += 1;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 减少消息数量
    ///
    /// # 返回
    /// - `Result<(), String>`: 成功返回空，失败返回错误信息
    pub fn remove_message(&mut self) -> Result<(), String> {
        if self.current_messages <= 0 {
            return Err("全局聊天室消息数量不能为负数".to_string());
        }

        self.current_messages -= 1;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 检查是否可以添加更多消息
    pub fn can_add_message(&self) -> bool {
        self.max_messages == 0 || self.current_messages < self.max_messages
    }

    /// 检查全局聊天室是否启用
    pub fn is_active(&self) -> bool {
        self.is_enabled
    }

    /// 检查消息是否需要清理（基于保留天数）
    ///
    /// # 参数
    /// - `message_created_at`: 消息创建时间
    ///
    /// # 返回
    /// - `bool`: 消息是否需要清理
    pub fn should_cleanup_message(&self, message_created_at: DateTime<Utc>) -> bool {
        if self.message_retention_days == 0 {
            return false; // 永久保留
        }

        let retention_duration = chrono::Duration::days(self.message_retention_days as i64);
        let cutoff_time = Utc::now() - retention_duration;
        message_created_at < cutoff_time
    }

    /// 获取消息保留截止时间
    ///
    /// # 返回
    /// - `Option<DateTime<Utc>>`: 消息保留截止时间，None表示永久保留
    pub fn get_message_retention_cutoff(&self) -> Option<DateTime<Utc>> {
        if self.message_retention_days == 0 {
            return None; // 永久保留
        }

        let retention_duration = chrono::Duration::days(self.message_retention_days as i64);
        Some(Utc::now() - retention_duration)
    }
}

// 注意：数据库模型转换逻辑已移至基础设施层
// 这里保留领域实体的纯业务逻辑，不直接依赖数据库模型

#[cfg(test)]
mod tests {
    use super::*;

    /// 测试创建有效的全局聊天室
    #[test]
    fn test_create_valid_global_chat_room() {
        let name = "全局聊天室".to_string();
        let description = Some("这是一个全局聊天室".to_string());
        let max_messages = Some(1000);
        let message_retention_days = Some(30);
        let settings = Some(r#"{"theme": "dark"}"#.to_string());

        let result = GlobalChatRoom::new(
            name.clone(),
            description.clone(),
            max_messages,
            message_retention_days,
            settings.clone(),
        );

        assert!(result.is_ok());
        let room = result.unwrap();
        assert_eq!(room.name, name);
        assert_eq!(room.description, description);
        assert_eq!(room.max_messages, 1000);
        assert_eq!(room.current_messages, 0);
        assert_eq!(room.message_retention_days, 30);
        assert_eq!(room.settings, settings);
        assert!(room.is_enabled);
        assert!(room.is_active());
    }

    /// 测试创建全局聊天室时名称验证
    #[test]
    fn test_create_global_chat_room_with_invalid_name() {
        // 测试空名称
        let result = GlobalChatRoom::new("".to_string(), None, None, None, None);
        assert!(result.is_err());

        // 测试名称过长
        let long_name = "a".repeat(101);
        let result = GlobalChatRoom::new(long_name, None, None, None, None);
        assert!(result.is_err());
    }

    /// 测试创建全局聊天室时描述验证
    #[test]
    fn test_create_global_chat_room_with_invalid_description() {
        let long_description = "a".repeat(1001);
        let result = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            Some(long_description),
            None,
            None,
            None,
        );
        assert!(result.is_err());
    }

    /// 测试创建全局聊天室时参数验证
    #[test]
    fn test_create_global_chat_room_with_invalid_parameters() {
        // 测试负数最大消息数量
        let result = GlobalChatRoom::new("测试聊天室".to_string(), None, Some(-1), None, None);
        assert!(result.is_err());

        // 测试超出范围的消息保留天数
        let result = GlobalChatRoom::new("测试聊天室".to_string(), None, None, Some(366), None);
        assert!(result.is_err());
    }

    /// 测试更新全局聊天室名称
    #[test]
    fn test_update_global_chat_room_name() {
        let mut room = GlobalChatRoom::new("原始名称".to_string(), None, None, None, None).unwrap();

        let new_name = "新名称".to_string();
        let result = room.update_name(new_name.clone());
        assert!(result.is_ok());
        assert_eq!(room.name, new_name);

        // 测试无效名称
        let result = room.update_name("".to_string());
        assert!(result.is_err());
    }

    /// 测试更新全局聊天室描述
    #[test]
    fn test_update_global_chat_room_description() {
        let mut room =
            GlobalChatRoom::new("测试聊天室".to_string(), None, None, None, None).unwrap();

        let new_description = Some("新描述".to_string());
        let result = room.update_description(new_description.clone());
        assert!(result.is_ok());
        assert_eq!(room.description, new_description);

        // 测试无效描述
        let long_description = Some("a".repeat(1001));
        let result = room.update_description(long_description);
        assert!(result.is_err());
    }

    /// 测试启用和禁用全局聊天室
    #[test]
    fn test_enable_disable_global_chat_room() {
        let mut room =
            GlobalChatRoom::new("测试聊天室".to_string(), None, None, None, None).unwrap();

        assert!(room.is_active());

        room.disable();
        assert!(!room.is_active());

        room.enable();
        assert!(room.is_active());
    }

    /// 测试添加和移除消息
    #[test]
    fn test_add_remove_message() {
        let mut room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            Some(2), // 最大2条消息
            None,
            None,
        )
        .unwrap();

        // 添加第一条消息
        let result = room.add_message();
        assert!(result.is_ok());
        assert_eq!(room.current_messages, 1);

        // 添加第二条消息
        let result = room.add_message();
        assert!(result.is_ok());
        assert_eq!(room.current_messages, 2);

        // 尝试添加第三条消息（应该失败）
        let result = room.add_message();
        assert!(result.is_err());
        assert_eq!(room.current_messages, 2);

        // 移除一条消息
        let result = room.remove_message();
        assert!(result.is_ok());
        assert_eq!(room.current_messages, 1);

        // 移除第二条消息
        let result = room.remove_message();
        assert!(result.is_ok());
        assert_eq!(room.current_messages, 0);

        // 尝试移除不存在的消息（应该失败）
        let result = room.remove_message();
        assert!(result.is_err());
        assert_eq!(room.current_messages, 0);
    }
}
