//! # 聊天室实体
//!
//! 聊天室领域实体，封装聊天室相关的业务逻辑和不变性约束

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::{Validate, ValidationErrors};

/// 聊天室类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ChatRoomType {
    /// 公共聊天室 - 所有用户都可以加入
    Public,
    /// 私人聊天室 - 需要邀请才能加入
    Private,
    /// 群组聊天室 - 多人聊天群组
    Group,
}

/// 聊天室状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ChatRoomStatus {
    /// 活跃状态 - 正常使用
    Active,
    /// 已归档 - 不再活跃但保留历史
    Archived,
    /// 已禁用 - 管理员禁用
    Disabled,
}

/// 聊天室领域实体
///
/// 设计原则：
/// - 封装聊天室的核心业务逻辑
/// - 维护聊天室数据的不变性约束
/// - 与数据库模型解耦，专注于业务规则
/// - 提供类型安全的聊天室操作接口
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Validate)]
pub struct ChatRoom {
    /// 聊天室唯一标识符
    pub id: Uuid,

    /// 聊天室名称，必须唯一
    #[validate(length(min = 1, max = 100, message = "聊天室名称长度必须在1-100个字符之间"))]
    pub name: String,

    /// 聊天室描述（可选）
    #[validate(length(max = 1000, message = "聊天室描述不能超过1000个字符"))]
    pub description: Option<String>,

    /// 聊天室类型
    pub room_type: ChatRoomType,

    /// 聊天室状态
    pub status: ChatRoomStatus,

    /// 创建者用户ID
    pub created_by: Uuid,

    /// 最大成员数量（0表示无限制）
    #[validate(range(min = 0, max = 10000, message = "最大成员数量必须在0-10000之间"))]
    pub max_members: i32,

    /// 当前成员数量
    #[validate(range(min = 0, message = "当前成员数量不能为负数"))]
    pub current_members: i32,

    /// 聊天室设置（JSON格式存储扩展配置）
    pub settings: Option<String>,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 创建聊天室的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateChatRoomRequest {
    #[validate(length(min = 1, max = 100, message = "聊天室名称长度必须在1-100个字符之间"))]
    pub name: String,

    #[validate(length(max = 1000, message = "聊天室描述不能超过1000个字符"))]
    pub description: Option<String>,

    pub room_type: ChatRoomType,

    #[validate(range(min = 0, max = 10000, message = "最大成员数量必须在0-10000之间"))]
    pub max_members: Option<i32>,

    pub settings: Option<String>,
}

/// 更新聊天室的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct UpdateChatRoomRequest {
    #[validate(length(min = 1, max = 100, message = "聊天室名称长度必须在1-100个字符之间"))]
    pub name: Option<String>,

    #[validate(length(max = 1000, message = "聊天室描述不能超过1000个字符"))]
    pub description: Option<String>,

    pub status: Option<ChatRoomStatus>,

    #[validate(range(min = 0, max = 10000, message = "最大成员数量必须在0-10000之间"))]
    pub max_members: Option<i32>,

    pub settings: Option<String>,
}

impl ChatRoom {
    /// 创建新聊天室
    ///
    /// # 参数
    /// - `name`: 聊天室名称
    /// - `description`: 聊天室描述（可选）
    /// - `room_type`: 聊天室类型
    /// - `created_by`: 创建者用户ID
    /// - `max_members`: 最大成员数量（可选，默认为0表示无限制）
    /// - `settings`: 聊天室设置（可选）
    ///
    /// # 返回
    /// - `Result<ChatRoom, ValidationErrors>`: 成功返回聊天室实体，失败返回验证错误
    pub fn new(
        name: String,
        description: Option<String>,
        room_type: ChatRoomType,
        created_by: Uuid,
        max_members: Option<i32>,
        settings: Option<String>,
    ) -> Result<Self, ValidationErrors> {
        let now = Utc::now();
        let chat_room = Self {
            id: Uuid::new_v4(),
            name,
            description,
            room_type,
            status: ChatRoomStatus::Active,
            created_by,
            max_members: max_members.unwrap_or(0),
            current_members: 0,
            settings,
            created_at: now,
            updated_at: now,
        };

        // 验证聊天室数据
        chat_room.validate()?;
        Ok(chat_room)
    }

    /// 更新聊天室名称
    ///
    /// # 参数
    /// - `new_name`: 新的聊天室名称
    ///
    /// # 返回
    /// - `Result<(), ValidationErrors>`: 成功返回空，失败返回验证错误
    pub fn update_name(&mut self, new_name: String) -> Result<(), ValidationErrors> {
        // 创建临时聊天室进行验证
        let temp_room = Self {
            name: new_name.clone(),
            ..self.clone()
        };

        // 验证新名称
        temp_room.validate()?;

        // 更新名称和时间戳
        self.name = new_name;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 更新聊天室描述
    ///
    /// # 参数
    /// - `new_description`: 新的聊天室描述
    ///
    /// # 返回
    /// - `Result<(), ValidationErrors>`: 成功返回空，失败返回验证错误
    pub fn update_description(
        &mut self,
        new_description: Option<String>,
    ) -> Result<(), ValidationErrors> {
        // 创建临时聊天室进行验证
        let temp_room = Self {
            description: new_description.clone(),
            ..self.clone()
        };

        // 验证新描述
        temp_room.validate()?;

        // 更新描述和时间戳
        self.description = new_description;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 更新聊天室状态
    ///
    /// # 参数
    /// - `new_status`: 新的聊天室状态
    pub fn update_status(&mut self, new_status: ChatRoomStatus) {
        self.status = new_status;
        self.updated_at = Utc::now();
    }

    /// 归档聊天室
    pub fn archive(&mut self) {
        self.status = ChatRoomStatus::Archived;
        self.updated_at = Utc::now();
    }

    /// 禁用聊天室
    pub fn disable(&mut self) {
        self.status = ChatRoomStatus::Disabled;
        self.updated_at = Utc::now();
    }

    /// 激活聊天室
    pub fn activate(&mut self) {
        self.status = ChatRoomStatus::Active;
        self.updated_at = Utc::now();
    }

    /// 增加成员数量
    ///
    /// # 返回
    /// - `Result<(), String>`: 成功返回空，失败返回错误信息
    pub fn add_member(&mut self) -> Result<(), String> {
        if self.max_members > 0 && self.current_members >= self.max_members {
            return Err("聊天室已达到最大成员数量限制".to_string());
        }

        self.current_members += 1;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 减少成员数量
    ///
    /// # 返回
    /// - `Result<(), String>`: 成功返回空，失败返回错误信息
    pub fn remove_member(&mut self) -> Result<(), String> {
        if self.current_members <= 0 {
            return Err("聊天室成员数量不能为负数".to_string());
        }

        self.current_members -= 1;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 检查是否可以加入更多成员
    pub fn can_add_member(&self) -> bool {
        self.max_members == 0 || self.current_members < self.max_members
    }

    /// 检查聊天室是否活跃
    pub fn is_active(&self) -> bool {
        self.status == ChatRoomStatus::Active
    }

    /// 检查用户是否为聊天室创建者
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `bool`: 用户是否为创建者
    pub fn is_creator(&self, user_id: &Uuid) -> bool {
        self.created_by == *user_id
    }

    /// 检查聊天室是否为公共聊天室
    pub fn is_public(&self) -> bool {
        self.room_type == ChatRoomType::Public
    }

    /// 检查聊天室是否为私人聊天室
    pub fn is_private(&self) -> bool {
        self.room_type == ChatRoomType::Private
    }

    /// 检查聊天室是否为群组聊天室
    pub fn is_group(&self) -> bool {
        self.room_type == ChatRoomType::Group
    }
}

// 注意：数据库模型转换逻辑已移至基础设施层
// 这里保留领域实体的纯业务逻辑，不直接依赖数据库模型
