//! # 聊天实体测试
//!
//! 测试GlobalChatRoom实体的业务逻辑和验证规则

#[cfg(test)]
mod tests {
    use super::super::chat::*;
    use chrono::{Duration, Utc};

    /// 测试创建有效的全局聊天室
    #[test]
    fn test_create_valid_global_chat_room() {
        let name = "全局聊天室".to_string();
        let description = Some("这是一个全局聊天室".to_string());
        let max_messages = Some(1000);
        let message_retention_days = Some(30);
        let settings = Some(r#"{"theme": "dark"}"#.to_string());

        let result = GlobalChatRoom::new(
            name.clone(),
            description.clone(),
            max_messages,
            message_retention_days,
            settings.clone(),
        );

        assert!(result.is_ok());
        let room = result.unwrap();
        assert_eq!(room.name, name);
        assert_eq!(room.description, description);
        assert_eq!(room.max_messages, 1000);
        assert_eq!(room.current_messages, 0);
        assert_eq!(room.message_retention_days, 30);
        assert_eq!(room.settings, settings);
        assert!(room.is_enabled);
        assert!(room.is_active());
    }

    /// 测试创建全局聊天室时名称验证
    #[test]
    fn test_create_global_chat_room_with_invalid_name() {
        // 测试空名称
        let result = GlobalChatRoom::new(
            "".to_string(),
            None,
            None,
            None,
            None,
        );
        assert!(result.is_err());

        // 测试名称过长
        let long_name = "a".repeat(101);
        let result = GlobalChatRoom::new(
            long_name,
            None,
            None,
            None,
            None,
        );
        assert!(result.is_err());
    }

    /// 测试创建全局聊天室时描述验证
    #[test]
    fn test_create_global_chat_room_with_invalid_description() {
        let long_description = "a".repeat(1001);
        let result = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            Some(long_description),
            None,
            None,
            None,
        );
        assert!(result.is_err());
    }

    /// 测试创建全局聊天室时参数验证
    #[test]
    fn test_create_global_chat_room_with_invalid_parameters() {
        // 测试负数最大消息数量
        let result = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            Some(-1),
            None,
            None,
        );
        assert!(result.is_err());

        // 测试超出范围的消息保留天数
        let result = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            None,
            Some(366),
            None,
        );
        assert!(result.is_err());
    }

    /// 测试更新全局聊天室名称
    #[test]
    fn test_update_global_chat_room_name() {
        let mut room = GlobalChatRoom::new(
            "原始名称".to_string(),
            None,
            None,
            None,
            None,
        ).unwrap();

        let new_name = "新名称".to_string();
        let result = room.update_name(new_name.clone());
        assert!(result.is_ok());
        assert_eq!(room.name, new_name);

        // 测试无效名称
        let result = room.update_name("".to_string());
        assert!(result.is_err());
    }

    /// 测试更新全局聊天室描述
    #[test]
    fn test_update_global_chat_room_description() {
        let mut room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            None,
            None,
            None,
        ).unwrap();

        let new_description = Some("新描述".to_string());
        let result = room.update_description(new_description.clone());
        assert!(result.is_ok());
        assert_eq!(room.description, new_description);

        // 测试无效描述
        let long_description = Some("a".repeat(1001));
        let result = room.update_description(long_description);
        assert!(result.is_err());
    }

    /// 测试启用和禁用全局聊天室
    #[test]
    fn test_enable_disable_global_chat_room() {
        let mut room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            None,
            None,
            None,
        ).unwrap();

        assert!(room.is_active());

        room.disable();
        assert!(!room.is_active());

        room.enable();
        assert!(room.is_active());
    }

    /// 测试添加和移除消息
    #[test]
    fn test_add_remove_message() {
        let mut room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            Some(2), // 最大2条消息
            None,
            None,
        ).unwrap();

        // 添加第一条消息
        let result = room.add_message();
        assert!(result.is_ok());
        assert_eq!(room.current_messages, 1);

        // 添加第二条消息
        let result = room.add_message();
        assert!(result.is_ok());
        assert_eq!(room.current_messages, 2);

        // 尝试添加第三条消息（应该失败）
        let result = room.add_message();
        assert!(result.is_err());
        assert_eq!(room.current_messages, 2);

        // 移除一条消息
        let result = room.remove_message();
        assert!(result.is_ok());
        assert_eq!(room.current_messages, 1);

        // 移除第二条消息
        let result = room.remove_message();
        assert!(result.is_ok());
        assert_eq!(room.current_messages, 0);

        // 尝试移除不存在的消息（应该失败）
        let result = room.remove_message();
        assert!(result.is_err());
        assert_eq!(room.current_messages, 0);
    }

    /// 测试检查是否可以添加消息
    #[test]
    fn test_can_add_message() {
        // 无限制的聊天室
        let mut room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            Some(0), // 0表示无限制
            None,
            None,
        ).unwrap();
        assert!(room.can_add_message());

        // 有限制的聊天室
        let mut room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            Some(1), // 最大1条消息
            None,
            None,
        ).unwrap();
        assert!(room.can_add_message());

        room.add_message().unwrap();
        assert!(!room.can_add_message());
    }

    /// 测试消息清理逻辑
    #[test]
    fn test_should_cleanup_message() {
        // 永久保留的聊天室
        let room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            None,
            Some(0), // 0表示永久保留
            None,
        ).unwrap();

        let old_message_time = Utc::now() - Duration::days(100);
        assert!(!room.should_cleanup_message(old_message_time));

        // 有保留期限的聊天室
        let room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            None,
            Some(30), // 保留30天
            None,
        ).unwrap();

        let recent_message_time = Utc::now() - Duration::days(10);
        assert!(!room.should_cleanup_message(recent_message_time));

        let old_message_time = Utc::now() - Duration::days(40);
        assert!(room.should_cleanup_message(old_message_time));
    }

    /// 测试获取消息保留截止时间
    #[test]
    fn test_get_message_retention_cutoff() {
        // 永久保留的聊天室
        let room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            None,
            Some(0), // 0表示永久保留
            None,
        ).unwrap();
        assert!(room.get_message_retention_cutoff().is_none());

        // 有保留期限的聊天室
        let room = GlobalChatRoom::new(
            "测试聊天室".to_string(),
            None,
            None,
            Some(30), // 保留30天
            None,
        ).unwrap();
        let cutoff = room.get_message_retention_cutoff();
        assert!(cutoff.is_some());

        let expected_cutoff = Utc::now() - Duration::days(30);
        let actual_cutoff = cutoff.unwrap();
        // 允许1分钟的时间差
        assert!((actual_cutoff - expected_cutoff).num_minutes().abs() < 1);
    }
}
