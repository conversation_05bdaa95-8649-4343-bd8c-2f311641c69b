//! # 消息实体
//!
//! 消息领域实体，封装消息相关的业务逻辑和不变性约束

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::{Validate, ValidationErrors};

/// 消息创建参数
#[derive(Debug, Clone)]
pub struct MessageCreateParams {
    pub content: String,
    pub message_type: MessageType,
    pub sender_id: Uuid,
    pub chat_room_id: Uuid,
    pub reply_to_id: Option<Uuid>,
    pub metadata: Option<String>,
    pub priority: Option<i32>,
    pub expires_at: Option<DateTime<Utc>>,
}

/// 消息类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MessageType {
    /// 普通文本消息
    Text,
    /// 图片消息
    Image,
    /// 文件消息
    File,
    /// 系统消息（如用户加入/离开）
    System,
    /// 语音消息
    Voice,
    /// 视频消息
    Video,
}

/// 消息状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MessageStatus {
    /// 已发送
    Sent,
    /// 已送达
    Delivered,
    /// 已读
    Read,
    /// 已删除
    Deleted,
    /// 已编辑
    Edited,
}

/// 消息领域实体
///
/// 设计原则：
/// - 封装消息的核心业务逻辑
/// - 维护消息数据的不变性约束
/// - 与数据库模型解耦，专注于业务规则
/// - 提供类型安全的消息操作接口
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Validate)]
pub struct Message {
    /// 消息唯一标识符
    pub id: Uuid,

    /// 消息内容
    #[validate(length(min = 1, max = 10000, message = "消息内容长度必须在1-10000个字符之间"))]
    pub content: String,

    /// 消息类型
    pub message_type: MessageType,

    /// 消息状态
    pub status: MessageStatus,

    /// 发送者用户ID
    pub sender_id: Uuid,

    /// 目标聊天室ID
    pub chat_room_id: Uuid,

    /// 回复的消息ID（可选，用于消息回复功能）
    pub reply_to_id: Option<Uuid>,

    /// 消息元数据（JSON格式，存储文件信息、位置信息等）
    pub metadata: Option<String>,

    /// 消息优先级（0-9，9为最高优先级）
    #[validate(range(min = 0, max = 9, message = "消息优先级必须在0-9之间"))]
    pub priority: i32,

    /// 是否置顶消息
    pub is_pinned: bool,

    /// 消息过期时间（可选，用于临时消息）
    pub expires_at: Option<DateTime<Utc>>,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 创建消息的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateMessageRequest {
    #[validate(length(min = 1, max = 10000, message = "消息内容长度必须在1-10000个字符之间"))]
    pub content: String,

    pub message_type: MessageType,

    pub chat_room_id: Uuid,

    pub reply_to_id: Option<Uuid>,

    pub metadata: Option<String>,

    #[validate(range(min = 0, max = 9, message = "消息优先级必须在0-9之间"))]
    pub priority: Option<i32>,

    pub expires_at: Option<DateTime<Utc>>,
}

/// 更新消息的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct UpdateMessageRequest {
    #[validate(length(min = 1, max = 10000, message = "消息内容长度必须在1-10000个字符之间"))]
    pub content: Option<String>,

    pub status: Option<MessageStatus>,

    pub metadata: Option<String>,

    #[validate(range(min = 0, max = 9, message = "消息优先级必须在0-9之间"))]
    pub priority: Option<i32>,

    pub is_pinned: Option<bool>,
}

impl Message {
    /// 创建新消息
    ///
    /// # 参数
    /// - `params`: 消息创建参数
    ///
    /// # 返回
    /// - `Result<Message, ValidationErrors>`: 成功返回消息实体，失败返回验证错误
    pub fn new(params: MessageCreateParams) -> Result<Self, ValidationErrors> {
        let now = Utc::now();
        let message = Self {
            id: Uuid::new_v4(),
            content: params.content,
            message_type: params.message_type,
            status: MessageStatus::Sent,
            sender_id: params.sender_id,
            chat_room_id: params.chat_room_id,
            reply_to_id: params.reply_to_id,
            metadata: params.metadata,
            priority: params.priority.unwrap_or(5),
            is_pinned: false,
            expires_at: params.expires_at,
            created_at: now,
            updated_at: now,
        };

        // 验证消息数据
        message.validate()?;
        Ok(message)
    }

    /// 更新消息内容
    ///
    /// # 参数
    /// - `new_content`: 新的消息内容
    ///
    /// # 返回
    /// - `Result<(), ValidationErrors>`: 成功返回空，失败返回验证错误
    pub fn update_content(&mut self, new_content: String) -> Result<(), ValidationErrors> {
        // 创建临时消息进行验证
        let temp_message = Self {
            content: new_content.clone(),
            ..self.clone()
        };

        // 验证新内容
        temp_message.validate()?;

        // 更新内容、状态和时间戳
        self.content = new_content;
        self.status = MessageStatus::Edited;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 更新消息状态
    ///
    /// # 参数
    /// - `new_status`: 新的消息状态
    pub fn update_status(&mut self, new_status: MessageStatus) {
        self.status = new_status;
        self.updated_at = Utc::now();
    }

    /// 标记消息为已送达
    pub fn mark_delivered(&mut self) {
        if self.status == MessageStatus::Sent {
            self.status = MessageStatus::Delivered;
            self.updated_at = Utc::now();
        }
    }

    /// 标记消息为已读
    pub fn mark_read(&mut self) {
        if matches!(self.status, MessageStatus::Sent | MessageStatus::Delivered) {
            self.status = MessageStatus::Read;
            self.updated_at = Utc::now();
        }
    }

    /// 删除消息
    pub fn delete(&mut self) {
        self.status = MessageStatus::Deleted;
        self.updated_at = Utc::now();
    }

    /// 置顶消息
    pub fn pin(&mut self) {
        self.is_pinned = true;
        self.updated_at = Utc::now();
    }

    /// 取消置顶消息
    pub fn unpin(&mut self) {
        self.is_pinned = false;
        self.updated_at = Utc::now();
    }

    /// 更新消息优先级
    ///
    /// # 参数
    /// - `new_priority`: 新的优先级（0-9）
    ///
    /// # 返回
    /// - `Result<(), String>`: 成功返回空，失败返回错误信息
    pub fn update_priority(&mut self, new_priority: i32) -> Result<(), String> {
        if !(0..=9).contains(&new_priority) {
            return Err("消息优先级必须在0-9之间".to_string());
        }

        self.priority = new_priority;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 检查消息是否已过期
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            Utc::now() > expires_at
        } else {
            false
        }
    }

    /// 检查消息是否为回复消息
    pub fn is_reply(&self) -> bool {
        self.reply_to_id.is_some()
    }

    /// 检查消息是否为系统消息
    pub fn is_system_message(&self) -> bool {
        self.message_type == MessageType::System
    }

    /// 检查消息是否为文本消息
    pub fn is_text_message(&self) -> bool {
        self.message_type == MessageType::Text
    }

    /// 检查消息是否为媒体消息（图片、语音、视频）
    pub fn is_media_message(&self) -> bool {
        matches!(
            self.message_type,
            MessageType::Image | MessageType::Voice | MessageType::Video
        )
    }

    /// 检查消息是否属于指定发送者
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `bool`: 消息是否属于该用户
    pub fn belongs_to_sender(&self, user_id: &Uuid) -> bool {
        self.sender_id == *user_id
    }

    /// 检查消息是否属于指定聊天室
    ///
    /// # 参数
    /// - `chat_room_id`: 聊天室ID
    ///
    /// # 返回
    /// - `bool`: 消息是否属于该聊天室
    pub fn belongs_to_chat_room(&self, chat_room_id: &Uuid) -> bool {
        self.chat_room_id == *chat_room_id
    }

    /// 检查消息是否已被删除
    pub fn is_deleted(&self) -> bool {
        self.status == MessageStatus::Deleted
    }

    /// 检查消息是否已被编辑
    pub fn is_edited(&self) -> bool {
        self.status == MessageStatus::Edited
    }

    /// 检查消息是否已被读取
    pub fn is_read(&self) -> bool {
        self.status == MessageStatus::Read
    }
}

// 注意：数据库模型转换逻辑已移至基础设施层
// 这里保留领域实体的纯业务逻辑，不直接依赖数据库模型
