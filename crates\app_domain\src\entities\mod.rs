//! # 领域实体模块
//!
//! 包含所有核心业务实体的定义，遵循领域驱动设计原则：
//! - 实体封装业务逻辑和不变性约束
//! - 实体具有唯一标识符
//! - 实体负责维护自身的业务规则
//! - 实体与数据库模型解耦，通过转换层进行映射

// 核心实体模块
pub mod chat;
pub mod chat_room;
pub mod message;
pub mod search_task;
pub mod task;
pub mod user;
pub mod user_session;

// 认证相关实体
pub mod auth;

// 重新导出所有实体
pub use auth::*;
pub use chat::*;
pub use chat_room::*;
pub use message::*;
pub use search_task::*;
pub use task::*;
pub use user::*;
pub use user_session::*;

// 重新导出常用类型
pub use chrono::{DateTime, Utc};
pub use serde::{Deserialize, Serialize};
pub use uuid::Uuid;
pub use validator::Validate;
