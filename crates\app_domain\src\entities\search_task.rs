//! # 搜索任务实体模块
//!
//! 定义异步搜索队列系统中的核心实体，包括：
//! - SearchTask: 搜索任务实体
//! - SearchTaskPriority: 任务优先级枚举
//! - SearchTaskStatus: 任务状态枚举
//! - SearchTaskResult: 搜索结果实体

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use validator::Validate;

/// 搜索任务优先级枚举
///
/// 定义搜索任务的优先级级别，用于队列调度
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum SearchTaskPriority {
    /// 低优先级 - 批量搜索、后台任务
    Low = 1,
    /// 普通优先级 - 常规用户搜索
    Normal = 2,
    /// 高优先级 - VIP用户搜索、实时搜索
    High = 3,
    /// 紧急优先级 - 系统关键搜索、管理员搜索
    Critical = 4,
}

impl Default for SearchTaskPriority {
    fn default() -> Self {
        Self::Normal
    }
}

/// 搜索任务状态枚举
///
/// 跟踪搜索任务在队列中的生命周期状态
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum SearchTaskStatus {
    /// 待处理 - 任务已创建，等待调度
    Pending,
    /// 处理中 - 任务正在执行
    Processing,
    /// 已完成 - 任务执行成功
    Completed,
    /// 失败 - 任务执行失败
    Failed,
    /// 已取消 - 任务被用户或系统取消
    Cancelled,
    /// 超时 - 任务执行超时
    Timeout,
    /// 重试中 - 任务正在重试
    Retrying,
}

impl Default for SearchTaskStatus {
    fn default() -> Self {
        Self::Pending
    }
}

/// 搜索任务类型枚举
///
/// 定义不同类型的搜索任务
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SearchTaskType {
    /// 全文搜索 - 基于内容的全文检索
    FullTextSearch,
    /// 用户搜索 - 搜索特定用户的消息
    UserSearch,
    /// 时间范围搜索 - 在指定时间范围内搜索
    TimeRangeSearch,
    /// 复合搜索 - 多条件组合搜索
    CompositeSearch,
    /// 预计算搜索 - 热门搜索词预计算
    PrecomputedSearch,
    /// 预计算任务调度 - 定期预计算热门搜索词
    PrecomputeScheduling,
    /// 预计算缓存更新 - 更新预计算结果缓存
    PrecomputeCacheUpdate,
}

/// 预计算任务类型枚举
///
/// 定义预计算系统中的不同任务类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PrecomputeTaskType {
    /// 热门搜索词分析 - 分析用户搜索行为，识别热门搜索词
    HotQueryAnalysis,
    /// 搜索结果预生成 - 为热门搜索词预生成搜索结果
    ResultPregeneration,
    /// 缓存预热 - 将预计算结果加载到缓存中
    CacheWarmup,
    /// 统计数据更新 - 更新搜索统计和分析数据
    StatisticsUpdate,
    /// 过期数据清理 - 清理过期的预计算结果
    ExpiredDataCleanup,
}

/// 预计算调度策略枚举
///
/// 定义预计算任务的调度策略
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PrecomputeScheduleStrategy {
    /// 固定间隔调度 - 按固定时间间隔执行
    FixedInterval,
    /// Cron表达式调度 - 基于Cron表达式的复杂调度
    CronExpression,
    /// 事件驱动调度 - 基于特定事件触发
    EventDriven,
    /// 负载感知调度 - 根据系统负载动态调整
    LoadAware,
    /// 混合调度 - 结合多种调度策略
    Hybrid,
}

/// 搜索任务实体
///
/// 表示异步搜索队列中的单个搜索任务
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct SearchTask {
    /// 任务唯一标识符
    pub id: Uuid,

    /// 发起搜索的用户ID
    pub user_id: Uuid,

    /// 搜索查询字符串
    #[validate(length(min = 1, max = 1000, message = "搜索查询长度必须在1-1000字符之间"))]
    pub query: String,

    /// 任务类型
    pub task_type: SearchTaskType,

    /// 任务优先级
    pub priority: SearchTaskPriority,

    /// 任务状态
    pub status: SearchTaskStatus,

    /// 搜索参数（JSON格式存储额外参数）
    pub search_params: HashMap<String, serde_json::Value>,

    /// 任务创建时间
    pub created_at: DateTime<Utc>,

    /// 任务开始处理时间（可选）
    pub started_at: Option<DateTime<Utc>>,

    /// 任务完成时间（可选）
    pub completed_at: Option<DateTime<Utc>>,

    /// 任务超时时间（秒）
    pub timeout_seconds: u32,

    /// 重试次数
    pub retry_count: u32,

    /// 最大重试次数
    pub max_retries: u32,

    /// 错误信息（任务失败时记录）
    pub error_message: Option<String>,

    /// 结果通知回调URL（可选）
    pub callback_url: Option<String>,

    /// 任务元数据（用于扩展信息）
    pub metadata: HashMap<String, String>,
}

impl SearchTask {
    /// 创建新的搜索任务
    ///
    /// # 参数
    /// - `user_id`: 发起搜索的用户ID
    /// - `query`: 搜索查询字符串
    /// - `task_type`: 任务类型
    /// - `priority`: 任务优先级
    ///
    /// # 返回
    /// - 新创建的搜索任务实例
    pub fn new(
        user_id: Uuid,
        query: String,
        task_type: SearchTaskType,
        priority: SearchTaskPriority,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            user_id,
            query,
            task_type,
            priority,
            status: SearchTaskStatus::Pending,
            search_params: HashMap::new(),
            created_at: Utc::now(),
            started_at: None,
            completed_at: None,
            timeout_seconds: 30, // 默认30秒超时
            retry_count: 0,
            max_retries: 3, // 默认最大重试3次
            error_message: None,
            callback_url: None,
            metadata: HashMap::new(),
        }
    }

    /// 设置搜索参数
    ///
    /// # 参数
    /// - `key`: 参数键
    /// - `value`: 参数值
    pub fn with_param<T: Serialize>(mut self, key: &str, value: T) -> Self {
        if let Ok(json_value) = serde_json::to_value(value) {
            self.search_params.insert(key.to_string(), json_value);
        }
        self
    }

    /// 设置超时时间
    ///
    /// # 参数
    /// - `timeout_seconds`: 超时时间（秒）
    pub fn with_timeout(mut self, timeout_seconds: u32) -> Self {
        self.timeout_seconds = timeout_seconds;
        self
    }

    /// 设置最大重试次数
    ///
    /// # 参数
    /// - `max_retries`: 最大重试次数
    pub fn with_max_retries(mut self, max_retries: u32) -> Self {
        self.max_retries = max_retries;
        self
    }

    /// 设置回调URL
    ///
    /// # 参数
    /// - `callback_url`: 结果通知回调URL
    pub fn with_callback(mut self, callback_url: String) -> Self {
        self.callback_url = Some(callback_url);
        self
    }

    /// 添加元数据
    ///
    /// # 参数
    /// - `key`: 元数据键
    /// - `value`: 元数据值
    pub fn with_metadata(mut self, key: &str, value: &str) -> Self {
        self.metadata.insert(key.to_string(), value.to_string());
        self
    }

    /// 开始处理任务
    ///
    /// 将任务状态更新为处理中，并记录开始时间
    pub fn start_processing(&mut self) {
        self.status = SearchTaskStatus::Processing;
        self.started_at = Some(Utc::now());
    }

    /// 完成任务
    ///
    /// 将任务状态更新为已完成，并记录完成时间
    pub fn complete(&mut self) {
        self.status = SearchTaskStatus::Completed;
        self.completed_at = Some(Utc::now());
    }

    /// 任务失败
    ///
    /// # 参数
    /// - `error_message`: 错误信息
    pub fn fail(&mut self, error_message: String) {
        self.status = SearchTaskStatus::Failed;
        self.completed_at = Some(Utc::now());
        self.error_message = Some(error_message);
    }

    /// 取消任务
    pub fn cancel(&mut self) {
        self.status = SearchTaskStatus::Cancelled;
        self.completed_at = Some(Utc::now());
    }

    /// 任务超时
    pub fn timeout(&mut self) {
        self.status = SearchTaskStatus::Timeout;
        self.completed_at = Some(Utc::now());
        self.error_message = Some("任务执行超时".to_string());
    }

    /// 开始重试
    pub fn start_retry(&mut self) {
        self.retry_count += 1;
        self.status = SearchTaskStatus::Retrying;
        self.started_at = Some(Utc::now());
        self.error_message = None;
    }

    /// 检查是否可以重试
    ///
    /// # 返回
    /// - true: 可以重试
    /// - false: 不能重试（已达到最大重试次数）
    pub fn can_retry(&self) -> bool {
        self.retry_count < self.max_retries
    }

    /// 检查任务是否已完成（成功、失败、取消或超时）
    ///
    /// # 返回
    /// - true: 任务已完成
    /// - false: 任务未完成
    pub fn is_finished(&self) -> bool {
        matches!(
            self.status,
            SearchTaskStatus::Completed
                | SearchTaskStatus::Failed
                | SearchTaskStatus::Cancelled
                | SearchTaskStatus::Timeout
        )
    }

    /// 检查任务是否正在处理中
    ///
    /// # 返回
    /// - true: 任务正在处理
    /// - false: 任务未在处理
    pub fn is_processing(&self) -> bool {
        matches!(
            self.status,
            SearchTaskStatus::Processing | SearchTaskStatus::Retrying
        )
    }

    /// 获取任务执行时长（如果任务已开始）
    ///
    /// # 返回
    /// - Some(Duration): 任务执行时长
    /// - None: 任务未开始
    pub fn execution_duration(&self) -> Option<chrono::Duration> {
        if let Some(started_at) = self.started_at {
            let end_time = self.completed_at.unwrap_or_else(Utc::now);
            Some(end_time - started_at)
        } else {
            None
        }
    }
}

/// 搜索任务结果实体
///
/// 表示搜索任务的执行结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchTaskResult {
    /// 关联的任务ID
    pub task_id: Uuid,

    /// 搜索结果数据（JSON格式）
    pub result_data: serde_json::Value,

    /// 结果总数
    pub total_count: usize,

    /// 搜索耗时（毫秒）
    pub execution_time_ms: u64,

    /// 缓存命中标识
    pub cache_hit: bool,

    /// 结果创建时间
    pub created_at: DateTime<Utc>,

    /// 结果元数据
    pub metadata: HashMap<String, String>,
}

impl SearchTaskResult {
    /// 创建新的搜索结果
    ///
    /// # 参数
    /// - `task_id`: 关联的任务ID
    /// - `result_data`: 搜索结果数据
    /// - `total_count`: 结果总数
    /// - `execution_time_ms`: 执行时间（毫秒）
    /// - `cache_hit`: 是否缓存命中
    ///
    /// # 返回
    /// - 新创建的搜索结果实例
    pub fn new(
        task_id: Uuid,
        result_data: serde_json::Value,
        total_count: usize,
        execution_time_ms: u64,
        cache_hit: bool,
    ) -> Self {
        Self {
            task_id,
            result_data,
            total_count,
            execution_time_ms,
            cache_hit,
            created_at: Utc::now(),
            metadata: HashMap::new(),
        }
    }

    /// 添加元数据
    ///
    /// # 参数
    /// - `key`: 元数据键
    /// - `value`: 元数据值
    pub fn with_metadata(mut self, key: &str, value: &str) -> Self {
        self.metadata.insert(key.to_string(), value.to_string());
        self
    }
}

/// 预计算任务实体
///
/// 表示搜索结果预计算系统中的预计算任务
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct PrecomputeTask {
    /// 任务唯一标识符
    pub id: Uuid,

    /// 预计算任务类型
    pub task_type: PrecomputeTaskType,

    /// 调度策略
    pub schedule_strategy: PrecomputeScheduleStrategy,

    /// 任务状态
    pub status: SearchTaskStatus,

    /// 目标搜索查询（用于特定查询的预计算）
    pub target_query: Option<String>,

    /// 预计算参数
    pub compute_params: HashMap<String, serde_json::Value>,

    /// 任务创建时间
    pub created_at: DateTime<Utc>,

    /// 计划执行时间
    pub scheduled_at: DateTime<Utc>,

    /// 实际开始时间
    pub started_at: Option<DateTime<Utc>>,

    /// 完成时间
    pub completed_at: Option<DateTime<Utc>>,

    /// 下次执行时间（用于周期性任务）
    pub next_run_at: Option<DateTime<Utc>>,

    /// 执行间隔（秒，用于固定间隔调度）
    pub interval_seconds: Option<u32>,

    /// Cron表达式（用于Cron调度）
    pub cron_expression: Option<String>,

    /// 任务优先级
    pub priority: SearchTaskPriority,

    /// 重试次数
    pub retry_count: u32,

    /// 最大重试次数
    pub max_retries: u32,

    /// 错误信息
    pub error_message: Option<String>,

    /// 执行结果统计
    pub execution_stats: Option<PrecomputeExecutionStats>,

    /// 任务元数据
    pub metadata: HashMap<String, String>,
}

/// 预计算执行统计信息
///
/// 记录预计算任务的执行统计数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrecomputeExecutionStats {
    /// 处理的查询数量
    pub processed_queries: u32,

    /// 生成的预计算结果数量
    pub generated_results: u32,

    /// 更新的缓存条目数量
    pub updated_cache_entries: u32,

    /// 执行时长（毫秒）
    pub execution_time_ms: u64,

    /// 内存使用峰值（字节）
    pub peak_memory_usage: u64,

    /// 数据库查询次数
    pub database_queries: u32,

    /// 缓存命中次数
    pub cache_hits: u32,

    /// 缓存未命中次数
    pub cache_misses: u32,
}

impl PrecomputeTask {
    /// 创建新的预计算任务
    ///
    /// # 参数
    /// - `task_type`: 预计算任务类型
    /// - `schedule_strategy`: 调度策略
    /// - `scheduled_at`: 计划执行时间
    ///
    /// # 返回
    /// - 新创建的预计算任务实例
    pub fn new(
        task_type: PrecomputeTaskType,
        schedule_strategy: PrecomputeScheduleStrategy,
        scheduled_at: DateTime<Utc>,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            task_type,
            schedule_strategy,
            status: SearchTaskStatus::Pending,
            target_query: None,
            compute_params: HashMap::new(),
            created_at: Utc::now(),
            scheduled_at,
            started_at: None,
            completed_at: None,
            next_run_at: None,
            interval_seconds: None,
            cron_expression: None,
            priority: SearchTaskPriority::Normal,
            retry_count: 0,
            max_retries: 3,
            error_message: None,
            execution_stats: None,
            metadata: HashMap::new(),
        }
    }

    /// 设置目标查询
    ///
    /// # 参数
    /// - `query`: 目标搜索查询
    pub fn with_target_query(mut self, query: String) -> Self {
        self.target_query = Some(query);
        self
    }

    /// 设置固定间隔调度
    ///
    /// # 参数
    /// - `interval_seconds`: 执行间隔（秒）
    pub fn with_fixed_interval(mut self, interval_seconds: u32) -> Self {
        self.schedule_strategy = PrecomputeScheduleStrategy::FixedInterval;
        self.interval_seconds = Some(interval_seconds);
        self
    }

    /// 设置Cron表达式调度
    ///
    /// # 参数
    /// - `cron_expression`: Cron表达式
    pub fn with_cron_schedule(mut self, cron_expression: String) -> Self {
        self.schedule_strategy = PrecomputeScheduleStrategy::CronExpression;
        self.cron_expression = Some(cron_expression);
        self
    }

    /// 设置任务优先级
    ///
    /// # 参数
    /// - `priority`: 任务优先级
    pub fn with_priority(mut self, priority: SearchTaskPriority) -> Self {
        self.priority = priority;
        self
    }

    /// 添加计算参数
    ///
    /// # 参数
    /// - `key`: 参数键
    /// - `value`: 参数值
    pub fn with_param<T: Serialize>(mut self, key: &str, value: T) -> Self {
        if let Ok(json_value) = serde_json::to_value(value) {
            self.compute_params.insert(key.to_string(), json_value);
        }
        self
    }

    /// 开始执行任务
    pub fn start_execution(&mut self) {
        self.status = SearchTaskStatus::Processing;
        self.started_at = Some(Utc::now());
    }

    /// 完成任务执行
    ///
    /// # 参数
    /// - `stats`: 执行统计信息
    pub fn complete_execution(&mut self, stats: PrecomputeExecutionStats) {
        self.status = SearchTaskStatus::Completed;
        self.completed_at = Some(Utc::now());
        self.execution_stats = Some(stats);

        // 计算下次执行时间（如果是周期性任务）
        self.calculate_next_run_time();
    }

    /// 任务执行失败
    ///
    /// # 参数
    /// - `error_message`: 错误信息
    pub fn fail_execution(&mut self, error_message: String) {
        self.status = SearchTaskStatus::Failed;
        self.completed_at = Some(Utc::now());
        self.error_message = Some(error_message);
    }

    /// 计算下次执行时间
    fn calculate_next_run_time(&mut self) {
        match self.schedule_strategy {
            PrecomputeScheduleStrategy::FixedInterval => {
                if let Some(interval) = self.interval_seconds {
                    self.next_run_at =
                        Some(Utc::now() + chrono::Duration::seconds(interval as i64));
                }
            }
            PrecomputeScheduleStrategy::CronExpression => {
                // Cron表达式解析将在调度器中实现
                // 这里只是占位符
                self.next_run_at = Some(Utc::now() + chrono::Duration::hours(1));
            }
            _ => {
                // 其他策略不设置下次执行时间
            }
        }
    }

    /// 检查任务是否应该执行
    ///
    /// # 返回
    /// - true: 任务应该执行
    /// - false: 任务不应该执行
    pub fn should_execute(&self) -> bool {
        match self.status {
            SearchTaskStatus::Pending => Utc::now() >= self.scheduled_at,
            _ => false,
        }
    }

    /// 检查任务是否可以重试
    ///
    /// # 返回
    /// - true: 可以重试
    /// - false: 不能重试
    pub fn can_retry(&self) -> bool {
        self.retry_count < self.max_retries
            && matches!(
                self.status,
                SearchTaskStatus::Failed | SearchTaskStatus::Timeout
            )
    }

    /// 开始重试
    pub fn start_retry(&mut self) {
        self.retry_count += 1;
        self.status = SearchTaskStatus::Retrying;
        self.started_at = Some(Utc::now());
        self.error_message = None;
    }
}
