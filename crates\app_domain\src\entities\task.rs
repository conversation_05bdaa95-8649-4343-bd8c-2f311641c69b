//! # 任务实体
//!
//! 任务领域实体，封装任务相关的业务逻辑和不变性约束

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::{Validate, ValidationErrors};

/// 任务领域实体
///
/// 设计原则：
/// - 封装任务的核心业务逻辑
/// - 维护任务数据的不变性约束
/// - 与数据库模型解耦，专注于业务规则
/// - 提供类型安全的任务操作接口
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Validate)]
pub struct Task {
    /// 任务唯一标识符
    pub id: Uuid,

    /// 任务标题
    #[validate(length(min = 1, max = 200, message = "任务标题长度必须在1-200个字符之间"))]
    pub title: String,

    /// 任务描述（可选）
    #[validate(length(max = 2000, message = "任务描述不能超过2000个字符"))]
    pub description: Option<String>,

    /// 任务完成状态
    pub completed: bool,

    /// 任务所有者用户ID（可选，兼容现有数据）
    pub user_id: Option<Uuid>,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 创建任务的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateTaskRequest {
    #[validate(length(min = 1, max = 200, message = "任务标题长度必须在1-200个字符之间"))]
    pub title: String,

    #[validate(length(max = 2000, message = "任务描述不能超过2000个字符"))]
    pub description: Option<String>,

    /// 任务所有者用户ID
    pub user_id: Option<Uuid>,
}

/// 更新任务的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct UpdateTaskRequest {
    #[validate(length(min = 1, max = 200, message = "任务标题长度必须在1-200个字符之间"))]
    pub title: Option<String>,

    #[validate(length(max = 2000, message = "任务描述不能超过2000个字符"))]
    pub description: Option<String>,

    pub completed: Option<bool>,
}

impl Task {
    /// 创建新任务
    ///
    /// # 参数
    /// - `title`: 任务标题
    /// - `description`: 任务描述（可选）
    /// - `user_id`: 任务所有者用户ID（可选）
    ///
    /// # 返回
    /// - `Result<Task, ValidationErrors>`: 成功返回任务实体，失败返回验证错误
    pub fn new(
        title: String,
        description: Option<String>,
        user_id: Option<Uuid>,
    ) -> Result<Self, ValidationErrors> {
        let now = Utc::now();
        let task = Self {
            id: Uuid::new_v4(),
            title,
            description,
            completed: false,
            user_id,
            created_at: now,
            updated_at: now,
        };

        // 验证任务数据
        task.validate()?;
        Ok(task)
    }

    /// 更新任务标题
    ///
    /// # 参数
    /// - `new_title`: 新的任务标题
    ///
    /// # 返回
    /// - `Result<(), ValidationErrors>`: 成功返回空，失败返回验证错误
    pub fn update_title(&mut self, new_title: String) -> Result<(), ValidationErrors> {
        // 创建临时任务进行验证
        let temp_task = Self {
            title: new_title.clone(),
            ..self.clone()
        };

        // 验证新标题
        temp_task.validate()?;

        // 更新标题和时间戳
        self.title = new_title;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 更新任务描述
    ///
    /// # 参数
    /// - `new_description`: 新的任务描述
    ///
    /// # 返回
    /// - `Result<(), ValidationErrors>`: 成功返回空，失败返回验证错误
    pub fn update_description(
        &mut self,
        new_description: Option<String>,
    ) -> Result<(), ValidationErrors> {
        // 创建临时任务进行验证
        let temp_task = Self {
            description: new_description.clone(),
            ..self.clone()
        };

        // 验证新描述
        temp_task.validate()?;

        // 更新描述和时间戳
        self.description = new_description;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 标记任务为完成
    pub fn mark_completed(&mut self) {
        if !self.completed {
            self.completed = true;
            self.updated_at = Utc::now();
        }
    }

    /// 标记任务为未完成
    pub fn mark_incomplete(&mut self) {
        if self.completed {
            self.completed = false;
            self.updated_at = Utc::now();
        }
    }

    /// 切换任务完成状态
    pub fn toggle_completed(&mut self) {
        self.completed = !self.completed;
        self.updated_at = Utc::now();
    }

    /// 分配任务给用户
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    pub fn assign_to_user(&mut self, user_id: Uuid) {
        self.user_id = Some(user_id);
        self.updated_at = Utc::now();
    }

    /// 取消任务分配
    pub fn unassign(&mut self) {
        self.user_id = None;
        self.updated_at = Utc::now();
    }

    /// 检查任务是否属于指定用户
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `bool`: 任务是否属于该用户
    pub fn belongs_to_user(&self, user_id: &Uuid) -> bool {
        self.user_id.as_ref() == Some(user_id)
    }

    /// 检查任务是否已完成
    pub fn is_completed(&self) -> bool {
        self.completed
    }

    /// 检查任务是否有描述
    pub fn has_description(&self) -> bool {
        self.description.is_some() && !self.description.as_ref().unwrap().is_empty()
    }
}

// 注意：数据库模型转换逻辑已移至基础设施层
// 这里保留领域实体的纯业务逻辑，不直接依赖数据库模型

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_task_success() {
        let title = "测试任务".to_string();
        let description = Some("这是一个测试任务".to_string());
        let user_id = Some(Uuid::new_v4());

        let task = Task::new(title.clone(), description.clone(), user_id).unwrap();

        assert_eq!(task.title, title);
        assert_eq!(task.description, description);
        assert_eq!(task.user_id, user_id);
        assert!(!task.completed);
        assert!(!task.id.is_nil());
    }

    #[test]
    fn test_create_task_invalid_title() {
        let title = "".to_string(); // 空标题
        let description = None;
        let user_id = None;

        let result = Task::new(title, description, user_id);
        assert!(result.is_err());
    }

    #[test]
    fn test_update_title_success() {
        let mut task = Task::new("原标题".to_string(), None, None).unwrap();
        let new_title = "新标题".to_string();

        let result = task.update_title(new_title.clone());
        assert!(result.is_ok());
        assert_eq!(task.title, new_title);
    }

    #[test]
    fn test_mark_completed() {
        let mut task = Task::new("测试任务".to_string(), None, None).unwrap();
        assert!(!task.is_completed());

        task.mark_completed();
        assert!(task.is_completed());

        // 再次标记完成不应该改变状态
        let old_updated_at = task.updated_at;
        task.mark_completed();
        assert_eq!(task.updated_at, old_updated_at);
    }

    #[test]
    fn test_toggle_completed() {
        let mut task = Task::new("测试任务".to_string(), None, None).unwrap();
        assert!(!task.is_completed());

        task.toggle_completed();
        assert!(task.is_completed());

        task.toggle_completed();
        assert!(!task.is_completed());
    }

    #[test]
    fn test_assign_to_user() {
        let mut task = Task::new("测试任务".to_string(), None, None).unwrap();
        let user_id = Uuid::new_v4();

        task.assign_to_user(user_id);
        assert!(task.belongs_to_user(&user_id));

        task.unassign();
        assert!(!task.belongs_to_user(&user_id));
    }

    #[test]
    fn test_has_description() {
        let task_with_desc =
            Task::new("测试任务".to_string(), Some("有描述".to_string()), None).unwrap();
        assert!(task_with_desc.has_description());

        let task_without_desc = Task::new("测试任务".to_string(), None, None).unwrap();
        assert!(!task_without_desc.has_description());

        let task_empty_desc =
            Task::new("测试任务".to_string(), Some("".to_string()), None).unwrap();
        assert!(!task_empty_desc.has_description());
    }
}
