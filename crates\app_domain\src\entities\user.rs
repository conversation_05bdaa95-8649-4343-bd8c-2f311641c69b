//! # 用户实体
//!
//! 用户领域实体，封装用户相关的业务逻辑和不变性约束

use chrono::{DateTime, Utc};
use lazy_static::lazy_static;
use regex::Regex;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::{Validate, ValidationErrors};

lazy_static! {
    /// 用户名验证正则表达式：只允许字母、数字、下划线和连字符
    static ref USERNAME_REGEX: Regex = Regex::new(r"^[a-zA-Z0-9_-]+$").unwrap();
}

/// 用户领域实体
///
/// 设计原则：
/// - 封装用户的核心业务逻辑
/// - 维护用户数据的不变性约束
/// - 与数据库模型解耦，专注于业务规则
/// - 提供类型安全的用户操作接口
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Validate)]
pub struct User {
    /// 用户唯一标识符
    pub id: Uuid,

    /// 用户名，必须唯一且符合规范
    #[validate(length(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间"))]
    #[validate(regex(
        path = "*USERNAME_REGEX",
        message = "用户名只能包含字母、数字、下划线和连字符"
    ))]
    pub username: String,

    /// 用户邮箱
    #[validate(email(message = "请输入有效的邮箱地址"))]
    pub email: Option<String>,

    /// 密码哈希值（不直接暴露密码）
    #[serde(skip_serializing)]
    pub password_hash: String,

    /// 用户显示名称（昵称）
    #[validate(length(max = 100, message = "显示名称长度不能超过100个字符"))]
    pub display_name: Option<String>,

    /// 用户头像URL
    #[validate(url(message = "请输入有效的头像URL"))]
    #[validate(length(max = 500, message = "头像URL长度不能超过500个字符"))]
    pub avatar_url: Option<String>,

    /// 用户个人简介
    #[validate(length(max = 1000, message = "个人简介长度不能超过1000个字符"))]
    pub bio: Option<String>,

    /// 用户位置信息
    #[validate(length(max = 100, message = "位置信息长度不能超过100个字符"))]
    pub location: Option<String>,

    /// 用户个人网站
    #[validate(url(message = "请输入有效的网站URL"))]
    #[validate(length(max = 200, message = "网站URL长度不能超过200个字符"))]
    pub website: Option<String>,

    /// 最后登录时间
    pub last_login_at: Option<DateTime<Utc>>,

    /// 账户状态
    #[validate(custom(function = "validate_user_status"))]
    pub status: String,

    /// 创建时间
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

// 注意：UserResponse、CreateUserRequest、UpdateUserRequest已迁移到app_interfaces crate
// 这些是API契约层的责任，不应该在领域层定义

/// 验证用户状态的有效性
///
/// # 参数
/// - `status`: 要验证的状态字符串
///
/// # 返回
/// - `Result<(), ValidationError>`: 验证结果
fn validate_user_status(status: &str) -> Result<(), validator::ValidationError> {
    const VALID_STATUSES: &[&str] = &["active", "inactive", "suspended"];

    if VALID_STATUSES.contains(&status) {
        Ok(())
    } else {
        Err(validator::ValidationError::new("invalid_status"))
    }
}

impl User {
    /// 创建新用户
    ///
    /// # 参数
    /// - `username`: 用户名
    /// - `password_hash`: 已哈希的密码
    ///
    /// # 返回
    /// - `Result<User, ValidationErrors>`: 成功返回用户实体，失败返回验证错误
    pub fn new(
        username: String,
        email: Option<String>,
        password_hash: String,
    ) -> Result<Self, ValidationErrors> {
        let now = Utc::now();
        let user = Self {
            id: Uuid::new_v4(),
            username,
            email,
            password_hash,
            display_name: None,
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(), // 默认状态为活跃
            created_at: now,
            updated_at: now,
        };

        // 验证用户数据
        user.validate()?;
        Ok(user)
    }

    /// 更新用户名
    ///
    /// # 参数
    /// - `new_username`: 新的用户名
    ///
    /// # 返回
    /// - `Result<(), ValidationErrors>`: 成功返回空，失败返回验证错误
    pub fn update_username(&mut self, new_username: String) -> Result<(), ValidationErrors> {
        // 创建临时用户进行验证
        let temp_user = Self {
            username: new_username.clone(),
            ..self.clone()
        };

        // 验证新用户名
        temp_user.validate()?;

        // 更新用户名和时间戳
        self.username = new_username;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 更新密码哈希
    ///
    /// # 参数
    /// - `new_password_hash`: 新的密码哈希值
    pub fn update_password_hash(&mut self, new_password_hash: String) {
        self.password_hash = new_password_hash;
        self.updated_at = Utc::now();
    }

    /// 验证密码
    ///
    /// # 参数
    /// - `password_hash`: 要验证的密码哈希
    ///
    /// # 返回
    /// - `bool`: 密码是否匹配
    pub fn verify_password(&self, password_hash: &str) -> bool {
        self.password_hash == password_hash
    }

    // 注意：to_response方法已移除，因为UserResponse已迁移到app_interfaces crate
    // 转换逻辑应该在应用层或基础设施层实现
}

// 注意：数据库模型转换逻辑已移至基础设施层
// 这里保留领域实体的纯业务逻辑，不直接依赖数据库模型

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_user_success() {
        let username = "test_user".to_string();
        let password_hash = "hashed_password".to_string();

        let user = User::new(username.clone(), None, password_hash.clone()).unwrap();

        assert_eq!(user.username, username);
        assert_eq!(user.password_hash, password_hash);
        assert!(!user.id.is_nil());
    }

    #[test]
    fn test_create_user_invalid_username() {
        let username = "ab".to_string(); // 太短
        let password_hash = "hashed_password".to_string();

        let result = User::new(username, None, password_hash);
        assert!(result.is_err());
    }

    #[test]
    fn test_update_username_success() {
        let mut user =
            User::new("test_user".to_string(), None, "hashed_password".to_string()).unwrap();
        let new_username = "new_username".to_string();

        let result = user.update_username(new_username.clone());
        assert!(result.is_ok());
        assert_eq!(user.username, new_username);
    }

    #[test]
    fn test_update_username_invalid() {
        let mut user =
            User::new("test_user".to_string(), None, "hashed_password".to_string()).unwrap();
        let invalid_username = "ab".to_string(); // 太短

        let result = user.update_username(invalid_username);
        assert!(result.is_err());
        assert_eq!(user.username, "test_user"); // 用户名不应该改变
    }

    #[test]
    fn test_verify_password() {
        let password_hash = "hashed_password".to_string();
        let user = User::new("test_user".to_string(), None, password_hash.clone()).unwrap();

        assert!(user.verify_password(&password_hash));
        assert!(!user.verify_password("wrong_password"));
    }

    #[test]
    fn test_to_response() {
        let user = User::new("test_user".to_string(), None, "hashed_password".to_string()).unwrap();
        // 注意：to_response方法已移除，因为UserResponse已迁移到app_interfaces crate

        assert_eq!(user.username, "test_user");
        assert!(!user.id.is_nil());
    }
}
