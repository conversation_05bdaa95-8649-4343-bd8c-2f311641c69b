//! # 用户会话实体
//!
//! 用户会话领域实体，封装会话相关的业务逻辑和不变性约束

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use validator::{Validate, ValidationErrors};

/// 用户会话创建参数
#[derive(Debug, Clone)]
pub struct UserSessionCreateParams {
    pub user_id: Uuid,
    pub session_token: String,
    pub device_type: DeviceType,
    pub device_info: Option<String>,
    pub ip_address: String,
    pub user_agent: Option<String>,
    pub metadata: Option<String>,
    pub expires_at: Option<DateTime<Utc>>,
}

/// 会话状态枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum SessionStatus {
    /// 在线状态
    Online,
    /// 离线状态
    Offline,
    /// 离开状态
    Away,
    /// 忙碌状态
    Busy,
    /// 隐身状态
    Invisible,
}

/// 设备类型枚举
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum DeviceType {
    /// 桌面设备
    Desktop,
    /// 移动设备
    Mobile,
    /// 平板设备
    Tablet,
    /// Web浏览器
    Web,
}

/// 用户会话领域实体
///
/// 设计原则：
/// - 封装会话的核心业务逻辑
/// - 维护会话数据的不变性约束
/// - 与数据库模型解耦，专注于业务规则
/// - 提供类型安全的会话操作接口
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize, Validate)]
pub struct UserSession {
    /// 会话唯一标识符
    pub id: Uuid,

    /// 关联的用户ID
    pub user_id: Uuid,

    /// 会话令牌（用于WebSocket认证）
    #[validate(length(min = 1, max = 255, message = "会话令牌长度必须在1-255个字符之间"))]
    pub session_token: String,

    /// 会话状态
    pub status: SessionStatus,

    /// 设备类型
    pub device_type: DeviceType,

    /// 设备信息（如设备名称、操作系统等）
    #[validate(length(max = 255, message = "设备信息不能超过255个字符"))]
    pub device_info: Option<String>,

    /// 客户端IP地址
    #[validate(length(min = 1, max = 45, message = "IP地址长度必须在1-45个字符之间"))]
    pub ip_address: String,

    /// 用户代理字符串
    pub user_agent: Option<String>,

    /// 当前所在聊天室ID（可选）
    pub current_chat_room_id: Option<Uuid>,

    /// 最后活跃时间（用于心跳检测）
    pub last_activity_at: DateTime<Utc>,

    /// 最后心跳时间
    pub last_heartbeat_at: DateTime<Utc>,

    /// 会话元数据（JSON格式，存储额外的会话信息）
    pub metadata: Option<String>,

    /// 会话过期时间
    pub expires_at: Option<DateTime<Utc>>,

    /// 创建时间（连接建立时间）
    pub created_at: DateTime<Utc>,

    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 创建用户会话的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateUserSessionRequest {
    pub user_id: Uuid,

    #[validate(length(min = 1, max = 255, message = "会话令牌长度必须在1-255个字符之间"))]
    pub session_token: String,

    pub device_type: DeviceType,

    #[validate(length(max = 255, message = "设备信息不能超过255个字符"))]
    pub device_info: Option<String>,

    #[validate(length(min = 1, max = 45, message = "IP地址长度必须在1-45个字符之间"))]
    pub ip_address: String,

    pub user_agent: Option<String>,

    pub metadata: Option<String>,

    pub expires_at: Option<DateTime<Utc>>,
}

/// 更新用户会话的请求载荷
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct UpdateUserSessionRequest {
    pub status: Option<SessionStatus>,

    #[validate(length(max = 255, message = "设备信息不能超过255个字符"))]
    pub device_info: Option<String>,

    pub current_chat_room_id: Option<Uuid>,

    pub metadata: Option<String>,
}

impl UserSession {
    /// 创建新用户会话
    ///
    /// # 参数
    /// - `params`: 用户会话创建参数
    ///
    /// # 返回
    /// - `Result<UserSession, ValidationErrors>`: 成功返回会话实体，失败返回验证错误
    pub fn new(params: UserSessionCreateParams) -> Result<Self, ValidationErrors> {
        let now = Utc::now();
        let session = Self {
            id: Uuid::new_v4(),
            user_id: params.user_id,
            session_token: params.session_token,
            status: SessionStatus::Online,
            device_type: params.device_type,
            device_info: params.device_info,
            ip_address: params.ip_address,
            user_agent: params.user_agent,
            current_chat_room_id: None,
            last_activity_at: now,
            last_heartbeat_at: now,
            metadata: params.metadata,
            expires_at: params.expires_at,
            created_at: now,
            updated_at: now,
        };

        // 验证会话数据
        session.validate()?;
        Ok(session)
    }

    /// 更新会话状态
    ///
    /// # 参数
    /// - `new_status`: 新的会话状态
    pub fn update_status(&mut self, new_status: SessionStatus) {
        self.status = new_status;
        self.updated_at = Utc::now();
    }

    /// 设置为在线状态
    pub fn set_online(&mut self) {
        self.status = SessionStatus::Online;
        self.last_activity_at = Utc::now();
        self.updated_at = Utc::now();
    }

    /// 设置为离线状态
    pub fn set_offline(&mut self) {
        self.status = SessionStatus::Offline;
        self.updated_at = Utc::now();
    }

    /// 设置为离开状态
    pub fn set_away(&mut self) {
        self.status = SessionStatus::Away;
        self.updated_at = Utc::now();
    }

    /// 设置为忙碌状态
    pub fn set_busy(&mut self) {
        self.status = SessionStatus::Busy;
        self.updated_at = Utc::now();
    }

    /// 设置为隐身状态
    pub fn set_invisible(&mut self) {
        self.status = SessionStatus::Invisible;
        self.updated_at = Utc::now();
    }

    /// 更新心跳时间
    pub fn update_heartbeat(&mut self) {
        self.last_heartbeat_at = Utc::now();
        self.last_activity_at = Utc::now();
        self.updated_at = Utc::now();
    }

    /// 更新活跃时间
    pub fn update_activity(&mut self) {
        self.last_activity_at = Utc::now();
        self.updated_at = Utc::now();
    }

    /// 加入聊天室
    ///
    /// # 参数
    /// - `chat_room_id`: 聊天室ID
    pub fn join_chat_room(&mut self, chat_room_id: Uuid) {
        self.current_chat_room_id = Some(chat_room_id);
        self.last_activity_at = Utc::now();
        self.updated_at = Utc::now();
    }

    /// 离开聊天室
    pub fn leave_chat_room(&mut self) {
        self.current_chat_room_id = None;
        self.last_activity_at = Utc::now();
        self.updated_at = Utc::now();
    }

    /// 更新设备信息
    ///
    /// # 参数
    /// - `new_device_info`: 新的设备信息
    ///
    /// # 返回
    /// - `Result<(), ValidationErrors>`: 成功返回空，失败返回验证错误
    pub fn update_device_info(
        &mut self,
        new_device_info: Option<String>,
    ) -> Result<(), ValidationErrors> {
        // 创建临时会话进行验证
        let temp_session = Self {
            device_info: new_device_info.clone(),
            ..self.clone()
        };

        // 验证新设备信息
        temp_session.validate()?;

        // 更新设备信息和时间戳
        self.device_info = new_device_info;
        self.updated_at = Utc::now();

        Ok(())
    }

    /// 检查会话是否已过期
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            Utc::now() > expires_at
        } else {
            false
        }
    }

    /// 检查会话是否在线
    pub fn is_online(&self) -> bool {
        self.status == SessionStatus::Online && !self.is_expired()
    }

    /// 检查会话是否离线
    pub fn is_offline(&self) -> bool {
        self.status == SessionStatus::Offline || self.is_expired()
    }

    /// 检查会话是否离开
    pub fn is_away(&self) -> bool {
        self.status == SessionStatus::Away
    }

    /// 检查会话是否忙碌
    pub fn is_busy(&self) -> bool {
        self.status == SessionStatus::Busy
    }

    /// 检查会话是否隐身
    pub fn is_invisible(&self) -> bool {
        self.status == SessionStatus::Invisible
    }

    /// 检查会话是否在指定聊天室中
    ///
    /// # 参数
    /// - `chat_room_id`: 聊天室ID
    ///
    /// # 返回
    /// - `bool`: 会话是否在该聊天室中
    pub fn is_in_chat_room(&self, chat_room_id: &Uuid) -> bool {
        self.current_chat_room_id.as_ref() == Some(chat_room_id)
    }

    /// 检查会话是否属于指定用户
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `bool`: 会话是否属于该用户
    pub fn belongs_to_user(&self, user_id: &Uuid) -> bool {
        self.user_id == *user_id
    }

    /// 检查会话是否需要心跳检测
    ///
    /// # 参数
    /// - `timeout_seconds`: 心跳超时时间（秒）
    ///
    /// # 返回
    /// - `bool`: 是否需要心跳检测
    pub fn needs_heartbeat(&self, timeout_seconds: i64) -> bool {
        let timeout_duration = chrono::Duration::seconds(timeout_seconds);
        Utc::now() - self.last_heartbeat_at > timeout_duration
    }

    /// 检查会话是否长时间未活跃
    ///
    /// # 参数
    /// - `timeout_seconds`: 活跃超时时间（秒）
    ///
    /// # 返回
    /// - `bool`: 是否长时间未活跃
    pub fn is_inactive(&self, timeout_seconds: i64) -> bool {
        let timeout_duration = chrono::Duration::seconds(timeout_seconds);
        Utc::now() - self.last_activity_at > timeout_duration
    }
}

/// 从CreateUserSessionRequest转换为UserSessionCreateParams
impl From<CreateUserSessionRequest> for UserSessionCreateParams {
    fn from(request: CreateUserSessionRequest) -> Self {
        Self {
            user_id: request.user_id,
            session_token: request.session_token,
            device_type: request.device_type,
            device_info: request.device_info,
            ip_address: request.ip_address,
            user_agent: request.user_agent,
            metadata: request.metadata,
            expires_at: request.expires_at,
        }
    }
}

// 注意：数据库模型转换逻辑已移至基础设施层
// 这里保留领域实体的纯业务逻辑，不直接依赖数据库模型
