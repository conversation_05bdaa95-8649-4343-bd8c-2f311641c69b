//! # 领域事件基础定义
//!
//! 定义领域事件的基础trait和通用功能

use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 领域事件基础trait
///
/// 所有领域事件都必须实现此trait，提供事件的基本信息
#[async_trait]
pub trait DomainEvent: Send + Sync + std::fmt::Debug {
    /// 获取事件类型标识符
    ///
    /// # 返回
    /// 事件类型的字符串标识符，用于事件路由和处理
    fn event_type(&self) -> &'static str;

    /// 获取事件发生时间
    ///
    /// # 返回
    /// 事件发生的UTC时间戳
    fn occurred_at(&self) -> DateTime<Utc>;

    /// 获取聚合根ID
    ///
    /// # 返回
    /// 触发此事件的聚合根的唯一标识符
    fn aggregate_id(&self) -> Uuid;

    /// 获取事件版本（可选）
    ///
    /// # 返回
    /// 事件的版本号，用于事件演化和兼容性处理
    fn version(&self) -> u32 {
        1
    }

    /// 获取事件元数据（可选）
    ///
    /// # 返回
    /// 事件的附加元数据，以JSON格式存储
    fn metadata(&self) -> Option<String> {
        None
    }
}

/// 事件元数据
///
/// 包含事件的附加信息，如用户ID、会话ID等
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EventMetadata {
    /// 触发事件的用户ID
    pub user_id: Option<Uuid>,

    /// 会话ID
    pub session_id: Option<String>,

    /// 请求ID（用于追踪）
    pub request_id: Option<String>,

    /// 事件来源
    pub source: Option<String>,

    /// 附加属性
    pub properties: std::collections::HashMap<String, String>,
}

impl EventMetadata {
    /// 创建新的事件元数据
    pub fn new() -> Self {
        Self {
            user_id: None,
            session_id: None,
            request_id: None,
            source: None,
            properties: std::collections::HashMap::new(),
        }
    }

    /// 设置用户ID
    pub fn with_user_id(mut self, user_id: Uuid) -> Self {
        self.user_id = Some(user_id);
        self
    }

    /// 设置会话ID
    pub fn with_session_id(mut self, session_id: String) -> Self {
        self.session_id = Some(session_id);
        self
    }

    /// 设置请求ID
    pub fn with_request_id(mut self, request_id: String) -> Self {
        self.request_id = Some(request_id);
        self
    }

    /// 设置事件来源
    pub fn with_source(mut self, source: String) -> Self {
        self.source = Some(source);
        self
    }

    /// 添加属性
    pub fn with_property(mut self, key: String, value: String) -> Self {
        self.properties.insert(key, value);
        self
    }

    /// 转换为JSON字符串
    pub fn to_json(&self) -> Result<String, app_common::serde_json::Error> {
        app_common::serde_json::to_string(self)
    }

    /// 从JSON字符串创建
    pub fn from_json(json: &str) -> Result<Self, app_common::serde_json::Error> {
        app_common::serde_json::from_str(json)
    }
}

impl Default for EventMetadata {
    fn default() -> Self {
        Self::new()
    }
}

/// 事件包装器
///
/// 包装领域事件，添加统一的元数据和序列化支持
#[derive(Debug, Clone)]
pub struct EventWrapper {
    /// 事件ID
    pub id: Uuid,

    /// 事件类型
    pub event_type: String,

    /// 聚合根ID
    pub aggregate_id: Uuid,

    /// 事件版本
    pub version: u32,

    /// 发生时间
    pub occurred_at: DateTime<Utc>,

    /// 事件数据（JSON格式）
    pub data: String,

    /// 事件元数据
    pub metadata: Option<EventMetadata>,
}

impl EventWrapper {
    /// 从领域事件创建包装器
    pub fn from_domain_event<T>(
        event: &T,
        metadata: Option<EventMetadata>,
    ) -> Result<Self, app_common::serde_json::Error>
    where
        T: DomainEvent + Serialize,
    {
        Ok(Self {
            id: Uuid::new_v4(),
            event_type: event.event_type().to_string(),
            aggregate_id: event.aggregate_id(),
            version: event.version(),
            occurred_at: event.occurred_at(),
            data: app_common::serde_json::to_string(event)?,
            metadata,
        })
    }

    /// 获取事件数据的类型化表示
    pub fn deserialize_data<T>(&self) -> Result<T, app_common::serde_json::Error>
    where
        T: for<'de> Deserialize<'de>,
    {
        app_common::serde_json::from_str(&self.data)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_event_metadata_creation() {
        let metadata = EventMetadata::new()
            .with_user_id(Uuid::new_v4())
            .with_session_id("session123".to_string())
            .with_request_id("req456".to_string())
            .with_source("web".to_string())
            .with_property("ip".to_string(), "***********".to_string());

        assert!(metadata.user_id.is_some());
        assert_eq!(metadata.session_id, Some("session123".to_string()));
        assert_eq!(metadata.request_id, Some("req456".to_string()));
        assert_eq!(metadata.source, Some("web".to_string()));
        assert_eq!(
            metadata.properties.get("ip"),
            Some(&"***********".to_string())
        );
    }

    #[test]
    fn test_event_metadata_serialization() {
        let metadata = EventMetadata::new()
            .with_user_id(Uuid::new_v4())
            .with_property("test".to_string(), "value".to_string());

        let json = metadata.to_json().unwrap();
        let deserialized = EventMetadata::from_json(&json).unwrap();

        assert_eq!(metadata.user_id, deserialized.user_id);
        assert_eq!(metadata.properties, deserialized.properties);
    }
}
