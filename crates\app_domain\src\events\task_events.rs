//! # 任务相关领域事件
//!
//! 定义任务聚合根相关的所有领域事件

use super::domain_event::DomainEvent;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 任务创建事件
///
/// 当新任务成功创建时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskCreatedEvent {
    /// 任务ID
    pub task_id: Uuid,

    /// 任务标题
    pub title: String,

    /// 任务所有者用户ID
    pub user_id: Uuid,

    /// 创建者用户ID（可能与所有者不同）
    pub created_by: Uuid,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl TaskCreatedEvent {
    /// 创建新的任务创建事件
    pub fn new(task_id: Uuid, title: String, user_id: Uuid, created_by: Uuid) -> Self {
        Self {
            task_id,
            title,
            user_id,
            created_by,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for TaskCreatedEvent {
    fn event_type(&self) -> &'static str {
        "TaskCreated"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.task_id
    }
}

/// 任务状态变更事件
///
/// 当任务的完成状态发生变化时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStatusChangedEvent {
    /// 任务ID
    pub task_id: Uuid,

    /// 任务标题
    pub title: String,

    /// 任务所有者用户ID
    pub user_id: Uuid,

    /// 旧状态
    pub old_status: bool,

    /// 新状态
    pub new_status: bool,

    /// 状态变更者用户ID
    pub changed_by: Uuid,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl TaskStatusChangedEvent {
    /// 创建新的任务状态变更事件
    pub fn new(
        task_id: Uuid,
        title: String,
        user_id: Uuid,
        old_status: bool,
        new_status: bool,
        changed_by: Uuid,
    ) -> Self {
        Self {
            task_id,
            title,
            user_id,
            old_status,
            new_status,
            changed_by,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for TaskStatusChangedEvent {
    fn event_type(&self) -> &'static str {
        "TaskStatusChanged"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.task_id
    }
}

/// 任务更新事件
///
/// 当任务信息被更新时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskUpdatedEvent {
    /// 任务ID
    pub task_id: Uuid,

    /// 任务标题
    pub title: String,

    /// 任务所有者用户ID
    pub user_id: Uuid,

    /// 更新的字段列表
    pub updated_fields: Vec<String>,

    /// 更新者用户ID
    pub updated_by: Uuid,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl TaskUpdatedEvent {
    /// 创建新的任务更新事件
    pub fn new(
        task_id: Uuid,
        title: String,
        user_id: Uuid,
        updated_fields: Vec<String>,
        updated_by: Uuid,
    ) -> Self {
        Self {
            task_id,
            title,
            user_id,
            updated_fields,
            updated_by,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for TaskUpdatedEvent {
    fn event_type(&self) -> &'static str {
        "TaskUpdated"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.task_id
    }
}

/// 任务删除事件
///
/// 当任务被删除时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskDeletedEvent {
    /// 任务ID
    pub task_id: Uuid,

    /// 任务标题（用于审计）
    pub title: String,

    /// 任务所有者用户ID
    pub user_id: Uuid,

    /// 删除者用户ID
    pub deleted_by: Uuid,

    /// 删除原因
    pub reason: Option<String>,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl TaskDeletedEvent {
    /// 创建新的任务删除事件
    pub fn new(
        task_id: Uuid,
        title: String,
        user_id: Uuid,
        deleted_by: Uuid,
        reason: Option<String>,
    ) -> Self {
        Self {
            task_id,
            title,
            user_id,
            deleted_by,
            reason,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for TaskDeletedEvent {
    fn event_type(&self) -> &'static str {
        "TaskDeleted"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.task_id
    }
}

/// 任务分配事件
///
/// 当任务被分配给新用户时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskAssignedEvent {
    /// 任务ID
    pub task_id: Uuid,

    /// 任务标题
    pub title: String,

    /// 原所有者用户ID
    pub old_user_id: Option<Uuid>,

    /// 新所有者用户ID
    pub new_user_id: Uuid,

    /// 分配者用户ID
    pub assigned_by: Uuid,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl TaskAssignedEvent {
    /// 创建新的任务分配事件
    pub fn new(
        task_id: Uuid,
        title: String,
        old_user_id: Option<Uuid>,
        new_user_id: Uuid,
        assigned_by: Uuid,
    ) -> Self {
        Self {
            task_id,
            title,
            old_user_id,
            new_user_id,
            assigned_by,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for TaskAssignedEvent {
    fn event_type(&self) -> &'static str {
        "TaskAssigned"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.task_id
    }
}

/// 任务完成事件
///
/// 当任务被标记为完成时触发此事件（TaskStatusChangedEvent的特化版本）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskCompletedEvent {
    /// 任务ID
    pub task_id: Uuid,

    /// 任务标题
    pub title: String,

    /// 任务所有者用户ID
    pub user_id: Uuid,

    /// 完成者用户ID
    pub completed_by: Uuid,

    /// 完成时间
    pub completed_at: DateTime<Utc>,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl TaskCompletedEvent {
    /// 创建新的任务完成事件
    pub fn new(task_id: Uuid, title: String, user_id: Uuid, completed_by: Uuid) -> Self {
        let now = Utc::now();
        Self {
            task_id,
            title,
            user_id,
            completed_by,
            completed_at: now,
            occurred_at: now,
        }
    }
}

#[async_trait]
impl DomainEvent for TaskCompletedEvent {
    fn event_type(&self) -> &'static str {
        "TaskCompleted"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.task_id
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_task_created_event() {
        let task_id = Uuid::new_v4();
        let user_id = Uuid::new_v4();
        let created_by = Uuid::new_v4();

        let event = TaskCreatedEvent::new(task_id, "测试任务".to_string(), user_id, created_by);

        assert_eq!(event.event_type(), "TaskCreated");
        assert_eq!(event.aggregate_id(), task_id);
        assert_eq!(event.title, "测试任务");
        assert_eq!(event.user_id, user_id);
        assert_eq!(event.created_by, created_by);
    }

    #[test]
    fn test_task_status_changed_event() {
        let task_id = Uuid::new_v4();
        let user_id = Uuid::new_v4();
        let changed_by = Uuid::new_v4();

        let event = TaskStatusChangedEvent::new(
            task_id,
            "测试任务".to_string(),
            user_id,
            false,
            true,
            changed_by,
        );

        assert_eq!(event.event_type(), "TaskStatusChanged");
        assert_eq!(event.aggregate_id(), task_id);
        assert!(!event.old_status);
        assert!(event.new_status);
        assert_eq!(event.changed_by, changed_by);
    }

    #[test]
    fn test_task_completed_event() {
        let task_id = Uuid::new_v4();
        let user_id = Uuid::new_v4();
        let completed_by = Uuid::new_v4();

        let event = TaskCompletedEvent::new(task_id, "测试任务".to_string(), user_id, completed_by);

        assert_eq!(event.event_type(), "TaskCompleted");
        assert_eq!(event.aggregate_id(), task_id);
        assert_eq!(event.completed_by, completed_by);
    }

    #[test]
    fn test_task_assigned_event() {
        let task_id = Uuid::new_v4();
        let old_user_id = Uuid::new_v4();
        let new_user_id = Uuid::new_v4();
        let assigned_by = Uuid::new_v4();

        let event = TaskAssignedEvent::new(
            task_id,
            "测试任务".to_string(),
            Some(old_user_id),
            new_user_id,
            assigned_by,
        );

        assert_eq!(event.event_type(), "TaskAssigned");
        assert_eq!(event.aggregate_id(), task_id);
        assert_eq!(event.old_user_id, Some(old_user_id));
        assert_eq!(event.new_user_id, new_user_id);
        assert_eq!(event.assigned_by, assigned_by);
    }
}
