//! # 用户相关领域事件
//!
//! 定义用户聚合根相关的所有领域事件

use super::domain_event::DomainEvent;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 用户创建事件
///
/// 当新用户成功创建时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserCreatedEvent {
    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: String,

    /// 邮箱地址
    pub email: String,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl UserCreatedEvent {
    /// 创建新的用户创建事件
    pub fn new(user_id: Uuid, username: String, email: String) -> Self {
        Self {
            user_id,
            username,
            email,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for UserCreatedEvent {
    fn event_type(&self) -> &'static str {
        "UserCreated"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.user_id
    }
}

/// 用户更新事件
///
/// 当用户信息被更新时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserUpdatedEvent {
    /// 用户ID
    pub user_id: Uuid,

    /// 更新的字段列表
    pub updated_fields: Vec<String>,

    /// 更新者用户ID（可能是管理员）
    pub updated_by: Uuid,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl UserUpdatedEvent {
    /// 创建新的用户更新事件
    pub fn new(user_id: Uuid, updated_fields: Vec<String>, updated_by: Uuid) -> Self {
        Self {
            user_id,
            updated_fields,
            updated_by,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for UserUpdatedEvent {
    fn event_type(&self) -> &'static str {
        "UserUpdated"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.user_id
    }
}

/// 用户删除事件
///
/// 当用户被删除时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserDeletedEvent {
    /// 用户ID
    pub user_id: Uuid,

    /// 用户名（用于审计）
    pub username: String,

    /// 删除者用户ID
    pub deleted_by: Uuid,

    /// 删除原因
    pub reason: Option<String>,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl UserDeletedEvent {
    /// 创建新的用户删除事件
    pub fn new(user_id: Uuid, username: String, deleted_by: Uuid, reason: Option<String>) -> Self {
        Self {
            user_id,
            username,
            deleted_by,
            reason,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for UserDeletedEvent {
    fn event_type(&self) -> &'static str {
        "UserDeleted"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.user_id
    }
}

/// 用户登录事件
///
/// 当用户成功登录时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserLoggedInEvent {
    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: String,

    /// 登录IP地址
    pub ip_address: Option<String>,

    /// 用户代理字符串
    pub user_agent: Option<String>,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl UserLoggedInEvent {
    /// 创建新的用户登录事件
    pub fn new(
        user_id: Uuid,
        username: String,
        ip_address: Option<String>,
        user_agent: Option<String>,
    ) -> Self {
        Self {
            user_id,
            username,
            ip_address,
            user_agent,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for UserLoggedInEvent {
    fn event_type(&self) -> &'static str {
        "UserLoggedIn"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.user_id
    }
}

/// 用户登出事件
///
/// 当用户登出时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserLoggedOutEvent {
    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: String,

    /// 会话持续时间（秒）
    pub session_duration: Option<u64>,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl UserLoggedOutEvent {
    /// 创建新的用户登出事件
    pub fn new(user_id: Uuid, username: String, session_duration: Option<u64>) -> Self {
        Self {
            user_id,
            username,
            session_duration,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for UserLoggedOutEvent {
    fn event_type(&self) -> &'static str {
        "UserLoggedOut"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.user_id
    }
}

/// 用户密码更改事件
///
/// 当用户密码被更改时触发此事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPasswordChangedEvent {
    /// 用户ID
    pub user_id: Uuid,

    /// 用户名
    pub username: String,

    /// 是否由管理员更改
    pub changed_by_admin: bool,

    /// 更改者用户ID（如果是管理员更改）
    pub changed_by: Option<Uuid>,

    /// 事件发生时间
    pub occurred_at: DateTime<Utc>,
}

impl UserPasswordChangedEvent {
    /// 创建新的用户密码更改事件
    pub fn new(
        user_id: Uuid,
        username: String,
        changed_by_admin: bool,
        changed_by: Option<Uuid>,
    ) -> Self {
        Self {
            user_id,
            username,
            changed_by_admin,
            changed_by,
            occurred_at: Utc::now(),
        }
    }
}

#[async_trait]
impl DomainEvent for UserPasswordChangedEvent {
    fn event_type(&self) -> &'static str {
        "UserPasswordChanged"
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.user_id
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_created_event() {
        let user_id = Uuid::new_v4();
        let event = UserCreatedEvent::new(
            user_id,
            "test_user".to_string(),
            "<EMAIL>".to_string(),
        );

        assert_eq!(event.event_type(), "UserCreated");
        assert_eq!(event.aggregate_id(), user_id);
        assert_eq!(event.username, "test_user");
        assert_eq!(event.email, "<EMAIL>");
    }

    #[test]
    fn test_user_updated_event() {
        let user_id = Uuid::new_v4();
        let updated_by = Uuid::new_v4();
        let event = UserUpdatedEvent::new(
            user_id,
            vec!["email".to_string(), "display_name".to_string()],
            updated_by,
        );

        assert_eq!(event.event_type(), "UserUpdated");
        assert_eq!(event.aggregate_id(), user_id);
        assert_eq!(event.updated_fields.len(), 2);
        assert_eq!(event.updated_by, updated_by);
    }

    #[test]
    fn test_user_logged_in_event() {
        let user_id = Uuid::new_v4();
        let event = UserLoggedInEvent::new(
            user_id,
            "test_user".to_string(),
            Some("***********".to_string()),
            Some("Mozilla/5.0".to_string()),
        );

        assert_eq!(event.event_type(), "UserLoggedIn");
        assert_eq!(event.aggregate_id(), user_id);
        assert_eq!(event.ip_address, Some("***********".to_string()));
    }
}
