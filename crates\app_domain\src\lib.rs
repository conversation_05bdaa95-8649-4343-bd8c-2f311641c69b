//! # App Domain
//!
//! 领域层，包含核心业务模型、实体和规则：
//! - 业务实体定义
//! - 领域服务接口
//! - 业务规则和不变量
//! - 值对象

// ============================================================================
// 模块声明 - 按DDD架构分层
// ============================================================================

// 核心实体模块 - 领域对象定义
pub mod entities;

// 领域事件模块 - 事件驱动架构支持
pub mod events;

// 仓库接口模块 - 数据访问抽象
pub mod repositories;

// 领域服务接口模块 - 业务逻辑抽象
pub mod services;

// 业务领域模块 - 按业务边界组织
pub mod chat;
pub mod task;
pub mod websocket;

// ============================================================================
// 核心类型重新导出 - 按使用频率和重要性排序
// ============================================================================

// 实体类型导出
pub use entities::{
    // 认证相关实体
    AuthToken,
    AuthUserSession,
    // 聊天相关实体
    ChatRoom,
    Message,
    PasswordHash,
    // 任务相关实体
    Task,
    // 用户相关实体
    User,
    UserSession,
};

// 领域事件导出
pub use events::{
    DomainEvent,
    DomainEventPublisher,
    EventMetadata,
    EventWrapper,
    InMemoryEventPublisher,
    NullEventPublisher,
    // 任务事件
    TaskAssignedEvent,
    TaskCompletedEvent,
    TaskCreatedEvent,
    TaskDeletedEvent,
    TaskStatusChangedEvent,
    TaskUpdatedEvent,
    // 用户事件
    UserCreatedEvent,
    UserDeletedEvent,
    UserLoggedInEvent,
    UserLoggedOutEvent,
    UserPasswordChangedEvent,
    UserUpdatedEvent,
};

// 仓库接口导出
pub use repositories::{ChatRepositoryContract, TaskRepositoryContract, UserRepositoryContract};

// 领域服务接口导出
pub use services::{AuthDomainService, ChatDomainService, TaskDomainService, UserDomainService};

// WebSocket相关类型导出
pub use websocket::{WebSocketConnectionService, WebSocketMessageService, WebSocketStatsService};

// ============================================================================
// 外部依赖重新导出 - 仅导出领域层需要的依赖
// ============================================================================

// 公共模块依赖
pub use app_common;

// 异步trait支持
pub use async_trait;

// 时间处理
pub use chrono;

// 序列化支持
pub use serde;

// 唯一标识符
pub use uuid;

// 验证支持
pub use validator;
