//! # 聊天仓库接口
//!
//! 定义聊天相关的数据访问接口

use crate::entities::chat::GlobalChatRoom;
use crate::entities::user_session::UserSession;
use crate::entities::{ChatRoom, Message};
use app_common::error::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sea_orm::prelude::Uuid;

/// 聊天仓库接口
///
/// 定义聊天相关的数据访问操作
#[async_trait]
pub trait ChatRepositoryContract: Send + Sync {
    // 聊天室相关操作

    /// 创建聊天室
    async fn create_chat_room(&self, chat_room: ChatRoom) -> Result<ChatRoom>;

    /// 根据ID查找聊天室
    async fn find_chat_room_by_id(&self, room_id: Uuid) -> Result<Option<ChatRoom>>;

    /// 根据名称查找聊天室
    async fn find_chat_room_by_name(&self, name: &str) -> Result<Option<ChatRoom>>;

    /// 获取用户的聊天室列表
    async fn find_user_chat_rooms(&self, user_id: Uuid) -> Result<Vec<ChatRoom>>;

    /// 更新聊天室
    async fn update_chat_room(&self, chat_room: ChatRoom) -> Result<ChatRoom>;

    /// 删除聊天室
    async fn delete_chat_room(&self, room_id: Uuid) -> Result<()>;

    // 消息相关操作

    /// 创建消息
    async fn create_message(&self, message: Message) -> Result<Message>;

    /// 根据ID查找消息
    async fn find_message_by_id(&self, message_id: Uuid) -> Result<Option<Message>>;

    /// 获取聊天室消息历史
    async fn find_room_messages(
        &self,
        room_id: Uuid,
        limit: u32,
        before: Option<DateTime<Utc>>,
    ) -> Result<Vec<Message>>;

    /// 删除消息
    async fn delete_message(&self, message_id: Uuid) -> Result<()>;

    // 用户会话相关操作

    /// 创建用户会话
    async fn create_user_session(&self, session: UserSession) -> Result<UserSession>;

    /// 查找用户会话
    async fn find_user_session(&self, room_id: Uuid, user_id: Uuid) -> Result<Option<UserSession>>;

    /// 获取聊天室在线用户
    async fn find_room_online_users(&self, room_id: Uuid) -> Result<Vec<UserSession>>;

    /// 更新用户会话
    async fn update_user_session(&self, session: UserSession) -> Result<UserSession>;

    /// 删除用户会话
    async fn delete_user_session(&self, session_id: Uuid) -> Result<()>;

    /// 设置用户离线
    async fn set_user_offline(&self, room_id: Uuid, user_id: Uuid) -> Result<()>;

    // 全局聊天室相关操作

    /// 查找全局聊天室
    async fn find_global_chat_room(&self) -> Result<Option<GlobalChatRoom>>;

    /// 创建全局聊天室
    async fn create_global_chat_room(&self, global_room: GlobalChatRoom) -> Result<GlobalChatRoom>;

    /// 更新全局聊天室
    async fn update_global_chat_room(&self, global_room: GlobalChatRoom) -> Result<GlobalChatRoom>;

    // 扩展的消息操作

    /// 在聊天室中搜索消息
    async fn search_messages_in_room(
        &self,
        room_id: Uuid,
        query: String,
        limit: u32,
        start_time: Option<DateTime<Utc>>,
        end_time: Option<DateTime<Utc>>,
        sender_id: Option<Uuid>,
    ) -> Result<Vec<Message>>;

    /// 获取聊天室消息历史（支持分页）
    async fn get_room_message_history(
        &self,
        room_id: Uuid,
        limit: u32,
        before: Option<DateTime<Utc>>,
        after: Option<DateTime<Utc>>,
    ) -> Result<Vec<Message>>;

    // 用户验证相关操作

    /// 检查用户是否存在
    async fn user_exists(&self, user_id: Uuid) -> Result<bool>;
}
