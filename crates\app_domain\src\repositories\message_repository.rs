//! # 消息仓库接口
//!
//! 定义消息数据访问的抽象接口

use crate::entities::{Message, MessageStatus, MessageType};
use async_trait::async_trait;
use sea_orm::DbErr;
use uuid::Uuid;

/// 分页查询参数
#[derive(Debug, Clone)]
pub struct PaginationParams {
    /// 页码（从1开始）
    pub page: u64,
    /// 每页大小
    pub page_size: u64,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            page: 1,
            page_size: 20,
        }
    }
}

impl PaginationParams {
    /// 创建新的分页参数
    pub fn new(page: u64, page_size: u64) -> Self {
        Self {
            page: page.max(1),                  // 确保页码至少为1
            page_size: page_size.clamp(1, 100), // 限制页面大小在1-100之间
        }
    }

    /// 计算偏移量
    pub fn offset(&self) -> u64 {
        (self.page - 1) * self.page_size
    }
}

/// 消息查询过滤器
#[derive(Debug, <PERSON><PERSON>, Default)]
pub struct MessageFilter {
    /// 消息类型过滤
    pub message_type: Option<MessageType>,
    /// 消息状态过滤
    pub status: Option<MessageStatus>,
    /// 发送者ID过滤
    pub sender_id: Option<Uuid>,
    /// 聊天室ID过滤（可选，用于跨聊天室搜索时限制范围）
    pub chat_room_id: Option<Uuid>,
    /// 开始时间过滤
    pub start_time: Option<chrono::DateTime<chrono::Utc>>,
    /// 结束时间过滤
    pub end_time: Option<chrono::DateTime<chrono::Utc>>,
    /// 关键词搜索（在消息内容中搜索）
    pub keyword: Option<String>,
}

/// 消息仓库的抽象 Trait。
///
/// 定义了消息仓库必须实现的所有功能协定。
/// 注意：在 Axum 0.8.4 升级中，此 trait 保留 `#[async_trait]` 宏，
/// 因为项目中大量使用 `Arc<dyn MessageRepositoryContract>` 需要 dyn compatible trait。
/// 原生异步 trait 语法不支持 dyn compatibility，所以继续使用 async_trait。
/// `Send + Sync` 约束是让它能在多线程环境下安全地共享。
#[async_trait]
pub trait MessageRepositoryContract: Send + Sync {
    /// 创建新消息
    async fn create(&self, message: Message) -> Result<Message, DbErr>;

    /// 根据ID查询单条消息
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Message>, DbErr>;

    /// 按聊天室查询消息（支持分页）
    async fn find_by_chat_room(
        &self,
        chat_room_id: Uuid,
        pagination: PaginationParams,
        filter: Option<MessageFilter>,
    ) -> Result<Vec<Message>, DbErr>;

    /// 更新消息状态
    async fn update_status(&self, id: Uuid, status: MessageStatus) -> Result<Message, DbErr>;

    /// 统计聊天室消息数量
    async fn count_by_chat_room(
        &self,
        chat_room_id: Uuid,
        filter: Option<MessageFilter>,
    ) -> Result<u64, DbErr>;

    /// 删除消息
    async fn delete(&self, id: Uuid) -> Result<(), DbErr>;

    /// 查询聊天室最近消息
    async fn find_recent_by_chat_room(
        &self,
        chat_room_id: Uuid,
        limit: u64,
    ) -> Result<Vec<Message>, DbErr>;
}
