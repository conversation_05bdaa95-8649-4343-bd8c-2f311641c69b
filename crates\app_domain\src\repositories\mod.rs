//! # 仓库接口模块
//!
//! 定义数据访问层的抽象接口，遵循依赖倒置原则
//!
//! ## 设计原则
//!
//! ### 1. 依赖倒置原则 (DIP)
//! - 领域层定义抽象接口，基础设施层实现具体逻辑
//! - 高层模块不依赖低层模块，两者都依赖抽象
//!
//! ### 2. 接口隔离原则 (ISP)
//! - 每个仓储接口专注于特定聚合根的操作
//! - 避免臃肿的接口，保持接口的单一职责
//!
//! ### 3. 类型安全性
//! - 使用强类型参数和返回值
//! - 利用Rust类型系统在编译期捕获错误
//!
//! ### 4. 异步支持
//! - 所有数据库操作都是异步的
//! - 使用async_trait支持trait中的异步方法
//!
//! ### 5. 错误处理
//! - 统一的错误类型和处理机制
//! - 领域特定的错误信息

// 核心仓储接口模块
pub mod chat_repository;
pub mod message_repository;
pub mod task_repository;
pub mod user_repository;
pub mod user_session_repository;

// 仓储接口增强模块
pub mod pagination;
pub mod query_builder;
pub mod repository_traits;

// 重新导出仓库接口
pub use chat_repository::ChatRepositoryContract;
pub use message_repository::{MessageFilter, MessageRepositoryContract, PaginationParams};
pub use task_repository::TaskRepositoryContract;
pub use user_repository::UserRepositoryContract;
pub use user_session_repository::UserSessionRepositoryContract;

// 重新导出增强功能
pub use pagination::*;
pub use query_builder::*;
pub use repository_traits::*;

// 测试模块
#[cfg(test)]
pub mod tests;
