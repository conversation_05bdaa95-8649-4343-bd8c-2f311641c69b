//! # 分页功能模块
//!
//! 提供统一的分页查询功能，支持高性能的大数据集分页操作。
//!
//! ## 设计原则
//!
//! ### 1. 性能优化 (Performance)
//! - 使用LIMIT/OFFSET进行高效分页
//! - 支持游标分页避免深度分页性能问题
//! - 索引友好的查询设计
//! - 缓存分页元数据
//!
//! ### 2. 一致性 (Consistency)
//! - 统一的分页参数和结果格式
//! - 标准化的分页元信息
//! - 跨模块的分页行为一致性
//!
//! ### 3. 灵活性 (Flexibility)
//! - 支持多种分页策略
//! - 可配置的分页大小限制
//! - 自定义排序和过滤
//!
//! ### 4. 易用性 (Usability)
//! - 简单直观的API接口
//! - 丰富的分页信息
//! - 前端友好的数据格式

use crate::repositories::repository_traits::{PaginatedResult, PaginationMeta, PaginationParams};
use async_trait::async_trait;
use sea_orm::{DbErr, PaginatorTrait};
use serde::{Deserialize, Serialize};
use std::fmt::Debug;

/// 分页查询特征
///
/// 为实体提供统一的分页查询接口，支持高性能的分页操作。
#[async_trait]
pub trait Paginator<T>: Send + Sync
where
    T: Send + Sync + Clone + Debug,
{
    /// 执行分页查询
    ///
    /// # 参数
    /// - `pagination`: 分页参数
    ///
    /// # 返回
    /// - `Result<PaginatedResult<T>, DbErr>`: 成功时返回分页结果，失败时返回数据库错误
    async fn paginate(&self, pagination: PaginationParams) -> Result<PaginatedResult<T>, DbErr>;

    /// 获取总记录数
    ///
    /// # 返回
    /// - `Result<u64, DbErr>`: 成功时返回总记录数，失败时返回数据库错误
    async fn count(&self) -> Result<u64, DbErr>;
}

/// 游标分页参数
///
/// 用于高性能的游标分页，避免深度分页的性能问题。
/// 特别适用于实时数据流和大数据集的分页查询。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CursorPaginationParams {
    /// 游标值（通常是上一页的最后一个记录的ID或时间戳）
    pub cursor: Option<String>,
    /// 每页大小
    pub limit: u64,
    /// 排序方向
    pub direction: CursorDirection,
    /// 游标字段名（默认为"id"）
    pub cursor_field: String,
}

/// 游标分页方向
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CursorDirection {
    /// 向前（获取更新的记录）
    Forward,
    /// 向后（获取更旧的记录）
    Backward,
}

impl Default for CursorPaginationParams {
    fn default() -> Self {
        Self {
            cursor: None,
            limit: 20,
            direction: CursorDirection::Forward,
            cursor_field: "id".to_string(),
        }
    }
}

impl CursorPaginationParams {
    /// 创建新的游标分页参数
    ///
    /// # 参数
    /// - `limit`: 每页大小
    ///
    /// # 返回
    /// 新的游标分页参数实例
    pub fn new(limit: u64) -> Self {
        Self {
            cursor: None,
            limit: limit.clamp(1, 100), // 限制在1-100之间
            direction: CursorDirection::Forward,
            cursor_field: "id".to_string(),
        }
    }

    /// 设置游标值
    ///
    /// # 参数
    /// - `cursor`: 游标值
    ///
    /// # 返回
    /// 更新后的游标分页参数
    pub fn with_cursor(mut self, cursor: String) -> Self {
        self.cursor = Some(cursor);
        self
    }

    /// 设置分页方向
    ///
    /// # 参数
    /// - `direction`: 分页方向
    ///
    /// # 返回
    /// 更新后的游标分页参数
    pub fn with_direction(mut self, direction: CursorDirection) -> Self {
        self.direction = direction;
        self
    }

    /// 设置游标字段
    ///
    /// # 参数
    /// - `field`: 游标字段名
    ///
    /// # 返回
    /// 更新后的游标分页参数
    pub fn with_cursor_field(mut self, field: String) -> Self {
        self.cursor_field = field;
        self
    }
}

/// 游标分页结果
///
/// 包含游标分页查询的结果和元信息。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CursorPaginatedResult<T> {
    /// 查询结果数据
    pub data: Vec<T>,
    /// 游标分页元信息
    pub pagination: CursorPaginationMeta,
}

/// 游标分页元信息
///
/// 包含游标分页的导航信息，用于前端实现无限滚动等功能。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CursorPaginationMeta {
    /// 是否有下一页
    pub has_next: bool,
    /// 是否有上一页
    pub has_prev: bool,
    /// 下一页游标
    pub next_cursor: Option<String>,
    /// 上一页游标
    pub prev_cursor: Option<String>,
    /// 当前页记录数
    pub count: u64,
    /// 每页限制数
    pub limit: u64,
}

/// 游标分页查询特征
///
/// 为实体提供游标分页查询接口，适用于高性能的实时数据分页。
#[async_trait]
pub trait CursorPaginator<T>: Send + Sync
where
    T: Send + Sync + Clone + Debug,
{
    /// 执行游标分页查询
    ///
    /// # 参数
    /// - `pagination`: 游标分页参数
    ///
    /// # 返回
    /// - `Result<CursorPaginatedResult<T>, DbErr>`: 成功时返回游标分页结果，失败时返回数据库错误
    async fn paginate_cursor(
        &self,
        pagination: CursorPaginationParams,
    ) -> Result<CursorPaginatedResult<T>, DbErr>;
}

/// 分页工具类
///
/// 提供分页相关的工具方法和辅助功能。
pub struct PaginationUtils;

impl PaginationUtils {
    /// 验证分页参数
    ///
    /// # 参数
    /// - `pagination`: 分页参数
    ///
    /// # 返回
    /// - `Result<(), String>`: 验证成功返回Ok，失败返回错误信息
    pub fn validate_pagination(pagination: &PaginationParams) -> Result<(), String> {
        if pagination.page == 0 {
            return Err("页码必须大于0".to_string());
        }

        if pagination.page_size == 0 {
            return Err("每页大小必须大于0".to_string());
        }

        if pagination.page_size > 100 {
            return Err("每页大小不能超过100".to_string());
        }

        Ok(())
    }

    /// 验证游标分页参数
    ///
    /// # 参数
    /// - `pagination`: 游标分页参数
    ///
    /// # 返回
    /// - `Result<(), String>`: 验证成功返回Ok，失败返回错误信息
    pub fn validate_cursor_pagination(pagination: &CursorPaginationParams) -> Result<(), String> {
        if pagination.limit == 0 {
            return Err("每页大小必须大于0".to_string());
        }

        if pagination.limit > 100 {
            return Err("每页大小不能超过100".to_string());
        }

        if pagination.cursor_field.is_empty() {
            return Err("游标字段名不能为空".to_string());
        }

        Ok(())
    }

    /// 计算总页数
    ///
    /// # 参数
    /// - `total_count`: 总记录数
    /// - `page_size`: 每页大小
    ///
    /// # 返回
    /// 总页数
    pub fn calculate_total_pages(total_count: u64, page_size: u64) -> u64 {
        if total_count == 0 || page_size == 0 {
            return 1;
        }
        total_count.div_ceil(page_size) // 向上取整
    }

    /// 计算偏移量
    ///
    /// # 参数
    /// - `page`: 页码（从1开始）
    /// - `page_size`: 每页大小
    ///
    /// # 返回
    /// 查询偏移量
    pub fn calculate_offset(page: u64, page_size: u64) -> u64 {
        if page == 0 {
            return 0;
        }
        (page - 1) * page_size
    }

    /// 创建分页元信息
    ///
    /// # 参数
    /// - `current_page`: 当前页码
    /// - `page_size`: 每页大小
    /// - `total_count`: 总记录数
    ///
    /// # 返回
    /// 分页元信息
    pub fn create_pagination_meta(
        current_page: u64,
        page_size: u64,
        total_count: u64,
    ) -> PaginationMeta {
        PaginationMeta::new(current_page, page_size, total_count)
    }

    /// 创建分页结果
    ///
    /// # 参数
    /// - `data`: 查询结果数据
    /// - `pagination_meta`: 分页元信息
    ///
    /// # 返回
    /// 分页结果
    pub fn create_paginated_result<T>(
        data: Vec<T>,
        pagination_meta: PaginationMeta,
    ) -> PaginatedResult<T> {
        PaginatedResult {
            data,
            pagination: pagination_meta,
        }
    }

    /// 创建游标分页元信息
    ///
    /// # 参数
    /// - `data_count`: 当前页数据数量
    /// - `limit`: 每页限制数
    /// - `next_cursor`: 下一页游标
    /// - `prev_cursor`: 上一页游标
    ///
    /// # 返回
    /// 游标分页元信息
    pub fn create_cursor_pagination_meta(
        data_count: u64,
        limit: u64,
        next_cursor: Option<String>,
        prev_cursor: Option<String>,
    ) -> CursorPaginationMeta {
        CursorPaginationMeta {
            has_next: data_count == limit, // 如果返回的数据等于限制数，可能还有下一页
            has_prev: prev_cursor.is_some(),
            next_cursor,
            prev_cursor,
            count: data_count,
            limit,
        }
    }

    /// 创建游标分页结果
    ///
    /// # 参数
    /// - `data`: 查询结果数据
    /// - `pagination_meta`: 游标分页元信息
    ///
    /// # 返回
    /// 游标分页结果
    pub fn create_cursor_paginated_result<T>(
        data: Vec<T>,
        pagination_meta: CursorPaginationMeta,
    ) -> CursorPaginatedResult<T> {
        CursorPaginatedResult {
            data,
            pagination: pagination_meta,
        }
    }
}

/// 分页配置
///
/// 全局分页配置，用于设置默认的分页行为。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationConfig {
    /// 默认每页大小
    pub default_page_size: u64,
    /// 最大每页大小
    pub max_page_size: u64,
    /// 是否启用深度分页保护
    pub enable_deep_pagination_protection: bool,
    /// 深度分页阈值（超过此页数将建议使用游标分页）
    pub deep_pagination_threshold: u64,
}

impl Default for PaginationConfig {
    fn default() -> Self {
        Self {
            default_page_size: 20,
            max_page_size: 100,
            enable_deep_pagination_protection: true,
            deep_pagination_threshold: 1000,
        }
    }
}

impl PaginationConfig {
    /// 创建新的分页配置
    ///
    /// # 参数
    /// - `default_page_size`: 默认每页大小
    /// - `max_page_size`: 最大每页大小
    ///
    /// # 返回
    /// 新的分页配置实例
    pub fn new(default_page_size: u64, max_page_size: u64) -> Self {
        Self {
            default_page_size,
            max_page_size,
            enable_deep_pagination_protection: true,
            deep_pagination_threshold: 1000,
        }
    }

    /// 启用深度分页保护
    ///
    /// # 参数
    /// - `threshold`: 深度分页阈值
    ///
    /// # 返回
    /// 更新后的分页配置
    pub fn with_deep_pagination_protection(mut self, threshold: u64) -> Self {
        self.enable_deep_pagination_protection = true;
        self.deep_pagination_threshold = threshold;
        self
    }

    /// 禁用深度分页保护
    ///
    /// # 返回
    /// 更新后的分页配置
    pub fn without_deep_pagination_protection(mut self) -> Self {
        self.enable_deep_pagination_protection = false;
        self
    }

    /// 验证分页参数是否符合配置
    ///
    /// # 参数
    /// - `pagination`: 分页参数
    ///
    /// # 返回
    /// - `Result<(), String>`: 验证成功返回Ok，失败返回错误信息
    pub fn validate(&self, pagination: &PaginationParams) -> Result<(), String> {
        // 基础验证
        PaginationUtils::validate_pagination(pagination)?;

        // 检查页面大小限制
        if pagination.page_size > self.max_page_size {
            return Err(format!("每页大小不能超过{}", self.max_page_size));
        }

        // 深度分页保护
        if self.enable_deep_pagination_protection {
            let estimated_offset = pagination.offset();
            if estimated_offset > self.deep_pagination_threshold * self.default_page_size {
                return Err(format!(
                    "分页深度过大（页码: {}），建议使用游标分页以获得更好的性能",
                    pagination.page
                ));
            }
        }

        Ok(())
    }
}
