//! # 仓储接口增强特征
//!
//! 定义通用的仓储操作抽象和增强功能，为所有仓储接口提供统一的基础能力。
//!
//! ## 设计原则
//!
//! ### 1. 通用性 (Genericity)
//! - 使用泛型定义通用的CRUD操作
//! - 支持不同的实体类型和主键类型
//! - 提供可扩展的查询接口
//!
//! ### 2. 类型安全 (Type Safety)
//! - 强类型约束确保编译期错误检查
//! - 使用关联类型定义实体和错误类型
//! - 避免运行时类型转换错误
//!
//! ### 3. 异步支持 (Async Support)
//! - 所有操作都是异步的，支持高并发
//! - 使用async_trait支持trait中的异步方法
//! - 兼容Tokio异步运行时
//!
//! ### 4. 错误处理 (Error Handling)
//! - 统一的错误类型和处理机制
//! - 领域特定的错误信息
//! - 支持错误链和上下文信息

use async_trait::async_trait;
use sea_orm::DbErr;
use serde::{Deserialize, Serialize};
use std::fmt::Debug;
use uuid::Uuid;

/// 通用仓储接口基础特征
///
/// 定义所有仓储接口的通用操作，包括基本的CRUD功能。
/// 使用泛型支持不同的实体类型，确保类型安全。
#[async_trait]
pub trait BaseRepositoryContract<T>: Send + Sync
where
    T: Send + Sync + Clone + Debug,
{
    /// 根据ID查询实体
    ///
    /// # 参数
    /// - `id`: 实体的唯一标识符
    ///
    /// # 返回
    /// - `Result<Option<T>, DbErr>`: 成功时返回实体（如果存在），失败时返回数据库错误
    async fn find_by_id(&self, id: Uuid) -> Result<Option<T>, DbErr>;

    /// 创建新实体
    ///
    /// # 参数
    /// - `entity`: 要创建的实体
    ///
    /// # 返回
    /// - `Result<T, DbErr>`: 成功时返回创建的实体，失败时返回数据库错误
    async fn create(&self, entity: T) -> Result<T, DbErr>;

    /// 更新实体
    ///
    /// # 参数
    /// - `entity`: 要更新的实体
    ///
    /// # 返回
    /// - `Result<T, DbErr>`: 成功时返回更新后的实体，失败时返回数据库错误
    async fn update(&self, entity: T) -> Result<T, DbErr>;

    /// 根据ID删除实体
    ///
    /// # 参数
    /// - `id`: 要删除的实体ID
    ///
    /// # 返回
    /// - `Result<bool, DbErr>`: 成功时返回是否删除了实体，失败时返回数据库错误
    async fn delete(&self, id: Uuid) -> Result<bool, DbErr>;

    /// 检查实体是否存在
    ///
    /// # 参数
    /// - `id`: 实体的唯一标识符
    ///
    /// # 返回
    /// - `Result<bool, DbErr>`: 成功时返回实体是否存在，失败时返回数据库错误
    async fn exists(&self, id: Uuid) -> Result<bool, DbErr>;
}

/// 分页查询支持特征
///
/// 为仓储接口提供分页查询能力，支持大数据集的高效查询。
#[async_trait]
pub trait PaginatedRepositoryContract<T>: Send + Sync
where
    T: Send + Sync + Clone + Debug,
{
    /// 分页查询所有实体
    ///
    /// # 参数
    /// - `pagination`: 分页参数
    ///
    /// # 返回
    /// - `Result<PaginatedResult<T>, DbErr>`: 成功时返回分页结果，失败时返回数据库错误
    async fn find_all_paginated(
        &self,
        pagination: PaginationParams,
    ) -> Result<PaginatedResult<T>, DbErr>;

    /// 根据条件分页查询实体
    ///
    /// # 参数
    /// - `filter`: 查询过滤条件
    /// - `pagination`: 分页参数
    ///
    /// # 返回
    /// - `Result<PaginatedResult<T>, DbErr>`: 成功时返回分页结果，失败时返回数据库错误
    async fn find_by_filter_paginated(
        &self,
        filter: QueryFilter,
        pagination: PaginationParams,
    ) -> Result<PaginatedResult<T>, DbErr>;
}

/// 批量操作支持特征
///
/// 为仓储接口提供批量操作能力，提高大量数据操作的性能。
#[async_trait]
pub trait BatchRepositoryContract<T>: Send + Sync
where
    T: Send + Sync + Clone + Debug,
{
    /// 批量创建实体
    ///
    /// # 参数
    /// - `entities`: 要创建的实体列表
    ///
    /// # 返回
    /// - `Result<Vec<T>, DbErr>`: 成功时返回创建的实体列表，失败时返回数据库错误
    async fn create_batch(&self, entities: Vec<T>) -> Result<Vec<T>, DbErr>;

    /// 批量更新实体
    ///
    /// # 参数
    /// - `entities`: 要更新的实体列表
    ///
    /// # 返回
    /// - `Result<Vec<T>, DbErr>`: 成功时返回更新后的实体列表，失败时返回数据库错误
    async fn update_batch(&self, entities: Vec<T>) -> Result<Vec<T>, DbErr>;

    /// 批量删除实体
    ///
    /// # 参数
    /// - `ids`: 要删除的实体ID列表
    ///
    /// # 返回
    /// - `Result<u64, DbErr>`: 成功时返回删除的实体数量，失败时返回数据库错误
    async fn delete_batch(&self, ids: Vec<Uuid>) -> Result<u64, DbErr>;
}

/// 统一分页参数
///
/// 提供标准化的分页查询参数，确保所有仓储接口的分页行为一致。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationParams {
    /// 页码（从1开始）
    pub page: u64,
    /// 每页大小（限制在1-100之间）
    pub page_size: u64,
    /// 排序字段
    pub sort_by: Option<String>,
    /// 排序方向（asc/desc）
    pub sort_order: Option<SortOrder>,
}

/// 排序方向枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SortOrder {
    /// 升序
    Asc,
    /// 降序
    Desc,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            page: 1,
            page_size: 20,
            sort_by: None,
            sort_order: Some(SortOrder::Desc),
        }
    }
}

impl PaginationParams {
    /// 创建新的分页参数
    ///
    /// # 参数
    /// - `page`: 页码（自动确保至少为1）
    /// - `page_size`: 每页大小（自动限制在1-100之间）
    ///
    /// # 返回
    /// 新的分页参数实例
    pub fn new(page: u64, page_size: u64) -> Self {
        Self {
            page: page.max(1),                  // 确保页码至少为1
            page_size: page_size.clamp(1, 100), // 限制页面大小在1-100之间
            sort_by: None,
            sort_order: Some(SortOrder::Desc),
        }
    }

    /// 设置排序参数
    ///
    /// # 参数
    /// - `sort_by`: 排序字段名
    /// - `sort_order`: 排序方向
    ///
    /// # 返回
    /// 更新后的分页参数实例
    pub fn with_sort(mut self, sort_by: String, sort_order: SortOrder) -> Self {
        self.sort_by = Some(sort_by);
        self.sort_order = Some(sort_order);
        self
    }

    /// 计算偏移量
    ///
    /// # 返回
    /// 查询偏移量
    pub fn offset(&self) -> u64 {
        (self.page - 1) * self.page_size
    }

    /// 获取限制数量
    ///
    /// # 返回
    /// 查询限制数量
    pub fn limit(&self) -> u64 {
        self.page_size
    }
}

/// 分页查询结果
///
/// 包含查询数据和分页元信息的统一结果结构。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginatedResult<T> {
    /// 查询结果数据
    pub data: Vec<T>,
    /// 分页元信息
    pub pagination: PaginationMeta,
}

/// 分页元信息
///
/// 包含分页查询的统计信息，用于前端分页组件显示。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationMeta {
    /// 当前页码
    pub current_page: u64,
    /// 每页大小
    pub page_size: u64,
    /// 总记录数
    pub total_count: u64,
    /// 总页数
    pub total_pages: u64,
    /// 是否有下一页
    pub has_next: bool,
    /// 是否有上一页
    pub has_prev: bool,
}

impl PaginationMeta {
    /// 创建分页元信息
    ///
    /// # 参数
    /// - `current_page`: 当前页码
    /// - `page_size`: 每页大小
    /// - `total_count`: 总记录数
    ///
    /// # 返回
    /// 分页元信息实例
    pub fn new(current_page: u64, page_size: u64, total_count: u64) -> Self {
        let total_pages = if total_count == 0 {
            1
        } else {
            total_count.div_ceil(page_size) // 向上取整
        };

        Self {
            current_page,
            page_size,
            total_count,
            total_pages,
            has_next: current_page < total_pages,
            has_prev: current_page > 1,
        }
    }
}

/// 通用查询过滤器
///
/// 提供灵活的查询条件构建能力，支持复杂的查询场景。
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QueryFilter {
    /// 字段过滤条件
    pub conditions: Vec<FilterCondition>,
    /// 逻辑操作符（AND/OR）
    pub operator: LogicalOperator,
}

/// 过滤条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FilterCondition {
    /// 字段名
    pub field: String,
    /// 操作符
    pub operator: ComparisonOperator,
    /// 值
    pub value: FilterValue,
}

/// 比较操作符
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ComparisonOperator {
    /// 等于
    Eq,
    /// 不等于
    Ne,
    /// 大于
    Gt,
    /// 大于等于
    Gte,
    /// 小于
    Lt,
    /// 小于等于
    Lte,
    /// 包含（LIKE）
    Contains,
    /// 开始于
    StartsWith,
    /// 结束于
    EndsWith,
    /// 在列表中
    In,
    /// 不在列表中
    NotIn,
    /// 为空
    IsNull,
    /// 不为空
    IsNotNull,
}

/// 逻辑操作符
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogicalOperator {
    /// 逻辑与
    And,
    /// 逻辑或
    Or,
}

/// 过滤值
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(untagged)]
pub enum FilterValue {
    /// 字符串值
    String(String),
    /// 整数值
    Integer(i64),
    /// 浮点数值
    Float(f64),
    /// 布尔值
    Boolean(bool),
    /// UUID值
    Uuid(Uuid),
    /// 字符串列表
    StringList(Vec<String>),
    /// 整数列表
    IntegerList(Vec<i64>),
    /// UUID列表
    UuidList(Vec<Uuid>),
}
