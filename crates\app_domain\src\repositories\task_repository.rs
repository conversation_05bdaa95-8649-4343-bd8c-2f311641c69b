//! # 任务仓库接口
//!
//! 定义任务数据访问的抽象接口

use crate::entities::Task;
use async_trait::async_trait;
use sea_orm::{DbErr, DeleteResult};
use uuid::Uuid;

/// 任务仓库的抽象 Trait。
///
/// 定义了任务仓库必须实现的所有功能协定。
/// 注意：在 Axum 0.8.4 升级中，此 trait 保留 `#[async_trait]` 宏，
/// 因为项目中大量使用 `Arc<dyn TaskRepositoryContract>` 需要 dyn compatible trait。
/// 原生异步 trait 语法不支持 dyn compatibility，所以继续使用 async_trait。
/// `Send + Sync` 约束是让它能在多线程环境下安全地共享。
#[async_trait]
pub trait TaskRepositoryContract: Send + Sync {
    /// 查询所有任务。
    async fn find_all(&self) -> Result<Vec<Task>, DbErr>;

    /// 根据用户ID查询所有任务。
    async fn find_all_by_user(&self, user_id: Uuid) -> Result<Vec<Task>, DbErr>;

    /// 根据 UUID 查询单个任务。
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Task>, DbErr>;

    /// 根据用户ID查询任务列表。
    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Vec<Task>, DbErr>;

    /// 创建一个新任务。
    async fn create(&self, task: Task) -> Result<Task, DbErr>;

    /// 更新一个现有任务。
    async fn update(&self, task: Task) -> Result<Task, DbErr>;

    /// 删除一个任务。
    async fn delete(&self, id: Uuid) -> Result<DeleteResult, DbErr>;
}
