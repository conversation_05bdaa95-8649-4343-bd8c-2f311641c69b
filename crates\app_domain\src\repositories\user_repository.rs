//! # 用户仓库接口
//!
//! 定义用户数据访问的抽象接口

use crate::entities::User;
use async_trait::async_trait;
use sea_orm::DbErr;
use uuid::Uuid;

/// 用户仓库的抽象 Trait。
///
/// 定义了用户仓库必须实现的所有功能协定。
/// 注意：在 Axum 0.8.4 升级中，此 trait 保留 `#[async_trait]` 宏，
/// 因为项目中大量使用 `Arc<dyn UserRepositoryContract>` 需要 dyn compatible trait。
/// 原生异步 trait 语法不支持 dyn compatibility，所以继续使用 async_trait。
/// `Send + Sync` 约束是让它能在多线程环境下安全地共享。
#[async_trait]
pub trait UserRepositoryContract: Send + Sync {
    /// 根据用户名查询用户。
    /// 这个方法主要用于登录验证，需要获取用户的密码哈希进行比较。
    async fn find_by_username(&self, username: &str) -> Result<Option<User>, DbErr>;

    /// 根据用户ID查询用户。
    /// 这个方法用于获取用户详细信息。
    async fn find_by_id(&self, id: Uuid) -> Result<Option<User>, DbErr>;

    /// 创建一个新用户。
    /// 这个方法用于用户注册，将新用户信息保存到数据库。
    async fn create(&self, user: User) -> Result<User, DbErr>;

    /// 更新用户信息。
    /// 这个方法用于更新现有用户的信息。
    async fn update(&self, user: User) -> Result<User, DbErr>;

    /// 删除用户。
    /// 这个方法用于删除指定的用户。
    async fn delete(&self, user_id: Uuid) -> Result<u64, DbErr>;

    /// 获取用户总数。
    /// 这个方法用于统计和健康检查。
    async fn count_all(&self) -> Result<u64, DbErr>;
}
