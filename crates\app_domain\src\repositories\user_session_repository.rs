//! # 用户会话仓库接口
//!
//! 定义用户会话数据访问的抽象接口，遵循依赖倒置原则

use crate::entities::user_session::UserSession;
use crate::entities::{CreateUserSessionRequest, UpdateUserSessionRequest};
use app_common::error::Result as AppResult;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use uuid::Uuid;

/// 用户会话仓库接口契约
///
/// 定义用户会话数据访问的标准接口，由基础设施层实现
#[async_trait]
pub trait UserSessionRepositoryContract: Send + Sync {
    /// 创建新的用户会话
    ///
    /// # 参数
    /// - `request`: 创建用户会话的请求数据
    ///
    /// # 返回
    /// - `AppResult<UserSession>`: 成功返回创建的会话实体，失败返回错误
    async fn create(&self, request: CreateUserSessionRequest) -> AppResult<UserSession>;

    /// 根据ID查找用户会话
    ///
    /// # 参数
    /// - `id`: 会话ID
    ///
    /// # 返回
    /// - `AppResult<Option<UserSession>>`: 成功返回会话实体（如果存在），失败返回错误
    async fn find_by_id(&self, id: &Uuid) -> AppResult<Option<UserSession>>;

    /// 根据会话令牌查找用户会话
    ///
    /// # 参数
    /// - `session_token`: 会话令牌
    ///
    /// # 返回
    /// - `AppResult<Option<UserSession>>`: 成功返回会话实体（如果存在），失败返回错误
    async fn find_by_session_token(&self, session_token: &str) -> AppResult<Option<UserSession>>;

    /// 根据用户ID查找所有活跃会话
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `AppResult<Vec<UserSession>>`: 成功返回会话列表，失败返回错误
    async fn find_active_sessions_by_user_id(&self, user_id: &Uuid) -> AppResult<Vec<UserSession>>;

    /// 根据聊天室ID查找所有在线会话
    ///
    /// # 参数
    /// - `chat_room_id`: 聊天室ID
    ///
    /// # 返回
    /// - `AppResult<Vec<UserSession>>`: 成功返回会话列表，失败返回错误
    async fn find_online_sessions_by_chat_room_id(
        &self,
        chat_room_id: &Uuid,
    ) -> AppResult<Vec<UserSession>>;

    /// 获取所有在线会话
    ///
    /// # 返回
    /// - `AppResult<Vec<UserSession>>`: 成功返回在线会话列表，失败返回错误
    async fn find_all_online_sessions(&self) -> AppResult<Vec<UserSession>>;

    /// 更新用户会话
    ///
    /// # 参数
    /// - `id`: 会话ID
    /// - `request`: 更新用户会话的请求数据
    ///
    /// # 返回
    /// - `AppResult<UserSession>`: 成功返回更新后的会话实体，失败返回错误
    async fn update(&self, id: &Uuid, request: UpdateUserSessionRequest) -> AppResult<UserSession>;

    /// 更新会话的最后活跃时间
    ///
    /// # 参数
    /// - `id`: 会话ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn update_activity(&self, id: &Uuid) -> AppResult<()>;

    /// 更新会话的心跳时间
    ///
    /// # 参数
    /// - `id`: 会话ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn update_heartbeat(&self, id: &Uuid) -> AppResult<()>;

    /// 设置会话状态为在线
    ///
    /// # 参数
    /// - `id`: 会话ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn set_online(&self, id: &Uuid) -> AppResult<()>;

    /// 设置会话状态为离线
    ///
    /// # 参数
    /// - `id`: 会话ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn set_offline(&self, id: &Uuid) -> AppResult<()>;

    /// 设置用户的所有会话为离线状态
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn set_user_sessions_offline(&self, user_id: &Uuid) -> AppResult<()>;

    /// 加入聊天室
    ///
    /// # 参数
    /// - `session_id`: 会话ID
    /// - `chat_room_id`: 聊天室ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn join_chat_room(&self, session_id: &Uuid, chat_room_id: &Uuid) -> AppResult<()>;

    /// 离开聊天室
    ///
    /// # 参数
    /// - `session_id`: 会话ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn leave_chat_room(&self, session_id: &Uuid) -> AppResult<()>;

    /// 删除用户会话
    ///
    /// # 参数
    /// - `id`: 会话ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn delete(&self, id: &Uuid) -> AppResult<()>;

    /// 删除用户的所有会话
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn delete_user_sessions(&self, user_id: &Uuid) -> AppResult<()>;

    /// 清理过期会话
    ///
    /// # 参数
    /// - `before`: 清理此时间之前的会话
    ///
    /// # 返回
    /// - `AppResult<u64>`: 成功返回清理的会话数量，失败返回错误
    async fn cleanup_expired_sessions(&self, before: DateTime<Utc>) -> AppResult<u64>;

    /// 清理长时间未活跃的会话
    ///
    /// # 参数
    /// - `timeout_seconds`: 超时时间（秒）
    ///
    /// # 返回
    /// - `AppResult<u64>`: 成功返回清理的会话数量，失败返回错误
    async fn cleanup_inactive_sessions(&self, timeout_seconds: i64) -> AppResult<u64>;

    /// 获取在线用户数量
    ///
    /// # 返回
    /// - `AppResult<i64>`: 成功返回在线用户数量，失败返回错误
    async fn count_online_users(&self) -> AppResult<i64>;

    /// 获取指定聊天室的在线用户数量
    ///
    /// # 参数
    /// - `chat_room_id`: 聊天室ID
    ///
    /// # 返回
    /// - `AppResult<i64>`: 成功返回在线用户数量，失败返回错误
    async fn count_online_users_in_chat_room(&self, chat_room_id: &Uuid) -> AppResult<i64>;

    /// 检查用户是否在线
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `AppResult<bool>`: 成功返回是否在线，失败返回错误
    async fn is_user_online(&self, user_id: &Uuid) -> AppResult<bool>;

    /// 检查用户是否在指定聊天室中
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    /// - `chat_room_id`: 聊天室ID
    ///
    /// # 返回
    /// - `AppResult<bool>`: 成功返回是否在聊天室中，失败返回错误
    async fn is_user_in_chat_room(&self, user_id: &Uuid, chat_room_id: &Uuid) -> AppResult<bool>;
}
