//! # 认证领域服务接口
//!
//! 定义认证相关的核心业务规则和操作接口

use crate::entities::user::User;
use app_common::error::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 认证领域服务接口
///
/// 定义认证相关的核心业务操作，包括：
/// - 用户注册和登录
/// - 密码验证和管理
/// - JWT令牌生成和验证
/// - 会话管理
///
/// 设计原则：
/// - 专注于认证业务逻辑，不涉及具体的数据存储实现
/// - 使用async-trait支持异步操作
/// - 提供清晰的错误处理机制
/// - 遵循单一职责原则
#[async_trait]
pub trait AuthDomainService: Send + Sync {
    /// 用户注册
    ///
    /// # 参数
    /// - `registration`: 注册请求信息
    ///
    /// # 返回
    /// - `Ok(user)`: 注册成功，返回创建的用户
    /// - `Err(...)`: 注册失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户名必须唯一
    /// - 密码必须符合强度要求
    /// - 注册信息必须通过验证
    async fn register(&self, registration: UserRegistration) -> Result<User>;

    /// 用户登录
    ///
    /// # 参数
    /// - `credentials`: 登录凭据
    ///
    /// # 返回
    /// - `Ok(auth_result)`: 登录成功，返回认证结果
    /// - `Err(...)`: 登录失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户名和密码必须正确
    /// - 用户账户必须处于活跃状态
    async fn login(&self, credentials: UserCredentials) -> Result<AuthenticationResult>;

    /// 刷新访问令牌
    ///
    /// # 参数
    /// - `refresh_token`: 刷新令牌
    ///
    /// # 返回
    /// - `Ok(auth_result)`: 刷新成功，返回新的认证结果
    /// - `Err(...)`: 刷新失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 刷新令牌必须有效且未过期
    /// - 用户账户必须仍然活跃
    async fn refresh_token(&self, refresh_token: &str) -> Result<AuthenticationResult>;

    /// 验证访问令牌
    ///
    /// # 参数
    /// - `access_token`: 访问令牌
    ///
    /// # 返回
    /// - `Ok(claims)`: 验证成功，返回令牌声明
    /// - `Err(...)`: 验证失败，包含具体错误信息
    async fn verify_token(&self, access_token: &str) -> Result<TokenClaims>;

    /// 用户登出
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    /// - `token_id`: 令牌ID（可选）
    ///
    /// # 返回
    /// - `Ok(())`: 登出成功
    /// - `Err(...)`: 登出失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 使相关的访问令牌和刷新令牌失效
    async fn logout(&self, user_id: Uuid, token_id: Option<String>) -> Result<()>;

    /// 更改密码
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    /// - `old_password`: 旧密码
    /// - `new_password`: 新密码
    ///
    /// # 返回
    /// - `Ok(())`: 更改成功
    /// - `Err(...)`: 更改失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 旧密码必须正确
    /// - 新密码必须符合强度要求
    /// - 新密码不能与旧密码相同
    async fn change_password(
        &self,
        user_id: Uuid,
        old_password: &str,
        new_password: &str,
    ) -> Result<()>;

    /// 重置密码
    ///
    /// # 参数
    /// - `username`: 用户名
    ///
    /// # 返回
    /// - `Ok(reset_token)`: 重置成功，返回重置令牌
    /// - `Err(...)`: 重置失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户必须存在
    /// - 生成临时重置令牌
    async fn reset_password(&self, username: &str) -> Result<String>;

    /// 确认密码重置
    ///
    /// # 参数
    /// - `reset_token`: 重置令牌
    /// - `new_password`: 新密码
    ///
    /// # 返回
    /// - `Ok(())`: 重置成功
    /// - `Err(...)`: 重置失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 重置令牌必须有效且未过期
    /// - 新密码必须符合强度要求
    async fn confirm_password_reset(&self, reset_token: &str, new_password: &str) -> Result<()>;

    /// 验证密码强度
    ///
    /// # 参数
    /// - `password`: 要验证的密码
    ///
    /// # 返回
    /// - `Ok(())`: 密码强度符合要求
    /// - `Err(...)`: 密码强度不足
    async fn validate_password_strength(&self, password: &str) -> Result<()>;

    /// 哈希密码
    ///
    /// # 参数
    /// - `password`: 明文密码
    ///
    /// # 返回
    /// - `Ok(hash)`: 哈希成功，返回密码哈希
    /// - `Err(...)`: 哈希失败，包含具体错误信息
    async fn hash_password(&self, password: &str) -> Result<String>;

    /// 验证密码
    ///
    /// # 参数
    /// - `password`: 明文密码
    /// - `hash`: 密码哈希
    ///
    /// # 返回
    /// - `Ok(true)`: 密码正确
    /// - `Ok(false)`: 密码错误
    /// - `Err(...)`: 验证过程中发生错误
    async fn verify_password(&self, password: &str, hash: &str) -> Result<bool>;
}

/// 用户注册请求
#[derive(Debug, Clone, Serialize, Deserialize, validator::Validate)]
pub struct UserRegistration {
    /// 用户名
    #[validate(length(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间"))]
    pub username: String,

    /// 密码
    #[validate(length(min = 8, max = 128, message = "密码长度必须在8-128个字符之间"))]
    pub password: String,

    /// 确认密码
    pub password_confirmation: String,
}

impl UserRegistration {
    /// 验证密码确认
    pub fn validate_password_confirmation(&self) -> Result<()> {
        if self.password != self.password_confirmation {
            return Err(app_common::error::AppError::ValidationError(
                "密码确认不匹配".to_string(),
            ));
        }
        Ok(())
    }
}

/// 用户登录凭据
#[derive(Debug, Clone, Serialize, Deserialize, validator::Validate)]
pub struct UserCredentials {
    /// 用户名
    #[validate(length(min = 1, message = "用户名不能为空"))]
    pub username: String,

    /// 密码
    #[validate(length(min = 1, message = "密码不能为空"))]
    pub password: String,
}

/// 认证结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticationResult {
    /// 访问令牌
    pub access_token: String,

    /// 刷新令牌
    pub refresh_token: String,

    /// 令牌类型
    pub token_type: String,

    /// 访问令牌过期时间（秒）
    pub expires_in: u64,

    /// 用户信息
    pub user: UserInfo,
}

/// 用户信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    /// 用户ID
    pub id: Uuid,

    /// 用户名
    pub username: String,

    /// 创建时间
    pub created_at: DateTime<Utc>,
}

impl From<User> for UserInfo {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            username: user.username,
            created_at: user.created_at,
        }
    }
}

/// JWT令牌声明
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenClaims {
    /// 主题（用户ID）
    pub sub: String,

    /// 用户名
    pub username: String,

    /// 签发时间
    pub iat: u64,

    /// 过期时间
    pub exp: u64,

    /// 令牌ID
    pub jti: String,
}

/// 认证业务规则验证器
///
/// 提供认证相关的业务规则验证功能
pub struct AuthBusinessRules;

impl AuthBusinessRules {
    /// 验证用户名格式
    pub fn validate_username(username: &str) -> Result<()> {
        use regex::Regex;

        if username.len() < 3 || username.len() > 50 {
            return Err(app_common::error::AppError::ValidationError(
                "用户名长度必须在3-50个字符之间".to_string(),
            ));
        }

        let username_regex = Regex::new(r"^[a-zA-Z0-9_-]+$").unwrap();
        if !username_regex.is_match(username) {
            return Err(app_common::error::AppError::ValidationError(
                "用户名只能包含字母、数字、下划线和连字符".to_string(),
            ));
        }

        Ok(())
    }

    /// 验证密码强度
    pub fn validate_password_strength(password: &str) -> Result<()> {
        if password.len() < 8 {
            return Err(app_common::error::AppError::ValidationError(
                "密码长度至少为8个字符".to_string(),
            ));
        }

        if password.len() > 128 {
            return Err(app_common::error::AppError::ValidationError(
                "密码长度不能超过128个字符".to_string(),
            ));
        }

        let has_letter = password.chars().any(|c| c.is_alphabetic());
        let has_digit = password.chars().any(|c| c.is_numeric());

        if !has_letter || !has_digit {
            return Err(app_common::error::AppError::ValidationError(
                "密码必须包含至少一个字母和一个数字".to_string(),
            ));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_registration_password_confirmation() {
        let valid_registration = UserRegistration {
            username: "testuser".to_string(),
            password: "password123".to_string(),
            password_confirmation: "password123".to_string(),
        };
        assert!(valid_registration.validate_password_confirmation().is_ok());

        let invalid_registration = UserRegistration {
            username: "testuser".to_string(),
            password: "password123".to_string(),
            password_confirmation: "different".to_string(),
        };
        assert!(
            invalid_registration
                .validate_password_confirmation()
                .is_err()
        );
    }

    #[test]
    fn test_validate_username() {
        // 测试有效用户名
        assert!(AuthBusinessRules::validate_username("valid_user").is_ok());
        assert!(AuthBusinessRules::validate_username("user123").is_ok());
        assert!(AuthBusinessRules::validate_username("test-user").is_ok());

        // 测试无效用户名
        assert!(AuthBusinessRules::validate_username("ab").is_err()); // 太短
        assert!(AuthBusinessRules::validate_username(&"a".repeat(51)).is_err()); // 太长
        assert!(AuthBusinessRules::validate_username("user@name").is_err()); // 包含非法字符
    }

    #[test]
    fn test_validate_password_strength() {
        // 测试有效密码
        assert!(AuthBusinessRules::validate_password_strength("password123").is_ok());
        assert!(AuthBusinessRules::validate_password_strength("MySecure1").is_ok());

        // 测试无效密码
        assert!(AuthBusinessRules::validate_password_strength("short1").is_err()); // 太短
        assert!(AuthBusinessRules::validate_password_strength("password").is_err()); // 无数字
        assert!(AuthBusinessRules::validate_password_strength("12345678").is_err()); // 无字母
    }
}
