//! # 聊天领域服务接口
//!
//! 定义聊天相关的核心业务规则和操作接口

use crate::entities::{chat_room::ChatRoom, message::Message, user_session::UserSession};
use app_common::error::Result;
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use uuid::Uuid;

/// 聊天领域服务接口
///
/// 定义聊天相关的核心业务操作，包括：
/// - 聊天室管理
/// - 消息发送和接收
/// - 用户会话管理
/// - 聊天权限控制
///
/// 设计原则：
/// - 专注于聊天业务逻辑，不涉及具体的数据存储实现
/// - 使用async-trait支持异步操作
/// - 提供清晰的错误处理机制
/// - 遵循单一职责原则
#[async_trait]
pub trait ChatDomainService: Send + Sync {
    /// 创建聊天室
    ///
    /// # 参数
    /// - `chat_room`: 要创建的聊天室实体
    /// - `creator_id`: 创建者用户ID
    ///
    /// # 返回
    /// - `Ok(chat_room)`: 创建成功，返回创建的聊天室
    /// - `Err(...)`: 创建失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 聊天室名称必须唯一
    /// - 创建者自动成为聊天室管理员
    /// - 聊天室信息必须通过验证
    async fn create_chat_room(&self, chat_room: ChatRoom, creator_id: Uuid) -> Result<ChatRoom>;

    /// 加入聊天室
    ///
    /// # 参数
    /// - `room_id`: 聊天室ID
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Ok(())`: 加入成功
    /// - `Err(...)`: 加入失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 聊天室必须存在且处于活跃状态
    /// - 用户不能重复加入同一聊天室
    /// - 聊天室未达到最大成员数限制
    async fn join_chat_room(&self, room_id: Uuid, user_id: Uuid) -> Result<()>;

    /// 离开聊天室
    ///
    /// # 参数
    /// - `room_id`: 聊天室ID
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Ok(())`: 离开成功
    /// - `Err(...)`: 离开失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户必须在聊天室中
    /// - 如果是最后一个成员，聊天室可能被删除或禁用
    async fn leave_chat_room(&self, room_id: Uuid, user_id: Uuid) -> Result<()>;

    /// 发送消息
    ///
    /// # 参数
    /// - `message`: 要发送的消息实体
    /// - `sender_id`: 发送者用户ID
    ///
    /// # 返回
    /// - `Ok(message)`: 发送成功，返回发送的消息
    /// - `Err(...)`: 发送失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 发送者必须在聊天室中
    /// - 消息内容必须通过验证
    /// - 聊天室必须处于活跃状态
    async fn send_message(&self, message: Message, sender_id: Uuid) -> Result<Message>;

    /// 获取聊天室消息历史
    ///
    /// # 参数
    /// - `room_id`: 聊天室ID
    /// - `user_id`: 请求用户ID
    /// - `limit`: 消息数量限制
    /// - `before`: 获取此时间之前的消息（可选）
    ///
    /// # 返回
    /// - `Ok(messages)`: 获取成功，返回消息列表
    /// - `Err(...)`: 获取失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户必须在聊天室中或有权限查看历史消息
    /// - 按时间倒序返回消息
    async fn get_message_history(
        &self,
        room_id: Uuid,
        user_id: Uuid,
        limit: u32,
        before: Option<DateTime<Utc>>,
    ) -> Result<Vec<Message>>;

    /// 获取聊天室在线用户列表
    ///
    /// # 参数
    /// - `room_id`: 聊天室ID
    /// - `user_id`: 请求用户ID
    ///
    /// # 返回
    /// - `Ok(sessions)`: 获取成功，返回在线用户会话列表
    /// - `Err(...)`: 获取失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户必须在聊天室中
    /// - 只返回活跃的用户会话
    async fn get_online_users(&self, room_id: Uuid, user_id: Uuid) -> Result<Vec<UserSession>>;

    /// 创建用户会话
    ///
    /// # 参数
    /// - `session`: 要创建的用户会话实体
    ///
    /// # 返回
    /// - `Ok(session)`: 创建成功，返回创建的会话
    /// - `Err(...)`: 创建失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户不能有多个活跃会话
    /// - 会话信息必须通过验证
    async fn create_user_session(&self, session: UserSession) -> Result<UserSession>;

    /// 更新用户会话状态
    ///
    /// # 参数
    /// - `session_id`: 会话ID
    /// - `is_active`: 是否活跃
    ///
    /// # 返回
    /// - `Ok(())`: 更新成功
    /// - `Err(...)`: 更新失败，包含具体错误信息
    async fn update_session_status(&self, session_id: Uuid, is_active: bool) -> Result<()>;

    /// 删除用户会话
    ///
    /// # 参数
    /// - `session_id`: 会话ID
    ///
    /// # 返回
    /// - `Ok(())`: 删除成功
    /// - `Err(...)`: 删除失败，包含具体错误信息
    async fn delete_user_session(&self, session_id: Uuid) -> Result<()>;

    /// 检查用户是否在聊天室中
    ///
    /// # 参数
    /// - `room_id`: 聊天室ID
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Ok(true)`: 用户在聊天室中
    /// - `Ok(false)`: 用户不在聊天室中
    /// - `Err(...)`: 检查过程中发生错误
    async fn is_user_in_room(&self, room_id: Uuid, user_id: Uuid) -> Result<bool>;

    /// 获取聊天室信息
    ///
    /// # 参数
    /// - `room_id`: 聊天室ID
    /// - `user_id`: 请求用户ID
    ///
    /// # 返回
    /// - `Ok(Some(room))`: 找到聊天室
    /// - `Ok(None)`: 聊天室不存在或无权限访问
    /// - `Err(...)`: 查询过程中发生错误
    async fn get_chat_room(&self, room_id: Uuid, user_id: Uuid) -> Result<Option<ChatRoom>>;
}

/// 聊天业务规则验证器
///
/// 提供聊天相关的业务规则验证功能
pub struct ChatBusinessRules;

impl ChatBusinessRules {
    /// 验证聊天室名称
    ///
    /// # 参数
    /// - `name`: 聊天室名称
    ///
    /// # 返回
    /// - `Ok(())`: 名称有效
    /// - `Err(...)`: 名称无效
    pub fn validate_room_name(name: &str) -> Result<()> {
        if name.trim().is_empty() {
            return Err(app_common::error::AppError::ValidationError(
                "聊天室名称不能为空".to_string(),
            ));
        }

        if name.len() > 100 {
            return Err(app_common::error::AppError::ValidationError(
                "聊天室名称长度不能超过100个字符".to_string(),
            ));
        }

        Ok(())
    }

    /// 验证消息内容
    ///
    /// # 参数
    /// - `content`: 消息内容
    ///
    /// # 返回
    /// - `Ok(())`: 内容有效
    /// - `Err(...)`: 内容无效
    pub fn validate_message_content(content: &str) -> Result<()> {
        if content.trim().is_empty() {
            return Err(app_common::error::AppError::ValidationError(
                "消息内容不能为空".to_string(),
            ));
        }

        if content.len() > 2000 {
            return Err(app_common::error::AppError::ValidationError(
                "消息内容长度不能超过2000个字符".to_string(),
            ));
        }

        Ok(())
    }

    /// 验证聊天室成员数量限制
    ///
    /// # 参数
    /// - `current_members`: 当前成员数
    /// - `max_members`: 最大成员数
    ///
    /// # 返回
    /// - `Ok(())`: 可以加入
    /// - `Err(...)`: 不能加入
    pub fn validate_member_limit(current_members: u32, max_members: u32) -> Result<()> {
        if max_members > 0 && current_members >= max_members {
            return Err(app_common::error::AppError::ValidationError(
                "聊天室已达到最大成员数量限制".to_string(),
            ));
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_room_name() {
        // 测试有效名称
        assert!(ChatBusinessRules::validate_room_name("有效的聊天室").is_ok());
        assert!(ChatBusinessRules::validate_room_name("General Chat").is_ok());

        // 测试无效名称
        assert!(ChatBusinessRules::validate_room_name("").is_err()); // 空名称
        assert!(ChatBusinessRules::validate_room_name("   ").is_err()); // 只有空格
        assert!(ChatBusinessRules::validate_room_name(&"a".repeat(101)).is_err()); // 太长
    }

    #[test]
    fn test_validate_message_content() {
        // 测试有效内容
        assert!(ChatBusinessRules::validate_message_content("Hello, world!").is_ok());

        // 测试无效内容
        assert!(ChatBusinessRules::validate_message_content("").is_err()); // 空内容
        assert!(ChatBusinessRules::validate_message_content("   ").is_err()); // 只有空格
        assert!(ChatBusinessRules::validate_message_content(&"a".repeat(2001)).is_err()); // 太长
    }

    #[test]
    fn test_validate_member_limit() {
        // 测试有效情况
        assert!(ChatBusinessRules::validate_member_limit(5, 10).is_ok());
        assert!(ChatBusinessRules::validate_member_limit(0, 0).is_ok()); // 无限制

        // 测试无效情况
        assert!(ChatBusinessRules::validate_member_limit(10, 10).is_err()); // 已满
        assert!(ChatBusinessRules::validate_member_limit(15, 10).is_err()); // 超出限制
    }
}
