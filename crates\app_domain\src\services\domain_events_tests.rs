//! # 领域事件处理机制测试模块
//!
//! 包含领域事件定义、发布器和处理器的单元测试

use super::*;
use crate::entities::*;
use crate::repositories::*;
use app_common::error::AppError;
use async_trait::async_trait;
use chrono::Utc;
use std::sync::Arc;
use uuid::Uuid;

// ============================================================================
// 领域事件相关定义
// ============================================================================

/// 领域事件基础trait
#[async_trait]
pub trait DomainEvent: Send + Sync + std::fmt::Debug {
    /// 事件类型
    fn event_type(&self) -> &'static str;

    /// 事件发生时间
    fn occurred_at(&self) -> chrono::DateTime<chrono::Utc>;

    /// 聚合根ID
    fn aggregate_id(&self) -> Uuid;
}

/// 用户创建事件
#[derive(Debug, Clone)]
pub struct UserCreatedEvent {
    pub user_id: Uuid,
    pub username: String,
    pub email: String,
    pub occurred_at: chrono::DateTime<chrono::Utc>,
}

#[async_trait]
impl DomainEvent for UserCreatedEvent {
    fn event_type(&self) -> &'static str {
        "UserCreated"
    }

    fn occurred_at(&self) -> chrono::DateTime<chrono::Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.user_id
    }
}

/// 任务状态变更事件
#[derive(Debug, Clone)]
pub struct TaskStatusChangedEvent {
    pub task_id: Uuid,
    pub user_id: Uuid,
    pub old_status: bool,
    pub new_status: bool,
    pub occurred_at: chrono::DateTime<chrono::Utc>,
}

#[async_trait]
impl DomainEvent for TaskStatusChangedEvent {
    fn event_type(&self) -> &'static str {
        "TaskStatusChanged"
    }

    fn occurred_at(&self) -> chrono::DateTime<chrono::Utc> {
        self.occurred_at
    }

    fn aggregate_id(&self) -> Uuid {
        self.task_id
    }
}

/// 领域事件发布器接口
#[async_trait]
pub trait DomainEventPublisher: Send + Sync {
    /// 发布单个事件
    async fn publish(&self, event: Box<dyn DomainEvent>) -> Result<(), AppError>;

    /// 批量发布事件
    async fn publish_batch(&self, events: Vec<Box<dyn DomainEvent>>) -> Result<(), AppError>;
}

/// 内存中的事件发布器（用于测试）
pub struct InMemoryEventPublisher {
    pub events: std::sync::Mutex<Vec<Box<dyn DomainEvent>>>,
}

impl Default for InMemoryEventPublisher {
    fn default() -> Self {
        Self::new()
    }
}

impl InMemoryEventPublisher {
    pub fn new() -> Self {
        Self {
            events: std::sync::Mutex::new(Vec::new()),
        }
    }

    pub fn get_events(&self) -> Vec<String> {
        let events = self.events.lock().unwrap();
        events.iter().map(|e| e.event_type().to_string()).collect()
    }

    pub fn clear(&self) {
        let mut events = self.events.lock().unwrap();
        events.clear();
    }
}

#[async_trait]
impl DomainEventPublisher for InMemoryEventPublisher {
    async fn publish(&self, event: Box<dyn DomainEvent>) -> Result<(), AppError> {
        let mut events = self.events.lock().unwrap();
        events.push(event);
        Ok(())
    }

    async fn publish_batch(
        &self,
        mut new_events: Vec<Box<dyn DomainEvent>>,
    ) -> Result<(), AppError> {
        let mut events = self.events.lock().unwrap();
        events.append(&mut new_events);
        Ok(())
    }
}

// ============================================================================
// 模拟仓储实现（用于测试）
// ============================================================================

/// 模拟用户仓储实现（用于测试）
pub struct MockUserRepository {
    pub users: std::sync::Mutex<Vec<User>>,
}

impl Default for MockUserRepository {
    fn default() -> Self {
        Self::new()
    }
}

impl MockUserRepository {
    pub fn new() -> Self {
        Self {
            users: std::sync::Mutex::new(Vec::new()),
        }
    }

    pub fn with_users(users: Vec<User>) -> Self {
        Self {
            users: std::sync::Mutex::new(users),
        }
    }
}

#[async_trait]
impl UserRepositoryContract for MockUserRepository {
    async fn create(&self, user: User) -> Result<User, sea_orm::DbErr> {
        let mut users = self.users.lock().unwrap();
        users.push(user.clone());
        Ok(user)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<User>, sea_orm::DbErr> {
        let users = self.users.lock().unwrap();
        Ok(users.iter().find(|u| u.id == id).cloned())
    }

    async fn find_by_username(&self, username: &str) -> Result<Option<User>, sea_orm::DbErr> {
        let users = self.users.lock().unwrap();
        Ok(users.iter().find(|u| u.username == username).cloned())
    }

    async fn update(&self, user: User) -> Result<User, sea_orm::DbErr> {
        let mut users = self.users.lock().unwrap();
        if let Some(existing) = users.iter_mut().find(|u| u.id == user.id) {
            *existing = user.clone();
            Ok(user)
        } else {
            Err(sea_orm::DbErr::RecordNotFound("用户不存在".to_string()))
        }
    }

    async fn delete(&self, id: Uuid) -> Result<u64, sea_orm::DbErr> {
        let mut users = self.users.lock().unwrap();
        if let Some(pos) = users.iter().position(|u| u.id == id) {
            users.remove(pos);
            Ok(1)
        } else {
            Err(sea_orm::DbErr::RecordNotFound("用户不存在".to_string()))
        }
    }

    async fn count_all(&self) -> Result<u64, sea_orm::DbErr> {
        let users = self.users.lock().unwrap();
        Ok(users.len() as u64)
    }
}

/// 模拟任务仓储实现（用于测试）
pub struct MockTaskRepository {
    pub tasks: std::sync::Mutex<Vec<Task>>,
}

impl Default for MockTaskRepository {
    fn default() -> Self {
        Self::new()
    }
}

impl MockTaskRepository {
    pub fn new() -> Self {
        Self {
            tasks: std::sync::Mutex::new(Vec::new()),
        }
    }

    pub fn with_tasks(tasks: Vec<Task>) -> Self {
        Self {
            tasks: std::sync::Mutex::new(tasks),
        }
    }
}

#[async_trait]
impl TaskRepositoryContract for MockTaskRepository {
    async fn create(&self, task: Task) -> Result<Task, sea_orm::DbErr> {
        let mut tasks = self.tasks.lock().unwrap();
        tasks.push(task.clone());
        Ok(task)
    }

    async fn find_by_id(&self, id: Uuid) -> Result<Option<Task>, sea_orm::DbErr> {
        let tasks = self.tasks.lock().unwrap();
        Ok(tasks.iter().find(|t| t.id == id).cloned())
    }

    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Vec<Task>, sea_orm::DbErr> {
        let tasks = self.tasks.lock().unwrap();
        Ok(tasks
            .iter()
            .filter(|t| t.user_id == Some(user_id))
            .cloned()
            .collect())
    }

    async fn update(&self, task: Task) -> Result<Task, sea_orm::DbErr> {
        let mut tasks = self.tasks.lock().unwrap();
        if let Some(existing) = tasks.iter_mut().find(|t| t.id == task.id) {
            *existing = task.clone();
            Ok(task)
        } else {
            Err(sea_orm::DbErr::RecordNotFound("任务不存在".to_string()))
        }
    }

    async fn delete(&self, id: Uuid) -> Result<sea_orm::DeleteResult, sea_orm::DbErr> {
        let mut tasks = self.tasks.lock().unwrap();
        if let Some(pos) = tasks.iter().position(|t| t.id == id) {
            tasks.remove(pos);
            Ok(sea_orm::DeleteResult { rows_affected: 1 })
        } else {
            Err(sea_orm::DbErr::RecordNotFound("任务不存在".to_string()))
        }
    }

    async fn find_all(&self) -> Result<Vec<Task>, sea_orm::DbErr> {
        let tasks = self.tasks.lock().unwrap();
        Ok(tasks.clone())
    }

    async fn find_all_by_user(&self, user_id: Uuid) -> Result<Vec<Task>, sea_orm::DbErr> {
        let tasks = self.tasks.lock().unwrap();
        Ok(tasks
            .iter()
            .filter(|t| t.user_id == Some(user_id))
            .cloned()
            .collect())
    }
}

// ============================================================================
// 领域事件测试用例
// ============================================================================

#[cfg(test)]
mod domain_event_tests {
    use super::*;
    use tokio;

    #[tokio::test]
    async fn test_user_created_event() {
        let user_id = Uuid::new_v4();
        let event = UserCreatedEvent {
            user_id,
            username: "test_user".to_string(),
            email: "<EMAIL>".to_string(),
            occurred_at: Utc::now(),
        };

        assert_eq!(event.event_type(), "UserCreated");
        assert_eq!(event.aggregate_id(), user_id);
    }

    #[tokio::test]
    async fn test_task_status_changed_event() {
        let task_id = Uuid::new_v4();
        let user_id = Uuid::new_v4();
        let event = TaskStatusChangedEvent {
            task_id,
            user_id,
            old_status: false,
            new_status: true,
            occurred_at: Utc::now(),
        };

        assert_eq!(event.event_type(), "TaskStatusChanged");
        assert_eq!(event.aggregate_id(), task_id);
    }

    #[tokio::test]
    async fn test_in_memory_event_publisher() {
        let publisher = InMemoryEventPublisher::new();

        // 测试发布单个事件
        let user_event = Box::new(UserCreatedEvent {
            user_id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: "<EMAIL>".to_string(),
            occurred_at: Utc::now(),
        });

        publisher.publish(user_event).await.unwrap();

        let events = publisher.get_events();
        assert_eq!(events.len(), 1);
        assert_eq!(events[0], "UserCreated");

        // 测试批量发布事件
        let task_event1 = Box::new(TaskStatusChangedEvent {
            task_id: Uuid::new_v4(),
            user_id: Uuid::new_v4(),
            old_status: false,
            new_status: true,
            occurred_at: Utc::now(),
        });

        let task_event2 = Box::new(TaskStatusChangedEvent {
            task_id: Uuid::new_v4(),
            user_id: Uuid::new_v4(),
            old_status: true,
            new_status: false,
            occurred_at: Utc::now(),
        });

        publisher
            .publish_batch(vec![task_event1, task_event2])
            .await
            .unwrap();

        let events = publisher.get_events();
        assert_eq!(events.len(), 3);
        assert_eq!(events[1], "TaskStatusChanged");
        assert_eq!(events[2], "TaskStatusChanged");

        // 测试清空事件
        publisher.clear();
        let events = publisher.get_events();
        assert_eq!(events.len(), 0);
    }

    #[tokio::test]
    async fn test_mock_user_repository() {
        let repository = MockUserRepository::new();

        let user = User {
            id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "hashed_password".to_string(),
            display_name: Some("Test User".to_string()),
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 测试创建用户
        let created_user = repository.create(user.clone()).await.unwrap();
        assert_eq!(created_user.id, user.id);

        // 测试查找用户
        let found_user = repository.find_by_id(user.id).await.unwrap();
        assert!(found_user.is_some());
        assert_eq!(found_user.unwrap().username, "test_user");

        // 测试按用户名查找
        let found_by_username = repository.find_by_username("test_user").await.unwrap();
        assert!(found_by_username.is_some());

        // 测试计数
        let count = repository.count_all().await.unwrap();
        assert_eq!(count, 1);
    }

    #[tokio::test]
    async fn test_mock_task_repository() {
        let repository = MockTaskRepository::new();

        let user_id = Uuid::new_v4();
        let task = Task {
            id: Uuid::new_v4(),
            title: "测试任务".to_string(),
            description: Some("这是一个测试任务".to_string()),
            completed: false,
            user_id: Some(user_id),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 测试创建任务
        let created_task = repository.create(task.clone()).await.unwrap();
        assert_eq!(created_task.id, task.id);

        // 测试查找任务
        let found_task = repository.find_by_id(task.id).await.unwrap();
        assert!(found_task.is_some());
        assert_eq!(found_task.unwrap().title, "测试任务");

        // 测试按用户ID查找任务
        let user_tasks = repository.find_by_user_id(user_id).await.unwrap();
        assert_eq!(user_tasks.len(), 1);

        // 验证任务数量
        let all_tasks = repository.find_all().await.unwrap();
        assert_eq!(all_tasks.len(), 1);
        assert!(!all_tasks[0].completed);
    }
}

// ============================================================================
// 重构后的领域服务测试
// ============================================================================

#[cfg(test)]
mod refactored_domain_service_tests {
    use super::*;
    use crate::services::{TaskDomainServiceImpl, UserDomainServiceImpl};

    #[tokio::test]
    async fn test_user_domain_service_with_events() {
        // 准备测试数据
        let user_repository = Arc::new(MockUserRepository::new());
        let event_publisher = Arc::new(crate::events::EventPublisherType::InMemory(
            crate::events::InMemoryEventPublisher::new(),
        ));

        let user_service =
            UserDomainServiceImpl::new(user_repository.clone(), event_publisher.clone());

        let user = User {
            id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "hashed_password".to_string(),
            display_name: Some("Test User".to_string()),
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 测试用户创建
        let created_user = user_service.create_user(user.clone()).await.unwrap();
        assert_eq!(created_user.username, "test_user");

        // 验证事件发布
        let events = match event_publisher.as_ref() {
            crate::events::EventPublisherType::InMemory(publisher) => publisher.get_events(),
            _ => panic!("Expected InMemory publisher"),
        };
        assert_eq!(events.len(), 1);
        assert_eq!(events[0].event_type, "UserCreated");

        // 验证事件数据
        let user_created_event: crate::events::UserCreatedEvent =
            events[0].deserialize_data().unwrap();
        assert_eq!(user_created_event.user_id, created_user.id);
        assert_eq!(user_created_event.username, "test_user");
        assert_eq!(user_created_event.email, "<EMAIL>");
    }

    #[tokio::test]
    async fn test_task_domain_service_with_events() {
        // 准备测试数据
        let user_repository = Arc::new(MockUserRepository::new());
        let task_repository = Arc::new(MockTaskRepository::new());
        let event_publisher = Arc::new(crate::events::EventPublisherType::InMemory(
            crate::events::InMemoryEventPublisher::new(),
        ));

        // 创建测试用户
        let user = User {
            id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "hashed_password".to_string(),
            display_name: Some("Test User".to_string()),
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };
        user_repository.create(user.clone()).await.unwrap();

        let task_service = TaskDomainServiceImpl::new(
            task_repository.clone(),
            user_repository.clone(),
            event_publisher.clone(),
        );

        let task = Task {
            id: Uuid::new_v4(),
            title: "测试任务".to_string(),
            description: Some("这是一个测试任务".to_string()),
            completed: false,
            user_id: None, // 将由服务设置
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 测试任务创建
        let created_task = task_service
            .create_task(task.clone(), user.id)
            .await
            .unwrap();
        assert_eq!(created_task.title, "测试任务");
        assert_eq!(created_task.user_id, Some(user.id));

        // 验证任务创建事件
        let events = match event_publisher.as_ref() {
            crate::events::EventPublisherType::InMemory(publisher) => publisher.get_events(),
            _ => panic!("Expected InMemory publisher"),
        };
        assert_eq!(events.len(), 1);
        assert_eq!(events[0].event_type, "TaskCreated");

        let task_created_event: crate::events::TaskCreatedEvent =
            events[0].deserialize_data().unwrap();
        assert_eq!(task_created_event.task_id, created_task.id);
        assert_eq!(task_created_event.title, "测试任务");
        assert_eq!(task_created_event.user_id, user.id);

        // 清空事件以测试任务完成
        match event_publisher.as_ref() {
            crate::events::EventPublisherType::InMemory(publisher) => publisher.clear(),
            _ => panic!("Expected InMemory publisher"),
        }

        // 测试任务完成
        let completed_task = task_service
            .complete_task(created_task.id, user.id)
            .await
            .unwrap();
        assert!(completed_task.completed);

        // 验证任务完成事件
        let events = match event_publisher.as_ref() {
            crate::events::EventPublisherType::InMemory(publisher) => publisher.get_events(),
            _ => panic!("Expected InMemory publisher"),
        };
        assert_eq!(events.len(), 2); // 状态变更事件 + 完成事件

        // 验证状态变更事件
        let status_event = events
            .iter()
            .find(|e| e.event_type == "TaskStatusChanged")
            .unwrap();
        let status_changed_event: crate::events::TaskStatusChangedEvent =
            status_event.deserialize_data().unwrap();
        assert_eq!(status_changed_event.task_id, completed_task.id);
        assert!(!status_changed_event.old_status);
        assert!(status_changed_event.new_status);

        // 验证完成事件
        let completed_event = events
            .iter()
            .find(|e| e.event_type == "TaskCompleted")
            .unwrap();
        let task_completed_event: crate::events::TaskCompletedEvent =
            completed_event.deserialize_data().unwrap();
        assert_eq!(task_completed_event.task_id, completed_task.id);
        assert_eq!(task_completed_event.completed_by, user.id);
    }

    #[tokio::test]
    async fn test_event_publisher_error_handling() {
        // 测试事件发布失败不影响主要业务流程
        let user_repository = Arc::new(MockUserRepository::new());

        // 创建一个会失败的事件发布器
        struct FailingEventPublisher;

        #[async_trait]
        impl crate::events::DomainEventPublisher for FailingEventPublisher {
            async fn publish_wrapper(
                &self,
                _event: crate::events::EventWrapper,
            ) -> Result<(), app_common::error::AppError> {
                Err(app_common::error::AppError::InternalServerError(
                    "事件发布失败".to_string(),
                ))
            }

            async fn publish_batch(
                &self,
                _events: Vec<crate::events::EventWrapper>,
            ) -> Result<(), app_common::error::AppError> {
                Err(app_common::error::AppError::InternalServerError(
                    "批量事件发布失败".to_string(),
                ))
            }

            async fn publish_and_wait(
                &self,
                _event: crate::events::EventWrapper,
            ) -> Result<(), app_common::error::AppError> {
                Err(app_common::error::AppError::InternalServerError(
                    "同步事件发布失败".to_string(),
                ))
            }
        }

        let failing_publisher = Arc::new(crate::events::EventPublisherType::Null(
            crate::events::NullEventPublisher::new(),
        ));
        let user_service = UserDomainServiceImpl::new(user_repository.clone(), failing_publisher);

        let user = User {
            id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "hashed_password".to_string(),
            display_name: Some("Test User".to_string()),
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // 即使事件发布失败，用户创建应该仍然成功
        let created_user = user_service.create_user(user.clone()).await.unwrap();
        assert_eq!(created_user.username, "test_user");

        // 验证用户确实被创建到仓储中
        let found_user = user_repository.find_by_id(created_user.id).await.unwrap();
        assert!(found_user.is_some());
    }
}
