//! # 领域服务接口模块
//!
//! 定义核心业务规则和服务接口，包括：
//! - 用户管理服务接口
//! - 任务管理服务接口
//! - 聊天服务接口
//! - 认证服务接口

pub mod auth_service;
pub mod chat_service;
pub mod task_service;
pub mod user_service;

// 测试模块
#[cfg(test)]
pub mod tests;

#[cfg(test)]
pub mod domain_events_tests;

// 重新导出服务接口
pub use auth_service::AuthDomainService;
pub use chat_service::ChatDomainService;
pub use task_service::{TaskDomainService, TaskDomainServiceImpl, TaskStatistics};
pub use user_service::{UserDomainService, UserDomainServiceImpl};
