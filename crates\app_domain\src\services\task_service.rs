//! # 任务领域服务接口
//!
//! 定义任务相关的核心业务规则和操作接口

use crate::entities::task::Task;
use crate::events::DomainEventPublisherExt;
use crate::repositories::task_repository::TaskRepositoryContract;
use crate::repositories::user_repository::UserRepositoryContract;
use app_common::error::{AppError, Result};
use app_common::tracing::{debug, error, info, warn};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

/// 任务领域服务接口
///
/// 定义任务相关的核心业务操作，包括：
/// - 任务创建和验证
/// - 任务状态管理
/// - 任务权限控制
/// - 任务业务规则验证
///
/// 设计原则：
/// - 专注于任务业务逻辑，不涉及具体的数据存储实现
/// - 使用async-trait支持异步操作
/// - 提供清晰的错误处理机制
/// - 遵循单一职责原则
#[async_trait]
pub trait TaskDomainService: Send + Sync {
    /// 验证任务实体的业务规则
    ///
    /// # 参数
    /// - `task`: 要验证的任务实体
    ///
    /// # 返回
    /// - `Ok(())`: 验证通过
    /// - `Err(...)`: 验证失败，包含具体错误信息
    async fn validate_task(&self, task: &Task) -> Result<()>;

    /// 创建新任务
    ///
    /// # 参数
    /// - `task`: 要创建的任务实体
    /// - `user_id`: 任务所有者ID
    ///
    /// # 返回
    /// - `Ok(task)`: 创建成功，返回创建的任务
    /// - `Err(...)`: 创建失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 任务信息必须通过验证
    /// - 用户必须存在且有权限创建任务
    /// - 任务标题不能为空
    async fn create_task(&self, task: Task, user_id: Uuid) -> Result<Task>;

    /// 更新任务信息
    ///
    /// # 参数
    /// - `task`: 更新后的任务实体
    /// - `user_id`: 操作用户ID
    ///
    /// # 返回
    /// - `Ok(task)`: 更新成功，返回更新后的任务
    /// - `Err(...)`: 更新失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 任务必须存在
    /// - 用户必须有权限修改该任务
    /// - 更新的信息必须通过验证
    async fn update_task(&self, task: Task, user_id: Uuid) -> Result<Task>;

    /// 完成任务
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    /// - `user_id`: 操作用户ID
    ///
    /// # 返回
    /// - `Ok(task)`: 完成成功，返回更新后的任务
    /// - `Err(...)`: 完成失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 任务必须存在且未完成
    /// - 用户必须有权限操作该任务
    async fn complete_task(&self, task_id: Uuid, user_id: Uuid) -> Result<Task>;

    /// 重新打开任务
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    /// - `user_id`: 操作用户ID
    ///
    /// # 返回
    /// - `Ok(task)`: 重新打开成功，返回更新后的任务
    /// - `Err(...)`: 重新打开失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 任务必须存在且已完成
    /// - 用户必须有权限操作该任务
    async fn reopen_task(&self, task_id: Uuid, user_id: Uuid) -> Result<Task>;

    /// 删除任务
    ///
    /// # 参数
    /// - `task_id`: 要删除的任务ID
    /// - `user_id`: 操作用户ID
    ///
    /// # 返回
    /// - `Ok(())`: 删除成功
    /// - `Err(...)`: 删除失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 任务必须存在
    /// - 用户必须有权限删除该任务
    async fn delete_task(&self, task_id: Uuid, user_id: Uuid) -> Result<()>;

    /// 获取用户的所有任务
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    /// - `include_completed`: 是否包含已完成的任务
    ///
    /// # 返回
    /// - `Ok(tasks)`: 获取成功，返回任务列表
    /// - `Err(...)`: 获取失败，包含具体错误信息
    async fn get_user_tasks(&self, user_id: Uuid, include_completed: bool) -> Result<Vec<Task>>;

    /// 根据ID获取任务
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    /// - `user_id`: 请求用户ID
    ///
    /// # 返回
    /// - `Ok(Some(task))`: 找到任务
    /// - `Ok(None)`: 任务不存在或无权限访问
    /// - `Err(...)`: 查询过程中发生错误
    async fn get_task_by_id(&self, task_id: Uuid, user_id: Uuid) -> Result<Option<Task>>;

    /// 检查用户是否有权限操作指定任务
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Ok(true)`: 有权限
    /// - `Ok(false)`: 无权限
    /// - `Err(...)`: 检查过程中发生错误
    async fn has_task_permission(&self, task_id: Uuid, user_id: Uuid) -> Result<bool>;

    /// 获取任务统计信息
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Ok(stats)`: 获取成功，返回统计信息
    /// - `Err(...)`: 获取失败，包含具体错误信息
    async fn get_task_statistics(&self, user_id: Uuid) -> Result<TaskStatistics>;
}

/// 任务统计信息
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct TaskStatistics {
    /// 总任务数
    pub total_tasks: u32,
    /// 已完成任务数
    pub completed_tasks: u32,
    /// 待完成任务数
    pub pending_tasks: u32,
    /// 完成率（百分比）
    pub completion_rate: f32,
}

impl TaskStatistics {
    /// 创建新的任务统计信息
    pub fn new(total: u32, completed: u32) -> Self {
        let pending = total.saturating_sub(completed);
        let completion_rate = if total > 0 {
            ((completed as f32) / (total as f32)) * 100.0
        } else {
            0.0
        };

        Self {
            total_tasks: total,
            completed_tasks: completed,
            pending_tasks: pending,
            completion_rate,
        }
    }
}

/// 任务领域服务的具体实现
///
/// 通过依赖注入使用仓储接口，实现任务相关的核心业务逻辑
pub struct TaskDomainServiceImpl {
    /// 任务仓储接口
    task_repository: Arc<dyn TaskRepositoryContract>,
    /// 用户仓储接口（用于验证用户权限）
    user_repository: Arc<dyn UserRepositoryContract>,

    /// 领域事件发布器
    event_publisher: Arc<crate::events::EventPublisherType>,
}

impl TaskDomainServiceImpl {
    /// 创建新的任务领域服务实例
    ///
    /// # 参数
    /// - `task_repository`: 任务仓储接口的实现
    /// - `user_repository`: 用户仓储接口的实现
    /// - `event_publisher`: 领域事件发布器的实现
    ///
    /// # 返回
    /// 返回任务领域服务实例
    pub fn new(
        task_repository: Arc<dyn TaskRepositoryContract>,
        user_repository: Arc<dyn UserRepositoryContract>,
        event_publisher: Arc<crate::events::EventPublisherType>,
    ) -> Self {
        info!("🏗️ 创建任务领域服务实例");
        Self {
            task_repository,
            user_repository,
            event_publisher,
        }
    }

    /// 验证用户是否存在
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Ok(())`: 用户存在
    /// - `Err(...)`: 用户不存在或查询失败
    async fn validate_user_exists(&self, user_id: Uuid) -> Result<()> {
        debug!("验证用户是否存在: {}", user_id);

        match self.user_repository.find_by_id(user_id).await {
            Ok(Some(_)) => {
                debug!("用户存在: {}", user_id);
                Ok(())
            }
            Ok(None) => {
                warn!("用户不存在: {}", user_id);
                Err(AppError::NotFound(format!("用户不存在: {user_id}")))
            }
            Err(err) => {
                error!("验证用户存在时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 验证任务是否存在
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    ///
    /// # 返回
    /// - `Ok(task)`: 任务存在，返回任务实体
    /// - `Err(...)`: 任务不存在或查询失败
    async fn validate_task_exists(&self, task_id: Uuid) -> Result<Task> {
        debug!("验证任务是否存在: {}", task_id);

        match self.task_repository.find_by_id(task_id).await {
            Ok(Some(task)) => {
                debug!("任务存在: {}", task_id);
                Ok(task)
            }
            Ok(None) => {
                warn!("任务不存在: {}", task_id);
                Err(AppError::NotFound(format!("任务不存在: {task_id}")))
            }
            Err(err) => {
                error!("验证任务存在时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 验证用户对任务的权限
    ///
    /// # 参数
    /// - `task`: 任务实体
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Ok(())`: 有权限
    /// - `Err(...)`: 无权限
    fn validate_task_permission(&self, task: &Task, user_id: Uuid) -> Result<()> {
        debug!("验证用户任务权限: task_id={}, user_id={}", task.id, user_id);

        if let Some(task_user_id) = task.user_id {
            if task_user_id == user_id {
                debug!("用户有权限操作任务: {}", task.id);
                Ok(())
            } else {
                warn!(
                    "用户无权限操作任务: task_id={}, user_id={}",
                    task.id, user_id
                );
                Err(AppError::Forbidden("无权限操作此任务".to_string()))
            }
        } else {
            warn!("任务没有关联用户: {}", task.id);
            Err(AppError::ValidationError("任务没有关联用户".to_string()))
        }
    }
}

#[async_trait]
impl TaskDomainService for TaskDomainServiceImpl {
    /// 验证任务实体的业务规则
    async fn validate_task(&self, task: &Task) -> Result<()> {
        info!("验证任务实体业务规则: {}", task.title);

        // 验证任务标题
        TaskBusinessRules::validate_title(&task.title)?;

        // 验证任务描述
        TaskBusinessRules::validate_description(&task.description)?;

        // 验证关联用户（如果有）
        if let Some(user_id) = task.user_id {
            self.validate_user_exists(user_id).await?;
        }

        info!("任务实体验证通过: {}", task.title);
        Ok(())
    }

    /// 创建新任务
    async fn create_task(&self, mut task: Task, user_id: Uuid) -> Result<Task> {
        info!("创建新任务: {} (用户: {})", task.title, user_id);

        // 验证用户存在
        self.validate_user_exists(user_id).await?;

        // 设置任务的用户ID
        task.user_id = Some(user_id);

        // 验证任务实体
        self.validate_task(&task).await?;

        // 创建任务
        match self.task_repository.create(task.clone()).await {
            Ok(created_task) => {
                info!("任务创建成功: {}", created_task.title);

                // 发布任务创建事件
                let event = crate::events::TaskCreatedEvent::new(
                    created_task.id,
                    created_task.title.clone(),
                    user_id,
                    user_id, // 创建者与所有者相同
                );

                if let Err(e) = self.event_publisher.publish(event, None).await {
                    warn!("发布任务创建事件失败: {:?}", e);
                    // 注意：事件发布失败不应该影响主要业务流程
                }

                Ok(created_task)
            }
            Err(err) => {
                error!("创建任务时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 更新任务信息
    async fn update_task(&self, task: Task, user_id: Uuid) -> Result<Task> {
        info!("更新任务信息: {} (用户: {})", task.title, user_id);

        // 验证用户存在
        self.validate_user_exists(user_id).await?;

        // 验证任务存在
        let existing_task = self.validate_task_exists(task.id).await?;

        // 验证用户权限
        self.validate_task_permission(&existing_task, user_id)?;

        // 验证更新后的任务实体
        self.validate_task(&task).await?;

        // 更新任务
        match self.task_repository.update(task).await {
            Ok(updated_task) => {
                info!("任务更新成功: {}", updated_task.title);
                Ok(updated_task)
            }
            Err(err) => {
                error!("更新任务时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 完成任务
    async fn complete_task(&self, task_id: Uuid, user_id: Uuid) -> Result<Task> {
        info!("完成任务: {} (用户: {})", task_id, user_id);

        // 验证用户存在
        self.validate_user_exists(user_id).await?;

        // 验证任务存在
        let mut task = self.validate_task_exists(task_id).await?;

        // 验证用户权限
        self.validate_task_permission(&task, user_id)?;

        // 检查任务是否已完成
        if task.completed {
            return Err(AppError::ValidationError("任务已经完成".to_string()));
        }

        // 验证状态转换
        TaskBusinessRules::validate_status_transition(task.completed, true)?;

        // 保存旧状态用于事件发布
        let old_status = task.completed;

        // 更新任务状态
        task.completed = true;
        task.updated_at = chrono::Utc::now();

        // 保存更新
        match self.task_repository.update(task.clone()).await {
            Ok(updated_task) => {
                info!("任务完成成功: {}", task_id);

                // 发布任务状态变更事件
                let status_event = crate::events::TaskStatusChangedEvent::new(
                    updated_task.id,
                    updated_task.title.clone(),
                    user_id,
                    old_status,
                    updated_task.completed,
                    user_id,
                );

                // 发布任务完成事件
                let completed_event = crate::events::TaskCompletedEvent::new(
                    updated_task.id,
                    updated_task.title.clone(),
                    user_id,
                    user_id,
                );

                // 批量发布事件
                if let Err(e) = self.event_publisher.publish(status_event, None).await {
                    warn!("发布任务状态变更事件失败: {:?}", e);
                }

                if let Err(e) = self.event_publisher.publish(completed_event, None).await {
                    warn!("发布任务完成事件失败: {:?}", e);
                }

                Ok(updated_task)
            }
            Err(err) => {
                error!("完成任务时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 重新打开任务
    async fn reopen_task(&self, task_id: Uuid, user_id: Uuid) -> Result<Task> {
        info!("重新打开任务: {} (用户: {})", task_id, user_id);

        // 验证用户存在
        self.validate_user_exists(user_id).await?;

        // 验证任务存在
        let mut task = self.validate_task_exists(task_id).await?;

        // 验证用户权限
        self.validate_task_permission(&task, user_id)?;

        // 检查任务是否已完成
        if !task.completed {
            return Err(AppError::ValidationError("任务尚未完成".to_string()));
        }

        // 验证状态转换
        TaskBusinessRules::validate_status_transition(task.completed, false)?;

        // 更新任务状态
        task.completed = false;
        task.updated_at = chrono::Utc::now();

        // 保存更新
        match self.task_repository.update(task).await {
            Ok(updated_task) => {
                info!("任务重新打开成功: {}", task_id);
                Ok(updated_task)
            }
            Err(err) => {
                error!("重新打开任务时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 删除任务
    async fn delete_task(&self, task_id: Uuid, user_id: Uuid) -> Result<()> {
        info!("删除任务: {} (用户: {})", task_id, user_id);

        // 验证用户存在
        self.validate_user_exists(user_id).await?;

        // 验证任务存在
        let task = self.validate_task_exists(task_id).await?;

        // 验证用户权限
        self.validate_task_permission(&task, user_id)?;

        // 删除任务
        match self.task_repository.delete(task_id).await {
            Ok(_) => {
                info!("任务删除成功: {}", task_id);
                Ok(())
            }
            Err(err) => {
                error!("删除任务时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 获取用户的所有任务
    async fn get_user_tasks(&self, user_id: Uuid, include_completed: bool) -> Result<Vec<Task>> {
        info!(
            "获取用户任务: {} (包含已完成: {})",
            user_id, include_completed
        );

        // 验证用户存在
        self.validate_user_exists(user_id).await?;

        // 获取用户任务
        match self.task_repository.find_by_user_id(user_id).await {
            Ok(tasks) => {
                let filtered_tasks: Vec<Task> = if include_completed {
                    tasks
                } else {
                    tasks.into_iter().filter(|task| !task.completed).collect()
                };

                info!("获取到 {} 个任务", filtered_tasks.len());
                Ok(filtered_tasks)
            }
            Err(err) => {
                error!("获取用户任务时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 根据ID获取任务
    async fn get_task_by_id(&self, task_id: Uuid, user_id: Uuid) -> Result<Option<Task>> {
        info!("根据ID获取任务: {} (用户: {})", task_id, user_id);

        // 验证用户存在
        self.validate_user_exists(user_id).await?;

        // 获取任务
        match self.task_repository.find_by_id(task_id).await {
            Ok(Some(task)) => {
                // 验证用户权限
                match self.validate_task_permission(&task, user_id) {
                    Ok(_) => {
                        info!("找到任务: {}", task_id);
                        Ok(Some(task))
                    }
                    Err(_) => {
                        warn!("用户无权限访问任务: {}", task_id);
                        Ok(None) // 返回None而不是错误，表示任务不存在或无权限
                    }
                }
            }
            Ok(None) => {
                warn!("任务不存在: {}", task_id);
                Ok(None)
            }
            Err(err) => {
                error!("根据ID获取任务时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 检查用户是否有权限操作指定任务
    async fn has_task_permission(&self, task_id: Uuid, user_id: Uuid) -> Result<bool> {
        info!("检查任务权限: task_id={}, user_id={}", task_id, user_id);

        // 验证用户存在
        self.validate_user_exists(user_id).await?;

        // 获取任务
        match self.task_repository.find_by_id(task_id).await {
            Ok(Some(task)) => {
                // 检查权限
                match self.validate_task_permission(&task, user_id) {
                    Ok(_) => {
                        info!("用户有权限操作任务: {}", task_id);
                        Ok(true)
                    }
                    Err(_) => {
                        info!("用户无权限操作任务: {}", task_id);
                        Ok(false)
                    }
                }
            }
            Ok(None) => {
                warn!("任务不存在: {}", task_id);
                Ok(false)
            }
            Err(err) => {
                error!("检查任务权限时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 获取任务统计信息
    async fn get_task_statistics(&self, user_id: Uuid) -> Result<TaskStatistics> {
        info!("获取任务统计信息: {}", user_id);

        // 验证用户存在
        self.validate_user_exists(user_id).await?;

        // 获取用户所有任务
        match self.task_repository.find_by_user_id(user_id).await {
            Ok(tasks) => {
                let total_tasks = tasks.len() as u32;
                let completed_tasks = tasks.iter().filter(|task| task.completed).count() as u32;

                let statistics = TaskStatistics::new(total_tasks, completed_tasks);
                info!("任务统计信息: {:?}", statistics);
                Ok(statistics)
            }
            Err(err) => {
                error!("获取任务统计信息时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }
}

/// 任务业务规则验证器
///
/// 提供任务相关的业务规则验证功能
pub struct TaskBusinessRules;

impl TaskBusinessRules {
    /// 验证任务标题
    ///
    /// # 参数
    /// - `title`: 任务标题
    ///
    /// # 返回
    /// - `Ok(())`: 标题有效
    /// - `Err(...)`: 标题无效
    pub fn validate_title(title: &str) -> Result<()> {
        if title.trim().is_empty() {
            return Err(app_common::error::AppError::ValidationError(
                "任务标题不能为空".to_string(),
            ));
        }

        if title.len() > 200 {
            return Err(app_common::error::AppError::ValidationError(
                "任务标题长度不能超过200个字符".to_string(),
            ));
        }

        Ok(())
    }

    /// 验证任务描述
    ///
    /// # 参数
    /// - `description`: 任务描述
    ///
    /// # 返回
    /// - `Ok(())`: 描述有效
    /// - `Err(...)`: 描述无效
    pub fn validate_description(description: &Option<String>) -> Result<()> {
        if let Some(desc) = description {
            if desc.len() > 1000 {
                return Err(app_common::error::AppError::ValidationError(
                    "任务描述长度不能超过1000个字符".to_string(),
                ));
            }
        }
        Ok(())
    }

    /// 验证任务状态转换是否有效
    ///
    /// # 参数
    /// - `from_completed`: 当前完成状态
    /// - `to_completed`: 目标完成状态
    ///
    /// # 返回
    /// - `Ok(())`: 状态转换有效
    /// - `Err(...)`: 状态转换无效
    pub fn validate_status_transition(from_completed: bool, to_completed: bool) -> Result<()> {
        // 目前允许任意状态转换，未来可以根据业务需求添加更复杂的规则
        if from_completed == to_completed {
            return Err(app_common::error::AppError::ValidationError(
                "任务状态没有变化".to_string(),
            ));
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_task_statistics_new() {
        let stats = TaskStatistics::new(10, 7);
        assert_eq!(stats.total_tasks, 10);
        assert_eq!(stats.completed_tasks, 7);
        assert_eq!(stats.pending_tasks, 3);
        assert_eq!(stats.completion_rate, 70.0);

        // 测试零除情况
        let empty_stats = TaskStatistics::new(0, 0);
        assert_eq!(empty_stats.completion_rate, 0.0);
    }

    #[test]
    fn test_validate_title() {
        // 测试有效标题
        assert!(TaskBusinessRules::validate_title("有效的任务标题").is_ok());

        // 测试无效标题
        assert!(TaskBusinessRules::validate_title("").is_err()); // 空标题
        assert!(TaskBusinessRules::validate_title("   ").is_err()); // 只有空格
        assert!(TaskBusinessRules::validate_title(&"a".repeat(201)).is_err()); // 太长
    }

    #[test]
    fn test_validate_description() {
        // 测试有效描述
        assert!(TaskBusinessRules::validate_description(&None).is_ok());
        assert!(TaskBusinessRules::validate_description(&Some("有效描述".to_string())).is_ok());

        // 测试无效描述
        assert!(TaskBusinessRules::validate_description(&Some("a".repeat(1001))).is_err()); // 太长
    }

    #[test]
    fn test_validate_status_transition() {
        // 测试有效状态转换
        assert!(TaskBusinessRules::validate_status_transition(false, true).is_ok());
        assert!(TaskBusinessRules::validate_status_transition(true, false).is_ok());

        // 测试无效状态转换
        assert!(TaskBusinessRules::validate_status_transition(false, false).is_err());
        assert!(TaskBusinessRules::validate_status_transition(true, true).is_err());
    }
}
