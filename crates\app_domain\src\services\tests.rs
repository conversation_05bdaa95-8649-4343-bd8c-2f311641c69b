//! # 领域服务接口测试模块
//!
//! 为领域服务接口提供全面的单元测试

#[cfg(test)]
mod user_service_tests {
    use super::super::user_service::*;
    use crate::entities::user::User;
    use chrono::Utc;
    use uuid::Uuid;
    use validator::Validate;

    /// 创建测试用户实体
    fn create_test_user() -> User {
        User {
            id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: None,
            password_hash: "hashed_password".to_string(),
            display_name: None,
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    #[test]
    fn test_user_business_rules_validate_username_format() {
        // 测试有效用户名
        assert!(UserBusinessRules::validate_username_format("valid_user").is_ok());
        assert!(UserBusinessRules::validate_username_format("user123").is_ok());
        assert!(UserBusinessRules::validate_username_format("test-user").is_ok());

        // 测试无效用户名
        assert!(UserBusinessRules::validate_username_format("ab").is_err()); // 太短
        assert!(UserBusinessRules::validate_username_format(&"a".repeat(51)).is_err()); // 太长
    }

    #[test]
    fn test_user_business_rules_validate_password_strength() {
        // 测试有效密码
        assert!(UserBusinessRules::validate_password_strength("password123").is_ok());
        assert!(UserBusinessRules::validate_password_strength("MySecure1").is_ok());

        // 测试无效密码
        assert!(UserBusinessRules::validate_password_strength("short1").is_err()); // 太短
        assert!(UserBusinessRules::validate_password_strength("password").is_err()); // 无数字
        assert!(UserBusinessRules::validate_password_strength("12345678").is_err()); // 无字母
        assert!(UserBusinessRules::validate_password_strength(&"a".repeat(129)).is_err()); // 太长
    }

    #[test]
    fn test_user_entity_validation() {
        let user = create_test_user();
        // 用户实体应该通过验证
        assert!(user.validate().is_ok());

        // 测试无效用户名的用户
        let invalid_user = User {
            username: "ab".to_string(), // 太短
            ..user
        };
        assert!(invalid_user.validate().is_err());
    }
}

#[cfg(test)]
mod task_service_tests {
    use super::super::task_service::*;

    #[test]
    fn test_task_statistics_new() {
        let stats = TaskStatistics::new(10, 7);
        assert_eq!(stats.total_tasks, 10);
        assert_eq!(stats.completed_tasks, 7);
        assert_eq!(stats.pending_tasks, 3);
        assert_eq!(stats.completion_rate, 70.0);

        // 测试零除情况
        let empty_stats = TaskStatistics::new(0, 0);
        assert_eq!(empty_stats.completion_rate, 0.0);

        // 测试边界情况
        let full_stats = TaskStatistics::new(5, 5);
        assert_eq!(full_stats.completion_rate, 100.0);
    }

    #[test]
    fn test_task_business_rules_validate_title() {
        // 测试有效标题
        assert!(TaskBusinessRules::validate_title("有效的任务标题").is_ok());
        assert!(TaskBusinessRules::validate_title("Valid Task Title").is_ok());

        // 测试无效标题
        assert!(TaskBusinessRules::validate_title("").is_err()); // 空标题
        assert!(TaskBusinessRules::validate_title("   ").is_err()); // 只有空格
        assert!(TaskBusinessRules::validate_title(&"a".repeat(201)).is_err()); // 太长
    }

    #[test]
    fn test_task_business_rules_validate_description() {
        // 测试有效描述
        assert!(TaskBusinessRules::validate_description(&None).is_ok());
        assert!(TaskBusinessRules::validate_description(&Some("有效描述".to_string())).is_ok());
        assert!(TaskBusinessRules::validate_description(&Some("".to_string())).is_ok()); // 空描述是允许的

        // 测试无效描述
        assert!(TaskBusinessRules::validate_description(&Some("a".repeat(1001))).is_err()); // 太长
    }

    #[test]
    fn test_task_business_rules_validate_status_transition() {
        // 测试有效状态转换
        assert!(TaskBusinessRules::validate_status_transition(false, true).is_ok()); // 未完成 -> 完成
        assert!(TaskBusinessRules::validate_status_transition(true, false).is_ok()); // 完成 -> 未完成

        // 测试无效状态转换
        assert!(TaskBusinessRules::validate_status_transition(false, false).is_err()); // 无变化
        assert!(TaskBusinessRules::validate_status_transition(true, true).is_err()); // 无变化
    }
}

#[cfg(test)]
mod chat_service_tests {
    use super::super::chat_service::*;

    #[test]
    fn test_chat_business_rules_validate_room_name() {
        // 测试有效名称
        assert!(ChatBusinessRules::validate_room_name("有效的聊天室").is_ok());
        assert!(ChatBusinessRules::validate_room_name("General Chat").is_ok());
        assert!(ChatBusinessRules::validate_room_name("Room123").is_ok());

        // 测试无效名称
        assert!(ChatBusinessRules::validate_room_name("").is_err()); // 空名称
        assert!(ChatBusinessRules::validate_room_name("   ").is_err()); // 只有空格
        assert!(ChatBusinessRules::validate_room_name(&"a".repeat(101)).is_err()); // 太长
    }

    #[test]
    fn test_chat_business_rules_validate_message_content() {
        // 测试有效内容
        assert!(ChatBusinessRules::validate_message_content("Hello, world!").is_ok());
        assert!(ChatBusinessRules::validate_message_content("你好，世界！").is_ok());

        // 测试无效内容
        assert!(ChatBusinessRules::validate_message_content("").is_err()); // 空内容
        assert!(ChatBusinessRules::validate_message_content("   ").is_err()); // 只有空格
        assert!(ChatBusinessRules::validate_message_content(&"a".repeat(2001)).is_err()); // 太长
    }

    #[test]
    fn test_chat_business_rules_validate_member_limit() {
        // 测试有效情况
        assert!(ChatBusinessRules::validate_member_limit(5, 10).is_ok()); // 未满
        assert!(ChatBusinessRules::validate_member_limit(0, 0).is_ok()); // 无限制
        assert!(ChatBusinessRules::validate_member_limit(5, 0).is_ok()); // 无限制

        // 测试无效情况
        assert!(ChatBusinessRules::validate_member_limit(10, 10).is_err()); // 已满
        assert!(ChatBusinessRules::validate_member_limit(15, 10).is_err()); // 超出限制
    }
}

#[cfg(test)]
mod auth_service_tests {
    use super::super::auth_service::*;

    #[test]
    fn test_user_registration_password_confirmation() {
        let valid_registration = UserRegistration {
            username: "testuser".to_string(),
            password: "password123".to_string(),
            password_confirmation: "password123".to_string(),
        };
        assert!(valid_registration.validate_password_confirmation().is_ok());

        let invalid_registration = UserRegistration {
            username: "testuser".to_string(),
            password: "password123".to_string(),
            password_confirmation: "different".to_string(),
        };
        assert!(
            invalid_registration
                .validate_password_confirmation()
                .is_err()
        );
    }

    #[test]
    fn test_auth_business_rules_validate_username() {
        // 测试有效用户名
        assert!(AuthBusinessRules::validate_username("valid_user").is_ok());
        assert!(AuthBusinessRules::validate_username("user123").is_ok());
        assert!(AuthBusinessRules::validate_username("test-user").is_ok());

        // 测试无效用户名
        assert!(AuthBusinessRules::validate_username("ab").is_err()); // 太短
        assert!(AuthBusinessRules::validate_username(&"a".repeat(51)).is_err()); // 太长
        assert!(AuthBusinessRules::validate_username("user@name").is_err()); // 包含非法字符
        assert!(AuthBusinessRules::validate_username("user name").is_err()); // 包含空格
    }

    #[test]
    fn test_auth_business_rules_validate_password_strength() {
        // 测试有效密码
        assert!(AuthBusinessRules::validate_password_strength("password123").is_ok());
        assert!(AuthBusinessRules::validate_password_strength("MySecure1").is_ok());
        assert!(AuthBusinessRules::validate_password_strength("Complex1Pass").is_ok());

        // 测试无效密码
        assert!(AuthBusinessRules::validate_password_strength("short1").is_err()); // 太短
        assert!(AuthBusinessRules::validate_password_strength("password").is_err()); // 无数字
        assert!(AuthBusinessRules::validate_password_strength("12345678").is_err()); // 无字母
        assert!(AuthBusinessRules::validate_password_strength(&"a".repeat(129)).is_err()); // 太长
    }

    #[test]
    fn test_user_info_from_user() {
        use crate::entities::user::User;
        use chrono::Utc;
        use uuid::Uuid;

        let user = User {
            id: Uuid::new_v4(),
            username: "testuser".to_string(),
            email: None,
            password_hash: "hashed".to_string(),
            display_name: None,
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let user_info: UserInfo = user.clone().into();
        assert_eq!(user_info.id, user.id);
        assert_eq!(user_info.username, user.username);
        assert_eq!(user_info.created_at, user.created_at);
    }
}

/// 集成测试辅助函数
#[cfg(test)]
pub mod test_helpers {
    use crate::entities::task::Task;
    use crate::entities::user::User;
    use chrono::Utc;
    use uuid::Uuid;

    /// 创建测试用户
    pub fn create_test_user_with_username(username: &str) -> User {
        User {
            id: Uuid::new_v4(),
            username: username.to_string(),
            email: None,
            password_hash: "hashed_password".to_string(),
            display_name: None,
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    /// 创建测试任务
    pub fn create_test_task_with_title(title: &str, user_id: Uuid) -> Task {
        Task {
            id: Uuid::new_v4(),
            title: title.to_string(),
            description: Some("测试任务描述".to_string()),
            completed: false,
            user_id: Some(user_id),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }
}
