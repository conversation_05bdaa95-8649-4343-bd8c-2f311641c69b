//! # 用户领域服务接口
//!
//! 定义用户相关的核心业务规则和操作接口

use crate::entities::user::User;
use crate::events::DomainEventPublisherExt;
use crate::repositories::user_repository::UserRepositoryContract;
use app_common::error::{AppError, Result};
use app_common::tracing::{debug, error, info, warn};
use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

/// 用户领域服务接口
///
/// 定义用户相关的核心业务操作，包括：
/// - 用户创建和验证
/// - 用户信息查询和更新
/// - 用户业务规则验证
///
/// 设计原则：
/// - 专注于业务逻辑，不涉及具体的数据存储实现
/// - 使用async-trait支持异步操作
/// - 提供清晰的错误处理机制
/// - 遵循单一职责原则
#[async_trait]
pub trait UserDomainService: Send + Sync {
    /// 验证用户名是否可用
    ///
    /// # 参数
    /// - `username`: 要验证的用户名
    ///
    /// # 返回
    /// - `Ok(true)`: 用户名可用
    /// - `Ok(false)`: 用户名已被使用
    /// - `Err(...)`: 验证过程中发生错误
    async fn is_username_available(&self, username: &str) -> Result<bool>;

    /// 验证用户实体的业务规则
    ///
    /// # 参数
    /// - `user`: 要验证的用户实体
    ///
    /// # 返回
    /// - `Ok(())`: 验证通过
    /// - `Err(...)`: 验证失败，包含具体错误信息
    async fn validate_user(&self, user: &User) -> Result<()>;

    /// 根据用户ID获取用户信息
    ///
    /// # 参数
    /// - `user_id`: 用户唯一标识符
    ///
    /// # 返回
    /// - `Ok(Some(user))`: 找到用户
    /// - `Ok(None)`: 用户不存在
    /// - `Err(...)`: 查询过程中发生错误
    async fn get_user_by_id(&self, user_id: Uuid) -> Result<Option<User>>;

    /// 根据用户名获取用户信息
    ///
    /// # 参数
    /// - `username`: 用户名
    ///
    /// # 返回
    /// - `Ok(Some(user))`: 找到用户
    /// - `Ok(None)`: 用户不存在
    /// - `Err(...)`: 查询过程中发生错误
    async fn get_user_by_username(&self, username: &str) -> Result<Option<User>>;

    /// 创建新用户
    ///
    /// # 参数
    /// - `user`: 要创建的用户实体
    ///
    /// # 返回
    /// - `Ok(user)`: 创建成功，返回创建的用户
    /// - `Err(...)`: 创建失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户名必须唯一
    /// - 用户信息必须通过验证
    /// - 密码必须已经过哈希处理
    async fn create_user(&self, user: User) -> Result<User>;

    /// 更新用户信息
    ///
    /// # 参数
    /// - `user`: 更新后的用户实体
    ///
    /// # 返回
    /// - `Ok(user)`: 更新成功，返回更新后的用户
    /// - `Err(...)`: 更新失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户必须存在
    /// - 更新的信息必须通过验证
    /// - 如果更新用户名，新用户名必须唯一
    async fn update_user(&self, user: User) -> Result<User>;

    /// 删除用户
    ///
    /// # 参数
    /// - `user_id`: 要删除的用户ID
    ///
    /// # 返回
    /// - `Ok(())`: 删除成功
    /// - `Err(...)`: 删除失败，包含具体错误信息
    ///
    /// # 业务规则
    /// - 用户必须存在
    /// - 删除前需要清理相关数据（任务、聊天记录等）
    async fn delete_user(&self, user_id: Uuid) -> Result<()>;

    /// 检查用户是否有权限访问指定资源
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    /// - `resource_id`: 资源ID
    /// - `resource_type`: 资源类型
    ///
    /// # 返回
    /// - `Ok(true)`: 有权限
    /// - `Ok(false)`: 无权限
    /// - `Err(...)`: 检查过程中发生错误
    async fn has_permission(
        &self,
        user_id: Uuid,
        resource_id: Uuid,
        resource_type: &str,
    ) -> Result<bool>;
}

/// 用户领域服务的具体实现
///
/// 通过依赖注入使用仓储接口，实现用户相关的核心业务逻辑
pub struct UserDomainServiceImpl {
    /// 用户仓储接口
    user_repository: Arc<dyn UserRepositoryContract>,

    /// 领域事件发布器
    event_publisher: Arc<crate::events::EventPublisherType>,
}

impl UserDomainServiceImpl {
    /// 创建新的用户领域服务实例
    ///
    /// # 参数
    /// - `user_repository`: 用户仓储接口的实现
    /// - `event_publisher`: 领域事件发布器的实现
    ///
    /// # 返回
    /// 返回用户领域服务实例
    pub fn new(
        user_repository: Arc<dyn UserRepositoryContract>,
        event_publisher: Arc<crate::events::EventPublisherType>,
    ) -> Self {
        info!("🏗️ 创建用户领域服务实例");
        Self {
            user_repository,
            event_publisher,
        }
    }

    /// 验证用户名格式
    ///
    /// # 参数
    /// - `username`: 要验证的用户名
    ///
    /// # 返回
    /// - `Ok(())`: 验证通过
    /// - `Err(...)`: 验证失败
    fn validate_username_format(&self, username: &str) -> Result<()> {
        debug!("验证用户名格式: {}", username);

        if username.is_empty() {
            return Err(AppError::ValidationError("用户名不能为空".to_string()));
        }

        if username.len() < 3 {
            return Err(AppError::ValidationError(
                "用户名长度不能少于3个字符".to_string(),
            ));
        }

        if username.len() > 50 {
            return Err(AppError::ValidationError(
                "用户名长度不能超过50个字符".to_string(),
            ));
        }

        // 检查用户名是否只包含字母、数字和下划线
        if !username.chars().all(|c| (c.is_alphanumeric() || c == '_')) {
            return Err(AppError::ValidationError(
                "用户名只能包含字母、数字和下划线".to_string(),
            ));
        }

        Ok(())
    }

    /// 验证密码强度
    ///
    /// # 参数
    /// - `password`: 要验证的密码
    ///
    /// # 返回
    /// - `Ok(())`: 验证通过
    /// - `Err(...)`: 验证失败
    fn validate_password_strength(&self, password: &str) -> Result<()> {
        debug!("验证密码强度");

        if password.is_empty() {
            return Err(AppError::ValidationError("密码不能为空".to_string()));
        }

        if password.len() < 8 {
            return Err(AppError::ValidationError(
                "密码长度不能少于8个字符".to_string(),
            ));
        }

        if password.len() > 128 {
            return Err(AppError::ValidationError(
                "密码长度不能超过128个字符".to_string(),
            ));
        }

        // 检查密码复杂性：至少包含一个字母和一个数字
        let has_letter = password.chars().any(|c| c.is_alphabetic());
        let has_digit = password.chars().any(|c| c.is_numeric());

        if !has_letter || !has_digit {
            return Err(AppError::ValidationError(
                "密码必须包含至少一个字母和一个数字".to_string(),
            ));
        }

        Ok(())
    }
}

#[async_trait]
impl UserDomainService for UserDomainServiceImpl {
    /// 验证用户名是否可用
    async fn is_username_available(&self, username: &str) -> Result<bool> {
        info!("检查用户名是否可用: {}", username);

        // 首先验证用户名格式
        self.validate_username_format(username)?;

        // 查询数据库检查用户名是否已存在
        match self.user_repository.find_by_username(username).await {
            Ok(Some(_)) => {
                warn!("用户名已存在: {}", username);
                Ok(false)
            }
            Ok(None) => {
                info!("用户名可用: {}", username);
                Ok(true)
            }
            Err(err) => {
                error!("检查用户名可用性时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 验证用户实体的业务规则
    async fn validate_user(&self, user: &User) -> Result<()> {
        info!("验证用户实体业务规则: {}", user.username);

        // 验证用户名格式
        self.validate_username_format(&user.username)?;

        // 验证邮箱格式（如果提供）
        if let Some(email) = &user.email {
            if !email.contains('@') || !email.contains('.') {
                return Err(AppError::ValidationError("邮箱格式无效".to_string()));
            }
        }

        // 验证状态值
        if !["active", "inactive", "suspended"].contains(&user.status.as_str()) {
            return Err(AppError::ValidationError("用户状态值无效".to_string()));
        }

        info!("用户实体验证通过: {}", user.username);
        Ok(())
    }

    /// 根据用户ID获取用户信息
    async fn get_user_by_id(&self, user_id: Uuid) -> Result<Option<User>> {
        info!("根据用户ID获取用户信息: {}", user_id);

        match self.user_repository.find_by_id(user_id).await {
            Ok(user) => {
                if user.is_some() {
                    info!("找到用户: {}", user_id);
                } else {
                    warn!("用户不存在: {}", user_id);
                }
                Ok(user)
            }
            Err(err) => {
                error!("根据用户ID查询用户时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 根据用户名获取用户信息
    async fn get_user_by_username(&self, username: &str) -> Result<Option<User>> {
        info!("根据用户名获取用户信息: {}", username);

        // 验证用户名格式
        self.validate_username_format(username)?;

        match self.user_repository.find_by_username(username).await {
            Ok(user) => {
                if user.is_some() {
                    info!("找到用户: {}", username);
                } else {
                    warn!("用户不存在: {}", username);
                }
                Ok(user)
            }
            Err(err) => {
                error!("根据用户名查询用户时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 创建新用户
    async fn create_user(&self, user: User) -> Result<User> {
        info!("创建新用户: {}", user.username);

        // 验证用户实体
        self.validate_user(&user).await?;

        // 检查用户名是否可用
        if !self.is_username_available(&user.username).await? {
            return Err(AppError::ValidationError(format!(
                "用户名已存在: {}",
                user.username
            )));
        }

        // 创建用户
        match self.user_repository.create(user.clone()).await {
            Ok(created_user) => {
                info!("用户创建成功: {}", created_user.username);

                // 发布用户创建事件
                let event = crate::events::UserCreatedEvent::new(
                    created_user.id,
                    created_user.username.clone(),
                    created_user.email.clone().unwrap_or_default(),
                );

                if let Err(e) = self.event_publisher.publish(event, None).await {
                    warn!("发布用户创建事件失败: {:?}", e);
                    // 注意：事件发布失败不应该影响主要业务流程
                }

                Ok(created_user)
            }
            Err(err) => {
                // 检查是否是用户名已存在的错误（竞态条件）
                if let sea_orm::DbErr::Custom(msg) = &err {
                    if msg == "用户名已存在" {
                        warn!("用户名已存在（竞态条件检测）: {}", user.username);
                        return Err(AppError::UserAlreadyExists(user.username.clone()));
                    }
                }

                // 检查是否是数据库唯一约束违反错误
                match &err {
                    sea_orm::DbErr::Exec(sea_orm::RuntimeErr::SqlxError(sqlx_err)) => {
                        if let Some(db_err) = sqlx_err.as_database_error() {
                            if db_err.code() == Some(std::borrow::Cow::Borrowed("23505")) {
                                warn!("用户名已存在（数据库约束）: {}", user.username);
                                return Err(AppError::UserAlreadyExists(user.username.clone()));
                            }
                        }
                    }
                    sea_orm::DbErr::Query(sea_orm::RuntimeErr::SqlxError(sqlx_err)) => {
                        if let Some(db_err) = sqlx_err.as_database_error() {
                            if db_err.code() == Some(std::borrow::Cow::Borrowed("23505")) {
                                warn!("用户名已存在（数据库约束）: {}", user.username);
                                return Err(AppError::UserAlreadyExists(user.username.clone()));
                            }
                        }
                    }
                    _ => {}
                }

                error!("创建用户时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 更新用户信息
    async fn update_user(&self, user: User) -> Result<User> {
        info!("更新用户信息: {}", user.username);

        // 验证用户实体
        self.validate_user(&user).await?;

        // 检查用户是否存在
        if self.get_user_by_id(user.id).await?.is_none() {
            return Err(AppError::NotFound(format!("用户不存在: {}", user.id)));
        }

        // 如果用户名发生变更，检查新用户名是否可用
        if let Ok(Some(existing_user)) = self.user_repository.find_by_id(user.id).await {
            if existing_user.username != user.username
                && !self.is_username_available(&user.username).await?
            {
                return Err(AppError::ValidationError(format!(
                    "用户名已存在: {}",
                    user.username
                )));
            }
        }

        // 更新用户
        match self.user_repository.update(user).await {
            Ok(updated_user) => {
                info!("用户更新成功: {}", updated_user.username);
                Ok(updated_user)
            }
            Err(err) => {
                error!("更新用户时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 删除用户
    async fn delete_user(&self, user_id: Uuid) -> Result<()> {
        info!("删除用户: {}", user_id);

        // 检查用户是否存在
        if self.get_user_by_id(user_id).await?.is_none() {
            return Err(AppError::NotFound(format!("用户不存在: {user_id}")));
        }

        // 删除用户
        match self.user_repository.delete(user_id).await {
            Ok(_) => {
                info!("用户删除成功: {}", user_id);
                Ok(())
            }
            Err(err) => {
                error!("删除用户时发生错误: {:?}", err);
                Err(AppError::DatabaseError(err.to_string()))
            }
        }
    }

    /// 检查用户是否有权限访问指定资源
    async fn has_permission(
        &self,
        user_id: Uuid,
        resource_id: Uuid,
        resource_type: &str,
    ) -> Result<bool> {
        info!(
            "检查用户权限: user_id={}, resource_id={}, resource_type={}",
            user_id, resource_id, resource_type
        );

        // 检查用户是否存在
        if self.get_user_by_id(user_id).await?.is_none() {
            return Err(AppError::NotFound(format!("用户不存在: {user_id}")));
        }

        // 简单的权限检查逻辑（实际项目中应该更复杂）
        // 这里假设用户对自己的资源有完全权限
        match resource_type {
            "task" | "profile" => {
                // 对于任务和个人资料，用户只能访问自己的
                Ok(user_id == resource_id)
            }
            "public" => {
                // 公共资源所有用户都可以访问
                Ok(true)
            }
            _ => {
                warn!("未知的资源类型: {}", resource_type);
                Ok(false)
            }
        }
    }
}

/// 用户业务规则验证器
///
/// 提供用户相关的业务规则验证功能
pub struct UserBusinessRules;

impl UserBusinessRules {
    /// 验证用户名格式
    ///
    /// # 参数
    /// - `username`: 要验证的用户名
    ///
    /// # 返回
    /// - `Ok(())`: 格式正确
    /// - `Err(...)`: 格式错误
    pub fn validate_username_format(username: &str) -> Result<()> {
        use validator::Validate;

        // 创建临时用户实体进行验证
        let temp_user = User {
            id: Uuid::new_v4(),
            username: username.to_string(),
            email: None,
            password_hash: "temp".to_string(),
            display_name: None,
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        };

        temp_user
            .validate()
            .map_err(|e| app_common::error::AppError::ValidationError(e.to_string()))?;

        Ok(())
    }

    /// 验证密码强度
    ///
    /// # 参数
    /// - `password`: 明文密码
    ///
    /// # 返回
    /// - `Ok(())`: 密码强度符合要求
    /// - `Err(...)`: 密码强度不足
    pub fn validate_password_strength(password: &str) -> Result<()> {
        if password.len() < 8 {
            return Err(app_common::error::AppError::ValidationError(
                "密码长度至少为8个字符".to_string(),
            ));
        }

        if password.len() > 128 {
            return Err(app_common::error::AppError::ValidationError(
                "密码长度不能超过128个字符".to_string(),
            ));
        }

        // 检查是否包含字母
        let has_letter = password.chars().any(|c| c.is_alphabetic());
        // 检查是否包含数字
        let has_digit = password.chars().any(|c| c.is_numeric());

        if !has_letter || !has_digit {
            return Err(app_common::error::AppError::ValidationError(
                "密码必须包含至少一个字母和一个数字".to_string(),
            ));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use async_trait::async_trait;
    use sea_orm::DbErr;
    use std::collections::HashMap;
    use std::sync::Mutex;

    // 模拟用户仓储实现，用于测试
    #[derive(Default)]
    struct MockUserRepository {
        users: Mutex<HashMap<Uuid, User>>,
        users_by_username: Mutex<HashMap<String, User>>,
    }

    #[async_trait]
    impl UserRepositoryContract for MockUserRepository {
        async fn find_by_username(
            &self,
            username: &str,
        ) -> std::result::Result<Option<User>, DbErr> {
            let users = self.users_by_username.lock().unwrap();
            Ok(users.get(username).cloned())
        }

        async fn find_by_id(&self, id: Uuid) -> std::result::Result<Option<User>, DbErr> {
            let users = self.users.lock().unwrap();
            Ok(users.get(&id).cloned())
        }

        async fn create(&self, user: User) -> std::result::Result<User, DbErr> {
            let mut users = self.users.lock().unwrap();
            let mut users_by_username = self.users_by_username.lock().unwrap();

            // 检查用户名是否已存在
            if users_by_username.contains_key(&user.username) {
                return Err(DbErr::Custom("用户名已存在".to_string()));
            }

            users.insert(user.id, user.clone());
            users_by_username.insert(user.username.clone(), user.clone());
            Ok(user)
        }

        async fn update(&self, user: User) -> std::result::Result<User, DbErr> {
            let mut users = self.users.lock().unwrap();
            let mut users_by_username = self.users_by_username.lock().unwrap();

            // 检查用户是否存在
            if !users.contains_key(&user.id) {
                return Err(DbErr::Custom("用户不存在".to_string()));
            }

            // 更新用户信息
            if let Some(old_user) = users.get(&user.id) {
                // 如果用户名发生变化，更新用户名索引
                if old_user.username != user.username {
                    users_by_username.remove(&old_user.username);
                    users_by_username.insert(user.username.clone(), user.clone());
                }
            }

            users.insert(user.id, user.clone());
            Ok(user)
        }

        async fn delete(&self, user_id: Uuid) -> std::result::Result<u64, DbErr> {
            let mut users = self.users.lock().unwrap();
            let mut users_by_username = self.users_by_username.lock().unwrap();

            if let Some(user) = users.remove(&user_id) {
                users_by_username.remove(&user.username);
                Ok(1)
            } else {
                Ok(0)
            }
        }

        async fn count_all(&self) -> std::result::Result<u64, DbErr> {
            let users = self.users.lock().unwrap();
            Ok(users.len() as u64)
        }
    }

    fn create_test_user() -> User {
        User {
            id: Uuid::new_v4(),
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "hashed_password".to_string(),
            display_name: Some("Test User".to_string()),
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
        }
    }

    #[tokio::test]
    async fn test_user_domain_service_create_user() {
        let repository = Arc::new(MockUserRepository::default());
        let event_publisher = Arc::new(crate::events::EventPublisherType::Null(
            crate::events::NullEventPublisher::new(),
        ));
        let service = UserDomainServiceImpl::new(repository, event_publisher);

        let user = create_test_user();
        let result = service.create_user(user.clone()).await;

        assert!(result.is_ok());
        let created_user = result.unwrap();
        assert_eq!(created_user.username, user.username);
        assert_eq!(created_user.email, user.email);
    }

    #[tokio::test]
    async fn test_user_domain_service_duplicate_username() {
        let repository = Arc::new(MockUserRepository::default());
        let event_publisher = Arc::new(crate::events::EventPublisherType::Null(
            crate::events::NullEventPublisher::new(),
        ));
        let service = UserDomainServiceImpl::new(repository, event_publisher);

        let user1 = create_test_user();
        let mut user2 = create_test_user();
        user2.id = Uuid::new_v4(); // 不同的ID
        // 但用户名相同

        // 创建第一个用户应该成功
        assert!(service.create_user(user1).await.is_ok());

        // 创建第二个用户应该失败（用户名重复）
        let result = service.create_user(user2).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_user_domain_service_get_user_by_username() {
        let repository = Arc::new(MockUserRepository::default());
        let event_publisher = Arc::new(crate::events::EventPublisherType::Null(
            crate::events::NullEventPublisher::new(),
        ));
        let service = UserDomainServiceImpl::new(repository, event_publisher);

        let user = create_test_user();
        let username = user.username.clone();

        // 创建用户
        service.create_user(user).await.unwrap();

        // 根据用户名查找用户
        let result = service.get_user_by_username(&username).await;
        assert!(result.is_ok());
        let found_user = result.unwrap();
        assert!(found_user.is_some());
        assert_eq!(found_user.unwrap().username, username);
    }

    #[tokio::test]
    async fn test_user_domain_service_validate_username_format() {
        let repository = Arc::new(MockUserRepository::default());
        let event_publisher = Arc::new(crate::events::EventPublisherType::Null(
            crate::events::NullEventPublisher::new(),
        ));
        let service = UserDomainServiceImpl::new(repository, event_publisher);

        // 测试有效用户名
        assert!(service.validate_username_format("valid_user").is_ok());
        assert!(service.validate_username_format("user123").is_ok());
        assert!(service.validate_username_format("test_user").is_ok());

        // 测试无效用户名
        assert!(service.validate_username_format("").is_err()); // 空用户名
        assert!(service.validate_username_format("ab").is_err()); // 太短
        assert!(service.validate_username_format(&"a".repeat(51)).is_err()); // 太长
        assert!(service.validate_username_format("user@name").is_err()); // 包含非法字符
    }

    #[test]
    fn test_validate_username_format() {
        // 测试有效用户名
        assert!(UserBusinessRules::validate_username_format("valid_user").is_ok());
        assert!(UserBusinessRules::validate_username_format("user123").is_ok());
        assert!(UserBusinessRules::validate_username_format("test-user").is_ok());

        // 测试无效用户名
        assert!(UserBusinessRules::validate_username_format("ab").is_err()); // 太短
        assert!(UserBusinessRules::validate_username_format(&"a".repeat(51)).is_err()); // 太长
    }

    #[test]
    fn test_validate_password_strength() {
        // 测试有效密码
        assert!(UserBusinessRules::validate_password_strength("password123").is_ok());
        assert!(UserBusinessRules::validate_password_strength("MySecure1").is_ok());

        // 测试无效密码
        assert!(UserBusinessRules::validate_password_strength("short1").is_err()); // 太短
        assert!(UserBusinessRules::validate_password_strength("password").is_err()); // 无数字
        assert!(UserBusinessRules::validate_password_strength("12345678").is_err()); // 无字母
    }
}
