//! # WebSocket 实体
//!
//! 定义WebSocket相关的领域实体

use crate::websocket::value_objects::*;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json;
use std::collections::HashMap;
use uuid::Uuid;

/// WebSocket会话实体
///
/// 【功能】: 表示一个WebSocket连接会话，包含连接信息和状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WsSession {
    /// 会话ID（连接ID）
    pub id: ConnectionId,
    /// 用户ID
    pub user_id: Uuid,
    /// 用户名
    pub username: String,
    /// 会话类型
    pub session_type: SessionType,
    /// 连接状态
    pub status: ConnectionStatus,
    /// 连接建立时间
    pub connected_at: DateTime<Utc>,
    /// 最后活跃时间
    pub last_activity: DateTime<Utc>,
    /// 连接质量指标
    pub quality: ConnectionQuality,
    /// 会话元数据
    pub metadata: HashMap<String, String>,
}

impl WsSession {
    /// 创建新的WebSocket会话
    ///
    /// 【参数】:
    /// * `user_id` - 用户ID
    /// * `username` - 用户名
    /// * `session_type` - 会话类型
    pub fn new(user_id: Uuid, username: String, session_type: SessionType) -> Self {
        let now = Utc::now();
        Self {
            id: ConnectionId::new(),
            user_id,
            username,
            session_type,
            status: ConnectionStatus::Connecting,
            connected_at: now,
            last_activity: now,
            quality: ConnectionQuality::default(),
            metadata: HashMap::new(),
        }
    }

    /// 标记会话为已连接
    pub fn mark_connected(&mut self) {
        self.status = ConnectionStatus::Connected;
        self.last_activity = Utc::now();
    }

    /// 标记会话为断开连接
    pub fn mark_disconnected(&mut self) {
        self.status = ConnectionStatus::Disconnected;
    }

    /// 更新最后活跃时间
    pub fn update_activity(&mut self) {
        self.last_activity = Utc::now();
    }

    /// 更新连接质量
    pub fn update_quality(&mut self, quality: ConnectionQuality) {
        self.quality = quality;
        self.last_activity = Utc::now();
    }

    /// 添加元数据
    pub fn add_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
    }

    /// 获取元数据
    pub fn get_metadata(&self, key: &str) -> Option<&String> {
        self.metadata.get(key)
    }

    /// 检查会话是否活跃
    pub fn is_active(&self) -> bool {
        matches!(self.status, ConnectionStatus::Connected)
    }

    /// 获取会话持续时间（秒）
    pub fn duration_seconds(&self) -> i64 {
        (Utc::now() - self.connected_at).num_seconds()
    }

    /// 获取空闲时间（秒）
    pub fn idle_seconds(&self) -> i64 {
        (Utc::now() - self.last_activity).num_seconds()
    }
}

/// WebSocket消息实体
///
/// 【功能】: 表示一个WebSocket消息，包含消息内容和元信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WsMessage {
    /// 消息ID
    pub id: Uuid,
    /// 发送者连接ID
    pub sender_id: Option<ConnectionId>,
    /// 接收者连接ID（None表示广播）
    pub receiver_id: Option<ConnectionId>,
    /// 消息类型
    pub message_type: MessageType,
    /// 消息内容
    pub content: Vec<u8>,
    /// 消息优先级
    pub priority: MessagePriority,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 过期时间
    pub expires_at: Option<DateTime<Utc>>,
    /// 重试次数
    pub retry_count: u32,
    /// 最大重试次数
    pub max_retries: u32,
}

impl WsMessage {
    /// 创建文本消息
    pub fn new_text(content: String, sender_id: Option<ConnectionId>) -> Self {
        Self {
            id: Uuid::new_v4(),
            sender_id,
            receiver_id: None,
            message_type: MessageType::Text,
            content: content.into_bytes(),
            priority: MessagePriority::Normal,
            created_at: Utc::now(),
            expires_at: None,
            retry_count: 0,
            max_retries: 3,
        }
    }

    /// 创建二进制消息
    pub fn new_binary(content: Vec<u8>, sender_id: Option<ConnectionId>) -> Self {
        Self {
            id: Uuid::new_v4(),
            sender_id,
            receiver_id: None,
            message_type: MessageType::Binary,
            content,
            priority: MessagePriority::Normal,
            created_at: Utc::now(),
            expires_at: None,
            retry_count: 0,
            max_retries: 3,
        }
    }

    /// 设置接收者
    pub fn with_receiver(mut self, receiver_id: ConnectionId) -> Self {
        self.receiver_id = Some(receiver_id);
        self
    }

    /// 设置优先级
    pub fn with_priority(mut self, priority: MessagePriority) -> Self {
        self.priority = priority;
        self
    }

    /// 设置过期时间
    pub fn with_expiry(mut self, expires_at: DateTime<Utc>) -> Self {
        self.expires_at = Some(expires_at);
        self
    }

    /// 获取文本内容
    pub fn as_text(&self) -> Option<String> {
        if self.message_type == MessageType::Text {
            String::from_utf8(self.content.clone()).ok()
        } else {
            None
        }
    }

    /// 检查消息是否过期
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            Utc::now() > expires_at
        } else {
            false
        }
    }

    /// 增加重试次数
    pub fn increment_retry(&mut self) -> bool {
        if self.retry_count < self.max_retries {
            self.retry_count += 1;
            true
        } else {
            false
        }
    }

    /// 检查是否可以重试
    pub fn can_retry(&self) -> bool {
        self.retry_count < self.max_retries
    }
}

/// WebSocket实时消息实体
///
/// 【功能】: 表示一个实时更新消息，用于任务、聊天等实时功能
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RealtimeMessage {
    /// 消息ID
    pub id: Uuid,
    /// 消息类型
    pub message_type: RealtimeMessageType,
    /// 消息内容（JSON格式）
    pub content: serde_json::Value,
    /// 发送者用户ID
    pub sender_id: Option<Uuid>,
    /// 发送者用户名
    pub sender_username: Option<String>,
    /// 目标用户ID列表（None表示广播给所有用户）
    pub target_users: Option<Vec<Uuid>>,
    /// 目标会话类型
    pub target_session_type: Option<SessionType>,
    /// 消息优先级
    pub priority: MessagePriority,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 过期时间
    pub expires_at: Option<DateTime<Utc>>,
}

impl RealtimeMessage {
    /// 创建新的实时消息
    pub fn new(
        message_type: RealtimeMessageType,
        content: serde_json::Value,
        sender_id: Option<Uuid>,
        sender_username: Option<String>,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            message_type,
            content,
            sender_id,
            sender_username,
            target_users: None,
            target_session_type: None,
            priority: MessagePriority::Normal,
            created_at: Utc::now(),
            expires_at: None,
        }
    }

    /// 创建任务创建消息
    pub fn task_created(task_data: serde_json::Value, sender_id: Option<Uuid>) -> Self {
        Self::new(
            RealtimeMessageType::TaskCreated,
            serde_json::json!({
                "type": "task_created",
                "data": task_data,
                "timestamp": Utc::now().to_rfc3339()
            }),
            sender_id,
            None,
        )
        .with_priority(MessagePriority::High)
        .with_target_session_type(SessionType::TaskRealtime)
    }

    /// 创建任务更新消息
    pub fn task_updated(task_data: serde_json::Value, sender_id: Option<Uuid>) -> Self {
        Self::new(
            RealtimeMessageType::TaskUpdated,
            serde_json::json!({
                "type": "task_updated",
                "data": task_data,
                "timestamp": Utc::now().to_rfc3339()
            }),
            sender_id,
            None,
        )
        .with_priority(MessagePriority::High)
        .with_target_session_type(SessionType::TaskRealtime)
    }

    /// 创建任务删除消息
    pub fn task_deleted(task_id: u64, sender_id: Option<Uuid>) -> Self {
        Self::new(
            RealtimeMessageType::TaskDeleted,
            serde_json::json!({
                "type": "task_deleted",
                "data": {
                    "id": task_id
                },
                "timestamp": Utc::now().to_rfc3339()
            }),
            sender_id,
            None,
        )
        .with_priority(MessagePriority::High)
        .with_target_session_type(SessionType::TaskRealtime)
    }

    /// 设置目标用户
    pub fn with_target_users(mut self, target_users: Vec<Uuid>) -> Self {
        self.target_users = Some(target_users);
        self
    }

    /// 设置目标会话类型
    pub fn with_target_session_type(mut self, session_type: SessionType) -> Self {
        self.target_session_type = Some(session_type);
        self
    }

    /// 设置优先级
    pub fn with_priority(mut self, priority: MessagePriority) -> Self {
        self.priority = priority;
        self
    }

    /// 设置过期时间
    pub fn with_expiry(mut self, expires_at: DateTime<Utc>) -> Self {
        self.expires_at = Some(expires_at);
        self
    }

    /// 检查消息是否过期
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            Utc::now() > expires_at
        } else {
            false
        }
    }

    /// 转换为JSON字符串
    pub fn to_json_string(&self) -> Result<String, serde_json::Error> {
        serde_json::to_string(&self.content)
    }

    /// 检查是否应该发送给指定用户
    pub fn should_send_to_user(&self, user_id: &Uuid) -> bool {
        match &self.target_users {
            Some(targets) => targets.contains(user_id),
            None => true, // 广播消息发送给所有用户
        }
    }

    /// 检查是否应该发送给指定会话类型
    pub fn should_send_to_session_type(&self, session_type: &SessionType) -> bool {
        match &self.target_session_type {
            Some(target_type) => target_type == session_type,
            None => true, // 没有指定会话类型则发送给所有会话
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_ws_session_creation() {
        let user_id = Uuid::new_v4();
        let username = "test_user".to_string();
        let session_type = SessionType::Chat;

        let session = WsSession::new(user_id, username.clone(), session_type.clone());

        assert_eq!(session.user_id, user_id);
        assert_eq!(session.username, username);
        assert_eq!(session.session_type, session_type);
        assert_eq!(session.status, ConnectionStatus::Connecting);
        assert!(!session.is_active());
    }

    #[test]
    fn test_ws_session_lifecycle() {
        let user_id = Uuid::new_v4();
        let mut session = WsSession::new(user_id, "test".to_string(), SessionType::Chat);

        // 标记为已连接
        session.mark_connected();
        assert!(session.is_active());
        assert_eq!(session.status, ConnectionStatus::Connected);

        // 更新活跃时间
        let old_activity = session.last_activity;
        std::thread::sleep(std::time::Duration::from_millis(1));
        session.update_activity();
        assert!(session.last_activity > old_activity);

        // 标记为断开连接
        session.mark_disconnected();
        assert!(!session.is_active());
        assert_eq!(session.status, ConnectionStatus::Disconnected);
    }

    #[test]
    fn test_ws_message_creation() {
        let sender_id = ConnectionId::new();
        let content = "Hello, World!".to_string();

        let message = WsMessage::new_text(content.clone(), Some(sender_id));

        assert_eq!(message.sender_id, Some(sender_id));
        assert_eq!(message.message_type, MessageType::Text);
        assert_eq!(message.as_text(), Some(content));
        assert_eq!(message.priority, MessagePriority::Normal);
        assert!(!message.is_expired());
        assert!(message.can_retry());
    }

    #[test]
    fn test_ws_message_retry() {
        let mut message = WsMessage::new_text("test".to_string(), None);
        message.max_retries = 2;

        assert!(message.can_retry());
        assert!(message.increment_retry());
        assert_eq!(message.retry_count, 1);

        assert!(message.can_retry());
        assert!(message.increment_retry());
        assert_eq!(message.retry_count, 2);

        assert!(!message.can_retry());
        assert!(!message.increment_retry());
        assert_eq!(message.retry_count, 2);
    }
}
