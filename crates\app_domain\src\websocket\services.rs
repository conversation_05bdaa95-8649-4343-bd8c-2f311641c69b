//! # WebSocket 领域服务接口
//!
//! 定义WebSocket相关的领域服务接口

use crate::websocket::{entities::*, value_objects::*};
use async_trait::async_trait;
use serde_json;
use std::any::Any;
use std::collections::HashMap;
use uuid::Uuid;

/// WebSocket连接管理服务接口
///
/// 【功能】: 定义WebSocket连接的生命周期管理接口
#[async_trait]
pub trait WebSocketConnectionService: Send + Sync {
    /// 获取Any引用用于类型转换
    fn as_any(&self) -> &dyn Any;
    /// 添加新连接
    ///
    /// 【参数】:
    /// * `session` - WebSocket会话
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn add_connection(&self, session: WsSession) -> Result<(), String>;

    /// 移除连接
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn remove_connection(&self, connection_id: &ConnectionId) -> Result<(), String>;

    /// 获取连接信息
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    ///
    /// 【返回值】: Option<WsSession> - 连接会话信息
    async fn get_connection(&self, connection_id: &ConnectionId) -> Option<WsSession>;

    /// 获取用户的所有连接
    ///
    /// 【参数】:
    /// * `user_id` - 用户ID
    ///
    /// 【返回值】: Vec<WsSession> - 用户的所有连接会话
    async fn get_user_connections(&self, user_id: &Uuid) -> Vec<WsSession>;

    /// 获取所有活跃连接
    ///
    /// 【返回值】: Vec<WsSession> - 所有活跃连接会话
    async fn get_active_connections(&self) -> Vec<WsSession>;

    /// 更新连接活跃时间
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn update_activity(&self, connection_id: &ConnectionId) -> Result<(), String>;

    /// 更新连接质量
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    /// * `quality` - 连接质量指标
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn update_quality(
        &self,
        connection_id: &ConnectionId,
        quality: ConnectionQuality,
    ) -> Result<(), String>;

    /// 清理空闲连接
    ///
    /// 【参数】:
    /// * `idle_timeout_seconds` - 空闲超时时间（秒）
    ///
    /// 【返回值】: usize - 清理的连接数量
    async fn cleanup_idle_connections(&self, idle_timeout_seconds: i64) -> usize;

    /// 注册连接的消息发送通道
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    /// * `sender` - 消息发送通道
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn register_sender(
        &self,
        connection_id: &ConnectionId,
        sender_id: String,
    ) -> Result<(), String>;

    /// 更新连接的消息发送通道
    ///
    /// 【参数】:
    /// * `connection_id` - 连接ID
    /// * `sender_info` - 发送通道信息（由具体实现决定类型）
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn update_connection_sender(
        &self,
        connection_id: &ConnectionId,
        sender_info: String,
    ) -> Result<(), String>;
}

/// WebSocket消息分发服务接口
///
/// 【功能】: 定义WebSocket消息的分发和广播接口
#[async_trait]
pub trait WebSocketMessageService: Send + Sync {
    /// 发送消息给指定连接
    ///
    /// 【参数】:
    /// * `connection_id` - 目标连接ID
    /// * `message` - 要发送的消息
    ///
    /// 【返回值】: Result<(), String> - 操作结果
    async fn send_to_connection(
        &self,
        connection_id: &ConnectionId,
        message: WsMessage,
    ) -> Result<(), String>;

    /// 发送消息给指定用户的所有连接
    ///
    /// 【参数】:
    /// * `user_id` - 目标用户ID
    /// * `message` - 要发送的消息
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn send_to_user(&self, user_id: &Uuid, message: WsMessage) -> Result<usize, String>;

    /// 广播消息给所有连接
    ///
    /// 【参数】:
    /// * `message` - 要广播的消息
    /// * `exclude_sender` - 是否排除发送者
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_to_all(
        &self,
        message: WsMessage,
        exclude_sender: bool,
    ) -> Result<usize, String>;

    /// 广播消息给指定用户列表
    ///
    /// 【参数】:
    /// * `user_ids` - 目标用户ID列表
    /// * `message` - 要广播的消息
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_to_users(
        &self,
        user_ids: &[Uuid],
        message: WsMessage,
    ) -> Result<usize, String>;

    /// 广播消息给指定会话类型的连接
    ///
    /// 【参数】:
    /// * `session_type` - 目标会话类型
    /// * `message` - 要广播的消息
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_to_session_type(
        &self,
        session_type: &SessionType,
        message: WsMessage,
    ) -> Result<usize, String>;
}

/// WebSocket统计服务接口
///
/// 【功能】: 定义WebSocket连接和消息的统计接口
#[async_trait]
pub trait WebSocketStatsService: Send + Sync {
    /// 获取连接统计信息
    ///
    /// 【返回值】: WebSocketStats - 连接统计信息
    async fn get_connection_stats(&self) -> WebSocketStats;

    /// 获取消息统计信息
    ///
    /// 【返回值】: MessageStats - 消息统计信息
    async fn get_message_stats(&self) -> MessageStats;

    /// 记录消息发送
    ///
    /// 【参数】:
    /// * `message_type` - 消息类型
    /// * `size_bytes` - 消息大小（字节）
    async fn record_message_sent(&self, message_type: &MessageType, size_bytes: usize);

    /// 记录消息接收
    ///
    /// 【参数】:
    /// * `message_type` - 消息类型
    /// * `size_bytes` - 消息大小（字节）
    async fn record_message_received(&self, message_type: &MessageType, size_bytes: usize);

    /// 记录连接事件
    ///
    /// 【参数】:
    /// * `event_type` - 事件类型
    async fn record_connection_event(&self, event_type: &ConnectionEventType);
}

/// WebSocket实时消息服务接口
///
/// 【功能】: 定义实时消息的广播和分发接口，专门用于任务、聊天等实时功能
#[async_trait]
pub trait WebSocketRealtimeService: Send + Sync {
    /// 广播实时消息给所有连接
    ///
    /// 【参数】:
    /// * `message` - 要广播的实时消息
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_realtime_message(&self, message: RealtimeMessage) -> Result<usize, String>;

    /// 发送实时消息给指定用户
    ///
    /// 【参数】:
    /// * `user_id` - 目标用户ID
    /// * `message` - 要发送的实时消息
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn send_realtime_to_user(
        &self,
        user_id: &Uuid,
        message: RealtimeMessage,
    ) -> Result<usize, String>;

    /// 发送实时消息给指定用户列表
    ///
    /// 【参数】:
    /// * `user_ids` - 目标用户ID列表
    /// * `message` - 要发送的实时消息
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn send_realtime_to_users(
        &self,
        user_ids: &[Uuid],
        message: RealtimeMessage,
    ) -> Result<usize, String>;

    /// 发送实时消息给指定会话类型的连接
    ///
    /// 【参数】:
    /// * `session_type` - 目标会话类型
    /// * `message` - 要发送的实时消息
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn send_realtime_to_session_type(
        &self,
        session_type: &SessionType,
        message: RealtimeMessage,
    ) -> Result<usize, String>;

    /// 广播任务创建消息
    ///
    /// 【参数】:
    /// * `task_data` - 任务数据（JSON格式）
    /// * `sender_id` - 发送者用户ID
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_task_created(
        &self,
        task_data: serde_json::Value,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String>;

    /// 广播任务更新消息
    ///
    /// 【参数】:
    /// * `task_data` - 任务数据（JSON格式）
    /// * `sender_id` - 发送者用户ID
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_task_updated(
        &self,
        task_data: serde_json::Value,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String>;

    /// 广播任务删除消息
    ///
    /// 【参数】:
    /// * `task_id` - 任务ID
    /// * `sender_id` - 发送者用户ID
    ///
    /// 【返回值】: Result<usize, String> - 成功发送的连接数量
    async fn broadcast_task_deleted(
        &self,
        task_id: u64,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String>;
}

/// WebSocket连接统计信息
///
/// 【功能】: 表示WebSocket连接的统计数据
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct WebSocketStats {
    /// 当前活跃连接数
    pub active_connections: usize,
    /// 总连接数（历史累计）
    pub total_connections: u64,
    /// 唯一活跃用户数
    pub unique_users: usize,
    /// 按会话类型分组的连接数
    pub connections_by_type: HashMap<SessionType, usize>,
    /// 平均连接持续时间（秒）
    pub avg_connection_duration: f64,
    /// 连接成功率（0.0-1.0）
    pub connection_success_rate: f64,
    /// 失败连接数
    pub failed_connections: u64,
}

/// WebSocket消息统计信息
///
/// 【功能】: 表示WebSocket消息的统计数据
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct MessageStats {
    /// 总发送消息数
    pub total_sent: u64,
    /// 总接收消息数
    pub total_received: u64,
    /// 按类型分组的消息数
    pub messages_by_type: HashMap<MessageType, u64>,
    /// 总传输字节数
    pub total_bytes: u64,
    /// 平均消息大小（字节）
    pub avg_message_size: f64,
    /// 消息发送成功率
    pub message_success_rate: f64,
}

/// 连接事件类型
///
/// 【功能】: 定义连接相关的事件类型
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub enum ConnectionEventType {
    /// 连接建立
    Connected,
    /// 连接断开
    Disconnected,
    /// 连接错误
    Error,
    /// 重连
    Reconnected,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_websocket_stats_creation() {
        let stats = WebSocketStats {
            active_connections: 10,
            total_connections: 100,
            unique_users: 8,
            connections_by_type: HashMap::new(),
            avg_connection_duration: 300.0,
            connection_success_rate: 95.5,
            failed_connections: 5,
        };

        assert_eq!(stats.active_connections, 10);
        assert_eq!(stats.total_connections, 100);
        assert_eq!(stats.unique_users, 8);
    }

    #[test]
    fn test_message_stats_creation() {
        let stats = MessageStats {
            total_sent: 1000,
            total_received: 950,
            messages_by_type: HashMap::new(),
            total_bytes: 50000,
            avg_message_size: 50.0,
            message_success_rate: 98.5,
        };

        assert_eq!(stats.total_sent, 1000);
        assert_eq!(stats.total_received, 950);
        assert_eq!(stats.total_bytes, 50000);
    }

    #[test]
    fn test_connection_event_type() {
        let event = ConnectionEventType::Connected;
        assert_eq!(event, ConnectionEventType::Connected);

        let error_event = ConnectionEventType::Error;
        assert_ne!(event, error_event);
    }
}
