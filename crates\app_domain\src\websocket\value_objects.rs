//! # WebSocket 值对象
//!
//! 定义WebSocket相关的值对象和枚举类型

use serde::{Deserialize, Serialize};
use std::fmt;
use uuid::Uuid;

/// WebSocket连接ID
///
/// 【功能】: 唯一标识一个WebSocket连接
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct ConnectionId(pub Uuid);

impl ConnectionId {
    /// 创建新的连接ID
    pub fn new() -> Self {
        Self(Uuid::new_v4())
    }

    /// 从UUID创建连接ID
    pub fn from_uuid(uuid: Uuid) -> Self {
        Self(uuid)
    }

    /// 获取内部UUID
    pub fn as_uuid(&self) -> Uuid {
        self.0
    }
}

impl Default for ConnectionId {
    fn default() -> Self {
        Self::new()
    }
}

impl fmt::Display for ConnectionId {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// WebSocket连接状态
///
/// 【功能】: 表示WebSocket连接的当前状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConnectionStatus {
    /// 连接中
    Connecting,
    /// 已连接
    Connected,
    /// 断开连接中
    Disconnecting,
    /// 已断开
    Disconnected,
    /// 连接错误
    Error(String),
}

impl Default for ConnectionStatus {
    fn default() -> Self {
        Self::Connecting
    }
}

/// WebSocket消息类型
///
/// 【功能】: 定义WebSocket支持的消息类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MessageType {
    /// 文本消息
    Text,
    /// 二进制消息
    Binary,
    /// Ping消息
    Ping,
    /// Pong消息
    Pong,
    /// 关闭消息
    Close,
}

/// WebSocket消息优先级
///
/// 【功能】: 定义消息的优先级，用于消息队列排序
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum MessagePriority {
    /// 低优先级
    Low = 0,
    /// 普通优先级
    Normal = 1,
    /// 高优先级
    High = 2,
    /// 紧急优先级
    Critical = 3,
}

impl Default for MessagePriority {
    fn default() -> Self {
        Self::Normal
    }
}

impl std::fmt::Display for MessagePriority {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MessagePriority::Low => write!(f, "low"),
            MessagePriority::Normal => write!(f, "normal"),
            MessagePriority::High => write!(f, "high"),
            MessagePriority::Critical => write!(f, "critical"),
        }
    }
}

/// WebSocket会话类型
///
/// 【功能】: 定义不同类型的WebSocket会话
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum SessionType {
    /// 聊天会话
    Chat,
    /// 通知会话
    Notification,
    /// 监控会话
    Monitoring,
    /// 管理会话
    Admin,
    /// 任务实时更新会话
    TaskRealtime,
}

/// WebSocket实时消息类型
///
/// 【功能】: 定义WebSocket实时更新支持的消息类型
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RealtimeMessageType {
    /// 聊天消息
    Chat,
    /// 系统消息
    System,
    /// 用户加入
    UserJoined,
    /// 用户离开
    UserLeft,
    /// 错误消息
    Error,
    /// Ping消息
    Ping,
    /// Pong消息
    Pong,
    /// 任务创建
    TaskCreated,
    /// 任务更新
    TaskUpdated,
    /// 任务删除
    TaskDeleted,
    /// 任务状态变更
    TaskStatusChanged,
    /// 任务列表刷新
    TaskListRefresh,
    /// 获取在线用户请求
    GetUsers,
    /// 在线用户列表响应
    UsersList,
}

impl std::fmt::Display for RealtimeMessageType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            RealtimeMessageType::Chat => write!(f, "chat"),
            RealtimeMessageType::System => write!(f, "system"),
            RealtimeMessageType::UserJoined => write!(f, "user_joined"),
            RealtimeMessageType::UserLeft => write!(f, "user_left"),
            RealtimeMessageType::Error => write!(f, "error"),
            RealtimeMessageType::Ping => write!(f, "ping"),
            RealtimeMessageType::Pong => write!(f, "pong"),
            RealtimeMessageType::TaskCreated => write!(f, "task_created"),
            RealtimeMessageType::TaskUpdated => write!(f, "task_updated"),
            RealtimeMessageType::TaskDeleted => write!(f, "task_deleted"),
            RealtimeMessageType::TaskStatusChanged => write!(f, "task_status_changed"),
            RealtimeMessageType::TaskListRefresh => write!(f, "task_list_refresh"),
            RealtimeMessageType::GetUsers => write!(f, "get_users"),
            RealtimeMessageType::UsersList => write!(f, "users_list"),
        }
    }
}

impl Default for SessionType {
    fn default() -> Self {
        Self::Chat
    }
}

impl std::fmt::Display for SessionType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            SessionType::Chat => write!(f, "chat"),
            SessionType::Notification => write!(f, "notification"),
            SessionType::Monitoring => write!(f, "monitoring"),
            SessionType::Admin => write!(f, "admin"),
            SessionType::TaskRealtime => write!(f, "task_realtime"),
        }
    }
}

/// WebSocket连接质量指标
///
/// 【功能】: 表示连接的质量指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConnectionQuality {
    /// 延迟（毫秒）
    pub latency_ms: u64,
    /// 丢包率（百分比）
    pub packet_loss_rate: f64,
    /// 连接稳定性评分（0-100）
    pub stability_score: u8,
}

impl Default for ConnectionQuality {
    fn default() -> Self {
        Self {
            latency_ms: 0,
            packet_loss_rate: 0.0,
            stability_score: 100,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_connection_id_creation() {
        let id1 = ConnectionId::new();
        let id2 = ConnectionId::new();

        // 每次创建的ID应该不同
        assert_ne!(id1, id2);

        // 可以转换为UUID
        let uuid = id1.as_uuid();
        let id3 = ConnectionId::from_uuid(uuid);
        assert_eq!(id1, id3);
    }

    #[test]
    fn test_connection_status() {
        let status = ConnectionStatus::default();
        assert_eq!(status, ConnectionStatus::Connecting);

        let error_status = ConnectionStatus::Error("连接失败".to_string());
        assert!(matches!(error_status, ConnectionStatus::Error(_)));
    }

    #[test]
    fn test_message_priority_ordering() {
        let low = MessagePriority::Low;
        let normal = MessagePriority::Normal;
        let high = MessagePriority::High;
        let critical = MessagePriority::Critical;

        assert!(low < normal);
        assert!(normal < high);
        assert!(high < critical);
    }

    #[test]
    fn test_connection_quality() {
        let quality = ConnectionQuality::default();
        assert_eq!(quality.latency_ms, 0);
        assert_eq!(quality.packet_loss_rate, 0.0);
        assert_eq!(quality.stability_score, 100);
    }
}
