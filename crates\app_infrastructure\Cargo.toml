[package]
name = "app_infrastructure"
version = "0.1.0"
edition = "2024"
license = "MIT OR Apache-2.0"
authors = ["Rust学习者"]
description = "基础设施层：数据库、缓存等具体实现"

[dependencies]
# 内部依赖
app_domain = { workspace = true }
app_common = { workspace = true }
app_interfaces = { workspace = true }
migration = { path = "../../migration" }

# SeaORM 数据库 - 使用workspace版本
sea-orm = { workspace = true }
sea-orm-migration = { workspace = true }

# SQLx PostgreSQL连接池支持
sqlx = { workspace = true }

# DragonflyDB/Redis缓存支持
fred = { workspace = true }
url = "2.5"

# 弹性组件支持 - 熔断器和限流器
failsafe = { workspace = true }
tower_governor = { workspace = true }

# 异步支持
async-trait = { workspace = true }

# 日志
tracing = { workspace = true }
log = { workspace = true }

# 时间处理
chrono = { workspace = true }

# UUID 支持
uuid = { workspace = true }

# 序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 错误处理
anyhow = { workspace = true }
thiserror = { workspace = true }

# WebSocket 支持
axum = { workspace = true }
tokio = { workspace = true }
futures-util = "0.3"
parking_lot = "0.12"
rand = { workspace = true }

# 数据库迁移 - 已移除对原项目migration的依赖，使用新架构的迁移器

[dev-dependencies]
# 测试运行时
tokio = { workspace = true }
# 环境变量加载（测试需要）
dotenvy = "0.15"
