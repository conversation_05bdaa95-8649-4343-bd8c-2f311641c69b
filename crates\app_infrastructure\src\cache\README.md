# 多层缓存策略实现

## 概述

本模块实现了企业级聊天室应用的多层缓存策略，基于DragonflyDB提供高性能、可扩展的缓存解决方案。支持热数据、温数据、冷数据的分层管理，根据数据访问频率和业务重要性设置不同的TTL和缓存层级。

## 架构设计

### 缓存层级

| 层级 | 前缀 | 默认TTL | 最大TTL | 适用场景 |
|------|------|---------|---------|----------|
| 热数据 (Hot) | `hot:` | 5分钟 | 15分钟 | 用户会话、在线状态、实时数据 |
| 温数据 (Warm) | `warm:` | 30分钟 | 2小时 | 聊天室成员、权限信息、统计数据 |
| 冷数据 (Cold) | `cold:` | 4小时 | 24小时 | 系统配置、静态资源、元数据 |

### 核心组件

```
crates/app_infrastructure/src/cache/
├── config.rs              # 缓存配置管理
├── client_manager.rs      # DragonflyDB客户端管理
├── service.rs             # 基础缓存服务接口
├── multi_tier.rs          # 多层缓存策略实现
├── examples.rs            # 使用示例
├── mod.rs                 # 模块导出
└── README.md              # 本文档
```

## 快速开始

### 1. 基本使用

```rust
use app_infrastructure::cache::{
    CacheConfig, MultiTierCacheService, MultiTierCacheConfig,
    CacheTier, create_multi_tier_cache_service
};

// 创建缓存配置
let cache_config = CacheConfig::default();
let multi_tier_config = MultiTierCacheConfig::default();

// 创建多层缓存服务
let cache_service = create_multi_tier_cache_service(
    cache_config, 
    multi_tier_config
).await?;

// 设置热数据（用户会话）
cache_service.set_with_tier(
    CacheTier::Hot,
    "user:session:12345",
    &user_session,
    Some(Duration::from_secs(10 * 60)) // 10分钟
).await?;

// 获取数据
let session: Option<UserSession> = cache_service
    .get_with_tier(CacheTier::Hot, "user:session:12345")
    .await?;
```

### 2. 智能缓存操作

```rust
// 使用带前缀的键，自动识别层级
cache_service.set("hot:user:online:12345", &"online", None).await?;
cache_service.set("warm:room:members:1001", &members, None).await?;
cache_service.set("cold:system:config", &config, None).await?;

// 智能获取（自动检测层级）
let value: Option<String> = cache_service.smart_get("hot:user:online:12345").await?;
```

## 使用场景

### 热数据层 (Hot Tier)

**适用场景：**
- 用户会话管理
- 在线状态跟踪
- 实时消息缓存
- 频繁访问的用户数据

**特点：**
- 高频访问（每秒-每分钟级别）
- 短TTL（5-15分钟）
- 快速响应要求

**示例：**
```rust
// 用户会话
let session_key = format!("user:session:{}", user_id);
cache_service.set_with_tier(
    CacheTier::Hot,
    &session_key,
    &user_session,
    Some(Duration::from_secs(10 * 60))
).await?;

// 在线状态
let online_key = format!("user:online:{}", user_id);
cache_service.set_with_tier(
    CacheTier::Hot,
    &online_key,
    &true,
    Some(Duration::from_secs(5 * 60))
).await?;
```

### 温数据层 (Warm Tier)

**适用场景：**
- 聊天室成员列表
- 用户权限信息
- 消息统计数据
- 中频访问的业务数据

**特点：**
- 中频访问（每小时级别）
- 中等TTL（30分钟-2小时）
- 平衡性能和一致性

**示例：**
```rust
// 聊天室成员
let members_key = format!("room:members:{}", room_id);
cache_service.set_with_tier(
    CacheTier::Warm,
    &members_key,
    &room_members,
    Some(Duration::from_secs(60 * 60)) // 1小时
).await?;

// 用户权限
let permission_key = format!("user:permissions:{}", user_id);
cache_service.set_with_tier(
    CacheTier::Warm,
    &permission_key,
    &user_permissions,
    Some(Duration::from_secs(30 * 60)) // 30分钟
).await?;
```

### 冷数据层 (Cold Tier)

**适用场景：**
- 系统配置信息
- 静态资源元数据
- 不常变化的参考数据
- 低频访问的历史数据

**特点：**
- 低频访问（每天级别）
- 长TTL（4-24小时）
- 减少数据库压力

**示例：**
```rust
// 系统配置
let config_key = format!("system:config:{}", config_name);
cache_service.set_with_tier(
    CacheTier::Cold,
    &config_key,
    &system_config,
    Some(Duration::from_secs(12 * 60 * 60)) // 12小时
).await?;

// 静态资源
let resource_key = format!("resource:metadata:{}", resource_id);
cache_service.set_with_tier(
    CacheTier::Cold,
    &resource_key,
    &resource_metadata,
    Some(Duration::from_secs(24 * 60 * 60)) // 24小时
).await?;
```

## 高级特性

### 1. 自动层级检测

系统支持根据键前缀自动检测缓存层级：

```rust
// 自动识别为热数据
cache_service.set("hot:user:session:123", &session, None).await?;

// 自动识别为温数据
cache_service.set("warm:room:members:456", &members, None).await?;

// 自动识别为冷数据
cache_service.set("cold:system:config:789", &config, None).await?;
```

### 2. TTL自动调整

系统会自动限制TTL不超过层级最大值：

```rust
// 尝试设置30分钟TTL给热数据，会被自动限制为15分钟
cache_service.set_with_tier(
    CacheTier::Hot,
    "user:session:123",
    &session,
    Some(Duration::from_secs(30 * 60)) // 会被限制为15分钟
).await?;
```

### 3. 缓存统计监控

```rust
// 获取统计信息
let stats = cache_service.get_stats().await;

println!("总操作数: {}", stats.total_operations);
println!("总命中率: {:.2}%", stats.hit_rate * 100.0);
println!("热数据层命中率: {:.2}%", 
    stats.hot_tier_stats.hits as f64 / stats.hot_tier_stats.reads as f64 * 100.0);
```

## 配置选项

### MultiTierCacheConfig

```rust
let config = MultiTierCacheConfig {
    auto_tier_detection: true,           // 启用自动层级检测
    auto_ttl_adjustment: true,           // 启用TTL自动调整
    stats_collection_interval: Duration::from_secs(60), // 统计收集间隔
    enable_cache_warming: false,         // 启用缓存预热
};
```

## 性能优化建议

### 1. 键命名规范

- 使用层级前缀：`hot:`、`warm:`、`cold:`
- 采用层次结构：`{tier}:{module}:{entity}:{id}`
- 避免过长的键名

### 2. TTL设置策略

- 热数据：根据业务需求设置较短TTL
- 温数据：平衡一致性和性能
- 冷数据：设置较长TTL减少数据库压力

### 3. 批量操作

```rust
// 批量设置
let pairs = vec![
    ("hot:user:1", &user1),
    ("hot:user:2", &user2),
    ("hot:user:3", &user3),
];
cache_service.mset(&pairs, Some(Duration::from_secs(10 * 60))).await?;

// 批量获取
let keys = vec!["hot:user:1", "hot:user:2", "hot:user:3"];
let results: Vec<Option<User>> = cache_service.mget(&keys).await?;
```

## 监控和调试

### 1. 日志记录

系统使用tracing库记录详细的操作日志：

```rust
// 启用调试日志
RUST_LOG=debug cargo run
```

### 2. 统计信息

定期检查缓存统计信息，优化缓存策略：

```rust
let stats = cache_service.get_stats().await;
if stats.hit_rate < 0.8 {
    // 命中率低于80%，需要优化缓存策略
}
```

## 测试

运行缓存相关测试：

```bash
# 运行所有缓存测试
cargo test --lib -p app_infrastructure cache

# 运行多层缓存测试
cargo test --lib -p app_infrastructure cache::multi_tier_test

# 跳过需要Redis服务器的测试
SKIP_REDIS_TESTS=1 cargo test --lib -p app_infrastructure cache
```

## 故障排除

### 常见问题

1. **连接失败**：检查DragonflyDB服务是否运行
2. **TTL异常**：确认TTL设置在层级限制范围内
3. **性能问题**：检查缓存命中率和网络延迟

### 调试技巧

1. 启用详细日志：`RUST_LOG=debug`
2. 检查连接池状态：`cache_manager.get_pool_stats()`
3. 监控缓存统计：定期调用`get_stats()`

## 最佳实践

1. **合理选择层级**：根据数据访问频率选择合适的缓存层级
2. **设置合适的TTL**：平衡数据一致性和性能
3. **使用批量操作**：提高网络效率
4. **监控缓存指标**：定期检查命中率和性能指标
5. **处理缓存失效**：实现优雅的缓存降级策略
