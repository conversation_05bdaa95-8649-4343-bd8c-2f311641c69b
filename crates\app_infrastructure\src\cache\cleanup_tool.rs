//! # 缓存清理工具
//!
//! 专门用于清理 fred 版本升级导致的不兼容缓存数据
//! 提供命令行工具和程序化接口

use super::{
    client_manager::CacheClientManager,
    config::CacheConfig,
    service::{ DragonflyCache, CacheService },
};
use anyhow::{ Result as AnyhowResult, anyhow };
use std::sync::Arc;
use tracing::{ info, error };

/// 缓存清理工具
///
/// 【目的】: 提供专门的工具来清理 fred 版本升级导致的不兼容缓存数据
pub struct CacheCleanupTool {
    /// 缓存服务实例
    cache_service: Arc<DragonflyCache>,
}

impl CacheCleanupTool {
    /// 创建新的缓存清理工具
    ///
    /// 【参数】:
    /// - config: 缓存配置
    ///
    /// 【返回】: 缓存清理工具实例
    pub async fn new(config: CacheConfig) -> AnyhowResult<Self> {
        info!("🔧 创建缓存清理工具...");

        // 创建缓存客户端管理器
        let manager = Arc::new(CacheClientManager::new(config).await?);

        // 创建缓存服务
        let cache_service = Arc::new(DragonflyCache::new(manager));

        Ok(Self { cache_service })
    }

    /// 执行完整的缓存清理流程
    ///
    /// 【功能】:
    /// 1. 扫描所有缓存键
    /// 2. 检测不兼容的数据
    /// 3. 清理不兼容的缓存键
    /// 4. 生成清理报告
    ///
    /// 【返回】: 清理报告
    pub async fn run_full_cleanup(&self) -> AnyhowResult<CleanupReport> {
        info!("🚀 开始执行完整的缓存清理流程...");

        let start_time = std::time::Instant::now();

        // 执行清理
        let cleaned_count = self.cache_service.cleanup_incompatible_cache_data().await?;

        let duration = start_time.elapsed();

        let report = CleanupReport {
            cleaned_keys_count: cleaned_count,
            duration_ms: duration.as_millis() as u64,
            success: true,
            error_message: None,
        };

        info!("✅ 缓存清理流程完成: {:?}", report);

        Ok(report)
    }

    /// 检查缓存健康状态
    ///
    /// 【返回】: 健康检查结果
    pub async fn health_check(&self) -> AnyhowResult<HealthCheckResult> {
        info!("🔍 执行缓存健康检查...");

        let is_healthy = self.cache_service.health_check().await;

        let result = HealthCheckResult {
            is_healthy,
            connection_status: if is_healthy {
                "Connected".to_string()
            } else {
                "Disconnected".to_string()
            },
            uptime_seconds: 0, // 暂时设为0，因为 DragonflyCache 没有 uptime 方法
        };

        info!("健康检查结果: {:?}", result);

        Ok(result)
    }

    /// 获取缓存统计信息
    ///
    /// 【返回】: 缓存统计信息
    pub async fn get_cache_stats(&self) -> AnyhowResult<CacheStats> {
        info!("📊 获取缓存统计信息...");

        let pool_stats = self.cache_service.get_pool_stats().await;

        let stats = CacheStats {
            total_connections: pool_stats.total_connections,
            active_connections: pool_stats.active_connections,
            connection_errors: pool_stats.connection_errors,
            is_healthy: pool_stats.is_healthy,
        };

        info!("缓存统计信息: {:?}", stats);

        Ok(stats)
    }

    /// 测试缓存读写功能
    ///
    /// 【返回】: 测试结果
    pub async fn test_cache_operations(&self) -> AnyhowResult<TestResult> {
        info!("🧪 测试缓存读写功能...");

        let test_key = "fred_10_1_compatibility_test";
        let test_value =
            serde_json::json!({
            "message": "Fred 10.1 兼容性测试",
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "version": "10.1"
        });

        // 测试写入
        match
            self.cache_service.set(
                test_key,
                &test_value,
                Some(std::time::Duration::from_secs(60))
            ).await
        {
            Ok(_) => {
                info!("✅ 缓存写入测试成功");

                // 测试读取
                match self.cache_service.get::<serde_json::Value>(test_key).await {
                    Ok(Some(retrieved_value)) => {
                        if retrieved_value == test_value {
                            info!("✅ 缓存读取测试成功");

                            // 清理测试数据
                            let _ = self.cache_service.delete(test_key).await;

                            Ok(TestResult {
                                success: true,
                                error_message: None,
                                write_success: true,
                                read_success: true,
                            })
                        } else {
                            error!("❌ 缓存数据不匹配");
                            Ok(TestResult {
                                success: false,
                                error_message: Some("缓存数据不匹配".to_string()),
                                write_success: true,
                                read_success: false,
                            })
                        }
                    }
                    Ok(None) => {
                        error!("❌ 缓存读取失败：数据不存在");
                        Ok(TestResult {
                            success: false,
                            error_message: Some("缓存读取失败：数据不存在".to_string()),
                            write_success: true,
                            read_success: false,
                        })
                    }
                    Err(e) => {
                        error!("❌ 缓存读取失败: {}", e);
                        Ok(TestResult {
                            success: false,
                            error_message: Some(format!("缓存读取失败: {}", e)),
                            write_success: true,
                            read_success: false,
                        })
                    }
                }
            }
            Err(e) => {
                error!("❌ 缓存写入失败: {}", e);
                Ok(TestResult {
                    success: false,
                    error_message: Some(format!("缓存写入失败: {}", e)),
                    write_success: false,
                    read_success: false,
                })
            }
        }
    }
}

/// 清理报告
#[derive(Debug, Clone)]
pub struct CleanupReport {
    /// 清理的键数量
    pub cleaned_keys_count: u64,
    /// 清理耗时（毫秒）
    pub duration_ms: u64,
    /// 是否成功
    pub success: bool,
    /// 错误信息（如果有）
    pub error_message: Option<String>,
}

/// 健康检查结果
#[derive(Debug, Clone)]
pub struct HealthCheckResult {
    /// 是否健康
    pub is_healthy: bool,
    /// 连接状态
    pub connection_status: String,
    /// 运行时间（秒）
    pub uptime_seconds: u64,
}

/// 缓存统计信息
#[derive(Debug, Clone)]
pub struct CacheStats {
    /// 总连接数
    pub total_connections: u32,
    /// 活跃连接数
    pub active_connections: u32,
    /// 连接错误次数
    pub connection_errors: u64,
    /// 是否健康
    pub is_healthy: bool,
}

/// 测试结果
#[derive(Debug, Clone)]
pub struct TestResult {
    /// 是否成功
    pub success: bool,
    /// 错误信息（如果有）
    pub error_message: Option<String>,
    /// 写入是否成功
    pub write_success: bool,
    /// 读取是否成功
    pub read_success: bool,
}

/// 命令行清理工具
///
/// 【用途】: 提供命令行接口来执行缓存清理
pub async fn run_cleanup_command(config: CacheConfig) -> AnyhowResult<()> {
    println!("🧹 Fred 10.1 缓存兼容性清理工具");
    println!("================================");

    let tool = CacheCleanupTool::new(config).await?;

    // 1. 健康检查
    println!("\n1. 执行健康检查...");
    let health = tool.health_check().await?;
    println!("   连接状态: {}", health.connection_status);
    println!("   运行时间: {} 秒", health.uptime_seconds);

    if !health.is_healthy {
        return Err(anyhow!("缓存服务不健康，无法执行清理"));
    }

    // 2. 获取统计信息
    println!("\n2. 获取缓存统计信息...");
    let stats = tool.get_cache_stats().await?;
    println!("   总连接数: {}", stats.total_connections);
    println!("   活跃连接数: {}", stats.active_connections);
    println!("   连接错误次数: {}", stats.connection_errors);

    // 3. 测试缓存功能
    println!("\n3. 测试缓存读写功能...");
    let test_result = tool.test_cache_operations().await?;
    if test_result.success {
        println!("   ✅ 缓存功能测试通过");
    } else {
        println!("   ❌ 缓存功能测试失败: {:?}", test_result.error_message);
    }

    // 4. 执行清理
    println!("\n4. 执行缓存清理...");
    let report = tool.run_full_cleanup().await?;
    println!("   清理的键数量: {}", report.cleaned_keys_count);
    println!("   清理耗时: {} 毫秒", report.duration_ms);

    if report.cleaned_keys_count > 0 {
        println!("\n✅ 清理完成！共清理了 {} 个不兼容的缓存键", report.cleaned_keys_count);
    } else {
        println!("\n✅ 清理完成！未发现不兼容的缓存数据");
    }

    println!("\n🎉 Fred 10.1 缓存兼容性清理完成！");

    Ok(())
}
