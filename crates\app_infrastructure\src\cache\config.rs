//! # 缓存配置模块
//!
//! DragonflyDB/Redis缓存连接池配置和相关设置
//! 支持百万并发连接的企业级缓存配置

use serde::{ Deserialize, Serialize };
use std::time::Duration;
use anyhow::{ anyhow, Result as AnyhowResult };
use tracing::{ info, warn };

/// DragonflyDB缓存连接池配置结构体
///
/// 【目的】: 专门用于配置fred客户端连接DragonflyDB的参数，支持百万并发优化
/// 【设计】: 基于fred客户端的最佳实践配置，针对企业级应用优化
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct CachePoolConfig {
    /// 最大连接数 - 支持百万并发的关键参数
    pub max_connections: u32,
    /// 最小连接数 - 保持基础连接池大小
    pub min_connections: u32,
    /// 连接超时时间 - 防止连接建立过慢
    #[serde(with = "duration_serde")]
    pub connect_timeout: Duration,
    /// 空闲超时时间 - 自动清理空闲连接
    #[serde(with = "duration_serde")]
    pub idle_timeout: Duration,
    /// 连接最大生命周期 - 防止连接过期
    #[serde(with = "option_duration_serde")]
    pub max_lifetime: Option<Duration>,
    /// 获取连接超时时间 - 防止应用阻塞
    #[serde(with = "duration_serde")]
    pub acquire_timeout: Duration,
    /// 启用TCP_NODELAY - 减少网络延迟
    pub tcp_nodelay: bool,
    /// 启用连接保活 - 检测断开的连接
    pub tcp_keepalive: bool,
    /// 重连尝试次数
    pub max_reconnect_attempts: u32,
    /// 重连间隔时间
    #[serde(with = "duration_serde")]
    pub reconnect_delay: Duration,
}

/// DragonflyDB缓存配置结构体
///
/// 【目的】: 统一管理DragonflyDB连接配置和连接池配置
/// 【设计】: 支持从环境变量加载配置，便于不同环境部署
#[derive(Clone, Debug, Serialize, Deserialize)]
pub struct CacheConfig {
    /// 缓存服务器连接URL
    pub cache_url: String,
    /// 缓存连接池配置
    pub pool_config: CachePoolConfig,
    /// 缓存键前缀 - 用于命名空间隔离
    pub key_prefix: String,
    /// 默认TTL（生存时间）- 秒
    pub default_ttl: u64,
    /// 启用压缩 - 减少内存使用
    pub enable_compression: bool,
    /// 启用集群模式
    pub cluster_mode: bool,
    /// 集群节点列表（集群模式下使用）
    pub cluster_nodes: Vec<String>,
}

// 序列化辅助模块
mod duration_serde {
    use serde::{ Deserialize, Deserializer, Serialize, Serializer };
    use std::time::Duration;

    pub fn serialize<S>(duration: &Duration, serializer: S) -> Result<S::Ok, S::Error>
        where S: Serializer
    {
        duration.as_secs().serialize(serializer)
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Duration, D::Error>
        where D: Deserializer<'de>
    {
        let secs = u64::deserialize(deserializer)?;
        Ok(Duration::from_secs(secs))
    }
}

mod option_duration_serde {
    use serde::{ Deserialize, Deserializer, Serialize, Serializer };
    use std::time::Duration;

    pub fn serialize<S>(duration: &Option<Duration>, serializer: S) -> Result<S::Ok, S::Error>
        where S: Serializer
    {
        match duration {
            Some(d) => Some(d.as_secs()).serialize(serializer),
            None => None::<u64>.serialize(serializer),
        }
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Option<Duration>, D::Error>
        where D: Deserializer<'de>
    {
        let secs = Option::<u64>::deserialize(deserializer)?;
        Ok(secs.map(Duration::from_secs))
    }
}

impl Default for CachePoolConfig {
    /// 创建默认的缓存连接池配置
    ///
    /// 【配置说明】: 针对百万并发聊天室应用优化的默认配置
    fn default() -> Self {
        Self {
            max_connections: 1000, // 最大1000个连接，支持高并发
            min_connections: 10, // 最小10个连接，保持基础性能
            connect_timeout: Duration::from_secs(10), // 10秒连接超时
            idle_timeout: Duration::from_secs(300), // 5分钟空闲超时
            max_lifetime: Some(Duration::from_secs(3600)), // 1小时最大生命周期
            acquire_timeout: Duration::from_secs(30), // 30秒获取连接超时
            tcp_nodelay: true, // 启用TCP_NODELAY减少延迟
            tcp_keepalive: true, // 启用TCP保活检测
            max_reconnect_attempts: 5, // 最大重连5次
            reconnect_delay: Duration::from_secs(1), // 1秒重连间隔
        }
    }
}

impl Default for CacheConfig {
    /// 创建默认的缓存配置
    ///
    /// 【配置说明】: 适用于开发环境的默认配置
    fn default() -> Self {
        Self {
            cache_url: "redis://127.0.0.1:6379".to_string(),
            pool_config: CachePoolConfig::default(),
            key_prefix: "axum_chat".to_string(),
            default_ttl: 3600, // 默认1小时TTL
            enable_compression: true, // 启用压缩节省内存
            cluster_mode: false, // 默认单机模式
            cluster_nodes: vec![], // 空集群节点列表
        }
    }
}

impl CachePoolConfig {
    /// 创建开发环境配置
    ///
    /// 【用途】: 适用于开发和测试环境的轻量级配置
    pub fn development() -> Self {
        Self {
            max_connections: 50, // 开发环境较少连接
            min_connections: 5, // 最小5个连接
            connect_timeout: Duration::from_secs(5), // 5秒连接超时
            idle_timeout: Duration::from_secs(600), // 10分钟空闲超时
            max_lifetime: Some(Duration::from_secs(1800)), // 30分钟最大生命周期
            acquire_timeout: Duration::from_secs(10), // 10秒获取连接超时
            tcp_nodelay: true,
            tcp_keepalive: true,
            max_reconnect_attempts: 3, // 最大重连3次
            reconnect_delay: Duration::from_secs(2), // 2秒重连间隔
        }
    }

    /// 创建生产环境配置
    ///
    /// 【用途】: 适用于生产环境的高性能配置
    pub fn production() -> Self {
        Self {
            max_connections: 2000, // 生产环境更多连接
            min_connections: 50, // 最小50个连接
            connect_timeout: Duration::from_secs(15), // 15秒连接超时
            idle_timeout: Duration::from_secs(180), // 3分钟空闲超时
            max_lifetime: Some(Duration::from_secs(7200)), // 2小时最大生命周期
            acquire_timeout: Duration::from_secs(60), // 60秒获取连接超时
            tcp_nodelay: true,
            tcp_keepalive: true,
            max_reconnect_attempts: 10, // 最大重连10次
            reconnect_delay: Duration::from_millis(500), // 500毫秒重连间隔
        }
    }

    /// 从环境变量创建配置
    ///
    /// 【环境变量】:
    /// - CACHE_MAX_CONNECTIONS: 最大连接数
    /// - CACHE_MIN_CONNECTIONS: 最小连接数
    /// - CACHE_CONNECT_TIMEOUT: 连接超时时间（秒）
    /// - CACHE_IDLE_TIMEOUT: 空闲超时时间（秒）
    /// - CACHE_ACQUIRE_TIMEOUT: 获取连接超时时间（秒）
    /// - CACHE_MAX_RECONNECT_ATTEMPTS: 最大重连尝试次数
    /// - CACHE_RECONNECT_DELAY: 重连间隔时间（秒）
    pub fn from_env() -> anyhow::Result<Self> {
        let max_connections = std::env
            ::var("CACHE_MAX_CONNECTIONS")
            .ok()
            .and_then(|s| s.parse::<u32>().ok())
            .unwrap_or(1000);

        let min_connections = std::env
            ::var("CACHE_MIN_CONNECTIONS")
            .ok()
            .and_then(|s| s.parse::<u32>().ok())
            .unwrap_or(10);

        let connect_timeout = std::env
            ::var("CACHE_CONNECT_TIMEOUT")
            .ok()
            .and_then(|s| s.parse::<u64>().ok())
            .map(Duration::from_secs)
            .unwrap_or_else(|| Duration::from_secs(10));

        let idle_timeout = std::env
            ::var("CACHE_IDLE_TIMEOUT")
            .ok()
            .and_then(|s| s.parse::<u64>().ok())
            .map(Duration::from_secs)
            .unwrap_or_else(|| Duration::from_secs(300));

        let acquire_timeout = std::env
            ::var("CACHE_ACQUIRE_TIMEOUT")
            .ok()
            .and_then(|s| s.parse::<u64>().ok())
            .map(Duration::from_secs)
            .unwrap_or_else(|| Duration::from_secs(30));

        let max_reconnect_attempts = std::env
            ::var("CACHE_MAX_RECONNECT_ATTEMPTS")
            .ok()
            .and_then(|s| s.parse::<u32>().ok())
            .unwrap_or(5);

        let reconnect_delay = std::env
            ::var("CACHE_RECONNECT_DELAY")
            .ok()
            .and_then(|s| s.parse::<u64>().ok())
            .map(Duration::from_secs)
            .unwrap_or_else(|| Duration::from_secs(1));

        Ok(CachePoolConfig {
            max_connections,
            min_connections,
            connect_timeout,
            idle_timeout,
            max_lifetime: Some(Duration::from_secs(3600)), // 固定1小时最大生命周期
            acquire_timeout,
            tcp_nodelay: true, // 固定启用TCP_NODELAY
            tcp_keepalive: true, // 固定启用TCP保活
            max_reconnect_attempts,
            reconnect_delay,
        })
    }
}

impl CacheConfig {
    /// 从环境变量创建缓存配置
    ///
    /// 【环境变量】:
    /// - CACHE_URL: 缓存服务器连接URL
    /// - CACHE_KEY_PREFIX: 缓存键前缀
    /// - CACHE_DEFAULT_TTL: 默认TTL（秒）
    /// - CACHE_ENABLE_COMPRESSION: 是否启用压缩（true/false）
    /// - CACHE_CLUSTER_MODE: 是否启用集群模式（true/false）
    /// - CACHE_CLUSTER_NODES: 集群节点列表（逗号分隔）
    pub fn from_env() -> anyhow::Result<Self> {
        // 智能缓存URL检测和配置
        let cache_url = Self::detect_cache_url()?;

        let pool_config = CachePoolConfig::from_env()?;

        let key_prefix = std::env
            ::var("CACHE_KEY_PREFIX")
            .unwrap_or_else(|_| "axum_chat".to_string());

        let default_ttl = std::env
            ::var("CACHE_DEFAULT_TTL")
            .ok()
            .and_then(|s| s.parse::<u64>().ok())
            .unwrap_or(3600);

        let enable_compression = std::env
            ::var("CACHE_ENABLE_COMPRESSION")
            .ok()
            .and_then(|s| s.parse::<bool>().ok())
            .unwrap_or(true);

        let cluster_mode = std::env
            ::var("CACHE_CLUSTER_MODE")
            .ok()
            .and_then(|s| s.parse::<bool>().ok())
            .unwrap_or(false);

        let cluster_nodes = std::env
            ::var("CACHE_CLUSTER_NODES")
            .ok()
            .map(|s|
                s
                    .split(',')
                    .map(|node| node.trim().to_string())
                    .collect()
            )
            .unwrap_or_default();

        Ok(CacheConfig {
            cache_url,
            pool_config,
            key_prefix,
            default_ttl,
            enable_compression,
            cluster_mode,
            cluster_nodes,
        })
    }

    /// 创建开发环境配置
    ///
    /// 【用途】: 适用于开发环境的配置
    /// 【特性】: 包含密码认证的本地连接配置
    pub fn development() -> Self {
        Self {
            cache_url: "redis://:dragonfly_secure_password_2025@127.0.0.1:6379".to_string(),
            pool_config: CachePoolConfig::development(),
            key_prefix: "dev_axum_chat".to_string(),
            default_ttl: 300, // 开发环境5分钟TTL
            enable_compression: false, // 开发环境关闭压缩便于调试
            cluster_mode: false,
            cluster_nodes: vec![],
        }
    }

    /// 创建生产环境配置
    ///
    /// 【用途】: 适用于生产环境的配置
    /// 【特性】: 包含密码认证的生产级配置
    pub fn production() -> Self {
        Self {
            cache_url: "redis://:dragonfly_secure_password_2025@127.0.0.1:6379".to_string(),
            pool_config: CachePoolConfig::production(),
            key_prefix: "prod_axum_chat".to_string(),
            default_ttl: 3600, // 生产环境1小时TTL
            enable_compression: true, // 生产环境启用压缩节省内存
            cluster_mode: false,
            cluster_nodes: vec![],
        }
    }

    /// 创建测试环境配置
    ///
    /// 【用途】: 适用于单元测试和集成测试的配置
    /// 【特性】: 自动检测WSL2环境并使用正确的连接地址
    pub fn for_tests() -> Self {
        // 尝试检测WSL2环境中的DragonflyDB
        let cache_url = Self::detect_test_cache_url();

        Self {
            cache_url,
            pool_config: CachePoolConfig::development(),
            key_prefix: "test_axum_chat".to_string(),
            default_ttl: 60, // 测试环境短TTL
            enable_compression: false, // 测试环境关闭压缩
            cluster_mode: false,
            cluster_nodes: vec![],
        }
    }

    /// 检测测试环境的缓存URL
    ///
    /// 【功能】: 自动检测WSL2环境并返回正确的连接地址，包含密码认证
    /// 【返回】: 缓存连接URL
    fn detect_test_cache_url() -> String {
        // 首先尝试环境变量
        if let Ok(url) = std::env::var("TEST_CACHE_URL") {
            return url;
        }

        // DragonflyDB密码
        let password = "dragonfly_secure_password_2025";

        // 在Windows环境下，尝试检测WSL2的IP地址
        #[cfg(target_os = "windows")]
        {
            if let Ok(wsl_ip) = Self::get_wsl2_ip() {
                return format!("redis://:{password}@{wsl_ip}:6379");
            }
        }

        // 默认使用localhost，包含密码认证
        format!("redis://:{password}@localhost:6379")
    }

    /// 获取WSL2的IP地址
    ///
    /// 【功能】: 在Windows环境下获取WSL2的IP地址
    /// 【返回】: WSL2的IP地址
    #[cfg(target_os = "windows")]
    fn get_wsl2_ip() -> Result<String, Box<dyn std::error::Error>> {
        use std::process::Command;

        // 尝试通过wsl命令获取IP地址
        let output = Command::new("wsl").args(["-d", "Ubuntu", "--", "hostname", "-I"]).output()?;

        if output.status.success() {
            let ip_str = String::from_utf8(output.stdout)?;
            let ip = ip_str.split_whitespace().next().unwrap_or("127.0.0.1");
            Ok(ip.to_string())
        } else {
            Err("无法获取WSL2 IP地址".into())
        }
    }

    /// 智能检测最佳缓存连接URL
    ///
    /// 【功能】:
    /// - 优先使用环境变量配置
    /// - 自动检测WSL2环境并获取IP
    /// - 提供最佳连接方案
    fn detect_cache_url() -> anyhow::Result<String> {
        // 1. 优先使用环境变量
        if let Ok(url) = std::env::var("CACHE_URL") {
            info!("🔧 使用CACHE_URL环境变量: {}", Self::mask_password(&url));
            return Ok(url);
        }

        if let Ok(url) = std::env::var("REDIS_URL") {
            info!("🔧 使用REDIS_URL环境变量: {}", Self::mask_password(&url));
            return Ok(url);
        }

        // 2. 检测WSL2环境
        if Self::is_wsl2_environment() {
            info!("🐧 检测到WSL2环境，尝试获取WSL2 IP地址");

            if let Ok(wsl2_ip) = Self::get_wsl2_ip_smart() {
                let url = format!("redis://:dragonfly_secure_password_2025@{}:6379", wsl2_ip);
                info!("🔗 使用WSL2 IP地址: {}", Self::mask_password(&url));
                return Ok(url);
            } else {
                warn!("⚠️ 无法获取WSL2 IP地址，使用默认配置");
            }
        }

        // 3. 默认配置
        let default_url = "redis://127.0.0.1:6379".to_string();
        info!("🔧 使用默认缓存配置: {}", default_url);
        Ok(default_url)
    }

    /// 检测是否在WSL2环境中运行
    fn is_wsl2_environment() -> bool {
        // 检查WSL环境变量
        if std::env::var("WSL_DISTRO_NAME").is_ok() {
            return true;
        }

        // 检查/proc/version文件（WSL2特征）
        if let Ok(version) = std::fs::read_to_string("/proc/version") {
            return version.contains("microsoft") || version.contains("WSL");
        }

        false
    }

    /// 智能获取WSL2的IP地址
    fn get_wsl2_ip_smart() -> anyhow::Result<String> {
        use std::process::Command;

        // Windows环境下获取WSL2 IP
        let output = Command::new("wsl")
            .args(&["hostname", "-I"])
            .output()
            .map_err(|e| anyhow!("执行wsl命令失败: {}", e))?;

        if output.status.success() {
            let ip = String::from_utf8(output.stdout)
                .map_err(|e| anyhow!("解析WSL2 IP输出失败: {}", e))?
                .trim()
                .to_string();

            if !ip.is_empty() {
                return Ok(ip);
            }
        }

        Err(anyhow!("无法获取WSL2 IP地址"))
    }

    /// 屏蔽URL中的密码信息用于日志输出
    fn mask_password(url: &str) -> String {
        if let Ok(parsed) = url::Url::parse(url) {
            if parsed.password().is_some() {
                let mut masked = parsed.clone();
                let _ = masked.set_password(Some("***"));
                return masked.to_string();
            }
        }
        url.to_string()
    }
}
