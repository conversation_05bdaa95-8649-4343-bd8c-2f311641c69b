//! # 缓存配置测试
//!
//! 测试缓存配置的各种功能，包括WSL2 IP检测

use super::config::CacheConfig;

#[test]
fn test_cache_config_for_tests() {
    let config = CacheConfig::for_tests();

    println!("测试配置的缓存URL: {}", config.cache_url);
    println!("键前缀: {}", config.key_prefix);
    println!("默认TTL: {}", config.default_ttl);
    println!("集群模式: {}", config.cluster_mode);

    // 验证基本配置
    assert_eq!(config.key_prefix, "test_axum_chat");
    assert_eq!(config.default_ttl, 60);
    assert!(!config.cluster_mode);

    // 验证URL格式
    assert!(config.cache_url.starts_with("redis://"));
    assert!(config.cache_url.contains(":6379"));
    assert!(config.cache_url.contains("dragonfly_secure_password_2025"));

    // 如果检测到WSL2 IP，应该不是127.0.0.1
    if !config.cache_url.contains("127.0.0.1") {
        println!("✅ 检测到WSL2环境，使用IP: {}", config.cache_url);
        // 验证IP格式（应该是172.x.x.x）
        assert!(config.cache_url.contains("172."));
    } else {
        println!("ℹ️ 使用默认localhost配置");
    }
}

#[cfg(target_os = "windows")]
#[test]
fn test_wsl2_ip_detection() {
    // 测试WSL2 IP检测功能 - 通过测试配置间接测试
    let config = CacheConfig::for_tests();

    // 如果检测到WSL2 IP，URL应该不包含127.0.0.1
    if !config.cache_url.contains("127.0.0.1") {
        println!("✅ 检测到WSL2环境配置");
        // 验证IP格式（应该是172.x.x.x）
        assert!(config.cache_url.contains("172."));
    } else {
        println!("ℹ️ 使用默认localhost配置");
    }
}

#[test]
fn test_cache_config_with_env_var() {
    // 测试环境变量覆盖
    unsafe {
        std::env::set_var("TEST_CACHE_URL", "redis://custom-host:6379");
    }

    // 验证环境变量设置
    let env_url = std::env::var("TEST_CACHE_URL").unwrap();
    assert_eq!(env_url, "redis://custom-host:6379");

    // 清理环境变量
    unsafe {
        std::env::remove_var("TEST_CACHE_URL");
    }
}

#[test]
fn test_cache_config_development() {
    let config = CacheConfig::development();

    println!("开发配置的缓存URL: {}", config.cache_url);

    // 验证开发配置
    assert_eq!(
        config.cache_url,
        "redis://:dragonfly_secure_password_2025@127.0.0.1:6379"
    );
    assert_eq!(config.key_prefix, "dev_axum_chat");
    assert_eq!(config.default_ttl, 300);
    assert!(!config.cluster_mode);
}

#[test]
fn test_cache_config_production() {
    let config = CacheConfig::production();

    println!("生产配置的缓存URL: {}", config.cache_url);

    // 验证生产配置
    assert_eq!(
        config.cache_url,
        "redis://:dragonfly_secure_password_2025@127.0.0.1:6379"
    );
    assert_eq!(config.key_prefix, "prod_axum_chat");
    assert_eq!(config.default_ttl, 3600);
    assert!(!config.cluster_mode);
}
