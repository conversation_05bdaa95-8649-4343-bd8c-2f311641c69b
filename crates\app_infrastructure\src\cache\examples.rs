//! # 多层缓存策略使用示例
//!
//! 展示如何在企业级聊天室应用中使用多层缓存策略

use super::{
    CacheConfig,
    CacheInvalidationConfig,
    CacheInvalidationPattern,
    // 缓存失效相关
    CacheInvalidationService,
    CacheInvalidationStrategy,
    CacheService,
    CacheTier,
    InvalidationScope,
    InvalidationTrigger,
    MultiTierCacheConfig,
    MultiTierCacheService,
    create_cache_invalidation_service,
    create_default_invalidation_patterns,
    create_multi_tier_cache_service,
};
use anyhow::Result as AnyhowResult;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Duration;
use tracing::{debug, info};

/// 用户会话数据（热数据示例）
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UserSession {
    pub user_id: u32,
    pub session_id: String,
    pub login_time: u64,
    pub last_activity: u64,
    pub device_info: String,
}

/// 聊天室成员信息（温数据示例）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RoomMembers {
    pub room_id: u32,
    pub member_ids: Vec<u32>,
    pub member_count: u32,
    pub last_updated: u64,
}

/// 系统配置信息（冷数据示例）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConfig {
    pub config_key: String,
    pub config_value: String,
    pub config_type: String,
    pub last_modified: u64,
}

/// 多层缓存策略使用示例
pub struct CacheUsageExamples {
    cache_service: Arc<MultiTierCacheService>,
}

impl CacheUsageExamples {
    /// 创建缓存使用示例实例
    ///
    /// 【参数】:
    /// - cache_config: 缓存配置
    ///
    /// 【返回】: 示例实例
    pub async fn new(cache_config: CacheConfig) -> AnyhowResult<Self> {
        info!("🚀 创建多层缓存使用示例");

        let multi_tier_config = MultiTierCacheConfig {
            auto_tier_detection: true,
            auto_ttl_adjustment: true,
            stats_collection_interval: Duration::from_secs(60),
            enable_cache_warming: false,
        };

        let cache_service =
            create_multi_tier_cache_service(cache_config, multi_tier_config).await?;

        Ok(Self { cache_service })
    }

    /// 示例1: 用户会话管理（热数据）
    ///
    /// 【特点】: 高频访问，短TTL (5-15分钟)
    /// 【用途】: 用户登录状态、在线状态、权限信息等
    pub async fn example_user_session_management(&self) -> AnyhowResult<()> {
        info!("📱 示例1: 用户会话管理（热数据）");

        let user_session = UserSession {
            user_id: 12345,
            session_id: "sess_abc123".to_string(),
            login_time: 1640995200,    // 2022-01-01 00:00:00 UTC
            last_activity: 1640995800, // 10分钟后
            device_info: "iPhone 13 Pro".to_string(),
        };

        // 使用热数据层级，TTL为10分钟
        let session_key = format!("user:session:{}", user_session.user_id);
        let ttl = Duration::from_secs(10 * 60);

        debug!("设置用户会话缓存: {}", session_key);
        self.cache_service
            .set_with_tier(CacheTier::Hot, &session_key, &user_session, Some(ttl))
            .await?;

        // 获取用户会话
        let retrieved_session: Option<UserSession> = self
            .cache_service
            .get_with_tier(CacheTier::Hot, &session_key)
            .await?;

        if let Some(session) = retrieved_session {
            info!(
                "✅ 成功获取用户会话: user_id={}, device={}",
                session.user_id, session.device_info
            );
        }

        // 更新最后活动时间
        let mut updated_session = user_session;
        updated_session.last_activity = 1640996400; // 20分钟后

        self.cache_service
            .set_with_tier(CacheTier::Hot, &session_key, &updated_session, Some(ttl))
            .await?;

        info!("✅ 用户会话管理示例完成");
        Ok(())
    }

    /// 示例2: 聊天室成员管理（温数据）
    ///
    /// 【特点】: 中频访问，中等TTL (30分钟-2小时)
    /// 【用途】: 聊天室成员列表、权限信息、统计数据等
    pub async fn example_room_members_management(&self) -> AnyhowResult<()> {
        info!("🏠 示例2: 聊天室成员管理（温数据）");

        let room_members = RoomMembers {
            room_id: 1001,
            member_ids: vec![12345, 12346, 12347, 12348],
            member_count: 4,
            last_updated: 1640995200,
        };

        // 使用温数据层级，TTL为1小时
        let room_key = format!("room:members:{}", room_members.room_id);
        let ttl = Duration::from_secs(60 * 60);

        debug!("设置聊天室成员缓存: {}", room_key);
        self.cache_service
            .set_with_tier(CacheTier::Warm, &room_key, &room_members, Some(ttl))
            .await?;

        // 获取聊天室成员
        let retrieved_members: Option<RoomMembers> = self
            .cache_service
            .get_with_tier(CacheTier::Warm, &room_key)
            .await?;

        if let Some(members) = retrieved_members {
            info!(
                "✅ 成功获取聊天室成员: room_id={}, member_count={}",
                members.room_id, members.member_count
            );
        }

        // 添加新成员
        let mut updated_members = room_members;
        updated_members.member_ids.push(12349);
        updated_members.member_count += 1;
        updated_members.last_updated = 1640996400;

        self.cache_service
            .set_with_tier(CacheTier::Warm, &room_key, &updated_members, Some(ttl))
            .await?;

        info!("✅ 聊天室成员管理示例完成");
        Ok(())
    }

    /// 示例3: 系统配置管理（冷数据）
    ///
    /// 【特点】: 低频访问，长TTL (4-24小时)
    /// 【用途】: 系统配置、静态资源、元数据等
    pub async fn example_system_config_management(&self) -> AnyhowResult<()> {
        info!("⚙️ 示例3: 系统配置管理（冷数据）");

        let system_config = SystemConfig {
            config_key: "max_message_length".to_string(),
            config_value: "2048".to_string(),
            config_type: "integer".to_string(),
            last_modified: 1640995200,
        };

        // 使用冷数据层级，TTL为12小时
        let config_key = format!("system:config:{}", system_config.config_key);
        let ttl = Duration::from_secs(12 * 60 * 60);

        debug!("设置系统配置缓存: {}", config_key);
        self.cache_service
            .set_with_tier(CacheTier::Cold, &config_key, &system_config, Some(ttl))
            .await?;

        // 获取系统配置
        let retrieved_config: Option<SystemConfig> = self
            .cache_service
            .get_with_tier(CacheTier::Cold, &config_key)
            .await?;

        if let Some(config) = retrieved_config {
            info!(
                "✅ 成功获取系统配置: {}={} ({})",
                config.config_key, config.config_value, config.config_type
            );
        }

        info!("✅ 系统配置管理示例完成");
        Ok(())
    }

    /// 示例4: 智能缓存获取（自动层级检测）
    ///
    /// 【特点】: 自动检测缓存层级，无需手动指定
    /// 【用途】: 简化缓存操作，提高开发效率
    pub async fn example_smart_cache_retrieval(&self) -> AnyhowResult<()> {
        info!("🧠 示例4: 智能缓存获取（自动层级检测）");

        // 先设置不同层级的数据
        let hot_key = "hot:user:online:12345";
        let warm_key = "warm:room:stats:1001";
        let cold_key = "cold:system:version";

        // 设置热数据
        self.cache_service
            .set(hot_key, &"online", Some(Duration::from_secs(5 * 60)))
            .await?;

        // 设置温数据
        self.cache_service
            .set(warm_key, &"active", Some(Duration::from_secs(30 * 60)))
            .await?;

        // 设置冷数据
        self.cache_service
            .set(cold_key, &"v1.0.0", Some(Duration::from_secs(4 * 60 * 60)))
            .await?;

        // 使用智能获取（自动检测层级）
        let hot_value: Option<String> = self.cache_service.smart_get(hot_key).await?;
        let warm_value: Option<String> = self.cache_service.smart_get(warm_key).await?;
        let cold_value: Option<String> = self.cache_service.smart_get(cold_key).await?;

        info!("✅ 智能获取结果:");
        info!("   热数据 ({}): {:?}", hot_key, hot_value);
        info!("   温数据 ({}): {:?}", warm_key, warm_value);
        info!("   冷数据 ({}): {:?}", cold_key, cold_value);

        info!("✅ 智能缓存获取示例完成");
        Ok(())
    }

    /// 示例5: 缓存统计信息监控
    ///
    /// 【特点】: 实时监控缓存使用情况
    /// 【用途】: 性能优化、容量规划、故障诊断
    pub async fn example_cache_statistics_monitoring(&self) -> AnyhowResult<()> {
        info!("📊 示例5: 缓存统计信息监控");

        // 执行一些缓存操作来生成统计数据
        for i in 1..=10 {
            let key = format!("test:stats:{i}");
            let value = format!("value_{i}");

            // 随机选择层级
            let tier = match i % 3 {
                0 => CacheTier::Hot,
                1 => CacheTier::Warm,
                _ => CacheTier::Cold,
            };

            self.cache_service
                .set_with_tier(tier, &key, &value, None)
                .await?;

            // 读取数据（生成命中统计）
            let _: Option<String> = self.cache_service.get_with_tier(tier, &key).await?;

            // 尝试读取不存在的数据（生成未命中统计）
            let _: Option<String> = self
                .cache_service
                .get_with_tier(tier, &format!("nonexistent:{i}"))
                .await?;
        }

        // 获取统计信息
        let stats = self.cache_service.get_stats().await;

        info!("✅ 缓存统计信息:");
        info!("   总操作数: {}", stats.total_operations);
        info!("   总命中率: {:.2}%", stats.hit_rate * 100.0);

        info!("   热数据层统计:");
        info!(
            "     读取: {}, 写入: {}",
            stats.hot_tier_stats.reads, stats.hot_tier_stats.writes
        );
        info!(
            "     命中: {}, 未命中: {}",
            stats.hot_tier_stats.hits, stats.hot_tier_stats.misses
        );

        info!("   温数据层统计:");
        info!(
            "     读取: {}, 写入: {}",
            stats.warm_tier_stats.reads, stats.warm_tier_stats.writes
        );
        info!(
            "     命中: {}, 未命中: {}",
            stats.warm_tier_stats.hits, stats.warm_tier_stats.misses
        );

        info!("   冷数据层统计:");
        info!(
            "     读取: {}, 写入: {}",
            stats.cold_tier_stats.reads, stats.cold_tier_stats.writes
        );
        info!(
            "     命中: {}, 未命中: {}",
            stats.cold_tier_stats.hits, stats.cold_tier_stats.misses
        );

        info!("✅ 缓存统计信息监控示例完成");
        Ok(())
    }

    /// 运行所有示例
    ///
    /// 【功能】: 依次执行所有缓存使用示例
    pub async fn run_all_examples(&self) -> AnyhowResult<()> {
        info!("🚀 开始运行多层缓存策略示例");

        self.example_user_session_management().await?;
        self.example_room_members_management().await?;
        self.example_system_config_management().await?;
        self.example_smart_cache_retrieval().await?;
        self.example_cache_statistics_monitoring().await?;

        info!("🎉 所有多层缓存策略示例运行完成");
        Ok(())
    }
}

/// 快速开始示例
///
/// 【功能】: 演示如何快速开始使用多层缓存策略
pub async fn quick_start_example() -> AnyhowResult<()> {
    info!("⚡ 多层缓存策略快速开始示例");

    // 1. 创建缓存配置
    let cache_config = CacheConfig::for_tests(); // 使用测试配置

    // 2. 创建缓存使用示例
    let examples = CacheUsageExamples::new(cache_config).await?;

    // 3. 运行所有示例
    examples.run_all_examples().await?;

    info!("✅ 快速开始示例完成");
    Ok(())
}

/// 缓存失效策略使用示例
///
/// 【目的】: 演示如何在企业级聊天室应用中使用缓存失效机制
pub async fn cache_invalidation_example() -> AnyhowResult<()> {
    println!("🔄 缓存失效策略使用示例");
    println!("{}", "=".repeat(50));

    // 1. 创建多层缓存服务
    let cache_config = CacheConfig::for_tests();
    let multi_tier_config = MultiTierCacheConfig::default();
    let cache_service = create_multi_tier_cache_service(cache_config, multi_tier_config).await?;

    // 2. 创建缓存失效服务
    let invalidation_config = CacheInvalidationConfig {
        enabled: true,
        default_strategy: CacheInvalidationStrategy::Immediate,
        batch_size: 50,
        batch_timeout: Duration::from_secs(5),
        event_retention: Duration::from_secs(3600), // 1小时
        enable_event_logging: true,
        max_concurrent_invalidations: 10,
        ..Default::default()
    };

    let invalidation_service =
        create_cache_invalidation_service(cache_service.clone(), Some(invalidation_config));

    println!("✅ 缓存失效服务创建成功");

    // 3. 注册默认失效模式
    let default_patterns = create_default_invalidation_patterns();
    for pattern in default_patterns {
        let pattern_name = pattern.name.clone();
        invalidation_service.register_pattern(pattern).await?;
        println!("📝 注册失效模式: {pattern_name}");
    }

    // 4. 演示手动失效操作
    println!("\n🔧 手动失效操作演示:");

    // 失效单个用户缓存
    let user_keys = vec!["hot:user:12345", "warm:user:profile:12345"];
    let event = invalidation_service
        .invalidate_keys(&user_keys, "用户信息更新")
        .await?;
    println!("  ✅ 失效用户缓存: {} 个键", event.invalidated_keys.len());

    // 按模式失效聊天室缓存
    let _event = invalidation_service
        .invalidate_pattern("warm:chatroom:*", "聊天室配置更新")
        .await?;
    println!("  ✅ 按模式失效聊天室缓存");

    // 按标签失效相关缓存
    let _event = invalidation_service
        .invalidate_by_tag("user_session", "会话过期清理")
        .await?;
    println!("  ✅ 按标签失效会话缓存");

    // 按层级失效热数据缓存
    let _event = invalidation_service
        .invalidate_by_tier(CacheTier::Hot, "热数据重置")
        .await?;
    println!("  ✅ 按层级失效热数据缓存");

    // 5. 演示触发器失效
    println!("\n🎯 触发器失效演示:");

    // 注册自定义失效模式
    let custom_pattern = CacheInvalidationPattern {
        name: "message_update_invalidation".to_string(),
        strategy: CacheInvalidationStrategy::Delayed {
            delay: Duration::from_secs(1),
        },
        scope: InvalidationScope::KeyPattern {
            pattern: "hot:message:*".to_string(),
        },
        trigger: InvalidationTrigger::DatabaseUpdate {
            table: "messages".to_string(),
            operation: "UPDATE".to_string(),
        },
        enabled: true,
        priority: 8,
        created_at: chrono::Utc::now(),
        updated_at: chrono::Utc::now(),
    };

    invalidation_service
        .register_pattern(custom_pattern)
        .await?;

    // 触发失效
    let trigger = InvalidationTrigger::DatabaseUpdate {
        table: "messages".to_string(),
        operation: "UPDATE".to_string(),
    };

    let event = invalidation_service
        .trigger_invalidation("message_update_invalidation", trigger)
        .await?;
    println!(
        "  ✅ 触发消息更新失效: 执行时间 {}ms",
        event.execution_time_ms
    );

    // 6. 获取失效统计信息
    println!("\n📊 失效统计信息:");
    let stats = invalidation_service.get_invalidation_stats().await?;
    for (key, value) in stats {
        println!("  {key} = {value}");
    }

    // 7. 获取失效历史
    println!("\n📜 失效历史记录:");
    let history = invalidation_service
        .get_invalidation_history(5, None)
        .await?;
    for (i, event) in history.iter().enumerate() {
        println!(
            "  {}. {} - {} ({}ms)",
            i + 1,
            event.pattern_name,
            if event.success { "成功" } else { "失败" },
            event.execution_time_ms
        );
    }

    println!("\n🎉 缓存失效策略示例完成！");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_cache_usage_examples() {
        // 跳过需要Redis服务器的测试
        if std::env::var("SKIP_REDIS_TESTS").is_ok() {
            println!("跳过Redis测试 - 服务器不可用");
            return;
        }

        let cache_config = CacheConfig::for_tests();

        // 尝试创建示例实例，如果失败则跳过测试
        let examples = match tokio::time::timeout(
            std::time::Duration::from_secs(5),
            CacheUsageExamples::new(cache_config),
        )
        .await
        {
            Ok(Ok(examples)) => examples,
            Ok(Err(e)) => {
                println!("⚠️ DragonflyDB连接失败，跳过测试: {e}");
                return;
            }
            Err(_) => {
                println!("⚠️ DragonflyDB连接超时，跳过测试");
                return;
            }
        };

        // 测试各个示例
        assert!(examples.example_user_session_management().await.is_ok());
        assert!(examples.example_room_members_management().await.is_ok());
        assert!(examples.example_system_config_management().await.is_ok());
        assert!(examples.example_smart_cache_retrieval().await.is_ok());
        assert!(examples.example_cache_statistics_monitoring().await.is_ok());
    }

    #[tokio::test]
    async fn test_quick_start_example() {
        // 跳过需要Redis服务器的测试
        if std::env::var("SKIP_REDIS_TESTS").is_ok() {
            println!("跳过Redis测试 - 服务器不可用");
            return;
        }

        // 使用超时机制测试快速开始示例
        match tokio::time::timeout(std::time::Duration::from_secs(5), quick_start_example()).await {
            Ok(Ok(_)) => {
                println!("✅ 快速开始示例测试通过");
            }
            Ok(Err(e)) => {
                println!("⚠️ DragonflyDB连接失败，跳过测试: {e}");
                return;
            }
            Err(_) => {
                println!("⚠️ DragonflyDB连接超时，跳过测试");
                return;
            }
        }
    }
}
