//! # 缓存失效和更新策略模块
//!
//! 实现企业级缓存失效机制和更新逻辑，确保数据一致性和性能优化
//! 支持多种失效策略：手动失效、自动失效、基于事件的失效等

use super::multi_tier::{CacheTier, MultiTierCacheService};
use super::service::CacheService;
use anyhow::{Result as AnyhowResult, anyhow};
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// 缓存失效策略枚举
///
/// 【目的】: 定义不同的缓存失效策略，支持多种业务场景
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum CacheInvalidationStrategy {
    /// 立即失效 - 数据更新时立即清除相关缓存
    Immediate,
    /// 延迟失效 - 数据更新后延迟一段时间再清除缓存
    Delayed { delay: Duration },
    /// 批量失效 - 收集失效请求，批量处理
    Batch {
        batch_size: usize,
        batch_timeout: Duration,
    },
    /// 基于版本的失效 - 使用版本号控制缓存失效
    Versioned { version_key: String },
    /// 基于标签的失效 - 使用标签组织相关缓存，支持批量失效
    TagBased { tags: Vec<String> },
}

/// 缓存失效触发器
///
/// 【目的】: 定义触发缓存失效的事件类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum InvalidationTrigger {
    /// 数据库更新触发
    DatabaseUpdate { table: String, operation: String },
    /// API调用触发
    ApiCall { endpoint: String, method: String },
    /// 定时触发
    Scheduled { cron_expression: String },
    /// 手动触发
    Manual {
        user_id: Option<Uuid>,
        reason: String,
    },
    /// 系统事件触发
    SystemEvent {
        event_type: String,
        metadata: HashMap<String, String>,
    },
}

/// 缓存失效范围
///
/// 【目的】: 定义缓存失效的影响范围
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum InvalidationScope {
    /// 单个键失效
    SingleKey { key: String },
    /// 键模式失效（支持通配符）
    KeyPattern { pattern: String },
    /// 标签失效（失效所有包含指定标签的缓存）
    Tag { tag: String },
    /// 层级失效（失效指定缓存层级的所有数据）
    Tier { tier: CacheTier },
    /// 全局失效（清空所有缓存）
    Global,
}

/// 缓存失效模式
///
/// 【目的】: 定义缓存失效的具体模式和规则
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheInvalidationPattern {
    /// 模式名称
    pub name: String,
    /// 失效策略
    pub strategy: CacheInvalidationStrategy,
    /// 失效范围
    pub scope: InvalidationScope,
    /// 触发器
    pub trigger: InvalidationTrigger,
    /// 是否启用
    pub enabled: bool,
    /// 优先级（数字越大优先级越高）
    pub priority: i32,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 缓存失效事件
///
/// 【目的】: 记录缓存失效的具体事件信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheInvalidationEvent {
    /// 事件ID
    pub id: Uuid,
    /// 失效模式名称
    pub pattern_name: String,
    /// 失效策略
    pub strategy: CacheInvalidationStrategy,
    /// 失效范围
    pub scope: InvalidationScope,
    /// 触发器
    pub trigger: InvalidationTrigger,
    /// 失效的键列表
    pub invalidated_keys: Vec<String>,
    /// 失效结果（成功/失败）
    pub success: bool,
    /// 错误信息（如果失败）
    pub error_message: Option<String>,
    /// 执行时间（毫秒）
    pub execution_time_ms: u64,
    /// 事件时间
    pub timestamp: DateTime<Utc>,
}

/// 缓存更新策略
///
/// 【目的】: 定义缓存更新的策略和行为
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum CacheUpdateStrategy {
    /// 写穿策略 - 同时更新缓存和数据库
    WriteThrough,
    /// 写回策略 - 先更新缓存，延迟写入数据库
    WriteBack { sync_interval: Duration },
    /// 写绕策略 - 直接更新数据库，失效缓存
    WriteAround,
    /// 刷新策略 - 异步刷新缓存数据
    RefreshAhead { refresh_threshold: f64 },
}

/// 缓存失效配置
///
/// 【目的】: 配置缓存失效服务的行为参数
#[derive(Debug, Clone)]
pub struct CacheInvalidationConfig {
    /// 是否启用缓存失效
    pub enabled: bool,
    /// 默认失效策略
    pub default_strategy: CacheInvalidationStrategy,
    /// 默认更新策略
    pub default_update_strategy: CacheUpdateStrategy,
    /// 批量处理大小
    pub batch_size: usize,
    /// 批量处理超时时间
    pub batch_timeout: Duration,
    /// 事件历史保留时间
    pub event_retention: Duration,
    /// 是否启用事件日志
    pub enable_event_logging: bool,
    /// 最大并发失效任务数
    pub max_concurrent_invalidations: usize,
}

impl Default for CacheInvalidationConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            default_strategy: CacheInvalidationStrategy::Immediate,
            default_update_strategy: CacheUpdateStrategy::WriteThrough,
            batch_size: 100,
            batch_timeout: Duration::from_secs(5),
            event_retention: Duration::from_secs(24 * 60 * 60), // 24小时
            enable_event_logging: true,
            max_concurrent_invalidations: 10,
        }
    }
}

/// 缓存失效服务特征
///
/// 【目的】: 定义缓存失效服务的统一接口
#[async_trait]
pub trait CacheInvalidationService: Send + Sync {
    /// 注册失效模式
    ///
    /// 【参数】:
    /// - pattern: 失效模式配置
    ///
    /// 【返回】: 操作结果
    async fn register_pattern(&self, pattern: CacheInvalidationPattern) -> AnyhowResult<()>;

    /// 触发缓存失效
    ///
    /// 【参数】:
    /// - pattern_name: 失效模式名称
    /// - trigger: 触发器信息
    ///
    /// 【返回】: 失效事件
    async fn trigger_invalidation(
        &self,
        pattern_name: &str,
        trigger: InvalidationTrigger,
    ) -> AnyhowResult<CacheInvalidationEvent>;

    /// 手动失效指定键
    ///
    /// 【参数】:
    /// - keys: 要失效的键列表
    /// - reason: 失效原因
    ///
    /// 【返回】: 失效事件
    async fn invalidate_keys(
        &self,
        keys: &[&str],
        reason: &str,
    ) -> AnyhowResult<CacheInvalidationEvent>;

    /// 按模式失效缓存
    ///
    /// 【参数】:
    /// - pattern: 键模式（支持通配符）
    /// - reason: 失效原因
    ///
    /// 【返回】: 失效事件
    async fn invalidate_pattern(
        &self,
        pattern: &str,
        reason: &str,
    ) -> AnyhowResult<CacheInvalidationEvent>;

    /// 按标签失效缓存
    ///
    /// 【参数】:
    /// - tag: 标签名称
    /// - reason: 失效原因
    ///
    /// 【返回】: 失效事件
    async fn invalidate_by_tag(
        &self,
        tag: &str,
        reason: &str,
    ) -> AnyhowResult<CacheInvalidationEvent>;

    /// 按层级失效缓存
    ///
    /// 【参数】:
    /// - tier: 缓存层级
    /// - reason: 失效原因
    ///
    /// 【返回】: 失效事件
    async fn invalidate_by_tier(
        &self,
        tier: CacheTier,
        reason: &str,
    ) -> AnyhowResult<CacheInvalidationEvent>;

    /// 获取失效事件历史
    ///
    /// 【参数】:
    /// - limit: 返回数量限制
    /// - since: 起始时间（可选）
    ///
    /// 【返回】: 失效事件列表
    async fn get_invalidation_history(
        &self,
        limit: usize,
        since: Option<DateTime<Utc>>,
    ) -> AnyhowResult<Vec<CacheInvalidationEvent>>;

    /// 获取失效统计信息
    ///
    /// 【返回】: 统计信息
    async fn get_invalidation_stats(&self) -> AnyhowResult<HashMap<String, u64>>;
}

/// DragonflyDB缓存失效服务实现
///
/// 【目的】: 基于DragonflyDB的缓存失效服务具体实现
pub struct DragonflyInvalidationService {
    /// 多层缓存服务
    cache_service: Arc<MultiTierCacheService>,
    /// 失效配置
    config: CacheInvalidationConfig,
    /// 注册的失效模式
    patterns: Arc<tokio::sync::RwLock<HashMap<String, CacheInvalidationPattern>>>,
    /// 失效事件历史
    event_history: Arc<tokio::sync::RwLock<Vec<CacheInvalidationEvent>>>,
}

impl DragonflyInvalidationService {
    /// 创建新的缓存失效服务
    ///
    /// 【参数】:
    /// - cache_service: 多层缓存服务
    /// - config: 失效配置
    ///
    /// 【返回】: 缓存失效服务实例
    pub fn new(cache_service: Arc<MultiTierCacheService>, config: CacheInvalidationConfig) -> Self {
        info!("🔧 正在创建缓存失效服务...");
        debug!("失效配置: {:?}", config);

        Self {
            cache_service,
            config,
            patterns: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
            event_history: Arc::new(tokio::sync::RwLock::new(Vec::new())),
        }
    }

    /// 执行实际的缓存失效操作
    ///
    /// 【参数】:
    /// - scope: 失效范围
    /// - strategy: 失效策略
    ///
    /// 【返回】: 失效的键列表和执行时间
    async fn execute_invalidation(
        &self,
        scope: &InvalidationScope,
        strategy: &CacheInvalidationStrategy,
    ) -> AnyhowResult<(Vec<String>, u64)> {
        let start_time = std::time::Instant::now();
        let mut invalidated_keys = Vec::new();

        match scope {
            InvalidationScope::SingleKey { key } => {
                debug!("失效单个键: {}", key);
                let success = self.cache_service.delete(key).await?;
                if success {
                    invalidated_keys.push(key.clone());
                }
            }
            InvalidationScope::KeyPattern { pattern } => {
                debug!("按模式失效缓存: {}", pattern);
                invalidated_keys = self.invalidate_keys_by_pattern(pattern).await?;
            }
            InvalidationScope::Tag { tag } => {
                debug!("按标签失效缓存: {}", tag);
                invalidated_keys = self.invalidate_keys_by_tag(tag).await?;
            }
            InvalidationScope::Tier { tier } => {
                debug!("按层级失效缓存: {:?}", tier);
                invalidated_keys = self.invalidate_keys_by_tier(*tier).await?;
            }
            InvalidationScope::Global => {
                warn!("⚠️  执行全局缓存失效！");
                self.cache_service.flush_all().await?;
                invalidated_keys.push("*".to_string()); // 表示全部键
            }
        }

        // 根据策略执行延迟失效
        match strategy {
            CacheInvalidationStrategy::Delayed { delay } => {
                debug!("延迟失效，等待 {:?}", delay);
                tokio::time::sleep(*delay).await;
            }
            CacheInvalidationStrategy::Batch { .. } => {
                // 批量失效逻辑在上层处理
                debug!("批量失效策略");
            }
            _ => {
                // 立即失效和其他策略不需要额外处理
            }
        }

        let execution_time = start_time.elapsed().as_millis() as u64;
        debug!(
            "缓存失效完成，失效键数量: {}, 执行时间: {}ms",
            invalidated_keys.len(),
            execution_time
        );

        Ok((invalidated_keys, execution_time))
    }

    /// 按模式失效缓存键
    ///
    /// 【参数】:
    /// - pattern: 键模式（支持通配符）
    ///
    /// 【返回】: 失效的键列表
    async fn invalidate_keys_by_pattern(&self, pattern: &str) -> AnyhowResult<Vec<String>> {
        debug!("按模式查找并失效缓存键: {}", pattern);

        // 注意：这里需要实现模式匹配逻辑
        // 由于DragonflyDB/Redis的KEYS命令在生产环境中性能较差，
        // 实际应用中应该使用SCAN命令或维护键的索引

        // 暂时返回空列表，实际实现需要根据具体需求优化
        warn!("模式匹配失效功能需要进一步实现，当前返回空列表");
        Ok(Vec::new())
    }

    /// 按标签失效缓存键
    ///
    /// 【参数】:
    /// - tag: 标签名称
    ///
    /// 【返回】: 失效的键列表
    async fn invalidate_keys_by_tag(&self, tag: &str) -> AnyhowResult<Vec<String>> {
        debug!("按标签查找并失效缓存键: {}", tag);

        // 标签失效需要维护标签到键的映射关系
        // 可以使用Redis的SET数据结构来存储每个标签对应的键列表
        let tag_key = format!("cache_tags:{tag}");

        // 获取标签对应的所有键
        // 注意：这里需要实现获取集合成员的逻辑
        warn!("标签失效功能需要进一步实现，当前返回空列表");
        Ok(Vec::new())
    }

    /// 按层级失效缓存键
    ///
    /// 【参数】:
    /// - tier: 缓存层级
    ///
    /// 【返回】: 失效的键列表
    async fn invalidate_keys_by_tier(&self, tier: CacheTier) -> AnyhowResult<Vec<String>> {
        debug!("按层级失效缓存键: {:?}", tier);

        let tier_prefix = tier.key_prefix();
        let pattern = format!("{tier_prefix}*");

        // 使用模式匹配失效指定层级的所有键
        self.invalidate_keys_by_pattern(&pattern).await
    }

    /// 记录失效事件
    ///
    /// 【参数】:
    /// - event: 失效事件
    async fn record_event(&self, event: CacheInvalidationEvent) -> AnyhowResult<()> {
        if !self.config.enable_event_logging {
            return Ok(());
        }

        let mut history = self.event_history.write().await;
        history.push(event.clone());

        // 清理过期事件
        let retention_cutoff =
            Utc::now() - chrono::Duration::from_std(self.config.event_retention)?;
        history.retain(|e| e.timestamp > retention_cutoff);

        debug!("记录失效事件: {}", event.id);
        Ok(())
    }

    /// 创建失效事件
    ///
    /// 【参数】:
    /// - pattern_name: 模式名称
    /// - strategy: 失效策略
    /// - scope: 失效范围
    /// - trigger: 触发器
    /// - invalidated_keys: 失效的键列表
    /// - success: 是否成功
    /// - error_message: 错误信息
    /// - execution_time_ms: 执行时间
    ///
    /// 【返回】: 失效事件
    fn create_event(
        pattern_name: String,
        strategy: CacheInvalidationStrategy,
        scope: InvalidationScope,
        trigger: InvalidationTrigger,
        invalidated_keys: Vec<String>,
        success: bool,
        error_message: Option<String>,
        execution_time_ms: u64,
    ) -> CacheInvalidationEvent {
        CacheInvalidationEvent {
            id: Uuid::new_v4(),
            pattern_name,
            strategy,
            scope,
            trigger,
            invalidated_keys,
            success,
            error_message,
            execution_time_ms,
            timestamp: Utc::now(),
        }
    }
}

#[async_trait]
impl CacheInvalidationService for DragonflyInvalidationService {
    /// 注册失效模式
    async fn register_pattern(&self, pattern: CacheInvalidationPattern) -> AnyhowResult<()> {
        info!("注册缓存失效模式: {}", pattern.name);
        debug!("失效模式详情: {:?}", pattern);

        let mut patterns = self.patterns.write().await;
        patterns.insert(pattern.name.clone(), pattern);

        info!("✅ 失效模式注册成功");
        Ok(())
    }

    /// 触发缓存失效
    async fn trigger_invalidation(
        &self,
        pattern_name: &str,
        trigger: InvalidationTrigger,
    ) -> AnyhowResult<CacheInvalidationEvent> {
        info!("触发缓存失效: {}", pattern_name);
        debug!("触发器: {:?}", trigger);

        // 获取失效模式
        let patterns = self.patterns.read().await;
        let pattern = patterns
            .get(pattern_name)
            .ok_or_else(|| anyhow!("未找到失效模式: {}", pattern_name))?;

        if !pattern.enabled {
            return Err(anyhow!("失效模式已禁用: {}", pattern_name));
        }

        // 执行失效操作
        let result = self
            .execute_invalidation(&pattern.scope, &pattern.strategy)
            .await;

        let event = match result {
            Ok((invalidated_keys, execution_time)) => {
                info!(
                    "缓存失效成功: {} (失效键数量: {}, 执行时间: {}ms)",
                    pattern_name,
                    invalidated_keys.len(),
                    execution_time
                );

                Self::create_event(
                    pattern_name.to_string(),
                    pattern.strategy.clone(),
                    pattern.scope.clone(),
                    trigger,
                    invalidated_keys,
                    true,
                    None,
                    execution_time,
                )
            }
            Err(e) => {
                error!("缓存失效失败: {} - {}", pattern_name, e);

                Self::create_event(
                    pattern_name.to_string(),
                    pattern.strategy.clone(),
                    pattern.scope.clone(),
                    trigger,
                    Vec::new(),
                    false,
                    Some(e.to_string()),
                    0,
                )
            }
        };

        // 记录事件
        self.record_event(event.clone()).await?;

        Ok(event)
    }

    /// 手动失效指定键
    async fn invalidate_keys(
        &self,
        keys: &[&str],
        reason: &str,
    ) -> AnyhowResult<CacheInvalidationEvent> {
        info!("手动失效缓存键: {:?}", keys);
        debug!("失效原因: {}", reason);

        let start_time = std::time::Instant::now();
        let mut invalidated_keys = Vec::new();
        let mut errors = Vec::new();

        // 逐个失效键
        for key in keys {
            match self.cache_service.delete(key).await {
                Ok(true) => {
                    invalidated_keys.push(key.to_string());
                    debug!("成功失效键: {}", key);
                }
                Ok(false) => {
                    debug!("键不存在或已失效: {}", key);
                }
                Err(e) => {
                    error!("失效键失败 '{}': {}", key, e);
                    errors.push(format!("键 '{key}': {e}"));
                }
            }
        }

        let execution_time = start_time.elapsed().as_millis() as u64;
        let success = errors.is_empty();
        let error_message = if errors.is_empty() {
            None
        } else {
            Some(errors.join("; "))
        };

        let trigger = InvalidationTrigger::Manual {
            user_id: None,
            reason: reason.to_string(),
        };

        let scope = if keys.len() == 1 {
            InvalidationScope::SingleKey {
                key: keys[0].to_string(),
            }
        } else {
            InvalidationScope::KeyPattern {
                pattern: format!("manual_batch_{}", Uuid::new_v4()),
            }
        };

        let event = Self::create_event(
            "manual_invalidation".to_string(),
            CacheInvalidationStrategy::Immediate,
            scope,
            trigger,
            invalidated_keys,
            success,
            error_message,
            execution_time,
        );

        // 记录事件
        self.record_event(event.clone()).await?;

        if success {
            info!(
                "手动失效完成，成功失效 {} 个键",
                event.invalidated_keys.len()
            );
        } else {
            warn!("手动失效部分失败: {:?}", event.error_message);
        }

        Ok(event)
    }

    /// 按模式失效缓存
    async fn invalidate_pattern(
        &self,
        pattern: &str,
        reason: &str,
    ) -> AnyhowResult<CacheInvalidationEvent> {
        info!("按模式失效缓存: {}", pattern);
        debug!("失效原因: {}", reason);

        let scope = InvalidationScope::KeyPattern {
            pattern: pattern.to_string(),
        };
        let strategy = CacheInvalidationStrategy::Immediate;

        let result = self.execute_invalidation(&scope, &strategy).await;

        let trigger = InvalidationTrigger::Manual {
            user_id: None,
            reason: reason.to_string(),
        };

        let event = match result {
            Ok((invalidated_keys, execution_time)) => Self::create_event(
                "pattern_invalidation".to_string(),
                strategy,
                scope,
                trigger,
                invalidated_keys,
                true,
                None,
                execution_time,
            ),
            Err(e) => Self::create_event(
                "pattern_invalidation".to_string(),
                strategy,
                scope,
                trigger,
                Vec::new(),
                false,
                Some(e.to_string()),
                0,
            ),
        };

        // 记录事件
        self.record_event(event.clone()).await?;

        Ok(event)
    }

    /// 按标签失效缓存
    async fn invalidate_by_tag(
        &self,
        tag: &str,
        reason: &str,
    ) -> AnyhowResult<CacheInvalidationEvent> {
        info!("按标签失效缓存: {}", tag);
        debug!("失效原因: {}", reason);

        let scope = InvalidationScope::Tag {
            tag: tag.to_string(),
        };
        let strategy = CacheInvalidationStrategy::Immediate;

        let result = self.execute_invalidation(&scope, &strategy).await;

        let trigger = InvalidationTrigger::Manual {
            user_id: None,
            reason: reason.to_string(),
        };

        let event = match result {
            Ok((invalidated_keys, execution_time)) => Self::create_event(
                "tag_invalidation".to_string(),
                strategy,
                scope,
                trigger,
                invalidated_keys,
                true,
                None,
                execution_time,
            ),
            Err(e) => Self::create_event(
                "tag_invalidation".to_string(),
                strategy,
                scope,
                trigger,
                Vec::new(),
                false,
                Some(e.to_string()),
                0,
            ),
        };

        // 记录事件
        self.record_event(event.clone()).await?;

        Ok(event)
    }

    /// 按层级失效缓存
    async fn invalidate_by_tier(
        &self,
        tier: CacheTier,
        reason: &str,
    ) -> AnyhowResult<CacheInvalidationEvent> {
        info!("按层级失效缓存: {:?}", tier);
        debug!("失效原因: {}", reason);

        let scope = InvalidationScope::Tier { tier };
        let strategy = CacheInvalidationStrategy::Immediate;

        let result = self.execute_invalidation(&scope, &strategy).await;

        let trigger = InvalidationTrigger::Manual {
            user_id: None,
            reason: reason.to_string(),
        };

        let event = match result {
            Ok((invalidated_keys, execution_time)) => Self::create_event(
                "tier_invalidation".to_string(),
                strategy,
                scope,
                trigger,
                invalidated_keys,
                true,
                None,
                execution_time,
            ),
            Err(e) => Self::create_event(
                "tier_invalidation".to_string(),
                strategy,
                scope,
                trigger,
                Vec::new(),
                false,
                Some(e.to_string()),
                0,
            ),
        };

        // 记录事件
        self.record_event(event.clone()).await?;

        Ok(event)
    }

    /// 获取失效事件历史
    async fn get_invalidation_history(
        &self,
        limit: usize,
        since: Option<DateTime<Utc>>,
    ) -> AnyhowResult<Vec<CacheInvalidationEvent>> {
        debug!("获取失效事件历史，限制: {}, 起始时间: {:?}", limit, since);

        let history = self.event_history.read().await;
        let mut events: Vec<CacheInvalidationEvent> = history
            .iter()
            .filter(|event| {
                if let Some(since_time) = since {
                    event.timestamp >= since_time
                } else {
                    true
                }
            })
            .cloned()
            .collect();

        // 按时间倒序排列
        events.sort_by(|a, b| b.timestamp.cmp(&a.timestamp));

        // 限制返回数量
        events.truncate(limit);

        debug!("返回 {} 个失效事件", events.len());
        Ok(events)
    }

    /// 获取失效统计信息
    async fn get_invalidation_stats(&self) -> AnyhowResult<HashMap<String, u64>> {
        debug!("获取失效统计信息");

        let history = self.event_history.read().await;
        let mut stats = HashMap::new();

        // 统计总事件数
        stats.insert("total_events".to_string(), history.len() as u64);

        // 统计成功和失败事件数
        let successful_events = history.iter().filter(|e| e.success).count() as u64;
        let failed_events = (history.len() as u64) - successful_events;
        stats.insert("successful_events".to_string(), successful_events);
        stats.insert("failed_events".to_string(), failed_events);

        // 统计不同策略的使用次数
        let mut strategy_counts = HashMap::new();
        for event in history.iter() {
            let strategy_name = match &event.strategy {
                CacheInvalidationStrategy::Immediate => "immediate",
                CacheInvalidationStrategy::Delayed { .. } => "delayed",
                CacheInvalidationStrategy::Batch { .. } => "batch",
                CacheInvalidationStrategy::Versioned { .. } => "versioned",
                CacheInvalidationStrategy::TagBased { .. } => "tag_based",
            };
            *strategy_counts
                .entry(strategy_name.to_string())
                .or_insert(0u64) += 1;
        }
        for (strategy, count) in strategy_counts {
            stats.insert(format!("strategy_{strategy}"), count);
        }

        // 统计不同触发器的使用次数
        let mut trigger_counts = HashMap::new();
        for event in history.iter() {
            let trigger_name = match &event.trigger {
                InvalidationTrigger::DatabaseUpdate { .. } => "database_update",
                InvalidationTrigger::ApiCall { .. } => "api_call",
                InvalidationTrigger::Scheduled { .. } => "scheduled",
                InvalidationTrigger::Manual { .. } => "manual",
                InvalidationTrigger::SystemEvent { .. } => "system_event",
            };
            *trigger_counts
                .entry(trigger_name.to_string())
                .or_insert(0u64) += 1;
        }
        for (trigger, count) in trigger_counts {
            stats.insert(format!("trigger_{trigger}"), count);
        }

        // 计算平均执行时间
        if !history.is_empty() {
            let total_execution_time: u64 = history.iter().map(|e| e.execution_time_ms).sum();
            let avg_execution_time = total_execution_time / (history.len() as u64);
            stats.insert("avg_execution_time_ms".to_string(), avg_execution_time);
        }

        // 统计失效的键总数
        let total_invalidated_keys: u64 = history
            .iter()
            .map(|e| e.invalidated_keys.len() as u64)
            .sum();
        stats.insert("total_invalidated_keys".to_string(), total_invalidated_keys);

        debug!("统计信息: {:?}", stats);
        Ok(stats)
    }
}

/// 创建缓存失效服务的便利函数
///
/// 【参数】:
/// - cache_service: 多层缓存服务
/// - config: 失效配置（可选，使用默认配置）
///
/// 【返回】: 缓存失效服务实例
pub fn create_cache_invalidation_service(
    cache_service: Arc<MultiTierCacheService>,
    config: Option<CacheInvalidationConfig>,
) -> Arc<dyn CacheInvalidationService> {
    let config = config.unwrap_or_default();
    Arc::new(DragonflyInvalidationService::new(cache_service, config))
}

/// 创建默认的缓存失效模式
///
/// 【目的】: 提供常用的缓存失效模式配置
///
/// 【返回】: 预定义的失效模式列表
pub fn create_default_invalidation_patterns() -> Vec<CacheInvalidationPattern> {
    let now = Utc::now();

    vec![
        // 用户数据更新失效模式
        CacheInvalidationPattern {
            name: "user_data_update".to_string(),
            strategy: CacheInvalidationStrategy::Immediate,
            scope: InvalidationScope::KeyPattern {
                pattern: "hot:user:*".to_string(),
            },
            trigger: InvalidationTrigger::DatabaseUpdate {
                table: "users".to_string(),
                operation: "UPDATE".to_string(),
            },
            enabled: true,
            priority: 10,
            created_at: now,
            updated_at: now,
        },
        // 聊天室数据更新失效模式
        CacheInvalidationPattern {
            name: "chatroom_data_update".to_string(),
            strategy: CacheInvalidationStrategy::Immediate,
            scope: InvalidationScope::KeyPattern {
                pattern: "warm:chatroom:*".to_string(),
            },
            trigger: InvalidationTrigger::DatabaseUpdate {
                table: "chat_rooms".to_string(),
                operation: "UPDATE".to_string(),
            },
            enabled: true,
            priority: 8,
            created_at: now,
            updated_at: now,
        },
        // 消息缓存失效模式
        CacheInvalidationPattern {
            name: "message_cache_cleanup".to_string(),
            strategy: CacheInvalidationStrategy::Delayed {
                delay: Duration::from_secs(30),
            },
            scope: InvalidationScope::KeyPattern {
                pattern: "hot:message:*".to_string(),
            },
            trigger: InvalidationTrigger::DatabaseUpdate {
                table: "messages".to_string(),
                operation: "INSERT".to_string(),
            },
            enabled: true,
            priority: 5,
            created_at: now,
            updated_at: now,
        },
        // 系统配置更新失效模式
        CacheInvalidationPattern {
            name: "system_config_update".to_string(),
            strategy: CacheInvalidationStrategy::Immediate,
            scope: InvalidationScope::Tier {
                tier: CacheTier::Cold,
            },
            trigger: InvalidationTrigger::SystemEvent {
                event_type: "config_update".to_string(),
                metadata: HashMap::new(),
            },
            enabled: true,
            priority: 15,
            created_at: now,
            updated_at: now,
        },
        // 定时清理失效模式
        CacheInvalidationPattern {
            name: "scheduled_cleanup".to_string(),
            strategy: CacheInvalidationStrategy::Batch {
                batch_size: 1000,
                batch_timeout: Duration::from_secs(60),
            },
            scope: InvalidationScope::Global,
            trigger: InvalidationTrigger::Scheduled {
                cron_expression: "0 2 * * *".to_string(), // 每天凌晨2点
            },
            enabled: false, // 默认禁用，需要手动启用
            priority: 1,
            created_at: now,
            updated_at: now,
        },
    ]
}
