//! # 消息搜索缓存服务测试
//!
//! 使用TDD模式测试消息搜索缓存服务的各项功能
//! 包括L1/L2缓存、缓存预热、统计信息等

#[cfg(test)]
mod tests {
    use super::super::{
        config::CacheConfig,
        message_search_cache::{MessageSearchCacheService, SearchQueryResult},
        multi_tier::{MultiTierCacheConfig, create_multi_tier_cache_service},
    };

    use tokio;

    /// 创建测试用的消息搜索缓存服务
    async fn create_test_message_search_cache() -> MessageSearchCacheService {
        let cache_config = CacheConfig::for_tests();
        let multi_tier_config = MultiTierCacheConfig::default();

        let multi_tier_service = create_multi_tier_cache_service(cache_config, multi_tier_config)
            .await
            .expect("创建多层缓存服务失败");

        MessageSearchCacheService::new(multi_tier_service)
    }

    /// 创建测试用的搜索查询结果
    fn create_test_search_result(
        query: &str,
        user_id: &str,
        room_id: Option<&str>,
    ) -> SearchQueryResult {
        SearchQueryResult {
            query: query.to_string(),
            message_ids: vec!["msg1".to_string(), "msg2".to_string(), "msg3".to_string()],
            total_count: 3,
            timestamp: chrono::Utc::now(),
            user_id: user_id.to_string(),
            room_id: room_id.map(|s| s.to_string()),
        }
    }

    #[tokio::test]
    async fn test_cache_and_get_search_result() {
        // 【测试目标】: 验证搜索结果的缓存和获取功能
        let cache_service = create_test_message_search_cache().await;

        let test_result = create_test_search_result("rust programming", "user123", Some("room456"));

        // 缓存搜索结果
        let cache_result = cache_service.cache_search_result(&test_result).await;
        assert!(cache_result.is_ok(), "缓存搜索结果应该成功");

        // 获取缓存的搜索结果
        let retrieved_result = cache_service
            .get_cached_search_result("rust programming", "user123", Some("room456"))
            .await;

        assert!(retrieved_result.is_ok(), "获取缓存搜索结果应该成功");
        let retrieved_result = retrieved_result.unwrap();
        assert!(retrieved_result.is_some(), "应该能找到缓存的搜索结果");

        let retrieved_result = retrieved_result.unwrap();
        assert_eq!(retrieved_result.query, "rust programming");
        assert_eq!(retrieved_result.user_id, "user123");
        assert_eq!(retrieved_result.room_id, Some("room456".to_string()));
        assert_eq!(retrieved_result.total_count, 3);
        assert_eq!(retrieved_result.message_ids.len(), 3);
    }

    #[tokio::test]
    async fn test_cache_miss_for_nonexistent_search() {
        // 【测试目标】: 验证不存在的搜索结果返回None
        let cache_service = create_test_message_search_cache().await;

        let result = cache_service
            .get_cached_search_result("nonexistent query", "user999", None)
            .await;

        assert!(result.is_ok(), "查询不存在的搜索结果应该成功");
        assert!(result.unwrap().is_none(), "不存在的搜索结果应该返回None");
    }

    #[tokio::test]
    async fn test_hot_queries_cache() {
        // 【测试目标】: 验证热门查询的缓存和获取功能
        let cache_service = create_test_message_search_cache().await;

        let hot_queries = vec![
            "rust".to_string(),
            "axum".to_string(),
            "tokio".to_string(),
            "async".to_string(),
        ];

        // 缓存热门查询
        let cache_result = cache_service.cache_hot_queries(&hot_queries).await;
        assert!(cache_result.is_ok(), "缓存热门查询应该成功");

        // 获取热门查询
        let retrieved_queries = cache_service.get_hot_queries().await;
        assert!(retrieved_queries.is_ok(), "获取热门查询应该成功");

        let retrieved_queries = retrieved_queries.unwrap();
        assert!(retrieved_queries.is_some(), "应该能找到缓存的热门查询");

        let retrieved_queries = retrieved_queries.unwrap();
        assert_eq!(retrieved_queries.len(), 4);
        assert!(retrieved_queries.contains(&"rust".to_string()));
        assert!(retrieved_queries.contains(&"axum".to_string()));
        assert!(retrieved_queries.contains(&"tokio".to_string()));
        assert!(retrieved_queries.contains(&"async".to_string()));
    }

    #[tokio::test]
    async fn test_user_search_history() {
        // 【测试目标】: 验证用户搜索历史的更新和获取功能
        let cache_service = create_test_message_search_cache().await;

        // 创建多个搜索结果来触发历史记录更新
        let queries = vec!["rust", "axum", "tokio", "async", "serde"];

        for query in &queries {
            let test_result = create_test_search_result(query, "user123", None);
            let _ = cache_service.cache_search_result(&test_result).await;
        }

        // 获取用户搜索历史
        let history = cache_service.get_user_search_history("user123").await;
        assert!(history.is_ok(), "获取用户搜索历史应该成功");

        let history = history.unwrap();
        assert!(history.is_some(), "应该能找到用户搜索历史");

        let history = history.unwrap();
        assert_eq!(history.user_id, "user123");
        assert!(!history.recent_queries.is_empty(), "最近查询列表不应该为空");

        // 验证最近查询是按时间倒序排列的（最新的在前面）
        assert_eq!(history.recent_queries[0], "serde");
        assert!(history.recent_queries.contains(&"rust".to_string()));
        assert!(history.recent_queries.contains(&"axum".to_string()));
    }

    #[tokio::test]
    async fn test_search_stats_update() {
        // 【测试目标】: 验证搜索统计信息的更新功能
        let cache_service = create_test_message_search_cache().await;

        let query = "rust programming";

        // 执行多次相同查询来更新统计信息
        for _ in 0..3 {
            let test_result = create_test_search_result(query, "user123", None);
            let _ = cache_service.cache_search_result(&test_result).await;
        }

        // 获取搜索统计信息
        let stats = cache_service.get_search_stats(query).await;
        assert!(stats.is_ok(), "获取搜索统计信息应该成功");

        let stats = stats.unwrap();
        assert!(stats.is_some(), "应该能找到搜索统计信息");

        let stats = stats.unwrap();
        assert_eq!(stats.query, query);
        assert_eq!(stats.query_count, 3, "查询次数应该为3");
        assert!(stats.last_queried > chrono::Utc::now() - chrono::Duration::minutes(1));
    }

    #[tokio::test]
    async fn test_cache_warming() {
        // 【测试目标】: 验证缓存预热功能
        let cache_service = create_test_message_search_cache().await;

        let hot_queries = vec!["rust".to_string(), "axum".to_string(), "tokio".to_string()];

        let recent_results = vec![
            create_test_search_result("rust", "user1", None),
            create_test_search_result("axum", "user2", Some("room1")),
            create_test_search_result("tokio", "user3", None),
        ];

        // 执行缓存预热
        let warm_result = cache_service
            .warm_cache(&hot_queries, &recent_results)
            .await;
        assert!(warm_result.is_ok(), "缓存预热应该成功");

        // 验证热门查询已被缓存
        let cached_hot_queries = cache_service.get_hot_queries().await.unwrap();
        assert!(cached_hot_queries.is_some(), "热门查询应该已被缓存");
        assert_eq!(cached_hot_queries.unwrap().len(), 3);

        // 验证搜索结果已被缓存
        let cached_result1 = cache_service
            .get_cached_search_result("rust", "user1", None)
            .await
            .unwrap();
        assert!(cached_result1.is_some(), "搜索结果1应该已被缓存");

        let cached_result2 = cache_service
            .get_cached_search_result("axum", "user2", Some("room1"))
            .await
            .unwrap();
        assert!(cached_result2.is_some(), "搜索结果2应该已被缓存");
    }

    #[tokio::test]
    async fn test_different_room_contexts() {
        // 【测试目标】: 验证不同聊天室上下文的搜索结果缓存隔离
        let cache_service = create_test_message_search_cache().await;

        let query = "rust programming";
        let user_id = "user123";

        // 在不同聊天室中缓存相同查询的不同结果
        let result_room1 = create_test_search_result(query, user_id, Some("room1"));
        let result_room2 = create_test_search_result(query, user_id, Some("room2"));
        let result_global = create_test_search_result(query, user_id, None);

        let _ = cache_service.cache_search_result(&result_room1).await;
        let _ = cache_service.cache_search_result(&result_room2).await;
        let _ = cache_service.cache_search_result(&result_global).await;

        // 验证不同上下文的结果是独立缓存的
        let cached_room1 = cache_service
            .get_cached_search_result(query, user_id, Some("room1"))
            .await
            .unwrap();
        assert!(cached_room1.is_some());
        assert_eq!(cached_room1.unwrap().room_id, Some("room1".to_string()));

        let cached_room2 = cache_service
            .get_cached_search_result(query, user_id, Some("room2"))
            .await
            .unwrap();
        assert!(cached_room2.is_some());
        assert_eq!(cached_room2.unwrap().room_id, Some("room2".to_string()));

        let cached_global = cache_service
            .get_cached_search_result(query, user_id, None)
            .await
            .unwrap();
        assert!(cached_global.is_some());
        assert_eq!(cached_global.unwrap().room_id, None);
    }

    #[tokio::test]
    async fn test_user_search_history_limit() {
        // 【测试目标】: 验证用户搜索历史的数量限制功能
        let cache_service = create_test_message_search_cache().await;

        let user_id = "user123";

        // 创建超过20个查询来测试历史记录限制
        for i in 0..25 {
            let query = format!("query_{i}");
            let test_result = create_test_search_result(&query, user_id, None);
            let _ = cache_service.cache_search_result(&test_result).await;
        }

        // 获取用户搜索历史
        let history = cache_service
            .get_user_search_history(user_id)
            .await
            .unwrap();
        assert!(history.is_some());

        let history = history.unwrap();
        assert_eq!(
            history.recent_queries.len(),
            20,
            "最近查询列表应该限制在20个"
        );

        // 验证最新的查询在列表前面
        assert_eq!(history.recent_queries[0], "query_24");
        assert_eq!(history.recent_queries[19], "query_5");
    }
}
