//! # 缓存模块
//!
//! 提供企业级缓存功能，支持DragonflyDB/Redis
//! 包含配置管理、连接池管理、缓存服务等核心组件

// ============================================================================
// 子模块声明
// ============================================================================

/// 缓存配置模块
pub mod config;

/// 缓存客户端管理器模块
pub mod client_manager;

/// 缓存服务实现模块
pub mod service;

/// 多层缓存策略模块
pub mod multi_tier;

/// 缓存失效和更新策略模块
pub mod invalidation;

/// 缓存使用示例模块
pub mod examples;

/// 消息搜索缓存服务模块
pub mod message_search_cache;

/// 弹性消息搜索缓存服务模块
pub mod resilient_message_search_cache;

/// 搜索结果预计算缓存服务模块
pub mod precompute_cache;

/// 分布式锁模块
pub mod distributed_lock;

/// 缓存清理工具模块（Fred 10.1 兼容性）
pub mod cleanup_tool;

/// Fred 10.1 缓存数据迁移工具模块
pub mod fred_migration_tool;

// ============================================================================
// 测试模块声明
// ============================================================================

#[cfg(test)]
mod service_test;

#[cfg(test)]
mod multi_tier_test;

#[cfg(test)]
mod dragonfly_connection_test;

#[cfg(test)]
mod advanced_cache_test;

#[cfg(test)]
mod config_test;

#[cfg(test)]
mod message_search_cache_test;

#[cfg(test)]
mod simple_cache_test;

// ============================================================================
// 公共接口重新导出
// ============================================================================

// 配置相关
pub use config::{ CacheConfig, CachePoolConfig };

// 客户端管理相关
pub use client_manager::{ CacheClientManager, CachePoolStats };

// 缓存服务相关
pub use service::{ CacheService, DragonflyCache };

// 多层缓存策略相关
pub use multi_tier::{
    CacheTier,
    MultiTierCacheConfig,
    MultiTierCacheService,
    MultiTierCacheStats,
    TierStats,
    create_multi_tier_cache_service,
};

// 缓存失效和更新策略相关
pub use invalidation::{
    CacheInvalidationConfig,
    CacheInvalidationEvent,
    CacheInvalidationPattern,
    CacheInvalidationService,
    CacheInvalidationStrategy,
    CacheUpdateStrategy,
    InvalidationScope,
    InvalidationTrigger,
    create_cache_invalidation_service,
    create_default_invalidation_patterns,
};

// 消息搜索缓存服务相关
pub use message_search_cache::{
    MessageSearchCacheService,
    SearchQueryResult,
    SearchQueryStats,
    UserSearchHistory,
};

// 弹性消息搜索缓存服务相关
pub use resilient_message_search_cache::ResilientMessageSearchCacheService;

// 搜索结果预计算缓存服务相关
pub use precompute_cache::{
    PrecomputeCache,
    PrecomputeCacheConfig,
    PrecomputeCacheStats,
    PrecomputeTaskStatus,
    PrecomputedResult,
};

// 分布式锁相关
pub use distributed_lock::{ DistributedLockManager, DistributedLockConfig, LockInfo };

// 缓存清理工具相关（Fred 10.1 兼容性）
pub use cleanup_tool::{
    CacheCleanupTool,
    CleanupReport,
    HealthCheckResult,
    CacheStats,
    TestResult,
    run_cleanup_command,
};

// Fred 10.1 缓存数据迁移工具相关
pub use fred_migration_tool::{ FredMigrationTool, MigrationStats };

// ============================================================================
// 便捷构造函数
// ============================================================================

use anyhow::Result as AnyhowResult;
use std::sync::Arc;
use tracing::info;

/// 创建标准缓存服务
///
/// 【参数】:
/// - config: 缓存配置
///
/// 【返回】: DragonflyCache服务实例
pub async fn create_cache_service(config: CacheConfig) -> AnyhowResult<Arc<DragonflyCache>> {
    info!("🚀 创建标准缓存服务");
    let manager = Arc::new(CacheClientManager::new(config).await?);
    Ok(Arc::new(DragonflyCache::new(manager)))
}

/// 创建默认多层缓存服务
///
/// 【参数】:
/// - config: 缓存配置
///
/// 【返回】: MultiTierCacheService服务实例（使用默认多层配置）
pub async fn create_default_multi_tier_cache_service(
    config: CacheConfig
) -> AnyhowResult<Arc<MultiTierCacheService>> {
    create_multi_tier_cache_service(config, MultiTierCacheConfig::default()).await
}

// ============================================================================
// 缓存工具函数
// ============================================================================

/// 构建缓存键
///
/// 【参数】:
/// - prefix: 键前缀
/// - key: 原始键
///
/// 【返回】: 完整的缓存键
pub fn build_cache_key(prefix: &str, key: &str) -> String {
    if prefix.is_empty() { key.to_string() } else { format!("{prefix}:{key}") }
}

/// 构建用户相关缓存键
///
/// 【参数】:
/// - user_id: 用户ID
/// - key: 键名
///
/// 【返回】: 用户相关的缓存键
pub fn build_user_cache_key(user_id: &str, key: &str) -> String {
    format!("user:{user_id}:{key}")
}

/// 构建聊天室相关缓存键
///
/// 【参数】:
/// - room_id: 聊天室ID
/// - key: 键名
///
/// 【返回】: 聊天室相关的缓存键
pub fn build_room_cache_key(room_id: &str, key: &str) -> String {
    format!("room:{room_id}:{key}")
}

/// 构建消息相关缓存键
///
/// 【参数】:
/// - message_id: 消息ID
/// - key: 键名
///
/// 【返回】: 消息相关的缓存键
pub fn build_message_cache_key(message_id: &str, key: &str) -> String {
    format!("message:{message_id}:{key}")
}

/// 构建会话相关缓存键
///
/// 【参数】:
/// - session_id: 会话ID
/// - key: 键名
///
/// 【返回】: 会话相关的缓存键
pub fn build_session_cache_key(session_id: &str, key: &str) -> String {
    format!("session:{session_id}:{key}")
}

// ============================================================================
// 常用缓存键常量
// ============================================================================

/// 缓存键常量
pub mod cache_keys {
    /// 用户在线状态缓存键前缀
    pub const USER_ONLINE_STATUS: &str = "user_online";

    /// 用户会话缓存键前缀
    pub const USER_SESSION: &str = "user_session";

    /// 聊天室成员列表缓存键前缀
    pub const ROOM_MEMBERS: &str = "room_members";

    /// 聊天室消息缓存键前缀
    pub const ROOM_MESSAGES: &str = "room_messages";

    /// 用户未读消息计数缓存键前缀
    pub const USER_UNREAD_COUNT: &str = "user_unread_count";

    /// WebSocket连接统计缓存键前缀
    pub const WS_STATS: &str = "ws_stats";

    /// 系统配置缓存键前缀
    pub const SYSTEM_CONFIG: &str = "system_config";

    /// 用户权限缓存键前缀
    pub const USER_PERMISSIONS: &str = "user_permissions";

    /// 聊天室信息缓存键前缀
    pub const ROOM_INFO: &str = "room_info";

    /// 消息搜索结果缓存键前缀
    pub const MESSAGE_SEARCH: &str = "message_search";

    /// 热门搜索查询缓存键前缀 (L1缓存)
    pub const HOT_SEARCH_QUERIES: &str = "hot_search_queries";

    /// 近期搜索结果缓存键前缀 (L2缓存)
    pub const RECENT_SEARCH_RESULTS: &str = "recent_search_results";

    /// 搜索查询统计缓存键前缀
    pub const SEARCH_QUERY_STATS: &str = "search_query_stats";

    /// 用户搜索历史缓存键前缀
    pub const USER_SEARCH_HISTORY: &str = "user_search_history";
}

// ============================================================================
// 缓存TTL常量
// ============================================================================

/// 缓存TTL常量（秒）
pub mod cache_ttl {
    use std::time::Duration;

    /// 用户会话TTL - 24小时
    pub const USER_SESSION: Duration = Duration::from_secs(24 * 60 * 60);

    /// 用户在线状态TTL - 5分钟
    pub const USER_ONLINE_STATUS: Duration = Duration::from_secs(5 * 60);

    /// 聊天室成员列表TTL - 30分钟
    pub const ROOM_MEMBERS: Duration = Duration::from_secs(30 * 60);

    /// 聊天室消息TTL - 2小时
    pub const ROOM_MESSAGES: Duration = Duration::from_secs(2 * 60 * 60);

    /// 用户未读消息计数TTL - 1小时
    pub const USER_UNREAD_COUNT: Duration = Duration::from_secs(60 * 60);

    /// WebSocket统计TTL - 10分钟
    pub const WS_STATS: Duration = Duration::from_secs(10 * 60);

    /// 系统配置TTL - 1小时
    pub const SYSTEM_CONFIG: Duration = Duration::from_secs(60 * 60);

    /// 用户权限TTL - 30分钟
    pub const USER_PERMISSIONS: Duration = Duration::from_secs(30 * 60);

    /// 聊天室信息TTL - 1小时
    pub const ROOM_INFO: Duration = Duration::from_secs(60 * 60);

    /// 消息搜索结果TTL - 15分钟
    pub const MESSAGE_SEARCH: Duration = Duration::from_secs(15 * 60);

    /// 热门搜索查询TTL - 5分钟 (L1缓存，高频访问)
    pub const HOT_SEARCH_QUERIES: Duration = Duration::from_secs(5 * 60);

    /// 近期搜索结果TTL - 30分钟 (L2缓存，中频访问)
    pub const RECENT_SEARCH_RESULTS: Duration = Duration::from_secs(30 * 60);

    /// 搜索查询统计TTL - 1小时
    pub const SEARCH_QUERY_STATS: Duration = Duration::from_secs(60 * 60);

    /// 用户搜索历史TTL - 24小时
    pub const USER_SEARCH_HISTORY: Duration = Duration::from_secs(24 * 60 * 60);
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_build_cache_key() {
        assert_eq!(build_cache_key("", "test"), "test");
        assert_eq!(build_cache_key("prefix", "test"), "prefix:test");
    }

    #[test]
    fn test_build_user_cache_key() {
        assert_eq!(build_user_cache_key("123", "profile"), "user:123:profile");
    }

    #[test]
    fn test_build_room_cache_key() {
        assert_eq!(build_room_cache_key("456", "members"), "room:456:members");
    }

    #[test]
    fn test_build_message_cache_key() {
        assert_eq!(build_message_cache_key("789", "content"), "message:789:content");
    }

    #[test]
    fn test_build_session_cache_key() {
        assert_eq!(build_session_cache_key("abc", "data"), "session:abc:data");
    }
}
