//! # 多层缓存策略实现
//!
//! 实现热数据、温数据、冷数据的分层缓存策略
//! 根据数据访问频率和业务重要性设置不同的TTL和缓存层级

use super::client_manager::CacheClientManager;
use super::service::{ CacheService, DragonflyCache };
use anyhow::Result as AnyhowResult;
use async_trait::async_trait;
use serde::{ Deserialize, Serialize };
use std::sync::Arc;
use std::time::Duration;
use tracing::{ debug, info, warn };

/// 缓存层级枚举
///
/// 【目的】: 定义不同的缓存层级，用于区分数据的访问频率和重要性
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CacheTier {
    /// 热数据层 - 高频访问，短TTL (5-15分钟)
    Hot,
    /// 温数据层 - 中频访问，中等TTL (30分钟-2小时)
    Warm,
    /// 冷数据层 - 低频访问，长TTL (4-24小时)
    Cold,
}

impl CacheTier {
    /// 获取缓存层级的默认TTL
    ///
    /// 【返回】: 对应层级的默认TTL
    pub fn default_ttl(&self) -> Duration {
        match self {
            CacheTier::Hot => Duration::from_secs(5 * 60), // 5分钟
            CacheTier::Warm => Duration::from_secs(30 * 60), // 30分钟
            CacheTier::Cold => Duration::from_secs(4 * 60 * 60), // 4小时
        }
    }

    /// 获取缓存层级的最大TTL
    ///
    /// 【返回】: 对应层级的最大TTL
    pub fn max_ttl(&self) -> Duration {
        match self {
            CacheTier::Hot => Duration::from_secs(15 * 60), // 15分钟
            CacheTier::Warm => Duration::from_secs(2 * 60 * 60), // 2小时
            CacheTier::Cold => Duration::from_secs(24 * 60 * 60), // 24小时
        }
    }

    /// 获取缓存层级的键前缀
    ///
    /// 【返回】: 对应层级的键前缀
    pub fn key_prefix(&self) -> &'static str {
        match self {
            CacheTier::Hot => "hot",
            CacheTier::Warm => "warm",
            CacheTier::Cold => "cold",
        }
    }

    /// 从键前缀推断缓存层级
    ///
    /// 【参数】:
    /// - key: 缓存键
    ///
    /// 【返回】: 推断的缓存层级
    pub fn from_key(key: &str) -> Option<Self> {
        if key.starts_with("hot:") {
            Some(CacheTier::Hot)
        } else if key.starts_with("warm:") {
            Some(CacheTier::Warm)
        } else if key.starts_with("cold:") {
            Some(CacheTier::Cold)
        } else {
            None
        }
    }
}

/// 多层缓存策略配置
///
/// 【目的】: 配置多层缓存的行为参数
#[derive(Debug, Clone)]
pub struct MultiTierCacheConfig {
    /// 是否启用自动层级推断
    pub auto_tier_detection: bool,
    /// 是否启用TTL自动调整
    pub auto_ttl_adjustment: bool,
    /// 缓存统计收集间隔
    pub stats_collection_interval: Duration,
    /// 是否启用缓存预热
    pub enable_cache_warming: bool,
}

impl Default for MultiTierCacheConfig {
    fn default() -> Self {
        Self {
            auto_tier_detection: true,
            auto_ttl_adjustment: true,
            stats_collection_interval: Duration::from_secs(60),
            enable_cache_warming: false,
        }
    }
}

/// 多层缓存服务实现
///
/// 【目的】: 基于DragonflyCache实现多层缓存策略
/// 【特性】: 支持热温冷数据分层、TTL自动管理、缓存统计等
pub struct MultiTierCacheService {
    /// 底层缓存服务
    cache_service: Arc<DragonflyCache>,
    /// 多层缓存配置
    config: MultiTierCacheConfig,
    /// 缓存统计信息
    stats: Arc<tokio::sync::RwLock<MultiTierCacheStats>>,
}

/// 多层缓存统计信息
///
/// 【目的】: 记录各层级缓存的使用统计
#[derive(Debug, Clone, Default)]
pub struct MultiTierCacheStats {
    /// 热数据层统计
    pub hot_tier_stats: TierStats,
    /// 温数据层统计
    pub warm_tier_stats: TierStats,
    /// 冷数据层统计
    pub cold_tier_stats: TierStats,
    /// 总体统计
    pub total_operations: u64,
    /// 缓存命中率
    pub hit_rate: f64,
}

/// 单层缓存统计
#[derive(Debug, Clone, Default)]
pub struct TierStats {
    /// 读取次数
    pub reads: u64,
    /// 写入次数
    pub writes: u64,
    /// 命中次数
    pub hits: u64,
    /// 未命中次数
    pub misses: u64,
    /// 过期次数
    pub expirations: u64,
}

impl MultiTierCacheService {
    /// 创建新的多层缓存服务
    ///
    /// 【参数】:
    /// - manager: 缓存客户端管理器
    /// - config: 多层缓存配置
    ///
    /// 【返回】: 多层缓存服务实例
    pub fn new(manager: Arc<CacheClientManager>, config: MultiTierCacheConfig) -> Self {
        info!("🚀 创建多层缓存服务");
        debug!("多层缓存配置: {:?}", config);

        let cache_service = Arc::new(DragonflyCache::new(manager));
        let stats = Arc::new(tokio::sync::RwLock::new(MultiTierCacheStats::default()));

        Self {
            cache_service,
            config,
            stats,
        }
    }

    /// 根据缓存层级设置数据
    ///
    /// 【参数】:
    /// - tier: 缓存层级
    /// - key: 缓存键
    /// - value: 缓存值
    /// - custom_ttl: 自定义TTL（可选）
    ///
    /// 【返回】: 操作结果
    pub async fn set_with_tier<T>(
        &self,
        tier: CacheTier,
        key: &str,
        value: &T,
        custom_ttl: Option<Duration>
    ) -> AnyhowResult<()>
        where T: Serialize + Send + Sync
    {
        let full_key = self.build_tier_key(tier, key);
        let ttl = custom_ttl.unwrap_or_else(|| tier.default_ttl());

        // 验证TTL不超过层级最大值
        let max_ttl = tier.max_ttl();
        let final_ttl = if ttl > max_ttl {
            warn!(
                "TTL {}秒超过{}层级最大值{}秒，使用最大值",
                ttl.as_secs(),
                tier.key_prefix(),
                max_ttl.as_secs()
            );
            max_ttl
        } else {
            ttl
        };

        debug!("设置{}层级缓存: {} (TTL: {}秒)", tier.key_prefix(), full_key, final_ttl.as_secs());

        let result = self.cache_service.set(&full_key, value, Some(final_ttl)).await;

        // 更新统计信息
        if result.is_ok() {
            self.update_tier_stats(tier, |stats| {
                stats.writes += 1;
            }).await;
        }

        result
    }

    /// 根据缓存层级获取数据
    ///
    /// 【参数】:
    /// - tier: 缓存层级
    /// - key: 缓存键
    ///
    /// 【返回】: 缓存值（如果存在）
    pub async fn get_with_tier<T>(&self, tier: CacheTier, key: &str) -> AnyhowResult<Option<T>>
        where T: for<'de> Deserialize<'de> + Send
    {
        let full_key = self.build_tier_key(tier, key);
        debug!("获取{}层级缓存: {}", tier.key_prefix(), full_key);

        let result = self.cache_service.get(&full_key).await;

        // 更新统计信息
        match &result {
            Ok(Some(_)) => {
                self.update_tier_stats(tier, |stats| {
                    stats.reads += 1;
                    stats.hits += 1;
                }).await;
            }
            Ok(None) => {
                self.update_tier_stats(tier, |stats| {
                    stats.reads += 1;
                    stats.misses += 1;
                }).await;
            }
            Err(_) => {
                self.update_tier_stats(tier, |stats| {
                    stats.reads += 1;
                }).await;
            }
        }

        result
    }

    /// 智能获取数据（自动检测层级）
    ///
    /// 【参数】:
    /// - key: 缓存键
    ///
    /// 【返回】: 缓存值（如果存在）
    pub async fn smart_get<T>(&self, key: &str) -> AnyhowResult<Option<T>>
        where T: for<'de> Deserialize<'de> + Send
    {
        if let Some(tier) = CacheTier::from_key(key) {
            // 键包含层级前缀，直接使用
            self.get_with_tier(tier, &key[4..]).await // 去掉前缀 "hot:", "warm:", "cold:"
        } else if self.config.auto_tier_detection {
            // 尝试从不同层级查找
            debug!("自动检测缓存层级: {}", key);

            // 按优先级顺序查找：热 -> 温 -> 冷
            for tier in [CacheTier::Hot, CacheTier::Warm, CacheTier::Cold] {
                if let Ok(Some(value)) = self.get_with_tier(tier, key).await {
                    debug!("在{}层级找到缓存: {}", tier.key_prefix(), key);
                    return Ok(Some(value));
                }
            }
            Ok(None)
        } else {
            // 使用默认缓存服务
            self.cache_service.get(key).await
        }
    }

    /// 构建带层级前缀的缓存键
    ///
    /// 【参数】:
    /// - tier: 缓存层级
    /// - key: 原始键
    ///
    /// 【返回】: 带前缀的完整键
    fn build_tier_key(&self, tier: CacheTier, key: &str) -> String {
        format!("{}:{}", tier.key_prefix(), key)
    }

    /// 更新层级统计信息
    ///
    /// 【参数】:
    /// - tier: 缓存层级
    /// - updater: 统计更新函数
    async fn update_tier_stats<F>(&self, tier: CacheTier, updater: F)
        where F: FnOnce(&mut TierStats)
    {
        let mut stats = self.stats.write().await;
        stats.total_operations += 1;

        let tier_stats = match tier {
            CacheTier::Hot => &mut stats.hot_tier_stats,
            CacheTier::Warm => &mut stats.warm_tier_stats,
            CacheTier::Cold => &mut stats.cold_tier_stats,
        };

        updater(tier_stats);

        // 更新总体命中率
        let total_hits =
            stats.hot_tier_stats.hits + stats.warm_tier_stats.hits + stats.cold_tier_stats.hits;
        let total_reads =
            stats.hot_tier_stats.reads + stats.warm_tier_stats.reads + stats.cold_tier_stats.reads;

        if total_reads > 0 {
            stats.hit_rate = (total_hits as f64) / (total_reads as f64);
        }
    }

    /// 获取多层缓存统计信息
    ///
    /// 【返回】: 统计信息
    pub async fn get_stats(&self) -> MultiTierCacheStats {
        self.stats.read().await.clone()
    }
}

#[async_trait]
impl CacheService for MultiTierCacheService {
    /// 获取缓存值（委托给智能获取）
    async fn get<T>(&self, key: &str) -> AnyhowResult<Option<T>>
        where T: for<'de> Deserialize<'de> + Send
    {
        self.smart_get(key).await
    }

    /// 设置缓存值（使用默认层级推断）
    async fn set<T>(&self, key: &str, value: &T, ttl: Option<Duration>) -> AnyhowResult<()>
        where T: Serialize + Send + Sync
    {
        if let Some(tier) = CacheTier::from_key(key) {
            // 键包含层级前缀
            self.set_with_tier(tier, &key[4..], value, ttl).await
        } else {
            // 根据TTL推断层级
            let tier = ttl
                .map(|t| {
                    if t <= Duration::from_secs(15 * 60) {
                        CacheTier::Hot
                    } else if t <= Duration::from_secs(2 * 60 * 60) {
                        CacheTier::Warm
                    } else {
                        CacheTier::Cold
                    }
                })
                .unwrap_or(CacheTier::Warm); // 默认温数据

            self.set_with_tier(tier, key, value, ttl).await
        }
    }

    /// 删除缓存值
    async fn delete(&self, key: &str) -> AnyhowResult<bool> {
        if let Some(tier) = CacheTier::from_key(key) {
            let full_key = self.build_tier_key(tier, &key[4..]);
            self.cache_service.delete(&full_key).await
        } else {
            self.cache_service.delete(key).await
        }
    }

    /// 检查缓存键是否存在
    async fn exists(&self, key: &str) -> AnyhowResult<bool> {
        if let Some(tier) = CacheTier::from_key(key) {
            let full_key = self.build_tier_key(tier, &key[4..]);
            self.cache_service.exists(&full_key).await
        } else {
            self.cache_service.exists(key).await
        }
    }

    /// 设置缓存过期时间
    async fn expire(&self, key: &str, ttl: Duration) -> AnyhowResult<bool> {
        if let Some(tier) = CacheTier::from_key(key) {
            let full_key = self.build_tier_key(tier, &key[4..]);
            self.cache_service.expire(&full_key, ttl).await
        } else {
            self.cache_service.expire(key, ttl).await
        }
    }

    /// 获取缓存键的剩余生存时间
    async fn ttl(&self, key: &str) -> AnyhowResult<i64> {
        if let Some(tier) = CacheTier::from_key(key) {
            let full_key = self.build_tier_key(tier, &key[4..]);
            self.cache_service.ttl(&full_key).await
        } else {
            self.cache_service.ttl(key).await
        }
    }

    /// 批量获取缓存值
    async fn mget<T>(&self, keys: &[&str]) -> AnyhowResult<Vec<Option<T>>>
        where T: for<'de> Deserialize<'de> + Send
    {
        // 对于批量操作，委托给底层服务
        self.cache_service.mget(keys).await
    }

    /// 批量设置缓存值
    async fn mset<T>(&self, pairs: &[(&str, &T)], ttl: Option<Duration>) -> AnyhowResult<()>
        where T: Serialize + Send + Sync
    {
        // 对于批量操作，委托给底层服务
        self.cache_service.mset(pairs, ttl).await
    }

    /// 清空所有缓存
    async fn flush_all(&self) -> AnyhowResult<()> {
        self.cache_service.flush_all().await
    }

    /// 获取原始缓存值（实现 CacheService trait）
    async fn get_raw(&self, key: &str) -> AnyhowResult<String> {
        // 委托给底层缓存服务
        self.cache_service.get_raw(key).await
    }
}

impl MultiTierCacheService {
    /// 从指定层级获取原始缓存值（用于兼容性检查）
    ///
    /// 【参数】:
    /// - tier: 缓存层级
    /// - key: 缓存键
    ///
    /// 【返回】: 原始缓存值或错误
    ///
    /// 【用途】: 专门用于缓存兼容性检查，不进行任何数据转换
    pub async fn get_raw_from_tier(
        &self,
        tier: CacheTier,
        key: &str
    ) -> AnyhowResult<Option<String>> {
        let full_key = self.build_tier_key(tier, key);
        debug!("获取{}层级原始缓存: {}", tier.key_prefix(), full_key);

        // 直接从底层缓存服务获取原始字符串值
        match self.cache_service.get_raw(&full_key).await {
            Ok(value) => Ok(Some(value)),
            Err(e) => {
                let error_msg = e.to_string();
                if error_msg.contains("缓存键不存在") || error_msg.contains("key not found") {
                    Ok(None)
                } else {
                    Err(e)
                }
            }
        }
    }
}

/// 创建多层缓存服务的便利函数
///
/// 【参数】:
/// - cache_config: 缓存配置
/// - multi_tier_config: 多层缓存配置
///
/// 【返回】: 多层缓存服务实例
pub async fn create_multi_tier_cache_service(
    cache_config: super::config::CacheConfig,
    multi_tier_config: MultiTierCacheConfig
) -> AnyhowResult<Arc<MultiTierCacheService>> {
    // 创建缓存客户端管理器
    let cache_manager = Arc::new(
        super::client_manager::CacheClientManager::new(cache_config).await?
    );

    // 创建多层缓存服务（直接使用管理器）
    let multi_tier_service = MultiTierCacheService::new(cache_manager, multi_tier_config);

    Ok(Arc::new(multi_tier_service))
}
