//! # 搜索结果预计算缓存模块
//!
//! 专门用于管理搜索结果预计算的缓存系统，提供：
//! - 预计算结果存储和检索
//! - 热门搜索词缓存管理
//! - 预计算任务状态缓存
//! - 缓存预热和更新机制
//! - 性能监控和统计

use crate::cache::{CacheService, CacheTier, MultiTierCacheService};
use anyhow::Result as AnyhowResult;
use app_domain::entities::search_task::PrecomputeExecutionStats;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;
use tracing::{debug, error, info};
use uuid::Uuid;

/// 预计算结果实体
///
/// 存储预计算的搜索结果数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrecomputedResult {
    /// 搜索查询
    pub query: String,
    /// 预计算结果数据（JSON格式）
    pub result_data: serde_json::Value,
    /// 结果总数
    pub total_count: usize,
    /// 预计算时间
    pub computed_at: DateTime<Utc>,
    /// 过期时间
    pub expires_at: DateTime<Utc>,
    /// 搜索频次（用于优先级排序）
    pub frequency: u32,
    /// 平均响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 缓存命中次数
    pub cache_hits: u32,
    /// 数据版本（用于缓存失效）
    pub version: u32,
    /// 元数据
    pub metadata: HashMap<String, String>,
}

/// 预计算任务状态缓存
///
/// 缓存预计算任务的执行状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrecomputeTaskStatus {
    /// 任务ID
    pub task_id: Uuid,
    /// 任务状态
    pub status: String,
    /// 进度百分比（0-100）
    pub progress: u8,
    /// 开始时间
    pub started_at: Option<DateTime<Utc>>,
    /// 预计完成时间
    pub estimated_completion: Option<DateTime<Utc>>,
    /// 错误信息
    pub error_message: Option<String>,
    /// 执行统计
    pub execution_stats: Option<PrecomputeExecutionStats>,
}

/// 预计算缓存配置
///
/// 定义预计算缓存的配置参数
#[derive(Debug, Clone)]
pub struct PrecomputeCacheConfig {
    /// 预计算结果缓存TTL（秒）
    pub result_cache_ttl: u32,
    /// 任务状态缓存TTL（秒）
    pub task_status_ttl: u32,
    /// 热门搜索词缓存TTL（秒）
    pub hot_query_ttl: u32,
    /// 最大缓存条目数
    pub max_cache_entries: usize,
    /// 缓存预热阈值（搜索频次）
    pub warmup_threshold: u32,
}

impl Default for PrecomputeCacheConfig {
    fn default() -> Self {
        Self {
            result_cache_ttl: 3600, // 1小时
            task_status_ttl: 300,   // 5分钟
            hot_query_ttl: 1800,    // 30分钟
            max_cache_entries: 1000,
            warmup_threshold: 10,
        }
    }
}

/// 预计算缓存统计信息
///
/// 记录预计算缓存的性能统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PrecomputeCacheStats {
    /// 总缓存条目数
    pub total_entries: u32,
    /// 预计算结果缓存命中率
    pub result_hit_rate: f64,
    /// 任务状态缓存命中率
    pub task_status_hit_rate: f64,
    /// 缓存预热次数
    pub warmup_count: u32,
    /// 缓存失效次数
    pub invalidation_count: u32,
    /// 平均缓存响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 最后更新时间
    pub last_updated: DateTime<Utc>,
}

/// 搜索结果预计算缓存服务
///
/// 管理预计算搜索结果的缓存操作
pub struct PrecomputeCache {
    /// 底层缓存服务
    cache_service: Arc<MultiTierCacheService>,
    /// 缓存配置
    config: PrecomputeCacheConfig,
    /// 缓存统计信息
    stats: Arc<tokio::sync::RwLock<PrecomputeCacheStats>>,
}

impl PrecomputeCache {
    /// 创建新的预计算缓存服务
    ///
    /// # 参数
    /// - `cache_service`: 底层缓存服务
    /// - `config`: 缓存配置
    ///
    /// # 返回
    /// - 新创建的预计算缓存服务实例
    pub fn new(cache_service: Arc<MultiTierCacheService>, config: PrecomputeCacheConfig) -> Self {
        Self {
            cache_service,
            config,
            stats: Arc::new(tokio::sync::RwLock::new(PrecomputeCacheStats {
                total_entries: 0,
                result_hit_rate: 0.0,
                task_status_hit_rate: 0.0,
                warmup_count: 0,
                invalidation_count: 0,
                avg_response_time_ms: 0.0,
                last_updated: Utc::now(),
            })),
        }
    }

    /// 缓存预计算结果
    ///
    /// # 参数
    /// - `result`: 预计算结果
    ///
    /// # 返回
    /// - 操作结果
    pub async fn cache_precomputed_result(&self, result: &PrecomputedResult) -> AnyhowResult<()> {
        let cache_key = self.build_result_cache_key(&result.query);

        debug!(
            "缓存预计算结果: {} (总数: {})",
            result.query, result.total_count
        );

        // 根据搜索频次选择缓存层级
        let cache_tier = if result.frequency >= self.config.warmup_threshold {
            CacheTier::Hot // 热门搜索词使用热缓存
        } else {
            CacheTier::Warm // 普通搜索词使用温缓存
        };

        // 缓存预计算结果
        self.cache_service
            .set_with_tier(
                cache_tier,
                &cache_key,
                result,
                Some(Duration::from_secs(self.config.result_cache_ttl as u64)),
            )
            .await?;

        // 更新统计信息
        self.update_cache_stats().await;

        info!(
            "预计算结果已缓存: {} (层级: {:?})",
            result.query, cache_tier
        );
        Ok(())
    }

    /// 获取预计算结果
    ///
    /// # 参数
    /// - `query`: 搜索查询
    ///
    /// # 返回
    /// - 预计算结果（如果存在）
    pub async fn get_precomputed_result(
        &self,
        query: &str,
    ) -> AnyhowResult<Option<PrecomputedResult>> {
        let cache_key = self.build_result_cache_key(query);

        debug!("查找预计算结果: {}", query);

        // 先从热缓存查找
        if let Ok(Some(result)) = self
            .cache_service
            .get_with_tier::<PrecomputedResult>(CacheTier::Hot, &cache_key)
            .await
        {
            debug!("从热缓存命中预计算结果: {}", query);
            self.record_cache_hit(true).await;
            return Ok(Some(result));
        }

        // 再从温缓存查找
        if let Ok(Some(result)) = self
            .cache_service
            .get_with_tier::<PrecomputedResult>(CacheTier::Warm, &cache_key)
            .await
        {
            debug!("从温缓存命中预计算结果: {}", query);
            self.record_cache_hit(true).await;

            // 如果是热门查询，提升到热缓存
            if result.frequency >= self.config.warmup_threshold {
                let _ = self
                    .cache_service
                    .set_with_tier(
                        CacheTier::Hot,
                        &cache_key,
                        &result,
                        Some(Duration::from_secs(self.config.result_cache_ttl as u64)),
                    )
                    .await;
                debug!("将热门预计算结果提升到热缓存: {}", query);
            }

            return Ok(Some(result));
        }

        debug!("预计算结果缓存未命中: {}", query);
        self.record_cache_hit(false).await;
        Ok(None)
    }

    /// 缓存预计算任务状态
    ///
    /// # 参数
    /// - `task_status`: 任务状态
    ///
    /// # 返回
    /// - 操作结果
    pub async fn cache_task_status(&self, task_status: &PrecomputeTaskStatus) -> AnyhowResult<()> {
        let cache_key = self.build_task_status_key(task_status.task_id);

        debug!(
            "缓存预计算任务状态: {} (状态: {})",
            task_status.task_id, task_status.status
        );

        // 使用温缓存存储任务状态
        self.cache_service
            .set_with_tier(
                CacheTier::Warm,
                &cache_key,
                task_status,
                Some(Duration::from_secs(self.config.task_status_ttl as u64)),
            )
            .await?;

        Ok(())
    }

    /// 获取预计算任务状态
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    ///
    /// # 返回
    /// - 任务状态（如果存在）
    pub async fn get_task_status(
        &self,
        task_id: Uuid,
    ) -> AnyhowResult<Option<PrecomputeTaskStatus>> {
        let cache_key = self.build_task_status_key(task_id);

        debug!("查找预计算任务状态: {}", task_id);

        match self
            .cache_service
            .get_with_tier::<PrecomputeTaskStatus>(CacheTier::Warm, &cache_key)
            .await
        {
            Ok(Some(status)) => {
                debug!("找到预计算任务状态: {} (状态: {})", task_id, status.status);
                Ok(Some(status))
            }
            Ok(None) => {
                debug!("预计算任务状态未找到: {}", task_id);
                Ok(None)
            }
            Err(e) => {
                error!("获取预计算任务状态失败: {} - {}", task_id, e);
                Err(e)
            }
        }
    }

    /// 失效预计算结果缓存
    ///
    /// # 参数
    /// - `query`: 搜索查询
    ///
    /// # 返回
    /// - 操作结果
    pub async fn invalidate_precomputed_result(&self, query: &str) -> AnyhowResult<()> {
        let cache_key = self.build_result_cache_key(query);

        debug!("失效预计算结果缓存: {}", query);

        // 从所有缓存层级中删除
        let _ = self.cache_service.delete(&cache_key).await;

        // 更新统计信息
        let mut stats = self.stats.write().await;
        stats.invalidation_count += 1;
        stats.last_updated = Utc::now();

        info!("预计算结果缓存已失效: {}", query);
        Ok(())
    }

    /// 获取缓存统计信息
    ///
    /// # 返回
    /// - 缓存统计信息
    pub async fn get_cache_stats(&self) -> PrecomputeCacheStats {
        let stats = self.stats.read().await;
        stats.clone()
    }

    /// 构建预计算结果缓存键
    ///
    /// # 参数
    /// - `query`: 搜索查询
    ///
    /// # 返回
    /// - 缓存键
    fn build_result_cache_key(&self, query: &str) -> String {
        format!("precompute:result:{query}")
    }

    /// 构建任务状态缓存键
    ///
    /// # 参数
    /// - `task_id`: 任务ID
    ///
    /// # 返回
    /// - 缓存键
    fn build_task_status_key(&self, task_id: Uuid) -> String {
        format!("precompute:task:{task_id}")
    }

    /// 记录缓存命中情况
    ///
    /// # 参数
    /// - `hit`: 是否命中
    async fn record_cache_hit(&self, hit: bool) {
        let mut stats = self.stats.write().await;

        // 简化的命中率计算（实际实现中应该维护更详细的统计）
        if hit {
            stats.result_hit_rate = stats.result_hit_rate * 0.9 + 0.1;
        } else {
            stats.result_hit_rate *= 0.9;
        }

        stats.last_updated = Utc::now();
    }

    /// 更新缓存统计信息
    async fn update_cache_stats(&self) {
        let mut stats = self.stats.write().await;
        stats.total_entries += 1;
        stats.last_updated = Utc::now();
    }
}
