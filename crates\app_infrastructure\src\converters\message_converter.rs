//! # Message数据转换器
//!
//! 实现Message实体与数据库模型之间的转换逻辑，
//! 使用统一的转换接口消除重复代码。

use crate::entities::{MessageActiveModel, MessageModel};
use app_common::error::{AppError, Result};
use app_common::utils::data_conversion::{DataConverter, EntityToActiveModel, ModelToEntity};
use app_domain::entities::{Message, MessageStatus, MessageType};
use migration::message_entity::{MessageStatus as DbMessageStatus, MessageType as DbMessageType};

/// Message模型到实体转换器
///
/// 【功能】：实现从数据库模型到领域实体的转换
pub struct MessageModelToEntityConverter;

impl ModelToEntity<MessageModel, Message> for MessageModelToEntityConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// * `model` - 数据库模型
    ///
    /// # 返回值
    /// * `Result<Message>` - 转换后的领域实体或错误
    fn model_to_entity(model: MessageModel) -> Result<Message> {
        // 验证必需字段
        if model.content.trim().is_empty() {
            return Err(AppError::ValidationError("消息内容不能为空".to_string()));
        }

        // 转换枚举类型
        let message_type = match model.message_type {
            DbMessageType::Text => MessageType::Text,
            DbMessageType::Image => MessageType::Image,
            DbMessageType::File => MessageType::File,
            DbMessageType::System => MessageType::System,
            DbMessageType::Voice => MessageType::Voice,
            DbMessageType::Video => MessageType::Video,
        };

        let status = match model.status {
            DbMessageStatus::Sent => MessageStatus::Sent,
            DbMessageStatus::Delivered => MessageStatus::Delivered,
            DbMessageStatus::Read => MessageStatus::Read,
            DbMessageStatus::Deleted => MessageStatus::Deleted,
            DbMessageStatus::Edited => MessageStatus::Edited,
        };

        Ok(Message {
            id: model.id,
            content: model.content,
            message_type,
            status,
            sender_id: model.sender_id,
            chat_room_id: model.chat_room_id,
            reply_to_id: model.reply_to_id,
            metadata: model.metadata,
            priority: model.priority,
            is_pinned: model.is_pinned,
            expires_at: model.expires_at,
            created_at: model.created_at,
            updated_at: model.updated_at,
        })
    }
}

/// Message实体到活动模型转换器
///
/// 【功能】：实现从领域实体到数据库活动模型的转换
pub struct MessageEntityToActiveModelConverter;

impl EntityToActiveModel<Message, MessageActiveModel> for MessageEntityToActiveModelConverter {
    /// 将领域实体转换为数据库活动模型（用于插入）
    ///
    /// # 参数
    /// * `entity` - 领域实体
    ///
    /// # 返回值
    /// * `Result<MessageActiveModel>` - 转换后的活动模型或错误
    fn entity_to_active_model(entity: Message) -> Result<MessageActiveModel> {
        // 验证实体数据
        DataConverter::validate_string_length(&entity.content, "content", 1, 4000)?;

        // 转换枚举类型为数据库枚举
        let db_message_type = match entity.message_type {
            MessageType::Text => DbMessageType::Text,
            MessageType::Image => DbMessageType::Image,
            MessageType::File => DbMessageType::File,
            MessageType::System => DbMessageType::System,
            MessageType::Voice => DbMessageType::Voice,
            MessageType::Video => DbMessageType::Video,
        };

        let db_status = match entity.status {
            MessageStatus::Sent => DbMessageStatus::Sent,
            MessageStatus::Delivered => DbMessageStatus::Delivered,
            MessageStatus::Read => DbMessageStatus::Read,
            MessageStatus::Deleted => DbMessageStatus::Deleted,
            MessageStatus::Edited => DbMessageStatus::Edited,
        };

        Ok(MessageActiveModel {
            id: DataConverter::set_value(entity.id),
            chat_room_id: DataConverter::set_value(entity.chat_room_id),
            sender_id: DataConverter::set_value(entity.sender_id),
            content: DataConverter::set_value(entity.content),
            message_type: DataConverter::set_value(db_message_type),
            status: DataConverter::set_value(db_status),
            reply_to_id: DataConverter::option_to_active_value(entity.reply_to_id),
            metadata: DataConverter::option_to_active_value(entity.metadata),
            priority: DataConverter::set_value(entity.priority),
            is_pinned: DataConverter::set_value(entity.is_pinned),
            expires_at: DataConverter::option_to_active_value(entity.expires_at),
            created_at: DataConverter::set_value(entity.created_at),
            updated_at: DataConverter::set_value(entity.updated_at),
        })
    }

    /// 将领域实体转换为部分更新的活动模型（用于更新）
    ///
    /// # 参数
    /// * `entity` - 领域实体
    /// * `exclude_fields` - 要排除的字段列表
    ///
    /// # 返回值
    /// * `Result<MessageActiveModel>` - 转换后的活动模型或错误
    fn entity_to_partial_active_model(
        entity: Message,
        exclude_fields: &[&str],
    ) -> Result<MessageActiveModel> {
        // 验证实体数据
        DataConverter::validate_string_length(&entity.content, "content", 1, 4000)?;

        // 转换枚举类型为数据库枚举
        let db_message_type = match entity.message_type {
            MessageType::Text => DbMessageType::Text,
            MessageType::Image => DbMessageType::Image,
            MessageType::File => DbMessageType::File,
            MessageType::System => DbMessageType::System,
            MessageType::Voice => DbMessageType::Voice,
            MessageType::Video => DbMessageType::Video,
        };

        let db_status = match entity.status {
            MessageStatus::Sent => DbMessageStatus::Sent,
            MessageStatus::Delivered => DbMessageStatus::Delivered,
            MessageStatus::Read => DbMessageStatus::Read,
            MessageStatus::Deleted => DbMessageStatus::Deleted,
            MessageStatus::Edited => DbMessageStatus::Edited,
        };

        let mut active_model = MessageActiveModel {
            id: DataConverter::set_value(entity.id),
            chat_room_id: DataConverter::set_value(entity.chat_room_id),
            sender_id: DataConverter::set_value(entity.sender_id),
            content: DataConverter::set_value(entity.content),
            message_type: DataConverter::set_value(db_message_type),
            status: DataConverter::set_value(db_status),
            reply_to_id: DataConverter::option_to_active_value(entity.reply_to_id),
            metadata: DataConverter::option_to_active_value(entity.metadata),
            priority: DataConverter::set_value(entity.priority),
            is_pinned: DataConverter::set_value(entity.is_pinned),
            expires_at: DataConverter::option_to_active_value(entity.expires_at),
            created_at: DataConverter::preserve_timestamp(), // 保持创建时间不变
            updated_at: DataConverter::update_timestamp(),   // 自动更新修改时间
        };

        // 根据排除字段列表设置为NotSet
        for field in exclude_fields {
            match *field {
                "id" => {
                    active_model.id = DataConverter::not_set();
                }
                "chat_room_id" => {
                    active_model.chat_room_id = DataConverter::not_set();
                }
                "sender_id" => {
                    active_model.sender_id = DataConverter::not_set();
                }
                "content" => {
                    active_model.content = DataConverter::not_set();
                }
                "message_type" => {
                    active_model.message_type = DataConverter::not_set();
                }
                "status" => {
                    active_model.status = DataConverter::not_set();
                }
                "reply_to_id" => {
                    active_model.reply_to_id = DataConverter::not_set();
                }
                "metadata" => {
                    active_model.metadata = DataConverter::not_set();
                }
                "priority" => {
                    active_model.priority = DataConverter::not_set();
                }
                "is_pinned" => {
                    active_model.is_pinned = DataConverter::not_set();
                }
                "expires_at" => {
                    active_model.expires_at = DataConverter::not_set();
                }
                "created_at" => {
                    active_model.created_at = DataConverter::not_set();
                }
                "updated_at" => {
                    active_model.updated_at = DataConverter::not_set();
                }
                _ => {
                    return Err(AppError::ValidationError(format!("未知的字段名: {field}")));
                }
            }
        }

        Ok(active_model)
    }
}

/// Message转换工具
///
/// 【功能】：提供Message相关的便捷转换方法
pub struct MessageConverter;

impl MessageConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// 【功能】：简化调用接口
    pub fn model_to_entity(model: MessageModel) -> Result<Message> {
        MessageModelToEntityConverter::model_to_entity(model)
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// 【功能】：简化调用接口
    pub fn entity_to_active_model(entity: Message) -> Result<MessageActiveModel> {
        MessageEntityToActiveModelConverter::entity_to_active_model(entity)
    }

    /// 将领域实体转换为部分更新的活动模型
    ///
    /// 【功能】：简化调用接口，用于更新操作
    pub fn entity_to_partial_active_model(
        entity: Message,
        exclude_fields: &[&str],
    ) -> Result<MessageActiveModel> {
        MessageEntityToActiveModelConverter::entity_to_partial_active_model(entity, exclude_fields)
    }

    /// 更新消息状态的活动模型
    ///
    /// 【功能】：仅更新消息状态
    pub fn update_message_status_active_model(
        id: uuid::Uuid,
        status: MessageStatus,
    ) -> Result<MessageActiveModel> {
        let db_status = match status {
            MessageStatus::Sent => DbMessageStatus::Sent,
            MessageStatus::Delivered => DbMessageStatus::Delivered,
            MessageStatus::Read => DbMessageStatus::Read,
            MessageStatus::Deleted => DbMessageStatus::Deleted,
            MessageStatus::Edited => DbMessageStatus::Edited,
        };

        Ok(MessageActiveModel {
            id: DataConverter::set_value(id),
            chat_room_id: DataConverter::not_set(),
            sender_id: DataConverter::not_set(),
            content: DataConverter::not_set(),
            message_type: DataConverter::not_set(),
            status: DataConverter::set_value(db_status),
            reply_to_id: DataConverter::not_set(),
            metadata: DataConverter::not_set(),
            priority: DataConverter::not_set(),
            is_pinned: DataConverter::not_set(),
            expires_at: DataConverter::not_set(),
            created_at: DataConverter::not_set(),
            updated_at: DataConverter::update_timestamp(),
        })
    }

    /// 批量转换模型到实体
    ///
    /// 【功能】：批量处理数据库查询结果
    pub fn models_to_entities(models: Vec<MessageModel>) -> Result<Vec<Message>> {
        models.into_iter().map(Self::model_to_entity).collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;
    use uuid::Uuid;

    fn create_test_message_model() -> MessageModel {
        MessageModel {
            id: Uuid::new_v4(),
            chat_room_id: Uuid::new_v4(),
            sender_id: Uuid::new_v4(),
            content: "测试消息".to_string(),
            message_type: DbMessageType::Text,
            status: DbMessageStatus::Sent,
            reply_to_id: None,
            metadata: None,
            priority: 0,
            is_pinned: false,
            expires_at: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    #[test]
    fn test_model_to_entity_conversion() {
        let model = create_test_message_model();
        let result = MessageConverter::model_to_entity(model.clone());

        assert!(result.is_ok());
        let entity = result.unwrap();
        assert_eq!(entity.id, model.id);
        assert_eq!(entity.content, model.content);
        assert!(matches!(entity.message_type, MessageType::Text));
        assert!(matches!(entity.status, MessageStatus::Sent));
    }

    #[test]
    fn test_validation_error_empty_content() {
        let mut model = create_test_message_model();
        model.content = "".to_string(); // 空内容应该失败

        let result = MessageConverter::model_to_entity(model);
        assert!(result.is_err());
    }
}
