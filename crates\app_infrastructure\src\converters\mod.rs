//! # 数据转换器模块
//!
//! 包含所有实体与数据库模型之间的转换器实现，
//! 统一数据转换逻辑，消除重复代码。

pub mod message_converter;
pub mod task_converter;
pub mod traits;
pub mod user_converter;
pub mod user_session_converter;

// 重新导出转换器
pub use message_converter::{
    MessageConverter, MessageEntityToActiveModelConverter, MessageModelToEntityConverter,
};
pub use task_converter::{
    TaskConverter, TaskEntityToActiveModelConverter, TaskModelToEntityConverter,
};
pub use user_converter::{
    UserConverter, UserEntityToActiveModelConverter, UserModelToEntityConverter,
};
pub use user_session_converter::{
    UserSessionConverter, UserSessionEntityToActiveModelConverter,
    UserSessionModelToEntityConverter,
};
