//! # Task数据转换器
//!
//! 实现Task实体与数据库模型之间的转换逻辑，
//! 使用统一的转换接口消除重复代码。

use crate::entities::{TaskActiveModel, TaskModel};
use app_common::error::{AppError, Result};
use app_common::utils::data_conversion::{DataConverter, EntityToActiveModel, ModelToEntity};
use app_domain::entities::Task;

/// Task模型到实体转换器
///
/// 【功能】：实现从数据库模型到领域实体的转换
pub struct TaskModelToEntityConverter;

impl ModelToEntity<TaskModel, Task> for TaskModelToEntityConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// * `model` - 数据库模型
    ///
    /// # 返回值
    /// * `Result<Task>` - 转换后的领域实体或错误
    fn model_to_entity(model: TaskModel) -> Result<Task> {
        // 验证必需字段
        if model.title.trim().is_empty() {
            return Err(AppError::ValidationError("任务标题不能为空".to_string()));
        }

        Ok(Task {
            id: model.id,
            title: model.title,
            description: model.description,
            completed: model.completed,
            user_id: model.user_id,
            created_at: model.created_at,
            updated_at: model.updated_at,
        })
    }
}

/// Task实体到活动模型转换器
///
/// 【功能】：实现从领域实体到数据库活动模型的转换
pub struct TaskEntityToActiveModelConverter;

impl EntityToActiveModel<Task, TaskActiveModel> for TaskEntityToActiveModelConverter {
    /// 将领域实体转换为数据库活动模型（用于插入）
    ///
    /// # 参数
    /// * `entity` - 领域实体
    ///
    /// # 返回值
    /// * `Result<TaskActiveModel>` - 转换后的活动模型或错误
    fn entity_to_active_model(entity: Task) -> Result<TaskActiveModel> {
        // 验证实体数据
        DataConverter::validate_string_length(&entity.title, "title", 1, 255)?;

        // 验证可选的描述字段
        if let Some(ref desc) = entity.description {
            DataConverter::validate_string_length(desc, "description", 0, 1000)?;
        }

        Ok(TaskActiveModel {
            id: DataConverter::set_value(entity.id),
            title: DataConverter::set_value(entity.title),
            description: DataConverter::option_to_active_value(entity.description),
            completed: DataConverter::set_value(entity.completed),
            user_id: DataConverter::option_to_active_value(entity.user_id),
            created_at: DataConverter::set_value(entity.created_at),
            updated_at: DataConverter::set_value(entity.updated_at),
        })
    }

    /// 将领域实体转换为部分更新的活动模型（用于更新）
    ///
    /// # 参数
    /// * `entity` - 领域实体
    /// * `exclude_fields` - 要排除的字段列表
    ///
    /// # 返回值
    /// * `Result<TaskActiveModel>` - 转换后的活动模型或错误
    fn entity_to_partial_active_model(
        entity: Task,
        exclude_fields: &[&str],
    ) -> Result<TaskActiveModel> {
        // 验证实体数据
        DataConverter::validate_string_length(&entity.title, "title", 1, 255)?;

        // 验证可选的描述字段
        if let Some(ref desc) = entity.description {
            DataConverter::validate_string_length(desc, "description", 0, 1000)?;
        }

        let mut active_model = TaskActiveModel {
            id: DataConverter::set_value(entity.id),
            title: DataConverter::set_value(entity.title),
            description: DataConverter::set_value(entity.description),
            completed: DataConverter::set_value(entity.completed),
            user_id: DataConverter::set_value(entity.user_id),
            created_at: DataConverter::preserve_timestamp(), // 保持创建时间不变
            updated_at: DataConverter::update_timestamp(),   // 自动更新修改时间
        };

        // 根据排除字段列表设置为NotSet
        for field in exclude_fields {
            match *field {
                "id" => {
                    active_model.id = DataConverter::not_set();
                }
                "title" => {
                    active_model.title = DataConverter::not_set();
                }
                "description" => {
                    active_model.description = DataConverter::not_set();
                }
                "completed" => {
                    active_model.completed = DataConverter::not_set();
                }
                "user_id" => {
                    active_model.user_id = DataConverter::not_set();
                }
                "created_at" => {
                    active_model.created_at = DataConverter::not_set();
                }
                "updated_at" => {
                    active_model.updated_at = DataConverter::not_set();
                }
                _ => {
                    return Err(AppError::ValidationError(format!("未知的字段名: {field}")));
                }
            }
        }

        Ok(active_model)
    }
}

/// Task转换工具
///
/// 【功能】：提供Task相关的便捷转换方法
pub struct TaskConverter;

impl TaskConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// 【功能】：简化调用接口
    pub fn model_to_entity(model: TaskModel) -> Result<Task> {
        TaskModelToEntityConverter::model_to_entity(model)
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// 【功能】：简化调用接口
    pub fn entity_to_active_model(entity: Task) -> Result<TaskActiveModel> {
        TaskEntityToActiveModelConverter::entity_to_active_model(entity)
    }

    /// 将领域实体转换为部分更新的活动模型
    ///
    /// 【功能】：简化调用接口，用于更新操作
    pub fn entity_to_partial_active_model(
        entity: Task,
        exclude_fields: &[&str],
    ) -> Result<TaskActiveModel> {
        TaskEntityToActiveModelConverter::entity_to_partial_active_model(entity, exclude_fields)
    }

    /// 创建新任务的活动模型
    ///
    /// 【功能】：为新任务创建带有自动生成字段的活动模型
    pub fn create_new_task_active_model(
        title: String,
        description: Option<String>,
        user_id: Option<uuid::Uuid>,
    ) -> Result<TaskActiveModel> {
        // 验证输入数据
        DataConverter::validate_string_length(&title, "title", 1, 255)?;

        // 验证可选的描述字段
        if let Some(ref desc) = description {
            DataConverter::validate_string_length(desc, "description", 0, 1000)?;
        }

        Ok(TaskActiveModel {
            id: DataConverter::new_uuid(),
            title: DataConverter::set_value(title),
            description: DataConverter::option_to_active_value(description),
            completed: DataConverter::set_value(false), // 新任务默认未完成
            user_id: DataConverter::option_to_active_value(user_id),
            created_at: DataConverter::current_timestamp(),
            updated_at: DataConverter::current_timestamp(),
        })
    }

    /// 更新任务状态的活动模型
    ///
    /// 【功能】：仅更新任务完成状态
    pub fn update_task_status_active_model(
        id: uuid::Uuid,
        completed: bool,
    ) -> Result<TaskActiveModel> {
        Ok(TaskActiveModel {
            id: DataConverter::set_value(id),
            title: DataConverter::not_set(),
            description: DataConverter::not_set(),
            completed: DataConverter::set_value(completed),
            user_id: DataConverter::not_set(),
            created_at: DataConverter::not_set(),
            updated_at: DataConverter::update_timestamp(),
        })
    }

    /// 批量转换模型到实体
    ///
    /// 【功能】：批量处理数据库查询结果
    pub fn models_to_entities(models: Vec<TaskModel>) -> Result<Vec<Task>> {
        models.into_iter().map(Self::model_to_entity).collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;
    use sea_orm::ActiveValue;
    use uuid::Uuid;

    fn create_test_task_model() -> TaskModel {
        TaskModel {
            id: Uuid::new_v4(),
            title: "测试任务".to_string(),
            description: Some("测试描述".to_string()),
            completed: false,
            user_id: Some(Uuid::new_v4()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    fn create_test_task() -> Task {
        Task {
            id: Uuid::new_v4(),
            title: "测试任务".to_string(),
            description: Some("测试描述".to_string()),
            completed: false,
            user_id: Some(Uuid::new_v4()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    #[test]
    fn test_model_to_entity_conversion() {
        let model = create_test_task_model();
        let result = TaskConverter::model_to_entity(model.clone());

        assert!(result.is_ok());
        let entity = result.unwrap();
        assert_eq!(entity.id, model.id);
        assert_eq!(entity.title, model.title);
        assert_eq!(entity.description, model.description);
        assert_eq!(entity.completed, model.completed);
        assert_eq!(entity.user_id, model.user_id);
    }

    #[test]
    fn test_entity_to_active_model_conversion() {
        let entity = create_test_task();
        let result = TaskConverter::entity_to_active_model(entity.clone());

        assert!(result.is_ok());
        let active_model = result.unwrap();

        match active_model.id {
            ActiveValue::Set(id) => assert_eq!(id, entity.id),
            _ => panic!("Expected Set value for id"),
        }

        match active_model.title {
            ActiveValue::Set(title) => assert_eq!(title, entity.title),
            _ => panic!("Expected Set value for title"),
        }
    }

    #[test]
    fn test_create_new_task_active_model() {
        let user_id = Uuid::new_v4();
        let result = TaskConverter::create_new_task_active_model(
            "新任务".to_string(),
            Some("新任务描述".to_string()),
            Some(user_id),
        );

        assert!(result.is_ok());
        let active_model = result.unwrap();

        match active_model.completed {
            ActiveValue::Set(completed) => assert!(!completed), // 新任务应该是未完成状态
            _ => panic!("Expected Set value for completed"),
        }
    }

    #[test]
    fn test_validation_error_empty_title() {
        let mut model = create_test_task_model();
        model.title = "".to_string(); // 空标题应该失败

        let result = TaskConverter::model_to_entity(model);
        assert!(result.is_err());
    }

    #[test]
    fn test_validation_error_long_title() {
        let entity = Task {
            id: Uuid::new_v4(),
            title: "a".repeat(300), // 超长标题应该失败
            description: Some("测试描述".to_string()),
            completed: false,
            user_id: Some(Uuid::new_v4()),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let result = TaskConverter::entity_to_active_model(entity);
        assert!(result.is_err());
    }
}
