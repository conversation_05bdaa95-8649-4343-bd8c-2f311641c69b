//! # 转换器特征定义
//!
//! 定义数据转换器的通用特征，提供统一的转换接口

use app_common::error::Result;

/// 模型到实体转换特征
///
/// 【功能】：定义从数据库模型到领域实体的转换接口
pub trait ModelToEntity<TModel, TEntity> {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// * `model` - 数据库模型
    ///
    /// # 返回值
    /// * `Result<TEntity>` - 转换后的领域实体或错误
    fn model_to_entity(model: TModel) -> Result<TEntity>;
}

/// 实体到活动模型转换特征
///
/// 【功能】：定义从领域实体到数据库活动模型的转换接口
pub trait EntityToActiveModel<TEntity, TActiveModel> {
    /// 将领域实体转换为数据库活动模型
    ///
    /// # 参数
    /// * `entity` - 领域实体
    ///
    /// # 返回值
    /// * `Result<TActiveModel>` - 转换后的活动模型或错误
    fn entity_to_active_model(entity: TEntity) -> Result<TActiveModel>;

    /// 将领域实体转换为部分更新的活动模型
    ///
    /// # 参数
    /// * `entity` - 领域实体
    /// * `exclude_fields` - 要排除的字段列表
    ///
    /// # 返回值
    /// * `Result<TActiveModel>` - 转换后的活动模型或错误
    fn entity_to_partial_active_model(
        entity: TEntity,
        exclude_fields: &[&str],
    ) -> Result<TActiveModel>;
}
