//! # User数据转换器
//!
//! 实现User实体与数据库模型之间的转换逻辑，
//! 使用统一的转换接口消除重复代码。

use crate::entities::{UserActiveModel, UserModel};
use app_common::error::{AppError, Result};
use app_common::utils::data_conversion::{DataConverter, EntityToActiveModel, ModelToEntity};
use app_domain::entities::User;

/// User模型到实体转换器
///
/// 【功能】：实现从数据库模型到领域实体的转换
pub struct UserModelToEntityConverter;

impl ModelToEntity<UserModel, User> for UserModelToEntityConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// * `model` - 数据库模型
    ///
    /// # 返回值
    /// * `Result<User>` - 转换后的领域实体或错误
    fn model_to_entity(model: UserModel) -> Result<User> {
        // 验证必需字段
        if model.username.trim().is_empty() {
            return Err(AppError::ValidationError("用户名不能为空".to_string()));
        }

        if model.password_hash.trim().is_empty() {
            return Err(AppError::ValidationError("密码哈希不能为空".to_string()));
        }

        Ok(User {
            id: model.id,
            username: model.username,
            email: model.email,
            password_hash: model.password_hash,
            display_name: model.display_name,
            avatar_url: model.avatar_url,
            bio: model.bio,
            location: model.location,
            website: model.website,
            last_login_at: model.last_login_at,
            status: model.status,
            created_at: model.created_at,
            updated_at: model.updated_at,
        })
    }
}

/// User实体到活动模型转换器
///
/// 【功能】：实现从领域实体到数据库活动模型的转换
pub struct UserEntityToActiveModelConverter;

impl EntityToActiveModel<User, UserActiveModel> for UserEntityToActiveModelConverter {
    /// 将领域实体转换为数据库活动模型（用于插入）
    ///
    /// # 参数
    /// * `entity` - 领域实体
    ///
    /// # 返回值
    /// * `Result<UserActiveModel>` - 转换后的活动模型或错误
    fn entity_to_active_model(entity: User) -> Result<UserActiveModel> {
        // 验证实体数据
        DataConverter::validate_string_length(&entity.username, "username", 3, 50)?;

        // 密码哈希长度验证（bcrypt哈希通常是60字符）
        if entity.password_hash.len() < 50 || entity.password_hash.len() > 100 {
            return Err(AppError::ValidationError("密码哈希长度无效".to_string()));
        }

        Ok(UserActiveModel {
            id: DataConverter::set_value(entity.id),
            username: DataConverter::set_value(entity.username),
            email: DataConverter::set_value(entity.email),
            password_hash: DataConverter::set_value(entity.password_hash),
            display_name: DataConverter::set_value(entity.display_name),
            avatar_url: DataConverter::set_value(entity.avatar_url),
            bio: DataConverter::set_value(entity.bio),
            location: DataConverter::set_value(entity.location),
            website: DataConverter::set_value(entity.website),
            last_login_at: DataConverter::set_value(entity.last_login_at),
            status: DataConverter::set_value(entity.status),
            created_at: DataConverter::set_value(entity.created_at),
            updated_at: DataConverter::set_value(entity.updated_at),
        })
    }

    /// 将领域实体转换为部分更新的活动模型（用于更新）
    ///
    /// # 参数
    /// * `entity` - 领域实体
    /// * `exclude_fields` - 要排除的字段列表
    ///
    /// # 返回值
    /// * `Result<UserActiveModel>` - 转换后的活动模型或错误
    fn entity_to_partial_active_model(
        entity: User,
        exclude_fields: &[&str],
    ) -> Result<UserActiveModel> {
        // 验证实体数据
        DataConverter::validate_string_length(&entity.username, "username", 3, 50)?;

        let mut active_model = UserActiveModel {
            id: DataConverter::set_value(entity.id),
            username: DataConverter::set_value(entity.username),
            email: DataConverter::set_value(entity.email),
            password_hash: DataConverter::set_value(entity.password_hash),
            display_name: DataConverter::set_value(entity.display_name),
            avatar_url: DataConverter::set_value(entity.avatar_url),
            bio: DataConverter::set_value(entity.bio),
            location: DataConverter::set_value(entity.location),
            website: DataConverter::set_value(entity.website),
            last_login_at: DataConverter::set_value(entity.last_login_at),
            status: DataConverter::set_value(entity.status),
            created_at: DataConverter::preserve_timestamp(), // 保持创建时间不变
            updated_at: DataConverter::update_timestamp(),   // 自动更新修改时间
        };

        // 根据排除字段列表设置为NotSet
        for field in exclude_fields {
            match *field {
                "id" => {
                    active_model.id = DataConverter::not_set();
                }
                "username" => {
                    active_model.username = DataConverter::not_set();
                }
                "email" => {
                    active_model.email = DataConverter::not_set();
                }
                "password_hash" => {
                    active_model.password_hash = DataConverter::not_set();
                }
                "created_at" => {
                    active_model.created_at = DataConverter::not_set();
                }
                "updated_at" => {
                    active_model.updated_at = DataConverter::not_set();
                }
                _ => {
                    return Err(AppError::ValidationError(format!("未知的字段名: {field}")));
                }
            }
        }

        Ok(active_model)
    }
}

/// User转换工具
///
/// 【功能】：提供User相关的便捷转换方法
pub struct UserConverter;

impl UserConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// 【功能】：简化调用接口
    pub fn model_to_entity(model: UserModel) -> Result<User> {
        UserModelToEntityConverter::model_to_entity(model)
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// 【功能】：简化调用接口
    pub fn entity_to_active_model(entity: User) -> Result<UserActiveModel> {
        UserEntityToActiveModelConverter::entity_to_active_model(entity)
    }

    /// 将领域实体转换为部分更新的活动模型
    ///
    /// 【功能】：简化调用接口，用于更新操作
    pub fn entity_to_partial_active_model(
        entity: User,
        exclude_fields: &[&str],
    ) -> Result<UserActiveModel> {
        UserEntityToActiveModelConverter::entity_to_partial_active_model(entity, exclude_fields)
    }

    /// 创建新用户的活动模型
    ///
    /// 【功能】：为新用户创建带有自动生成字段的活动模型
    pub fn create_new_user_active_model(
        username: String,
        password_hash: String,
    ) -> Result<UserActiveModel> {
        // 验证输入数据
        DataConverter::validate_string_length(&username, "username", 3, 50)?;

        if password_hash.len() < 50 || password_hash.len() > 100 {
            return Err(AppError::ValidationError("密码哈希长度无效".to_string()));
        }

        Ok(UserActiveModel {
            id: DataConverter::new_uuid(),
            username: DataConverter::set_value(username),
            email: DataConverter::set_value(None::<String>), // 新用户默认无邮箱
            password_hash: DataConverter::set_value(password_hash),
            display_name: DataConverter::set_value(None::<String>),
            avatar_url: DataConverter::set_value(None::<String>),
            bio: DataConverter::set_value(None::<String>),
            location: DataConverter::set_value(None::<String>),
            website: DataConverter::set_value(None::<String>),
            last_login_at: DataConverter::set_value(None::<chrono::DateTime<chrono::Utc>>),
            status: DataConverter::set_value("active".to_string()),
            created_at: DataConverter::current_timestamp(),
            updated_at: DataConverter::current_timestamp(),
        })
    }

    /// 更新用户密码的活动模型
    ///
    /// 【功能】：仅更新用户密码
    pub fn update_user_password_active_model(
        id: uuid::Uuid,
        new_password_hash: String,
    ) -> Result<UserActiveModel> {
        // 验证密码哈希
        if new_password_hash.len() < 50 || new_password_hash.len() > 100 {
            return Err(AppError::ValidationError("密码哈希长度无效".to_string()));
        }

        Ok(UserActiveModel {
            id: DataConverter::set_value(id),
            username: DataConverter::not_set(),
            email: DataConverter::not_set(),
            password_hash: DataConverter::set_value(new_password_hash),
            display_name: DataConverter::not_set(),
            avatar_url: DataConverter::not_set(),
            bio: DataConverter::not_set(),
            location: DataConverter::not_set(),
            website: DataConverter::not_set(),
            last_login_at: DataConverter::not_set(),
            status: DataConverter::not_set(),
            created_at: DataConverter::not_set(),
            updated_at: DataConverter::update_timestamp(),
        })
    }

    /// 批量转换模型到实体
    ///
    /// 【功能】：批量处理数据库查询结果
    pub fn models_to_entities(models: Vec<UserModel>) -> Result<Vec<User>> {
        models.into_iter().map(Self::model_to_entity).collect()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;
    use sea_orm::ActiveValue;
    use uuid::Uuid;

    fn create_test_user_model() -> UserModel {
        UserModel {
            id: Uuid::new_v4(),
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "$2b$12$abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOP"
                .to_string(),
            display_name: Some("Test User".to_string()),
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    fn create_test_user() -> User {
        User {
            id: Uuid::new_v4(),
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "$2b$12$abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOP"
                .to_string(),
            display_name: Some("Test User".to_string()),
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    #[test]
    fn test_model_to_entity_conversion() {
        let model = create_test_user_model();
        let result = UserConverter::model_to_entity(model.clone());

        assert!(result.is_ok());
        let entity = result.unwrap();
        assert_eq!(entity.id, model.id);
        assert_eq!(entity.username, model.username);
        assert_eq!(entity.password_hash, model.password_hash);
    }

    #[test]
    fn test_entity_to_active_model_conversion() {
        let entity = create_test_user();
        let result = UserConverter::entity_to_active_model(entity.clone());

        assert!(result.is_ok());
        let active_model = result.unwrap();

        match active_model.id {
            ActiveValue::Set(id) => assert_eq!(id, entity.id),
            _ => panic!("Expected Set value for id"),
        }

        match active_model.username {
            ActiveValue::Set(username) => assert_eq!(username, entity.username),
            _ => panic!("Expected Set value for username"),
        }
    }

    #[test]
    fn test_create_new_user_active_model() {
        let result = UserConverter::create_new_user_active_model(
            "newuser".to_string(),
            "$2b$12$abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOP".to_string(),
        );

        assert!(result.is_ok());
        let active_model = result.unwrap();

        match active_model.username {
            ActiveValue::Set(username) => assert_eq!(username, "newuser"),
            _ => panic!("Expected Set value for username"),
        }
    }

    #[test]
    fn test_validation_error_empty_username() {
        let mut model = create_test_user_model();
        model.username = "".to_string(); // 空用户名应该失败

        let result = UserConverter::model_to_entity(model);
        assert!(result.is_err());
    }

    #[test]
    fn test_validation_error_short_username() {
        let entity = User {
            id: Uuid::new_v4(),
            username: "ab".to_string(), // 用户名太短应该失败
            email: None,
            password_hash: "$2b$12$abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOP"
                .to_string(),
            display_name: None,
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let result = UserConverter::entity_to_active_model(entity);
        assert!(result.is_err());
    }

    #[test]
    fn test_validation_error_invalid_password_hash() {
        let entity = User {
            id: Uuid::new_v4(),
            username: "testuser".to_string(),
            email: None,
            password_hash: "short".to_string(), // 密码哈希太短应该失败
            display_name: None,
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let result = UserConverter::entity_to_active_model(entity);
        assert!(result.is_err());
    }
}
