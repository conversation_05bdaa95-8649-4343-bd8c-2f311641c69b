//! # 用户会话转换器
//!
//! 用户会话转换器模块，负责在领域实体和数据库模型之间进行转换

use crate::converters::traits::{EntityToActiveModel, ModelToEntity};
use crate::entities::{
    UserSessionActiveModel, UserSessionModel,
    user_session_entity::{DeviceType as DbDeviceType, SessionStatus as DbSessionStatus},
};
use app_common::error::{AppError, Result};
use app_domain::entities::user_session::{
    DeviceType as DomainDeviceType, SessionStatus as DomainSessionStatus, UserSession,
};
use sea_orm::{ActiveValue, Set};

/// 用户会话模型到实体转换器
///
/// 【功能】：实现从数据库模型到领域实体的转换
pub struct UserSessionModelToEntityConverter;

impl ModelToEntity<UserSessionModel, UserSession> for UserSessionModelToEntityConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// * `model` - 数据库模型
    ///
    /// # 返回值
    /// * `Result<UserSession>` - 转换后的领域实体或错误
    fn model_to_entity(model: UserSessionModel) -> Result<UserSession> {
        // 验证必需字段
        if model.session_token.trim().is_empty() {
            return Err(AppError::ValidationError("会话令牌不能为空".to_string()));
        }

        if model.ip_address.trim().is_empty() {
            return Err(AppError::ValidationError("IP地址不能为空".to_string()));
        }

        // 转换枚举类型
        let status = match model.status {
            DbSessionStatus::Online => DomainSessionStatus::Online,
            DbSessionStatus::Offline => DomainSessionStatus::Offline,
            DbSessionStatus::Away => DomainSessionStatus::Away,
            DbSessionStatus::Busy => DomainSessionStatus::Busy,
            DbSessionStatus::Invisible => DomainSessionStatus::Invisible,
        };

        let device_type = match model.device_type {
            DbDeviceType::Desktop => DomainDeviceType::Desktop,
            DbDeviceType::Mobile => DomainDeviceType::Mobile,
            DbDeviceType::Tablet => DomainDeviceType::Tablet,
            DbDeviceType::Web => DomainDeviceType::Web,
        };

        Ok(UserSession {
            id: model.id,
            user_id: model.user_id,
            session_token: model.session_token,
            status,
            device_type,
            device_info: model.device_info,
            ip_address: model.ip_address,
            user_agent: model.user_agent,
            current_chat_room_id: model.current_chat_room_id,
            last_activity_at: model.last_activity_at,
            last_heartbeat_at: model.last_heartbeat_at,
            metadata: model.metadata,
            expires_at: model.expires_at,
            created_at: model.created_at,
            updated_at: model.updated_at,
        })
    }
}

/// UserSession实体到活动模型转换器
///
/// 【功能】：实现从领域实体到数据库活动模型的转换
pub struct UserSessionEntityToActiveModelConverter;

impl EntityToActiveModel<UserSession, UserSessionActiveModel>
    for UserSessionEntityToActiveModelConverter
{
    /// 将领域实体转换为数据库活动模型
    ///
    /// # 参数
    /// * `entity` - 领域实体
    ///
    /// # 返回值
    /// * `Result<UserSessionActiveModel>` - 转换后的活动模型或错误
    fn entity_to_active_model(entity: UserSession) -> Result<UserSessionActiveModel> {
        // 转换枚举类型
        let status = match entity.status {
            DomainSessionStatus::Online => DbSessionStatus::Online,
            DomainSessionStatus::Offline => DbSessionStatus::Offline,
            DomainSessionStatus::Away => DbSessionStatus::Away,
            DomainSessionStatus::Busy => DbSessionStatus::Busy,
            DomainSessionStatus::Invisible => DbSessionStatus::Invisible,
        };

        let device_type = match entity.device_type {
            DomainDeviceType::Desktop => DbDeviceType::Desktop,
            DomainDeviceType::Mobile => DbDeviceType::Mobile,
            DomainDeviceType::Tablet => DbDeviceType::Tablet,
            DomainDeviceType::Web => DbDeviceType::Web,
        };

        Ok(UserSessionActiveModel {
            id: Set(entity.id),
            user_id: Set(entity.user_id),
            session_token: Set(entity.session_token),
            status: Set(status),
            device_type: Set(device_type),
            device_info: Set(entity.device_info),
            ip_address: Set(entity.ip_address),
            user_agent: Set(entity.user_agent),
            current_chat_room_id: Set(entity.current_chat_room_id),
            last_activity_at: Set(entity.last_activity_at),
            last_heartbeat_at: Set(entity.last_heartbeat_at),
            metadata: Set(entity.metadata),
            expires_at: Set(entity.expires_at),
            created_at: Set(entity.created_at),
            updated_at: Set(entity.updated_at),
        })
    }

    /// 将领域实体转换为部分更新的活动模型
    ///
    /// # 参数
    /// * `entity` - 领域实体
    /// * `exclude_fields` - 要排除的字段列表
    ///
    /// # 返回值
    /// * `Result<UserSessionActiveModel>` - 转换后的活动模型或错误
    fn entity_to_partial_active_model(
        entity: UserSession,
        exclude_fields: &[&str],
    ) -> Result<UserSessionActiveModel> {
        let mut active_model = Self::entity_to_active_model(entity)?;

        // 根据排除字段列表设置为NotSet
        for field in exclude_fields {
            match *field {
                "id" => active_model.id = ActiveValue::NotSet,
                "user_id" => active_model.user_id = ActiveValue::NotSet,
                "session_token" => active_model.session_token = ActiveValue::NotSet,
                "status" => active_model.status = ActiveValue::NotSet,
                "device_type" => active_model.device_type = ActiveValue::NotSet,
                "device_info" => active_model.device_info = ActiveValue::NotSet,
                "ip_address" => active_model.ip_address = ActiveValue::NotSet,
                "user_agent" => active_model.user_agent = ActiveValue::NotSet,
                "current_chat_room_id" => active_model.current_chat_room_id = ActiveValue::NotSet,
                "last_activity_at" => active_model.last_activity_at = ActiveValue::NotSet,
                "last_heartbeat_at" => active_model.last_heartbeat_at = ActiveValue::NotSet,
                "metadata" => active_model.metadata = ActiveValue::NotSet,
                "expires_at" => active_model.expires_at = ActiveValue::NotSet,
                "created_at" => active_model.created_at = ActiveValue::NotSet,
                "updated_at" => active_model.updated_at = ActiveValue::NotSet,
                _ => {
                    tracing::warn!("未知的排除字段: {}", field);
                }
            }
        }

        Ok(active_model)
    }
}

/// 用户会话转换器统一接口
///
/// 【功能】：提供统一的转换接口，简化调用
pub struct UserSessionConverter;

impl UserSessionConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// 【功能】：简化调用接口
    pub fn model_to_entity(model: UserSessionModel) -> Result<UserSession> {
        UserSessionModelToEntityConverter::model_to_entity(model)
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// 【功能】：简化调用接口
    pub fn entity_to_active_model(entity: UserSession) -> Result<UserSessionActiveModel> {
        UserSessionEntityToActiveModelConverter::entity_to_active_model(entity)
    }

    /// 将领域实体转换为部分更新的活动模型
    ///
    /// 【功能】：简化调用接口，用于更新操作
    pub fn entity_to_partial_active_model(
        entity: UserSession,
        exclude_fields: &[&str],
    ) -> Result<UserSessionActiveModel> {
        UserSessionEntityToActiveModelConverter::entity_to_partial_active_model(
            entity,
            exclude_fields,
        )
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use chrono::Utc;
    use uuid::Uuid;

    #[test]
    fn test_model_to_entity_conversion() {
        let model = UserSessionModel {
            id: Uuid::new_v4(),
            user_id: Uuid::new_v4(),
            session_token: "test_token".to_string(),
            status: DbSessionStatus::Online,
            device_type: DbDeviceType::Web,
            device_info: Some("Test Device".to_string()),
            ip_address: "127.0.0.1".to_string(),
            user_agent: Some("Test Agent".to_string()),
            current_chat_room_id: Some(Uuid::new_v4()),
            last_activity_at: Utc::now(),
            last_heartbeat_at: Utc::now(),
            metadata: None,
            expires_at: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let result = UserSessionConverter::model_to_entity(model);
        assert!(result.is_ok());

        let entity = result.unwrap();
        assert_eq!(entity.session_token, "test_token");
        assert_eq!(entity.status, DomainSessionStatus::Online);
        assert_eq!(entity.device_type, DomainDeviceType::Web);
    }

    #[test]
    fn test_entity_to_active_model_conversion() {
        let entity = UserSession {
            id: Uuid::new_v4(),
            user_id: Uuid::new_v4(),
            session_token: "test_token".to_string(),
            status: DomainSessionStatus::Online,
            device_type: DomainDeviceType::Web,
            device_info: Some("Test Device".to_string()),
            ip_address: "127.0.0.1".to_string(),
            user_agent: Some("Test Agent".to_string()),
            current_chat_room_id: Some(Uuid::new_v4()),
            last_activity_at: Utc::now(),
            last_heartbeat_at: Utc::now(),
            metadata: None,
            expires_at: None,
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let result = UserSessionConverter::entity_to_active_model(entity);
        assert!(result.is_ok());
    }
}
