//! # 数据库配置模块
//!
//! 数据库连接池配置和相关设置

use std::time::Duration;

/// 数据库连接池配置结构体
///
/// 【目的】: 专门用于配置SeaORM数据库连接池的参数，支持百万并发连接优化
/// 【设计】: 基于SeaORM ConnectOptions的最佳实践配置
#[derive(Clone, Debug)]
pub struct DatabasePoolConfig {
    /// 最大连接数 - 支持百万并发的关键参数
    pub max_connections: u32,
    /// 最小连接数 - 保持基础连接池大小
    pub min_connections: u32,
    /// 连接超时时间 - 防止连接建立过慢
    pub connect_timeout: Duration,
    /// 空闲超时时间 - 自动清理空闲连接
    pub idle_timeout: Duration,
    /// 连接最大生命周期 - 防止连接过期
    pub max_lifetime: Option<Duration>,
    /// 获取连接超时时间 - 防止应用阻塞
    pub acquire_timeout: Duration,
    /// 启用TCP_NODELAY - 减少网络延迟
    pub tcp_nodelay: bool,
    /// 启用连接保活 - 检测断开的连接
    pub tcp_keepalive: bool,
}

impl DatabasePoolConfig {
    /// 创建生产环境优化的数据库连接池配置
    ///
    /// 【功能】: 为百万并发场景优化的数据库连接池配置
    /// 【参数】: 基于SQLx PostgreSQL最佳实践和企业级应用需求
    pub fn production() -> Self {
        Self {
            max_connections: 1000,                         // 支持百万并发的连接数
            min_connections: 50,                           // 保持基础连接池
            connect_timeout: Duration::from_secs(30),      // 连接建立超时
            idle_timeout: Duration::from_secs(600),        // 10分钟空闲超时
            max_lifetime: Some(Duration::from_secs(3600)), // 1小时最大生命周期
            acquire_timeout: Duration::from_secs(30),      // 获取连接超时
            tcp_nodelay: true,                             // 启用TCP_NODELAY减少延迟
            tcp_keepalive: true,                           // 启用TCP保活检测
        }
    }

    /// 创建开发环境的数据库连接池配置
    ///
    /// 【功能】: 适合开发和测试的轻量级配置
    pub fn development() -> Self {
        Self {
            max_connections: 20,                           // 开发环境较少连接
            min_connections: 2,                            // 最小连接数
            connect_timeout: Duration::from_secs(10),      // 较短连接超时
            idle_timeout: Duration::from_secs(300),        // 5分钟空闲超时
            max_lifetime: Some(Duration::from_secs(1800)), // 30分钟最大生命周期
            acquire_timeout: Duration::from_secs(5),       // 获取连接超时
            tcp_nodelay: true,                             // 仍然启用TCP_NODELAY
            tcp_keepalive: true,                           // 启用TCP保活检测
        }
    }

    /// 验证配置参数的有效性
    ///
    /// 【功能】: 检查配置参数是否合理，防止配置错误
    /// 【返回】: 配置有效返回Ok(())，否则返回错误信息
    pub fn validate(&self) -> Result<(), String> {
        if self.max_connections == 0 {
            return Err("最大连接数不能为0".to_string());
        }

        if self.min_connections > self.max_connections {
            return Err("最小连接数不能大于最大连接数".to_string());
        }

        if self.connect_timeout.as_secs() == 0 {
            return Err("连接超时时间不能为0".to_string());
        }

        if self.acquire_timeout.as_secs() == 0 {
            return Err("获取连接超时时间不能为0".to_string());
        }

        Ok(())
    }
}

/// 数据库配置结构体
#[derive(Clone, Debug)]
pub struct DatabaseConfig {
    /// 数据库连接URL
    pub database_url: String,
    /// 连接池配置
    pub pool_config: DatabasePoolConfig,
}

impl DatabaseConfig {
    /// 创建新的数据库配置
    pub fn new(database_url: String, pool_config: DatabasePoolConfig) -> Self {
        Self {
            database_url,
            pool_config,
        }
    }

    /// 从环境变量创建数据库配置
    ///
    /// 【功能】: 从环境变量读取PostgreSQL连接配置和连接池参数
    /// 【支持】: 自定义连接池参数，支持百万并发优化
    pub fn from_env() -> Result<Self, String> {
        let database_url =
            std::env::var("DATABASE_URL").map_err(|_| "DATABASE_URL环境变量未设置".to_string())?;

        if database_url.is_empty() {
            return Err("数据库URL不能为空".to_string());
        }

        // 检测运行环境
        let is_production = std::env::var("ENVIRONMENT")
            .unwrap_or_else(|_| "development".to_string())
            .to_lowercase()
            == "production";

        // 从环境变量读取连接池配置，支持自定义参数
        let pool_config = if is_production {
            Self::create_pool_config_from_env(true)?
        } else {
            Self::create_pool_config_from_env(false)?
        };

        pool_config.validate()?;

        Ok(Self::new(database_url, pool_config))
    }

    /// 从环境变量创建连接池配置
    ///
    /// 【功能】: 支持从环境变量自定义连接池参数
    /// 【参数】: is_production - 是否为生产环境
    fn create_pool_config_from_env(is_production: bool) -> Result<DatabasePoolConfig, String> {
        // 获取环境变量，如果未设置则使用默认值
        let max_connections = std::env::var("MAX_CONNECTIONS")
            .ok()
            .and_then(|s| s.parse::<u32>().ok())
            .unwrap_or(if is_production { 1000 } else { 20 });

        let min_connections = std::env::var("MIN_CONNECTIONS")
            .ok()
            .and_then(|s| s.parse::<u32>().ok())
            .unwrap_or(if is_production { 50 } else { 5 });

        let connection_timeout = std::env::var("CONNECTION_TIMEOUT")
            .ok()
            .and_then(|s| s.parse::<u64>().ok())
            .map(Duration::from_secs)
            .unwrap_or_else(|| Duration::from_secs(30));

        let idle_timeout = std::env::var("IDLE_TIMEOUT")
            .ok()
            .and_then(|s| s.parse::<u64>().ok())
            .map(Duration::from_secs)
            .unwrap_or_else(|| Duration::from_secs(600));

        let acquire_timeout = std::env::var("ACQUIRE_TIMEOUT")
            .ok()
            .and_then(|s| s.parse::<u64>().ok())
            .map(Duration::from_secs)
            .unwrap_or_else(|| Duration::from_secs(30));

        Ok(DatabasePoolConfig {
            max_connections,
            min_connections,
            connect_timeout: connection_timeout,
            idle_timeout,
            max_lifetime: Some(Duration::from_secs(3600)), // 1小时最大生命周期
            acquire_timeout,
            tcp_nodelay: true,   // 启用TCP_NODELAY减少延迟
            tcp_keepalive: true, // 启用TCP保活检测
        })
    }

    /// 创建测试环境配置
    pub fn for_tests() -> Self {
        let database_url = std::env::var("DATABASE_URL").unwrap_or_else(|_| {
            "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial"
                .to_string()
        });
        Self::new(database_url, DatabasePoolConfig::development())
    }
}
