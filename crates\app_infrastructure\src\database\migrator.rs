//! # 数据库迁移器
//!
//! 整合原项目的迁移脚本到新架构中
//!
//! 这个模块实现了企业级的数据库迁移器，包括：
//! - 任务表迁移
//! - 用户表迁移
//! - 聊天室相关表迁移
//! - 迁移依赖管理
//!
//! 【重要】：使用与原项目一致的迁移版本号以避免冲突

use sea_orm_migration::prelude::*;

/// 主迁移器
///
/// 【功能】：管理所有数据库迁移脚本的执行顺序
/// 【设计】：基于SeaORM Migration的企业级迁移管理
/// 【版本】：与原项目保持一致的迁移版本号
pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            // 基础表迁移 - 使用与原项目一致的版本号
            Box::new(m20250610_035426_create_task_table::Migration),
            Box::new(m20250615_075512_create_users_table::Migration),
            Box::new(m20250615_081240_add_user_id_to_tasks::Migration),
            // 聊天室功能迁移 - 使用与原项目一致的版本号
            Box::new(m20250624_120000_create_chat_rooms_table::Migration),
            Box::new(m20250624_120001_create_messages_table::Migration),
            Box::new(m20250624_120002_create_user_sessions_table::Migration),
            // 性能优化迁移
            Box::new(m20250717_120000_optimize_message_indexes::Migration),
        ]
    }
}

/// 优化消息表索引的迁移
mod m20250717_120000_optimize_message_indexes {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            // 1. 为created_at字段单独创建索引（用于全局时间排序）
            manager
                .create_index(
                    Index::create()
                        .name("idx_messages_created_at_desc")
                        .table(Messages::Table)
                        .col((Messages::CreatedAt, IndexOrder::Desc))
                        .to_owned(),
                )
                .await?;

            // 2. 为用户在特定聊天室的消息查询创建复合索引
            manager
                .create_index(
                    Index::create()
                        .name("idx_messages_sender_chat_room_time")
                        .table(Messages::Table)
                        .col(Messages::SenderId)
                        .col(Messages::ChatRoomId)
                        .col((Messages::CreatedAt, IndexOrder::Desc))
                        .to_owned(),
                )
                .await?;

            // 3. 为消息状态查询创建索引
            manager
                .create_index(
                    Index::create()
                        .name("idx_messages_status_created_at")
                        .table(Messages::Table)
                        .col(Messages::Status)
                        .col((Messages::CreatedAt, IndexOrder::Desc))
                        .to_owned(),
                )
                .await?;

            Ok(())
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            // 删除所有新创建的索引
            manager
                .drop_index(
                    Index::drop()
                        .name("idx_messages_status_created_at")
                        .to_owned(),
                )
                .await?;

            manager
                .drop_index(
                    Index::drop()
                        .name("idx_messages_sender_chat_room_time")
                        .to_owned(),
                )
                .await?;

            manager
                .drop_index(
                    Index::drop()
                        .name("idx_messages_created_at_desc")
                        .to_owned(),
                )
                .await?;

            Ok(())
        }
    }

    /// 消息表的列定义枚举（引用现有定义）
    #[derive(DeriveIden)]
    enum Messages {
        Table,
        Id,
        Content,
        MessageType,
        Status,
        SenderId,
        ChatRoomId,
        ReplyToId,
        Metadata,
        Priority,
        IsPinned,
        ExpiresAt,
        CreatedAt,
        UpdatedAt,
    }
}

/// 创建任务表迁移 - 与原项目版本号一致
mod m20250610_035426_create_task_table {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Tasks::Table)
                        .if_not_exists()
                        .col(ColumnDef::new(Tasks::Id).uuid().not_null().primary_key())
                        .col(ColumnDef::new(Tasks::Title).string().not_null())
                        .col(ColumnDef::new(Tasks::Description).text().null())
                        .col(
                            ColumnDef::new(Tasks::Completed)
                                .boolean()
                                .not_null()
                                .default(false),
                        )
                        .col(
                            ColumnDef::new(Tasks::CreatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Tasks::UpdatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .drop_table(Table::drop().table(Tasks::Table).to_owned())
                .await
        }
    }

    #[derive(DeriveIden)]
    enum Tasks {
        Table,
        Id,
        Title,
        Description,
        Completed,
        CreatedAt,
        UpdatedAt,
    }
}

/// 创建用户表迁移 - 与原项目版本号一致
mod m20250615_075512_create_users_table {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Users::Table)
                        .if_not_exists()
                        .col(ColumnDef::new(Users::Id).uuid().not_null().primary_key())
                        .col(
                            ColumnDef::new(Users::Username)
                                .string()
                                .not_null()
                                .unique_key(),
                        )
                        .col(ColumnDef::new(Users::PasswordHash).string().not_null())
                        .col(
                            ColumnDef::new(Users::CreatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Users::UpdatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .drop_table(Table::drop().table(Users::Table).to_owned())
                .await
        }
    }

    #[derive(DeriveIden)]
    enum Users {
        Table,
        Id,
        Username,
        PasswordHash,
        CreatedAt,
        UpdatedAt,
    }
}

/// 为任务表添加用户ID字段迁移 - 与原项目版本号一致
mod m20250615_081240_add_user_id_to_tasks {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .alter_table(
                    Table::alter()
                        .table(Tasks::Table)
                        .add_column(ColumnDef::new(Tasks::UserId).uuid().null())
                        .to_owned(),
                )
                .await?;

            // 添加外键约束
            manager
                .create_foreign_key(
                    ForeignKey::create()
                        .name("fk_tasks_user_id")
                        .from(Tasks::Table, Tasks::UserId)
                        .to(Users::Table, Users::Id)
                        .on_delete(ForeignKeyAction::SetNull)
                        .on_update(ForeignKeyAction::Cascade)
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            // 删除外键约束
            manager
                .drop_foreign_key(
                    ForeignKey::drop()
                        .name("fk_tasks_user_id")
                        .table(Tasks::Table)
                        .to_owned(),
                )
                .await?;

            // 删除列
            manager
                .alter_table(
                    Table::alter()
                        .table(Tasks::Table)
                        .drop_column(Tasks::UserId)
                        .to_owned(),
                )
                .await
        }
    }

    #[derive(DeriveIden)]
    enum Tasks {
        Table,
        UserId,
    }

    #[derive(DeriveIden)]
    enum Users {
        Table,
        Id,
    }
}

/// 创建聊天室表迁移 - 与原项目版本号一致
mod m20250624_120000_create_chat_rooms_table {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(ChatRooms::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(ChatRooms::Id)
                                .uuid()
                                .not_null()
                                .primary_key(),
                        )
                        .col(
                            ColumnDef::new(ChatRooms::Name)
                                .string_len(100)
                                .not_null()
                                .unique_key(),
                        )
                        .col(ColumnDef::new(ChatRooms::Description).text().null())
                        .col(
                            ColumnDef::new(ChatRooms::RoomType)
                                .string_len(20)
                                .not_null()
                                .default("public"),
                        )
                        .col(
                            ColumnDef::new(ChatRooms::Status)
                                .string_len(20)
                                .not_null()
                                .default("active"),
                        )
                        .col(ColumnDef::new(ChatRooms::CreatedBy).uuid().not_null())
                        .col(
                            ColumnDef::new(ChatRooms::MaxMembers)
                                .integer()
                                .not_null()
                                .default(0),
                        )
                        .col(
                            ColumnDef::new(ChatRooms::CreatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(ChatRooms::UpdatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_chat_rooms_created_by")
                                .from(ChatRooms::Table, ChatRooms::CreatedBy)
                                .to(Users::Table, Users::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .drop_table(Table::drop().table(ChatRooms::Table).to_owned())
                .await
        }
    }

    #[derive(DeriveIden)]
    enum ChatRooms {
        Table,
        Id,
        Name,
        Description,
        RoomType,
        Status,
        CreatedBy,
        MaxMembers,
        CreatedAt,
        UpdatedAt,
    }

    #[derive(DeriveIden)]
    enum Users {
        Table,
        Id,
    }
}

/// 创建消息表迁移 - 与原项目版本号一致
mod m20250624_120001_create_messages_table {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(Messages::Table)
                        .if_not_exists()
                        .col(ColumnDef::new(Messages::Id).uuid().not_null().primary_key())
                        .col(ColumnDef::new(Messages::Content).text().not_null())
                        .col(
                            ColumnDef::new(Messages::MessageType)
                                .string_len(20)
                                .not_null()
                                .default("text"),
                        )
                        .col(
                            ColumnDef::new(Messages::Status)
                                .string_len(20)
                                .not_null()
                                .default("sent"),
                        )
                        .col(ColumnDef::new(Messages::SenderId).uuid().not_null())
                        .col(ColumnDef::new(Messages::ChatRoomId).uuid().not_null())
                        .col(ColumnDef::new(Messages::ReplyToId).uuid().null())
                        .col(ColumnDef::new(Messages::Metadata).text().null())
                        .col(
                            ColumnDef::new(Messages::Priority)
                                .integer()
                                .not_null()
                                .default(5),
                        )
                        .col(
                            ColumnDef::new(Messages::IsPinned)
                                .boolean()
                                .not_null()
                                .default(false),
                        )
                        .col(
                            ColumnDef::new(Messages::ExpiresAt)
                                .timestamp_with_time_zone()
                                .null(),
                        )
                        .col(
                            ColumnDef::new(Messages::CreatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(Messages::UpdatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_messages_sender_id")
                                .from(Messages::Table, Messages::SenderId)
                                .to(Users::Table, Users::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_messages_chat_room_id")
                                .from(Messages::Table, Messages::ChatRoomId)
                                .to(ChatRooms::Table, ChatRooms::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .to_owned(),
                )
                .await?;

            // 创建索引以优化查询性能
            manager
                .create_index(
                    Index::create()
                        .name("idx_messages_chat_room_created")
                        .table(Messages::Table)
                        .col(Messages::ChatRoomId)
                        .col(Messages::CreatedAt)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_messages_sender_created")
                        .table(Messages::Table)
                        .col(Messages::SenderId)
                        .col(Messages::CreatedAt)
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .drop_table(Table::drop().table(Messages::Table).to_owned())
                .await
        }
    }

    #[derive(DeriveIden)]
    enum Messages {
        Table,
        Id,
        Content,
        MessageType,
        Status,
        SenderId,
        ChatRoomId,
        ReplyToId,
        Metadata,
        Priority,
        IsPinned,
        ExpiresAt,
        CreatedAt,
        UpdatedAt,
    }

    #[derive(DeriveIden)]
    enum Users {
        Table,
        Id,
    }

    #[derive(DeriveIden)]
    enum ChatRooms {
        Table,
        Id,
    }
}

/// 创建用户会话表迁移 - 与原项目版本号一致
mod m20250624_120002_create_user_sessions_table {
    use super::*;

    #[derive(DeriveMigrationName)]
    pub struct Migration;

    #[async_trait::async_trait]
    impl MigrationTrait for Migration {
        async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .create_table(
                    Table::create()
                        .table(UserSessions::Table)
                        .if_not_exists()
                        .col(
                            ColumnDef::new(UserSessions::Id)
                                .uuid()
                                .not_null()
                                .primary_key(),
                        )
                        .col(ColumnDef::new(UserSessions::UserId).uuid().not_null())
                        .col(
                            ColumnDef::new(UserSessions::SessionToken)
                                .string()
                                .not_null()
                                .unique_key(),
                        )
                        .col(
                            ColumnDef::new(UserSessions::Status)
                                .string_len(20)
                                .not_null()
                                .default("active"),
                        )
                        .col(
                            ColumnDef::new(UserSessions::DeviceType)
                                .string_len(50)
                                .null(),
                        )
                        .col(ColumnDef::new(UserSessions::DeviceInfo).text().null())
                        .col(
                            ColumnDef::new(UserSessions::IpAddress)
                                .string_len(45)
                                .null(),
                        )
                        .col(ColumnDef::new(UserSessions::UserAgent).text().null())
                        .col(
                            ColumnDef::new(UserSessions::ConnectedAt)
                                .timestamp_with_time_zone()
                                .null(),
                        )
                        .col(
                            ColumnDef::new(UserSessions::LastHeartbeat)
                                .timestamp_with_time_zone()
                                .null(),
                        )
                        .col(
                            ColumnDef::new(UserSessions::DisconnectedAt)
                                .timestamp_with_time_zone()
                                .null(),
                        )
                        .col(ColumnDef::new(UserSessions::Metadata).text().null())
                        .col(
                            ColumnDef::new(UserSessions::CreatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .col(
                            ColumnDef::new(UserSessions::UpdatedAt)
                                .timestamp_with_time_zone()
                                .not_null()
                                .default(Expr::current_timestamp()),
                        )
                        .foreign_key(
                            ForeignKey::create()
                                .name("fk_user_sessions_user_id")
                                .from(UserSessions::Table, UserSessions::UserId)
                                .to(Users::Table, Users::Id)
                                .on_delete(ForeignKeyAction::Cascade)
                                .on_update(ForeignKeyAction::Cascade),
                        )
                        .to_owned(),
                )
                .await?;

            // 创建索引以优化查询性能
            manager
                .create_index(
                    Index::create()
                        .name("idx_user_sessions_user_status")
                        .table(UserSessions::Table)
                        .col(UserSessions::UserId)
                        .col(UserSessions::Status)
                        .to_owned(),
                )
                .await?;

            manager
                .create_index(
                    Index::create()
                        .name("idx_user_sessions_token")
                        .table(UserSessions::Table)
                        .col(UserSessions::SessionToken)
                        .to_owned(),
                )
                .await
        }

        async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
            manager
                .drop_table(Table::drop().table(UserSessions::Table).to_owned())
                .await
        }
    }

    #[derive(DeriveIden)]
    enum UserSessions {
        Table,
        Id,
        UserId,
        SessionToken,
        Status,
        DeviceType,
        DeviceInfo,
        IpAddress,
        UserAgent,
        ConnectedAt,
        LastHeartbeat,
        DisconnectedAt,
        Metadata,
        CreatedAt,
        UpdatedAt,
    }

    #[derive(DeriveIden)]
    enum Users {
        Table,
        Id,
    }
}
