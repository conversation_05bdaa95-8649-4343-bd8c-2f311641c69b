//! # 数据库连接管理模块
//!
//! 提供数据库连接池管理和配置功能

pub mod config;
pub mod migration_manager;
pub mod migrator;
pub mod performance_monitor;
pub mod pool_manager;
pub mod query_optimizer;

// PostgreSQL连接池测试模块（仅在测试时编译）
#[cfg(test)]
pub mod postgres_test;

// 重新导出主要组件
pub use config::{DatabaseConfig, DatabasePoolConfig};
pub use migration_manager::{MigrationManager, MigrationStatus};
pub use migrator::Migrator;
pub use performance_monitor::{
    DatabasePerformanceMonitor, PerformanceAlert, PerformanceMonitorConfig, PerformanceSnapshot,
};
pub use pool_manager::DatabasePoolManager;
pub use query_optimizer::{
    DatabaseStats, IndexRecommendation, OptimizationStrategy, QueryAnalyzer, QueryOptimizer,
    QueryPerformanceMetrics,
};

// 测试相关导出
#[cfg(test)]
pub use postgres_test::{PostgresPoolTester, create_test_database_config};
