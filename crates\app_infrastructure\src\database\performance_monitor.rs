//! # 数据库性能监控模块
//!
//! 实时监控数据库查询性能，提供性能指标收集和分析功能

use sea_orm::{ConnectionTrait, DatabaseConnection, DbErr, Statement};
use serde::{Deserialize, Serialize};
use std::collections::{HashMap, VecDeque};
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use tokio::time::interval;
use tracing::{debug, error, info, instrument, warn};

use super::query_optimizer::{DatabaseStats, QueryPerformanceMetrics};

/// 性能监控配置
#[derive(Debug, Clone)]
pub struct PerformanceMonitorConfig {
    /// 监控间隔（秒）
    pub monitor_interval_secs: u64,
    /// 慢查询阈值（毫秒）
    pub slow_query_threshold_ms: u64,
    /// 性能历史记录保留数量
    pub history_retention_count: usize,
    /// 是否启用自动优化建议
    pub enable_auto_optimization: bool,
}

impl Default for PerformanceMonitorConfig {
    fn default() -> Self {
        Self {
            monitor_interval_secs: 30,
            slow_query_threshold_ms: 1000,
            history_retention_count: 1000,
            enable_auto_optimization: true,
        }
    }
}

/// 性能监控指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceSnapshot {
    /// 快照时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 数据库统计信息
    pub database_stats: DatabaseStats,
    /// 活跃查询数量
    pub active_queries: u32,
    /// 平均查询响应时间（毫秒）
    pub avg_response_time_ms: f64,
    /// 查询吞吐量（每秒查询数）
    pub queries_per_second: f64,
    /// 慢查询数量
    pub slow_queries_count: u32,
    /// 连接池使用率
    pub connection_pool_usage: f64,
}

/// 性能告警类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PerformanceAlert {
    /// 慢查询告警
    SlowQuery {
        query_sql: String,
        execution_time_ms: u64,
        threshold_ms: u64,
    },
    /// 高连接使用率告警
    HighConnectionUsage { current_usage: f64, threshold: f64 },
    /// 低缓存命中率告警
    LowCacheHitRatio { current_ratio: f64, threshold: f64 },
    /// 数据库大小告警
    DatabaseSizeAlert {
        current_size_gb: f64,
        threshold_gb: f64,
    },
}

/// 数据库性能监控器
pub struct DatabasePerformanceMonitor {
    /// 数据库连接
    db: Arc<DatabaseConnection>,
    /// 监控配置
    config: PerformanceMonitorConfig,
    /// 性能历史记录
    performance_history: Arc<Mutex<VecDeque<PerformanceSnapshot>>>,
    /// 查询性能指标
    query_metrics: Arc<Mutex<HashMap<String, QueryPerformanceMetrics>>>,
    /// 性能告警列表
    alerts: Arc<Mutex<Vec<PerformanceAlert>>>,
    /// 监控是否运行
    is_running: Arc<Mutex<bool>>,
}

impl DatabasePerformanceMonitor {
    /// 创建新的性能监控器
    ///
    /// # 参数
    /// - `db`: 数据库连接
    /// - `config`: 监控配置
    pub fn new(db: Arc<DatabaseConnection>, config: PerformanceMonitorConfig) -> Self {
        Self {
            db,
            config,
            performance_history: Arc::new(Mutex::new(VecDeque::new())),
            query_metrics: Arc::new(Mutex::new(HashMap::new())),
            alerts: Arc::new(Mutex::new(Vec::new())),
            is_running: Arc::new(Mutex::new(false)),
        }
    }

    /// 启动性能监控
    #[instrument(skip(self))]
    pub async fn start_monitoring(&self) {
        info!("启动数据库性能监控");

        {
            let mut is_running = self.is_running.lock().unwrap();
            if *is_running {
                warn!("性能监控已在运行中");
                return;
            }
            *is_running = true;
        }

        let db = self.db.clone();
        let config = self.config.clone();
        let performance_history = self.performance_history.clone();
        let alerts = self.alerts.clone();
        let is_running = self.is_running.clone();

        tokio::spawn(async move {
            let mut interval_timer = interval(Duration::from_secs(config.monitor_interval_secs));

            loop {
                interval_timer.tick().await;

                // 检查是否应该停止监控
                {
                    let running = is_running.lock().unwrap();
                    if !*running {
                        break;
                    }
                }

                // 收集性能指标
                match Self::collect_performance_metrics(&db).await {
                    Ok(snapshot) => {
                        debug!("收集到性能快照: {:?}", snapshot);

                        // 检查性能告警
                        let new_alerts = Self::check_performance_alerts(&snapshot, &config);
                        if !new_alerts.is_empty() {
                            let mut alerts_guard = alerts.lock().unwrap();
                            alerts_guard.extend(new_alerts);
                        }

                        // 保存性能历史
                        {
                            let mut history = performance_history.lock().unwrap();
                            history.push_back(snapshot);

                            // 保持历史记录数量限制
                            while history.len() > config.history_retention_count {
                                history.pop_front();
                            }
                        }
                    }
                    Err(e) => {
                        error!("收集性能指标失败: {}", e);
                    }
                }
            }

            info!("数据库性能监控已停止");
        });
    }

    /// 停止性能监控
    pub fn stop_monitoring(&self) {
        info!("停止数据库性能监控");
        let mut is_running = self.is_running.lock().unwrap();
        *is_running = false;
    }

    /// 收集性能指标
    async fn collect_performance_metrics(
        db: &DatabaseConnection,
    ) -> Result<PerformanceSnapshot, DbErr> {
        let start_time = Instant::now();

        // 查询数据库统计信息
        let database_stats = Self::get_database_stats(db).await?;

        // 查询活跃连接数
        let active_queries = Self::get_active_queries_count(db).await?;

        // 计算性能指标
        let collection_time = start_time.elapsed();

        let snapshot = PerformanceSnapshot {
            timestamp: chrono::Utc::now(),
            database_stats,
            active_queries,
            avg_response_time_ms: collection_time.as_millis() as f64,
            queries_per_second: 0.0,    // 需要基于历史数据计算
            slow_queries_count: 0,      // 需要从慢查询日志获取
            connection_pool_usage: 0.0, // 需要从连接池获取
        };

        Ok(snapshot)
    }

    /// 获取数据库统计信息
    async fn get_database_stats(db: &DatabaseConnection) -> Result<DatabaseStats, DbErr> {
        // 查询数据库大小
        let size_query = "SELECT pg_database_size(current_database()) as size";
        let size_result = db
            .query_one(Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                size_query.to_string(),
            ))
            .await?;

        // 查询活跃连接数
        let connections_query =
            "SELECT count(*) as connections FROM pg_stat_activity WHERE state = 'active'";
        let connections_result = db
            .query_one(Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                connections_query.to_string(),
            ))
            .await?;

        // 查询缓存命中率
        let cache_hit_query = r#"
            SELECT 
                CASE 
                    WHEN sum(blks_hit + blks_read) = 0 THEN 100.0
                    ELSE sum(blks_hit) * 100.0 / sum(blks_hit + blks_read)
                END as hit_ratio 
            FROM pg_stat_database 
            WHERE datname = current_database()
        "#;
        let cache_hit_result = db
            .query_one(Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                cache_hit_query.to_string(),
            ))
            .await?;

        Ok(DatabaseStats {
            database_size_bytes: 0, // 需要从查询结果解析
            active_connections: 0,  // 需要从查询结果解析
            cache_hit_ratio: 95.0,  // 需要从查询结果解析
            total_queries: 0,
            slow_queries: 0,
        })
    }

    /// 获取活跃查询数量
    async fn get_active_queries_count(db: &DatabaseConnection) -> Result<u32, DbErr> {
        let query = "SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'";
        let result = db
            .query_one(Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                query.to_string(),
            ))
            .await?;

        // 简化返回，实际需要从结果中解析
        Ok(0)
    }

    /// 检查性能告警
    fn check_performance_alerts(
        snapshot: &PerformanceSnapshot,
        config: &PerformanceMonitorConfig,
    ) -> Vec<PerformanceAlert> {
        let mut alerts = Vec::new();

        // 检查缓存命中率
        if snapshot.database_stats.cache_hit_ratio < 90.0 {
            alerts.push(PerformanceAlert::LowCacheHitRatio {
                current_ratio: snapshot.database_stats.cache_hit_ratio,
                threshold: 90.0,
            });
        }

        // 检查连接池使用率
        if snapshot.connection_pool_usage > 80.0 {
            alerts.push(PerformanceAlert::HighConnectionUsage {
                current_usage: snapshot.connection_pool_usage,
                threshold: 80.0,
            });
        }

        // 检查数据库大小
        let size_gb =
            (snapshot.database_stats.database_size_bytes as f64) / (1024.0 * 1024.0 * 1024.0);
        if size_gb > 10.0 {
            alerts.push(PerformanceAlert::DatabaseSizeAlert {
                current_size_gb: size_gb,
                threshold_gb: 10.0,
            });
        }

        alerts
    }

    /// 获取性能历史记录
    pub fn get_performance_history(&self) -> Vec<PerformanceSnapshot> {
        let history = self.performance_history.lock().unwrap();
        history.iter().cloned().collect()
    }

    /// 获取当前告警列表
    pub fn get_current_alerts(&self) -> Vec<PerformanceAlert> {
        let alerts = self.alerts.lock().unwrap();
        alerts.clone()
    }

    /// 清除告警
    pub fn clear_alerts(&self) {
        let mut alerts = self.alerts.lock().unwrap();
        alerts.clear();
        info!("已清除所有性能告警");
    }

    /// 获取性能摘要
    pub fn get_performance_summary(&self) -> String {
        let history = self.performance_history.lock().unwrap();
        let alerts = self.alerts.lock().unwrap();

        let history_count = history.len();
        let alerts_count = alerts.len();

        let avg_response_time = if history_count > 0 {
            history.iter().map(|s| s.avg_response_time_ms).sum::<f64>() / (history_count as f64)
        } else {
            0.0
        };

        format!(
            "数据库性能监控摘要:\n\
            - 监控历史记录数: {}\n\
            - 当前告警数: {}\n\
            - 平均响应时间: {:.2}ms\n\
            - 监控状态: {}",
            history_count,
            alerts_count,
            avg_response_time,
            if *self.is_running.lock().unwrap() {
                "运行中"
            } else {
                "已停止"
            }
        )
    }
}
