//! # 数据库连接池管理器模块
//!
//! 本模块实现了企业级数据库连接池管理功能，包括：
//! 1. 优化的SeaORM连接池配置
//! 2. TCP_NODELAY和连接保活机制
//! 3. 连接池监控指标
//! 4. 连接生命周期管理

use super::config::DatabaseConfig;
use anyhow::Result;
use sea_orm::{ConnectOptions, Database, DatabaseConnection};
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
use std::time::{Duration, Instant};
use tracing::{error, info};

/// 连接池性能指标
#[derive(Debug)]
pub struct PoolMetrics {
    /// 总连接数
    pub total_connections: AtomicU64,
    /// 活跃连接数
    pub active_connections: AtomicU64,
    /// 空闲连接数
    pub idle_connections: AtomicU64,
    /// 连接获取成功次数
    pub acquire_success_count: AtomicU64,
    /// 连接获取失败次数
    pub acquire_failure_count: AtomicU64,
    /// 平均连接获取时间（毫秒）
    pub avg_acquire_time_ms: AtomicU64,
    /// 连接池是否健康
    pub is_healthy: AtomicBool,
}

impl PoolMetrics {
    /// 创建新的连接池指标实例
    pub fn new() -> Self {
        Self {
            total_connections: AtomicU64::new(0),
            active_connections: AtomicU64::new(0),
            idle_connections: AtomicU64::new(0),
            acquire_success_count: AtomicU64::new(0),
            acquire_failure_count: AtomicU64::new(0),
            avg_acquire_time_ms: AtomicU64::new(0),
            is_healthy: AtomicBool::new(true),
        }
    }

    /// 记录连接获取成功
    pub fn record_acquire_success(&self, duration: Duration) {
        self.acquire_success_count.fetch_add(1, Ordering::Relaxed);

        // 更新平均获取时间
        let duration_ms = duration.as_millis() as u64;
        let current_avg = self.avg_acquire_time_ms.load(Ordering::Relaxed);
        let success_count = self.acquire_success_count.load(Ordering::Relaxed);

        if success_count > 0 {
            let new_avg = (current_avg * (success_count - 1) + duration_ms) / success_count;
            self.avg_acquire_time_ms.store(new_avg, Ordering::Relaxed);
        }
    }

    /// 记录连接获取失败
    pub fn record_acquire_failure(&self) {
        self.acquire_failure_count.fetch_add(1, Ordering::Relaxed);
        self.is_healthy.store(false, Ordering::Relaxed);
    }

    /// 获取成功率
    pub fn get_success_rate(&self) -> f64 {
        let success = self.acquire_success_count.load(Ordering::Relaxed) as f64;
        let failure = self.acquire_failure_count.load(Ordering::Relaxed) as f64;
        let total = success + failure;

        if total > 0.0 { success / total } else { 1.0 }
    }
}

impl Default for PoolMetrics {
    fn default() -> Self {
        Self::new()
    }
}

/// 数据库连接池管理器
///
/// 【功能】: 管理SeaORM数据库连接池，提供企业级连接池功能
/// 【特性】: 支持监控、故障转移、负载均衡和自动恢复
pub struct DatabasePoolManager {
    /// 主数据库连接
    primary_connection: Arc<DatabaseConnection>,
    /// 数据库配置
    config: DatabaseConfig,
    /// 连接池统计指标
    metrics: Arc<PoolMetrics>,
    /// 健康检查间隔
    health_check_interval: Duration,
    /// 是否启用监控
    monitoring_enabled: bool,
}

impl DatabasePoolManager {
    /// 创建新的数据库连接池管理器
    ///
    /// 【功能】: 使用优化的连接池配置创建数据库连接
    /// 【参数】:
    /// * `config` - 数据库配置，包含数据库URL和连接池配置
    ///
    /// 【返回值】: Result<DatabasePoolManager> - 成功返回管理器实例，失败返回错误
    pub async fn new(config: DatabaseConfig) -> Result<Self> {
        info!("DATABASE_POOL: 正在创建PostgreSQL优化的数据库连接池...");
        info!(
            "DATABASE_POOL: 目标配置 - 最大连接数: {}, 最小连接数: {}",
            config.pool_config.max_connections, config.pool_config.min_connections
        );

        // 创建SeaORM连接选项，应用PostgreSQL企业级优化配置
        let mut connect_options = ConnectOptions::new(&config.database_url);

        // 应用连接池优化配置
        connect_options
            .max_connections(config.pool_config.max_connections)
            .min_connections(config.pool_config.min_connections)
            .connect_timeout(config.pool_config.connect_timeout)
            .idle_timeout(config.pool_config.idle_timeout)
            .acquire_timeout(config.pool_config.acquire_timeout);

        // 设置连接最大生命周期（如果配置了）
        if let Some(max_lifetime) = config.pool_config.max_lifetime {
            connect_options.max_lifetime(max_lifetime);
        }

        // PostgreSQL特定优化配置
        if config.database_url.starts_with("postgres") {
            info!("DATABASE_POOL: 应用PostgreSQL特定优化配置");

            // 启用PostgreSQL特定的连接池优化
            connect_options.sqlx_logging_level(log::LevelFilter::Debug);

            // 设置PostgreSQL连接参数
            connect_options.set_schema_search_path("public");
        }

        // 启用SQL日志记录（开发环境）
        let is_development = std::env::var("ENVIRONMENT")
            .unwrap_or_else(|_| "development".to_string())
            != "production";

        if is_development {
            connect_options.sqlx_logging(true);
            info!("DATABASE_POOL: 开发环境 - 启用SQL日志记录");
        }

        // 建立数据库连接
        info!("DATABASE_POOL: 正在建立数据库连接...");
        let primary_connection = Database::connect(connect_options).await?;

        // 创建连接池指标
        let metrics = Arc::new(PoolMetrics::new());

        // 初始化连接数统计
        metrics
            .total_connections
            .store(config.pool_config.max_connections as u64, Ordering::Relaxed);
        metrics
            .idle_connections
            .store(config.pool_config.min_connections as u64, Ordering::Relaxed);

        info!(
            "DATABASE_POOL: 连接池创建成功 - 最大连接数: {}, 最小连接数: {}, TCP_NODELAY: {}, TCP_KEEPALIVE: {}",
            config.pool_config.max_connections,
            config.pool_config.min_connections,
            config.pool_config.tcp_nodelay,
            config.pool_config.tcp_keepalive
        );

        Ok(Self {
            primary_connection: Arc::new(primary_connection),
            config,
            metrics,
            health_check_interval: Duration::from_secs(30), // 30秒健康检查间隔
            monitoring_enabled: true,
        })
    }

    /// 获取数据库连接
    ///
    /// 【功能】: 从连接池获取数据库连接，记录性能指标
    /// 【返回值】: Arc<DatabaseConnection> - 数据库连接的Arc引用
    pub fn get_connection(&self) -> Arc<DatabaseConnection> {
        let start_time = Instant::now();

        // 克隆Arc引用（成本很低）
        let connection = self.primary_connection.clone();

        // 记录获取成功
        let duration = start_time.elapsed();
        self.metrics.record_acquire_success(duration);

        connection
    }

    /// 获取连接池统计指标
    ///
    /// 【功能】: 返回连接池的性能和健康指标
    /// 【返回值】: Arc<PoolMetrics> - 连接池指标的共享引用
    pub fn get_metrics(&self) -> Arc<PoolMetrics> {
        self.metrics.clone()
    }

    /// 检查连接池健康状态
    ///
    /// 【功能】: 执行健康检查，验证连接池是否正常工作
    /// 【返回值】: bool - 健康返回true，否则返回false
    pub async fn health_check(&self) -> bool {
        // 使用简单的查询来检查连接健康状态
        match sea_orm::DatabaseConnection::ping(self.primary_connection.as_ref()).await {
            Ok(_) => {
                self.metrics.is_healthy.store(true, Ordering::Relaxed);
                true
            }
            Err(e) => {
                error!("DATABASE_POOL: 健康检查失败: {}", e);
                self.metrics.is_healthy.store(false, Ordering::Relaxed);
                false
            }
        }
    }

    /// 获取数据库配置
    pub fn get_config(&self) -> &DatabaseConfig {
        &self.config
    }

    /// 是否启用监控
    pub fn is_monitoring_enabled(&self) -> bool {
        self.monitoring_enabled
    }

    /// 设置监控状态
    pub fn set_monitoring_enabled(&mut self, enabled: bool) {
        self.monitoring_enabled = enabled;
    }

    /// 启动连接池监控任务
    ///
    /// 【功能】: 启动后台监控任务，定期检查连接池健康状态
    /// 【返回值】: JoinHandle<()> - 监控任务句柄
    pub fn start_monitoring(&self) -> tokio::task::JoinHandle<()> {
        let metrics = self.metrics.clone();
        let connection = self.primary_connection.clone();
        let interval = self.health_check_interval;

        tokio::spawn(async move {
            let mut interval_timer = tokio::time::interval(interval);
            loop {
                interval_timer.tick().await;

                // 执行健康检查
                match sea_orm::DatabaseConnection::ping(connection.as_ref()).await {
                    Ok(_) => {
                        metrics.is_healthy.store(true, Ordering::Relaxed);
                        tracing::debug!("DATABASE_POOL: 健康检查通过");
                    }
                    Err(e) => {
                        error!("DATABASE_POOL: 健康检查失败: {}", e);
                        metrics.is_healthy.store(false, Ordering::Relaxed);
                        metrics.record_acquire_failure();
                    }
                }

                // 记录连接池统计信息
                let success_rate = metrics.get_success_rate();
                let avg_time = metrics.avg_acquire_time_ms.load(Ordering::Relaxed);

                if success_rate < 0.95 {
                    tracing::warn!(
                        "DATABASE_POOL: 连接成功率较低: {:.2}%, 平均获取时间: {}ms",
                        success_rate * 100.0,
                        avg_time
                    );
                }
            }
        })
    }
}
