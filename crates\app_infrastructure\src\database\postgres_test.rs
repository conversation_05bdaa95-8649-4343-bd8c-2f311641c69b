//! # PostgreSQL连接池测试模块
//!
//! 本模块提供PostgreSQL连接池的功能测试和验证功能，包括：
//! 1. 连接池基本功能测试
//! 2. 高并发连接测试
//! 3. 连接池健康检查测试
//! 4. 性能基准测试

use super::config::DatabaseConfig;
use super::pool_manager::DatabasePoolManager;
use anyhow::Result;
use sea_orm::{ConnectionTrait, Statement};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::sleep;
use tracing::{error, info, warn};

/// PostgreSQL连接池测试器
pub struct PostgresPoolTester {
    /// 数据库连接池管理器
    pool_manager: Arc<DatabasePoolManager>,
    /// 测试配置
    config: DatabaseConfig,
}

impl PostgresPoolTester {
    /// 创建新的PostgreSQL连接池测试器
    ///
    /// 【功能】: 初始化测试器，创建连接池管理器
    /// 【参数】: config - 数据库配置
    pub async fn new(config: DatabaseConfig) -> Result<Self> {
        info!("POSTGRES_TEST: 正在初始化PostgreSQL连接池测试器...");

        let pool_manager = Arc::new(DatabasePoolManager::new(config.clone()).await?);

        Ok(Self {
            pool_manager,
            config,
        })
    }

    /// 执行基本连接测试
    ///
    /// 【功能】: 测试连接池是否能够成功建立连接并执行简单查询
    /// 【返回】: Result<()> - 测试成功返回Ok，失败返回错误
    pub async fn test_basic_connection(&self) -> Result<()> {
        info!("POSTGRES_TEST: 开始基本连接测试...");

        let start_time = Instant::now();

        // 获取数据库连接
        let connection = self.pool_manager.get_connection();

        // 执行简单查询
        let result = connection
            .execute(Statement::from_string(
                sea_orm::DatabaseBackend::Postgres,
                "SELECT 1 as test_value".to_string(),
            ))
            .await?;

        let duration = start_time.elapsed();

        info!(
            "POSTGRES_TEST: 基本连接测试成功 - 查询结果: {:?}, 耗时: {:?}",
            result, duration
        );

        Ok(())
    }

    /// 执行连接池健康检查测试
    ///
    /// 【功能】: 测试连接池的健康检查功能
    /// 【返回】: Result<bool> - 健康检查结果
    pub async fn test_health_check(&self) -> Result<bool> {
        info!("POSTGRES_TEST: 开始连接池健康检查测试...");

        let start_time = Instant::now();
        let is_healthy = self.pool_manager.health_check().await;
        let duration = start_time.elapsed();

        if is_healthy {
            info!("POSTGRES_TEST: 连接池健康检查通过 - 耗时: {:?}", duration);
        } else {
            error!("POSTGRES_TEST: 连接池健康检查失败 - 耗时: {:?}", duration);
        }

        Ok(is_healthy)
    }

    /// 执行并发连接测试
    ///
    /// 【功能】: 测试连接池在高并发情况下的表现
    /// 【参数】: concurrent_count - 并发连接数
    /// 【返回】: Result<()> - 测试结果
    pub async fn test_concurrent_connections(&self, concurrent_count: usize) -> Result<()> {
        info!(
            "POSTGRES_TEST: 开始并发连接测试 - 并发数: {}",
            concurrent_count
        );

        let start_time = Instant::now();
        let mut handles = Vec::new();

        // 创建并发任务
        for i in 0..concurrent_count {
            let pool_manager = self.pool_manager.clone();
            let handle = tokio::spawn(async move {
                let task_start = Instant::now();

                // 获取连接并执行查询
                let connection = pool_manager.get_connection();
                let result = connection
                    .execute(Statement::from_string(
                        sea_orm::DatabaseBackend::Postgres,
                        format!("SELECT {i} as task_id"),
                    ))
                    .await;

                let task_duration = task_start.elapsed();

                match result {
                    Ok(_) => {
                        tracing::debug!(
                            "POSTGRES_TEST: 任务 {} 完成 - 耗时: {:?}",
                            i,
                            task_duration
                        );
                        Ok(task_duration)
                    }
                    Err(e) => {
                        error!("POSTGRES_TEST: 任务 {} 失败: {}", i, e);
                        Err(e)
                    }
                }
            });
            handles.push(handle);
        }

        // 等待所有任务完成
        let mut success_count = 0;
        let mut total_duration = Duration::ZERO;

        for handle in handles {
            match handle.await? {
                Ok(duration) => {
                    success_count += 1;
                    total_duration += duration;
                }
                Err(e) => {
                    error!("POSTGRES_TEST: 并发任务执行失败: {}", e);
                }
            }
        }

        let total_test_duration = start_time.elapsed();
        let avg_task_duration = if success_count > 0 {
            total_duration / success_count as u32
        } else {
            Duration::ZERO
        };

        info!(
            "POSTGRES_TEST: 并发连接测试完成 - 成功: {}/{}, 总耗时: {:?}, 平均任务耗时: {:?}",
            success_count, concurrent_count, total_test_duration, avg_task_duration
        );

        if success_count < concurrent_count {
            warn!(
                "POSTGRES_TEST: 部分并发任务失败 - 失败率: {:.2}%",
                (concurrent_count - success_count) as f64 / concurrent_count as f64 * 100.0
            );
        }

        Ok(())
    }

    /// 执行连接池压力测试
    ///
    /// 【功能】: 测试连接池在持续高负载下的稳定性
    /// 【参数】:
    /// * duration_secs - 测试持续时间（秒）
    /// * requests_per_second - 每秒请求数
    /// 【返回】: Result<()> - 测试结果
    pub async fn test_connection_pool_stress(
        &self,
        duration_secs: u64,
        requests_per_second: usize,
    ) -> Result<()> {
        info!(
            "POSTGRES_TEST: 开始连接池压力测试 - 持续时间: {}秒, 每秒请求数: {}",
            duration_secs, requests_per_second
        );

        let test_start = Instant::now();
        let test_duration = Duration::from_secs(duration_secs);
        let request_interval = Duration::from_millis(1000 / requests_per_second as u64);

        let mut total_requests = 0;
        let mut successful_requests = 0;
        let mut failed_requests = 0;

        while test_start.elapsed() < test_duration {
            let batch_start = Instant::now();
            let mut batch_handles = Vec::new();

            // 创建一批请求
            for _ in 0..requests_per_second {
                let pool_manager = self.pool_manager.clone();
                let handle = tokio::spawn(async move {
                    let connection = pool_manager.get_connection();
                    connection
                        .execute(Statement::from_string(
                            sea_orm::DatabaseBackend::Postgres,
                            "SELECT NOW()".to_string(),
                        ))
                        .await
                });
                batch_handles.push(handle);
                total_requests += 1;
            }

            // 等待这批请求完成
            for handle in batch_handles {
                match handle.await? {
                    Ok(_) => successful_requests += 1,
                    Err(_) => failed_requests += 1,
                }
            }

            // 控制请求频率
            let batch_duration = batch_start.elapsed();
            if batch_duration < Duration::from_secs(1) {
                sleep(Duration::from_secs(1) - batch_duration).await;
            }

            // 每10秒输出一次进度
            if total_requests % (requests_per_second * 10) == 0 {
                let elapsed = test_start.elapsed();
                let success_rate = successful_requests as f64 / total_requests as f64 * 100.0;
                info!(
                    "POSTGRES_TEST: 压力测试进度 - 已运行: {:?}, 总请求: {}, 成功率: {:.2}%",
                    elapsed, total_requests, success_rate
                );
            }
        }

        let total_test_duration = test_start.elapsed();
        let success_rate = successful_requests as f64 / total_requests as f64 * 100.0;
        let avg_rps = total_requests as f64 / total_test_duration.as_secs_f64();

        info!(
            "POSTGRES_TEST: 连接池压力测试完成 - 总耗时: {:?}, 总请求: {}, 成功: {}, 失败: {}, 成功率: {:.2}%, 平均RPS: {:.2}",
            total_test_duration,
            total_requests,
            successful_requests,
            failed_requests,
            success_rate,
            avg_rps
        );

        // 获取连接池指标
        let metrics = self.pool_manager.get_metrics();
        let pool_success_rate = metrics.get_success_rate();

        info!(
            "POSTGRES_TEST: 连接池指标 - 连接成功率: {:.2}%, 平均获取时间: {}ms",
            pool_success_rate * 100.0,
            metrics
                .avg_acquire_time_ms
                .load(std::sync::atomic::Ordering::Relaxed)
        );

        Ok(())
    }

    /// 执行完整的连接池测试套件
    ///
    /// 【功能】: 运行所有连接池测试
    /// 【返回】: Result<()> - 测试结果
    pub async fn run_full_test_suite(&self) -> Result<()> {
        info!("POSTGRES_TEST: 开始执行完整的PostgreSQL连接池测试套件...");

        // 1. 基本连接测试
        self.test_basic_connection().await?;

        // 2. 健康检查测试
        self.test_health_check().await?;

        // 3. 并发连接测试（50个并发）
        self.test_concurrent_connections(50).await?;

        // 4. 高并发测试（200个并发）
        self.test_concurrent_connections(200).await?;

        // 5. 压力测试（30秒，每秒100个请求）
        self.test_connection_pool_stress(30, 100).await?;

        info!("POSTGRES_TEST: PostgreSQL连接池测试套件执行完成！");

        Ok(())
    }

    /// 获取连接池配置信息
    pub fn get_pool_config(&self) -> &DatabaseConfig {
        &self.config
    }

    /// 获取连接池管理器
    pub fn get_pool_manager(&self) -> Arc<DatabasePoolManager> {
        self.pool_manager.clone()
    }
}

/// 创建测试用的数据库配置
///
/// 【功能】: 为测试创建优化的数据库配置
/// 【返回】: Result<DatabaseConfig> - 数据库配置
pub fn create_test_database_config() -> Result<DatabaseConfig> {
    info!("POSTGRES_TEST: 创建测试数据库配置...");

    // 从环境变量创建配置，如果失败则使用默认测试配置
    match DatabaseConfig::from_env() {
        Ok(config) => {
            info!("POSTGRES_TEST: 使用环境变量配置");
            Ok(config)
        }
        Err(e) => {
            warn!(
                "POSTGRES_TEST: 无法从环境变量创建配置: {}, 使用测试默认配置",
                e
            );
            Ok(DatabaseConfig::for_tests())
        }
    }
}
