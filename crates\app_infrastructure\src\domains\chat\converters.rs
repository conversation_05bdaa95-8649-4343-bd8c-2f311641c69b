//! # 聊天领域数据转换器
//!
//! 聊天领域的数据转换器实现，遵循模块化DDD架构原则。
//! 负责聊天领域相关的数据转换操作。
//!
//! ## 架构设计
//!
//! ### 1. 转换器职责
//! - 领域实体与数据库模型之间的转换
//! - DTO与领域实体之间的映射
//! - 数据验证和清洗逻辑
//!
//! ### 2. 转换策略
//! - 类型安全的转换操作
//! - 错误处理和验证
//! - 性能优化的转换逻辑
//!
//! ### 3. 扩展性设计
//! - 支持新的转换需求
//! - 可配置的转换规则
//! - 版本兼容性处理

use crate::entities::{
    ChatRoomActiveModel, ChatRoomModel, MessageActiveModel, MessageModel, chat_room_entity,
    message_entity,
};
use app_common::error::AppResult;
use app_domain::entities::{
    ChatRoom, ChatRoomStatus, ChatRoomType, Message, MessageStatus, MessageType,
};
use sea_orm::Set;
use tracing::debug;

/// 聊天室数据转换器
///
/// 负责聊天室实体与数据库模型之间的转换操作
pub struct ChatRoomConverter;

impl ChatRoomConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// - `model`: 数据库聊天室模型
    ///
    /// # 返回
    /// - `AppResult<ChatRoom>`: 成功返回聊天室领域实体，失败返回错误
    pub fn model_to_entity(model: ChatRoomModel) -> AppResult<ChatRoom> {
        debug!("转换聊天室模型到领域实体: room_id={}", model.id);

        let room_type = match model.room_type {
            chat_room_entity::ChatRoomType::Public => ChatRoomType::Public,
            chat_room_entity::ChatRoomType::Private => ChatRoomType::Private,
            chat_room_entity::ChatRoomType::Group => ChatRoomType::Group,
        };

        let status = match model.status {
            chat_room_entity::ChatRoomStatus::Active => ChatRoomStatus::Active,
            chat_room_entity::ChatRoomStatus::Archived => ChatRoomStatus::Archived,
            chat_room_entity::ChatRoomStatus::Disabled => ChatRoomStatus::Disabled,
        };

        Ok(ChatRoom {
            id: model.id,
            name: model.name,
            description: model.description,
            room_type,
            status,
            created_by: model.created_by,
            max_members: model.max_members,
            current_members: model.current_members,
            settings: model.settings,
            created_at: model.created_at,
            updated_at: model.updated_at,
        })
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// # 参数
    /// - `chat_room`: 聊天室领域实体
    ///
    /// # 返回
    /// - `AppResult<ChatRoomActiveModel>`: 成功返回数据库活动模型，失败返回错误
    pub fn entity_to_active_model(chat_room: ChatRoom) -> AppResult<ChatRoomActiveModel> {
        debug!("转换聊天室领域实体到活动模型: room_id={}", chat_room.id);

        let db_room_type = match chat_room.room_type {
            ChatRoomType::Public => chat_room_entity::ChatRoomType::Public,
            ChatRoomType::Private => chat_room_entity::ChatRoomType::Private,
            ChatRoomType::Group => chat_room_entity::ChatRoomType::Group,
        };

        let db_status = match chat_room.status {
            ChatRoomStatus::Active => chat_room_entity::ChatRoomStatus::Active,
            ChatRoomStatus::Archived => chat_room_entity::ChatRoomStatus::Archived,
            ChatRoomStatus::Disabled => chat_room_entity::ChatRoomStatus::Disabled,
        };

        Ok(ChatRoomActiveModel {
            id: Set(chat_room.id),
            name: Set(chat_room.name),
            description: Set(chat_room.description),
            room_type: Set(db_room_type),
            status: Set(db_status),
            created_by: Set(chat_room.created_by),
            max_members: Set(chat_room.max_members),
            current_members: Set(chat_room.current_members),
            settings: Set(chat_room.settings),
            created_at: Set(chat_room.created_at),
            updated_at: Set(chat_room.updated_at),
        })
    }
}

/// 消息数据转换器
///
/// 负责消息实体与数据库模型之间的转换操作
pub struct MessageConverter;

impl MessageConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// - `model`: 数据库消息模型
    ///
    /// # 返回
    /// - `AppResult<Message>`: 成功返回消息领域实体，失败返回错误
    pub fn model_to_entity(model: MessageModel) -> AppResult<Message> {
        debug!("转换消息模型到领域实体: message_id={}", model.id);

        let message_type = match model.message_type {
            message_entity::MessageType::Text => MessageType::Text,
            message_entity::MessageType::Image => MessageType::Image,
            message_entity::MessageType::File => MessageType::File,
            message_entity::MessageType::System => MessageType::System,
            message_entity::MessageType::Voice => MessageType::Voice,
            message_entity::MessageType::Video => MessageType::Video,
        };

        let status = match model.status {
            message_entity::MessageStatus::Sent => MessageStatus::Sent,
            message_entity::MessageStatus::Delivered => MessageStatus::Delivered,
            message_entity::MessageStatus::Read => MessageStatus::Read,
            message_entity::MessageStatus::Deleted => MessageStatus::Deleted,
            message_entity::MessageStatus::Edited => MessageStatus::Edited,
        };

        Ok(Message {
            id: model.id,
            chat_room_id: model.chat_room_id,
            sender_id: model.sender_id,
            content: model.content,
            message_type,
            status,
            reply_to_id: model.reply_to_id,
            metadata: model.metadata,
            priority: model.priority,
            is_pinned: model.is_pinned,
            expires_at: model.expires_at,
            created_at: model.created_at,
            updated_at: model.updated_at,
        })
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// # 参数
    /// - `message`: 消息领域实体
    ///
    /// # 返回
    /// - `AppResult<MessageActiveModel>`: 成功返回数据库活动模型，失败返回错误
    pub fn entity_to_active_model(message: Message) -> AppResult<MessageActiveModel> {
        debug!("转换消息领域实体到活动模型: message_id={}", message.id);

        let db_message_type = match message.message_type {
            MessageType::Text => message_entity::MessageType::Text,
            MessageType::Image => message_entity::MessageType::Image,
            MessageType::File => message_entity::MessageType::File,
            MessageType::System => message_entity::MessageType::System,
            MessageType::Voice => message_entity::MessageType::Voice,
            MessageType::Video => message_entity::MessageType::Video,
        };

        let db_status = match message.status {
            MessageStatus::Sent => message_entity::MessageStatus::Sent,
            MessageStatus::Delivered => message_entity::MessageStatus::Delivered,
            MessageStatus::Read => message_entity::MessageStatus::Read,
            MessageStatus::Deleted => message_entity::MessageStatus::Deleted,
            MessageStatus::Edited => message_entity::MessageStatus::Edited,
        };

        Ok(MessageActiveModel {
            id: Set(message.id),
            chat_room_id: Set(message.chat_room_id),
            sender_id: Set(message.sender_id),
            content: Set(message.content),
            message_type: Set(db_message_type),
            status: Set(db_status),
            reply_to_id: Set(message.reply_to_id),
            metadata: Set(message.metadata),
            priority: Set(message.priority),
            is_pinned: Set(message.is_pinned),
            expires_at: Set(message.expires_at),
            created_at: Set(message.created_at),
            updated_at: Set(message.updated_at),
        })
    }
}

// 为了向后兼容，重新导出转换器的别名
pub use ChatRoomConverter as ChatRoomEntityToActiveModelConverter;
pub use ChatRoomConverter as ChatRoomModelToEntityConverter;
pub use MessageConverter as MessageEntityToActiveModelConverter;
pub use MessageConverter as MessageModelToEntityConverter;
