//! # 消息仓库实现
//!
//! 消息领域的仓库实现，遵循模块化DDD架构原则。
//! 负责消息聚合根的所有数据访问操作。
//!
//! ## 架构设计
//!
//! ### 1. 聚合根管理
//! - Message作为聚合根，封装消息的核心业务逻辑
//! - 仓库只负责聚合根的持久化操作
//! - 保证聚合内部的一致性约束
//!
//! ### 2. 数据访问模式
//! - 使用SeaORM作为ORM工具进行数据库操作
//! - 实现领域层定义的MessageRepositoryContract接口
//! - 提供异步、类型安全的数据访问接口
//!
//! ### 3. 消息历史管理
//! - 支持消息历史记录查询
//! - 消息状态跟踪和更新
//! - 消息归档和清理
//!
//! ## 主要功能
//! - 消息的CRUD操作
//! - 消息历史查询
//! - 消息状态管理
//! - 批量消息操作

use app_common::error::{AppError, AppResult};
use app_domain::entities::{Message, MessageStatus};
use app_domain::repositories::{MessageFilter, MessageRepositoryContract, PaginationParams};
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, EntityTrait, QueryFilter, QueryOrder,
    QuerySelect, prelude::Uuid,
};
use std::sync::Arc;
use tracing::{debug, error, info, warn};

// 导入数据库实体
use crate::converters::MessageConverter;
use crate::entities::MessageEntity;

/// 消息仓库实现
///
/// 负责消息聚合根的数据持久化操作。
/// 使用Arc<DatabaseConnection>来共享数据库连接，支持高并发访问。
#[derive(Debug, Clone)]
pub struct MessageRepository {
    /// 数据库连接池
    db: Arc<DatabaseConnection>,
}

impl MessageRepository {
    /// 创建新的消息仓库实例
    ///
    /// # 参数
    /// - `db`: 数据库连接，将被包装在Arc中以支持共享
    ///
    /// # 返回
    /// - `Self`: 消息仓库实例
    pub fn new(db: DatabaseConnection) -> Self {
        info!("创建消息仓库实例");
        Self { db: Arc::new(db) }
    }

    /// 从Arc<DatabaseConnection>创建消息仓库实例
    ///
    /// # 参数
    /// - `db`: 已经包装在Arc中的数据库连接
    ///
    /// # 返回
    /// - `Self`: 消息仓库实例
    pub fn from_arc(db: Arc<DatabaseConnection>) -> Self {
        info!("从Arc<DatabaseConnection>创建消息仓库实例");
        Self { db }
    }
}

impl MessageRepository {
    /// 创建消息（内部方法）
    ///
    /// # 参数
    /// - `message`: 要创建的消息领域实体
    ///
    /// # 返回
    /// - `AppResult<Message>`: 成功返回创建的消息实体，失败返回错误
    async fn create_message_internal(&self, message: Message) -> AppResult<Message> {
        info!(
            "创建消息: sender_id={}, room_id={}",
            message.sender_id, message.chat_room_id
        );

        let active_model = MessageConverter::entity_to_active_model(message)?;
        let result = active_model.insert(self.db.as_ref()).await;

        match result {
            Ok(model) => {
                let created_message = MessageConverter::model_to_entity(model)?;
                info!("消息创建成功: message_id={}", created_message.id);
                Ok(created_message)
            }
            Err(err) => {
                error!("消息创建失败: {:?}", err);
                Err(AppError::DatabaseError(format!("创建消息失败: {err}")))
            }
        }
    }

    /// 根据ID查找消息（内部方法）
    ///
    /// # 参数
    /// - `message_id`: 消息ID
    ///
    /// # 返回
    /// - `AppResult<Option<Message>>`: 成功返回消息实体（如果存在），失败返回错误
    async fn find_message_internal(&self, message_id: Uuid) -> AppResult<Option<Message>> {
        debug!("根据ID查找消息: {}", message_id);

        let result = MessageEntity::find_by_id(message_id)
            .one(self.db.as_ref())
            .await;

        match result {
            Ok(Some(model)) => {
                let message = MessageConverter::model_to_entity(model)?;
                debug!("找到消息: {}", message_id);
                Ok(Some(message))
            }
            Ok(None) => {
                debug!("消息不存在: {}", message_id);
                Ok(None)
            }
            Err(err) => {
                error!("查询消息失败: {}, 错误: {:?}", message_id, err);
                Err(AppError::DatabaseError(format!("查询消息失败: {err}")))
            }
        }
    }

    /// 查找聊天室消息历史（内部方法）
    ///
    /// # 参数
    /// - `room_id`: 聊天室ID
    /// - `limit`: 限制返回的消息数量
    /// - `before`: 查询此时间之前的消息（可选）
    ///
    /// # 返回
    /// - `AppResult<Vec<Message>>`: 成功返回消息列表，失败返回错误
    async fn find_room_messages_internal(
        &self,
        room_id: Uuid,
        limit: u64,
        before: Option<DateTime<Utc>>,
    ) -> AppResult<Vec<Message>> {
        debug!("查找聊天室消息历史: room_id={}, limit={}", room_id, limit);

        let mut query = MessageEntity::find()
            .filter(crate::entities::message_entity::Column::ChatRoomId.eq(room_id))
            .order_by_desc(crate::entities::message_entity::Column::CreatedAt)
            .limit(limit);

        // 如果指定了时间点，则查询该时间之前的消息
        if let Some(before_time) = before {
            query =
                query.filter(crate::entities::message_entity::Column::CreatedAt.lt(before_time));
        }

        let result = query.all(self.db.as_ref()).await;

        match result {
            Ok(models) => {
                let messages: Result<Vec<Message>, _> = models
                    .into_iter()
                    .map(MessageConverter::model_to_entity)
                    .collect();

                match messages {
                    Ok(message_list) => {
                        debug!("查询到 {} 条消息", message_list.len());
                        Ok(message_list)
                    }
                    Err(err) => {
                        error!("消息模型转换失败: {:?}", err);
                        Err(AppError::InternalServerError(
                            "消息模型转换失败".to_string(),
                        ))
                    }
                }
            }
            Err(err) => {
                error!("查询聊天室消息失败: room_id={}, 错误: {:?}", room_id, err);
                Err(AppError::DatabaseError(format!(
                    "查询聊天室消息失败: {err}"
                )))
            }
        }
    }

    /// 更新消息
    ///
    /// # 参数
    /// - `message`: 要更新的消息领域实体
    ///
    /// # 返回
    /// - `AppResult<Message>`: 成功返回更新后的消息实体，失败返回错误
    async fn update_message(&self, message: Message) -> AppResult<Message> {
        info!("更新消息: message_id={}", message.id);

        let active_model = MessageConverter::entity_to_active_model(message)?;
        let result = active_model.update(self.db.as_ref()).await;

        match result {
            Ok(model) => {
                let updated_message = MessageConverter::model_to_entity(model)?;
                info!("消息更新成功: message_id={}", updated_message.id);
                Ok(updated_message)
            }
            Err(err) => {
                error!("消息更新失败: {:?}", err);
                Err(AppError::DatabaseError(format!("更新消息失败: {err}")))
            }
        }
    }

    /// 删除消息（内部方法）
    ///
    /// # 参数
    /// - `message_id`: 要删除的消息ID
    ///
    /// # 返回
    /// - `AppResult<()>`: 成功返回空，失败返回错误
    async fn delete_message_internal(&self, message_id: Uuid) -> AppResult<()> {
        info!("删除消息: {}", message_id);

        let result = MessageEntity::delete_by_id(message_id)
            .exec(self.db.as_ref())
            .await;

        match result {
            Ok(delete_result) => {
                if delete_result.rows_affected > 0 {
                    info!("消息删除成功: {}", message_id);
                } else {
                    warn!("消息不存在或已被删除: {}", message_id);
                }
                Ok(())
            }
            Err(err) => {
                error!("消息删除失败: {}, 错误: {:?}", message_id, err);
                Err(AppError::DatabaseError(format!("删除消息失败: {err}")))
            }
        }
    }

    /// 批量删除聊天室消息
    ///
    /// # 参数
    /// - `room_id`: 聊天室ID
    /// - `before`: 删除此时间之前的消息（可选）
    ///
    /// # 返回
    /// - `AppResult<u64>`: 成功返回删除的消息数量，失败返回错误
    async fn delete_room_messages(
        &self,
        room_id: Uuid,
        before: Option<DateTime<Utc>>,
    ) -> AppResult<u64> {
        info!("批量删除聊天室消息: room_id={}", room_id);

        let mut delete_query = MessageEntity::delete_many()
            .filter(crate::entities::message_entity::Column::ChatRoomId.eq(room_id));

        // 如果指定了时间点，则删除该时间之前的消息
        if let Some(before_time) = before {
            delete_query = delete_query
                .filter(crate::entities::message_entity::Column::CreatedAt.lt(before_time));
        }

        let result = delete_query.exec(self.db.as_ref()).await;

        match result {
            Ok(delete_result) => {
                let deleted_count = delete_result.rows_affected;
                info!(
                    "批量删除消息成功: room_id={}, 删除数量={}",
                    room_id, deleted_count
                );
                Ok(deleted_count)
            }
            Err(err) => {
                error!("批量删除消息失败: room_id={}, 错误: {:?}", room_id, err);
                Err(AppError::DatabaseError(format!("批量删除消息失败: {err}")))
            }
        }
    }
}

#[async_trait]
impl MessageRepositoryContract for MessageRepository {
    /// 创建消息
    async fn create(&self, message: Message) -> Result<Message, sea_orm::DbErr> {
        // 重用现有的create_message_internal方法，但需要转换错误类型
        match self.create_message_internal(message).await {
            Ok(msg) => Ok(msg),
            Err(_) => Err(sea_orm::DbErr::Custom("创建消息失败".to_string())),
        }
    }

    /// 根据ID查找消息
    async fn find_by_id(&self, message_id: Uuid) -> Result<Option<Message>, sea_orm::DbErr> {
        // 重用现有的find_message_internal方法，但需要转换错误类型
        match self.find_message_internal(message_id).await {
            Ok(msg) => Ok(msg),
            Err(_) => Err(sea_orm::DbErr::Custom("查找消息失败".to_string())),
        }
    }

    /// 根据聊天室查找消息
    async fn find_by_chat_room(
        &self,
        room_id: Uuid,
        _pagination: PaginationParams,
        _filter: Option<MessageFilter>,
    ) -> Result<Vec<Message>, sea_orm::DbErr> {
        // 重用现有的find_room_messages_internal方法，但需要转换错误类型
        match self.find_room_messages_internal(room_id, 50u64, None).await {
            Ok(messages) => Ok(messages),
            Err(_) => Err(sea_orm::DbErr::Custom("查找聊天室消息失败".to_string())),
        }
    }

    /// 更新消息状态
    async fn update_status(
        &self,
        _id: Uuid,
        _status: MessageStatus,
    ) -> Result<Message, sea_orm::DbErr> {
        // 简化实现
        Err(sea_orm::DbErr::Custom("功能尚未实现".to_string()))
    }

    /// 统计聊天室消息数量
    async fn count_by_chat_room(
        &self,
        _room_id: Uuid,
        _filter: Option<MessageFilter>,
    ) -> Result<u64, sea_orm::DbErr> {
        // 简化实现
        Ok(0)
    }

    /// 删除消息
    async fn delete(&self, message_id: Uuid) -> Result<(), sea_orm::DbErr> {
        // 重用现有的delete_message_internal方法，但需要转换错误类型
        match self.delete_message_internal(message_id).await {
            Ok(_) => Ok(()),
            Err(_) => Err(sea_orm::DbErr::Custom("删除消息失败".to_string())),
        }
    }

    /// 查找最近消息
    async fn find_recent_by_chat_room(
        &self,
        room_id: Uuid,
        limit: u64,
    ) -> Result<Vec<Message>, sea_orm::DbErr> {
        // 重用现有的find_room_messages_internal方法，但需要转换错误类型
        match self.find_room_messages_internal(room_id, limit, None).await {
            Ok(messages) => Ok(messages),
            Err(_) => Err(sea_orm::DbErr::Custom("查找最近消息失败".to_string())),
        }
    }
}
