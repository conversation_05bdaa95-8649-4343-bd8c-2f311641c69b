//! # 聊天领域数据访问模块
//!
//! 聊天领域的数据访问层实现，遵循模块化DDD架构原则。
//!
//! ## 职责范围
//!
//! ### 1. 聊天室聚合根管理
//! - 聊天室基本信息的持久化操作
//! - 聊天室状态管理和生命周期控制
//! - 聊天室成员管理和权限控制
//!
//! ### 2. 消息聚合根管理
//! - 消息的创建、存储和检索
//! - 消息状态管理（已发送、已读等）
//! - 消息历史记录和归档
//!
//! ### 3. 实时通信支持
//! - WebSocket连接管理
//! - 消息广播和分发
//! - 在线状态同步
//!
//! ### 4. 数据转换和映射
//! - 领域实体与数据库模型之间的转换
//! - DTO与领域实体之间的映射
//! - 数据验证和清洗
//!
//! ## 设计原则
//!
//! ### 1. 聚合边界清晰
//! - ChatRoom聚合根管理聊天室的核心信息
//! - Message聚合根管理消息的生命周期
//! - 避免跨聚合的直接引用
//!
//! ### 2. 事务边界明确
//! - 每个仓库方法对应一个事务边界
//! - 保证聊天数据的一致性
//! - 支持批量操作的事务管理
//!
//! ### 3. 性能优化
//! - 合理使用缓存策略
//! - 优化消息查询性能
//! - 支持分页和实时加载

// ============================================================================
// 模块声明
// ============================================================================

/// 聊天室仓库实现
///
/// 负责聊天室聚合根的数据访问操作，包括：
/// - 聊天室的创建、查询、更新、删除
/// - 聊天室成员管理
/// - 聊天室状态维护
mod chat_repository;

/// 消息仓库实现
///
/// 负责消息聚合根的数据访问操作，包括：
/// - 消息的创建、查询、更新、删除
/// - 消息历史记录管理
/// - 消息状态跟踪
mod message_repository;

/// 聊天领域数据转换器
///
/// 负责聊天领域相关的数据转换，包括：
/// - 领域实体与数据库模型的转换
/// - DTO与领域实体的映射
/// - 数据验证和清洗逻辑
mod converters;

// ============================================================================
// 公共接口重新导出
// ============================================================================

pub use chat_repository::ChatRepository;
pub use converters::{
    ChatRoomConverter, ChatRoomEntityToActiveModelConverter, ChatRoomModelToEntityConverter,
    MessageConverter, MessageEntityToActiveModelConverter, MessageModelToEntityConverter,
};
pub use message_repository::MessageRepository;
