//! # 领域模块化数据访问层
//!
//! 按照模块化领域驱动设计(Modular DDD)原则组织的数据访问层实现。
//! 每个领域模块包含该领域的所有数据访问相关实现：
//! - 仓库实现(Repository Implementation)
//! - 数据转换器(Data Converters)
//! - 领域特定的数据访问逻辑
//!
//! ## 架构原则
//!
//! ### 1. 领域边界清晰
//! - 每个领域模块独立管理自己的数据访问逻辑
//! - 避免跨领域的直接数据访问依赖
//! - 通过领域服务协调跨领域操作
//!
//! ### 2. 单一职责原则
//! - 每个仓库只负责一个聚合根的数据访问
//! - 数据转换器专注于特定的转换逻辑
//! - 避免在仓库中混合业务逻辑
//!
//! ### 3. 依赖倒置原则
//! - 仓库实现依赖于领域层定义的接口
//! - 通过依赖注入提供具体实现
//! - 保持领域层的纯净性
//!
//! ## 模块组织结构
//!
//! ```
//! domains/
//! ├── mod.rs              # 本文件，模块声明和重新导出
//! ├── user/               # 用户领域数据访问
//! │   ├── mod.rs          # 用户领域模块声明
//! │   ├── repository.rs   # 用户仓库实现
//! │   ├── session_repository.rs # 用户会话仓库实现
//! │   └── converters.rs   # 用户领域数据转换器
//! ├── task/               # 任务领域数据访问
//! │   ├── mod.rs          # 任务领域模块声明
//! │   ├── repository.rs   # 任务仓库实现
//! │   └── converters.rs   # 任务领域数据转换器
//! └── chat/               # 聊天领域数据访问
//!     ├── mod.rs          # 聊天领域模块声明
//!     ├── chat_repository.rs    # 聊天室仓库实现
//!     ├── message_repository.rs # 消息仓库实现
//!     └── converters.rs   # 聊天领域数据转换器
//! ```

// ============================================================================
// 领域模块声明
// ============================================================================

/// 用户领域数据访问模块
///
/// 包含用户和用户会话相关的数据访问实现：
/// - UserRepository: 用户基本信息的CRUD操作
/// - UserSessionRepository: 用户会话管理
/// - 用户领域的数据转换器
pub mod user;

/// 任务领域数据访问模块
///
/// 包含任务管理相关的数据访问实现：
/// - TaskRepository: 任务的CRUD操作
/// - 任务领域的数据转换器
pub mod task;

/// 聊天领域数据访问模块
///
/// 包含聊天室和消息相关的数据访问实现：
/// - ChatRepository: 聊天室的CRUD操作
/// - MessageRepository: 消息的CRUD操作
/// - 聊天领域的数据转换器
pub mod chat;

// ============================================================================
// 公共重新导出
// ============================================================================

// 用户领域
pub use user::{UserRepository, UserSessionRepository};

// 任务领域
pub use task::TaskRepository;

// 聊天领域
pub use chat::{ChatRepository, MessageRepository};
