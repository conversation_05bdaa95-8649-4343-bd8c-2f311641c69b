//! # 任务领域数据转换器
//!
//! 任务领域的数据转换器实现，遵循模块化DDD架构原则。
//! 负责任务领域相关的数据转换操作。
//!
//! ## 架构设计
//!
//! ### 1. 转换器职责
//! - 领域实体与数据库模型之间的转换
//! - DTO与领域实体之间的映射
//! - 数据验证和清洗逻辑
//!
//! ### 2. 转换策略
//! - 类型安全的转换操作
//! - 错误处理和验证
//! - 性能优化的转换逻辑
//!
//! ### 3. 扩展性设计
//! - 支持新的转换需求
//! - 可配置的转换规则
//! - 版本兼容性处理

use crate::entities::{TaskActiveModel, TaskModel};
use app_common::error::AppResult;
use app_domain::entities::Task;
use sea_orm::ActiveValue;
use tracing::debug;

/// 任务数据转换器
///
/// 负责任务实体与数据库模型之间的转换操作
pub struct TaskConverter;

impl TaskConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// - `model`: 数据库任务模型
    ///
    /// # 返回
    /// - `AppResult<Task>`: 成功返回任务领域实体，失败返回错误
    pub fn model_to_entity(model: TaskModel) -> AppResult<Task> {
        debug!("转换任务模型到领域实体: task_id={}", model.id);

        Ok(Task {
            id: model.id,
            user_id: model.user_id,
            title: model.title,
            description: model.description,
            completed: model.completed,
            created_at: model.created_at,
            updated_at: model.updated_at,
        })
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// # 参数
    /// - `task`: 任务领域实体
    ///
    /// # 返回
    /// - `AppResult<TaskActiveModel>`: 成功返回数据库活动模型，失败返回错误
    pub fn entity_to_active_model(task: Task) -> AppResult<TaskActiveModel> {
        debug!("转换任务领域实体到活动模型: task_id={}", task.id);

        Ok(TaskActiveModel {
            id: ActiveValue::Set(task.id),
            user_id: ActiveValue::Set(task.user_id),
            title: ActiveValue::Set(task.title),
            description: ActiveValue::Set(task.description),
            completed: ActiveValue::Set(task.completed),
            created_at: ActiveValue::Set(task.created_at),
            updated_at: ActiveValue::Set(task.updated_at),
        })
    }
}

// 为了向后兼容，重新导出转换器的别名
pub use TaskConverter as TaskEntityToActiveModelConverter;
pub use TaskConverter as TaskModelToEntityConverter;
