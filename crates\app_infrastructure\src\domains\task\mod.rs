//! # 任务领域数据访问模块
//!
//! 任务领域的数据访问层实现，遵循模块化DDD架构原则。
//!
//! ## 职责范围
//!
//! ### 1. 任务聚合根管理
//! - 任务基本信息的持久化操作
//! - 任务状态管理和生命周期控制
//! - 任务优先级和分类管理
//!
//! ### 2. 任务关系管理
//! - 任务与用户的关联关系
//! - 任务依赖关系管理
//! - 任务层次结构维护
//!
//! ### 3. 数据转换和映射
//! - 领域实体与数据库模型之间的转换
//! - DTO与领域实体之间的映射
//! - 数据验证和清洗
//!
//! ## 设计原则
//!
//! ### 1. 聚合边界清晰
//! - Task聚合根管理任务的核心信息
//! - 避免跨聚合的直接引用
//! - 通过领域服务协调跨聚合操作
//!
//! ### 2. 事务边界明确
//! - 每个仓库方法对应一个事务边界
//! - 保证任务数据的一致性
//! - 支持批量操作的事务管理
//!
//! ### 3. 性能优化
//! - 合理使用缓存策略
//! - 优化数据库查询
//! - 支持分页和排序

// ============================================================================
// 模块声明
// ============================================================================

/// 任务仓库实现
///
/// 负责任务聚合根的数据访问操作，包括：
/// - 任务的创建、查询、更新、删除
/// - 任务状态管理
/// - 任务关系维护
mod repository;

/// 任务领域数据转换器
///
/// 负责任务领域相关的数据转换，包括：
/// - 领域实体与数据库模型的转换
/// - DTO与领域实体的映射
/// - 数据验证和清洗逻辑
mod converters;

// ============================================================================
// 公共接口重新导出
// ============================================================================

pub use converters::{TaskConverter, TaskEntityToActiveModelConverter, TaskModelToEntityConverter};
pub use repository::TaskRepository;
