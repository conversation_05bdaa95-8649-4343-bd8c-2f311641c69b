//! # 任务仓库实现
//!
//! 任务领域的仓库实现，遵循模块化DDD架构原则。
//! 负责任务聚合根的所有数据访问操作。
//!
//! ## 架构设计
//!
//! ### 1. 聚合根管理
//! - Task作为聚合根，封装任务的核心业务逻辑
//! - 仓库只负责聚合根的持久化操作
//! - 保证聚合内部的一致性约束
//!
//! ### 2. 数据访问模式
//! - 使用SeaORM作为ORM工具进行数据库操作
//! - 实现领域层定义的TaskRepositoryContract接口
//! - 提供异步、类型安全的数据访问接口
//!
//! ### 3. 错误处理策略
//! - 将数据库错误透明地传递给上层
//! - 保持错误信息的完整性和可追溯性
//! - 支持事务回滚和错误恢复
//!
//! ## 主要功能
//! - `find_all`: 查询所有任务
//! - `find_by_id`: 根据ID查询任务
//! - `find_by_user_id`: 根据用户ID查询任务
//! - `create`: 创建新任务
//! - `update`: 更新任务
//! - `delete`: 删除任务
//! - 通用CRUD操作：创建、读取、更新、删除
//! - 分页查询功能：支持高性能的大数据集分页操作
//! - 批量操作功能：批量创建、更新、删除

use crate::converters::TaskConverter;
use crate::entities::TaskEntity;
use crate::sea_orm::{
    ActiveModelTrait, ColumnTrait, DatabaseConnection, DbErr, DeleteResult, EntityTrait,
    QueryFilter, prelude::Uuid,
};
use app_domain::entities::Task;
use app_domain::repositories::{BaseRepositoryContract, TaskRepositoryContract};
use async_trait::async_trait;
use std::sync::Arc;
use tracing::{debug, error, info, warn};

/// 任务仓库实现
///
/// 负责任务聚合根的数据持久化操作。
/// 使用Arc<DatabaseConnection>来共享数据库连接，因为在SeaORM 1.1.12中
/// DatabaseConnection不再实现Clone trait。Arc提供了线程安全的引用计数共享。
#[derive(Debug, Clone)]
pub struct TaskRepository {
    /// 数据库连接池
    db: Arc<DatabaseConnection>,
}

impl TaskRepository {
    /// 创建新的任务仓库实例
    ///
    /// # 参数
    /// - `db`: 数据库连接，将被包装在Arc中以支持共享
    ///
    /// # 返回
    /// - `Self`: 任务仓库实例
    pub fn new(db: DatabaseConnection) -> Self {
        info!("创建任务仓库实例");
        Self { db: Arc::new(db) }
    }

    /// 从Arc<DatabaseConnection>创建任务仓库实例
    ///
    /// # 参数
    /// - `db`: 已经包装在Arc中的数据库连接
    ///
    /// # 返回
    /// - `Self`: 任务仓库实例
    pub fn from_arc(db: Arc<DatabaseConnection>) -> Self {
        info!("从Arc<DatabaseConnection>创建任务仓库实例");
        Self { db }
    }
}

#[async_trait]
impl TaskRepositoryContract for TaskRepository {
    /// 查询所有任务
    ///
    /// # 返回
    /// - `Result<Vec<Task>, DbErr>`: 成功时返回任务列表，失败时返回数据库错误
    async fn find_all(&self) -> Result<Vec<Task>, DbErr> {
        info!("查询所有任务");

        let result = TaskEntity::find().all(self.db.as_ref()).await;

        match result {
            Ok(models) => {
                let tasks: Result<Vec<Task>, _> = models
                    .into_iter()
                    .map(TaskConverter::model_to_entity)
                    .collect();

                match tasks {
                    Ok(task_list) => {
                        info!("查询到 {} 个任务", task_list.len());
                        Ok(task_list)
                    }
                    Err(err) => {
                        error!("任务模型转换失败: {:?}", err);
                        Err(DbErr::Custom("任务模型转换失败".to_string()))
                    }
                }
            }
            Err(err) => {
                error!("查询所有任务失败: {:?}", err);
                Err(err)
            }
        }
    }

    /// 根据任务ID查询任务
    ///
    /// # 参数
    /// - `id`: 任务ID
    ///
    /// # 返回
    /// - `Result<Option<Task>, DbErr>`: 成功时返回任务实体（如果存在），失败时返回数据库错误
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Task>, DbErr> {
        debug!("根据ID查询任务: {}", id);

        let result = TaskEntity::find_by_id(id).one(self.db.as_ref()).await;

        match result {
            Ok(Some(model)) => match TaskConverter::model_to_entity(model) {
                Ok(task) => {
                    debug!("找到任务: {}", id);
                    Ok(Some(task))
                }
                Err(err) => {
                    error!("任务模型转换失败: task_id={}, 错误: {:?}", id, err);
                    Err(DbErr::Custom("任务模型转换失败".to_string()))
                }
            },
            Ok(None) => {
                debug!("任务不存在: {}", id);
                Ok(None)
            }
            Err(err) => {
                error!("查询任务失败: {}, 错误: {:?}", id, err);
                Err(err)
            }
        }
    }

    /// 根据用户ID查询任务
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Result<Vec<Task>, DbErr>`: 成功时返回任务列表，失败时返回数据库错误
    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Vec<Task>, DbErr> {
        debug!("根据用户ID查询任务: {}", user_id);

        let result = TaskEntity::find()
            .filter(crate::entities::task_entity::Column::UserId.eq(user_id))
            .all(self.db.as_ref())
            .await;

        match result {
            Ok(models) => {
                let tasks: Result<Vec<Task>, _> = models
                    .into_iter()
                    .map(TaskConverter::model_to_entity)
                    .collect();

                match tasks {
                    Ok(task_list) => {
                        debug!("用户 {} 有 {} 个任务", user_id, task_list.len());
                        Ok(task_list)
                    }
                    Err(err) => {
                        error!("任务模型转换失败: user_id={}, 错误: {:?}", user_id, err);
                        Err(DbErr::Custom("任务模型转换失败".to_string()))
                    }
                }
            }
            Err(err) => {
                error!("查询用户任务失败: user_id={}, 错误: {:?}", user_id, err);
                Err(err)
            }
        }
    }

    /// 创建新任务
    ///
    /// # 参数
    /// - `task`: 要创建的任务领域实体
    ///
    /// # 返回
    /// - `Result<Task, DbErr>`: 成功时返回创建的任务实体，失败时返回数据库错误
    async fn create(&self, task: Task) -> Result<Task, DbErr> {
        info!("创建新任务: {}", task.title);

        let active_model = match TaskConverter::entity_to_active_model(task) {
            Ok(model) => model,
            Err(err) => {
                error!("任务实体转换失败: {:?}", err);
                return Err(DbErr::Custom("任务实体转换失败".to_string()));
            }
        };

        let result = active_model.insert(self.db.as_ref()).await;

        match result {
            Ok(model) => match TaskConverter::model_to_entity(model) {
                Ok(created_task) => {
                    info!("任务创建成功: {}", created_task.title);
                    Ok(created_task)
                }
                Err(err) => {
                    error!("任务模型转换失败: {:?}", err);
                    Err(DbErr::Custom("任务模型转换失败".to_string()))
                }
            },
            Err(err) => {
                error!("任务创建失败: {:?}", err);
                Err(err)
            }
        }
    }

    /// 更新任务
    ///
    /// # 参数
    /// - `task`: 要更新的任务领域实体
    ///
    /// # 返回
    /// - `Result<Task, DbErr>`: 成功时返回更新后的任务实体，失败时返回数据库错误
    async fn update(&self, task: Task) -> Result<Task, DbErr> {
        info!("更新任务: {}", task.title);

        let active_model = match TaskConverter::entity_to_active_model(task) {
            Ok(model) => model,
            Err(err) => {
                error!("任务实体转换失败: {:?}", err);
                return Err(DbErr::Custom("任务实体转换失败".to_string()));
            }
        };

        let result = active_model.update(self.db.as_ref()).await;

        match result {
            Ok(model) => match TaskConverter::model_to_entity(model) {
                Ok(updated_task) => {
                    info!("任务更新成功: {}", updated_task.title);
                    Ok(updated_task)
                }
                Err(err) => {
                    error!("任务模型转换失败: {:?}", err);
                    Err(DbErr::Custom("任务模型转换失败".to_string()))
                }
            },
            Err(err) => {
                error!("任务更新失败: {:?}", err);
                Err(err)
            }
        }
    }

    /// 删除任务
    ///
    /// # 参数
    /// - `id`: 要删除的任务ID
    ///
    /// # 返回
    /// - `Result<DeleteResult, DbErr>`: 成功时返回删除结果，失败时返回数据库错误
    async fn delete(&self, id: Uuid) -> Result<DeleteResult, DbErr> {
        info!("删除任务: {}", id);

        let result = TaskEntity::delete_by_id(id).exec(self.db.as_ref()).await;

        match result {
            Ok(delete_result) => {
                if delete_result.rows_affected > 0 {
                    info!("任务删除成功: {}", id);
                } else {
                    warn!("任务不存在或已被删除: {}", id);
                }
                Ok(delete_result)
            }
            Err(err) => {
                error!("任务删除失败: {}, 错误: {:?}", id, err);
                Err(err)
            }
        }
    }

    /// 根据用户查找所有任务
    ///
    /// # 参数
    /// - `user_id`: 用户ID
    ///
    /// # 返回
    /// - `Result<Vec<Task>, DbErr>`: 成功时返回任务列表，失败时返回数据库错误
    async fn find_all_by_user(&self, user_id: Uuid) -> Result<Vec<Task>, DbErr> {
        // 重用现有的find_by_user_id方法
        self.find_by_user_id(user_id).await
    }
}

// ============================================================================
// 通用仓储接口实现
// ============================================================================

#[async_trait]
impl BaseRepositoryContract<Task> for TaskRepository {
    /// 根据ID查询任务实体
    ///
    /// # 参数
    /// - `id`: 任务的唯一标识符
    ///
    /// # 返回
    /// - `Result<Option<Task>, DbErr>`: 成功时返回任务实体（如果存在），失败时返回数据库错误
    async fn find_by_id(&self, id: Uuid) -> Result<Option<Task>, DbErr> {
        debug!("BaseRepositoryContract::find_by_id 调用，任务ID: {}", id);

        // 复用TaskRepositoryContract的实现
        TaskRepositoryContract::find_by_id(self, id).await
    }

    /// 创建新任务实体
    ///
    /// # 参数
    /// - `entity`: 要创建的任务实体
    ///
    /// # 返回
    /// - `Result<Task, DbErr>`: 成功时返回创建的任务实体，失败时返回数据库错误
    async fn create(&self, entity: Task) -> Result<Task, DbErr> {
        debug!(
            "BaseRepositoryContract::create 调用，任务标题: {}",
            entity.title
        );

        // 复用TaskRepositoryContract的实现
        TaskRepositoryContract::create(self, entity).await
    }

    /// 更新任务实体
    ///
    /// # 参数
    /// - `entity`: 要更新的任务实体
    ///
    /// # 返回
    /// - `Result<Task, DbErr>`: 成功时返回更新后的任务实体，失败时返回数据库错误
    async fn update(&self, entity: Task) -> Result<Task, DbErr> {
        debug!(
            "BaseRepositoryContract::update 调用，任务标题: {}",
            entity.title
        );

        // 复用TaskRepositoryContract的实现
        TaskRepositoryContract::update(self, entity).await
    }

    /// 删除任务实体
    ///
    /// # 参数
    /// - `id`: 要删除的任务ID
    ///
    /// # 返回
    /// - `Result<bool, DbErr>`: 成功时返回是否删除了实体，失败时返回数据库错误
    async fn delete(&self, id: Uuid) -> Result<bool, DbErr> {
        info!("删除任务: {}", id);

        let result = TaskRepositoryContract::delete(self, id).await;

        match result {
            Ok(delete_result) => {
                let deleted = delete_result.rows_affected > 0;
                info!(
                    "任务删除结果: {}, 删除记录数: {}",
                    deleted, delete_result.rows_affected
                );
                Ok(deleted)
            }
            Err(err) => {
                error!("任务删除失败: {}, 错误: {:?}", id, err);
                Err(err)
            }
        }
    }

    /// 检查任务实体是否存在
    ///
    /// # 参数
    /// - `id`: 任务的唯一标识符
    ///
    /// # 返回
    /// - `Result<bool, DbErr>`: 成功时返回任务是否存在，失败时返回数据库错误
    async fn exists(&self, id: Uuid) -> Result<bool, DbErr> {
        debug!("检查任务是否存在: {}", id);

        let result = TaskEntity::find_by_id(id).one(self.db.as_ref()).await;

        match result {
            Ok(Some(_)) => {
                debug!("任务存在: {}", id);
                Ok(true)
            }
            Ok(None) => {
                debug!("任务不存在: {}", id);
                Ok(false)
            }
            Err(err) => {
                error!("检查任务存在性失败: {}, 错误: {:?}", id, err);
                Err(err)
            }
        }
    }
}
