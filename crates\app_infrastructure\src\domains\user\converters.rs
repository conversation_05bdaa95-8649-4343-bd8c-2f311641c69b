//! # 用户领域数据转换器
//!
//! 用户领域的数据转换器实现，遵循模块化DDD架构原则。
//! 负责用户领域相关的数据转换操作。
//!
//! ## 架构设计
//!
//! ### 1. 转换器职责
//! - 领域实体与数据库模型之间的转换
//! - DTO与领域实体之间的映射
//! - 数据验证和清洗逻辑
//!
//! ### 2. 转换策略
//! - 类型安全的转换操作
//! - 错误处理和验证
//! - 性能优化的转换逻辑
//!
//! ### 3. 扩展性设计
//! - 支持新的转换需求
//! - 可配置的转换规则
//! - 版本兼容性处理

use crate::entities::{
    UserActiveModel, UserModel, UserSessionActiveModel, UserSessionModel, user_session_entity,
};
use app_common::error::AppResult;
use app_domain::entities::{DeviceType, SessionStatus, User, UserSession};
use sea_orm::ActiveValue;
use tracing::debug;

/// 用户数据转换器
///
/// 负责用户实体与数据库模型之间的转换操作
pub struct UserConverter;

impl UserConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// - `model`: 数据库用户模型
    ///
    /// # 返回
    /// - `AppResult<User>`: 成功返回用户领域实体，失败返回错误
    pub fn model_to_entity(model: UserModel) -> AppResult<User> {
        debug!("转换用户模型到领域实体: user_id={}", model.id);

        Ok(User {
            id: model.id,
            username: model.username,
            email: model.email,
            password_hash: model.password_hash,
            display_name: model.display_name,
            avatar_url: model.avatar_url,
            bio: model.bio,
            location: model.location,
            website: model.website,
            last_login_at: model.last_login_at,
            status: model.status,
            created_at: model.created_at,
            updated_at: model.updated_at,
        })
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// # 参数
    /// - `user`: 用户领域实体
    ///
    /// # 返回
    /// - `AppResult<UserActiveModel>`: 成功返回数据库活动模型，失败返回错误
    pub fn entity_to_active_model(user: User) -> AppResult<UserActiveModel> {
        debug!("转换用户领域实体到活动模型: user_id={}", user.id);

        Ok(UserActiveModel {
            id: ActiveValue::Set(user.id),
            username: ActiveValue::Set(user.username),
            email: ActiveValue::Set(user.email),
            password_hash: ActiveValue::Set(user.password_hash),
            display_name: ActiveValue::Set(user.display_name),
            avatar_url: ActiveValue::Set(user.avatar_url),
            bio: ActiveValue::Set(user.bio),
            location: ActiveValue::Set(user.location),
            website: ActiveValue::Set(user.website),
            last_login_at: ActiveValue::Set(user.last_login_at),
            status: ActiveValue::Set(user.status),
            created_at: ActiveValue::Set(user.created_at),
            updated_at: ActiveValue::Set(user.updated_at),
        })
    }
}

/// 用户会话数据转换器
///
/// 负责用户会话实体与数据库模型之间的转换操作
pub struct UserSessionConverter;

impl UserSessionConverter {
    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// - `model`: 数据库用户会话模型
    ///
    /// # 返回
    /// - `AppResult<UserSession>`: 成功返回用户会话领域实体，失败返回错误
    pub fn model_to_entity(model: UserSessionModel) -> AppResult<UserSession> {
        debug!("转换用户会话模型到领域实体: session_id={}", model.id);

        Ok(UserSession {
            id: model.id,
            user_id: model.user_id,
            session_token: model.session_token,
            status: Self::convert_session_status_from_db(model.status)?,
            device_type: Self::convert_device_type_from_db(model.device_type)?,
            device_info: model.device_info,
            ip_address: model.ip_address,
            user_agent: model.user_agent,
            current_chat_room_id: model.current_chat_room_id,
            last_activity_at: model.last_activity_at,
            last_heartbeat_at: model.last_heartbeat_at,
            metadata: model.metadata,
            expires_at: model.expires_at,
            created_at: model.created_at,
            updated_at: model.updated_at,
        })
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// # 参数
    /// - `session`: 用户会话领域实体
    ///
    /// # 返回
    /// - `AppResult<UserSessionActiveModel>`: 成功返回数据库活动模型，失败返回错误
    pub fn entity_to_active_model(session: UserSession) -> AppResult<UserSessionActiveModel> {
        debug!("转换用户会话领域实体到活动模型: session_id={}", session.id);

        Ok(UserSessionActiveModel {
            id: ActiveValue::Set(session.id),
            user_id: ActiveValue::Set(session.user_id),
            session_token: ActiveValue::Set(session.session_token),
            status: ActiveValue::Set(Self::convert_session_status_to_db(session.status)?),
            device_type: ActiveValue::Set(Self::convert_device_type_to_db(session.device_type)?),
            device_info: ActiveValue::Set(session.device_info),
            ip_address: ActiveValue::Set(session.ip_address),
            user_agent: ActiveValue::Set(session.user_agent),
            current_chat_room_id: ActiveValue::Set(session.current_chat_room_id),
            last_activity_at: ActiveValue::Set(session.last_activity_at),
            last_heartbeat_at: ActiveValue::Set(session.last_heartbeat_at),
            metadata: ActiveValue::Set(session.metadata),
            expires_at: ActiveValue::Set(session.expires_at),
            created_at: ActiveValue::Set(session.created_at),
            updated_at: ActiveValue::Set(session.updated_at),
        })
    }

    /// 将数据库会话状态转换为领域会话状态
    ///
    /// # 参数
    /// - `status`: 数据库会话状态
    ///
    /// # 返回
    /// - `AppResult<SessionStatus>`: 成功返回领域会话状态，失败返回错误
    fn convert_session_status_from_db(
        status: user_session_entity::SessionStatus,
    ) -> AppResult<SessionStatus> {
        match status {
            user_session_entity::SessionStatus::Online => Ok(SessionStatus::Online),
            user_session_entity::SessionStatus::Offline => Ok(SessionStatus::Offline),
            user_session_entity::SessionStatus::Busy => Ok(SessionStatus::Busy),
            user_session_entity::SessionStatus::Away => Ok(SessionStatus::Away),
            user_session_entity::SessionStatus::Invisible => Ok(SessionStatus::Invisible),
        }
    }

    /// 将领域会话状态转换为数据库会话状态
    ///
    /// # 参数
    /// - `status`: 领域会话状态
    ///
    /// # 返回
    /// - `AppResult<user_session_entity::SessionStatus>`: 成功返回数据库会话状态，失败返回错误
    fn convert_session_status_to_db(
        status: SessionStatus,
    ) -> AppResult<user_session_entity::SessionStatus> {
        match status {
            SessionStatus::Online => Ok(user_session_entity::SessionStatus::Online),
            SessionStatus::Offline => Ok(user_session_entity::SessionStatus::Offline),
            SessionStatus::Busy => Ok(user_session_entity::SessionStatus::Busy),
            SessionStatus::Away => Ok(user_session_entity::SessionStatus::Away),
            SessionStatus::Invisible => Ok(user_session_entity::SessionStatus::Invisible),
        }
    }

    /// 将数据库设备类型转换为领域设备类型
    ///
    /// # 参数
    /// - `device_type`: 数据库设备类型
    ///
    /// # 返回
    /// - `AppResult<DeviceType>`: 成功返回领域设备类型，失败返回错误
    fn convert_device_type_from_db(
        device_type: user_session_entity::DeviceType,
    ) -> AppResult<DeviceType> {
        match device_type {
            user_session_entity::DeviceType::Web => Ok(DeviceType::Web),
            user_session_entity::DeviceType::Mobile => Ok(DeviceType::Mobile),
            user_session_entity::DeviceType::Desktop => Ok(DeviceType::Desktop),
            user_session_entity::DeviceType::Tablet => Ok(DeviceType::Tablet),
        }
    }

    /// 将领域设备类型转换为数据库设备类型
    ///
    /// # 参数
    /// - `device_type`: 领域设备类型
    ///
    /// # 返回
    /// - `AppResult<user_session_entity::DeviceType>`: 成功返回数据库设备类型，失败返回错误
    fn convert_device_type_to_db(
        device_type: DeviceType,
    ) -> AppResult<user_session_entity::DeviceType> {
        match device_type {
            DeviceType::Web => Ok(user_session_entity::DeviceType::Web),
            DeviceType::Mobile => Ok(user_session_entity::DeviceType::Mobile),
            DeviceType::Desktop => Ok(user_session_entity::DeviceType::Desktop),
            DeviceType::Tablet => Ok(user_session_entity::DeviceType::Tablet),
        }
    }
}

// 为了向后兼容，重新导出转换器的别名
pub use UserConverter as UserEntityToActiveModelConverter;
pub use UserConverter as UserModelToEntityConverter;
pub use UserSessionConverter as UserSessionEntityToActiveModelConverter;
pub use UserSessionConverter as UserSessionModelToEntityConverter;
