//! # 用户领域数据访问模块
//!
//! 用户领域的数据访问层实现，遵循模块化DDD架构原则。
//!
//! ## 职责范围
//!
//! ### 1. 用户聚合根管理
//! - 用户基本信息的持久化操作
//! - 用户身份验证相关的数据访问
//! - 用户状态管理
//!
//! ### 2. 用户会话管理
//! - 用户登录会话的创建和管理
//! - 会话状态的持久化
//! - 会话过期和清理机制
//!
//! ### 3. 数据转换和映射
//! - 领域实体与数据库模型之间的转换
//! - DTO与领域实体之间的映射
//! - 数据验证和清洗
//!
//! ## 设计原则
//!
//! ### 1. 聚合边界清晰
//! - User聚合根管理用户的核心信息
//! - UserSession作为独立的聚合根管理会话状态
//! - 避免跨聚合的直接引用
//!
//! ### 2. 事务边界明确
//! - 每个仓库方法对应一个事务边界
//! - 跨聚合操作通过领域服务协调
//! - 保证数据一致性
//!
//! ### 3. 性能优化
//! - 合理使用缓存策略
//! - 优化数据库查询
//! - 支持批量操作

// ============================================================================
// 模块声明
// ============================================================================

/// 用户仓库实现
///
/// 负责用户聚合根的数据访问操作，包括：
/// - 用户的创建、查询、更新、删除
/// - 用户身份验证相关查询
/// - 用户状态管理
mod repository;

/// 用户会话仓库实现
///
/// 负责用户会话的数据访问操作，包括：
/// - 会话的创建和销毁
/// - 会话状态查询和更新
/// - 会话过期管理
mod session_repository;

/// 用户领域数据转换器
///
/// 负责用户领域相关的数据转换，包括：
/// - 领域实体与数据库模型的转换
/// - DTO与领域实体的映射
/// - 数据验证和清洗逻辑
mod converters;

// ============================================================================
// 公共接口重新导出
// ============================================================================

pub use converters::{
    UserConverter, UserEntityToActiveModelConverter, UserModelToEntityConverter,
    UserSessionConverter, UserSessionEntityToActiveModelConverter,
    UserSessionModelToEntityConverter,
};
pub use repository::UserRepository;
pub use session_repository::UserSessionRepository;
