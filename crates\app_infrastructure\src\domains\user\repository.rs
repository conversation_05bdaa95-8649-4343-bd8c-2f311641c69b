//! # 用户仓库实现
//!
//! 用户领域的仓库实现，遵循模块化DDD架构原则。
//! 负责用户聚合根的所有数据访问操作。
//!
//! ## 架构设计
//!
//! ### 1. 聚合根管理
//! - User作为聚合根，封装用户的核心业务逻辑
//! - 仓库只负责聚合根的持久化操作
//! - 保证聚合内部的一致性约束
//!
//! ### 2. 数据访问模式
//! - 使用SeaORM作为ORM工具进行数据库操作
//! - 实现领域层定义的UserRepositoryContract接口
//! - 实现通用仓储接口：BaseRepositoryContract、PaginatedRepositoryContract、BatchRepositoryContract
//! - 提供异步、类型安全的数据访问接口
//!
//! ### 3. 错误处理策略
//! - 将数据库错误透明地传递给上层
//! - 保持错误信息的完整性和可追溯性
//! - 支持事务回滚和错误恢复
//!
//! ## 主要功能
//! - `find_by_username`: 根据用户名查询用户（用于身份验证）
//! - `find_by_id`: 根据用户ID查询用户（用于用户信息获取）
//! - `create`: 创建新用户（用于用户注册）
//! - 通用CRUD操作：创建、读取、更新、删除
//! - 分页查询功能：支持高性能的大数据集分页操作
//! - 批量操作功能：批量创建、更新、删除

use crate::entities::{UserActiveModel, UserEntity, UserModel};
use crate::sea_orm::{
    ActiveModelTrait, ActiveValue, ColumnTrait, DatabaseConnection, DbErr, EntityTrait,
    PaginatorTrait, QueryFilter, QueryOrder, QuerySelect, Set, prelude::Uuid,
};
use app_domain::entities::User;
use app_domain::repositories::{
    BaseRepositoryContract, BatchRepositoryContract, PaginatedRepositoryContract, PaginationUtils,
    QueryFilter as DomainQueryFilter, UserRepositoryContract,
    repository_traits::{PaginatedResult, PaginationParams},
};
use async_trait::async_trait;
use std::sync::Arc;
use tracing::{debug, error, info, warn};

/// 用户仓库实现
///
/// 负责用户聚合根的数据持久化操作。
/// 使用Arc<DatabaseConnection>来共享数据库连接，因为在SeaORM 1.1.12中
/// DatabaseConnection不再实现Clone trait。Arc提供了线程安全的引用计数共享。
#[derive(Debug, Clone)]
pub struct UserRepository {
    /// 数据库连接池
    db: Arc<DatabaseConnection>,
}

impl UserRepository {
    /// 创建新的用户仓库实例
    ///
    /// # 参数
    /// - `db`: 数据库连接，将被包装在Arc中以支持共享
    ///
    /// # 返回
    /// - `Self`: 用户仓库实例
    pub fn new(db: DatabaseConnection) -> Self {
        info!("创建用户仓库实例");
        Self { db: Arc::new(db) }
    }

    /// 从Arc<DatabaseConnection>创建用户仓库实例
    ///
    /// # 参数
    /// - `db`: 已经包装在Arc中的数据库连接
    ///
    /// # 返回
    /// - `Self`: 用户仓库实例
    pub fn from_arc(db: Arc<DatabaseConnection>) -> Self {
        info!("从Arc<DatabaseConnection>创建用户仓库实例");
        Self { db }
    }

    /// 将数据库模型转换为领域实体
    ///
    /// # 参数
    /// - `model`: 数据库用户模型
    ///
    /// # 返回
    /// - `User`: 用户领域实体
    fn model_to_entity(model: UserModel) -> User {
        User {
            id: model.id,
            username: model.username,
            email: model.email,
            password_hash: model.password_hash,
            display_name: model.display_name,
            avatar_url: model.avatar_url,
            bio: model.bio,
            location: model.location,
            website: model.website,
            last_login_at: model.last_login_at,
            status: model.status,
            created_at: model.created_at,
            updated_at: model.updated_at,
        }
    }

    /// 将领域实体转换为数据库活动模型
    ///
    /// # 参数
    /// - `user`: 用户领域实体
    ///
    /// # 返回
    /// - `UserActiveModel`: 数据库活动模型
    fn entity_to_active_model(user: User) -> UserActiveModel {
        UserActiveModel {
            id: ActiveValue::Set(user.id),
            username: ActiveValue::Set(user.username),
            email: ActiveValue::Set(user.email),
            password_hash: ActiveValue::Set(user.password_hash),
            display_name: ActiveValue::Set(user.display_name),
            avatar_url: ActiveValue::Set(user.avatar_url),
            bio: ActiveValue::Set(user.bio),
            location: ActiveValue::Set(user.location),
            website: ActiveValue::Set(user.website),
            last_login_at: ActiveValue::Set(user.last_login_at),
            status: ActiveValue::Set(user.status),
            created_at: ActiveValue::Set(user.created_at),
            updated_at: ActiveValue::Set(user.updated_at),
        }
    }
}

#[async_trait]
impl UserRepositoryContract for UserRepository {
    /// 根据用户名查询用户
    ///
    /// # 参数
    /// - `username`: 要查找的用户名
    ///
    /// # 返回
    /// - `Result<Option<User>, DbErr>`: 成功时返回用户实体（如果存在），失败时返回数据库错误
    async fn find_by_username(&self, username: &str) -> Result<Option<User>, DbErr> {
        info!("根据用户名查询用户: {}", username);

        let result = UserEntity::find()
            .filter(crate::entities::user_entity::Column::Username.eq(username))
            .one(self.db.as_ref())
            .await;

        match result {
            Ok(Some(model)) => {
                info!("找到用户: {}", username);
                Ok(Some(Self::model_to_entity(model)))
            }
            Ok(None) => {
                warn!("用户不存在: {}", username);
                Ok(None)
            }
            Err(err) => {
                error!("查询用户失败: {}, 错误: {:?}", username, err);
                Err(err)
            }
        }
    }

    /// 根据用户ID查询用户
    ///
    /// # 参数
    /// - `id`: 用户ID
    ///
    /// # 返回
    /// - `Result<Option<User>, DbErr>`: 成功时返回用户实体（如果存在），失败时返回数据库错误
    async fn find_by_id(&self, id: Uuid) -> Result<Option<User>, DbErr> {
        info!("根据用户ID查询用户: {}", id);

        let result = UserEntity::find_by_id(id).one(self.db.as_ref()).await;

        match result {
            Ok(Some(model)) => {
                info!("找到用户: {}", id);
                Ok(Some(Self::model_to_entity(model)))
            }
            Ok(None) => {
                warn!("用户不存在: {}", id);
                Ok(None)
            }
            Err(err) => {
                error!("查询用户失败: {}, 错误: {:?}", id, err);
                Err(err)
            }
        }
    }

    /// 创建新用户
    ///
    /// # 参数
    /// - `user`: 要创建的用户领域实体
    ///
    /// # 返回
    /// - `Result<User, DbErr>`: 成功时返回创建的用户实体，失败时返回数据库错误
    async fn create(&self, user: User) -> Result<User, DbErr> {
        info!("创建新用户: {}", user.username);

        let username = user.username.clone(); // 保存用户名用于错误日志
        let active_model = Self::entity_to_active_model(user);
        let result = active_model.insert(self.db.as_ref()).await;

        match result {
            Ok(model) => {
                info!("用户创建成功: {}", model.username);
                Ok(Self::model_to_entity(model))
            }
            Err(err) => {
                // 检查是否是唯一约束违反错误（竞态条件导致的用户名重复）
                match &err {
                    DbErr::Exec(sea_orm::RuntimeErr::SqlxError(sqlx_err)) => {
                        if let Some(db_err) = sqlx_err.as_database_error() {
                            // PostgreSQL 唯一约束违反错误代码是 23505
                            if db_err.code() == Some(std::borrow::Cow::Borrowed("23505")) {
                                warn!("用户名已存在（竞态条件）: {}", username);
                                // 返回自定义错误，表示用户名已存在
                                return Err(DbErr::Custom("用户名已存在".to_string()));
                            }
                        }
                    }
                    DbErr::Query(sea_orm::RuntimeErr::SqlxError(sqlx_err)) => {
                        if let Some(db_err) = sqlx_err.as_database_error() {
                            // PostgreSQL 唯一约束违反错误代码是 23505
                            if db_err.code() == Some(std::borrow::Cow::Borrowed("23505")) {
                                warn!("用户名已存在（竞态条件）: {}", username);
                                // 返回自定义错误，表示用户名已存在
                                return Err(DbErr::Custom("用户名已存在".to_string()));
                            }
                        }
                    }
                    _ => {}
                }

                error!("用户创建失败, 错误: {:?}", err);
                Err(err)
            }
        }
    }

    /// 更新用户信息
    ///
    /// # 参数
    /// - `user`: 要更新的用户领域实体
    ///
    /// # 返回
    /// - `Result<User, DbErr>`: 成功时返回更新后的用户实体，失败时返回数据库错误
    async fn update(&self, user: User) -> Result<User, DbErr> {
        info!("更新用户信息: {}", user.username);

        // 创建ActiveModel用于更新
        let mut active_model = Self::entity_to_active_model(user.clone());
        // 设置ID为NotSet，因为我们要更新现有记录
        active_model.id = ActiveValue::Unchanged(user.id);

        let result = active_model.update(self.db.as_ref()).await;

        match result {
            Ok(model) => {
                info!("用户更新成功: {}", model.username);
                Ok(Self::model_to_entity(model))
            }
            Err(err) => {
                error!("用户更新失败, 错误: {:?}", err);
                Err(err)
            }
        }
    }

    /// 删除用户
    ///
    /// # 参数
    /// - `user_id`: 要删除的用户ID
    ///
    /// # 返回
    /// - `Result<u64, DbErr>`: 成功时返回删除的记录数，失败时返回数据库错误
    async fn delete(&self, user_id: Uuid) -> Result<u64, DbErr> {
        info!("删除用户: {}", user_id);

        let result = UserEntity::delete_by_id(user_id)
            .exec(self.db.as_ref())
            .await;

        match result {
            Ok(delete_result) => {
                let rows_affected = delete_result.rows_affected;
                if rows_affected > 0 {
                    info!("用户删除成功: {}, 影响行数: {}", user_id, rows_affected);
                } else {
                    warn!("用户删除失败，用户不存在: {}", user_id);
                }
                Ok(rows_affected)
            }
            Err(err) => {
                error!("用户删除失败, 错误: {:?}", err);
                Err(err)
            }
        }
    }

    /// 获取用户总数
    ///
    /// # 返回
    /// - `Result<u64, DbErr>`: 成功时返回用户总数，失败时返回数据库错误
    async fn count_all(&self) -> Result<u64, DbErr> {
        info!("获取用户总数");

        let result = UserEntity::find().count(self.db.as_ref()).await;

        match result {
            Ok(count) => {
                info!("用户总数查询成功: {}", count);
                Ok(count)
            }
            Err(err) => {
                error!("用户总数查询失败, 错误: {:?}", err);
                Err(err)
            }
        }
    }
}

// ============================================================================
// 通用仓储接口实现
// ============================================================================

#[async_trait]
impl BaseRepositoryContract<User> for UserRepository {
    /// 根据ID查询用户实体
    ///
    /// # 参数
    /// - `id`: 用户的唯一标识符
    ///
    /// # 返回
    /// - `Result<Option<User>, DbErr>`: 成功时返回用户实体（如果存在），失败时返回数据库错误
    async fn find_by_id(&self, id: Uuid) -> Result<Option<User>, DbErr> {
        debug!("BaseRepositoryContract::find_by_id 调用，用户ID: {}", id);

        // 复用UserRepositoryContract的实现
        UserRepositoryContract::find_by_id(self, id).await
    }

    /// 创建新用户实体
    ///
    /// # 参数
    /// - `entity`: 要创建的用户实体
    ///
    /// # 返回
    /// - `Result<User, DbErr>`: 成功时返回创建的用户实体，失败时返回数据库错误
    async fn create(&self, entity: User) -> Result<User, DbErr> {
        debug!(
            "BaseRepositoryContract::create 调用，用户名: {}",
            entity.username
        );

        // 复用UserRepositoryContract的实现
        UserRepositoryContract::create(self, entity).await
    }

    /// 更新用户实体
    ///
    /// # 参数
    /// - `entity`: 要更新的用户实体
    ///
    /// # 返回
    /// - `Result<User, DbErr>`: 成功时返回更新后的用户实体，失败时返回数据库错误
    async fn update(&self, entity: User) -> Result<User, DbErr> {
        info!("更新用户: {}", entity.username);

        let active_model = UserActiveModel {
            id: Set(entity.id),
            username: Set(entity.username.clone()),
            email: Set(entity.email.clone()),
            password_hash: Set(entity.password_hash.clone()),
            display_name: Set(None),           // 默认为None
            avatar_url: Set(None),             // 默认为None
            bio: Set(None),                    // 默认为None
            location: Set(None),               // 默认为None
            website: Set(None),                // 默认为None
            last_login_at: Set(None),          // 默认为None
            status: Set("active".to_string()), // 默认状态为active
            created_at: Set(entity.created_at),
            updated_at: Set(entity.updated_at),
        };

        let result = active_model.update(self.db.as_ref()).await;

        match result {
            Ok(model) => {
                info!("用户更新成功: {}", model.username);
                Ok(Self::model_to_entity(model))
            }
            Err(err) => {
                error!("用户更新失败: {}, 错误: {:?}", entity.username, err);
                Err(err)
            }
        }
    }

    /// 删除用户实体
    ///
    /// # 参数
    /// - `id`: 要删除的用户ID
    ///
    /// # 返回
    /// - `Result<bool, DbErr>`: 成功时返回是否删除了实体，失败时返回数据库错误
    async fn delete(&self, id: Uuid) -> Result<bool, DbErr> {
        info!("删除用户: {}", id);

        let result = UserEntity::delete_by_id(id).exec(self.db.as_ref()).await;

        match result {
            Ok(delete_result) => {
                let deleted = delete_result.rows_affected > 0;
                info!(
                    "用户删除结果: {}, 删除记录数: {}",
                    deleted, delete_result.rows_affected
                );
                Ok(deleted)
            }
            Err(err) => {
                error!("用户删除失败: {}, 错误: {:?}", id, err);
                Err(err)
            }
        }
    }

    /// 检查用户实体是否存在
    ///
    /// # 参数
    /// - `id`: 用户的唯一标识符
    ///
    /// # 返回
    /// - `Result<bool, DbErr>`: 成功时返回用户是否存在，失败时返回数据库错误
    async fn exists(&self, id: Uuid) -> Result<bool, DbErr> {
        debug!("检查用户是否存在: {}", id);

        let result = UserEntity::find_by_id(id).one(self.db.as_ref()).await;

        match result {
            Ok(Some(_)) => {
                debug!("用户存在: {}", id);
                Ok(true)
            }
            Ok(None) => {
                debug!("用户不存在: {}", id);
                Ok(false)
            }
            Err(err) => {
                error!("检查用户存在性失败: {}, 错误: {:?}", id, err);
                Err(err)
            }
        }
    }
}

#[async_trait]
impl PaginatedRepositoryContract<User> for UserRepository {
    /// 分页查询所有用户实体
    ///
    /// # 参数
    /// - `pagination`: 分页参数
    ///
    /// # 返回
    /// - `Result<PaginatedResult<User>, DbErr>`: 成功时返回分页结果，失败时返回数据库错误
    async fn find_all_paginated(
        &self,
        pagination: PaginationParams,
    ) -> Result<PaginatedResult<User>, DbErr> {
        info!(
            "分页查询所有用户，页码: {}, 每页大小: {}",
            pagination.page, pagination.page_size
        );

        // 构建基础查询
        let query = UserEntity::find();

        // 获取总记录数
        let total_count = query.clone().count(self.db.as_ref()).await?;

        // 执行分页查询
        let models = query
            .offset(Some((pagination.page - 1) * pagination.page_size))
            .limit(Some(pagination.page_size))
            .order_by_asc(crate::entities::user_entity::Column::CreatedAt)
            .all(self.db.as_ref())
            .await?;

        // 转换为领域实体
        let users: Vec<User> = models.into_iter().map(Self::model_to_entity).collect();

        // 创建分页元信息
        let pagination_meta = PaginationUtils::create_pagination_meta(
            pagination.page,
            pagination.page_size,
            total_count,
        );

        // 创建分页结果
        let result = PaginationUtils::create_paginated_result(users, pagination_meta);

        info!("分页查询完成，返回 {} 条记录", result.data.len());
        Ok(result)
    }

    /// 根据条件分页查询用户实体
    ///
    /// # 参数
    /// - `filter`: 查询过滤条件
    /// - `pagination`: 分页参数
    ///
    /// # 返回
    /// - `Result<PaginatedResult<User>, DbErr>`: 成功时返回分页结果，失败时返回数据库错误
    async fn find_by_filter_paginated(
        &self,
        filter: DomainQueryFilter,
        pagination: PaginationParams,
    ) -> Result<PaginatedResult<User>, DbErr> {
        info!(
            "根据条件分页查询用户，页码: {}, 每页大小: {}",
            pagination.page, pagination.page_size
        );

        // 构建基础查询
        let query = UserEntity::find();

        // 应用过滤条件
        // 这里简化处理，实际应该根据filter.conditions来构建复杂查询
        // 暂时跳过过滤条件的应用，后续可以扩展
        if !filter.conditions.is_empty() {
            debug!("应用过滤条件，条件数量: {}", filter.conditions.len());
            // TODO: 根据filter.conditions构建复杂的查询条件
        }

        // 获取总记录数
        let total_count = query.clone().count(self.db.as_ref()).await?;

        // 执行分页查询
        let models = query
            .offset(Some((pagination.page - 1) * pagination.page_size))
            .limit(Some(pagination.page_size))
            .order_by_asc(crate::entities::user_entity::Column::CreatedAt)
            .all(self.db.as_ref())
            .await?;

        // 转换为领域实体
        let users: Vec<User> = models.into_iter().map(Self::model_to_entity).collect();

        // 创建分页元信息
        let pagination_meta = PaginationUtils::create_pagination_meta(
            pagination.page,
            pagination.page_size,
            total_count,
        );

        // 创建分页结果
        let result = PaginationUtils::create_paginated_result(users, pagination_meta);

        info!("条件分页查询完成，返回 {} 条记录", result.data.len());
        Ok(result)
    }
}

#[async_trait]
impl BatchRepositoryContract<User> for UserRepository {
    /// 批量创建用户实体
    ///
    /// # 参数
    /// - `entities`: 要创建的用户实体列表
    ///
    /// # 返回
    /// - `Result<Vec<User>, DbErr>`: 成功时返回创建的用户实体列表，失败时返回数据库错误
    async fn create_batch(&self, entities: Vec<User>) -> Result<Vec<User>, DbErr> {
        info!("批量创建 {} 个用户", entities.len());

        if entities.is_empty() {
            warn!("批量创建用户列表为空");
            return Ok(Vec::new());
        }

        // 转换为ActiveModel列表
        let active_models: Vec<UserActiveModel> = entities
            .into_iter()
            .map(Self::entity_to_active_model)
            .collect();

        // 执行批量插入
        // 注意：SeaORM的批量插入在某些数据库中可能不支持返回插入的记录
        // 这里我们逐个插入以确保能够返回创建的实体
        let mut created_users = Vec::new();

        for active_model in active_models {
            let result = active_model.insert(self.db.as_ref()).await?;
            created_users.push(Self::model_to_entity(result));
        }

        info!("批量创建用户成功，创建了 {} 个用户", created_users.len());
        Ok(created_users)
    }

    /// 批量更新用户实体
    ///
    /// # 参数
    /// - `entities`: 要更新的用户实体列表
    ///
    /// # 返回
    /// - `Result<Vec<User>, DbErr>`: 成功时返回更新后的用户实体列表，失败时返回数据库错误
    async fn update_batch(&self, entities: Vec<User>) -> Result<Vec<User>, DbErr> {
        info!("批量更新 {} 个用户", entities.len());

        if entities.is_empty() {
            warn!("批量更新用户列表为空");
            return Ok(Vec::new());
        }

        let mut updated_users = Vec::new();

        // 由于SeaORM不直接支持批量更新返回数据，我们逐个更新
        // 在事务中执行以保证一致性
        for entity in entities {
            let active_model = UserActiveModel {
                id: Set(entity.id),
                username: Set(entity.username.clone()),
                email: Set(entity.email.clone()),
                password_hash: Set(entity.password_hash.clone()),
                display_name: Set(None),           // 默认为None
                avatar_url: Set(None),             // 默认为None
                bio: Set(None),                    // 默认为None
                location: Set(None),               // 默认为None
                website: Set(None),                // 默认为None
                last_login_at: Set(None),          // 默认为None
                status: Set("active".to_string()), // 默认状态为active
                created_at: Set(entity.created_at),
                updated_at: Set(entity.updated_at),
            };

            let updated_model = active_model.update(self.db.as_ref()).await?;
            updated_users.push(Self::model_to_entity(updated_model));
        }

        info!("批量更新用户成功，更新了 {} 个用户", updated_users.len());
        Ok(updated_users)
    }

    /// 批量删除用户实体
    ///
    /// # 参数
    /// - `ids`: 要删除的用户ID列表
    ///
    /// # 返回
    /// - `Result<u64, DbErr>`: 成功时返回删除的用户数量，失败时返回数据库错误
    async fn delete_batch(&self, ids: Vec<Uuid>) -> Result<u64, DbErr> {
        info!("批量删除 {} 个用户", ids.len());

        if ids.is_empty() {
            warn!("批量删除用户ID列表为空");
            return Ok(0);
        }

        // 执行批量删除
        let result = UserEntity::delete_many()
            .filter(crate::entities::user_entity::Column::Id.is_in(ids))
            .exec(self.db.as_ref())
            .await;

        match result {
            Ok(delete_result) => {
                info!(
                    "批量删除用户成功，删除了 {} 个用户",
                    delete_result.rows_affected
                );
                Ok(delete_result.rows_affected)
            }
            Err(err) => {
                error!("批量删除用户失败, 错误: {:?}", err);
                Err(err)
            }
        }
    }
}

// ============================================================================
// 测试模块
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;
    use app_domain::entities::User;
    use chrono::Utc;
    use uuid::Uuid;

    /// 创建测试用户实体
    fn create_test_user() -> User {
        User {
            id: Uuid::new_v4(),
            username: "test_user".to_string(),
            email: Some("<EMAIL>".to_string()),
            password_hash: "hashed_password".to_string(),
            display_name: Some("Test User".to_string()),
            avatar_url: None,
            bio: None,
            location: None,
            website: None,
            last_login_at: None,
            status: "active".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        }
    }

    #[tokio::test]
    async fn test_user_repository_contract_methods() {
        // 这是一个基本的编译测试，确保所有方法签名正确
        // 实际的数据库测试需要数据库连接

        // 验证UserRepository实现了所有必需的trait
        fn assert_implements_traits<T>()
        where
            T: UserRepositoryContract
                + BaseRepositoryContract<User>
                + PaginatedRepositoryContract<User>
                + BatchRepositoryContract<User>
                + Send
                + Sync,
        {
        }

        assert_implements_traits::<UserRepository>();
    }

    #[test]
    fn test_user_entity_conversion() {
        let user = create_test_user();

        // 测试实体到ActiveModel的转换
        let active_model = UserRepository::entity_to_active_model(user.clone());

        // 验证关键字段
        match active_model.id {
            Set(id) => assert_eq!(id, user.id),
            _ => panic!("ID字段应该被设置"),
        }

        match active_model.username {
            Set(username) => assert_eq!(username, user.username),
            _ => panic!("用户名字段应该被设置"),
        }
    }
}
