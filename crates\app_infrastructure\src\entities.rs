//! # 数据库实体模块
//!
//! 重新导出migration模块中定义的SeaORM实体，
//! 为基础设施层提供数据库访问所需的实体定义。
//!
//! 这种设计避免了领域层直接依赖migration模块，
//! 保持了清洁的架构边界。

// 重新导出migration模块中的所有实体
pub use migration::{
    chat_room_entity, message_entity, task_entity, user_entity, user_session_entity,
};

// 为方便使用，重新导出常用的实体类型
pub use chat_room_entity::{
    ActiveModel as ChatRoomActiveModel, Entity as ChatRoomEntity, Model as ChatRoomModel,
};
pub use message_entity::{
    ActiveModel as MessageActiveModel, Entity as MessageEntity, Model as MessageModel,
};
pub use task_entity::{ActiveModel as TaskActiveModel, Entity as TaskEntity, Model as TaskModel};
pub use user_entity::{ActiveModel as UserActiveModel, Entity as UserEntity, Model as UserModel};
pub use user_session_entity::{
    ActiveModel as UserSessionActiveModel, Entity as UserSessionEntity, Model as UserSessionModel,
};

// 重新导出枚举类型
pub use chat_room_entity::{ChatRoomStatus, ChatRoomType};
pub use message_entity::{MessageStatus, MessageType};
pub use user_session_entity::{DeviceType, SessionStatus};
