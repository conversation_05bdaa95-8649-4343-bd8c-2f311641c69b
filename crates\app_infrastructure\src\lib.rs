//! # App Infrastructure
//!
//! 基础设施层，提供具体实现：
//! - 数据库访问实现
//! - 外部服务集成
//! - 缓存实现
//! - 消息队列等

// ============================================================================
// 模块声明 - 按模块化DDD架构组织
// ============================================================================

// 核心基础设施模块
pub mod cache;
pub mod converters;
pub mod database;
pub mod entities;

// 弹性基础设施模块 - 熔断器、限流器、降级策略
pub mod resilience;

// 领域模块化数据访问层 - 按业务领域组织
pub mod domains;

// WebSocket基础设施
pub mod websocket;

// ============================================================================
// 核心组件重新导出 - 按功能分组
// ============================================================================

// 数据库相关组件
pub use database::{DatabaseConfig, DatabasePoolConfig, DatabasePoolManager};

// 缓存相关组件
pub use cache::{
    CacheClientManager,
    CacheConfig,
    CacheInvalidationConfig,
    CacheInvalidationEvent,
    CacheInvalidationPattern,
    // 缓存失效相关
    CacheInvalidationService,
    CacheInvalidationStrategy,
    CachePoolConfig,
    CachePoolStats,
    CacheUpdateStrategy,
    InvalidationScope,
    InvalidationTrigger,
    // 弹性消息搜索缓存服务
    ResilientMessageSearchCacheService,
    build_cache_key,
    build_message_cache_key,
    build_room_cache_key,
    build_session_cache_key,
    build_user_cache_key,
    cache_keys,
    cache_ttl,
    create_cache_invalidation_service,
    create_default_invalidation_patterns,
};

// 数据库实体（从migration重新导出）
pub use entities::{
    // 实体类型别名
    ChatRoomActiveModel,
    ChatRoomEntity,
    ChatRoomModel,
    // 枚举类型
    ChatRoomStatus,
    ChatRoomType,
    DeviceType,
    MessageActiveModel,
    MessageEntity,
    MessageModel,
    MessageStatus,
    MessageType,
    SessionStatus,
    TaskActiveModel,
    TaskEntity,
    TaskModel,
    UserActiveModel,
    UserEntity,
    UserModel,
    UserSessionActiveModel,
    UserSessionEntity,
    UserSessionModel,
    // 实体模块
    chat_room_entity,
    message_entity,
    task_entity,
    user_entity,
    user_session_entity,
};

// 数据转换器（从领域模块重新导出）
pub use domains::{
    // 聊天领域转换器
    chat::{
        ChatRoomConverter, ChatRoomEntityToActiveModelConverter, ChatRoomModelToEntityConverter,
        MessageConverter, MessageEntityToActiveModelConverter, MessageModelToEntityConverter,
    },
    // 任务领域转换器
    task::{TaskConverter, TaskEntityToActiveModelConverter, TaskModelToEntityConverter},
    // 用户领域转换器
    user::{
        UserConverter, UserEntityToActiveModelConverter, UserModelToEntityConverter,
        UserSessionConverter, UserSessionEntityToActiveModelConverter,
        UserSessionModelToEntityConverter,
    },
};

// 领域模块化仓库实现
pub use domains::{
    // 聊天领域
    chat::{ChatRepository, MessageRepository},
    // 任务领域
    task::TaskRepository,
    // 用户领域
    user::{UserRepository, UserSessionRepository},
};

// WebSocket基础设施组件
pub use websocket::{
    WebSocketConnectionManager, WebSocketMessageDistributor, WebSocketStatsCollector,
};

// 弹性基础设施组件
pub use resilience::{
    ResilienceConfig,
    // 弹性管理器
    ResilienceManager,
    ResilienceStats,
    // 熔断器相关
    circuit_breaker::{
        CircuitBreaker, CircuitBreakerConfig, CircuitBreakerError, CircuitBreakerManager,
        CircuitBreakerState,
    },
    // 降级策略相关
    fallback::{
        FallbackConfig, FallbackManager, FallbackMessage, FallbackStrategy, SearchFallbackResult,
    },
    // 限流器相关
    rate_limiter::{
        RateLimiterConfig, RateLimiterError, RateLimiterManager, RateLimiterType,
        TokenBucketRateLimiter,
    },
};

// ============================================================================
// 外部依赖重新导出 - 仅导出基础设施层需要的依赖
// ============================================================================

// 内部依赖
pub use app_common;
pub use app_domain;

// 异步trait支持
pub use async_trait;

// 数据库ORM
pub use sea_orm;

// 日志支持
pub use tracing;
