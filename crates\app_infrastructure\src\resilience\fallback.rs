//! # 降级策略实现
//!
//! 为缓存失效、数据库故障等场景提供降级响应策略
//! 确保系统在部分组件故障时仍能提供基本服务

use anyhow::Result as AnyhowResult;
use app_domain::entities::chat::SearchGlobalChatRoomMessagesRequest;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{debug, info, warn};
use uuid::Uuid;

/// 降级策略类型
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum FallbackStrategy {
    /// 返回空结果
    EmptyResult,
    /// 返回缓存的默认结果
    CachedDefault,
    /// 返回简化的结果
    SimplifiedResult,
    /// 重定向到备用服务
    RedirectToBackup,
    /// 返回错误信息
    ErrorResponse,
}

/// 降级响应配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FallbackConfig {
    /// 降级策略
    pub strategy: FallbackStrategy,
    /// 降级消息
    pub message: String,
    /// 是否启用降级
    pub enabled: bool,
    /// 降级超时时间（毫秒）
    pub timeout_ms: u64,
}

impl Default for FallbackConfig {
    fn default() -> Self {
        Self {
            strategy: FallbackStrategy::EmptyResult,
            message: "服务暂时不可用，请稍后重试".to_string(),
            enabled: true,
            timeout_ms: 1000,
        }
    }
}

/// 搜索结果降级响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchFallbackResult {
    /// 消息列表（可能为空或简化）
    pub messages: Vec<FallbackMessage>,
    /// 总数
    pub total_count: usize,
    /// 是否为降级结果
    pub is_fallback: bool,
    /// 降级原因
    pub fallback_reason: String,
    /// 建议操作
    pub suggested_action: Option<String>,
}

/// 降级消息结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FallbackMessage {
    /// 消息ID
    pub id: Uuid,
    /// 消息内容
    pub content: String,
    /// 发送者ID
    pub sender_id: Uuid,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 消息类型
    pub message_type: String,
    /// 是否为模拟数据
    pub is_mock: bool,
}

/// 降级策略管理器
///
/// 【目的】: 统一管理各种降级策略，提供灵活的降级响应机制
/// 【设计】: 支持多种降级策略，可根据故障类型选择合适的降级方案
pub struct FallbackManager {
    /// 不同场景的降级配置
    configs: HashMap<String, FallbackConfig>,
    /// 默认降级消息模板
    default_messages: Vec<FallbackMessage>,
}

impl FallbackManager {
    /// 创建新的降级策略管理器
    pub fn new() -> Self {
        info!("🔄 创建降级策略管理器");

        let mut manager = Self {
            configs: HashMap::new(),
            default_messages: Vec::new(),
        };

        // 初始化默认配置
        manager.init_default_configs();
        manager.init_default_messages();

        manager
    }

    /// 初始化默认降级配置
    fn init_default_configs(&mut self) {
        // 缓存故障降级配置
        self.configs.insert(
            "cache_failure".to_string(),
            FallbackConfig {
                strategy: FallbackStrategy::SimplifiedResult,
                message: "缓存服务暂时不可用，返回基础搜索结果".to_string(),
                enabled: true,
                timeout_ms: 500,
            },
        );

        // 数据库故障降级配置
        self.configs.insert(
            "database_failure".to_string(),
            FallbackConfig {
                strategy: FallbackStrategy::CachedDefault,
                message: "数据库服务暂时不可用，返回缓存结果".to_string(),
                enabled: true,
                timeout_ms: 1000,
            },
        );

        // 搜索服务故障降级配置
        self.configs.insert(
            "search_failure".to_string(),
            FallbackConfig {
                strategy: FallbackStrategy::EmptyResult,
                message: "搜索服务暂时不可用，请稍后重试".to_string(),
                enabled: true,
                timeout_ms: 2000,
            },
        );

        // 限流降级配置
        self.configs.insert(
            "rate_limit".to_string(),
            FallbackConfig {
                strategy: FallbackStrategy::ErrorResponse,
                message: "请求频率过高，请稍后重试".to_string(),
                enabled: true,
                timeout_ms: 100,
            },
        );

        info!("✅ 初始化 {} 个默认降级配置", self.configs.len());
    }

    /// 初始化默认降级消息
    fn init_default_messages(&mut self) {
        let now = Utc::now();
        let system_user_id = Uuid::nil(); // 使用nil UUID表示系统用户

        self.default_messages = vec![
            FallbackMessage {
                id: Uuid::new_v4(),
                content: "欢迎使用聊天室！这是一条示例消息。".to_string(),
                sender_id: system_user_id,
                created_at: now,
                message_type: "system".to_string(),
                is_mock: true,
            },
            FallbackMessage {
                id: Uuid::new_v4(),
                content: "系统正在维护中，部分功能可能受到影响。".to_string(),
                sender_id: system_user_id,
                created_at: now,
                message_type: "system".to_string(),
                is_mock: true,
            },
        ];

        info!("✅ 初始化 {} 条默认降级消息", self.default_messages.len());
    }

    /// 执行搜索降级策略
    ///
    /// 【参数】:
    /// - scenario: 降级场景名称
    /// - request: 原始搜索请求
    /// - error_msg: 错误信息
    ///
    /// 【返回】: 降级搜索结果
    pub async fn execute_search_fallback(
        &self,
        scenario: &str,
        request: &SearchGlobalChatRoomMessagesRequest,
        error_msg: &str,
    ) -> AnyhowResult<SearchFallbackResult> {
        let default_config = FallbackConfig::default();
        let config = self.configs.get(scenario).unwrap_or(&default_config);

        if !config.enabled {
            warn!("降级策略 {} 已禁用，返回错误", scenario);
            return Err(anyhow::anyhow!("降级策略已禁用: {}", error_msg));
        }

        info!(
            "🔄 执行搜索降级策略: {} (策略: {:?})",
            scenario, config.strategy
        );

        let result = match config.strategy {
            FallbackStrategy::EmptyResult => self.create_empty_result(config, error_msg),
            FallbackStrategy::CachedDefault => self.create_cached_default_result(config, error_msg),
            FallbackStrategy::SimplifiedResult => {
                self.create_simplified_result(config, request, error_msg)
            }
            FallbackStrategy::RedirectToBackup => {
                self.create_backup_redirect_result(config, error_msg)
            }
            FallbackStrategy::ErrorResponse => {
                return Err(anyhow::anyhow!("降级错误响应: {}", config.message));
            }
        };

        debug!("降级策略执行完成: {}", scenario);
        Ok(result)
    }

    /// 创建空结果降级响应
    fn create_empty_result(
        &self,
        config: &FallbackConfig,
        error_msg: &str,
    ) -> SearchFallbackResult {
        SearchFallbackResult {
            messages: vec![],
            total_count: 0,
            is_fallback: true,
            fallback_reason: format!("空结果降级: {error_msg}"),
            suggested_action: Some("请稍后重试或联系管理员".to_string()),
        }
    }

    /// 创建缓存默认结果降级响应
    fn create_cached_default_result(
        &self,
        config: &FallbackConfig,
        error_msg: &str,
    ) -> SearchFallbackResult {
        SearchFallbackResult {
            messages: self.default_messages.clone(),
            total_count: self.default_messages.len(),
            is_fallback: true,
            fallback_reason: format!("缓存默认结果降级: {error_msg}"),
            suggested_action: Some("显示的是缓存数据，可能不是最新内容".to_string()),
        }
    }

    /// 创建简化结果降级响应
    fn create_simplified_result(
        &self,
        config: &FallbackConfig,
        request: &SearchGlobalChatRoomMessagesRequest,
        error_msg: &str,
    ) -> SearchFallbackResult {
        // 创建一条模拟的搜索结果消息
        let mock_message = FallbackMessage {
            id: Uuid::new_v4(),
            content: format!("搜索关键词 '{}' 的简化结果", request.query),
            sender_id: Uuid::nil(),
            created_at: Utc::now(),
            message_type: "system".to_string(),
            is_mock: true,
        };

        SearchFallbackResult {
            messages: vec![mock_message],
            total_count: 1,
            is_fallback: true,
            fallback_reason: format!("简化结果降级: {error_msg}"),
            suggested_action: Some("这是简化的搜索结果，完整结果请稍后重试".to_string()),
        }
    }

    /// 创建备用重定向结果降级响应
    fn create_backup_redirect_result(
        &self,
        config: &FallbackConfig,
        error_msg: &str,
    ) -> SearchFallbackResult {
        SearchFallbackResult {
            messages: vec![],
            total_count: 0,
            is_fallback: true,
            fallback_reason: format!("备用服务重定向: {error_msg}"),
            suggested_action: Some("请尝试使用备用搜索功能".to_string()),
        }
    }

    /// 注册自定义降级配置
    ///
    /// 【参数】:
    /// - scenario: 场景名称
    /// - config: 降级配置
    pub fn register_fallback_config(&mut self, scenario: String, config: FallbackConfig) {
        self.configs.insert(scenario.clone(), config);
        info!("✅ 注册降级配置: {}", scenario);
    }

    /// 获取降级配置
    ///
    /// 【参数】:
    /// - scenario: 场景名称
    ///
    /// 【返回】: 降级配置（如果存在）
    pub fn get_fallback_config(&self, scenario: &str) -> Option<&FallbackConfig> {
        self.configs.get(scenario)
    }

    /// 列出所有降级场景
    pub fn list_scenarios(&self) -> Vec<String> {
        self.configs.keys().cloned().collect()
    }
}

impl Default for FallbackManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_fallback_manager_creation() {
        let manager = FallbackManager::new();

        // 检查默认配置是否已加载
        assert!(manager.get_fallback_config("cache_failure").is_some());
        assert!(manager.get_fallback_config("database_failure").is_some());
        assert!(manager.get_fallback_config("search_failure").is_some());
        assert!(manager.get_fallback_config("rate_limit").is_some());

        // 检查默认消息是否已加载
        assert!(!manager.default_messages.is_empty());
    }

    #[tokio::test]
    async fn test_search_fallback_execution() {
        let manager = FallbackManager::new();

        let request = SearchGlobalChatRoomMessagesRequest {
            query: "test".to_string(),
            limit: Some(10),
            start_time: None,
            end_time: None,
            sender_id: None,
        };

        // 测试空结果降级
        let result = manager
            .execute_search_fallback("search_failure", &request, "test error")
            .await
            .unwrap();

        assert!(result.is_fallback);
        assert_eq!(result.messages.len(), 0);
        assert_eq!(result.total_count, 0);

        // 测试缓存默认结果降级
        let result = manager
            .execute_search_fallback("database_failure", &request, "db error")
            .await
            .unwrap();

        assert!(result.is_fallback);
        assert!(!result.messages.is_empty());
        assert_eq!(result.total_count, result.messages.len());
    }

    #[tokio::test]
    async fn test_custom_fallback_config() {
        let mut manager = FallbackManager::new();

        let custom_config = FallbackConfig {
            strategy: FallbackStrategy::SimplifiedResult,
            message: "自定义降级消息".to_string(),
            enabled: true,
            timeout_ms: 500,
        };

        manager.register_fallback_config("custom_scenario".to_string(), custom_config);

        let config = manager.get_fallback_config("custom_scenario");
        assert!(config.is_some());
        assert_eq!(config.unwrap().message, "自定义降级消息");
    }
}
