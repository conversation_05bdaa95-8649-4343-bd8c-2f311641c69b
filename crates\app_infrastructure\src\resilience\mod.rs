//! # 弹性模块
//!
//! 提供企业级弹性能力，包括熔断器、限流器、降级策略等
//! 防止系统级联故障，确保高可用性和稳定性

pub mod circuit_breaker;
pub mod fallback;
pub mod rate_limiter;

use anyhow::Result as AnyhowResult;
pub use circuit_breaker::{
    CircuitBreaker, CircuitBreakerConfig, CircuitBreakerError, CircuitBreakerManager,
};
use fallback::FallbackManager;
pub use rate_limiter::{RateLimiterConfig, RateLimiterManager, RateLimiterType};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn};

/// 弹性配置
///
/// 【目的】: 统一配置所有弹性组件的参数
/// 【设计】: 支持不同场景的弹性策略配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResilienceConfig {
    /// 熔断器配置
    pub circuit_breaker: CircuitBreakerConfig,
    /// 限流器配置
    pub rate_limiter: RateLimiterConfig,
    /// 是否启用弹性功能
    pub enabled: bool,
    /// 监控间隔（秒）
    pub monitoring_interval_seconds: u64,
}

impl Default for ResilienceConfig {
    fn default() -> Self {
        Self {
            circuit_breaker: CircuitBreakerConfig::default(),
            rate_limiter: RateLimiterConfig::default(),
            enabled: true,
            monitoring_interval_seconds: 30,
        }
    }
}

/// 弹性统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResilienceStats {
    /// 熔断器触发次数
    pub circuit_breaker_trips: u64,
    /// 限流器拒绝次数
    pub rate_limit_rejections: u64,
    /// 降级执行次数
    pub fallback_executions: u64,
    /// 成功请求数
    pub successful_requests: u64,
    /// 失败请求数
    pub failed_requests: u64,
    /// 统计开始时间
    pub stats_start_time: chrono::DateTime<chrono::Utc>,
}

impl Default for ResilienceStats {
    fn default() -> Self {
        Self {
            circuit_breaker_trips: 0,
            rate_limit_rejections: 0,
            fallback_executions: 0,
            successful_requests: 0,
            failed_requests: 0,
            stats_start_time: chrono::Utc::now(),
        }
    }
}

/// 弹性管理器
///
/// 【目的】: 统一管理所有弹性组件，提供一站式弹性服务
/// 【设计】: 集成熔断器、限流器、降级策略，支持统一配置和监控
pub struct ResilienceManager {
    /// 熔断器管理器
    circuit_breaker_manager: Arc<RwLock<CircuitBreakerManager>>,
    /// 限流器管理器
    rate_limiter_manager: Arc<RwLock<RateLimiterManager>>,
    /// 降级策略管理器
    fallback_manager: Arc<RwLock<FallbackManager>>,
    /// 弹性配置
    config: ResilienceConfig,
    /// 统计信息
    stats: Arc<RwLock<ResilienceStats>>,
}

impl ResilienceManager {
    /// 创建新的弹性管理器
    ///
    /// 【参数】:
    /// - config: 弹性配置
    ///
    /// 【返回】: 弹性管理器实例
    pub async fn new(config: ResilienceConfig) -> AnyhowResult<Self> {
        info!("🛡️ 创建弹性管理器 (启用: {})", config.enabled);

        // 创建熔断器管理器
        let mut circuit_breaker_manager = CircuitBreakerManager::new();

        // 注册默认熔断器
        circuit_breaker_manager
            .register_breaker("cache".to_string(), config.circuit_breaker.clone())?;
        circuit_breaker_manager
            .register_breaker("database".to_string(), config.circuit_breaker.clone())?;
        circuit_breaker_manager
            .register_breaker("search".to_string(), config.circuit_breaker.clone())?;

        // 创建限流器管理器
        let mut rate_limiter_manager = RateLimiterManager::new(config.rate_limiter.clone());

        // 设置全局限流器
        let mut global_config = config.rate_limiter.clone();
        global_config.name = "global".to_string();
        global_config.requests_per_second = 1000; // 全局限流更宽松
        global_config.burst_capacity = 2000;
        rate_limiter_manager.set_global_limiter(global_config);

        // 创建降级策略管理器
        let fallback_manager = FallbackManager::new();

        let manager = Self {
            circuit_breaker_manager: Arc::new(RwLock::new(circuit_breaker_manager)),
            rate_limiter_manager: Arc::new(RwLock::new(rate_limiter_manager)),
            fallback_manager: Arc::new(RwLock::new(fallback_manager)),
            config,
            stats: Arc::new(RwLock::new(ResilienceStats::default())),
        };

        info!("✅ 弹性管理器创建完成");
        Ok(manager)
    }

    /// 获取熔断器实例
    ///
    /// 【参数】:
    /// - name: 熔断器名称
    ///
    /// 【返回】: 熔断器实例（如果存在）
    pub async fn get_circuit_breaker(&self, name: &str) -> Option<Arc<CircuitBreaker>> {
        let manager = self.circuit_breaker_manager.read().await;
        manager.get_breaker(name)
    }

    /// 检查限流状态
    ///
    /// 【参数】:
    /// - limiter_type: 限流器类型
    /// - identifier: 标识符（用户ID、IP等）
    /// - tokens_needed: 需要的令牌数
    ///
    /// 【返回】: 是否通过限流检查
    pub async fn check_rate_limit(
        &self,
        limiter_type: RateLimiterType,
        identifier: &str,
        tokens_needed: f64,
    ) -> bool {
        if !self.config.enabled {
            return true;
        }

        let manager = self.rate_limiter_manager.read().await;

        let result = match limiter_type {
            RateLimiterType::Global => manager.check_global_rate_limit(tokens_needed),
            RateLimiterType::UserBased => {
                if let Ok(user_id) = uuid::Uuid::parse_str(identifier) {
                    manager.check_user_rate_limit(user_id, tokens_needed)
                } else {
                    false
                }
            }
            RateLimiterType::IpBased => {
                if let Ok(ip) = identifier.parse() {
                    manager.check_ip_rate_limit(ip, tokens_needed)
                } else {
                    false
                }
            }
            RateLimiterType::EndpointBased => {
                manager.check_endpoint_rate_limit(identifier, tokens_needed)
            }
        };

        if !result {
            // 更新统计信息
            let mut stats = self.stats.write().await;
            stats.rate_limit_rejections += 1;
            warn!("限流器拒绝请求: {:?} - {}", limiter_type, identifier);
        }

        result
    }

    /// 执行降级策略
    ///
    /// 【参数】:
    /// - scenario: 降级场景
    /// - request: 原始请求
    /// - error_msg: 错误信息
    ///
    /// 【返回】: 降级结果
    pub async fn execute_fallback<T>(
        &self,
        scenario: &str,
        request: &T,
        error_msg: &str,
    ) -> AnyhowResult<fallback::SearchFallbackResult>
    where
        T: Clone,
    {
        if !self.config.enabled {
            return Err(anyhow::anyhow!("弹性功能已禁用"));
        }

        let manager = self.fallback_manager.read().await;

        // 这里需要根据实际请求类型进行转换
        // 暂时使用默认的搜索请求
        let search_request = app_domain::entities::chat::SearchGlobalChatRoomMessagesRequest {
            query: "fallback".to_string(),
            limit: Some(10),
            start_time: None,
            end_time: None,
            sender_id: None,
        };

        let result = manager
            .execute_search_fallback(scenario, &search_request, error_msg)
            .await?;

        // 更新统计信息
        let mut stats = self.stats.write().await;
        stats.fallback_executions += 1;

        Ok(result)
    }

    /// 记录成功请求
    pub async fn record_success(&self) {
        let mut stats = self.stats.write().await;
        stats.successful_requests += 1;
    }

    /// 记录失败请求
    pub async fn record_failure(&self) {
        let mut stats = self.stats.write().await;
        stats.failed_requests += 1;
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> ResilienceStats {
        let stats = self.stats.read().await;
        stats.clone()
    }

    /// 重置统计信息
    pub async fn reset_stats(&self) {
        let mut stats = self.stats.write().await;
        *stats = ResilienceStats::default();
        info!("🔄 弹性统计信息已重置");
    }

    /// 获取配置信息
    pub fn config(&self) -> &ResilienceConfig {
        &self.config
    }

    /// 启动监控任务
    pub async fn start_monitoring(&self) {
        if !self.config.enabled {
            return;
        }

        let stats = self.stats.clone();
        let interval = self.config.monitoring_interval_seconds;

        tokio::spawn(async move {
            let mut interval_timer =
                tokio::time::interval(std::time::Duration::from_secs(interval));

            loop {
                interval_timer.tick().await;

                let current_stats = stats.read().await;
                info!(
                    "📊 弹性统计 - 成功: {}, 失败: {}, 熔断: {}, 限流: {}, 降级: {}",
                    current_stats.successful_requests,
                    current_stats.failed_requests,
                    current_stats.circuit_breaker_trips,
                    current_stats.rate_limit_rejections,
                    current_stats.fallback_executions
                );
            }
        });

        info!("📊 弹性监控任务已启动 (间隔: {}秒)", interval);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_resilience_manager_creation() {
        let config = ResilienceConfig::default();
        let manager = ResilienceManager::new(config).await.unwrap();

        // 测试获取熔断器
        let breaker = manager.get_circuit_breaker("cache").await;
        assert!(breaker.is_some());

        // 测试限流检查
        let result = manager
            .check_rate_limit(RateLimiterType::Global, "test", 1.0)
            .await;
        assert!(result);
    }

    #[tokio::test]
    async fn test_stats_recording() {
        let config = ResilienceConfig::default();
        let manager = ResilienceManager::new(config).await.unwrap();

        // 记录成功和失败
        manager.record_success().await;
        manager.record_failure().await;

        let stats = manager.get_stats().await;
        assert_eq!(stats.successful_requests, 1);
        assert_eq!(stats.failed_requests, 1);

        // 重置统计
        manager.reset_stats().await;
        let stats = manager.get_stats().await;
        assert_eq!(stats.successful_requests, 0);
        assert_eq!(stats.failed_requests, 0);
    }
}
