//! # 限流器实现
//!
//! 基于tower-governor的企业级限流器实现，支持多种限流策略
//! 包括用户级限流、全局限流和API端点限流

use serde::{Deserialize, Serialize};
use std::{
    collections::HashMap,
    net::SocketAddr,
    sync::{Arc, Mutex},
    time::{Duration, Instant},
};
use tracing::{debug, error, info, warn};
use uuid::Uuid;

/// 限流器配置
///
/// 【目的】: 配置限流器的行为参数，支持不同场景的限流策略
/// 【设计】: 基于令牌桶算法，提供灵活的限流配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimiterConfig {
    /// 每秒允许的请求数
    pub requests_per_second: u64,
    /// 突发请求容量（令牌桶大小）
    pub burst_capacity: u64,
    /// 限流器名称
    pub name: String,
    /// 是否启用
    pub enabled: bool,
}

impl Default for RateLimiterConfig {
    fn default() -> Self {
        Self {
            requests_per_second: 10,
            burst_capacity: 20,
            name: "default".to_string(),
            enabled: true,
        }
    }
}

/// 限流器类型
#[derive(Debug, Clone, PartialEq)]
pub enum RateLimiterType {
    /// 基于IP地址的限流
    IpBased,
    /// 基于用户ID的限流
    UserBased,
    /// 全局限流
    Global,
    /// 基于API端点的限流
    EndpointBased,
}

/// 限流器错误类型
#[derive(Debug, thiserror::Error)]
pub enum RateLimiterError {
    #[error("请求频率过高，已被限流")]
    RateLimited,
    #[error("限流器配置错误: {0}")]
    ConfigError(String),
    #[error("限流器初始化失败: {0}")]
    InitializationError(String),
}

/// 令牌桶限流器实现
///
/// 【目的】: 基于令牌桶算法的高性能限流器
/// 【特性】: 支持突发流量、平滑限流、自动令牌补充
pub struct TokenBucketRateLimiter {
    /// 配置信息
    config: RateLimiterConfig,
    /// 当前令牌数
    tokens: Arc<Mutex<f64>>,
    /// 上次补充令牌的时间
    last_refill: Arc<Mutex<Instant>>,
}

impl TokenBucketRateLimiter {
    /// 创建新的令牌桶限流器
    ///
    /// 【参数】:
    /// - config: 限流器配置
    ///
    /// 【返回】: 限流器实例
    pub fn new(config: RateLimiterConfig) -> Self {
        info!(
            "🚦 创建令牌桶限流器: {} (QPS: {}, 突发容量: {})",
            config.name, config.requests_per_second, config.burst_capacity
        );

        Self {
            tokens: Arc::new(Mutex::new(config.burst_capacity as f64)),
            last_refill: Arc::new(Mutex::new(Instant::now())),
            config,
        }
    }

    /// 尝试获取令牌
    ///
    /// 【参数】:
    /// - tokens_needed: 需要的令牌数（默认为1）
    ///
    /// 【返回】: 是否成功获取令牌
    pub fn try_acquire(&self, tokens_needed: f64) -> bool {
        if !self.config.enabled {
            return true;
        }

        // 补充令牌
        self.refill_tokens();

        let mut tokens = self.tokens.lock().unwrap();
        if *tokens >= tokens_needed {
            *tokens -= tokens_needed;
            debug!(
                "限流器 {} 获取令牌成功，剩余令牌: {:.2}",
                self.config.name, *tokens
            );
            true
        } else {
            warn!(
                "限流器 {} 令牌不足，需要: {:.2}, 剩余: {:.2}",
                self.config.name, tokens_needed, *tokens
            );
            false
        }
    }

    /// 补充令牌
    fn refill_tokens(&self) {
        let now = Instant::now();
        let mut last_refill = self.last_refill.lock().unwrap();
        let time_passed = now.duration_since(*last_refill);

        if time_passed >= Duration::from_millis(100) {
            // 每100ms补充一次令牌
            let tokens_to_add = time_passed.as_secs_f64() * self.config.requests_per_second as f64;

            let mut tokens = self.tokens.lock().unwrap();
            *tokens = (*tokens + tokens_to_add).min(self.config.burst_capacity as f64);
            *last_refill = now;

            debug!(
                "限流器 {} 补充令牌: +{:.2}, 当前令牌: {:.2}",
                self.config.name, tokens_to_add, *tokens
            );
        }
    }

    /// 获取当前令牌数
    pub fn current_tokens(&self) -> f64 {
        self.refill_tokens();
        *self.tokens.lock().unwrap()
    }

    /// 获取配置信息
    pub fn config(&self) -> &RateLimiterConfig {
        &self.config
    }
}

/// 多级限流器管理器
///
/// 【目的】: 管理多个限流器实例，支持不同类型的限流策略
/// 【设计】: 支持用户级、IP级、全局和端点级限流
pub struct RateLimiterManager {
    /// 用户级限流器
    user_limiters: Arc<Mutex<HashMap<Uuid, Arc<TokenBucketRateLimiter>>>>,
    /// IP级限流器
    ip_limiters: Arc<Mutex<HashMap<SocketAddr, Arc<TokenBucketRateLimiter>>>>,
    /// 全局限流器
    global_limiter: Option<Arc<TokenBucketRateLimiter>>,
    /// 端点级限流器
    endpoint_limiters: Arc<Mutex<HashMap<String, Arc<TokenBucketRateLimiter>>>>,
    /// 默认配置
    default_config: RateLimiterConfig,
}

impl RateLimiterManager {
    /// 创建新的限流器管理器
    ///
    /// 【参数】:
    /// - default_config: 默认限流器配置
    ///
    /// 【返回】: 限流器管理器实例
    pub fn new(default_config: RateLimiterConfig) -> Self {
        info!("🚦 创建限流器管理器");

        Self {
            user_limiters: Arc::new(Mutex::new(HashMap::new())),
            ip_limiters: Arc::new(Mutex::new(HashMap::new())),
            global_limiter: None,
            endpoint_limiters: Arc::new(Mutex::new(HashMap::new())),
            default_config,
        }
    }

    /// 设置全局限流器
    ///
    /// 【参数】:
    /// - config: 全局限流器配置
    pub fn set_global_limiter(&mut self, config: RateLimiterConfig) {
        let limiter = Arc::new(TokenBucketRateLimiter::new(config));
        self.global_limiter = Some(limiter);
        info!("✅ 设置全局限流器");
    }

    /// 检查用户级限流
    ///
    /// 【参数】:
    /// - user_id: 用户ID
    /// - tokens_needed: 需要的令牌数
    ///
    /// 【返回】: 是否通过限流检查
    pub fn check_user_rate_limit(&self, user_id: Uuid, tokens_needed: f64) -> bool {
        let mut limiters = self.user_limiters.lock().unwrap();

        let limiter = limiters
            .entry(user_id)
            .or_insert_with(|| {
                let mut config = self.default_config.clone();
                config.name = format!("user_{user_id}");
                Arc::new(TokenBucketRateLimiter::new(config))
            })
            .clone();

        drop(limiters);
        limiter.try_acquire(tokens_needed)
    }

    /// 检查IP级限流
    ///
    /// 【参数】:
    /// - ip: IP地址
    /// - tokens_needed: 需要的令牌数
    ///
    /// 【返回】: 是否通过限流检查
    pub fn check_ip_rate_limit(&self, ip: SocketAddr, tokens_needed: f64) -> bool {
        let mut limiters = self.ip_limiters.lock().unwrap();

        let limiter = limiters
            .entry(ip)
            .or_insert_with(|| {
                let mut config = self.default_config.clone();
                config.name = format!("ip_{ip}");
                Arc::new(TokenBucketRateLimiter::new(config))
            })
            .clone();

        drop(limiters);
        limiter.try_acquire(tokens_needed)
    }

    /// 检查全局限流
    ///
    /// 【参数】:
    /// - tokens_needed: 需要的令牌数
    ///
    /// 【返回】: 是否通过限流检查
    pub fn check_global_rate_limit(&self, tokens_needed: f64) -> bool {
        if let Some(limiter) = &self.global_limiter {
            limiter.try_acquire(tokens_needed)
        } else {
            true // 如果没有设置全局限流器，则不限流
        }
    }

    /// 检查端点级限流
    ///
    /// 【参数】:
    /// - endpoint: 端点名称
    /// - tokens_needed: 需要的令牌数
    ///
    /// 【返回】: 是否通过限流检查
    pub fn check_endpoint_rate_limit(&self, endpoint: &str, tokens_needed: f64) -> bool {
        let mut limiters = self.endpoint_limiters.lock().unwrap();

        let limiter = limiters
            .entry(endpoint.to_string())
            .or_insert_with(|| {
                let mut config = self.default_config.clone();
                config.name = format!("endpoint_{endpoint}");
                Arc::new(TokenBucketRateLimiter::new(config))
            })
            .clone();

        drop(limiters);
        limiter.try_acquire(tokens_needed)
    }

    /// 清理过期的限流器实例（定期调用以释放内存）
    pub fn cleanup_expired_limiters(&self) {
        // 这里可以实现基于时间的清理逻辑
        // 例如，清理超过一定时间未使用的限流器实例
        debug!("清理过期的限流器实例");
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::time::{Duration, sleep};

    #[tokio::test]
    async fn test_token_bucket_rate_limiter() {
        let config = RateLimiterConfig {
            requests_per_second: 5,
            burst_capacity: 10,
            name: "test".to_string(),
            enabled: true,
        };

        let limiter = TokenBucketRateLimiter::new(config);

        // 测试突发请求
        for i in 0..10 {
            assert!(limiter.try_acquire(1.0), "第{}次请求应该成功", i + 1);
        }

        // 第11次请求应该失败
        assert!(!limiter.try_acquire(1.0), "第11次请求应该失败");

        // 等待令牌补充
        sleep(Duration::from_millis(300)).await;

        // 现在应该可以再次获取令牌
        assert!(limiter.try_acquire(1.0), "等待后的请求应该成功");
    }

    #[tokio::test]
    async fn test_rate_limiter_manager() {
        let config = RateLimiterConfig::default();
        let mut manager = RateLimiterManager::new(config.clone());

        // 设置全局限流器
        manager.set_global_limiter(config);

        let user_id = Uuid::new_v4();
        let ip = "127.0.0.1:8080".parse().unwrap();

        // 测试用户级限流
        assert!(manager.check_user_rate_limit(user_id, 1.0));

        // 测试IP级限流
        assert!(manager.check_ip_rate_limit(ip, 1.0));

        // 测试全局限流
        assert!(manager.check_global_rate_limit(1.0));

        // 测试端点级限流
        assert!(manager.check_endpoint_rate_limit("search", 1.0));
    }
}
