//! # 数据库集成测试
//!
//! 测试数据库连接和基本操作

use app_common::test_config::{init_test_environment, create_test_database, TestAssertions};
use sea_orm::{Database, DatabaseConnection};

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_database_connection() {
        init_test_environment();
        
        // 测试数据库连接创建
        let db_result = create_test_database().await;
        TestAssertions::assert_ok(&db_result);
        
        let _db = db_result.unwrap();
        // 数据库连接创建成功
    }

    #[tokio::test]
    async fn test_multiple_database_connections() {
        init_test_environment();
        
        // 测试多个数据库连接
        let db1_result = create_test_database().await;
        let db2_result = create_test_database().await;
        
        TestAssertions::assert_ok(&db1_result);
        TestAssertions::assert_ok(&db2_result);
        
        let _db1 = db1_result.unwrap();
        let _db2 = db2_result.unwrap();
        
        // 两个连接都应该成功创建
    }

    #[test]
    fn test_database_url_configuration() {
        init_test_environment();
        
        // 测试数据库URL配置
        let config = app_common::test_config::TestConfig::new()
            .with_database_url("sqlite::memory:".to_string());
        
        assert_eq!(config.database_url, "sqlite::memory:");
    }

    #[test]
    fn test_test_configuration() {
        init_test_environment();
        
        // 测试配置创建
        let config = app_common::test_config::TestConfig::new()
            .with_logging()
            .with_timeout(60)
            .with_concurrent_tests(8);
        
        assert!(config.enable_logging);
        assert_eq!(config.test_timeout, 60);
        assert_eq!(config.concurrent_tests, 8);
    }
}
