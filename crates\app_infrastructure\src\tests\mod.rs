//! # 基础设施层集成测试模块
//!
//! 测试仓储实现类与数据库的集成，确保数据访问层正常工作
//!
//! ## 测试策略
//! - 使用真实的数据库连接（内存SQLite）
//! - 测试完整的CRUD操作
//! - 验证数据一致性和事务处理
//! - 测试错误处理和边界条件

pub mod repository_integration_tests;
pub mod database_integration_tests;
pub mod transaction_tests;

// 重新导出测试模块
pub use repository_integration_tests::*;
pub use database_integration_tests::*;
pub use transaction_tests::*;

#[cfg(test)]
mod integration_tests {
    use super::*;
    use app_common::test_config::{init_test_environment, get_test_database, TestAssertions};

    #[tokio::test]
    async fn test_infrastructure_module_integration() {
        init_test_environment();
        
        // 测试数据库连接
        let db_result = get_test_database().await;
        TestAssertions::assert_ok(&db_result);
        
        // 验证基础设施模块正确导入
        assert!(std::any::type_name::<crate::UserRepository>()
            .contains("UserRepository"));
        assert!(std::any::type_name::<crate::TaskRepository>()
            .contains("TaskRepository"));
        assert!(std::any::type_name::<crate::ChatRepository>()
            .contains("ChatRepository"));
    }
}
