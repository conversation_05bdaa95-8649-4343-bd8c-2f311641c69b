//! # WebSocket 连接管理器实现
//!
//! 提供WebSocket连接的具体管理实现

use app_domain::websocket::*;
use async_trait::async_trait;
use axum::extract::ws::Message;
use parking_lot::RwLock;
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{ AtomicU64, Ordering };
use std::time::{ Duration, Instant };
use tokio::sync::mpsc;
use tracing::{ info, warn };
use uuid::Uuid;

/// WebSocket连接信息
///
/// 【功能】: 存储单个WebSocket连接的详细信息
#[derive(Debug)]
pub struct ConnectionInfo {
    /// 会话信息
    pub session: WsSession,
    /// 消息发送通道
    pub sender: mpsc::UnboundedSender<Message>,
    /// 最后活跃时间
    pub last_activity: Instant,
}

impl ConnectionInfo {
    /// 创建新的连接信息
    pub fn new(session: WsSession, sender: mpsc::UnboundedSender<Message>) -> Self {
        Self {
            session,
            sender,
            last_activity: Instant::now(),
        }
    }

    /// 发送消息
    pub fn send_message(&self, message: Message) -> Result<(), String> {
        self.sender.send(message).map_err(|e| format!("发送消息失败: {e}"))
    }

    /// 检查连接是否活跃
    pub fn is_active(&self) -> bool {
        // 🔧 修复：在连接建立阶段，如果会话状态是Connected，则认为连接是活跃的
        // 即使发送通道可能是占位通道（在update_connection_sender之前）
        if self.session.is_active() {
            true
        } else {
            // 只有当会话不活跃时，才检查发送通道状态
            !self.sender.is_closed()
        }
    }
}

/// WebSocket连接管理器实现
///
/// 【功能】: 实现WebSocket连接的生命周期管理
pub struct WebSocketConnectionManager {
    /// 活跃连接映射表
    connections: Arc<RwLock<HashMap<ConnectionId, ConnectionInfo>>>,
    /// 用户ID到连接ID的映射
    user_connections: Arc<RwLock<HashMap<Uuid, Vec<ConnectionId>>>>,
    /// 统计计数器
    total_connections: AtomicU64,
    successful_connections: AtomicU64,
    failed_connections: AtomicU64,
}

impl WebSocketConnectionManager {
    /// 创建新的连接管理器
    pub fn new() -> Self {
        Self {
            connections: Arc::new(RwLock::new(HashMap::new())),
            user_connections: Arc::new(RwLock::new(HashMap::new())),
            total_connections: AtomicU64::new(0),
            successful_connections: AtomicU64::new(0),
            failed_connections: AtomicU64::new(0),
        }
    }

    /// 添加连接（带外部sender）
    ///
    /// 【功能】: 添加WebSocket连接，使用外部提供的消息发送通道
    pub async fn add_connection_with_sender(
        &self,
        session: WsSession,
        sender: mpsc::UnboundedSender<Message>
    ) -> Result<(), String> {
        let connection_id = session.id;
        let user_id = session.user_id;

        // 🔧 修复：在添加新连接前，先清理该用户的所有旧连接
        self.cleanup_user_connections(&user_id).await;

        // 创建连接信息
        let conn_info = ConnectionInfo::new(session, sender);

        // 添加到连接映射
        {
            let mut connections = self.connections.write();
            connections.insert(connection_id, conn_info);
            tracing::info!(
                "✅ 连接已添加到管理器: 连接ID={}, 当前总连接数={}",
                connection_id,
                connections.len()
            );
        }

        // 添加到用户映射
        self.add_user_connection(user_id, connection_id);

        // 更新统计
        self.total_connections.fetch_add(1, Ordering::Relaxed);
        self.successful_connections.fetch_add(1, Ordering::Relaxed);

        info!("WebSocket连接已添加: 连接ID={}, 用户ID={}", connection_id, user_id);
        Ok(())
    }

    /// 添加连接到用户映射
    fn add_user_connection(&self, user_id: Uuid, connection_id: ConnectionId) {
        let mut user_connections = self.user_connections.write();
        user_connections.entry(user_id).or_default().push(connection_id);
    }

    /// 从用户映射中移除连接
    fn remove_user_connection(&self, user_id: &Uuid, connection_id: &ConnectionId) {
        let mut user_connections = self.user_connections.write();
        if let Some(connections) = user_connections.get_mut(user_id) {
            connections.retain(|id| id != connection_id);
            if connections.is_empty() {
                user_connections.remove(user_id);
            }
        }
    }

    /// 获取连接统计信息
    pub fn get_stats(&self) -> (usize, u64, u64, u64) {
        let active_count = self.connections.read().len();
        let total = self.total_connections.load(Ordering::Relaxed);
        let successful = self.successful_connections.load(Ordering::Relaxed);
        let failed = self.failed_connections.load(Ordering::Relaxed);
        (active_count, total, successful, failed)
    }

    /// 更新连接的发送通道
    ///
    /// 【功能】: 更新已存在连接的消息发送通道
    /// 【参数】:
    /// * `connection_id` - 连接ID
    /// * `sender` - 新的消息发送通道
    /// 【返回值】: Result<(), String> - 操作结果
    pub async fn update_connection_sender(
        &self,
        connection_id: &ConnectionId,
        sender: mpsc::UnboundedSender<Message>
    ) -> Result<(), String> {
        let mut connections = self.connections.write();
        if let Some(conn_info) = connections.get_mut(connection_id) {
            // 检查旧的发送通道是否已关闭
            if conn_info.sender.is_closed() {
                warn!("旧的发送通道已关闭，正在更新: 连接ID={}", connection_id);
            }

            // 更新发送通道
            conn_info.sender = sender;
            info!("WebSocket连接发送通道已更新: 连接ID={}", connection_id);
            Ok(())
        } else {
            warn!("尝试更新不存在的连接发送通道: 连接ID={}", connection_id);
            Err(format!("连接不存在: {connection_id}"))
        }
    }

    /// 验证连接状态
    ///
    /// 【功能】: 检查连接是否仍然活跃和有效
    /// 【参数】:
    /// * `connection_id` - 连接ID
    /// 【返回值】: bool - 连接是否有效
    pub async fn validate_connection_state(&self, connection_id: &ConnectionId) -> bool {
        let connections = self.connections.read();
        if let Some(conn_info) = connections.get(connection_id) {
            conn_info.is_active()
        } else {
            false
        }
    }

    /// 清理无效连接
    ///
    /// 【功能】: 清理已断开或无效的连接
    /// 【返回值】: usize - 清理的连接数量
    pub async fn cleanup_stale_connections(&self) -> usize {
        let mut connections = self.connections.write();
        let mut user_connections = self.user_connections.write();

        let mut stale_connections = Vec::new();

        // 找出无效连接
        for (connection_id, conn_info) in connections.iter() {
            if !conn_info.is_active() {
                stale_connections.push(*connection_id);
            }
        }

        let cleanup_count = stale_connections.len();

        // 移除无效连接
        for connection_id in stale_connections {
            if let Some(conn_info) = connections.remove(&connection_id) {
                let user_id = conn_info.session.user_id;

                // 从用户连接映射中移除
                if let Some(user_conn_list) = user_connections.get_mut(&user_id) {
                    user_conn_list.retain(|&id| id != connection_id);
                    if user_conn_list.is_empty() {
                        user_connections.remove(&user_id);
                    }
                }

                info!("已清理无效连接: 连接ID={}, 用户ID={}", connection_id, user_id);
            }
        }

        if cleanup_count > 0 {
            info!("连接清理完成: 清理了{}个无效连接", cleanup_count);
        }

        cleanup_count
    }

    /// 清理指定用户的所有连接
    ///
    /// 【功能】: 清理指定用户的所有现有连接，确保每个用户只有一个活跃连接
    /// 【参数】:
    /// * `user_id` - 用户ID
    /// 【返回值】: usize - 清理的连接数量
    pub async fn cleanup_user_connections(&self, user_id: &Uuid) -> usize {
        let mut cleanup_count = 0;

        // 🔧 修复：分两步进行清理，避免死锁和确保彻底清理
        // 第一步：获取该用户的所有连接ID
        let user_conn_list = {
            let user_connections = self.user_connections.read();
            user_connections.get(user_id).cloned().unwrap_or_default()
        };

        // 第二步：逐个清理连接
        for connection_id in user_conn_list {
            let removed = {
                let mut connections = self.connections.write();
                connections.remove(&connection_id).is_some()
            };

            if removed {
                cleanup_count += 1;
                info!("🧹 清理用户旧连接: 用户ID={}, 连接ID={}", user_id, connection_id);
            }
        }

        // 第三步：清空该用户的连接列表
        {
            let mut user_connections = self.user_connections.write();
            user_connections.remove(user_id);
        }

        if cleanup_count > 0 {
            info!("用户连接清理完成: 用户ID={}, 清理了{}个连接", user_id, cleanup_count);
        }

        cleanup_count
    }

    /// 向连接发送消息
    pub async fn send_to_connection(
        &self,
        connection_id: &ConnectionId,
        message: Message
    ) -> Result<(), String> {
        let connections = self.connections.read();
        if let Some(conn_info) = connections.get(connection_id) {
            conn_info.send_message(message)
        } else {
            Err(format!("连接不存在: {connection_id}"))
        }
    }

    /// 向用户的所有连接发送消息
    pub async fn send_to_user_connections(
        &self,
        user_id: &Uuid,
        message: Message
    ) -> Result<usize, String> {
        let user_connections = self.user_connections.read();
        let connections = self.connections.read();

        if let Some(connection_ids) = user_connections.get(user_id) {
            let mut sent_count = 0;
            for connection_id in connection_ids {
                if let Some(conn_info) = connections.get(connection_id) {
                    if conn_info.send_message(message.clone()).is_ok() {
                        sent_count += 1;
                    }
                }
            }
            Ok(sent_count)
        } else {
            Ok(0)
        }
    }

    /// 广播消息给所有连接
    pub async fn broadcast_to_all_connections(
        &self,
        message: Message,
        exclude_connection: Option<&ConnectionId>
    ) -> Result<usize, String> {
        let connections = self.connections.read();
        let mut sent_count = 0;
        let total_connections = connections.len();

        tracing::info!(
            "开始广播消息: 总连接数={}, 排除连接={:?}",
            total_connections,
            exclude_connection
        );

        // 添加详细的连接信息调试
        if total_connections == 0 {
            tracing::warn!("⚠️ 连接管理器中没有活跃连接！");
        } else {
            for (id, _) in connections.iter() {
                tracing::debug!("活跃连接: {}", id);
            }
        }

        for (connection_id, conn_info) in connections.iter() {
            tracing::debug!("检查连接: 连接ID={}", connection_id);

            // 排除指定连接
            if let Some(exclude_id) = exclude_connection {
                if connection_id == exclude_id {
                    tracing::debug!("跳过发送者连接: 连接ID={}", connection_id);
                    continue;
                }
            }

            match conn_info.send_message(message.clone()) {
                Ok(_) => {
                    sent_count += 1;
                    tracing::debug!("消息发送成功: 连接ID={}", connection_id);
                }
                Err(e) => {
                    tracing::warn!("消息发送失败: 连接ID={}, 错误={}", connection_id, e);
                }
            }
        }

        // 检查广播异常情况
        if sent_count == 0 && total_connections > 0 {
            tracing::warn!("⚠️ 消息广播异常: 有{}个连接但发送了0条消息", total_connections);
        }
        tracing::info!("广播消息完成: 总连接数={}, 成功发送数={}", total_connections, sent_count);
        Ok(sent_count)
    }

    /// 获取非活跃连接列表
    ///
    /// 【功能】: 获取超过指定时间未活跃的连接ID列表
    pub async fn get_inactive_connections(&self, timeout: Duration) -> Vec<ConnectionId> {
        let now = Instant::now();
        let connections = self.connections.read();

        connections
            .values()
            .filter(|conn| now.duration_since(conn.last_activity) > timeout)
            .map(|conn| conn.session.id)
            .collect()
    }

    /// 获取健康连接列表
    ///
    /// 【功能】: 获取在指定时间内有活动的健康连接列表
    pub async fn get_healthy_connections(&self, health_timeout: Duration) -> Vec<WsSession> {
        let now = Instant::now();
        let connections = self.connections.read();

        connections
            .values()
            .filter(|conn| now.duration_since(conn.last_activity) <= health_timeout)
            .map(|conn| conn.session.clone())
            .collect()
    }

    /// 更新连接活跃状态
    ///
    /// 【功能】: 更新指定连接的最后活跃时间
    pub async fn update_connection_activity(
        &self,
        connection_id: &ConnectionId
    ) -> Result<(), String> {
        let mut connections = self.connections.write();

        if let Some(conn_info) = connections.get_mut(connection_id) {
            conn_info.last_activity = Instant::now();
            tracing::debug!("更新连接活跃状态: 连接ID={}", connection_id);
            Ok(())
        } else {
            Err(format!("连接不存在: {connection_id}"))
        }
    }

    /// 执行连接健康检查
    ///
    /// 【功能】: 检查所有连接的健康状态，移除超时连接
    pub async fn perform_health_check(&self, timeout: Duration) -> usize {
        let inactive_connections = self.get_inactive_connections(timeout).await;
        let removed_count = inactive_connections.len();

        for connection_id in inactive_connections {
            if let Err(e) = self.remove_connection(&connection_id).await {
                tracing::warn!("移除非活跃连接失败: 连接ID={}, 错误={}", connection_id, e);
            } else {
                tracing::info!("移除非活跃连接: 连接ID={}", connection_id);
            }
        }

        removed_count
    }
}

impl Default for WebSocketConnectionManager {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl WebSocketConnectionService for WebSocketConnectionManager {
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }
    async fn add_connection(&self, session: WsSession) -> Result<(), String> {
        // 创建一个占位的发送通道，实际的发送通道需要通过update_connection_sender更新
        // 🔧 修复：保持receiver以防止通道立即关闭
        let (sender, receiver) = mpsc::unbounded_channel();

        // 将receiver存储在连接信息中，防止通道关闭
        // 这是一个临时解决方案，直到真正的发送通道被设置
        tokio::spawn(async move {
            let mut receiver = receiver;
            while receiver.recv().await.is_some() {
                // 丢弃消息，这只是为了保持通道开放
            }
        });

        // 使用add_connection_with_sender方法
        self.add_connection_with_sender(session, sender).await
    }

    async fn remove_connection(&self, connection_id: &ConnectionId) -> Result<(), String> {
        let user_id = {
            let mut connections = self.connections.write();
            if let Some(conn_info) = connections.remove(connection_id) {
                conn_info.session.user_id
            } else {
                // 🔧 修复：连接不存在时返回Ok，实现幂等性
                // 这避免了重复清理时的错误，特别是在并发清理场景下
                tracing::debug!("尝试移除不存在的连接: {}", connection_id);
                return Ok(());
            }
        };

        // 从用户映射中移除
        self.remove_user_connection(&user_id, connection_id);

        info!("WebSocket连接已移除: 连接ID={}, 用户ID={}", connection_id, user_id);
        Ok(())
    }

    async fn get_connection(&self, connection_id: &ConnectionId) -> Option<WsSession> {
        let connections = self.connections.read();
        connections.get(connection_id).map(|info| info.session.clone())
    }

    async fn get_user_connections(&self, user_id: &Uuid) -> Vec<WsSession> {
        let user_connections = self.user_connections.read();
        let connections = self.connections.read();

        if let Some(connection_ids) = user_connections.get(user_id) {
            connection_ids
                .iter()
                .filter_map(|id| connections.get(id).map(|info| info.session.clone()))
                .collect()
        } else {
            Vec::new()
        }
    }

    async fn get_active_connections(&self) -> Vec<WsSession> {
        let connections = self.connections.read();
        connections
            .values()
            .filter(|info| info.is_active())
            .map(|info| info.session.clone())
            .collect()
    }

    async fn update_activity(&self, connection_id: &ConnectionId) -> Result<(), String> {
        let mut connections = self.connections.write();
        if let Some(conn_info) = connections.get_mut(connection_id) {
            conn_info.session.update_activity();
            conn_info.last_activity = std::time::Instant::now(); // 🔧 修复：同时更新连接信息的活跃时间
            Ok(())
        } else {
            // 🔧 修复：连接不存在时返回Ok，避免心跳任务因此退出
            // 这种情况在连接清理过程中是正常的
            tracing::debug!("尝试更新不存在的连接活跃状态: {}", connection_id);
            Ok(())
        }
    }

    async fn update_quality(
        &self,
        connection_id: &ConnectionId,
        quality: ConnectionQuality
    ) -> Result<(), String> {
        let mut connections = self.connections.write();
        if let Some(conn_info) = connections.get_mut(connection_id) {
            conn_info.session.update_quality(quality);
            Ok(())
        } else {
            Err(format!("连接不存在: {connection_id}"))
        }
    }

    async fn cleanup_idle_connections(&self, idle_timeout_seconds: i64) -> usize {
        let mut to_remove = Vec::new();

        // 查找空闲连接
        {
            let connections = self.connections.read();
            for (connection_id, conn_info) in connections.iter() {
                if
                    !conn_info.is_active() ||
                    conn_info.session.idle_seconds() > idle_timeout_seconds
                {
                    to_remove.push(*connection_id);
                }
            }
        }

        // 移除空闲连接
        let mut removed_count = 0;
        for connection_id in to_remove {
            if self.remove_connection(&connection_id).await.is_ok() {
                removed_count += 1;
            }
        }

        if removed_count > 0 {
            info!("清理了{}个空闲WebSocket连接", removed_count);
        }

        removed_count
    }

    async fn register_sender(
        &self,
        connection_id: &ConnectionId,
        sender_id: String
    ) -> Result<(), String> {
        // 验证连接是否存在
        let connections = self.connections.read();
        if connections.contains_key(connection_id) {
            info!("连接发送通道已注册: 连接ID={}, 发送通道ID={}", connection_id, sender_id);
            Ok(())
        } else {
            Err(format!("连接不存在，无法注册发送通道: {connection_id}"))
        }
    }

    async fn update_connection_sender(
        &self,
        connection_id: &ConnectionId,
        _sender_info: String
    ) -> Result<(), String> {
        // 注意：这个方法现在只是标记，实际的发送通道更新通过公共方法完成
        info!("连接发送通道更新请求: 连接ID={}", connection_id);
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_connection_manager_basic_operations() {
        let manager = WebSocketConnectionManager::new();
        let user_id = Uuid::new_v4();
        let session = WsSession::new(user_id, "test_user".to_string(), SessionType::Chat);
        let connection_id = session.id;

        // 测试添加连接
        assert!(manager.add_connection(session).await.is_ok());

        // 测试获取连接
        let retrieved_session = manager.get_connection(&connection_id).await;
        assert!(retrieved_session.is_some());
        assert_eq!(retrieved_session.unwrap().user_id, user_id);

        // 测试获取用户连接
        let user_connections = manager.get_user_connections(&user_id).await;
        assert_eq!(user_connections.len(), 1);

        // 测试移除连接
        assert!(manager.remove_connection(&connection_id).await.is_ok());

        // 验证连接已移除
        let retrieved_session = manager.get_connection(&connection_id).await;
        assert!(retrieved_session.is_none());
    }

    #[tokio::test]
    async fn test_connection_manager_stats() {
        let manager = WebSocketConnectionManager::new();
        let user_id = Uuid::new_v4();

        // 初始统计
        let (active, total, _successful, _failed) = manager.get_stats();
        assert_eq!(active, 0);
        assert_eq!(total, 0);

        // 添加连接
        let session = WsSession::new(user_id, "test_user".to_string(), SessionType::Chat);
        assert!(manager.add_connection(session).await.is_ok());

        // 检查统计
        let (active, total, successful, _failed) = manager.get_stats();
        assert_eq!(active, 1);
        assert_eq!(total, 1);
        assert_eq!(successful, 1);
    }
}
