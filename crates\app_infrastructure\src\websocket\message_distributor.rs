//! # WebSocket 消息分发器实现
//!
//! 提供WebSocket消息分发的具体实现

use app_domain::websocket::*;
use async_trait::async_trait;
use axum::extract::ws::Message;
use std::sync::Arc;
use tracing::{debug, warn};
use uuid::Uuid;

use super::connection_manager::WebSocketConnectionManager;

/// WebSocket消息分发器实现
///
/// 【功能】: 实现WebSocket消息的分发和广播
pub struct WebSocketMessageDistributor {
    /// 连接管理器
    connection_manager: Arc<WebSocketConnectionManager>,
}

impl WebSocketMessageDistributor {
    /// 创建新的消息分发器
    ///
    /// 【参数】:
    /// * `connection_manager` - 连接管理器
    pub fn new(connection_manager: Arc<WebSocketConnectionManager>) -> Self {
        Self { connection_manager }
    }

    /// 将领域消息转换为WebSocket消息
    fn convert_to_ws_message(&self, message: &WsMessage) -> Message {
        match message.message_type {
            MessageType::Text => {
                if let Some(text) = message.as_text() {
                    Message::Text(text.into())
                } else {
                    Message::Text(String::from_utf8_lossy(&message.content).to_string().into())
                }
            }
            MessageType::Binary => Message::Binary(message.content.clone().into()),
            MessageType::Ping => Message::Ping(message.content.clone().into()),
            MessageType::Pong => Message::Pong(message.content.clone().into()),
            MessageType::Close => Message::Close(None),
        }
    }
}

#[async_trait]
impl WebSocketMessageService for WebSocketMessageDistributor {
    async fn send_to_connection(
        &self,
        connection_id: &ConnectionId,
        message: WsMessage,
    ) -> Result<(), String> {
        debug!("发送消息到连接: 连接ID={}", connection_id);

        let ws_message = self.convert_to_ws_message(&message);

        self.connection_manager
            .send_to_connection(connection_id, ws_message)
            .await
    }

    async fn send_to_user(&self, user_id: &Uuid, message: WsMessage) -> Result<usize, String> {
        debug!("发送消息到用户: 用户ID={}", user_id);

        let ws_message = self.convert_to_ws_message(&message);

        self.connection_manager
            .send_to_user_connections(user_id, ws_message)
            .await
    }

    async fn broadcast_to_all(
        &self,
        message: WsMessage,
        exclude_sender: bool,
    ) -> Result<usize, String> {
        debug!("广播消息到所有连接: 排除发送者={}", exclude_sender);

        let ws_message = self.convert_to_ws_message(&message);
        let exclude_connection = if exclude_sender {
            message.sender_id.as_ref()
        } else {
            None
        };

        self.connection_manager
            .broadcast_to_all_connections(ws_message, exclude_connection)
            .await
    }

    async fn broadcast_to_users(
        &self,
        user_ids: &[Uuid],
        message: WsMessage,
    ) -> Result<usize, String> {
        debug!("广播消息到指定用户: 用户数量={}", user_ids.len());

        let ws_message = self.convert_to_ws_message(&message);
        let mut total_sent = 0;

        for user_id in user_ids {
            match self
                .connection_manager
                .send_to_user_connections(user_id, ws_message.clone())
                .await
            {
                Ok(sent_count) => {
                    total_sent += sent_count;
                }
                Err(e) => {
                    warn!("发送消息到用户 {} 失败: {}", user_id, e);
                }
            }
        }

        Ok(total_sent)
    }

    async fn broadcast_to_session_type(
        &self,
        session_type: &SessionType,
        message: WsMessage,
    ) -> Result<usize, String> {
        debug!("广播消息到会话类型: 类型={:?}", session_type);

        let ws_message = self.convert_to_ws_message(&message);
        let active_connections = self.connection_manager.get_active_connections().await;

        let mut sent_count = 0;
        for session in active_connections {
            if session.session_type == *session_type {
                match self
                    .connection_manager
                    .send_to_connection(&session.id, ws_message.clone())
                    .await
                {
                    Ok(()) => {
                        sent_count += 1;
                    }
                    Err(e) => {
                        warn!("发送消息到连接 {} 失败: {}", session.id, e);
                    }
                }
            }
        }

        Ok(sent_count)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tokio::sync::mpsc;

    #[tokio::test]
    async fn test_message_distributor_creation() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let distributor = WebSocketMessageDistributor::new(connection_manager);

        // 测试消息转换
        let message = WsMessage::new_text("Hello".to_string(), None);
        let ws_message = distributor.convert_to_ws_message(&message);

        match ws_message {
            Message::Text(text) => assert_eq!(text, "Hello"),
            _ => panic!("Expected text message"),
        }
    }

    #[tokio::test]
    async fn test_send_to_connection() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let distributor = WebSocketMessageDistributor::new(connection_manager.clone());

        // 创建测试连接
        let user_id = Uuid::new_v4();
        let session = WsSession::new(user_id, "test_user".to_string(), SessionType::Chat);
        let connection_id = session.id;

        let (sender, _receiver) = mpsc::unbounded_channel();

        // 添加连接
        connection_manager
            .add_connection_with_sender(session, sender)
            .await
            .unwrap();

        // 发送消息
        let message = WsMessage::new_text("Test message".to_string(), None);
        let result = distributor
            .send_to_connection(&connection_id, message)
            .await;

        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_send_to_user() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let distributor = WebSocketMessageDistributor::new(connection_manager.clone());

        // 创建测试连接
        let user_id = Uuid::new_v4();
        let session = WsSession::new(user_id, "test_user".to_string(), SessionType::Chat);

        let (sender, _receiver) = mpsc::unbounded_channel();

        // 添加连接
        connection_manager
            .add_connection_with_sender(session, sender)
            .await
            .unwrap();

        // 发送消息到用户
        let message = WsMessage::new_text("User message".to_string(), None);
        let result = distributor.send_to_user(&user_id, message).await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 1);
    }

    #[tokio::test]
    async fn test_broadcast_to_all() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let distributor = WebSocketMessageDistributor::new(connection_manager.clone());

        // 创建多个测试连接，保持接收端活跃
        let mut _receivers = Vec::new();
        for i in 0..3 {
            let user_id = Uuid::new_v4();
            let mut session = WsSession::new(user_id, format!("test_user_{i}"), SessionType::Chat);
            session.mark_connected(); // 标记为已连接

            let (sender, receiver) = mpsc::unbounded_channel();
            _receivers.push(receiver); // 保持接收端活跃

            connection_manager
                .add_connection_with_sender(session, sender)
                .await
                .unwrap();
        }

        // 广播消息
        let message = WsMessage::new_text("Broadcast message".to_string(), None);
        let result = distributor.broadcast_to_all(message, false).await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 3);
    }

    #[tokio::test]
    async fn test_broadcast_to_session_type() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let distributor = WebSocketMessageDistributor::new(connection_manager.clone());

        // 创建不同类型的连接
        let user_id1 = Uuid::new_v4();
        let mut session1 = WsSession::new(user_id1, "chat_user".to_string(), SessionType::Chat);
        session1.mark_connected(); // 标记为已连接
        let (sender1, receiver1) = mpsc::unbounded_channel();
        connection_manager
            .add_connection_with_sender(session1, sender1)
            .await
            .unwrap();

        let user_id2 = Uuid::new_v4();
        let mut session2 = WsSession::new(user_id2, "admin_user".to_string(), SessionType::Admin);
        session2.mark_connected(); // 标记为已连接
        let (sender2, receiver2) = mpsc::unbounded_channel();
        connection_manager
            .add_connection_with_sender(session2, sender2)
            .await
            .unwrap();

        // 保持接收端活跃
        let _receivers = [receiver1, receiver2];

        // 广播到Chat类型
        let message = WsMessage::new_text("Chat message".to_string(), None);
        let result = distributor
            .broadcast_to_session_type(&SessionType::Chat, message)
            .await;

        assert!(result.is_ok());
        assert_eq!(result.unwrap(), 1);
    }
}
