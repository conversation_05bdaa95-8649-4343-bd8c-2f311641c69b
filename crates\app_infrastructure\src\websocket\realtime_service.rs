//! # WebSocket实时消息服务实现
//!
//! 提供WebSocket实时消息的广播和分发功能

use app_domain::websocket::*;
use async_trait::async_trait;
use axum::extract::ws::Message;
use serde_json;
use std::sync::Arc;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

use super::connection_manager::WebSocketConnectionManager;

/// WebSocket实时消息服务实现
///
/// 【功能】: 实现WebSocket实时消息的广播和分发
pub struct WebSocketRealtimeServiceImpl {
    /// 连接管理器
    connection_manager: Arc<WebSocketConnectionManager>,
}

impl WebSocketRealtimeServiceImpl {
    /// 创建新的实时消息服务
    pub fn new(connection_manager: Arc<WebSocketConnectionManager>) -> Self {
        Self { connection_manager }
    }

    /// 将实时消息转换为WebSocket消息
    fn convert_to_ws_message(&self, realtime_message: &RealtimeMessage) -> Message {
        match realtime_message.to_json_string() {
            Ok(json_str) => Message::Text(json_str.into()),
            Err(e) => {
                error!("转换实时消息为JSON失败: {}", e);
                Message::Text(
                    format!(
                        r#"{{"type":"error","message":"消息序列化失败","timestamp":"{}"}}"#,
                        chrono::Utc::now().to_rfc3339()
                    )
                    .into(),
                )
            }
        }
    }

    /// 过滤符合条件的连接
    async fn filter_connections_for_message(&self, message: &RealtimeMessage) -> Vec<WsSession> {
        let all_connections = self.connection_manager.get_active_connections().await;

        all_connections
            .into_iter()
            .filter(|session| {
                // 检查会话类型匹配
                if !message.should_send_to_session_type(&session.session_type) {
                    return false;
                }

                // 检查用户匹配
                if !message.should_send_to_user(&session.user_id) {
                    return false;
                }

                // 只发送给活跃连接
                session.is_active()
            })
            .collect()
    }
}

#[async_trait]
impl WebSocketRealtimeService for WebSocketRealtimeServiceImpl {
    async fn broadcast_realtime_message(&self, message: RealtimeMessage) -> Result<usize, String> {
        debug!("广播实时消息: 类型={:?}", message.message_type);

        let target_connections = self.filter_connections_for_message(&message).await;
        let ws_message = self.convert_to_ws_message(&message);

        let mut sent_count = 0;
        for session in target_connections {
            match self
                .connection_manager
                .send_to_connection(&session.id, ws_message.clone())
                .await
            {
                Ok(()) => {
                    sent_count += 1;
                    debug!("实时消息已发送到连接: {}", session.id);
                }
                Err(e) => {
                    warn!("发送实时消息到连接 {} 失败: {}", session.id, e);
                }
            }
        }

        info!(
            "实时消息广播完成: 类型={:?}, 发送数量={}",
            message.message_type, sent_count
        );

        Ok(sent_count)
    }

    async fn send_realtime_to_user(
        &self,
        user_id: &Uuid,
        message: RealtimeMessage,
    ) -> Result<usize, String> {
        debug!(
            "发送实时消息给用户: 用户ID={}, 类型={:?}",
            user_id, message.message_type
        );

        let user_connections = self.connection_manager.get_user_connections(user_id).await;
        let ws_message = self.convert_to_ws_message(&message);

        let mut sent_count = 0;
        for session in user_connections {
            if !session.is_active() {
                continue;
            }

            // 检查会话类型匹配
            if !message.should_send_to_session_type(&session.session_type) {
                continue;
            }

            match self
                .connection_manager
                .send_to_connection(&session.id, ws_message.clone())
                .await
            {
                Ok(()) => {
                    sent_count += 1;
                    debug!("实时消息已发送到用户连接: {}", session.id);
                }
                Err(e) => {
                    warn!("发送实时消息到用户连接 {} 失败: {}", session.id, e);
                }
            }
        }

        info!(
            "用户实时消息发送完成: 用户ID={}, 类型={:?}, 发送数量={}",
            user_id, message.message_type, sent_count
        );

        Ok(sent_count)
    }

    async fn send_realtime_to_users(
        &self,
        user_ids: &[Uuid],
        message: RealtimeMessage,
    ) -> Result<usize, String> {
        debug!(
            "发送实时消息给多个用户: 用户数量={}, 类型={:?}",
            user_ids.len(),
            message.message_type
        );

        let mut total_sent = 0;
        for user_id in user_ids {
            match self.send_realtime_to_user(user_id, message.clone()).await {
                Ok(sent_count) => {
                    total_sent += sent_count;
                }
                Err(e) => {
                    warn!("发送实时消息到用户 {} 失败: {}", user_id, e);
                }
            }
        }

        info!(
            "多用户实时消息发送完成: 用户数量={}, 类型={:?}, 总发送数量={}",
            user_ids.len(),
            message.message_type,
            total_sent
        );

        Ok(total_sent)
    }

    async fn send_realtime_to_session_type(
        &self,
        session_type: &SessionType,
        message: RealtimeMessage,
    ) -> Result<usize, String> {
        debug!(
            "发送实时消息给会话类型: 会话类型={:?}, 消息类型={:?}",
            session_type, message.message_type
        );

        let all_connections = self.connection_manager.get_active_connections().await;
        let ws_message = self.convert_to_ws_message(&message);

        let mut sent_count = 0;
        for session in all_connections {
            if session.session_type != *session_type || !session.is_active() {
                continue;
            }

            // 检查用户匹配
            if !message.should_send_to_user(&session.user_id) {
                continue;
            }

            match self
                .connection_manager
                .send_to_connection(&session.id, ws_message.clone())
                .await
            {
                Ok(()) => {
                    sent_count += 1;
                    debug!("实时消息已发送到会话: {}", session.id);
                }
                Err(e) => {
                    warn!("发送实时消息到会话 {} 失败: {}", session.id, e);
                }
            }
        }

        info!(
            "会话类型实时消息发送完成: 会话类型={:?}, 消息类型={:?}, 发送数量={}",
            session_type, message.message_type, sent_count
        );

        Ok(sent_count)
    }

    async fn broadcast_task_created(
        &self,
        task_data: serde_json::Value,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String> {
        info!("广播任务创建消息: 发送者={:?}", sender_id);

        let message = RealtimeMessage::task_created(task_data, sender_id);
        self.broadcast_realtime_message(message).await
    }

    async fn broadcast_task_updated(
        &self,
        task_data: serde_json::Value,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String> {
        info!("广播任务更新消息: 发送者={:?}", sender_id);

        let message = RealtimeMessage::task_updated(task_data, sender_id);
        self.broadcast_realtime_message(message).await
    }

    async fn broadcast_task_deleted(
        &self,
        task_id: u64,
        sender_id: Option<Uuid>,
    ) -> Result<usize, String> {
        info!(
            "广播任务删除消息: 任务ID={}, 发送者={:?}",
            task_id, sender_id
        );

        let message = RealtimeMessage::task_deleted(task_id, sender_id);
        self.broadcast_realtime_message(message).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_realtime_service_creation() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let service = WebSocketRealtimeServiceImpl::new(connection_manager);

        // 测试服务创建成功
        assert!(true); // 如果能创建就说明成功
    }

    #[tokio::test]
    async fn test_convert_to_ws_message() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let service = WebSocketRealtimeServiceImpl::new(connection_manager);

        let task_data = serde_json::json!({
            "id": 1,
            "title": "测试任务",
            "status": "pending"
        });

        let message = RealtimeMessage::task_created(task_data, None);
        let ws_message = service.convert_to_ws_message(&message);

        match ws_message {
            Message::Text(text) => {
                assert!(text.contains("task_created"));
                assert!(text.contains("测试任务"));
            }
            _ => panic!("应该返回文本消息"),
        }
    }
}
