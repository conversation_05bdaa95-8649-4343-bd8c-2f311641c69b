//! # WebSocket 连接稳定性管理器
//!
//! 提供WebSocket连接的稳定性优化功能，包括心跳机制、重连逻辑、超时检测等

use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::{RwLock, mpsc};
use tokio::time::interval;
use tracing::{debug, error, info, warn};
use uuid::Uuid;

use super::connection_manager::WebSocketConnectionManager;
use app_domain::websocket::{ConnectionId, WebSocketConnectionService};

/// WebSocket连接稳定性配置
#[derive(Debug, Clone)]
pub struct StabilityConfig {
    /// 心跳间隔时间
    pub heartbeat_interval: Duration,
    /// 连接超时时间
    pub connection_timeout: Duration,
    /// 健康检查间隔
    pub health_check_interval: Duration,
    /// 最大重连尝试次数
    pub max_reconnect_attempts: u32,
    /// 基础重连延迟
    pub base_reconnect_delay: Duration,
    /// 最大重连延迟
    pub max_reconnect_delay: Duration,
    /// 是否启用指数退避
    pub enable_exponential_backoff: bool,
}

impl Default for StabilityConfig {
    fn default() -> Self {
        Self {
            heartbeat_interval: Duration::from_secs(30),
            connection_timeout: Duration::from_secs(60),
            health_check_interval: Duration::from_secs(10),
            max_reconnect_attempts: 5,
            base_reconnect_delay: Duration::from_millis(1000),
            max_reconnect_delay: Duration::from_secs(30),
            enable_exponential_backoff: true,
        }
    }
}

/// 连接重连状态
#[derive(Debug, Clone)]
pub struct ReconnectState {
    /// 连接ID
    pub connection_id: ConnectionId,
    /// 用户ID
    pub user_id: Uuid,
    /// 重连尝试次数
    pub attempt_count: u32,
    /// 最后尝试时间
    pub last_attempt: Instant,
    /// 下次重连时间
    pub next_attempt: Instant,
}

/// WebSocket连接稳定性管理器
pub struct WebSocketStabilityManager {
    /// 连接管理器
    connection_manager: Arc<WebSocketConnectionManager>,
    /// 稳定性配置
    config: StabilityConfig,
    /// 重连状态映射
    reconnect_states: Arc<RwLock<std::collections::HashMap<ConnectionId, ReconnectState>>>,
    /// 停止信号
    shutdown_tx: Option<mpsc::UnboundedSender<()>>,
}

impl WebSocketStabilityManager {
    /// 创建新的稳定性管理器
    pub fn new(
        connection_manager: Arc<WebSocketConnectionManager>,
        config: Option<StabilityConfig>,
    ) -> Self {
        Self {
            connection_manager,
            config: config.unwrap_or_default(),
            reconnect_states: Arc::new(RwLock::new(std::collections::HashMap::new())),
            shutdown_tx: None,
        }
    }

    /// 启动稳定性管理服务
    pub async fn start(&mut self) -> Result<(), String> {
        let (shutdown_tx, shutdown_rx) = mpsc::unbounded_channel();
        self.shutdown_tx = Some(shutdown_tx);

        // 创建多个接收器
        let (health_shutdown_tx, mut health_shutdown_rx) = mpsc::unbounded_channel();
        let (heartbeat_shutdown_tx, mut heartbeat_shutdown_rx) = mpsc::unbounded_channel();

        // 启动健康检查任务
        let health_check_manager = self.connection_manager.clone();
        let health_check_config = self.config.clone();
        let _health_check_task = tokio::spawn(async move {
            let mut interval = interval(health_check_config.health_check_interval);

            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        let removed_count = health_check_manager
                            .perform_health_check(health_check_config.connection_timeout)
                            .await;

                        if removed_count > 0 {
                            info!("健康检查完成: 移除了{}个非活跃连接", removed_count);
                        }
                    }
                    _ = health_shutdown_rx.recv() => {
                        info!("健康检查任务收到停止信号");
                        break;
                    }
                }
            }
        });

        // 启动心跳任务
        let heartbeat_manager = self.connection_manager.clone();
        let heartbeat_config = self.config.clone();
        let _heartbeat_task = tokio::spawn(async move {
            let mut interval = interval(heartbeat_config.heartbeat_interval);

            loop {
                tokio::select! {
                    _ = interval.tick() => {
                        Self::send_heartbeat_to_all(&heartbeat_manager).await;
                    }
                    _ = heartbeat_shutdown_rx.recv() => {
                        info!("心跳任务收到停止信号");
                        break;
                    }
                }
            }
        });

        // 启动主控制任务来分发停止信号
        tokio::spawn(async move {
            let mut shutdown_rx = shutdown_rx;
            if shutdown_rx.recv().await.is_some() {
                let _ = health_shutdown_tx.send(());
                let _ = heartbeat_shutdown_tx.send(());
            }
        });

        info!("WebSocket稳定性管理器已启动");
        Ok(())
    }

    /// 停止稳定性管理服务
    pub async fn stop(&mut self) {
        if let Some(shutdown_tx) = self.shutdown_tx.take() {
            let _ = shutdown_tx.send(());
            info!("WebSocket稳定性管理器已停止");
        }
    }

    /// 向所有连接发送心跳
    async fn send_heartbeat_to_all(manager: &Arc<WebSocketConnectionManager>) {
        let active_connections: Vec<_> = {
            use app_domain::websocket::WebSocketConnectionService;
            manager.get_active_connections().await
        };
        let mut heartbeat_count = 0;

        for session in active_connections {
            // 发送心跳ping消息
            let heartbeat_message = axum::extract::ws::Message::Ping(vec![].into());

            match manager
                .send_to_connection(&session.id, heartbeat_message)
                .await
            {
                Ok(()) => {
                    heartbeat_count += 1;
                    debug!("发送心跳到连接: {}", session.id);
                }
                Err(e) => {
                    warn!("发送心跳失败: 连接ID={}, 错误={}", session.id, e);
                }
            }
        }

        if heartbeat_count > 0 {
            debug!("心跳发送完成: 发送了{}个心跳", heartbeat_count);
        }
    }

    /// 添加重连状态
    pub async fn add_reconnect_state(&self, connection_id: ConnectionId, user_id: Uuid) {
        let state = ReconnectState {
            connection_id,
            user_id,
            attempt_count: 0,
            last_attempt: Instant::now(),
            next_attempt: Instant::now() + self.config.base_reconnect_delay,
        };

        let mut states = self.reconnect_states.write().await;
        states.insert(connection_id, state);

        info!("添加重连状态: 连接ID={}, 用户ID={}", connection_id, user_id);
    }

    /// 移除重连状态
    pub async fn remove_reconnect_state(&self, connection_id: &ConnectionId) {
        let mut states = self.reconnect_states.write().await;
        if states.remove(connection_id).is_some() {
            info!("移除重连状态: 连接ID={}", connection_id);
        }
    }

    /// 计算指数退避延迟
    pub fn calculate_exponential_backoff_delay(&self, attempt: u32) -> Duration {
        if !self.config.enable_exponential_backoff {
            return self.config.base_reconnect_delay;
        }

        let delay_ms =
            self.config.base_reconnect_delay.as_millis() * (2_u128).pow(attempt.saturating_sub(1));
        let capped_delay_ms = std::cmp::min(delay_ms, self.config.max_reconnect_delay.as_millis());

        // 添加随机抖动（±10%）
        let jitter_factor = 0.9 + rand::random::<f64>() * 0.2; // 0.9 到 1.1
        let final_delay_ms = ((capped_delay_ms as f64) * jitter_factor) as u64;

        Duration::from_millis(final_delay_ms)
    }

    /// 执行连接重试
    pub async fn attempt_reconnect(&self, connection_id: &ConnectionId) -> Result<(), String> {
        let mut states = self.reconnect_states.write().await;

        if let Some(state) = states.get_mut(connection_id) {
            if state.attempt_count >= self.config.max_reconnect_attempts {
                error!(
                    "连接重试次数已达上限: 连接ID={}, 尝试次数={}",
                    connection_id, state.attempt_count
                );
                return Err("重试次数已达上限".to_string());
            }

            state.attempt_count += 1;
            state.last_attempt = Instant::now();

            let delay = self.calculate_exponential_backoff_delay(state.attempt_count);
            state.next_attempt = Instant::now() + delay;

            info!(
                "执行连接重试: 连接ID={}, 尝试次数={}/{}, 下次重试延迟={:?}",
                connection_id, state.attempt_count, self.config.max_reconnect_attempts, delay
            );

            // 在实际实现中，这里会触发重新连接逻辑
            // 目前只是记录状态
            Ok(())
        } else {
            Err("重连状态不存在".to_string())
        }
    }

    /// 获取连接统计信息
    pub async fn get_stability_stats(&self) -> StabilityStats {
        let states = self.reconnect_states.read().await;
        let active_connections = {
            use app_domain::websocket::WebSocketConnectionService;
            self.connection_manager.get_active_connections().await
        };
        let healthy_connections = self
            .connection_manager
            .get_healthy_connections(self.config.connection_timeout)
            .await;

        StabilityStats {
            total_connections: active_connections.len(),
            healthy_connections: healthy_connections.len(),
            reconnecting_connections: states.len(),
            config: self.config.clone(),
        }
    }
}

/// 稳定性统计信息
#[derive(Debug, Clone)]
pub struct StabilityStats {
    /// 总连接数
    pub total_connections: usize,
    /// 健康连接数
    pub healthy_connections: usize,
    /// 重连中的连接数
    pub reconnecting_connections: usize,
    /// 配置信息
    pub config: StabilityConfig,
}
