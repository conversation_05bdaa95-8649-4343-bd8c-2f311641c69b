//! # WebSocket 统计收集器实现
//!
//! 提供WebSocket统计信息收集的具体实现

use app_domain::websocket::*;
use async_trait::async_trait;
use parking_lot::RwLock;
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};
use tracing::debug;

use super::connection_manager::WebSocketConnectionManager;

/// WebSocket统计收集器实现
///
/// 【功能】: 实现WebSocket连接和消息的统计收集
pub struct WebSocketStatsCollector {
    /// 连接管理器
    connection_manager: Arc<WebSocketConnectionManager>,
    /// 消息统计
    message_stats: Arc<MessageStatsData>,
    /// 连接事件统计
    connection_events: Arc<ConnectionEventStats>,
}

/// 消息统计数据
#[derive(Debug)]
struct MessageStatsData {
    /// 总发送消息数
    total_sent: AtomicU64,
    /// 总接收消息数
    total_received: AtomicU64,
    /// 按类型分组的消息数
    messages_by_type: RwLock<HashMap<MessageType, u64>>,
    /// 总传输字节数
    total_bytes: AtomicU64,
}

impl MessageStatsData {
    fn new() -> Self {
        Self {
            total_sent: AtomicU64::new(0),
            total_received: AtomicU64::new(0),
            messages_by_type: RwLock::new(HashMap::new()),
            total_bytes: AtomicU64::new(0),
        }
    }

    fn record_sent(&self, message_type: &MessageType, size_bytes: usize) {
        self.total_sent.fetch_add(1, Ordering::Relaxed);
        self.total_bytes
            .fetch_add(size_bytes as u64, Ordering::Relaxed);

        let mut by_type = self.messages_by_type.write();
        *by_type.entry(message_type.clone()).or_insert(0) += 1;
    }

    fn record_received(&self, message_type: &MessageType, size_bytes: usize) {
        self.total_received.fetch_add(1, Ordering::Relaxed);
        self.total_bytes
            .fetch_add(size_bytes as u64, Ordering::Relaxed);

        let mut by_type = self.messages_by_type.write();
        *by_type.entry(message_type.clone()).or_insert(0) += 1;
    }

    fn get_stats(&self) -> MessageStats {
        let total_sent = self.total_sent.load(Ordering::Relaxed);
        let total_received = self.total_received.load(Ordering::Relaxed);
        let total_bytes = self.total_bytes.load(Ordering::Relaxed);
        let messages_by_type = self.messages_by_type.read().clone();

        let total_messages = total_sent + total_received;
        let avg_message_size = if total_messages > 0 {
            (total_bytes as f64) / (total_messages as f64)
        } else {
            0.0
        };

        // 简单的成功率计算（实际应用中需要更复杂的逻辑）
        let message_success_rate = if total_sent > 0 {
            // 假设成功发送的消息数量，这里简化为100%成功率
            100.0
        } else {
            100.0
        };

        MessageStats {
            total_sent,
            total_received,
            messages_by_type,
            total_bytes,
            avg_message_size,
            message_success_rate,
        }
    }
}

/// 连接事件统计
#[derive(Debug)]
struct ConnectionEventStats {
    /// 连接事件计数
    events: RwLock<HashMap<ConnectionEventType, u64>>,
}

impl ConnectionEventStats {
    fn new() -> Self {
        Self {
            events: RwLock::new(HashMap::new()),
        }
    }

    fn record_event(&self, event_type: &ConnectionEventType) {
        let mut events = self.events.write();
        *events.entry(event_type.clone()).or_insert(0) += 1;
    }

    fn get_connection_success_rate(&self) -> f64 {
        let events = self.events.read();
        let connected = events.get(&ConnectionEventType::Connected).unwrap_or(&0);
        let errors = events.get(&ConnectionEventType::Error).unwrap_or(&0);

        let total_attempts = connected + errors;
        if total_attempts > 0 {
            ((*connected as f64) / (total_attempts as f64)) * 100.0
        } else {
            100.0
        }
    }
}

impl WebSocketStatsCollector {
    /// 创建新的统计收集器
    ///
    /// 【参数】:
    /// * `connection_manager` - 连接管理器
    pub fn new(connection_manager: Arc<WebSocketConnectionManager>) -> Self {
        Self {
            connection_manager,
            message_stats: Arc::new(MessageStatsData::new()),
            connection_events: Arc::new(ConnectionEventStats::new()),
        }
    }
}

#[async_trait]
impl WebSocketStatsService for WebSocketStatsCollector {
    async fn get_connection_stats(&self) -> WebSocketStats {
        let active_connections = self.connection_manager.get_active_connections().await;
        let (active_count, total, _successful, _failed) = self.connection_manager.get_stats();

        // 统计唯一用户数
        let mut unique_users = std::collections::HashSet::new();
        let mut connections_by_type = HashMap::new();

        for session in &active_connections {
            unique_users.insert(session.user_id);
            *connections_by_type
                .entry(session.session_type.clone())
                .or_insert(0) += 1;
        }

        // 计算平均连接持续时间
        let avg_connection_duration = if !active_connections.is_empty() {
            let total_duration: i64 = active_connections
                .iter()
                .map(|s| s.duration_seconds())
                .sum();
            (total_duration as f64) / (active_connections.len() as f64)
        } else {
            0.0
        };

        let connection_success_rate = self.connection_events.get_connection_success_rate();

        WebSocketStats {
            active_connections: active_count,
            total_connections: total,
            unique_users: unique_users.len(),
            connections_by_type,
            avg_connection_duration,
            connection_success_rate,
            failed_connections: _failed,
        }
    }

    async fn get_message_stats(&self) -> MessageStats {
        self.message_stats.get_stats()
    }

    async fn record_message_sent(&self, message_type: &MessageType, size_bytes: usize) {
        debug!(
            "记录消息发送: 类型={:?}, 大小={}字节",
            message_type, size_bytes
        );
        self.message_stats.record_sent(message_type, size_bytes);
    }

    async fn record_message_received(&self, message_type: &MessageType, size_bytes: usize) {
        debug!(
            "记录消息接收: 类型={:?}, 大小={}字节",
            message_type, size_bytes
        );
        self.message_stats.record_received(message_type, size_bytes);
    }

    async fn record_connection_event(&self, event_type: &ConnectionEventType) {
        debug!("记录连接事件: 类型={:?}", event_type);
        self.connection_events.record_event(event_type);
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_stats_collector_creation() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let stats_collector = WebSocketStatsCollector::new(connection_manager);

        // 测试初始统计
        let connection_stats = stats_collector.get_connection_stats().await;
        assert_eq!(connection_stats.active_connections, 0);
        assert_eq!(connection_stats.total_connections, 0);
        assert_eq!(connection_stats.unique_users, 0);

        let message_stats = stats_collector.get_message_stats().await;
        assert_eq!(message_stats.total_sent, 0);
        assert_eq!(message_stats.total_received, 0);
        assert_eq!(message_stats.total_bytes, 0);
    }

    #[tokio::test]
    async fn test_message_stats_recording() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let stats_collector = WebSocketStatsCollector::new(connection_manager);

        // 记录消息发送
        stats_collector
            .record_message_sent(&MessageType::Text, 100)
            .await;
        stats_collector
            .record_message_sent(&MessageType::Binary, 200)
            .await;

        // 记录消息接收
        stats_collector
            .record_message_received(&MessageType::Text, 150)
            .await;

        // 检查统计
        let message_stats = stats_collector.get_message_stats().await;
        assert_eq!(message_stats.total_sent, 2);
        assert_eq!(message_stats.total_received, 1);
        assert_eq!(message_stats.total_bytes, 450);
        assert_eq!(message_stats.avg_message_size, 150.0);

        // 检查按类型统计
        assert_eq!(
            message_stats.messages_by_type.get(&MessageType::Text),
            Some(&2)
        );
        assert_eq!(
            message_stats.messages_by_type.get(&MessageType::Binary),
            Some(&1)
        );
    }

    #[tokio::test]
    async fn test_connection_event_recording() {
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let stats_collector = WebSocketStatsCollector::new(connection_manager);

        // 记录连接事件
        stats_collector
            .record_connection_event(&ConnectionEventType::Connected)
            .await;
        stats_collector
            .record_connection_event(&ConnectionEventType::Connected)
            .await;
        stats_collector
            .record_connection_event(&ConnectionEventType::Error)
            .await;

        // 检查成功率
        let success_rate = stats_collector
            .connection_events
            .get_connection_success_rate();
        assert!((success_rate - 66.67).abs() < 0.1); // 2/3 ≈ 66.67%
    }

    #[test]
    fn test_message_stats_data() {
        let stats_data = MessageStatsData::new();

        // 记录一些消息
        stats_data.record_sent(&MessageType::Text, 100);
        stats_data.record_sent(&MessageType::Binary, 200);
        stats_data.record_received(&MessageType::Text, 150);

        let stats = stats_data.get_stats();
        assert_eq!(stats.total_sent, 2);
        assert_eq!(stats.total_received, 1);
        assert_eq!(stats.total_bytes, 450);
        assert_eq!(stats.avg_message_size, 150.0);
    }
}
