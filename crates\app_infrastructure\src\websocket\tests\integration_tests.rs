//! # WebSocket 集成测试
//!
//! 测试WebSocket连接管理、消息广播等核心功能

use super::super::*;
use app_domain::websocket::*;
use axum::extract::ws::Message;
use std::sync::Arc;
use tokio::sync::mpsc;
use tokio::time::{Duration, timeout};
use uuid::Uuid;

/// 测试WebSocket连接管理和消息广播
#[tokio::test]
async fn test_websocket_message_broadcast() {
    // 创建连接管理器和消息分发器
    let connection_manager = Arc::new(WebSocketConnectionManager::new());
    let message_distributor = WebSocketMessageDistributor::new(connection_manager.clone());

    // 创建两个用户连接
    let user1_id = Uuid::new_v4();
    let user2_id = Uuid::new_v4();

    let mut session1 = WsSession::new(user1_id, "user1".to_string(), SessionType::Chat);
    let mut session2 = WsSession::new(user2_id, "user2".to_string(), SessionType::Chat);

    // 标记会话为已连接状态
    session1.mark_connected();
    session2.mark_connected();

    let connection1_id = session1.id;
    let _connection2_id = session2.id;

    // 创建消息通道
    let (sender1, mut receiver1) = mpsc::unbounded_channel::<Message>();
    let (sender2, mut receiver2) = mpsc::unbounded_channel::<Message>();

    // 添加连接到管理器
    connection_manager
        .add_connection_with_sender(session1, sender1)
        .await
        .expect("添加连接1失败");

    connection_manager
        .add_connection_with_sender(session2, sender2)
        .await
        .expect("添加连接2失败");

    // 验证连接已添加
    assert_eq!(connection_manager.get_stats().0, 2);

    // 创建测试消息
    let test_message = WsMessage::new_text("Hello from user1!".to_string(), Some(connection1_id));

    // 广播消息给所有连接（排除发送者）
    let sent_count = message_distributor
        .broadcast_to_all(test_message, true)
        .await // exclude_sender = true
        .expect("广播消息失败");

    // 验证消息发送计数
    assert_eq!(sent_count, 1); // 只发送给user2，排除了发送者user1

    // 验证user2收到消息
    let received_message = timeout(Duration::from_millis(100), receiver2.recv())
        .await
        .expect("超时等待消息")
        .expect("接收消息失败");

    match received_message {
        Message::Text(text) => {
            assert_eq!(text, "Hello from user1!");
        }
        _ => panic!("期望收到文本消息"),
    }

    // 验证user1没有收到消息（因为排除了发送者）
    let no_message = timeout(Duration::from_millis(50), receiver1.recv()).await;
    assert!(no_message.is_err(), "user1不应该收到自己发送的消息");
}

/// 测试多用户聊天场景
#[tokio::test]
async fn test_multi_user_chat_scenario() {
    let connection_manager = Arc::new(WebSocketConnectionManager::new());
    let message_distributor = WebSocketMessageDistributor::new(connection_manager.clone());

    // 创建3个用户
    let mut users = Vec::new();
    let mut receivers = Vec::new();

    for i in 0..3 {
        let user_id = Uuid::new_v4();
        let mut session = WsSession::new(user_id, format!("user{}", i + 1), SessionType::Chat);
        session.mark_connected(); // 标记为已连接状态

        let (sender, receiver) = mpsc::unbounded_channel::<Message>();

        connection_manager
            .add_connection_with_sender(session.clone(), sender)
            .await
            .unwrap_or_else(|_| panic!("添加用户{}连接失败", i + 1));

        users.push(session);
        receivers.push(receiver);
    }

    // 验证所有连接已建立
    assert_eq!(connection_manager.get_stats().0, 3);

    // 用户1发送消息
    let message_from_user1 = WsMessage::new_text("大家好！".to_string(), Some(users[0].id));

    let sent_count = message_distributor
        .broadcast_to_all(message_from_user1, true)
        .await
        .expect("广播消息失败");

    // 应该发送给其他2个用户
    assert_eq!(sent_count, 2);

    // 验证用户2和用户3都收到消息
    for i in 1..3 {
        let received = timeout(Duration::from_millis(100), receivers[i].recv())
            .await
            .unwrap_or_else(|_| panic!("用户{}接收消息超时", i + 1))
            .unwrap_or_else(|| panic!("用户{}接收消息失败", i + 1));

        match received {
            Message::Text(text) => {
                assert_eq!(text, "大家好！");
            }
            _ => panic!("期望收到文本消息"),
        }
    }

    // 验证用户1没有收到自己的消息
    let no_message = timeout(Duration::from_millis(50), receivers[0].recv()).await;
    assert!(no_message.is_err(), "用户1不应该收到自己发送的消息");
}

/// 测试连接断开后的清理
#[tokio::test]
async fn test_connection_cleanup() {
    let connection_manager = Arc::new(WebSocketConnectionManager::new());

    let user_id = Uuid::new_v4();
    let session = WsSession::new(user_id, "test_user".to_string(), SessionType::Chat);
    let connection_id = session.id;

    let (sender, _receiver) = mpsc::unbounded_channel::<Message>();

    // 添加连接
    connection_manager
        .add_connection_with_sender(session, sender)
        .await
        .expect("添加连接失败");

    // 验证连接存在
    assert_eq!(connection_manager.get_stats().0, 1);

    // 移除连接
    connection_manager
        .remove_connection(&connection_id)
        .await
        .expect("移除连接失败");

    // 验证连接已清理
    assert_eq!(connection_manager.get_stats().0, 0);
}

/// 测试会话类型过滤广播
#[tokio::test]
async fn test_session_type_filtering() {
    let connection_manager = Arc::new(WebSocketConnectionManager::new());
    let message_distributor = WebSocketMessageDistributor::new(connection_manager.clone());

    // 创建不同类型的连接
    let chat_user = Uuid::new_v4();
    let admin_user = Uuid::new_v4();

    let mut chat_session = WsSession::new(chat_user, "chat_user".to_string(), SessionType::Chat);
    let mut admin_session =
        WsSession::new(admin_user, "admin_user".to_string(), SessionType::Admin);

    // 标记会话为已连接状态
    chat_session.mark_connected();
    admin_session.mark_connected();

    let (chat_sender, mut chat_receiver) = mpsc::unbounded_channel::<Message>();
    let (admin_sender, mut admin_receiver) = mpsc::unbounded_channel::<Message>();

    connection_manager
        .add_connection_with_sender(chat_session, chat_sender)
        .await
        .expect("添加聊天连接失败");

    connection_manager
        .add_connection_with_sender(admin_session, admin_sender)
        .await
        .expect("添加管理员连接失败");

    // 只向Chat类型广播消息
    let chat_message = WsMessage::new_text("聊天消息".to_string(), None);
    let sent_count = message_distributor
        .broadcast_to_session_type(&SessionType::Chat, chat_message)
        .await
        .expect("广播到聊天会话失败");

    // 只有1个Chat连接应该收到消息
    assert_eq!(sent_count, 1);

    // 验证Chat用户收到消息
    let received = timeout(Duration::from_millis(100), chat_receiver.recv())
        .await
        .expect("聊天用户接收消息超时")
        .expect("聊天用户接收消息失败");

    match received {
        Message::Text(text) => {
            assert_eq!(text, "聊天消息");
        }
        _ => panic!("期望收到文本消息"),
    }

    // 验证Admin用户没有收到消息
    let no_message = timeout(Duration::from_millis(50), admin_receiver.recv()).await;
    assert!(no_message.is_err(), "管理员用户不应该收到聊天消息");
}
