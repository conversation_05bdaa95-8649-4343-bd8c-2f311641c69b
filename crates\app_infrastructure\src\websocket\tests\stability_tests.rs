//! # WebSocket 连接稳定性测试
//!
//! 测试WebSocket连接的稳定性优化功能，包括心跳机制、重连逻辑、超时检测等

use super::super::*;
use app_domain::websocket::*;
use std::sync::Arc;
use std::sync::atomic::{AtomicU32, Ordering};
use std::time::Duration;
use tokio::sync::mpsc;
use tokio::time::sleep;
use uuid::Uuid;

/// 测试WebSocket连接心跳机制
#[tokio::test]
async fn test_connection_heartbeat_mechanism() {
    let manager = WebSocketConnectionManager::new();
    let session = WsSession::new(Uuid::new_v4(), "test_user".to_string(), SessionType::Chat);

    let connection_id = session.id;
    let (tx, rx) = mpsc::unbounded_channel();

    // 添加连接
    manager
        .add_connection_with_sender(session, tx)
        .await
        .unwrap();

    // 测试心跳更新活跃状态
    manager
        .update_connection_activity(&connection_id)
        .await
        .unwrap();

    // 验证连接仍然活跃
    let connections = manager.get_active_connections().await;
    assert_eq!(connections.len(), 1);
    assert_eq!(connections[0].id, connection_id);
}

/// 测试连接超时检测
#[tokio::test]
async fn test_connection_timeout_detection() {
    let manager = WebSocketConnectionManager::new();
    let session = WsSession::new(Uuid::new_v4(), "test_user".to_string(), SessionType::Chat);

    let connection_id = session.id;
    let (tx, _rx) = mpsc::unbounded_channel::<axum::extract::ws::Message>();

    // 添加连接
    manager.add_connection(session).await.unwrap();

    // 等待一小段时间让连接变为非活跃状态
    sleep(Duration::from_millis(100)).await;

    // 检查非活跃连接（使用很短的超时时间）
    let inactive_connections = manager
        .get_inactive_connections(Duration::from_millis(50))
        .await;
    assert_eq!(inactive_connections.len(), 1);
    assert_eq!(inactive_connections[0], connection_id);
}

/// 测试连接失败时的清理机制
#[tokio::test]
async fn test_connection_cleanup_on_failure() {
    let manager = WebSocketConnectionManager::new();
    let session = WsSession::new(Uuid::new_v4(), "test_user".to_string(), SessionType::Chat);

    let connection_id = session.id;
    let user_id = session.user_id;
    let (tx, _rx) = mpsc::unbounded_channel::<axum::extract::ws::Message>();

    // 添加连接
    manager.add_connection(session).await.unwrap();

    // 验证连接已添加
    let connections = manager.get_active_connections().await;
    assert_eq!(connections.len(), 1);

    // 模拟连接失败，清理用户连接
    manager.cleanup_user_connections(&user_id).await;

    // 验证连接已被清理
    let connections_after_cleanup = manager.get_active_connections().await;
    assert_eq!(connections_after_cleanup.len(), 0);
}

/// 测试连接重试机制
#[tokio::test]
async fn test_connection_retry_mechanism() {
    let retry_count = Arc::new(AtomicU32::new(0));
    let max_retries = 3;

    // 模拟连接重试逻辑
    for attempt in 1..=max_retries {
        retry_count.store(attempt, Ordering::Relaxed);

        // 模拟连接失败后的重试延迟
        let delay = Duration::from_millis(100 * (attempt as u64));
        sleep(delay).await;

        // 在实际实现中，这里会尝试重新建立连接
        tracing::info!("连接重试尝试 {}/{}", attempt, max_retries);
    }

    assert_eq!(retry_count.load(Ordering::Relaxed), max_retries);
}

/// 测试指数退避延迟计算
#[tokio::test]
async fn test_exponential_backoff_delay() {
    let base_delay = Duration::from_millis(100);
    let max_delay = Duration::from_secs(30);

    for attempt in 1..=5 {
        let delay = calculate_exponential_backoff_delay(attempt, base_delay, max_delay);

        // 验证延迟时间随尝试次数增加
        let expected_min = base_delay.as_millis() * (2_u128).pow(attempt - 1);
        let expected_max = std::cmp::min(expected_min * 2, max_delay.as_millis());

        assert!(delay.as_millis() >= expected_min / 2); // 允许一些抖动
        assert!(delay.as_millis() <= expected_max);

        tracing::info!("尝试 {}: 延迟 {:?}", attempt, delay);
    }
}

/// 测试连接池管理
#[tokio::test]
async fn test_connection_pool_management() {
    let manager = WebSocketConnectionManager::new();
    let max_connections = 5;
    let mut connections = Vec::new();

    // 创建多个连接
    for i in 0..max_connections {
        let session = WsSession::new(Uuid::new_v4(), format!("user_{i}"), SessionType::Chat);

        let (tx, _rx) = mpsc::unbounded_channel::<axum::extract::ws::Message>();
        let connection_id = session.id;

        manager.add_connection(session).await.unwrap();
        connections.push(connection_id);
    }

    // 验证所有连接都已添加
    let active_connections = manager.get_active_connections().await;
    assert_eq!(active_connections.len(), max_connections);

    // 移除一半连接
    for i in 0..max_connections / 2 {
        manager.remove_connection(&connections[i]).await.unwrap();
    }

    // 验证连接数量正确
    let remaining_connections = manager.get_active_connections().await;
    assert_eq!(
        remaining_connections.len(),
        max_connections - max_connections / 2
    );
}

/// 测试并发连接管理
#[tokio::test]
async fn test_concurrent_connection_management() {
    let manager = Arc::new(WebSocketConnectionManager::new());
    let concurrent_tasks = 10;
    let mut handles = Vec::new();

    // 并发添加连接
    for i in 0..concurrent_tasks {
        let manager_clone = manager.clone();
        let handle = tokio::spawn(async move {
            let session = WsSession::new(
                Uuid::new_v4(),
                format!("concurrent_user_{i}"),
                SessionType::Chat,
            );

            manager_clone.add_connection(session).await
        });
        handles.push(handle);
    }

    // 等待所有任务完成
    for handle in handles {
        handle.await.unwrap().unwrap();
    }

    // 验证所有连接都已添加
    let connections = manager.get_active_connections().await;
    assert_eq!(connections.len(), concurrent_tasks);
}

/// 测试连接健康检查
#[tokio::test]
async fn test_connection_health_check() {
    let manager = WebSocketConnectionManager::new();
    let session = WsSession::new(
        Uuid::new_v4(),
        "health_check_user".to_string(),
        SessionType::Chat,
    );

    let connection_id = session.id;
    let (tx, _rx) = mpsc::unbounded_channel::<axum::extract::ws::Message>();

    // 添加连接
    manager.add_connection(session).await.unwrap();

    // 立即检查健康状态（应该是健康的）
    let healthy_connections = manager
        .get_healthy_connections(Duration::from_secs(1))
        .await;
    assert_eq!(healthy_connections.len(), 1);

    // 等待超过健康检查时间
    sleep(Duration::from_millis(1100)).await;

    // 再次检查（应该不健康了）
    let unhealthy_connections = manager
        .get_healthy_connections(Duration::from_secs(1))
        .await;
    assert_eq!(unhealthy_connections.len(), 0);
}

/// 计算指数退避延迟时间
fn calculate_exponential_backoff_delay(
    attempt: u32,
    base_delay: Duration,
    max_delay: Duration,
) -> Duration {
    let delay_ms = base_delay.as_millis() * (2_u128).pow(attempt.saturating_sub(1));
    let capped_delay_ms = std::cmp::min(delay_ms, max_delay.as_millis());

    // 添加随机抖动（±10%）
    let jitter_factor = 0.9 + rand::random::<f64>() * 0.2; // 0.9 到 1.1
    let final_delay_ms = ((capped_delay_ms as f64) * jitter_factor) as u64;

    Duration::from_millis(final_delay_ms)
}
