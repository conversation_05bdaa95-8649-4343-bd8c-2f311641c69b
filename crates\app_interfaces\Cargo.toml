[package]
name = "app_interfaces"
version = "0.1.0"
edition = "2024"
license = "MIT OR Apache-2.0"
authors = ["Rust学习者"]
description = "API契约层 - 定义所有对外暴露的数据结构"

[dependencies]
# 序列化与反序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 唯一ID生成
uuid = { workspace = true }

# 时间处理
chrono = { workspace = true }

# 数据验证 - 升级到0.20修复RUSTSEC-2024-0421 (idna漏洞)
validator = { workspace = true }

# 正则表达式
regex = { workspace = true }

# 错误处理
thiserror = { workspace = true }
anyhow = { workspace = true }




