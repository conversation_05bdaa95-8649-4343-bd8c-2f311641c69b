//! 认证相关的API数据传输对象

use serde::{Deserialize, Serialize};
use validator::Validate;

/// 用户注册请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct RegisterRequest {
    #[validate(length(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间"))]
    pub username: String,

    #[validate(length(min = 6, message = "密码长度至少6个字符"))]
    pub password: String,

    #[validate(must_match(other = "password", message = "两次输入的密码不一致"))]
    pub confirm_password: String,

    /// 邮箱地址（可选，学习项目中暂不使用）
    #[validate(email(message = "请输入有效的邮箱地址"))]
    #[serde(skip_serializing_if = "Option::is_none")]
    pub email: Option<String>,
}

/// 用户登录请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct LoginRequest {
    #[validate(length(min = 1, message = "用户名不能为空"))]
    pub username: String,

    #[validate(length(min = 1, message = "密码不能为空"))]
    pub password: String,
}

/// 认证响应
#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResponse {
    /// JWT访问令牌
    pub access_token: String,
    /// 令牌类型
    pub token_type: String,
    /// 令牌过期时间（秒）
    pub expires_in: i64,
    /// 用户信息
    pub user: UserInfo,
}

/// 完整认证响应（包含更多令牌信息）
#[derive(Debug, Serialize, Deserialize)]
pub struct FullAuthResponse {
    /// JWT访问令牌
    pub access_token: String,
    /// 令牌类型
    pub token_type: String,
    /// 令牌过期时间（秒）
    pub expires_in: i64,
    /// 用户信息
    pub user: UserInfo,
}

/// 用户信息（用于认证响应）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub id: uuid::Uuid,
    pub username: String,
    pub email: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// JWT令牌刷新请求
#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenRequest {
    pub refresh_token: String,
}

/// JWT令牌刷新响应
#[derive(Debug, Serialize, Deserialize)]
pub struct RefreshTokenResponse {
    pub access_token: String,
    pub refresh_token: String,
}

/// 密码重置请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct PasswordResetRequest {
    #[validate(email(message = "请输入有效的邮箱地址"))]
    pub email: String,
}

/// 密码重置确认请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct PasswordResetConfirmRequest {
    pub token: String,

    #[validate(length(min = 6, message = "密码长度至少6个字符"))]
    pub new_password: String,

    #[validate(must_match(other = "new_password", message = "两次输入的密码不一致"))]
    pub confirm_password: String,
}

/// 用户登录凭据（用于领域服务）
#[derive(Debug, Clone, Serialize, Deserialize, Validate)]
pub struct UserCredentials {
    /// 用户名
    #[validate(length(min = 1, message = "用户名不能为空"))]
    pub username: String,

    /// 密码
    #[validate(length(min = 1, message = "密码不能为空"))]
    pub password: String,
}

/// 认证结果（包含完整令牌信息）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthenticationResult {
    /// 访问令牌
    pub access_token: String,

    /// 刷新令牌
    pub refresh_token: String,

    /// 令牌类型
    pub token_type: String,

    /// 访问令牌过期时间（秒）
    pub expires_in: u64,

    /// 用户信息
    pub user: UserInfo,
}

/// JWT 声明结构体
///
/// 注意：此结构体必须与 app_common::utils::jwt_utils::Claims 保持完全一致
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct Claims {
    /// 用户 ID (Subject)
    pub sub: String,
    /// 用户名
    pub username: String,
    /// 令牌过期时间（Unix 时间戳）
    pub exp: i64,
    /// 令牌签发时间（Unix 时间戳）
    pub iat: i64,
}
