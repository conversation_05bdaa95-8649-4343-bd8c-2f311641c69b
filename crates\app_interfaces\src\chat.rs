//! 聊天管理相关的API数据传输对象

use serde::{Deserialize, Serialize};
use validator::Validate;

/// 聊天室状态枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum ChatRoomStatus {
    #[serde(rename = "active")]
    Active,
    #[serde(rename = "inactive")]
    Inactive,
    #[serde(rename = "archived")]
    Archived,
}

/// 聊天室类型枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum ChatRoomType {
    #[serde(rename = "public")]
    Public,
    #[serde(rename = "private")]
    Private,
    #[serde(rename = "group")]
    Group,
    #[serde(rename = "direct")]
    Direct,
}

/// 消息类型枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum MessageType {
    #[serde(rename = "text")]
    Text,
    #[serde(rename = "image")]
    Image,
    #[serde(rename = "file")]
    File,
    #[serde(rename = "system")]
    System,
    #[serde(rename = "notification")]
    Notification,
}

/// 消息状态枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum MessageStatus {
    #[serde(rename = "sent")]
    Sent,
    #[serde(rename = "delivered")]
    Delivered,
    #[serde(rename = "read")]
    Read,
    #[serde(rename = "failed")]
    Failed,
}

/// 聊天室响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ChatRoomResponse {
    pub id: uuid::Uuid,
    pub name: String,
    pub description: Option<String>,
    pub room_type: ChatRoomType,
    pub status: ChatRoomStatus,
    pub is_private: bool,
    pub creator_id: uuid::Uuid,
    pub member_count: u32,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub last_message_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// 聊天室列表响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ChatRoomListResponse {
    pub rooms: Vec<ChatRoomResponse>,
    pub total: u64,
    pub page: u32,
    pub per_page: u32,
}

/// 聊天室成员响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ChatRoomMemberResponse {
    pub user_id: uuid::Uuid,
    pub username: String,
    pub role: ChatRoomMemberRole,
    pub joined_at: chrono::DateTime<chrono::Utc>,
    pub last_seen: Option<chrono::DateTime<chrono::Utc>>,
    pub is_online: bool,
}

/// 聊天室成员角色枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum ChatRoomMemberRole {
    #[serde(rename = "owner")]
    Owner,
    #[serde(rename = "admin")]
    Admin,
    #[serde(rename = "moderator")]
    Moderator,
    #[serde(rename = "member")]
    Member,
}

/// 聊天室成员管理请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct ManageChatRoomMemberRequest {
    pub user_ids: Vec<uuid::Uuid>,
    pub action: ChatRoomMemberAction,
    pub role: Option<ChatRoomMemberRole>,
}

/// 聊天室成员操作枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum ChatRoomMemberAction {
    #[serde(rename = "add")]
    Add,
    #[serde(rename = "remove")]
    Remove,
    #[serde(rename = "update_role")]
    UpdateRole,
    #[serde(rename = "mute")]
    Mute,
    #[serde(rename = "unmute")]
    Unmute,
}

/// 聊天室查询参数
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct ChatRoomQueryParams {
    #[validate(range(min = 1, message = "页码必须大于0"))]
    pub page: Option<u32>,

    #[validate(range(min = 1, max = 100, message = "每页数量必须在1-100之间"))]
    pub per_page: Option<u32>,

    pub room_type: Option<ChatRoomType>,

    pub status: Option<ChatRoomStatus>,

    pub search: Option<String>,

    pub is_member: Option<bool>,

    pub sort_by: Option<String>,

    pub sort_order: Option<String>,
}

/// 消息查询参数
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct MessageQueryParams {
    #[validate(range(min = 1, message = "页码必须大于0"))]
    pub page: Option<u32>,

    #[validate(range(min = 1, max = 100, message = "每页数量必须在1-100之间"))]
    pub per_page: Option<u32>,

    pub message_type: Option<MessageType>,

    pub sender_id: Option<uuid::Uuid>,

    pub search: Option<String>,

    pub before: Option<chrono::DateTime<chrono::Utc>>,

    pub after: Option<chrono::DateTime<chrono::Utc>>,

    pub sort_order: Option<String>,
}

/// 设备类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum DeviceType {
    Web,
    Mobile,
    Desktop,
    Tablet,
}

/// 用户会话响应
#[derive(Debug, Serialize, Deserialize)]
pub struct UserSessionResponse {
    pub id: uuid::Uuid,
    pub user_id: uuid::Uuid,
    pub username: String,
    pub room_id: uuid::Uuid,
    pub connected_at: chrono::DateTime<chrono::Utc>,
    pub last_activity: chrono::DateTime<chrono::Utc>,
    pub is_online: bool,
    pub device_type: Option<String>,
    pub ip_address: Option<String>,
}
