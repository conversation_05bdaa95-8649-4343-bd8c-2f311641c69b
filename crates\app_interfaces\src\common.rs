//! 通用的API数据传输对象

use serde::{Deserialize, Serialize};

/// 标准API响应包装器
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub error: Option<ApiError>,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            error: None,
        }
    }

    /// 创建成功响应（带消息）
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: Some(message),
            error: None,
        }
    }

    /// 创建错误响应
    pub fn error(error: ApiError) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(error),
        }
    }

    /// 创建错误响应（简化版）
    pub fn error_with_message(message: String) -> Self {
        Self {
            success: false,
            data: None,
            message: None,
            error: Some(ApiError {
                code: "GENERIC_ERROR".to_string(),
                message,
                details: None,
            }),
        }
    }
}

/// API错误信息
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiError {
    pub code: String,
    pub message: String,
    pub details: Option<serde_json::Value>,
}

/// 分页信息
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationInfo {
    pub page: u32,
    pub per_page: u32,
    pub total: u64,
    pub total_pages: u32,
    pub has_next: bool,
    pub has_prev: bool,
}

impl PaginationInfo {
    pub fn new(page: u32, per_page: u32, total: u64) -> Self {
        let total_pages = ((total as f64) / (per_page as f64)).ceil() as u32;
        Self {
            page,
            per_page,
            total,
            total_pages,
            has_next: page < total_pages,
            has_prev: page > 1,
        }
    }
}

/// 分页响应包装器
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub pagination: PaginationInfo,
}

impl<T> PaginatedResponse<T> {
    pub fn new(data: Vec<T>, page: u32, per_page: u32, total: u64) -> Self {
        Self {
            data,
            pagination: PaginationInfo::new(page, per_page, total),
        }
    }
}

/// 健康检查响应
#[derive(Debug, Serialize, Deserialize)]
pub struct HealthCheckResponse {
    pub status: HealthStatus,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub version: String,
    pub uptime: u64, // 运行时间（秒）
    pub checks: Vec<ComponentHealth>,
}

/// 健康状态枚举
#[derive(Debug, Serialize, Deserialize)]
pub enum HealthStatus {
    #[serde(rename = "healthy")]
    Healthy,
    #[serde(rename = "degraded")]
    Degraded,
    #[serde(rename = "unhealthy")]
    Unhealthy,
}

/// 组件健康状态
#[derive(Debug, Serialize, Deserialize)]
pub struct ComponentHealth {
    pub name: String,
    pub status: HealthStatus,
    pub message: Option<String>,
    pub response_time_ms: Option<u64>,
}

/// 系统指标响应
#[derive(Debug, Serialize, Deserialize)]
pub struct SystemMetricsResponse {
    pub cpu_usage: f64,
    pub memory_usage: f64,
    pub disk_usage: f64,
    pub active_connections: u32,
    pub request_count: u64,
    pub error_count: u64,
    pub average_response_time: f64,
}

/// 文件上传响应
#[derive(Debug, Serialize, Deserialize)]
pub struct FileUploadResponse {
    pub file_id: uuid::Uuid,
    pub filename: String,
    pub file_size: u64,
    pub content_type: String,
    pub url: String,
    pub uploaded_at: chrono::DateTime<chrono::Utc>,
}

/// 批量操作响应
#[derive(Debug, Serialize, Deserialize)]
pub struct BatchOperationResponse {
    pub total_count: u32,
    pub success_count: u32,
    pub failed_count: u32,
    pub errors: Vec<BatchOperationError>,
}

/// 批量操作错误
#[derive(Debug, Serialize, Deserialize)]
pub struct BatchOperationError {
    pub item_id: String,
    pub error_code: String,
    pub error_message: String,
}

/// 通用错误响应结构（来自server层）
#[derive(Debug, Serialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
}

/// 通用成功响应结构（来自server层）
#[derive(Debug, Serialize)]
pub struct SuccessResponse<T> {
    pub data: T,
    pub message: String,
}
