//! # 消息相关的DTO

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 消息过滤器
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct MessageFilter {
    /// 发送者ID
    pub sender_id: Option<Uuid>,
    /// 消息类型
    pub message_type: Option<String>,
    /// 消息状态
    pub status: Option<String>,
    /// 开始时间
    pub start_time: Option<DateTime<Utc>>,
    /// 结束时间
    pub end_time: Option<DateTime<Utc>>,
    /// 关键词搜索
    pub keyword: Option<String>,
    /// 是否置顶
    pub is_pinned: Option<bool>,
}

impl MessageFilter {
    /// 创建新的消息过滤器
    pub fn new() -> Self {
        Self::default()
    }

    /// 设置发送者ID过滤
    pub fn with_sender_id(mut self, sender_id: Uuid) -> Self {
        self.sender_id = Some(sender_id);
        self
    }

    /// 设置消息类型过滤
    pub fn with_message_type(mut self, message_type: String) -> Self {
        self.message_type = Some(message_type);
        self
    }

    /// 设置时间范围过滤
    pub fn with_time_range(mut self, start_time: DateTime<Utc>, end_time: DateTime<Utc>) -> Self {
        self.start_time = Some(start_time);
        self.end_time = Some(end_time);
        self
    }

    /// 设置关键词搜索
    pub fn with_keyword(mut self, keyword: String) -> Self {
        self.keyword = Some(keyword);
        self
    }
}
