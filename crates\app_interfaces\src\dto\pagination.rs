//! # 分页相关的DTO

use serde::{Deserialize, Serialize};

/// 分页参数
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct PaginationParams {
    /// 页码（从1开始）
    pub page: u32,
    /// 每页数量
    pub per_page: u32,
    /// 排序字段
    pub sort_by: Option<String>,
    /// 排序方向（asc/desc）
    pub sort_order: Option<String>,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            page: 1,
            per_page: 20,
            sort_by: None,
            sort_order: Some("desc".to_string()),
        }
    }
}

impl PaginationParams {
    /// 创建新的分页参数
    pub fn new(page: u32, per_page: u32) -> Self {
        Self {
            page,
            per_page,
            sort_by: None,
            sort_order: Some("desc".to_string()),
        }
    }

    /// 设置排序字段
    pub fn with_sort(mut self, sort_by: String, sort_order: String) -> Self {
        self.sort_by = Some(sort_by);
        self.sort_order = Some(sort_order);
        self
    }

    /// 计算偏移量
    pub fn offset(&self) -> u32 {
        (self.page - 1) * self.per_page
    }

    /// 获取限制数量
    pub fn limit(&self) -> u32 {
        self.per_page
    }
}
