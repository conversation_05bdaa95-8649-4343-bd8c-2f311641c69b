//! # 用户会话相关的DTO

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// 创建用户会话请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateUserSessionRequest {
    /// 用户ID
    pub user_id: Uuid,
    /// 设备类型
    pub device_type: String,
    /// 设备信息
    pub device_info: Option<String>,
    /// IP地址
    pub ip_address: Option<String>,
    /// 用户代理
    pub user_agent: Option<String>,
    /// 元数据
    pub metadata: Option<serde_json::Value>,
    /// 过期时间
    pub expires_at: Option<DateTime<Utc>>,
}

/// 更新用户会话请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateUserSessionRequest {
    /// 会话状态
    pub status: Option<String>,
    /// 当前聊天室ID
    pub current_chat_room_id: Option<Uuid>,
    /// 元数据
    pub metadata: Option<serde_json::Value>,
    /// 过期时间
    pub expires_at: Option<DateTime<Utc>>,
}
