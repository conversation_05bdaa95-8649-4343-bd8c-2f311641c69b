//! # API契约层 (app_interfaces)
//!
//! 本模块定义了所有对外暴露的API数据结构，包括：
//! - HTTP请求的Request Body
//! - HTTP响应的Response Body
//! - WebSocket通信消息结构
//! - API版本控制和向后兼容性保障
//!
//! ## 设计原则
//! - 单一事实来源：API契约的唯一权威定义
//! - 领域解耦：不依赖业务领域层，保持纯粹性
//! - 并行开发：支持前后端基于稳定契约并行开发
//! - 向后兼容：确保API升级不破坏现有客户端

pub mod auth;
pub mod chat;
pub mod common;
pub mod dto;
pub mod task;
pub mod user;
pub mod versioning;
pub mod websocket;

// 重新导出常用类型，简化外部调用路径
pub use auth::*;
pub use chat::*;
pub use common::*;
pub use dto::*;
pub use task::*;
pub use user::*;
pub use versioning::*;
pub use websocket::*;
