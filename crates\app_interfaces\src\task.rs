//! 任务管理相关的API数据传输对象

use serde::{Deserialize, Serialize};
use validator::Validate;

/// 任务状态枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum TaskStatus {
    #[serde(rename = "pending")]
    Pending,
    #[serde(rename = "in_progress")]
    InProgress,
    #[serde(rename = "completed")]
    Completed,
    #[serde(rename = "cancelled")]
    Cancelled,
}

/// 任务优先级枚举
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub enum TaskPriority {
    #[serde(rename = "low")]
    Low,
    #[serde(rename = "medium")]
    Medium,
    #[serde(rename = "high")]
    High,
    #[serde(rename = "urgent")]
    Urgent,
}

/// 任务响应
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskResponse {
    pub id: uuid::Uuid,
    pub title: String,
    pub description: Option<String>,
    pub status: TaskStatus,
    pub priority: TaskPriority,
    pub user_id: uuid::Uuid,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 兼容前端的完成状态字段
    pub completed: bool,
}

/// 简化的任务响应（来自领域层）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskDomainResponse {
    pub id: uuid::Uuid,
    pub title: String,
    pub description: Option<String>,
    pub completed: bool,
    pub user_id: Option<uuid::Uuid>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

/// 任务创建请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateTaskRequest {
    #[validate(length(min = 1, max = 200, message = "任务标题长度必须在1-200个字符之间"))]
    pub title: String,

    #[validate(length(max = 1000, message = "任务描述不能超过1000个字符"))]
    pub description: Option<String>,

    pub priority: Option<TaskPriority>,

    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
}

/// 简化的任务创建请求（来自领域层）
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateTaskDomainRequest {
    #[validate(length(min = 1, max = 200, message = "任务标题长度必须在1-200个字符之间"))]
    pub title: String,

    #[validate(length(max = 2000, message = "任务描述不能超过2000个字符"))]
    pub description: Option<String>,

    /// 任务所有者用户ID
    pub user_id: Option<uuid::Uuid>,
}

/// 任务更新请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UpdateTaskRequest {
    #[validate(length(min = 1, max = 200, message = "任务标题长度必须在1-200个字符之间"))]
    pub title: Option<String>,

    #[validate(length(max = 1000, message = "任务描述不能超过1000个字符"))]
    pub description: Option<String>,

    pub status: Option<TaskStatus>,

    pub priority: Option<TaskPriority>,

    pub due_date: Option<chrono::DateTime<chrono::Utc>>,
}

/// 简化的任务更新请求（来自领域层）
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct UpdateTaskDomainRequest {
    #[validate(length(min = 1, max = 200, message = "任务标题长度必须在1-200个字符之间"))]
    pub title: Option<String>,

    #[validate(length(max = 2000, message = "任务描述不能超过2000个字符"))]
    pub description: Option<String>,

    pub completed: Option<bool>,
}

/// 任务列表响应
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskListResponse {
    pub tasks: Vec<TaskResponse>,
    pub total: u64,
    pub page: u32,
    pub per_page: u32,
}

/// 任务查询参数
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct TaskQueryParams {
    #[validate(range(min = 1, message = "页码必须大于0"))]
    pub page: Option<u32>,

    #[validate(range(min = 1, max = 100, message = "每页数量必须在1-100之间"))]
    pub per_page: Option<u32>,

    pub status: Option<TaskStatus>,

    pub priority: Option<TaskPriority>,

    pub search: Option<String>,

    pub sort_by: Option<String>,

    pub sort_order: Option<String>,

    pub due_before: Option<chrono::DateTime<chrono::Utc>>,

    pub due_after: Option<chrono::DateTime<chrono::Utc>>,
}

/// 任务统计信息响应
#[derive(Debug, Serialize, Deserialize)]
pub struct TaskStatsResponse {
    pub total_tasks: u64,
    pub pending_tasks: u64,
    pub in_progress_tasks: u64,
    pub completed_tasks: u64,
    pub cancelled_tasks: u64,
    pub overdue_tasks: u64,
}

/// 批量任务操作请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct BatchTaskOperationRequest {
    #[validate(length(min = 1, message = "任务ID列表不能为空"))]
    pub task_ids: Vec<uuid::Uuid>,

    pub operation: BatchTaskOperation,
}

/// 批量任务操作类型
#[derive(Debug, Serialize, Deserialize)]
pub enum BatchTaskOperation {
    #[serde(rename = "delete")]
    Delete,
    #[serde(rename = "update_status")]
    UpdateStatus { status: TaskStatus },
    #[serde(rename = "update_priority")]
    UpdatePriority { priority: TaskPriority },
}

/// 批量任务操作响应
#[derive(Debug, Serialize, Deserialize)]
pub struct BatchTaskOperationResponse {
    pub success_count: u32,
    pub failed_count: u32,
    pub failed_task_ids: Vec<uuid::Uuid>,
}
