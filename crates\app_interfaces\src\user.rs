//! 用户管理相关的API数据传输对象

use regex::Regex;
use serde::{Deserialize, Serialize};
use std::sync::LazyLock;
use validator::Validate;

/// 用户名验证正则表达式
static USERNAME_REGEX: LazyLock<Regex> =
    LazyLock::new(|| Regex::new(r"^[a-zA-Z0-9_-]+$").expect("Invalid username regex"));

/// 用户响应
#[derive(Debug, Serialize, Deserialize)]
pub struct UserResponse {
    pub id: uuid::Uuid,
    pub username: String,
    pub email: String,
    /// 用户显示名称（昵称）
    pub display_name: Option<String>,
    /// 用户头像URL
    pub avatar_url: Option<String>,
    /// 用户个人简介
    pub bio: Option<String>,
    /// 用户位置信息
    pub location: Option<String>,
    /// 用户个人网站
    pub website: Option<String>,
    /// 最后登录时间
    pub last_login_at: Option<chrono::DateTime<chrono::Utc>>,
    /// 账户状态
    pub status: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
}

/// 用户列表响应
#[derive(Debug, Serialize, Deserialize)]
pub struct UserListResponse {
    pub users: Vec<UserResponse>,
    pub total: u64,
    pub page: u32,
    pub per_page: u32,
}

/// 用户更新请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UpdateUserRequest {
    #[validate(length(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间"))]
    pub username: Option<String>,

    #[validate(email(message = "请输入有效的邮箱地址"))]
    pub email: Option<String>,
}

/// 用户创建请求（管理员用）
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateUserRequest {
    #[validate(length(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间"))]
    pub username: String,

    #[validate(email(message = "请输入有效的邮箱地址"))]
    pub email: String,

    #[validate(length(min = 6, message = "密码长度至少6个字符"))]
    pub password: String,
}

/// 简化的用户创建请求（来自领域层）
#[derive(Debug, Clone, Deserialize, Validate)]
pub struct CreateUserDomainRequest {
    #[validate(length(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间"))]
    #[validate(regex(
        path = "*USERNAME_REGEX",
        message = "用户名只能包含字母、数字、下划线和连字符"
    ))]
    pub username: String,

    #[validate(length(min = 8, max = 128, message = "密码长度必须在8-128个字符之间"))]
    pub password: String,
}

/// 简化的用户响应（来自领域层）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserDomainResponse {
    pub id: uuid::Uuid,
    pub username: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// 用户查询参数
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UserQueryParams {
    #[validate(range(min = 1, message = "页码必须大于0"))]
    pub page: Option<u32>,

    #[validate(range(min = 1, max = 100, message = "每页数量必须在1-100之间"))]
    pub per_page: Option<u32>,

    pub search: Option<String>,

    pub sort_by: Option<String>,

    pub sort_order: Option<String>,
}

/// 用户密码修改请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct ChangePasswordRequest {
    #[validate(length(min = 1, message = "当前密码不能为空"))]
    pub current_password: String,

    #[validate(length(min = 6, message = "新密码长度至少6个字符"))]
    pub new_password: String,

    #[validate(must_match(other = "new_password", message = "两次输入的密码不一致"))]
    pub confirm_password: String,
}

/// 用户头像上传响应
#[derive(Debug, Serialize, Deserialize)]
pub struct AvatarUploadResponse {
    pub avatar_url: String,
}

/// 用户统计信息响应
#[derive(Debug, Serialize, Deserialize)]
pub struct UserStatsResponse {
    pub total_users: u64,
    pub active_users: u64,
    pub new_users_today: u64,
    pub new_users_this_week: u64,
    pub new_users_this_month: u64,
}
