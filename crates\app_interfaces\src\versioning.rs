//! # API版本控制模块
//!
//! 基于2025年最佳实践的API版本控制和向后兼容性保障系统
//!
//! ## 核心功能
//! - 语义化版本控制 (Semantic Versioning)
//! - 向后兼容性检查
//! - API变更跟踪
//! - 版本协商和路由
//! - 兼容性适配层
//!
//! ## 设计原则
//! - 明确版本要求：客户端必须明确指定API版本
//! - 渐进式升级：支持多版本并存，平滑迁移
//! - 破坏性变更控制：严格审批和文档化
//! - 自动化检测：CI/CD集成兼容性检查

use serde::{Deserialize, Serialize};
use std::fmt;
use std::str::FromStr;

/// API版本号结构体
///
/// 遵循语义化版本控制规范 (MAJOR.MINOR.PATCH)
/// - MAJOR: 不兼容的API变更
/// - MINOR: 向后兼容的功能新增
/// - PATCH: 向后兼容的问题修复
#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub struct ApiVersion {
    /// 主版本号：不兼容的API变更
    pub major: u32,
    /// 次版本号：向后兼容的功能新增
    pub minor: u32,
    /// 修订版本号：向后兼容的问题修复
    pub patch: u32,
}

impl ApiVersion {
    /// 创建新的API版本
    pub fn new(major: u32, minor: u32, patch: u32) -> Self {
        Self {
            major,
            minor,
            patch,
        }
    }

    /// 当前支持的最新API版本
    pub const CURRENT: ApiVersion = ApiVersion {
        major: 1,
        minor: 0,
        patch: 0,
    };

    /// 最小支持的API版本
    pub const MIN_SUPPORTED: ApiVersion = ApiVersion {
        major: 1,
        minor: 0,
        patch: 0,
    };

    /// 检查版本是否兼容
    pub fn is_compatible_with(&self, other: &ApiVersion) -> bool {
        // 主版本号必须相同
        if self.major != other.major {
            return false;
        }

        // 次版本号向后兼容
        self.minor >= other.minor
    }

    /// 检查是否为破坏性变更
    pub fn is_breaking_change_from(&self, other: &ApiVersion) -> bool {
        self.major > other.major
    }

    /// 检查版本是否仍受支持
    pub fn is_supported(&self) -> bool {
        *self >= Self::MIN_SUPPORTED && *self <= Self::CURRENT
    }
}

impl fmt::Display for ApiVersion {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}.{}.{}", self.major, self.minor, self.patch)
    }
}

impl FromStr for ApiVersion {
    type Err = ApiVersionError;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        let parts: Vec<&str> = s.split('.').collect();
        if parts.len() != 3 {
            return Err(ApiVersionError::InvalidFormat(s.to_string()));
        }

        let major = parts[0]
            .parse()
            .map_err(|_| ApiVersionError::InvalidFormat(s.to_string()))?;
        let minor = parts[1]
            .parse()
            .map_err(|_| ApiVersionError::InvalidFormat(s.to_string()))?;
        let patch = parts[2]
            .parse()
            .map_err(|_| ApiVersionError::InvalidFormat(s.to_string()))?;

        Ok(ApiVersion::new(major, minor, patch))
    }
}

/// API版本错误类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ApiVersionError {
    /// 版本格式无效
    InvalidFormat(String),
    /// 版本不受支持
    UnsupportedVersion(ApiVersion),
    /// 版本不兼容
    IncompatibleVersion {
        requested: ApiVersion,
        current: ApiVersion,
    },
    /// 缺少版本信息
    MissingVersion,
}

impl fmt::Display for ApiVersionError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            ApiVersionError::InvalidFormat(version) => {
                write!(f, "无效的版本格式: {version}")
            }
            ApiVersionError::UnsupportedVersion(version) => {
                write!(f, "不支持的版本: {version}")
            }
            ApiVersionError::IncompatibleVersion { requested, current } => {
                write!(
                    f,
                    "版本不兼容: 请求版本 {requested} 与当前版本 {current} 不兼容"
                )
            }
            ApiVersionError::MissingVersion => {
                write!(f, "缺少API版本信息")
            }
        }
    }
}

impl std::error::Error for ApiVersionError {}

/// API版本协商结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionNegotiation {
    /// 请求的版本
    pub requested_version: ApiVersion,
    /// 实际使用的版本
    pub resolved_version: ApiVersion,
    /// 是否需要适配层
    pub requires_adaptation: bool,
    /// 兼容性警告
    pub compatibility_warnings: Vec<String>,
}

/// API变更类型
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ChangeType {
    /// 添加新字段（向后兼容）
    AddField,
    /// 添加新端点（向后兼容）
    AddEndpoint,
    /// 修改字段类型（破坏性变更）
    ModifyFieldType,
    /// 删除字段（破坏性变更）
    RemoveField,
    /// 删除端点（破坏性变更）
    RemoveEndpoint,
    /// 修改端点行为（可能破坏性）
    ModifyEndpointBehavior,
    /// 修改错误响应格式（破坏性变更）
    ModifyErrorFormat,
}

impl ChangeType {
    /// 检查变更是否为破坏性变更
    pub fn is_breaking(&self) -> bool {
        matches!(
            self,
            ChangeType::ModifyFieldType
                | ChangeType::RemoveField
                | ChangeType::RemoveEndpoint
                | ChangeType::ModifyErrorFormat
        )
    }
}

/// API变更记录
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiChange {
    /// 变更类型
    pub change_type: ChangeType,
    /// 变更描述
    pub description: String,
    /// 影响的端点
    pub affected_endpoints: Vec<String>,
    /// 变更版本
    pub version: ApiVersion,
    /// 变更时间
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 迁移指南
    pub migration_guide: Option<String>,
}

/// 兼容性检查结果
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompatibilityCheck {
    /// 是否兼容
    pub is_compatible: bool,
    /// 破坏性变更列表
    pub breaking_changes: Vec<ApiChange>,
    /// 兼容性警告
    pub warnings: Vec<String>,
    /// 建议的迁移步骤
    pub migration_suggestions: Vec<String>,
}

/// 版本控制配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersioningConfig {
    /// 当前API版本
    pub current_version: ApiVersion,
    /// 支持的版本列表
    pub supported_versions: Vec<ApiVersion>,
    /// 是否强制要求版本头
    pub require_version_header: bool,
    /// 默认版本（当客户端未指定时使用）
    pub default_version: Option<ApiVersion>,
    /// 版本弃用警告阈值（天数）
    pub deprecation_warning_days: u32,
}

impl Default for VersioningConfig {
    fn default() -> Self {
        Self {
            current_version: ApiVersion::CURRENT,
            supported_versions: vec![ApiVersion::CURRENT],
            require_version_header: true,
            default_version: None,         // 强制要求客户端指定版本
            deprecation_warning_days: 180, // 6个月弃用警告期
        }
    }
}

/// 版本控制中间件状态
#[derive(Debug, Clone)]
pub struct VersioningState {
    /// 版本控制配置
    pub config: VersioningConfig,
    /// 变更历史记录
    pub change_history: Vec<ApiChange>,
}

impl VersioningState {
    /// 创建新的版本控制状态
    pub fn new(config: VersioningConfig) -> Self {
        Self {
            config,
            change_history: Vec::new(),
        }
    }

    /// 协商API版本
    pub fn negotiate_version(
        &self,
        requested: Option<&str>,
    ) -> Result<VersionNegotiation, ApiVersionError> {
        let requested_version = match requested {
            Some(version_str) => version_str.parse::<ApiVersion>()?,
            None => {
                if self.config.require_version_header {
                    return Err(ApiVersionError::MissingVersion);
                }
                self.config
                    .default_version
                    .clone()
                    .unwrap_or(self.config.current_version.clone())
            }
        };

        // 检查版本是否受支持
        if !requested_version.is_supported() {
            return Err(ApiVersionError::UnsupportedVersion(requested_version));
        }

        // 检查版本兼容性
        if !self
            .config
            .current_version
            .is_compatible_with(&requested_version)
        {
            return Err(ApiVersionError::IncompatibleVersion {
                requested: requested_version,
                current: self.config.current_version.clone(),
            });
        }

        let requires_adaptation = requested_version != self.config.current_version;
        let compatibility_warnings = self.generate_compatibility_warnings(&requested_version);

        Ok(VersionNegotiation {
            requested_version: requested_version.clone(),
            resolved_version: requested_version,
            requires_adaptation,
            compatibility_warnings,
        })
    }

    /// 生成兼容性警告
    fn generate_compatibility_warnings(&self, version: &ApiVersion) -> Vec<String> {
        let mut warnings = Vec::new();

        if *version < self.config.current_version {
            warnings.push(format!(
                "您正在使用较旧的API版本 {}，建议升级到最新版本 {}",
                version, self.config.current_version
            ));
        }

        // 检查是否有即将弃用的功能
        for change in &self.change_history {
            if change.version > *version && change.change_type.is_breaking() {
                warnings.push(format!(
                    "注意：版本 {} 中的破坏性变更：{}",
                    change.version, change.description
                ));
            }
        }

        warnings
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_version_parsing() {
        let version = "1.2.3".parse::<ApiVersion>().unwrap();
        assert_eq!(version.major, 1);
        assert_eq!(version.minor, 2);
        assert_eq!(version.patch, 3);
    }

    #[test]
    fn test_version_compatibility() {
        let v1_0_0 = ApiVersion::new(1, 0, 0);
        let v1_1_0 = ApiVersion::new(1, 1, 0);
        let v2_0_0 = ApiVersion::new(2, 0, 0);

        assert!(v1_1_0.is_compatible_with(&v1_0_0));
        assert!(!v1_0_0.is_compatible_with(&v1_1_0));
        assert!(!v2_0_0.is_compatible_with(&v1_0_0));
    }

    #[test]
    fn test_breaking_changes() {
        let v1_0_0 = ApiVersion::new(1, 0, 0);
        let v1_1_0 = ApiVersion::new(1, 1, 0);
        let v2_0_0 = ApiVersion::new(2, 0, 0);

        assert!(!v1_1_0.is_breaking_change_from(&v1_0_0));
        assert!(v2_0_0.is_breaking_change_from(&v1_0_0));
    }
}
