//! WebSocket通信相关的API数据传输对象

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use validator::Validate;

/// WebSocket消息类型
#[derive(Debug, Serialize, Deserialize, Clone)]
#[serde(tag = "type", content = "data")]
pub enum WebSocketMessage {
    /// 用户连接
    #[serde(rename = "user_connected")]
    UserConnected { user_id: uuid::Uuid },

    /// 用户断开连接
    #[serde(rename = "user_disconnected")]
    UserDisconnected { user_id: uuid::Uuid },

    /// 聊天消息
    #[serde(rename = "chat_message")]
    ChatMessage(ChatMessageData),

    /// 任务更新通知
    #[serde(rename = "task_updated")]
    TaskUpdated(TaskUpdateNotification),

    /// 系统通知
    #[serde(rename = "system_notification")]
    SystemNotification(SystemNotificationData),

    /// 心跳包
    #[serde(rename = "ping")]
    Ping,

    /// 心跳响应
    #[serde(rename = "pong")]
    Pong,

    /// 错误消息
    #[serde(rename = "error")]
    Error { message: String },

    /// 获取在线用户请求
    #[serde(rename = "get_users")]
    GetUsers,

    /// 在线用户列表响应
    #[serde(rename = "users_list")]
    UsersList { users: Vec<OnlineUserInfo> },
}

/// 在线用户信息
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct OnlineUserInfo {
    pub user_id: uuid::Uuid,
    pub username: String,
    pub connected_at: chrono::DateTime<chrono::Utc>,
    pub session_type: String,
}

/// 聊天消息数据
#[derive(Debug, Serialize, Deserialize, Clone, Validate)]
pub struct ChatMessageData {
    pub id: uuid::Uuid,

    #[validate(length(min = 1, max = 1000, message = "消息内容长度必须在1-1000个字符之间"))]
    pub content: String,

    pub sender_id: uuid::Uuid,
    pub sender_username: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub message_type: ChatMessageType,
}

/// 聊天消息类型
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum ChatMessageType {
    #[serde(rename = "text")]
    Text,
    #[serde(rename = "image")]
    Image,
    #[serde(rename = "file")]
    File,
    #[serde(rename = "system")]
    System,
}

/// 任务更新通知
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TaskUpdateNotification {
    pub task_id: uuid::Uuid,
    pub task_title: String,
    pub updated_by: uuid::Uuid,
    pub updated_by_username: String,
    pub update_type: TaskUpdateType,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// 任务更新类型
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum TaskUpdateType {
    #[serde(rename = "created")]
    Created,
    #[serde(rename = "updated")]
    Updated,
    #[serde(rename = "status_changed")]
    StatusChanged {
        old_status: super::task::TaskStatus,
        new_status: super::task::TaskStatus,
    },
    #[serde(rename = "deleted")]
    Deleted,
}

/// 系统通知数据
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct SystemNotificationData {
    pub id: uuid::Uuid,
    pub title: String,
    pub message: String,
    pub notification_type: SystemNotificationType,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub target_user_id: Option<uuid::Uuid>, // None表示广播给所有用户
}

/// 系统通知类型
#[derive(Debug, Serialize, Deserialize, Clone)]
pub enum SystemNotificationType {
    #[serde(rename = "info")]
    Info,
    #[serde(rename = "warning")]
    Warning,
    #[serde(rename = "error")]
    Error,
    #[serde(rename = "success")]
    Success,
}

/// WebSocket连接认证请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct WebSocketAuthRequest {
    #[validate(length(min = 1, message = "令牌不能为空"))]
    pub token: String,
}

/// WebSocket连接认证响应
#[derive(Debug, Serialize, Deserialize)]
pub struct WebSocketAuthResponse {
    pub success: bool,
    pub user_id: Option<uuid::Uuid>,
    pub message: String,
}

/// 在线用户列表响应
#[derive(Debug, Serialize, Deserialize)]
pub struct OnlineUsersResponse {
    pub users: Vec<OnlineUserInfo>,
    pub total_count: u32,
}

/// WebSocket连接请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketConnectionRequest {
    /// 用户ID
    pub user_id: Uuid,
    /// 用户名
    pub username: String,
    /// 会话类型
    pub session_type: String,
    /// 连接元数据
    pub metadata: Option<HashMap<String, String>>,
}

/// WebSocket消息请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketMessageRequest {
    /// 发送者连接ID
    pub sender_id: Option<String>,
    /// 接收者连接ID（None表示广播）
    pub receiver_id: Option<String>,
    /// 消息内容
    pub content: String,
    /// 消息优先级
    pub priority: Option<String>,
}

/// 发送消息请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendMessageRequest {
    /// 用户ID
    pub user_id: uuid::Uuid,
    /// 消息内容
    pub content: String,
    /// 消息优先级
    pub priority: Option<MessagePriority>,
}

/// 消息优先级
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, PartialOrd, Ord)]
pub enum MessagePriority {
    /// 低优先级
    Low,
    /// 普通优先级
    Normal,
    /// 高优先级
    High,
    /// 紧急优先级
    Critical,
}

impl std::fmt::Display for MessagePriority {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MessagePriority::Low => write!(f, "low"),
            MessagePriority::Normal => write!(f, "normal"),
            MessagePriority::High => write!(f, "high"),
            MessagePriority::Critical => write!(f, "critical"),
        }
    }
}

/// 广播请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BroadcastRequest {
    /// 消息内容
    pub content: String,
    /// 目标用户ID列表（None表示广播给所有用户）
    pub target_users: Option<Vec<Uuid>>,
    /// 目标会话类型（None表示所有类型）
    pub target_session_type: Option<String>,
    /// 是否排除发送者
    pub exclude_sender: bool,
    /// 发送者连接ID
    pub sender_id: Option<String>,
    /// 消息优先级
    pub priority: Option<String>,
}

/// WebSocket统计响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketStatsResponse {
    /// 连接统计
    pub connection_stats: WebSocketStats,
    /// 消息统计
    pub message_stats: MessageStats,
    /// 性能指标
    pub performance_metrics: PerformanceMetrics,
    /// 统计时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// WebSocket连接统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebSocketStats {
    /// 总连接数（历史累计）
    pub total_connections: u64,
    /// 当前活跃连接数
    pub active_connections: u64,
    /// 唯一活跃用户数
    pub unique_active_users: u64,
    /// 连接成功率（0.0-1.0）
    pub connection_success_rate: f64,
    /// 平均连接持续时间（秒）
    pub avg_connection_duration: f64,
    /// 失败连接数
    pub failed_connections: u64,
    /// 按会话类型分组的连接数
    pub connections_by_type: std::collections::HashMap<String, u64>,
}

/// WebSocket消息统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MessageStats {
    /// 总发送消息数
    pub total_sent: u64,
    /// 总接收消息数
    pub total_received: u64,
    /// 总消息数
    pub total_messages: u64,
    /// 消息吞吐量（消息/秒）
    pub message_throughput: f64,
    /// 平均消息大小（字节）
    pub avg_message_size: f64,
    /// 总传输字节数
    pub total_bytes: u64,
    /// 消息发送成功率（0.0-1.0）
    pub message_success_rate: f64,
    /// 按消息类型分组的统计
    pub messages_by_type: std::collections::HashMap<String, u64>,
}

/// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    /// 平均延迟（毫秒）
    pub avg_latency_ms: f64,
    /// P95延迟（毫秒）
    pub p95_latency_ms: f64,
    /// P99延迟（毫秒）
    pub p99_latency_ms: f64,
    /// CPU使用率（百分比）
    pub cpu_usage_percent: f64,
    /// 内存使用量（MB）
    pub memory_usage_mb: f64,
    /// 网络带宽使用（Mbps）
    pub network_bandwidth_mbps: f64,
    /// 错误率（0.0-1.0）
    pub error_rate: f64,
}
