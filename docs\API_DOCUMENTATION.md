# Axum 企业级后端 API 文档

## 📋 API 概述

本文档详细描述了基于 Axum 0.8.4 构建的企业级后端应用的所有 API 端点。所有 API 都遵循 RESTful 设计原则，使用 JSON 格式进行数据交换。

### 基础信息

- **基础URL**: `http://127.0.0.1:3000`
- **API版本**: v1
- **内容类型**: `application/json`
- **认证方式**: JWT Bearer Token
- **字符编码**: UTF-8

### 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {
    // 具体数据内容
  },
  "message": "操作成功"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  }
}
```

### HTTP 状态码

| 状态码 | 含义 | 说明 |
|--------|------|------|
| 200 | OK | 请求成功 |
| 201 | Created | 资源创建成功 |
| 204 | No Content | 请求成功，无返回内容 |
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证或认证失败 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 409 | Conflict | 资源冲突 |
| 422 | Unprocessable Entity | 数据验证失败 |
| 500 | Internal Server Error | 服务器内部错误 |

## 🔐 认证 API

### 用户注册

**端点**: `POST /api/auth/register`

**描述**: 注册新用户账户

**请求体**:
```json
{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "created_at": "2025-01-11T10:00:00Z"
    }
  },
  "message": "用户注册成功"
}
```

**验证规则**:
- `username`: 3-50字符，字母数字下划线
- `email`: 有效邮箱格式
- `password`: 最少8字符

### 用户登录

**端点**: `POST /api/auth/login`

**描述**: 用户登录获取JWT令牌

**请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>"
    },
    "expires_in": 3600
  },
  "message": "登录成功"
}
```

### 令牌刷新

**端点**: `POST /api/auth/refresh`

**描述**: 刷新JWT令牌

**请求头**:
```
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 3600
  },
  "message": "令牌刷新成功"
}
```

## 📋 任务管理 API

*所有任务API都需要Bearer Token认证*

### 获取任务列表

**端点**: `GET /api/tasks`

**描述**: 获取当前用户的所有任务

**请求头**:
```
Authorization: Bearer <token>
```

**查询参数**:
- `page`: 页码（默认1）
- `limit`: 每页数量（默认10，最大100）
- `status`: 任务状态过滤（pending/completed/cancelled）
- `search`: 搜索关键词

**响应**:
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "id": 1,
        "title": "完成项目文档",
        "description": "编写API文档和部署指南",
        "status": "pending",
        "priority": "high",
        "created_at": "2025-01-11T10:00:00Z",
        "updated_at": "2025-01-11T10:00:00Z",
        "due_date": "2025-01-15T18:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "total_pages": 3
    }
  }
}
```

### 创建任务

**端点**: `POST /api/tasks`

**描述**: 创建新任务

**请求体**:
```json
{
  "title": "新任务标题",
  "description": "任务详细描述",
  "priority": "medium",
  "due_date": "2025-01-15T18:00:00Z"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "task": {
      "id": 2,
      "title": "新任务标题",
      "description": "任务详细描述",
      "status": "pending",
      "priority": "medium",
      "created_at": "2025-01-11T10:30:00Z",
      "updated_at": "2025-01-11T10:30:00Z",
      "due_date": "2025-01-15T18:00:00Z"
    }
  },
  "message": "任务创建成功"
}
```

### 获取任务详情

**端点**: `GET /api/tasks/{id}`

**描述**: 获取指定任务的详细信息

**路径参数**:
- `id`: 任务ID

**响应**:
```json
{
  "success": true,
  "data": {
    "task": {
      "id": 1,
      "title": "完成项目文档",
      "description": "编写API文档和部署指南",
      "status": "pending",
      "priority": "high",
      "created_at": "2025-01-11T10:00:00Z",
      "updated_at": "2025-01-11T10:00:00Z",
      "due_date": "2025-01-15T18:00:00Z"
    }
  }
}
```

### 更新任务

**端点**: `PUT /api/tasks/{id}`

**描述**: 更新指定任务

**请求体**:
```json
{
  "title": "更新后的任务标题",
  "description": "更新后的任务描述",
  "status": "completed",
  "priority": "high"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "task": {
      "id": 1,
      "title": "更新后的任务标题",
      "description": "更新后的任务描述",
      "status": "completed",
      "priority": "high",
      "created_at": "2025-01-11T10:00:00Z",
      "updated_at": "2025-01-11T11:00:00Z",
      "due_date": "2025-01-15T18:00:00Z"
    }
  },
  "message": "任务更新成功"
}
```

### 删除任务

**端点**: `DELETE /api/tasks/{id}`

**描述**: 删除指定任务

**响应**:
```json
{
  "success": true,
  "message": "任务删除成功"
}
```

## 👥 用户管理 API

### 获取用户信息

**端点**: `GET /api/users/{id}`

**描述**: 获取指定用户的信息

**响应**:
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "created_at": "2025-01-11T10:00:00Z",
      "last_login": "2025-01-11T10:30:00Z",
      "is_online": true
    }
  }
}
```

### 获取在线用户列表

**端点**: `GET /api/users/online-users`

**描述**: 获取当前在线用户列表

**响应**:
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "username": "user1",
        "last_seen": "2025-01-11T10:30:00Z"
      },
      {
        "id": 2,
        "username": "user2",
        "last_seen": "2025-01-11T10:25:00Z"
      }
    ],
    "total": 2
  }
}
```

## 💬 聊天 API

### 获取聊天室列表

**端点**: `GET /api/chat/rooms`

**描述**: 获取用户可访问的聊天室列表

**响应**:
```json
{
  "success": true,
  "data": {
    "rooms": [
      {
        "id": 1,
        "name": "通用聊天室",
        "description": "所有用户都可以参与的聊天室",
        "member_count": 25,
        "created_at": "2025-01-11T09:00:00Z"
      }
    ]
  }
}
```

### 创建聊天室

**端点**: `POST /api/chat/rooms`

**描述**: 创建新的聊天室

**请求体**:
```json
{
  "name": "项目讨论室",
  "description": "项目相关讨论"
}
```

### 获取聊天消息

**端点**: `GET /api/chat/rooms/{room_id}/messages`

**描述**: 获取指定聊天室的消息历史

**查询参数**:
- `page`: 页码
- `limit`: 每页消息数量
- `before`: 获取指定时间之前的消息

**响应**:
```json
{
  "success": true,
  "data": {
    "messages": [
      {
        "id": 1,
        "room_id": 1,
        "user_id": 1,
        "username": "testuser",
        "content": "大家好！",
        "message_type": "text",
        "created_at": "2025-01-11T10:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 100
    }
  }
}
```

### 发送消息

**端点**: `POST /api/chat/rooms/{room_id}/messages`

**描述**: 在指定聊天室发送消息

**请求体**:
```json
{
  "content": "Hello, World!",
  "message_type": "text"
}
```

### 搜索消息

**端点**: `GET /api/messages/search`

**描述**: 搜索消息内容

**查询参数**:
- `q`: 搜索关键词
- `room_id`: 限制搜索的聊天室ID
- `limit`: 结果数量限制

## 🔌 WebSocket API

### 建立连接

**端点**: `GET /ws`

**描述**: 建立WebSocket连接进行实时通信

**认证方式**:
1. 查询参数: `ws://127.0.0.1:3000/ws?token=<jwt_token>`
2. 认证头: `Authorization: Bearer <jwt_token>`

### 消息格式

#### 客户端发送消息
```json
{
  "type": "chat_message",
  "room_id": 1,
  "content": "Hello, World!",
  "timestamp": "2025-01-11T10:00:00Z"
}
```

#### 服务器推送消息
```json
{
  "type": "chat_message",
  "data": {
    "id": 123,
    "room_id": 1,
    "user_id": 1,
    "username": "testuser",
    "content": "Hello, World!",
    "created_at": "2025-01-11T10:00:00Z"
  }
}
```

#### 系统消息类型
- `user_joined`: 用户加入聊天室
- `user_left`: 用户离开聊天室
- `typing`: 用户正在输入
- `error`: 错误消息

## 📊 监控 API

### 健康检查

**端点**: `GET /api/health`

**描述**: 基础健康检查

**响应**:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-11T10:00:00Z",
  "version": "1.0.0"
}
```

### 深度健康检查

**端点**: `GET /api/health/deep`

**描述**: 深度健康检查，包含系统诊断

**响应**:
```json
{
  "status": "healthy",
  "checks": {
    "database": "healthy",
    "cache": "healthy",
    "websocket": "healthy"
  },
  "metrics": {
    "uptime": 3600,
    "memory_usage": "45.2MB",
    "active_connections": 150
  }
}
```

### 性能指标

**端点**: `GET /api/performance/stats`

**描述**: 获取实时性能统计

**响应**:
```json
{
  "success": true,
  "data": {
    "requests_per_second": 125.5,
    "average_response_time": 45.2,
    "active_connections": 150,
    "memory_usage": 47185920,
    "cpu_usage": 15.5
  }
}
```

### Prometheus 指标

**端点**: `GET /metrics`

**描述**: 导出 Prometheus 格式的监控指标

**响应格式**: Prometheus 文本格式

## 🔧 错误处理

### 错误码定义

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| `VALIDATION_ERROR` | 422 | 数据验证失败 |
| `AUTHENTICATION_FAILED` | 401 | 认证失败 |
| `AUTHORIZATION_FAILED` | 403 | 权限不足 |
| `RESOURCE_NOT_FOUND` | 404 | 资源不存在 |
| `RESOURCE_CONFLICT` | 409 | 资源冲突 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `INTERNAL_SERVER_ERROR` | 500 | 服务器内部错误 |

### 错误响应示例

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "数据验证失败",
    "details": {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  }
}
```

## 📝 使用示例

### cURL 示例

```bash
# 用户注册
curl -X POST http://127.0.0.1:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'

# 用户登录
curl -X POST http://127.0.0.1:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# 获取任务列表（需要替换token）
curl -X GET http://127.0.0.1:3000/api/tasks \
  -H "Authorization: Bearer <your_jwt_token>"

# 创建任务
curl -X POST http://127.0.0.1:3000/api/tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <your_jwt_token>" \
  -d '{"title":"新任务","description":"任务描述","priority":"medium"}'
```

### JavaScript 示例

```javascript
// 用户登录
const loginResponse = await fetch('http://127.0.0.1:3000/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123'
  })
});

const loginData = await loginResponse.json();
const token = loginData.data.token;

// 获取任务列表
const tasksResponse = await fetch('http://127.0.0.1:3000/api/tasks', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const tasksData = await tasksResponse.json();
console.log(tasksData.data.tasks);
```

## 📚 相关文档

- [技术文档](./TECHNICAL_DOCUMENTATION.md)
- [部署指南](./DEPLOYMENT_GUIDE.md)
- [WebSocket协议文档](./WEBSOCKET_PROTOCOL.md)
- [错误码参考](./ERROR_CODES.md)
