# Axum 企业级后端项目部署指南

## 📋 部署概述

本指南详细说明了如何在不同环境中部署基于 Rust 2024 Edition 和 Axum 0.8.4 的企业级后端应用。项目支持多种部署方式，从本地开发到生产环境的容器化部署。

## 🏗️ 部署架构

### 混合部署架构（推荐开发环境）

```
┌─────────────────────────────────────────────────────────────┐
│                    Windows 10 本机                         │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Axum 后端应用                              │ │
│  │         (127.0.0.1:3000)                               │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    WSL2 容器环境                           │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   PostgreSQL    │  │   DragonflyDB   │                  │
│  │ (localhost:5432)│  │ (localhost:6379)│                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

### 生产环境架构

```
┌─────────────────────────────────────────────────────────────┐
│                    负载均衡器                               │
│                  (Nginx/HAProxy)                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Axum 应用集群                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   实例 1    │ │   实例 2    │ │   实例 N    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据层                                   │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   PostgreSQL    │  │   DragonflyDB   │                  │
│  │     主从集群    │  │     集群模式    │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 本地开发环境部署

### 1. 环境准备

#### 安装 Rust 工具链
```bash
# 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 更新到最新版本
rustup update

# 安装开发工具
cargo install cargo-watch cargo-tarpaulin cargo-audit cargo-outdated cargo-deny
```

#### 安装 WSL2（Windows 用户）
```powershell
# 启用 WSL2
wsl --install

# 安装 Ubuntu
wsl --install -d Ubuntu

# 设置默认版本
wsl --set-default-version 2
```

### 2. 数据库环境设置

#### 方式一：SQLite（快速开始）
```bash
# 无需额外安装，项目自带 SQLite 支持
# 数据库文件：task_manager.db
```

#### 方式二：PostgreSQL + DragonflyDB（完整环境）

**在 WSL2 中安装 Podman：**
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装 Podman
sudo apt install -y podman podman-compose

# 验证安装
podman --version
podman-compose --version
```

**启动数据库服务：**
```bash
# 进入项目目录
cd /mnt/c/path/to/axum-tutorial

# 启动数据库容器
podman-compose up -d

# 验证服务状态
podman-compose ps
```

### 3. 项目配置

#### 创建环境配置文件
```bash
# 复制环境配置模板
cp .env.example .env

# 编辑配置文件
nano .env
```

#### 环境变量配置示例
```env
# 服务器配置
HTTP_ADDR=127.0.0.1:3000
RUST_ENV=development

# 数据库配置（SQLite）
DATABASE_URL=sqlite://task_manager.db

# 数据库配置（PostgreSQL）
# DATABASE_URL=postgres://user:password@localhost:5432/axum_chat

# 缓存配置
REDIS_URL=redis://localhost:6379

# 认证配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-environment

# 日志配置
RUST_LOG=info,server=debug,app_application=debug,app_infrastructure=debug

# 性能配置
DATABASE_MAX_CONNECTIONS=10
DATABASE_MIN_CONNECTIONS=5
WEBSOCKET_MAX_CONNECTIONS=1000
```

### 4. 数据库初始化

```bash
# 运行数据库迁移
cargo run -p migration

# 验证数据库结构
cargo run -p migration -- status
```

### 5. 启动应用

```bash
# 开发模式启动
cargo run -p server

# 使用自动重载（推荐开发时使用）
cargo watch -x "run -p server"

# 详细日志模式
RUST_LOG=debug cargo run -p server
```

### 6. 验证部署

```bash
# 健康检查
curl http://127.0.0.1:3000/api/health

# 深度健康检查
curl http://127.0.0.1:3000/api/health/deep

# 性能指标
curl http://127.0.0.1:3000/metrics
```

## 🐳 容器化部署

### 1. Docker 部署

#### 创建 Dockerfile
```dockerfile
# 多阶段构建 Dockerfile
FROM rust:1.75-slim as builder

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制 Cargo 文件
COPY Cargo.toml Cargo.lock ./
COPY crates/ ./crates/
COPY server/ ./server/
COPY migration/ ./migration/

# 构建应用
RUN cargo build --release -p server

# 运行时镜像
FROM debian:bookworm-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -m -u 1000 appuser

# 复制构建产物
COPY --from=builder /app/target/release/server /usr/local/bin/server

# 设置权限
RUN chown appuser:appuser /usr/local/bin/server
USER appuser

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["server"]
```

#### 构建和运行
```bash
# 构建镜像
docker build -t axum-tutorial:latest .

# 运行容器
docker run -d \
  --name axum-tutorial \
  -p 3000:3000 \
  --env-file .env \
  axum-tutorial:latest

# 查看日志
docker logs -f axum-tutorial
```

### 2. Docker Compose 部署

#### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=************************************/axum_chat
      - REDIS_URL=redis://cache:6379
      - JWT_SECRET=${JWT_SECRET}
      - RUST_LOG=info
    depends_on:
      - db
      - cache
    restart: unless-stopped

  db:
    image: postgres:17
    environment:
      - POSTGRES_DB=axum_chat
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  cache:
    image: docker.dragonflydb.io/dragonflydb/dragonfly:latest
    ports:
      - "6379:6379"
    volumes:
      - dragonfly_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  dragonfly_data:
```

#### 启动完整环境
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app

# 停止服务
docker-compose down
```

## 🏭 生产环境部署

### 1. 系统要求

#### 硬件要求
- **CPU**: 最少 2 核，推荐 4 核以上
- **内存**: 最少 4GB，推荐 8GB 以上
- **存储**: 最少 20GB SSD，推荐 100GB 以上
- **网络**: 1Gbps 网络连接

#### 软件要求
- **操作系统**: Ubuntu 22.04 LTS 或 CentOS 8+
- **容器运行时**: Docker 24+ 或 Podman 4+
- **负载均衡器**: Nginx 1.20+ 或 HAProxy 2.4+
- **监控系统**: Prometheus + Grafana

### 2. 生产环境配置

#### 环境变量配置
```env
# 生产环境配置
RUST_ENV=production
HTTP_ADDR=0.0.0.0:3000

# 数据库配置
DATABASE_URL=*****************************************/database
DATABASE_MAX_CONNECTIONS=20
DATABASE_MIN_CONNECTIONS=5

# 缓存配置
REDIS_URL=redis://cache-host:6379

# 安全配置
JWT_SECRET=your-super-long-and-random-jwt-secret-key-for-production
JWT_EXPIRATION=3600

# 日志配置
RUST_LOG=info,server=warn

# 性能配置
WEBSOCKET_MAX_CONNECTIONS=10000
REQUEST_TIMEOUT=30
BODY_LIMIT=10485760
```

#### 编译优化配置
```toml
# Cargo.toml
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
```

### 3. 负载均衡配置

#### Nginx 配置
```nginx
# /etc/nginx/sites-available/axum-tutorial
upstream axum_backend {
    least_conn;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
    server 127.0.0.1:3003;
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name your-domain.com;

    # SSL 配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    # API 代理
    location /api/ {
        proxy_pass http://axum_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # WebSocket 代理
    location /ws {
        proxy_pass http://axum_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 特殊配置
        proxy_read_timeout 86400;
        proxy_send_timeout 86400;
    }

    # 静态文件
    location /static/ {
        root /var/www/axum-tutorial;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 健康检查
    location /health {
        proxy_pass http://axum_backend/api/health;
        access_log off;
    }
}
```

### 4. 监控配置

#### Prometheus 配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'axum-tutorial'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/metrics'
    scrape_interval: 5s
```

#### Grafana 仪表板
- **应用指标**: 请求率、响应时间、错误率
- **系统指标**: CPU、内存、磁盘、网络
- **数据库指标**: 连接数、查询时间、慢查询
- **WebSocket指标**: 连接数、消息率、延迟

### 5. 部署脚本

#### 自动化部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

echo "开始部署 Axum 企业级后端应用..."

# 1. 拉取最新代码
git pull origin main

# 2. 构建应用
echo "构建应用..."
cargo build --release -p server

# 3. 运行测试
echo "运行测试..."
cargo test --workspace --release

# 4. 备份当前版本
echo "备份当前版本..."
sudo systemctl stop axum-tutorial || true
cp /usr/local/bin/server /usr/local/bin/server.backup.$(date +%Y%m%d_%H%M%S)

# 5. 部署新版本
echo "部署新版本..."
sudo cp target/release/server /usr/local/bin/server
sudo chmod +x /usr/local/bin/server

# 6. 运行数据库迁移
echo "运行数据库迁移..."
cargo run -p migration

# 7. 启动服务
echo "启动服务..."
sudo systemctl start axum-tutorial
sudo systemctl enable axum-tutorial

# 8. 验证部署
echo "验证部署..."
sleep 5
curl -f http://localhost:3000/api/health || {
    echo "健康检查失败，回滚到备份版本..."
    sudo systemctl stop axum-tutorial
    sudo cp /usr/local/bin/server.backup.* /usr/local/bin/server
    sudo systemctl start axum-tutorial
    exit 1
}

echo "部署成功完成！"
```

#### Systemd 服务配置
```ini
# /etc/systemd/system/axum-tutorial.service
[Unit]
Description=Axum Tutorial Backend Service
After=network.target postgresql.service

[Service]
Type=simple
User=axum
Group=axum
WorkingDirectory=/opt/axum-tutorial
Environment=RUST_LOG=info
EnvironmentFile=/opt/axum-tutorial/.env
ExecStart=/usr/local/bin/server
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal

# 安全配置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/axum-tutorial

[Install]
WantedBy=multi-user.target
```

## 🔧 故障排除

### 常见问题

1. **端口占用**
```bash
# 检查端口占用
sudo netstat -tlnp | grep :3000
# 或
sudo ss -tlnp | grep :3000
```

2. **数据库连接失败**
```bash
# 检查数据库服务状态
sudo systemctl status postgresql
# 测试连接
psql -h localhost -U username -d database
```

3. **内存不足**
```bash
# 检查内存使用
free -h
# 检查应用内存使用
ps aux | grep server
```

4. **日志查看**
```bash
# 查看应用日志
sudo journalctl -u axum-tutorial -f
# 查看系统日志
sudo journalctl -xe
```

### 性能调优

1. **数据库连接池优化**
2. **WebSocket连接数限制**
3. **请求超时配置**
4. **缓存策略优化**
5. **负载均衡算法选择**

## 📊 监控和维护

### 关键指标监控
- **响应时间**: P50, P95, P99
- **吞吐量**: QPS, 并发连接数
- **错误率**: 4xx/5xx 错误统计
- **资源使用**: CPU, 内存, 磁盘, 网络

### 日常维护任务
- **日志轮转**: 防止日志文件过大
- **数据库备份**: 定期备份数据
- **安全更新**: 及时更新依赖和系统
- **性能监控**: 持续监控系统性能

## 🔐 安全最佳实践

1. **使用强随机JWT密钥**
2. **启用HTTPS和安全头**
3. **定期更新依赖包**
4. **限制网络访问**
5. **实施访问控制**
6. **定期安全审计**

## 📚 相关文档

- [技术文档](./TECHNICAL_DOCUMENTATION.md)
- [API文档](./API_DOCUMENTATION.md)
- [监控配置](./MONITORING_SETUP.md)
- [故障排除指南](./TROUBLESHOOTING.md)
