  # 错误码参考文档

## 📋 错误码概述

本文档详细列出了 Axum 企业级后端应用中所有可能出现的错误码、对应的HTTP状态码、错误描述和处理建议。

### 错误响应格式

所有API错误都遵循统一的响应格式：

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "用户友好的错误描述",
    "details": "详细的技术错误信息（可选）",
    "timestamp": "2025-01-11T10:00:00Z",
    "request_id": "req_123456789"
  }
}
```

## 🔐 认证相关错误 (1000-1999)

### 1001 - AUTHENTICATION_REQUIRED
- **HTTP状态码**: 401 Unauthorized
- **描述**: 请求需要认证但未提供认证信息
- **原因**: 缺少 Authorization 头或 token 参数
- **解决方案**: 在请求头中添加 `Authorization: Bearer <token>`

```json
{
  "success": false,
  "error": {
    "code": "AUTHENTICATION_REQUIRED",
    "message": "此操作需要用户认证",
    "details": "请在请求头中提供有效的JWT令牌"
  }
}
```

### 1002 - INVALID_TOKEN
- **HTTP状态码**: 401 Unauthorized
- **描述**: 提供的JWT令牌无效或格式错误
- **原因**: 令牌格式错误、签名无效或令牌损坏
- **解决方案**: 重新获取有效的JWT令牌

### 1003 - TOKEN_EXPIRED
- **HTTP状态码**: 401 Unauthorized
- **描述**: JWT令牌已过期
- **原因**: 令牌超过有效期限
- **解决方案**: 使用刷新令牌获取新的访问令牌

### 1004 - INVALID_CREDENTIALS
- **HTTP状态码**: 401 Unauthorized
- **描述**: 用户名或密码错误
- **原因**: 登录时提供的凭据不正确
- **解决方案**: 检查用户名和密码是否正确

### 1005 - ACCOUNT_LOCKED
- **HTTP状态码**: 423 Locked
- **描述**: 用户账户已被锁定
- **原因**: 多次登录失败或管理员锁定
- **解决方案**: 联系管理员解锁账户

### 1006 - ACCOUNT_DISABLED
- **HTTP状态码**: 403 Forbidden
- **描述**: 用户账户已被禁用
- **原因**: 账户被管理员禁用
- **解决方案**: 联系管理员启用账户

## 🚫 授权相关错误 (2000-2999)

### 2001 - INSUFFICIENT_PERMISSIONS
- **HTTP状态码**: 403 Forbidden
- **描述**: 用户权限不足，无法执行此操作
- **原因**: 用户角色或权限级别不够
- **解决方案**: 联系管理员获取相应权限

### 2002 - RESOURCE_ACCESS_DENIED
- **HTTP状态码**: 403 Forbidden
- **描述**: 无权访问指定资源
- **原因**: 尝试访问不属于当前用户的资源
- **解决方案**: 确认资源ID是否正确，或联系资源所有者

### 2003 - OPERATION_NOT_ALLOWED
- **HTTP状态码**: 403 Forbidden
- **描述**: 当前状态下不允许执行此操作
- **原因**: 资源状态不允许执行该操作
- **解决方案**: 检查资源状态，确保满足操作条件

## ✅ 数据验证错误 (3000-3999)

### 3001 - VALIDATION_FAILED
- **HTTP状态码**: 422 Unprocessable Entity
- **描述**: 请求数据验证失败
- **原因**: 输入数据不符合验证规则
- **解决方案**: 检查并修正输入数据

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_FAILED",
    "message": "数据验证失败",
    "details": {
      "field_errors": {
        "email": "邮箱格式不正确",
        "password": "密码长度至少8位"
      }
    }
  }
}
```

### 3002 - REQUIRED_FIELD_MISSING
- **HTTP状态码**: 422 Unprocessable Entity
- **描述**: 缺少必需字段
- **原因**: 请求中缺少必需的参数
- **解决方案**: 添加缺少的必需字段

### 3003 - INVALID_FORMAT
- **HTTP状态码**: 422 Unprocessable Entity
- **描述**: 字段格式不正确
- **原因**: 数据格式不符合要求（如邮箱、日期等）
- **解决方案**: 使用正确的数据格式

### 3004 - VALUE_OUT_OF_RANGE
- **HTTP状态码**: 422 Unprocessable Entity
- **描述**: 字段值超出允许范围
- **原因**: 数值、长度或大小超出限制
- **解决方案**: 调整值到允许范围内

### 3005 - DUPLICATE_VALUE
- **HTTP状态码**: 409 Conflict
- **描述**: 字段值重复，违反唯一性约束
- **原因**: 尝试创建已存在的唯一值（如邮箱、用户名）
- **解决方案**: 使用不同的值

## 🔍 资源相关错误 (4000-4999)

### 4001 - RESOURCE_NOT_FOUND
- **HTTP状态码**: 404 Not Found
- **描述**: 请求的资源不存在
- **原因**: 资源ID不存在或已被删除
- **解决方案**: 检查资源ID是否正确

### 4002 - RESOURCE_ALREADY_EXISTS
- **HTTP状态码**: 409 Conflict
- **描述**: 资源已存在，无法重复创建
- **原因**: 尝试创建已存在的资源
- **解决方案**: 使用不同的标识符或更新现有资源

### 4003 - RESOURCE_IN_USE
- **HTTP状态码**: 409 Conflict
- **描述**: 资源正在使用中，无法删除
- **原因**: 资源被其他实体引用
- **解决方案**: 先解除相关引用再删除

### 4004 - RESOURCE_LOCKED
- **HTTP状态码**: 423 Locked
- **描述**: 资源已被锁定，无法修改
- **原因**: 资源被其他操作锁定
- **解决方案**: 等待锁定释放或联系管理员

## 🌐 网络和系统错误 (5000-5999)

### 5001 - INTERNAL_SERVER_ERROR
- **HTTP状态码**: 500 Internal Server Error
- **描述**: 服务器内部错误
- **原因**: 服务器端发生未预期的错误
- **解决方案**: 稍后重试，如持续出现请联系技术支持

### 5002 - DATABASE_ERROR
- **HTTP状态码**: 500 Internal Server Error
- **描述**: 数据库操作失败
- **原因**: 数据库连接失败或查询错误
- **解决方案**: 稍后重试，如持续出现请联系技术支持

### 5003 - EXTERNAL_SERVICE_ERROR
- **HTTP状态码**: 502 Bad Gateway
- **描述**: 外部服务调用失败
- **原因**: 依赖的外部服务不可用
- **解决方案**: 稍后重试，检查外部服务状态

### 5004 - SERVICE_UNAVAILABLE
- **HTTP状态码**: 503 Service Unavailable
- **描述**: 服务暂时不可用
- **原因**: 服务器维护或过载
- **解决方案**: 稍后重试

### 5005 - TIMEOUT_ERROR
- **HTTP状态码**: 504 Gateway Timeout
- **描述**: 请求处理超时
- **原因**: 操作耗时过长
- **解决方案**: 稍后重试或优化请求

## 🚦 限流和配额错误 (6000-6999)

### 6001 - RATE_LIMIT_EXCEEDED
- **HTTP状态码**: 429 Too Many Requests
- **描述**: 请求频率超过限制
- **原因**: 在指定时间内发送了过多请求
- **解决方案**: 降低请求频率，等待限制重置

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "请求频率超过限制",
    "details": {
      "limit": 100,
      "window": "1分钟",
      "retry_after": 45
    }
  }
}
```

### 6002 - QUOTA_EXCEEDED
- **HTTP状态码**: 429 Too Many Requests
- **描述**: 用户配额已用完
- **原因**: 超出了分配的资源配额
- **解决方案**: 升级账户或等待配额重置

### 6003 - CONCURRENT_LIMIT_EXCEEDED
- **HTTP状态码**: 429 Too Many Requests
- **描述**: 并发连接数超过限制
- **原因**: 同时建立的连接数过多
- **解决方案**: 关闭一些连接后重试

## 📡 WebSocket 相关错误 (7000-7999)

### 7001 - WEBSOCKET_AUTH_FAILED
- **描述**: WebSocket连接认证失败
- **原因**: 未提供有效的认证令牌
- **解决方案**: 在连接URL中提供有效的token参数

### 7002 - WEBSOCKET_ROOM_NOT_FOUND
- **描述**: 聊天室不存在
- **原因**: 尝试加入不存在的聊天室
- **解决方案**: 检查聊天室ID是否正确

### 7003 - WEBSOCKET_MESSAGE_TOO_LARGE
- **描述**: WebSocket消息过大
- **原因**: 消息大小超过限制（64KB）
- **解决方案**: 减小消息大小或分割发送

### 7004 - WEBSOCKET_CONNECTION_LIMIT
- **描述**: WebSocket连接数超过限制
- **原因**: 用户建立的连接数过多
- **解决方案**: 关闭一些现有连接

## 📊 业务逻辑错误 (8000-8999)

### 8001 - TASK_STATUS_INVALID
- **HTTP状态码**: 422 Unprocessable Entity
- **描述**: 任务状态转换无效
- **原因**: 尝试进行不允许的状态转换
- **解决方案**: 检查任务当前状态和目标状态

### 8002 - CHAT_ROOM_FULL
- **HTTP状态码**: 409 Conflict
- **描述**: 聊天室已满
- **原因**: 聊天室成员数达到上限
- **解决方案**: 等待有成员离开或选择其他聊天室

### 8003 - MESSAGE_EDIT_TIMEOUT
- **HTTP状态码**: 422 Unprocessable Entity
- **描述**: 消息编辑时间已过
- **原因**: 超过了消息编辑的时间限制
- **解决方案**: 无法编辑，可以发送新消息

## 🔧 错误处理最佳实践

### 客户端错误处理

```javascript
async function handleApiCall(apiFunction) {
  try {
    const response = await apiFunction();
    return response.data;
  } catch (error) {
    if (error.response) {
      const errorData = error.response.data.error;
      
      switch (errorData.code) {
        case 'TOKEN_EXPIRED':
          // 自动刷新令牌
          await refreshToken();
          return handleApiCall(apiFunction);
          
        case 'RATE_LIMIT_EXCEEDED':
          // 等待后重试
          const retryAfter = errorData.details.retry_after || 60;
          await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
          return handleApiCall(apiFunction);
          
        case 'VALIDATION_FAILED':
          // 显示验证错误
          displayValidationErrors(errorData.details.field_errors);
          break;
          
        default:
          // 显示通用错误消息
          showErrorMessage(errorData.message);
      }
    }
    throw error;
  }
}
```

### 服务端错误记录

```rust
use tracing::{error, warn, info};

// 记录不同级别的错误
match error_code {
    "INTERNAL_SERVER_ERROR" | "DATABASE_ERROR" => {
        error!(
            error_code = %error_code,
            request_id = %request_id,
            user_id = ?user_id,
            "严重错误发生"
        );
    },
    "VALIDATION_FAILED" | "RESOURCE_NOT_FOUND" => {
        warn!(
            error_code = %error_code,
            request_id = %request_id,
            "客户端错误"
        );
    },
    _ => {
        info!(
            error_code = %error_code,
            request_id = %request_id,
            "一般错误"
        );
    }
}
```

## 📈 错误监控和告警

### Prometheus 错误指标

```rust
// 错误计数器
static ref ERROR_COUNTER: CounterVec = register_counter_vec!(
    "api_errors_total",
    "Total number of API errors",
    &["error_code", "endpoint"]
).unwrap();

// 记录错误
ERROR_COUNTER
    .with_label_values(&[error_code, endpoint])
    .inc();
```

### 告警规则

```yaml
# 高错误率告警
- alert: HighErrorRate
  expr: rate(api_errors_total[5m]) / rate(http_requests_total[5m]) > 0.05
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "API错误率过高"
    description: "错误率超过5%，当前值: {{ $value }}"

# 特定错误告警
- alert: DatabaseErrors
  expr: rate(api_errors_total{error_code="DATABASE_ERROR"}[5m]) > 0
  for: 1m
  labels:
    severity: critical
  annotations:
    summary: "数据库错误"
    description: "检测到数据库错误"
```

## 📚 相关文档

- [API文档](./API_DOCUMENTATION.md)
- [WebSocket协议文档](./WEBSOCKET_PROTOCOL.md)
- [技术文档](./TECHNICAL_DOCUMENTATION.md)
- [故障排除指南](./TROUBLESHOOTING.md)
