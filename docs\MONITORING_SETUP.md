# 监控系统配置指南

## 📋 监控概述

本文档详细说明了如何为 Axum 企业级后端应用配置完整的监控系统。监控系统包括指标收集、日志聚合、告警通知和可视化仪表板等组件。

### 监控架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Axum 应用实例                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   /metrics  │ │   日志输出  │ │  健康检查   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    监控数据收集层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ Prometheus  │ │   Loki      │ │   J<PERSON>ger    │           │
│  │  (指标)     │ │  (日志)     │ │  (链路)     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    可视化和告警层                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Grafana   │ │ AlertManager│ │   PagerDuty │           │
│  │  (仪表板)   │ │   (告警)    │ │   (通知)    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 Prometheus 配置

### 1. Prometheus 安装

#### Docker 方式
```yaml
# docker-compose.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./monitoring/rules:/etc/prometheus/rules
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped

volumes:
  prometheus_data:
```

### 2. Prometheus 配置文件

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # Axum 应用指标
  - job_name: 'axum-tutorial'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: '/metrics'
    scrape_interval: 5s
    scrape_timeout: 5s

  # Prometheus 自身指标
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Node Exporter 系统指标
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # PostgreSQL 指标
  - job_name: 'postgres-exporter'
    static_configs:
      - targets: ['postgres-exporter:9187']

  # DragonflyDB 指标
  - job_name: 'dragonfly-exporter'
    static_configs:
      - targets: ['dragonfly-exporter:9121']
```

### 3. 告警规则配置

```yaml
# monitoring/rules/axum-alerts.yml
groups:
  - name: axum-tutorial-alerts
    rules:
      # 应用可用性告警
      - alert: AxumServiceDown
        expr: up{job="axum-tutorial"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Axum 服务不可用"
          description: "Axum 服务已停止响应超过1分钟"

      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "HTTP 错误率过高"
          description: "5xx 错误率超过 10%，当前值: {{ $value }}"

      # 高响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过长"
          description: "95% 响应时间超过1秒，当前值: {{ $value }}s"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 500
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "内存使用超过500MB，当前值: {{ $value }}MB"

      # WebSocket 连接数告警
      - alert: HighWebSocketConnections
        expr: websocket_active_connections > 8000
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "WebSocket 连接数过高"
          description: "活跃 WebSocket 连接数超过8000，当前值: {{ $value }}"

      # 数据库连接池告警
      - alert: DatabaseConnectionPoolExhausted
        expr: database_connections_active / database_connections_max > 0.9
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "数据库连接池即将耗尽"
          description: "数据库连接池使用率超过90%，当前值: {{ $value }}"
```

## 📊 Grafana 配置

### 1. Grafana 安装

```yaml
# docker-compose.yml 中添加
  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    restart: unless-stopped

volumes:
  grafana_data:
```

### 2. 数据源配置

```yaml
# monitoring/grafana/provisioning/datasources/prometheus.yml
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
```

### 3. 仪表板配置

```yaml
# monitoring/grafana/provisioning/dashboards/dashboard.yml
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /var/lib/grafana/dashboards
```

### 4. 应用监控仪表板

```json
{
  "dashboard": {
    "id": null,
    "title": "Axum 企业级后端监控",
    "tags": ["axum", "rust", "backend"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "请求率 (QPS)",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total[1m])",
            "legendFormat": "QPS"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "reqps"
          }
        }
      },
      {
        "id": 2,
        "title": "响应时间分布",
        "type": "heatmap",
        "targets": [
          {
            "expr": "rate(http_request_duration_seconds_bucket[5m])",
            "legendFormat": "{{le}}"
          }
        ]
      },
      {
        "id": 3,
        "title": "错误率",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"4..|5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
            "legendFormat": "错误率"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "unit": "percent"
          }
        }
      },
      {
        "id": 4,
        "title": "WebSocket 连接数",
        "type": "graph",
        "targets": [
          {
            "expr": "websocket_active_connections",
            "legendFormat": "活跃连接"
          }
        ]
      },
      {
        "id": 5,
        "title": "数据库连接池",
        "type": "graph",
        "targets": [
          {
            "expr": "database_connections_active",
            "legendFormat": "活跃连接"
          },
          {
            "expr": "database_connections_idle",
            "legendFormat": "空闲连接"
          }
        ]
      },
      {
        "id": 6,
        "title": "内存使用",
        "type": "graph",
        "targets": [
          {
            "expr": "process_resident_memory_bytes / 1024 / 1024",
            "legendFormat": "内存使用 (MB)"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

## 🚨 AlertManager 配置

### 1. AlertManager 安装

```yaml
# docker-compose.yml 中添加
  alertmanager:
    image: prom/alertmanager:latest
    container_name: alertmanager
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - alertmanager_data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    restart: unless-stopped

volumes:
  alertmanager_data:
```

### 2. AlertManager 配置

```yaml
# monitoring/alertmanager.yml
global:
  smtp_smarthost: 'smtp.gmail.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-app-password'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
    - match:
        severity: critical
      receiver: 'critical-alerts'
    - match:
        severity: warning
      receiver: 'warning-alerts'

receivers:
  - name: 'web.hook'
    webhook_configs:
      - url: 'http://localhost:5001/webhook'

  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '🚨 严重告警: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          时间: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - api_url: 'YOUR_SLACK_WEBHOOK_URL'
        channel: '#alerts'
        title: '🚨 严重告警'
        text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '⚠️ 警告告警: {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          描述: {{ .Annotations.description }}
          时间: {{ .StartsAt }}
          {{ end }}

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
```

## 📝 日志聚合配置

### 1. Loki 安装

```yaml
# docker-compose.yml 中添加
  loki:
    image: grafana/loki:latest
    container_name: loki
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki-config.yml:/etc/loki/local-config.yaml
      - loki_data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    restart: unless-stopped

  promtail:
    image: grafana/promtail:latest
    container_name: promtail
    volumes:
      - ./monitoring/promtail-config.yml:/etc/promtail/config.yml
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
    command: -config.file=/etc/promtail/config.yml
    restart: unless-stopped

volumes:
  loki_data:
```

### 2. Loki 配置

```yaml
# monitoring/loki-config.yml
auth_enabled: false

server:
  http_listen_port: 3100

ingester:
  lifecycler:
    address: 127.0.0.1
    ring:
      kvstore:
        store: inmemory
      replication_factor: 1
    final_sleep: 0s
  chunk_idle_period: 1h
  max_chunk_age: 1h
  chunk_target_size: 1048576
  chunk_retain_period: 30s

schema_config:
  configs:
    - from: 2020-10-24
      store: boltdb-shipper
      object_store: filesystem
      schema: v11
      index:
        prefix: index_
        period: 24h

storage_config:
  boltdb_shipper:
    active_index_directory: /loki/boltdb-shipper-active
    cache_location: /loki/boltdb-shipper-cache
    shared_store: filesystem
  filesystem:
    directory: /loki/chunks

limits_config:
  enforce_metric_name: false
  reject_old_samples: true
  reject_old_samples_max_age: 168h

chunk_store_config:
  max_look_back_period: 0s

table_manager:
  retention_deletes_enabled: false
  retention_period: 0s
```

### 3. Promtail 配置

```yaml
# monitoring/promtail-config.yml
server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  - job_name: axum-logs
    static_configs:
      - targets:
          - localhost
        labels:
          job: axum-tutorial
          __path__: /var/log/axum-tutorial/*.log

  - job_name: docker-logs
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        regex: '/(.*)'
        target_label: 'container'
```

## 🔍 链路追踪配置

### 1. Jaeger 安装

```yaml
# docker-compose.yml 中添加
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    restart: unless-stopped
```

### 2. 应用中集成链路追踪

在 `Cargo.toml` 中添加依赖：

```toml
[dependencies]
opentelemetry = "0.20"
opentelemetry-jaeger = "0.19"
tracing-opentelemetry = "0.21"
```

在应用启动代码中配置：

```rust
use opentelemetry::global;
use opentelemetry_jaeger::new_agent_pipeline;
use tracing_opentelemetry::OpenTelemetryLayer;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

pub fn init_tracing() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化 Jaeger tracer
    let tracer = new_agent_pipeline()
        .with_service_name("axum-tutorial")
        .with_endpoint("http://localhost:14268/api/traces")
        .install_simple()?;

    // 配置 tracing subscriber
    tracing_subscriber::registry()
        .with(tracing_subscriber::EnvFilter::new(
            std::env::var("RUST_LOG").unwrap_or_else(|_| "info".into()),
        ))
        .with(tracing_subscriber::fmt::layer())
        .with(OpenTelemetryLayer::new(tracer))
        .init();

    Ok(())
}
```

## 📈 性能监控指标

### 应用级指标

```rust
// 在应用中暴露的关键指标
use prometheus::{Counter, Histogram, Gauge, register_counter, register_histogram, register_gauge};

lazy_static! {
    // HTTP 请求计数器
    static ref HTTP_REQUESTS_TOTAL: Counter = register_counter!(
        "http_requests_total",
        "Total number of HTTP requests"
    ).unwrap();

    // HTTP 请求延迟直方图
    static ref HTTP_REQUEST_DURATION: Histogram = register_histogram!(
        "http_request_duration_seconds",
        "HTTP request duration in seconds"
    ).unwrap();

    // WebSocket 连接数
    static ref WEBSOCKET_CONNECTIONS: Gauge = register_gauge!(
        "websocket_active_connections",
        "Number of active WebSocket connections"
    ).unwrap();

    // 数据库连接池指标
    static ref DB_CONNECTIONS_ACTIVE: Gauge = register_gauge!(
        "database_connections_active",
        "Number of active database connections"
    ).unwrap();

    static ref DB_CONNECTIONS_IDLE: Gauge = register_gauge!(
        "database_connections_idle",
        "Number of idle database connections"
    ).unwrap();
}
```

### 系统级指标

使用 Node Exporter 收集系统指标：

```yaml
# docker-compose.yml 中添加
  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    restart: unless-stopped
```

## 🚀 部署监控栈

### 完整启动脚本

```bash
#!/bin/bash
# scripts/start-monitoring.sh

set -e

echo "启动监控系统..."

# 创建必要的目录
mkdir -p monitoring/{grafana/dashboards,grafana/provisioning/{datasources,dashboards},rules}

# 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d

# 等待服务启动
echo "等待服务启动..."
sleep 30

# 验证服务状态
echo "验证服务状态..."
curl -f http://localhost:9090/-/healthy || echo "Prometheus 未就绪"
curl -f http://localhost:3001/api/health || echo "Grafana 未就绪"
curl -f http://localhost:9093/-/healthy || echo "AlertManager 未就绪"

echo "监控系统启动完成！"
echo "访问地址："
echo "- Prometheus: http://localhost:9090"
echo "- Grafana: http://localhost:3001 (admin/admin123)"
echo "- AlertManager: http://localhost:9093"
echo "- Jaeger: http://localhost:16686"
```

### 健康检查脚本

```bash
#!/bin/bash
# scripts/check-monitoring.sh

echo "检查监控系统状态..."

services=("prometheus:9090" "grafana:3001" "alertmanager:9093" "jaeger:16686")

for service in "${services[@]}"; do
    name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    
    if curl -f -s http://localhost:$port > /dev/null; then
        echo "✅ $name 运行正常"
    else
        echo "❌ $name 无法访问"
    fi
done

echo "检查完成！"
```

## 📚 相关文档

- [技术文档](./TECHNICAL_DOCUMENTATION.md)
- [API文档](./API_DOCUMENTATION.md)
- [部署指南](./DEPLOYMENT_GUIDE.md)
- [故障排除指南](./TROUBLESHOOTING.md)
