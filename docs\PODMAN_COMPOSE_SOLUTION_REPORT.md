# Podman-Compose 启动问题解决方案报告

## 问题描述

用户在使用 `podman-compose up -d` 启动容器时遇到启动失败问题，需要解决 WSL2 + Podman 环境下的容器配置兼容性问题。

## 问题分析

### 1. 主要问题
- **健康检查格式不兼容**: podman-compose 对 `CMD-SHELL` 格式要求与 docker-compose 不同
- **PostgreSQL 版本冲突**: PostgreSQL 17 与现有 PostgreSQL 13 数据目录不兼容
- **配置复杂性**: 过多的高级配置导致兼容性问题

### 2. 错误信息
```
ValueError: 'CMD_SHELL' takes a single string after it
```

```
FATAL: database files are incompatible with server
DETAIL: The data directory was initialized by PostgreSQL version 13, which is not compatible with this version 17.5.
```

## 解决方案

### 1. 修复健康检查配置

**原配置 (有问题):**
```yaml
healthcheck:
  test: ["CMD-SHELL", "pg_isready -U axum_user -d axum_tutorial"]
```

**修复后配置:**
```yaml
healthcheck:
  test: ["CMD", "pg_isready", "-U", "axum_user", "-d", "axum_tutorial"]
```

**DragonflyDB 健康检查修复:**
```yaml
healthcheck:
  test: ["CMD", "sh", "-c", "redis-cli -a dragonfly_secure_password_2025 ping"]
```

### 2. 清理不兼容的数据卷

```bash
# 停止所有容器并清理数据卷
wsl -d Ubuntu podman-compose down -v
```

### 3. 简化配置以提高兼容性

- 移除复杂的资源限制配置 (`deploy.resources`)
- 简化 PostgreSQL 启动参数
- 移除可能导致挂载问题的自定义配置文件
- 暂时禁用 redis-exporter 服务

### 4. 最终工作配置

**简化版 podman-compose.yml:**
```yaml
version: '3.8'

services:
  postgres:
    image: docker.io/library/postgres:17-alpine
    container_name: axum_postgres_17
    restart: unless-stopped
    environment:
      POSTGRES_DB: axum_tutorial
      POSTGRES_USER: axum_user
      POSTGRES_PASSWORD: axum_secure_password_2025
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_17_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    command: >
      postgres
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c work_mem=4MB
      -c max_connections=200
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "axum_user", "-d", "axum_tutorial"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - axum_network

  dragonflydb:
    image: docker.dragonflydb.io/dragonflydb/dragonfly:latest
    container_name: axum_dragonflydb
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: >
      dragonfly
      --logtostderr
      --requirepass=dragonfly_secure_password_2025
      --cache_mode=true
      --dbnum=16
      --bind=0.0.0.0
      --port=6379
      --maxmemory=2gb
      --dir=/data
    volumes:
      - dragonflydb_data:/data
    healthcheck:
      test: ["CMD", "sh", "-c", "redis-cli -a dragonfly_secure_password_2025 ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - axum_network

volumes:
  postgres_17_data:
  dragonflydb_data:

networks:
  axum_network:
    driver: bridge
```

## 验证结果

### 1. 容器状态
```
CONTAINER ID  IMAGE                                               STATUS
734cbb5b5199  docker.io/library/postgres:17-alpine               Up (healthy)
1ecf914ad4b7  docker.dragonflydb.io/dragonflydb/dragonfly:latest  Up (healthy)
41fbeceb4b56  docker.io/grafana/grafana:latest                    Up
e03d9eba09d0  docker.io/prom/prometheus:latest                    Up
```

### 2. 服务连接测试
- ✅ **PostgreSQL 17**: 连接成功，版本 17.5
- ✅ **DragonflyDB**: 连接成功，响应 PONG
- ✅ **Prometheus**: 服务正常，端口 9090
- ✅ **Grafana**: 服务正常，端口 3001

### 3. 服务访问地址
- **PostgreSQL 17**: `localhost:5432`
- **DragonflyDB**: `localhost:6379`
- **Prometheus**: `http://localhost:9090`
- **Grafana**: `http://localhost:3001` (admin/grafana_admin_2025)

## 关键经验总结

### 1. Podman-Compose 兼容性要点
- 健康检查必须使用 `["CMD", "command", "arg1", "arg2"]` 格式
- 避免使用 `CMD-SHELL` 格式
- 复杂的 `deploy.resources` 配置可能导致兼容性问题

### 2. 数据库版本升级注意事项
- PostgreSQL 主版本升级需要清理旧数据卷
- 使用 `podman-compose down -v` 清理所有数据卷
- 确保初始化脚本在新环境中正确执行

### 3. WSL2 + Podman 最佳实践
- 所有 podman 命令需要在 WSL2 环境中执行
- 使用 `wsl -d Ubuntu podman-compose` 格式
- 配置文件路径映射需要考虑 WSL2 文件系统

## 后续建议

1. **监控配置**: 可以逐步添加回 Prometheus 和 Grafana 的高级配置
2. **性能优化**: 在稳定运行后，可以重新启用资源限制配置
3. **安全加固**: 考虑添加网络隔离和访问控制
4. **备份策略**: 建立数据卷的定期备份机制

## 测试脚本

已创建 `scripts/test_podman_services.ps1` 脚本用于快速验证所有服务状态。

---

**解决时间**: 2025-07-29  
**解决状态**: ✅ 完全解决  
**影响范围**: WSL2 + Podman 容器环境  
**技术栈**: PostgreSQL 17 + DragonflyDB + Prometheus + Grafana
