# Axum 企业级后端项目技术文档

## 📋 项目概述

本项目是一个基于 Rust 2024 Edition 和 Axum 0.8.4 的企业级后端应用，采用模块化领域驱动设计（Modular DDD）结合整洁架构（Clean Architecture）的设计模式。项目旨在为构建支持百万吞吐量百万并发的企业级移动手机聊天室应用后端项目奠定技术基础。

### 核心特性

- **高性能异步架构**: 基于 Tokio 1.45.1 和 Axum 0.8.4 的异步Web框架
- **企业级架构设计**: 模块化DDD + 整洁架构，实现关注点分离
- **类型安全**: 充分利用 Rust 强类型系统，编译期错误检查
- **实时通信**: WebSocket 支持，实现聊天室功能
- **安全认证**: JWT + Argon2 密码哈希的安全认证体系
- **数据持久化**: SeaORM 1.1.12 + PostgreSQL/SQLite 双数据库支持
- **高性能缓存**: DragonflyDB 内存数据库缓存层
- **全面监控**: Prometheus 指标 + 健康检查端点
- **容器化部署**: Docker + Podman 容器化支持

## 🏗️ 架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    表示层 (server)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   HTTP API  │ │  WebSocket  │ │  监控端点   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (app_application)                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  认证服务   │ │  任务服务   │ │  聊天服务   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    领域层 (app_domain)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  用户实体   │ │  任务实体   │ │  消息实体   │           │
│  │  仓库接口   │ │  领域服务   │ │  值对象     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                基础设施层 (app_infrastructure)              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ SeaORM仓库  │ │ 缓存服务    │ │ 外部API     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### Crate 职责划分

#### 1. `server` - 表示层 & 应用入口
- **职责**: HTTP服务器启动、路由配置、中间件加载、依赖注入
- **核心文件**:
  - `main.rs`: 应用程序入口点
  - `startup.rs`: 服务器启动和依赖注入配置
  - `handlers/`: HTTP请求处理器
  - `middleware/`: 自定义中间件
- **依赖**: `app_application`, `app_infrastructure`, `app_common`

#### 2. `app_application` - 应用层
- **职责**: 业务用例编排、事务管理、DTO定义
- **核心组件**:
  - `services/`: 应用服务（AuthApplicationService, TaskApplicationService）
  - `dtos/`: 数据传输对象
  - `use_cases/`: 具体业务用例实现
- **依赖**: `app_domain`, `app_common`

#### 3. `app_domain` - 领域层
- **职责**: 核心业务逻辑、领域实体、仓库接口定义
- **核心组件**:
  - `entities/`: 领域实体（User, Task, Message）
  - `repositories/`: 仓库接口定义
  - `services/`: 领域服务接口
  - `value_objects/`: 值对象
- **依赖**: `app_common`（仅用于共享错误类型）

#### 4. `app_infrastructure` - 基础设施层
- **职责**: 外部系统集成、数据库访问、缓存实现
- **核心组件**:
  - `repositories/`: SeaORM仓库实现
  - `database/`: 数据库连接和配置
  - `cache/`: 缓存服务实现
  - `external/`: 外部API集成
- **依赖**: `app_domain`, `app_common`

#### 5. `app_common` - 公共模块
- **职责**: 共享工具、错误处理、配置管理
- **核心组件**:
  - `errors/`: 统一错误处理（AppError）
  - `config/`: 配置管理
  - `utils/`: 工具函数
- **依赖**: 无内部项目依赖

## 🛠️ 技术栈详解

### 核心技术栈

| 组件 | 技术/库 | 版本 | 用途说明 |
|------|---------|------|----------|
| **语言版本** | Rust | 2024 Edition | 最新语言特性和优化 |
| **Web框架** | Axum | 0.8.4 | 高性能模块化Web框架 |
| **异步运行时** | Tokio | 1.45.1 | 异步I/O和任务调度 |
| **ORM框架** | SeaORM | 1.1.12 | 异步数据库ORM |
| **数据库** | PostgreSQL | 17 | 主数据库 |
| **缓存** | DragonflyDB | latest | 高性能内存数据库 |
| **序列化** | Serde | 1.0 | JSON序列化/反序列化 |
| **认证** | jsonwebtoken | 9.3 | JWT令牌处理 |
| **密码哈希** | argon2 | 0.5 | 安全密码哈希 |
| **日志跟踪** | tracing | 0.1 | 结构化日志和跟踪 |
| **错误处理** | anyhow, thiserror | 1.0 | 错误处理和上下文 |
| **数据验证** | validator | 0.18 | 输入数据验证 |

### 开发工具链

| 工具 | 用途 | 命令示例 |
|------|------|----------|
| **cargo-watch** | 自动重新编译 | `cargo watch -x run` |
| **cargo-tarpaulin** | 代码覆盖率 | `cargo tarpaulin --workspace` |
| **cargo-audit** | 安全审计 | `cargo audit` |
| **cargo-outdated** | 依赖更新检查 | `cargo outdated` |
| **cargo-deny** | 依赖审计 | `cargo deny check` |

## 🔧 环境配置

### 部署架构

本项目采用混合部署架构：
- **后端应用**: Windows 10 本机运行（开发便利）
- **数据库服务**: WSL2 容器运行（环境一致性）

### 环境变量配置

创建 `.env` 文件（基于 `.env.example`）：

```env
# 服务器配置
HTTP_ADDR=127.0.0.1:3000
RUST_ENV=development

# 数据库配置
DATABASE_URL=sqlite://task_manager.db
# PostgreSQL示例: DATABASE_URL=postgres://user:password@localhost:5432/database

# 缓存配置
REDIS_URL=redis://localhost:6379

# 认证配置
JWT_SECRET=your-super-secret-and-long-jwt-key-change-this-in-production

# 日志配置
RUST_LOG=info,server=debug,app_application=debug

# 性能配置
DATABASE_MAX_CONNECTIONS=10
DATABASE_MIN_CONNECTIONS=5
```

### 数据库配置

#### SQLite（开发环境）
```toml
[dependencies]
sea-orm = { version = "1.1.12", features = ["sqlx-sqlite", "runtime-tokio-rustls", "macros"] }
```

#### PostgreSQL（生产环境）
```toml
[dependencies]
sea-orm = { version = "1.1.12", features = ["sqlx-postgres", "runtime-tokio-rustls", "macros"] }
```

## 🚀 快速开始指南

### 1. 环境准备

```bash
# 安装 Rust 工具链
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
rustup update

# 安装开发工具
cargo install cargo-watch cargo-tarpaulin cargo-audit cargo-outdated
```

### 2. 项目初始化

```bash
# 克隆项目
git clone <repository-url>
cd axum-tutorial

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，修改必要的配置

# 数据库初始化
cargo run -p migration
```

### 3. 启动服务

```bash
# 开发模式启动
cargo run -p server

# 或使用自动重载
cargo watch -x "run -p server"

# 生产模式构建
cargo build -p server --release
./target/release/server
```

### 4. 验证服务

```bash
# 健康检查
curl http://127.0.0.1:3000/api/health

# 注册用户
curl -X POST http://127.0.0.1:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'

# 用户登录
curl -X POST http://127.0.0.1:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## 📊 性能监控

### 监控端点

| 端点 | 用途 | 示例 |
|------|------|------|
| `/metrics` | Prometheus指标 | `curl http://127.0.0.1:3000/metrics` |
| `/api/health` | 基础健康检查 | `curl http://127.0.0.1:3000/api/health` |
| `/api/health/deep` | 深度健康检查 | `curl http://127.0.0.1:3000/api/health/deep` |
| `/api/performance/stats` | 性能统计 | `curl http://127.0.0.1:3000/api/performance/stats` |
| `/api/websocket/stats` | WebSocket统计 | `curl http://127.0.0.1:3000/api/websocket/stats` |

### 性能基准测试

```bash
# 运行性能基准测试
cargo bench

# API性能测试
cargo test --test api_performance_test_runner --release

# WebSocket延迟测试
cargo test --test websocket_latency_test --release

# 并发负载测试
cargo test --test concurrent_load_benchmarks --release
```

## 🔒 安全特性

### 认证与授权
- **JWT令牌**: 无状态认证，支持令牌刷新
- **Argon2密码哈希**: 抗彩虹表攻击的安全哈希算法
- **Bearer Token**: 标准HTTP认证头支持

### 安全中间件
- **CORS**: 跨域资源共享控制
- **Rate Limiting**: API请求频率限制
- **Request Timeout**: 请求超时保护
- **Body Size Limit**: 请求体大小限制

### 数据验证
- **输入验证**: 使用validator crate进行数据验证
- **SQL注入防护**: SeaORM提供的参数化查询
- **XSS防护**: 自动转义用户输入

## 🧪 测试策略

### 测试分层

1. **单元测试**: 每个模块内部的独立测试
2. **集成测试**: 跨模块交互测试
3. **端到端测试**: 完整API流程测试
4. **性能测试**: 负载和压力测试

### 测试命令

```bash
# 运行所有测试
cargo test --workspace

# 运行特定包测试
cargo test -p app_domain
cargo test -p app_application
cargo test -p app_infrastructure

# 运行集成测试
cargo test --test '*'

# 生成覆盖率报告
cargo tarpaulin --workspace --out Html --output-dir coverage
```

## 📈 性能优化

### 编译优化

```toml
[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
```

### 运行时优化
- **连接池**: 数据库连接池优化
- **异步批处理**: I/O操作批量处理
- **缓存策略**: 多层缓存架构
- **内存管理**: 零拷贝和内存复用

### 监控指标
- **响应时间**: P50, P95, P99延迟监控
- **吞吐量**: QPS和并发连接数
- **资源使用**: CPU、内存、磁盘I/O
- **错误率**: 4xx/5xx错误统计

## 🔧 故障排除

### 常见问题

1. **编译错误**: 检查Rust版本和依赖版本
2. **数据库连接**: 验证DATABASE_URL配置
3. **端口占用**: 检查3000端口是否被占用
4. **内存不足**: 调整数据库连接池大小

### 调试工具

```bash
# 详细日志输出
RUST_LOG=debug cargo run -p server

# 性能分析
cargo run -p server --features profiling

# 内存泄漏检测
valgrind --tool=memcheck ./target/debug/server
```

## 📚 API文档

详细的API文档请参考：
- [API接口文档](./API_DOCUMENTATION.md)
- [WebSocket协议文档](./WEBSOCKET_PROTOCOL.md)
- [错误码参考](./ERROR_CODES.md)

## 🚀 部署指南

详细的部署说明请参考：
- [生产环境部署指南](./DEPLOYMENT_GUIDE.md)
- [Docker容器化部署](./DOCKER_DEPLOYMENT.md)
- [监控系统集成](./MONITORING_SETUP.md)
