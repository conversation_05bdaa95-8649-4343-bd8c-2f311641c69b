# 故障排除指南

## 📋 故障排除概述

本文档提供了 Axum 企业级后端应用常见问题的诊断和解决方案。按照问题类型分类，提供系统化的排查步骤。

## 🚀 启动相关问题

### 问题1：应用启动失败

**症状**：
```
Error: Failed to bind to address 127.0.0.1:3000
```

**可能原因**：
1. 端口3000已被占用
2. 权限不足
3. 网络配置问题

**解决步骤**：
```bash
# 1. 检查端口占用
netstat -tlnp | grep :3000
# 或
ss -tlnp | grep :3000

# 2. 查找占用进程
lsof -i :3000

# 3. 终止占用进程
kill -9 <PID>

# 4. 或者修改配置使用其他端口
export HTTP_ADDR=127.0.0.1:3001
```

### 问题2：数据库连接失败

**症状**：
```
Error: Failed to connect to database
Database connection pool initialization failed
```

**解决步骤**：
```bash
# 1. 检查数据库服务状态
systemctl status postgresql
# 或检查容器状态
podman ps | grep postgres

# 2. 验证数据库连接
psql -h localhost -U username -d database

# 3. 检查环境变量
echo $DATABASE_URL

# 4. 测试连接池配置
cargo run -p migration -- status
```

### 问题3：依赖编译错误

**症状**：
```
error: failed to compile `sea-orm v1.1.12`
linker `cc` not found
```

**解决步骤**：
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install build-essential pkg-config libssl-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install openssl-devel

# 清理并重新编译
cargo clean
cargo build
```

## 🔐 认证相关问题

### 问题4：JWT令牌验证失败

**症状**：
```json
{
  "error": {
    "code": "INVALID_TOKEN",
    "message": "JWT令牌无效"
  }
}
```

**诊断步骤**：
```bash
# 1. 检查JWT密钥配置
echo $JWT_SECRET

# 2. 验证令牌格式（使用jwt.io或命令行工具）
echo "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." | base64 -d

# 3. 检查令牌过期时间
curl -H "Authorization: Bearer <token>" http://localhost:3000/api/auth/verify

# 4. 重新获取令牌
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### 问题5：用户认证失败

**症状**：用户无法登录，提示密码错误

**解决步骤**：
```bash
# 1. 检查用户是否存在
psql -d database -c "SELECT id, username, email FROM users WHERE email='<EMAIL>';"

# 2. 重置用户密码（开发环境）
cargo run -p migration -- reset-password <EMAIL>

# 3. 检查密码哈希算法配置
grep -r "argon2" crates/
```

## 🌐 网络连接问题

### 问题6：API请求超时

**症状**：
```
Error: Request timeout after 30 seconds
```

**诊断步骤**：
```bash
# 1. 检查服务器负载
top
htop

# 2. 检查网络连接
curl -w "@curl-format.txt" http://localhost:3000/api/health

# 3. 检查数据库查询性能
tail -f /var/log/postgresql/postgresql.log

# 4. 调整超时配置
export REQUEST_TIMEOUT=60
```

### 问题7：WebSocket连接断开

**症状**：WebSocket连接频繁断开或无法建立

**解决步骤**：
```bash
# 1. 检查WebSocket端点
curl -i -N -H "Connection: Upgrade" \
  -H "Upgrade: websocket" \
  -H "Sec-WebSocket-Version: 13" \
  -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" \
  http://localhost:3000/ws

# 2. 检查防火墙设置
sudo ufw status
sudo iptables -L

# 3. 检查代理配置（如果使用Nginx）
nginx -t
systemctl status nginx

# 4. 查看WebSocket连接统计
curl http://localhost:3000/api/websocket/stats
```

## 💾 数据库相关问题

### 问题8：数据库连接池耗尽

**症状**：
```
Error: Database connection pool exhausted
All connections are currently in use
```

**解决步骤**：
```bash
# 1. 检查连接池配置
grep -r "DATABASE_MAX_CONNECTIONS" .env

# 2. 查看当前连接数
psql -d database -c "SELECT count(*) FROM pg_stat_activity;"

# 3. 查找长时间运行的查询
psql -d database -c "SELECT pid, now() - pg_stat_activity.query_start AS duration, query FROM pg_stat_activity WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes';"

# 4. 终止长时间运行的查询
psql -d database -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE pid = <PID>;"

# 5. 调整连接池大小
export DATABASE_MAX_CONNECTIONS=20
```

### 问题9：数据库迁移失败

**症状**：
```
Error: Migration failed at version 20250111_create_users
```

**解决步骤**：
```bash
# 1. 检查迁移状态
cargo run -p migration -- status

# 2. 回滚到上一个版本
cargo run -p migration -- down

# 3. 手动检查数据库结构
psql -d database -c "\dt"

# 4. 重新运行迁移
cargo run -p migration -- up

# 5. 如果需要，重置数据库（开发环境）
dropdb database && createdb database
cargo run -p migration
```

## 🔧 性能相关问题

### 问题10：响应时间过长

**症状**：API响应时间超过1秒

**诊断步骤**：
```bash
# 1. 检查系统资源使用
top
free -h
df -h

# 2. 分析慢查询
tail -f /var/log/postgresql/postgresql.log | grep "slow query"

# 3. 检查应用性能指标
curl http://localhost:3000/api/performance/stats

# 4. 使用性能分析工具
cargo build --release
perf record ./target/release/server
perf report
```

### 问题11：内存使用过高

**症状**：应用内存使用持续增长

**解决步骤**：
```bash
# 1. 监控内存使用
ps aux | grep server
pmap -x <PID>

# 2. 检查内存泄漏
valgrind --tool=memcheck --leak-check=full ./target/debug/server

# 3. 分析堆内存
cargo install cargo-profiler
cargo profiler callgrind --bin server

# 4. 调整垃圾回收参数（如果适用）
export RUST_BACKTRACE=1
```

## 📊 监控和日志问题

### 问题12：日志不输出

**症状**：应用运行但没有日志输出

**解决步骤**：
```bash
# 1. 检查日志级别配置
echo $RUST_LOG

# 2. 设置详细日志
export RUST_LOG=debug
cargo run -p server

# 3. 检查日志文件权限
ls -la /var/log/axum-tutorial/

# 4. 测试日志输出
curl http://localhost:3000/api/health
```

### 问题13：Prometheus指标不可用

**症状**：`/metrics` 端点返回404或空内容

**解决步骤**：
```bash
# 1. 检查指标端点
curl http://localhost:3000/metrics

# 2. 验证Prometheus配置
grep -r "prometheus" crates/

# 3. 检查依赖
cargo tree | grep prometheus

# 4. 重新编译启用指标
cargo build --features metrics
```

## 🐳 容器化部署问题

### 问题14：Docker容器启动失败

**症状**：
```
Error: Container exited with code 1
```

**解决步骤**：
```bash
# 1. 查看容器日志
docker logs axum-tutorial

# 2. 进入容器调试
docker run -it --entrypoint /bin/bash axum-tutorial

# 3. 检查环境变量
docker exec axum-tutorial env

# 4. 验证网络连接
docker network ls
docker network inspect bridge
```

### 问题15：容器间通信失败

**症状**：应用无法连接到数据库容器

**解决步骤**：
```bash
# 1. 检查容器网络
docker-compose ps
docker network ls

# 2. 测试容器间连接
docker exec axum-tutorial ping postgres

# 3. 检查端口映射
docker port postgres

# 4. 验证服务发现
docker exec axum-tutorial nslookup postgres
```

## 🔍 调试工具和技巧

### 启用详细日志

```bash
# 设置环境变量
export RUST_LOG=debug,server=trace,app_application=debug
export RUST_BACKTRACE=1

# 或在代码中设置
RUST_LOG=debug cargo run -p server
```

### 使用调试工具

```bash
# 1. 使用GDB调试
gdb ./target/debug/server
(gdb) run
(gdb) bt

# 2. 使用strace跟踪系统调用
strace -o trace.log ./target/debug/server

# 3. 使用tcpdump抓包
sudo tcpdump -i lo -w capture.pcap port 3000
```

### 健康检查脚本

```bash
#!/bin/bash
# scripts/health-check.sh

echo "=== 系统健康检查 ==="

# 检查服务状态
echo "1. 检查服务状态..."
curl -f http://localhost:3000/api/health || echo "❌ 服务不可用"

# 检查数据库连接
echo "2. 检查数据库连接..."
psql -h localhost -U postgres -d axum_chat -c "SELECT 1;" || echo "❌ 数据库连接失败"

# 检查内存使用
echo "3. 检查内存使用..."
free -h

# 检查磁盘空间
echo "4. 检查磁盘空间..."
df -h

# 检查端口监听
echo "5. 检查端口监听..."
ss -tlnp | grep :3000 || echo "❌ 端口3000未监听"

echo "=== 检查完成 ==="
```

## 📞 获取帮助

### 收集诊断信息

在寻求帮助时，请提供以下信息：

```bash
#!/bin/bash
# scripts/collect-diagnostics.sh

echo "=== 诊断信息收集 ==="

echo "系统信息:"
uname -a
cat /etc/os-release

echo "Rust版本:"
rustc --version
cargo --version

echo "应用版本:"
cargo run -p server -- --version

echo "环境变量:"
env | grep -E "(RUST_|DATABASE_|HTTP_)" | sort

echo "进程信息:"
ps aux | grep server

echo "网络连接:"
ss -tlnp | grep :3000

echo "最近的错误日志:"
tail -50 /var/log/axum-tutorial/error.log

echo "=== 收集完成 ==="
```

### 联系支持

- **GitHub Issues**: 提交bug报告和功能请求
- **文档**: 查看最新的技术文档
- **社区**: 参与社区讨论

## 📚 相关文档

- [技术文档](./TECHNICAL_DOCUMENTATION.md)
- [部署指南](./DEPLOYMENT_GUIDE.md)
- [API文档](./API_DOCUMENTATION.md)
- [错误码参考](./ERROR_CODES.md)
- [监控配置](./MONITORING_SETUP.md)
