# WebSocket 协议文档

## 📋 协议概述

本文档详细描述了 Axum 企业级后端应用的 WebSocket 实时通信协议。WebSocket 连接用于实现聊天室功能、实时通知和状态同步等功能。

### 基础信息

- **WebSocket端点**: `ws://127.0.0.1:3000/ws`
- **协议版本**: v1
- **消息格式**: JSON
- **认证方式**: JWT Bearer Token
- **字符编码**: UTF-8
- **心跳间隔**: 30秒

## 🔐 连接认证

### 认证方式

#### 方式一：查询参数认证（推荐）
```
ws://127.0.0.1:3000/ws?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 方式二：子协议认证
```javascript
const ws = new WebSocket('ws://127.0.0.1:3000/ws', ['bearer', jwt_token]);
```

#### 方式三：首次消息认证
```javascript
const ws = new WebSocket('ws://127.0.0.1:3000/ws');
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'auth',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
  }));
};
```

### 连接状态

| 状态 | 描述 |
|------|------|
| `connecting` | 正在建立连接 |
| `connected` | 连接已建立，等待认证 |
| `authenticated` | 认证成功，可以发送消息 |
| `disconnected` | 连接已断开 |
| `error` | 连接错误 |

## 📨 消息格式

### 基础消息结构

所有 WebSocket 消息都遵循以下 JSON 格式：

```json
{
  "type": "message_type",
  "data": {
    // 消息数据
  },
  "timestamp": "2025-01-11T10:00:00Z",
  "id": "unique_message_id"
}
```

### 消息字段说明

| 字段 | 类型 | 必需 | 描述 |
|------|------|------|------|
| `type` | string | 是 | 消息类型标识符 |
| `data` | object | 否 | 消息数据内容 |
| `timestamp` | string | 是 | ISO 8601 格式时间戳 |
| `id` | string | 否 | 唯一消息标识符 |

## 📤 客户端发送消息

### 1. 认证消息

```json
{
  "type": "auth",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 2. 聊天消息

```json
{
  "type": "chat_message",
  "data": {
    "room_id": 1,
    "content": "Hello, World!",
    "message_type": "text"
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 3. 加入聊天室

```json
{
  "type": "join_room",
  "data": {
    "room_id": 1
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 4. 离开聊天室

```json
{
  "type": "leave_room",
  "data": {
    "room_id": 1
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 5. 正在输入状态

```json
{
  "type": "typing",
  "data": {
    "room_id": 1,
    "is_typing": true
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 6. 心跳消息

```json
{
  "type": "ping",
  "timestamp": "2025-01-11T10:00:00Z"
}
```

## 📥 服务器推送消息

### 1. 认证响应

#### 认证成功
```json
{
  "type": "auth_success",
  "data": {
    "user_id": 1,
    "username": "testuser",
    "session_id": "session_123"
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

#### 认证失败
```json
{
  "type": "auth_error",
  "data": {
    "error": "Invalid token",
    "code": "AUTHENTICATION_FAILED"
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 2. 聊天消息广播

```json
{
  "type": "chat_message",
  "data": {
    "id": 123,
    "room_id": 1,
    "user_id": 2,
    "username": "otheruser",
    "content": "Hello everyone!",
    "message_type": "text",
    "created_at": "2025-01-11T10:00:00Z"
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 3. 用户状态变化

#### 用户加入聊天室
```json
{
  "type": "user_joined",
  "data": {
    "room_id": 1,
    "user_id": 3,
    "username": "newuser",
    "member_count": 15
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

#### 用户离开聊天室
```json
{
  "type": "user_left",
  "data": {
    "room_id": 1,
    "user_id": 3,
    "username": "newuser",
    "member_count": 14
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 4. 正在输入状态广播

```json
{
  "type": "user_typing",
  "data": {
    "room_id": 1,
    "user_id": 2,
    "username": "otheruser",
    "is_typing": true
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 5. 系统通知

```json
{
  "type": "system_notification",
  "data": {
    "level": "info",
    "title": "系统维护通知",
    "message": "系统将在30分钟后进行维护",
    "action_url": "/maintenance"
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 6. 错误消息

```json
{
  "type": "error",
  "data": {
    "code": "ROOM_NOT_FOUND",
    "message": "聊天室不存在",
    "details": "Room ID 999 does not exist"
  },
  "timestamp": "2025-01-11T10:00:00Z"
}
```

### 7. 心跳响应

```json
{
  "type": "pong",
  "timestamp": "2025-01-11T10:00:00Z"
}
```

## 🔄 连接生命周期

### 连接建立流程

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务器
    
    C->>S: WebSocket连接请求
    S->>C: 连接建立
    C->>S: 认证消息
    S->>C: 认证成功响应
    C->>S: 加入聊天室
    S->>C: 加入成功确认
    Note over C,S: 开始正常消息交换
```

### 心跳机制

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as 服务器
    
    loop 每30秒
        C->>S: ping
        S->>C: pong
    end
    
    Note over C,S: 如果3次心跳失败，连接将被关闭
```

### 重连机制

客户端应实现指数退避重连策略：

```javascript
class WebSocketClient {
  constructor(url) {
    this.url = url;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 1000; // 1秒
    this.connect();
  }
  
  connect() {
    this.ws = new WebSocket(this.url);
    
    this.ws.onopen = () => {
      console.log('WebSocket连接已建立');
      this.reconnectAttempts = 0;
      this.authenticate();
    };
    
    this.ws.onclose = () => {
      console.log('WebSocket连接已关闭');
      this.reconnect();
    };
    
    this.ws.onerror = (error) => {
      console.error('WebSocket错误:', error);
    };
  }
  
  reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
      
      setTimeout(() => {
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, delay);
    } else {
      console.error('达到最大重连次数，停止重连');
    }
  }
}
```

## 📊 消息类型参考

### 客户端消息类型

| 类型 | 描述 | 数据字段 |
|------|------|----------|
| `auth` | 认证请求 | `token` |
| `chat_message` | 发送聊天消息 | `room_id`, `content`, `message_type` |
| `join_room` | 加入聊天室 | `room_id` |
| `leave_room` | 离开聊天室 | `room_id` |
| `typing` | 输入状态 | `room_id`, `is_typing` |
| `ping` | 心跳检测 | 无 |

### 服务器消息类型

| 类型 | 描述 | 数据字段 |
|------|------|----------|
| `auth_success` | 认证成功 | `user_id`, `username`, `session_id` |
| `auth_error` | 认证失败 | `error`, `code` |
| `chat_message` | 聊天消息广播 | `id`, `room_id`, `user_id`, `username`, `content` |
| `user_joined` | 用户加入 | `room_id`, `user_id`, `username`, `member_count` |
| `user_left` | 用户离开 | `room_id`, `user_id`, `username`, `member_count` |
| `user_typing` | 用户输入状态 | `room_id`, `user_id`, `username`, `is_typing` |
| `system_notification` | 系统通知 | `level`, `title`, `message`, `action_url` |
| `error` | 错误消息 | `code`, `message`, `details` |
| `pong` | 心跳响应 | 无 |

## 🔧 错误处理

### 错误码定义

| 错误码 | 描述 | 处理建议 |
|--------|------|----------|
| `AUTHENTICATION_FAILED` | 认证失败 | 重新获取有效token |
| `INVALID_MESSAGE_FORMAT` | 消息格式错误 | 检查消息JSON格式 |
| `ROOM_NOT_FOUND` | 聊天室不存在 | 检查room_id是否正确 |
| `PERMISSION_DENIED` | 权限不足 | 检查用户权限 |
| `RATE_LIMIT_EXCEEDED` | 消息频率超限 | 降低发送频率 |
| `CONNECTION_TIMEOUT` | 连接超时 | 重新建立连接 |

### 错误处理示例

```javascript
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  if (message.type === 'error') {
    switch (message.data.code) {
      case 'AUTHENTICATION_FAILED':
        // 重新认证
        this.authenticate();
        break;
      case 'ROOM_NOT_FOUND':
        // 显示错误提示
        console.error('聊天室不存在');
        break;
      case 'RATE_LIMIT_EXCEEDED':
        // 延迟发送
        setTimeout(() => this.retrySend(), 1000);
        break;
      default:
        console.error('未知错误:', message.data);
    }
  }
};
```

## 🧪 测试工具

### 简单测试页面

项目提供了多个测试页面：

- `static/websocket_test.html` - 基础WebSocket测试
- `static/websocket_chat_test.html` - 聊天功能测试
- `static/test_auth_websocket.html` - 认证WebSocket测试

### 使用 wscat 测试

```bash
# 安装 wscat
npm install -g wscat

# 连接WebSocket
wscat -c "ws://127.0.0.1:3000/ws?token=your_jwt_token"

# 发送消息
> {"type":"chat_message","data":{"room_id":1,"content":"Hello"},"timestamp":"2025-01-11T10:00:00Z"}
```

### JavaScript 客户端示例

```javascript
class ChatClient {
  constructor(token) {
    this.token = token;
    this.ws = null;
    this.connect();
  }
  
  connect() {
    this.ws = new WebSocket(`ws://127.0.0.1:3000/ws?token=${this.token}`);
    
    this.ws.onopen = () => {
      console.log('连接已建立');
    };
    
    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(message);
    };
    
    this.ws.onclose = () => {
      console.log('连接已关闭');
    };
  }
  
  sendMessage(roomId, content) {
    const message = {
      type: 'chat_message',
      data: {
        room_id: roomId,
        content: content,
        message_type: 'text'
      },
      timestamp: new Date().toISOString()
    };
    
    this.ws.send(JSON.stringify(message));
  }
  
  joinRoom(roomId) {
    const message = {
      type: 'join_room',
      data: { room_id: roomId },
      timestamp: new Date().toISOString()
    };
    
    this.ws.send(JSON.stringify(message));
  }
  
  handleMessage(message) {
    switch (message.type) {
      case 'chat_message':
        console.log(`${message.data.username}: ${message.data.content}`);
        break;
      case 'user_joined':
        console.log(`${message.data.username} 加入了聊天室`);
        break;
      case 'user_left':
        console.log(`${message.data.username} 离开了聊天室`);
        break;
      case 'error':
        console.error('错误:', message.data.message);
        break;
    }
  }
}

// 使用示例
const client = new ChatClient('your_jwt_token');
client.joinRoom(1);
client.sendMessage(1, 'Hello, World!');
```

## 📈 性能考虑

### 连接限制

- **最大并发连接数**: 10,000（可配置）
- **单用户最大连接数**: 5
- **消息发送频率限制**: 每秒10条
- **消息大小限制**: 64KB

### 优化建议

1. **消息批处理**: 合并多个小消息
2. **连接复用**: 避免频繁建立/断开连接
3. **消息压缩**: 对大消息进行压缩
4. **心跳优化**: 根据网络状况调整心跳间隔

## 📚 相关文档

- [API文档](./API_DOCUMENTATION.md)
- [技术文档](./TECHNICAL_DOCUMENTATION.md)
- [部署指南](./DEPLOYMENT_GUIDE.md)
- [错误码参考](./ERROR_CODES.md)
