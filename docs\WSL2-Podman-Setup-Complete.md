# WSL2 + Podman 设置完成报告

## 设置状态 ✅ 完成

**设置时间**: 2025年7月29日  
**环境**: Windows 10 x86 64位 + WSL2 Ubuntu + Podman  
**项目**: Axum企业级后端项目  

## 系统架构

```
Windows 10 主机 (172.19.0.1)
    ↓ WSL2虚拟网络适配器
Ubuntu WSL2 (172.19.9.132/20)
    ↓ Podman容器网络
容器服务 (**********/16)
```

## 服务状态

| 服务 | 状态 | 端口 | 版本 | 说明 |
|------|------|------|------|------|
| PostgreSQL | ✅ 运行中 | 5432 | 17.5 | 主数据库 |
| DragonflyDB | ✅ 运行中 | 6379/6380 | 7.4.0 | 高性能缓存 |
| Prometheus | ✅ 运行中 | 9090 | latest | 监控服务 |
| Grafana | ✅ 运行中 | 3001 | latest | 可视化面板 |
| Redis Exporter | ✅ 运行中 | 9121 | latest | 指标导出 (81个指标) |

## 连接测试结果

### ✅ 成功项目
- PostgreSQL 数据库连接
- DragonflyDB 缓存连接  
- Prometheus API 访问
- Grafana Web 界面
- Redis Exporter 指标导出
- 缓存读写操作

### ⚠️ 注意事项
- 数据库中文字符需要正确转义
- Redis CLI 密码警告（正常现象）

## 快速操作命令

### 容器管理
```powershell
# 启动所有容器
.\scripts\start-containers.ps1

# 查看容器状态
.\scripts\start-containers.ps1 -Status

# 停止所有容器
.\scripts\start-containers.ps1 -Stop

# 重启容器
.\scripts\start-containers.ps1 -Restart

# 查看日志
.\scripts\start-containers.ps1 -Logs
```

### 连接测试
```powershell
# 完整连接测试
.\scripts\test-connections.ps1

# 环境检查
.\scripts\setup-wsl2-podman.ps1 -CheckOnly
```

### 数据库操作
```bash
# PostgreSQL 连接
podman exec axum_postgres_17 psql -U axum_user -d axum_tutorial

# DragonflyDB 连接
podman exec axum_dragonflydb redis-cli -a dragonfly_secure_password_2025
```

## 服务访问地址

### 数据库连接
- **PostgreSQL**: `localhost:5432`
  - 用户: `axum_user`
  - 密码: `axum_secure_password_2025`
  - 数据库: `axum_tutorial`

- **DragonflyDB**: `localhost:6379`
  - 密码: `dragonfly_secure_password_2025`
  - 管理端口: `6380`

### 监控面板
- **Grafana**: http://localhost:3001
  - 用户: `admin`
  - 密码: `grafana_admin_2025`

- **Prometheus**: http://localhost:9090
  - 无需认证

- **Redis Exporter**: http://localhost:9121/metrics
  - 指标端点

## 配置文件位置

### Windows 配置
- WSL2配置: `%USERPROFILE%\.wslconfig`
- 项目脚本: `scripts/`

### WSL2 Ubuntu 配置
- Podman配置: `~/.config/containers/containers.conf`
- 容器定义: `podman-compose.yml`

## 性能优化设置

### WSL2 优化
- 内存限制: 8GB
- CPU核心: 4个
- 启用localhost转发
- 启用DNS隧道

### 容器资源限制
- PostgreSQL: 2GB内存, 2CPU
- DragonflyDB: 3GB内存, 2CPU  
- Prometheus: 默认限制
- Grafana: 默认限制
- Redis Exporter: 256MB内存, 0.5CPU

## 网络配置

### 端口映射
```
容器端口 → 主机端口
5432 → 5432 (PostgreSQL)
6379 → 6379 (DragonflyDB)
6380 → 6380 (DragonflyDB Admin)
9090 → 9090 (Prometheus)
3000 → 3001 (Grafana)
9121 → 9121 (Redis Exporter)
```

### 网络模式
- WSL2: NAT模式
- Podman: Bridge网络 (**********/16)
- 容器间通信: 内部DNS解析

## 数据持久化

### 数据卷
- `postgres_17_data`: PostgreSQL数据
- `dragonflydb_data`: DragonflyDB数据
- `prometheus_data`: Prometheus数据
- `grafana_data`: Grafana配置

### 备份建议
```bash
# 导出数据卷
podman volume export postgres_17_data > postgres_backup.tar
podman volume export dragonflydb_data > dragonfly_backup.tar
```

## 下一步操作

### 1. 启动Axum服务器
```bash
cargo run -p axum-server
```

### 2. 验证API连接
- 确保Axum服务器能连接到PostgreSQL
- 确保缓存功能正常工作

### 3. 配置监控
- 访问Grafana配置数据源
- 导入预定义的仪表板
- 设置告警规则

### 4. 开发环境就绪
- 数据库迁移: `cargo run --bin migrate`
- 运行测试: `cargo test`
- 开始开发功能

## 故障排除

### 常见问题
1. **端口占用**: 检查Windows防火墙和端口占用
2. **容器启动失败**: 查看容器日志
3. **网络连接问题**: 重启WSL2网络
4. **性能问题**: 调整资源限制

### 维护命令
```powershell
# 清理未使用的镜像
wsl -d Ubuntu -- podman system prune -a

# 更新容器镜像
wsl -d Ubuntu -- podman-compose -f podman-compose.yml pull

# 重建容器
.\scripts\start-containers.ps1 -Restart
```

## 总结

✅ **WSL2 + Podman环境已成功替代Docker Desktop**  
✅ **所有容器服务正常运行**  
✅ **网络连接和数据持久化配置完成**  
✅ **监控和指标收集系统就绪**  
✅ **开发环境完全准备就绪**

现在您可以开始使用这个高性能的容器化开发环境来开发您的Axum企业级后端项目了！
