# WSL2 + Podman 设置指南

## 概述

本指南将帮助您在Windows 10系统上设置WSL2 + Podman环境，替代Docker Desktop，用于运行Axum项目的PostgreSQL和DragonflyDB容器。

## 系统要求

- Windows 10 x86 64位系统
- WSL2已安装并启用
- Ubuntu 22.04 LTS (推荐)

## 快速设置

### 1. 检查当前环境

```powershell
# 在PowerShell中运行
.\scripts\setup-wsl2-podman.ps1 -CheckOnly
```

### 2. 完整自动设置

```powershell
# 在PowerShell中运行 (管理员权限)
.\scripts\setup-wsl2-podman.ps1
```

### 3. 启动容器服务

```powershell
# 启动所有容器
.\scripts\start-containers.ps1

# 查看容器状态
.\scripts\start-containers.ps1 -Status

# 停止所有容器
.\scripts\start-containers.ps1 -Stop

# 重启容器
.\scripts\start-containers.ps1 -Restart

# 查看日志
.\scripts\start-containers.ps1 -Logs
```

## 手动设置步骤

### 步骤1: 配置WSL2网络

1. 创建WSL配置文件:
```powershell
# 复制网络配置到用户目录
Copy-Item "config\wsl2-network.conf" "$env:USERPROFILE\.wslconfig"
```

2. 重启WSL2:
```powershell
wsl --shutdown
wsl -d Ubuntu
```

### 步骤2: 在WSL2中安装Podman

```bash
# 在WSL2 Ubuntu中执行
sudo apt update
sudo apt install -y curl wget gnupg2 software-properties-common

# 添加Podman仓库
echo 'deb https://download.opensuse.org/repositories/devel:/kubic:/libcontainers:/stable/xUbuntu_22.04/ /' | sudo tee /etc/apt/sources.list.d/devel:kubic:libcontainers:stable.list
curl -L https://download.opensuse.org/repositories/devel:kubic:libcontainers:stable/xUbuntu_22.04/Release.key | sudo apt-key add -

# 安装Podman
sudo apt update
sudo apt install -y podman

# 安装podman-compose
sudo pip3 install podman-compose
```

### 步骤3: 配置Podman

```bash
# 创建配置目录
mkdir -p ~/.config/containers

# 复制Podman配置文件
cp /mnt/d/ceshi/ceshi/axum-tutorial/config/podman-containers.conf ~/.config/containers/containers.conf

# 启用Podman socket服务
systemctl --user enable podman.socket
systemctl --user start podman.socket
```

### 步骤4: 启动容器服务

```bash
# 切换到项目目录
cd /mnt/d/ceshi/ceshi/axum-tutorial

# 启动容器
podman-compose -f podman-compose.yml up -d

# 查看容器状态
podman-compose -f podman-compose.yml ps
```

## 网络配置说明

### WSL2网络架构

```
Windows主机 (**********)
    ↓
WSL2虚拟网络适配器 (vEthernet WSL)
    ↓
Ubuntu WSL2 (************/20)
    ↓
Podman容器网络 (**********/16)
```

### 端口映射

| 服务 | 容器端口 | 主机端口 | 说明 |
|------|----------|----------|------|
| PostgreSQL | 5432 | 5432 | 主数据库 |
| DragonflyDB | 6379 | 6379 | 缓存数据库 |
| DragonflyDB Admin | 6380 | 6380 | 管理端口 |
| Prometheus | 9090 | 9090 | 监控服务 |
| Grafana | 3000 | 3001 | 可视化面板 |
| Redis Exporter | 9121 | 9121 | 指标导出 |

## 故障排除

### 常见问题

1. **端口连接失败**
   ```powershell
   # 检查端口占用
   netstat -ano | findstr :5432
   
   # 重启WSL2网络
   wsl --shutdown
   wsl -d Ubuntu
   ```

2. **容器启动失败**
   ```bash
   # 查看详细日志
   podman-compose -f podman-compose.yml logs
   
   # 检查Podman状态
   podman system info
   ```

3. **网络连接问题**
   ```bash
   # 测试容器网络
   podman network ls
   podman network inspect axum_network
   
   # 重建网络
   podman network rm axum_network
   podman-compose -f podman-compose.yml up -d
   ```

### 性能优化

1. **内存优化**
   - 调整WSL2内存限制 (`.wslconfig`)
   - 配置容器资源限制

2. **网络优化**
   - 启用localhost转发
   - 使用镜像网络模式 (Windows 11)

3. **存储优化**
   - 使用overlay存储驱动
   - 定期清理未使用的镜像和卷

## 验证安装

### 连接测试

```powershell
# 测试数据库连接
Test-NetConnection -ComputerName localhost -Port 5432
Test-NetConnection -ComputerName localhost -Port 6379

# 测试监控服务
Test-NetConnection -ComputerName localhost -Port 9090
Test-NetConnection -ComputerName localhost -Port 3001
```

### 功能测试

```bash
# PostgreSQL连接测试
psql -h localhost -p 5432 -U axum_user -d axum_tutorial

# DragonflyDB连接测试
redis-cli -h localhost -p 6379 -a dragonfly_secure_password_2025 ping
```

## 维护命令

```powershell
# 查看系统状态
.\scripts\start-containers.ps1 -Status

# 更新容器镜像
wsl -d Ubuntu -- bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman-compose -f podman-compose.yml pull"

# 清理系统
wsl -d Ubuntu -- podman system prune -a

# 备份数据卷
wsl -d Ubuntu -- podman volume export postgres_17_data > postgres_backup.tar
```

## 下一步

设置完成后，您可以：

1. 启动Axum服务器: `cargo run -p axum-server`
2. 访问Grafana面板: http://localhost:3001
3. 查看Prometheus指标: http://localhost:9090
4. 开始开发您的Axum应用

## 支持

如遇问题，请检查：
- WSL2版本和状态
- 网络配置是否正确
- 防火墙设置
- 容器日志输出
