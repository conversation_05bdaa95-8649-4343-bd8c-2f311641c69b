# API兼容性分析报告

**报告ID**: {{report_id}}  
**生成时间**: {{generated_at}}  
**分析版本**: {{from_version}} → {{to_version}}  
**报告版本**: {{report_version}}

---

## 📊 执行摘要

### 兼容性状态
- **总体兼容性**: {{#if is_compatible}}✅ 兼容{{else}}❌ 不兼容{{/if}}
- **兼容性得分**: {{compatibility_score}}/100
- **风险等级**: {{risk_level}}

### 变更统计
- **总变更数**: {{total_changes}}
- **破坏性变更**: {{breaking_changes_count}}
- **兼容性警告**: {{warnings_count}}
- **安全变更**: {{safe_changes_count}}

---

## 🚨 破坏性变更分析

{{#if breaking_changes}}
{{#each breaking_changes}}
### {{@index}}. {{description}}

**变更类型**: {{change_type}}  
**影响版本**: {{version}}  
**影响端点**: {{#each affected_endpoints}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}

**影响评估**:
- **严重程度**: {{impact.severity}}
- **影响范围**: {{impact.scope.affected_endpoints.length}} 个端点
- **预估影响客户端**: {{#if impact.estimated_affected_clients}}{{impact.estimated_affected_clients}}{{else}}待评估{{/if}}

**迁移指南**:
{{#if migration_guide}}
```
{{migration_guide}}
```
{{else}}
*迁移指南待补充*
{{/if}}

**缓解建议**:
{{#each mitigation_suggestions}}
- {{this}}
{{/each}}

---
{{/each}}
{{else}}
*未发现破坏性变更* ✅
{{/if}}

## ⚠️ 兼容性警告

{{#if warnings}}
{{#each warnings}}
- {{this}}
{{/each}}
{{else}}
*无兼容性警告* ✅
{{/if}}

---

## 📈 影响评估

### 客户端影响
- **需要更新的客户端类型**: {{#each client_impact.client_types_requiring_updates}}{{this}}{{#unless @last}}, {{/unless}}{{/each}}
- **预估更新工作量**: {{client_impact.estimated_update_effort_days}} 人天
- **向后兼容窗口**: {{client_impact.backward_compatibility_window_days}} 天

### 服务端影响
- **代码变更需求**: {{#if server_impact.required_code_changes}}需要{{else}}无需{{/if}}
- **数据库迁移**: {{#if server_impact.database_migration_required}}需要{{else}}无需{{/if}}
- **配置变更**: {{#if server_impact.configuration_changes_required}}需要{{else}}无需{{/if}}

### 运维影响
- **部署复杂度**: {{operational_impact.deployment_complexity}}
- **监控变更**: {{#if operational_impact.monitoring_changes_required}}需要{{else}}无需{{/if}}
- **文档更新**: {{#if operational_impact.documentation_updates_required}}需要{{else}}无需{{/if}}

---

## 🔄 迁移复杂度评估

### 复杂度分析
- **总体复杂度**: {{migration_complexity.overall_complexity}}
- **技术复杂度**: {{migration_complexity.technical_complexity}}
- **业务复杂度**: {{migration_complexity.business_complexity}}

### 时间估算
- **预估迁移时间**: {{migration_complexity.estimated_migration_weeks}} 周
- **建议迁移窗口**: {{#if migration_complexity.estimated_migration_weeks}}{{migration_complexity.estimated_migration_weeks}}{{else}}2-4{{/if}} 周

---

## 💡 建议和行动项

{{#each recommendations}}
### {{@index}}. {{description}}

**类型**: {{recommendation_type}}  
**优先级**: {{priority}}

**行动项**:
{{#each action_items}}
- [ ] {{description}}
  {{#if assignee}}- 负责人: {{assignee}}{{/if}}
  {{#if due_date}}- 截止日期: {{due_date}}{{/if}}
  {{#if estimated_hours}}- 预估工作量: {{estimated_hours}} 小时{{/if}}
{{/each}}

---
{{/each}}

## 📋 迁移检查清单

### 准备阶段
- [ ] 创建详细迁移计划
- [ ] 准备测试环境
- [ ] 通知相关团队和客户
- [ ] 准备回滚方案

### 实施阶段
- [ ] 执行服务端变更
- [ ] 运行兼容性测试
- [ ] 验证客户端兼容性
- [ ] 监控系统指标

### 完成阶段
- [ ] 确认所有客户端正常工作
- [ ] 更新文档和监控
- [ ] 清理旧版本代码
- [ ] 总结经验教训

---

## 📊 详细统计

### 变更类型分布
| 变更类型 | 数量 | 百分比 |
|---------|------|--------|
{{#each changes_by_type}}
| {{@key}} | {{this}} | {{percentage}}% |
{{/each}}

### 影响级别分布
| 影响级别 | 数量 | 百分比 |
|---------|------|--------|
{{#each changes_by_impact}}
| {{@key}} | {{this}} | {{percentage}}% |
{{/each}}

---

## 🔍 技术细节

### 分析工具信息
- **工具名称**: {{generator.name}}
- **工具版本**: {{generator.version}}
- **分析配置**: 
{{#each generator.config}}
  - {{@key}}: {{this}}
{{/each}}

### 分析范围
- **起始版本**: {{version_range.from_version}}
- **目标版本**: {{version_range.to_version}}
- **分析时间**: {{generated_at}}

---

## 📞 联系信息

如有疑问或需要支持，请联系：

- **技术负责人**: [姓名] ([邮箱])
- **架构团队**: [邮箱]
- **项目经理**: [姓名] ([邮箱])

---

## 📚 相关文档

- [API兼容性变更审批流程](./compatibility-change-approval-process.md)
- [API版本控制指南](./api-versioning-guide.md)
- [迁移最佳实践](./migration-best-practices.md)
- [兼容性测试指南](./compatibility-testing-guide.md)

---

**报告生成时间**: {{generated_at}}  
**下次评估建议**: {{next_review_date}}  
**报告有效期**: 30天
