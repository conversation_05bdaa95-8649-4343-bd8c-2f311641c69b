# 并发负载基准测试指南

## 概述

本文档介绍如何使用Axum项目中的并发负载基准测试工具，这是任务11.4的实现成果。该测试套件专门用于评估系统在高并发场景下的性能表现。

## 测试组件

### 1. 基准测试文件

- **`benches/concurrent_load_benchmarks.rs`**: 专门的并发负载基准测试
- **`benches/api_performance_benchmarks.rs`**: 增强的API性能基准测试（包含并发测试）

### 2. 执行脚本

- **`scripts/run_concurrent_load_benchmarks.ps1`**: Windows PowerShell执行脚本
- **`scripts/run_concurrent_load_benchmarks.sh`**: Linux/macOS Bash执行脚本
- **`scripts/test_concurrent_benchmarks.ps1`**: 快速验证脚本

## 测试场景

### 极高并发GET负载测试

测试不同并发级别下的GET请求性能：
- 100个并发请求
- 500个并发请求
- 1000个并发请求
- 2000个并发请求

### 混合API并发负载测试

模拟真实场景的混合API操作：
- 40% GET任务列表请求
- 30% POST创建任务请求
- 20% 用户认证请求
- 10% GET特定任务请求

## 使用方法

### 前置条件

1. **启动Axum服务器**
   ```bash
   cargo run -p server
   ```

2. **确保测试用户存在**
   - 邮箱: `<EMAIL>`
   - 密码: `password123`

### 运行基准测试

#### Windows环境

```powershell
# 运行所有并发负载基准测试
.\scripts\run_concurrent_load_benchmarks.ps1

# 只运行并发测试
.\scripts\run_concurrent_load_benchmarks.ps1 -TestType concurrent

# 禁用监控系统
.\scripts\run_concurrent_load_benchmarks.ps1 -WithMonitoring:$false

# 快速验证测试
.\scripts\test_concurrent_benchmarks.ps1 -QuickTest
```

#### Linux/macOS环境

```bash
# 运行所有并发负载基准测试
./scripts/run_concurrent_load_benchmarks.sh

# 只运行并发测试
./scripts/run_concurrent_load_benchmarks.sh --test-type concurrent

# 禁用监控系统
./scripts/run_concurrent_load_benchmarks.sh --no-monitoring

# 显示帮助信息
./scripts/run_concurrent_load_benchmarks.sh --help
```

#### 直接使用Cargo

```bash
# 运行特定基准测试
cargo bench --bench concurrent_load_benchmarks

# 运行所有基准测试
cargo bench

# 快速测试模式
cargo bench --bench concurrent_load_benchmarks -- --quick
```

## 监控集成

### Prometheus + Grafana

基准测试脚本可以自动启动监控系统：

1. **Grafana仪表板**: http://localhost:3001
   - 用户名: `admin`
   - 密码: `admin`

2. **Prometheus指标**: http://localhost:9090

### 监控指标

观察以下关键指标：
- CPU使用率
- 内存使用情况
- 网络I/O
- 响应时间分布
- 错误率
- 并发连接数

## 结果分析

### 输出位置

- **HTML报告**: `target/criterion/reports/`
- **JSON数据**: `target/criterion/`
- **测试报告**: `target/criterion/concurrent_load_test_report.md`

### 关键指标

1. **吞吐量**: 每秒处理的请求数
2. **响应时间**: 平均、中位数、95%分位数
3. **错误率**: 失败请求的百分比
4. **资源使用**: CPU、内存、网络使用情况

### 性能基准

| 并发级别 | 期望吞吐量 | 最大响应时间 | 错误率 |
|---------|-----------|-------------|--------|
| 100     | > 1000 RPS | < 100ms     | < 1%   |
| 500     | > 3000 RPS | < 200ms     | < 2%   |
| 1000    | > 5000 RPS | < 500ms     | < 5%   |
| 2000    | > 7000 RPS | < 1000ms    | < 10%  |

## 故障排除

### 常见问题

1. **服务器未运行**
   ```
   错误: Axum服务器未运行，请先启动服务器
   解决: cargo run -p server
   ```

2. **认证失败**
   ```
   错误: 登录失败: 401 Unauthorized
   解决: 确保测试用户已注册
   ```

3. **连接超时**
   ```
   错误: 并发API请求失败
   解决: 降低并发级别或增加超时时间
   ```

### 性能调优建议

1. **数据库优化**
   - 添加适当的索引
   - 优化查询语句
   - 考虑连接池配置

2. **服务器配置**
   - 调整Tokio运行时参数
   - 优化内存分配
   - 配置适当的超时时间

3. **网络优化**
   - 启用HTTP/2
   - 配置适当的缓冲区大小
   - 考虑负载均衡

## 扩展测试

### 自定义测试场景

可以通过修改基准测试文件来添加新的测试场景：

```rust
// 添加新的并发级别
for concurrent_requests in [100, 500, 1000, 2000, 5000].iter() {
    // 测试逻辑
}

// 添加新的API端点测试
let response = client
    .get(&format!("{}/api/new-endpoint", BASE_URL))
    .header("Authorization", format!("Bearer {}", token))
    .send()
    .await;
```

### 集成CI/CD

将基准测试集成到持续集成流程中：

```yaml
# GitHub Actions示例
- name: Run Concurrent Load Benchmarks
  run: |
    cargo run -p server &
    sleep 10
    cargo bench --bench concurrent_load_benchmarks -- --quick
```

## 最佳实践

1. **测试环境隔离**: 在专用环境中运行基准测试
2. **多次运行**: 运行多次测试以获得稳定结果
3. **监控资源**: 始终监控系统资源使用情况
4. **记录基准**: 保存基准测试结果用于性能回归检测
5. **渐进式测试**: 从低并发开始，逐步增加负载

## 相关文档

- [Criterion.rs用户指南](https://bheisler.github.io/criterion.rs/book/)
- [Tokio性能指南](https://tokio.rs/tokio/topics/performance)
- [Axum性能最佳实践](https://docs.rs/axum/latest/axum/)
- [Prometheus监控指南](https://prometheus.io/docs/)
