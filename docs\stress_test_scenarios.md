# 高并发压力测试场景设计文档

## 概述

本文档详细描述了针对Axum企业级后端项目的高并发压力测试场景，旨在验证系统在极限负载下的稳定性和性能表现。测试目标是模拟1万并发WebSocket连接，验证系统能否支持百万吞吐量百万并发的企业级移动手机聊天室应用后端需求。

## 测试环境配置

### 硬件要求
- **CPU**: 至少8核心，推荐16核心以上
- **内存**: 至少16GB，推荐32GB以上
- **网络**: 千兆网卡，低延迟网络环境
- **存储**: SSD存储，确保数据库I/O性能

### 软件环境
- **操作系统**: Windows 10 + WSL2 (容器运行PostgreSQL和DragonflyDB)
- **Rust版本**: 2024 Edition
- **Axum版本**: 0.8.4
- **Tokio版本**: 1.45.1
- **数据库**: PostgreSQL 17 (容器化部署)
- **缓存**: DragonflyDB (容器化部署)

## 测试场景分类

### 1. 正常负载场景 (Normal Load)

#### 1.1 基础API并发测试
- **并发用户数**: 100-500
- **测试持续时间**: 5分钟
- **请求类型**: GET /api/tasks, POST /api/tasks, PUT /api/tasks/{id}
- **预期响应时间**: < 100ms (P95)
- **预期成功率**: > 99%

#### 1.2 WebSocket连接稳定性测试
- **并发连接数**: 500-1000
- **测试持续时间**: 10分钟
- **消息频率**: 每秒1-5条消息
- **预期连接成功率**: > 99%
- **预期消息传输成功率**: > 99.5%

### 2. 峰值负载场景 (Peak Load)

#### 2.1 高并发API压力测试
- **并发用户数**: 1000-5000
- **测试持续时间**: 10分钟
- **请求类型**: 混合API请求 (70% GET, 20% POST, 10% PUT/DELETE)
- **预期响应时间**: < 500ms (P95)
- **预期成功率**: > 95%

#### 2.2 大规模WebSocket并发测试
- **并发连接数**: 2500-7500
- **测试持续时间**: 15分钟
- **消息频率**: 每秒5-10条消息
- **预期连接成功率**: > 95%
- **预期消息传输成功率**: > 95%

### 3. 异常负载场景 (Stress Load)

#### 3.1 极限并发连接测试
- **并发连接数**: 10000+
- **测试持续时间**: 20分钟
- **消息频率**: 每秒10-20条消息
- **预期连接成功率**: > 90%
- **预期系统稳定性**: 无崩溃，优雅降级

#### 3.2 资源耗尽测试
- **内存压力**: 逐步增加连接数直到内存使用率达到90%
- **CPU压力**: 高频率消息发送，CPU使用率达到80%
- **网络压力**: 大量并发连接建立和断开
- **数据库压力**: 高频率读写操作

## 详细测试用例

### 测试用例1: 渐进式并发连接测试

**目标**: 验证系统在连接数逐步增加时的性能表现

**步骤**:
1. 启动100个WebSocket连接
2. 每30秒增加500个连接
3. 持续增加直到达到10000个连接
4. 监控系统资源使用情况
5. 记录连接建立时间和失败率

**成功标准**:
- 连接建立时间 < 1秒 (P95)
- 连接失败率 < 5%
- 系统内存使用率 < 85%
- CPU使用率 < 80%

### 测试用例2: 消息广播性能测试

**目标**: 验证系统在高频率消息广播时的性能

**步骤**:
1. 建立5000个WebSocket连接
2. 每秒发送100条广播消息
3. 持续测试10分钟
4. 监控消息传输延迟
5. 记录消息丢失率

**成功标准**:
- 消息传输延迟 < 100ms (P95)
- 消息丢失率 < 1%
- 系统响应时间稳定

### 测试用例3: 混合负载压力测试

**目标**: 模拟真实业务场景的混合负载

**步骤**:
1. 同时进行API请求和WebSocket连接
2. API并发: 2000个用户
3. WebSocket并发: 5000个连接
4. 持续测试15分钟
5. 监控整体系统性能

**成功标准**:
- API响应时间 < 200ms (P95)
- WebSocket连接稳定性 > 95%
- 数据库查询性能稳定
- 缓存命中率 > 80%

### 测试用例4: 故障恢复测试

**目标**: 验证系统在异常情况下的恢复能力

**步骤**:
1. 建立8000个WebSocket连接
2. 模拟网络中断 (断开50%连接)
3. 观察系统恢复时间
4. 重新建立连接
5. 验证数据一致性

**成功标准**:
- 系统无崩溃
- 恢复时间 < 30秒
- 数据无丢失
- 重连成功率 > 90%

## 性能指标监控

### 系统级指标
- **CPU使用率**: 实时监控各核心使用情况
- **内存使用率**: 监控堆内存和系统内存
- **网络I/O**: 监控网络带宽使用情况
- **磁盘I/O**: 监控数据库读写性能

### 应用级指标
- **连接数**: 当前活跃WebSocket连接数
- **消息吞吐量**: 每秒处理的消息数量
- **响应时间**: API和WebSocket消息的响应时间分布
- **错误率**: 各类错误的发生频率

### 数据库指标
- **连接池使用率**: 数据库连接池的使用情况
- **查询响应时间**: SQL查询的执行时间
- **锁等待时间**: 数据库锁的等待时间
- **缓存命中率**: DragonflyDB缓存的命中率

## 测试工具配置

### Criterion.rs基准测试
- **样本数量**: 根据测试场景调整 (5-50)
- **测试时间**: 30秒-5分钟
- **预热时间**: 10-30秒
- **并发控制**: 使用Semaphore控制并发数

### 监控工具
- **Prometheus**: 收集系统和应用指标
- **Grafana**: 可视化性能数据
- **系统监控**: 使用sysinfo库监控系统资源

## 预期结果与基准

### 性能基准
- **WebSocket连接建立**: < 500ms (P95)
- **消息传输延迟**: < 50ms (P95)
- **API响应时间**: < 100ms (P95)
- **系统吞吐量**: > 10000 消息/秒
- **并发连接数**: 支持10000+连接

### 稳定性基准
- **连接成功率**: > 95%
- **消息传输成功率**: > 99%
- **系统可用性**: > 99.9%
- **错误恢复时间**: < 30秒

## 风险评估与缓解

### 潜在风险
1. **内存溢出**: 大量连接可能导致内存不足
2. **文件描述符耗尽**: 系统文件描述符限制
3. **网络拥塞**: 高并发可能导致网络瓶颈
4. **数据库连接池耗尽**: 数据库连接数限制

### 缓解措施
1. **内存管理**: 优化内存使用，及时释放资源
2. **系统配置**: 调整文件描述符限制
3. **负载均衡**: 使用连接池和负载均衡
4. **监控告警**: 实时监控关键指标

## 测试执行计划

### 阶段1: 环境准备 (1天)
- 配置测试环境
- 部署监控系统
- 验证基础功能

### 阶段2: 基础测试 (2天)
- 执行正常负载测试
- 验证基本性能指标
- 调优系统配置

### 阶段3: 压力测试 (3天)
- 执行峰值负载测试
- 执行异常负载测试
- 收集详细性能数据

### 阶段4: 分析报告 (1天)
- 分析测试结果
- 生成性能报告
- 提出优化建议
