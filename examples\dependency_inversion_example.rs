//! # 依赖倒置原则示例
//!
//! 展示如何使用接口分离和依赖倒置来解决循环依赖问题
//!
//! ## 问题场景
//! 在企业级架构中，经常会遇到以下循环依赖问题：
//! - 基础设施层依赖应用层
//! - 应用层依赖基础设施层
//!
//! ## 解决方案
//! 使用依赖倒置原则：
//! 1. 在应用层定义抽象接口
//! 2. 在基础设施层实现具体接口
//! 3. 在组装层（server）进行依赖注入

use async_trait::async_trait;
use std::sync::Arc;
use uuid::Uuid;

// ============================================================================
// 领域层：定义核心业务实体
// ============================================================================

#[derive(Debug, Clone)]
pub struct User {
    pub id: Uuid,
    pub username: String,
    pub email: String,
}

#[derive(Debug, Clone)]
pub struct Task {
    pub id: Uuid,
    pub title: String,
    pub user_id: Uuid,
    pub completed: bool,
}

// ============================================================================
// 应用层：定义抽象接口（不依赖具体实现）
// ============================================================================

/// 用户仓库抽象接口
///
/// 这个trait定义在应用层，但不依赖任何具体实现
#[async_trait]
pub trait UserRepositoryContract: Send + Sync {
    async fn find_by_id(&self, id: Uuid) -> Result<Option<User>, String>;
    async fn create(&self, user: User) -> Result<User, String>;
}

/// 任务仓库抽象接口
#[async_trait]
pub trait TaskRepositoryContract: Send + Sync {
    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Vec<Task>, String>;
    async fn create(&self, task: Task) -> Result<Task, String>;
}

/// 用户应用服务接口
///
/// 定义业务用例，依赖抽象接口而非具体实现
#[async_trait]
pub trait UserApplicationService: Send + Sync {
    async fn create_user(&self, username: String, email: String) -> Result<User, String>;
    async fn get_user_with_tasks(&self, user_id: Uuid) -> Result<(User, Vec<Task>), String>;
}

// ============================================================================
// 应用层：实现业务逻辑（依赖抽象接口）
// ============================================================================

pub struct UserApplicationServiceImpl {
    user_repository: Arc<dyn UserRepositoryContract>,
    task_repository: Arc<dyn TaskRepositoryContract>,
}

impl UserApplicationServiceImpl {
    pub fn new(
        user_repository: Arc<dyn UserRepositoryContract>,
        task_repository: Arc<dyn TaskRepositoryContract>,
    ) -> Self {
        Self {
            user_repository,
            task_repository,
        }
    }
}

#[async_trait]
impl UserApplicationService for UserApplicationServiceImpl {
    async fn create_user(&self, username: String, email: String) -> Result<User, String> {
        let user = User {
            id: Uuid::new_v4(),
            username,
            email,
        };

        self.user_repository.create(user).await
    }

    async fn get_user_with_tasks(&self, user_id: Uuid) -> Result<(User, Vec<Task>), String> {
        let user = self
            .user_repository
            .find_by_id(user_id)
            .await?
            .ok_or("User not found")?;

        let tasks = self.task_repository.find_by_user_id(user_id).await?;

        Ok((user, tasks))
    }
}

// ============================================================================
// 基础设施层：实现具体接口（不依赖应用层）
// ============================================================================

/// 内存用户仓库实现
///
/// 这个实现在基础设施层，只依赖领域层的实体和应用层的接口
pub struct InMemoryUserRepository {
    users: Arc<std::sync::Mutex<Vec<User>>>,
}

impl Default for InMemoryUserRepository {
    fn default() -> Self {
        Self::new()
    }
}

impl InMemoryUserRepository {
    pub fn new() -> Self {
        Self {
            users: Arc::new(std::sync::Mutex::new(Vec::new())),
        }
    }
}

#[async_trait]
impl UserRepositoryContract for InMemoryUserRepository {
    async fn find_by_id(&self, id: Uuid) -> Result<Option<User>, String> {
        let users = self.users.lock().map_err(|e| e.to_string())?;
        Ok(users.iter().find(|u| u.id == id).cloned())
    }

    async fn create(&self, user: User) -> Result<User, String> {
        let mut users = self.users.lock().map_err(|e| e.to_string())?;
        users.push(user.clone());
        Ok(user)
    }
}

/// 内存任务仓库实现
pub struct InMemoryTaskRepository {
    tasks: Arc<std::sync::Mutex<Vec<Task>>>,
}

impl Default for InMemoryTaskRepository {
    fn default() -> Self {
        Self::new()
    }
}

impl InMemoryTaskRepository {
    pub fn new() -> Self {
        Self {
            tasks: Arc::new(std::sync::Mutex::new(Vec::new())),
        }
    }
}

#[async_trait]
impl TaskRepositoryContract for InMemoryTaskRepository {
    async fn find_by_user_id(&self, user_id: Uuid) -> Result<Vec<Task>, String> {
        let tasks = self.tasks.lock().map_err(|e| e.to_string())?;
        Ok(tasks
            .iter()
            .filter(|t| t.user_id == user_id)
            .cloned()
            .collect())
    }

    async fn create(&self, task: Task) -> Result<Task, String> {
        let mut tasks = self.tasks.lock().map_err(|e| e.to_string())?;
        tasks.push(task.clone());
        Ok(task)
    }
}

// ============================================================================
// 组装层：依赖注入（在server层进行）
// ============================================================================

/// 服务容器
///
/// 负责创建和管理所有服务实例，解决依赖注入
pub struct ServiceContainer {
    user_service: Arc<dyn UserApplicationService>,
}

impl Default for ServiceContainer {
    fn default() -> Self {
        Self::new()
    }
}

impl ServiceContainer {
    pub fn new() -> Self {
        // 创建基础设施层实例
        let user_repository: Arc<dyn UserRepositoryContract> =
            Arc::new(InMemoryUserRepository::new());
        let task_repository: Arc<dyn TaskRepositoryContract> =
            Arc::new(InMemoryTaskRepository::new());

        // 创建应用层实例，注入依赖
        let user_service: Arc<dyn UserApplicationService> = Arc::new(
            UserApplicationServiceImpl::new(user_repository, task_repository),
        );

        Self { user_service }
    }

    pub fn get_user_service(&self) -> Arc<dyn UserApplicationService> {
        self.user_service.clone()
    }
}

// ============================================================================
// 测试示例
// ============================================================================

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_dependency_inversion() {
        // 创建服务容器
        let container = ServiceContainer::new();
        let user_service = container.get_user_service();

        // 创建用户
        let user = user_service
            .create_user("test_user".to_string(), "<EMAIL>".to_string())
            .await
            .unwrap();

        // 获取用户和任务
        let (retrieved_user, tasks) = user_service.get_user_with_tasks(user.id).await.unwrap();

        assert_eq!(retrieved_user.username, "test_user");
        assert_eq!(tasks.len(), 0); // 新用户没有任务

        println!("✅ 依赖倒置原则测试通过");
    }
}

fn main() {
    println!("🔄 依赖倒置原则示例");
    println!("📋 架构层次：");
    println!("  1. 领域层：定义核心实体");
    println!("  2. 应用层：定义抽象接口和业务逻辑");
    println!("  3. 基础设施层：实现具体接口");
    println!("  4. 组装层：进行依赖注入");
    println!();
    println!("✅ 这种架构避免了循环依赖，遵循了整洁架构原则");
}
