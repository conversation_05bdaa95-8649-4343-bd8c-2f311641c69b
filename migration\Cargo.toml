[package]
name = "migration"
version = "0.1.0"
edition = "2024"
license = "MIT OR Apache-2.0"
description = "Axum Tutorial 数据库迁移工具"
authors = ["Axum Tutorial Team"]

[[bin]]
name = "migration"
path = "src/main.rs"

[lib]
name = "migration"
path = "src/lib.rs"

[dependencies]
# 异步运行时 - 使用workspace版本
tokio = { workspace = true }

# SeaORM 迁移库 - 使用workspace版本，保持特性一致性
sea-orm-migration = { workspace = true }

# 实体定义所需的依赖 - 使用workspace版本
sea-orm = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
uuid = { workspace = true }
chrono = { workspace = true }

# 环境变量和配置
dotenvy = "0.15"
clap = { version = "4.0", features = ["derive"] }

# 日志和错误处理
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
anyhow = "1.0"
