# Axum Tutorial 数据库迁移工具

这是一个独立的数据库迁移工具，用于管理 Axum Tutorial 项目的数据库架构变更。

## 🎯 设计理念

### 为什么要分离迁移和应用启动？

1. **部署安全性**: 避免服务器启动时自动执行可能破坏性的迁移
2. **权限控制**: 迁移通常需要更高的数据库权限，而应用运行时只需要基本权限
3. **部署流程**: 可以在部署前单独验证和执行迁移
4. **回滚能力**: 独立的迁移工具便于回滚操作
5. **多实例部署**: 避免多个应用实例同时执行迁移造成冲突

## 🚀 快速开始

### 安装和构建

```bash
# 构建迁移工具
cargo build --bin migration --release

# 或者直接运行
cargo run --bin migration
```

### 基本用法

```bash
# 执行所有待执行的迁移（默认操作）
cargo run --bin migration

# 或者显式指定
cargo run --bin migration up

# 检查迁移状态
cargo run --bin migration status

# 检查数据库连接
cargo run --bin migration check

# 回滚最后一次迁移
cargo run --bin migration down

# 生成新的迁移文件
cargo run --bin migration generate create_new_table

# 重置数据库（危险操作）
cargo run --bin migration reset --confirm
```

### 环境配置

迁移工具会按以下优先级查找数据库连接：

1. 命令行参数 `--database-url`
2. 环境变量 `DATABASE_URL`
3. 默认值: `postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial`

```bash
# 使用环境变量
export DATABASE_URL="postgres://user:password@localhost:5432/database"
cargo run --bin migration

# 使用命令行参数
cargo run --bin migration --database-url "postgres://user:password@localhost:5432/database"

# 启用详细日志
cargo run --bin migration --verbose
```

## 📋 命令详解

### `up` - 执行迁移
执行所有待执行的数据库迁移。

```bash
cargo run --bin migration up
```

### `down` - 回滚迁移
回滚最后一次应用的迁移。

```bash
cargo run --bin migration down
```

### `status` - 检查状态
显示当前数据库的迁移状态，包括已应用和待执行的迁移。

```bash
cargo run --bin migration status
```

### `check` - 检查连接
测试数据库连接是否正常。

```bash
cargo run --bin migration check
```

### `generate` - 生成迁移
生成新的迁移文件模板。

```bash
cargo run --bin migration generate create_user_profiles
```

### `reset` - 重置数据库
**危险操作**: 删除所有表并重新应用所有迁移。

```bash
cargo run --bin migration reset --confirm
```

## 🏗️ 最佳实践

### 1. 部署流程

```bash
# 1. 部署前检查迁移状态
cargo run --bin migration status

# 2. 执行迁移
cargo run --bin migration up

# 3. 验证迁移结果
cargo run --bin migration status

# 4. 启动应用服务器
cargo run --bin axum-server
```

### 2. 开发流程

```bash
# 1. 生成新迁移
cargo run --bin migration generate add_user_avatar

# 2. 编辑生成的迁移文件
# 编辑 migration/src/m20250720_xxxxxx_add_user_avatar.rs

# 3. 测试迁移
cargo run --bin migration up

# 4. 如果有问题，回滚
cargo run --bin migration down
```
