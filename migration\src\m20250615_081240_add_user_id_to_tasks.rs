use sea_orm::ConnectionTrait;
use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 为 tasks 表添加 user_id 列，设为可空以兼容现有数据
        manager
            .alter_table(
                Table::alter()
                    .table(Tasks::Table)
                    .add_column(ColumnDef::new(Tasks::UserId).uuid().null())
                    .to_owned(),
            )
            .await?;

        // 创建索引以优化查询性能
        manager
            .create_index(
                Index::create()
                    .name("idx_tasks_user_id")
                    .table(Tasks::Table)
                    .col(Tasks::UserId)
                    .to_owned(),
            )
            .await?;

        // 添加外键约束，关联到 users 表
        // 注意：PostgreSQL支持在ALTER TABLE中添加外键约束
        let add_fk_sql = format!(
            "ALTER TABLE {} ADD CONSTRAINT {} FOREIGN KEY ({}) REFERENCES {} ({}) ON DELETE SET NULL ON UPDATE CASCADE",
            "tasks", "fk_tasks_user_id", "user_id", "users", "id"
        );

        manager
            .get_connection()
            .execute(sea_orm::Statement::from_string(
                manager.get_database_backend(),
                add_fk_sql,
            ))
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 删除外键约束
        let drop_fk_sql = "ALTER TABLE tasks DROP CONSTRAINT IF EXISTS fk_tasks_user_id";
        manager
            .get_connection()
            .execute(sea_orm::Statement::from_string(
                manager.get_database_backend(),
                drop_fk_sql.to_string(),
            ))
            .await?;

        // 删除索引
        manager
            .drop_index(
                Index::drop()
                    .name("idx_tasks_user_id")
                    .table(Tasks::Table)
                    .to_owned(),
            )
            .await?;

        // 删除 user_id 列
        manager
            .alter_table(
                Table::alter()
                    .table(Tasks::Table)
                    .drop_column(Tasks::UserId)
                    .to_owned(),
            )
            .await
    }
}

/// 定义 tasks 表的标识符
#[derive(DeriveIden)]
enum Tasks {
    #[sea_orm(iden = "tasks")]
    Table,
    UserId,
}

/// 定义 users 表的标识符（用于外键引用）
#[derive(DeriveIden)]
#[allow(dead_code)]
enum Users {
    #[sea_orm(iden = "users")]
    Table,
    Id,
}
