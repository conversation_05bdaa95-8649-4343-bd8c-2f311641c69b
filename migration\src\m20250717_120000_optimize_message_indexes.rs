//! 优化消息表索引的迁移
//!
//! 这个迁移添加了额外的索引来优化常见的查询模式：
//! 1. 单独的created_at索引用于全局时间排序
//! 2. 复合索引优化用户在特定聊天室的消息查询
//! 3. 消息状态索引用于过滤未读/已读消息

use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 1. 为created_at字段单独创建索引（用于全局时间排序）
        manager
            .create_index(
                Index::create()
                    .name("idx_messages_created_at_desc")
                    .table(Messages::Table)
                    .col((Messages::CreatedAt, IndexOrder::Desc))
                    .to_owned(),
            )
            .await?;

        // 2. 为用户在特定聊天室的消息查询创建复合索引
        manager
            .create_index(
                Index::create()
                    .name("idx_messages_sender_chat_room_time")
                    .table(Messages::Table)
                    .col(Messages::SenderId)
                    .col(Messages::ChatRoomId)
                    .col((Messages::CreatedAt, IndexOrder::Desc))
                    .to_owned(),
            )
            .await?;

        // 3. 为消息状态查询创建索引
        manager
            .create_index(
                Index::create()
                    .name("idx_messages_status_created_at")
                    .table(Messages::Table)
                    .col(Messages::Status)
                    .col((Messages::CreatedAt, IndexOrder::Desc))
                    .to_owned(),
            )
            .await?;

        // 4. 为消息类型查询创建索引
        manager
            .create_index(
                Index::create()
                    .name("idx_messages_type_created_at")
                    .table(Messages::Table)
                    .col(Messages::MessageType)
                    .col((Messages::CreatedAt, IndexOrder::Desc))
                    .to_owned(),
            )
            .await?;

        // 5. 为置顶消息查询创建索引
        manager
            .create_index(
                Index::create()
                    .name("idx_messages_pinned_chat_room")
                    .table(Messages::Table)
                    .col(Messages::IsPinned)
                    .col(Messages::ChatRoomId)
                    .col((Messages::CreatedAt, IndexOrder::Desc))
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 删除所有新创建的索引
        manager
            .drop_index(
                Index::drop()
                    .name("idx_messages_pinned_chat_room")
                    .to_owned(),
            )
            .await?;

        manager
            .drop_index(
                Index::drop()
                    .name("idx_messages_type_created_at")
                    .to_owned(),
            )
            .await?;

        manager
            .drop_index(
                Index::drop()
                    .name("idx_messages_status_created_at")
                    .to_owned(),
            )
            .await?;

        manager
            .drop_index(
                Index::drop()
                    .name("idx_messages_sender_chat_room_time")
                    .to_owned(),
            )
            .await?;

        manager
            .drop_index(
                Index::drop()
                    .name("idx_messages_created_at_desc")
                    .to_owned(),
            )
            .await?;

        Ok(())
    }
}

/// 消息表的列定义枚举（引用现有定义）
#[derive(DeriveIden)]
#[allow(dead_code)]
enum Messages {
    Table,
    Id,
    Content,
    MessageType,
    Status,
    SenderId,
    ChatRoomId,
    ReplyToId,
    Metadata,
    Priority,
    IsPinned,
    ExpiresAt,
    CreatedAt,
    UpdatedAt,
}
