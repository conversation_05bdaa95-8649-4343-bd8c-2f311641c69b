//! # 用户资料字段扩展迁移
//!
//! 为用户表添加更多资料字段，支持完整的用户资料功能：
//! - 昵称 (display_name)
//! - 头像URL (avatar_url)
//! - 个人简介 (bio)
//! - 位置信息 (location)
//! - 个人网站 (website)
//! - 最后登录时间 (last_login_at)
//! - 账户状态 (status)

use sea_orm_migration::prelude::*;

#[derive(DeriveMigrationName)]
pub struct Migration;

#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 为用户表添加新的资料字段
        manager
            .alter_table(
                Table::alter()
                    .table(User::Table)
                    // 昵称字段（可选，用于显示）
                    .add_column(
                        ColumnDef::new(User::DisplayName)
                            .string_len(100)
                            .null()
                    )
                    // 头像URL字段（可选）
                    .add_column(
                        ColumnDef::new(User::AvatarUrl)
                            .string_len(500)
                            .null()
                    )
                    // 个人简介字段（可选）
                    .add_column(
                        ColumnDef::new(User::Bio)
                            .text()
                            .null()
                    )
                    // 位置信息字段（可选）
                    .add_column(
                        ColumnDef::new(User::Location)
                            .string_len(100)
                            .null()
                    )
                    // 个人网站字段（可选）
                    .add_column(
                        ColumnDef::new(User::Website)
                            .string_len(200)
                            .null()
                    )
                    // 最后登录时间字段（可选）
                    .add_column(
                        ColumnDef::new(User::LastLoginAt)
                            .timestamp_with_time_zone()
                            .null()
                    )
                    // 账户状态字段（默认为active）
                    .add_column(
                        ColumnDef::new(User::Status)
                            .string_len(20)
                            .not_null()
                            .default("active")
                    )
                    .to_owned(),
            )
            .await?;

        // 为新字段创建索引以提高查询性能
        manager
            .create_index(
                Index::create()
                    .name("idx_users_status")
                    .table(User::Table)
                    .col(User::Status)
                    .to_owned(),
            )
            .await?;

        manager
            .create_index(
                Index::create()
                    .name("idx_users_last_login_at")
                    .table(User::Table)
                    .col(User::LastLoginAt)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        // 删除索引
        manager
            .drop_index(
                Index::drop()
                    .name("idx_users_last_login_at")
                    .table(User::Table)
                    .to_owned(),
            )
            .await?;

        manager
            .drop_index(
                Index::drop()
                    .name("idx_users_status")
                    .table(User::Table)
                    .to_owned(),
            )
            .await?;

        // 删除添加的字段
        manager
            .alter_table(
                Table::alter()
                    .table(User::Table)
                    .drop_column(User::Status)
                    .drop_column(User::LastLoginAt)
                    .drop_column(User::Website)
                    .drop_column(User::Location)
                    .drop_column(User::Bio)
                    .drop_column(User::AvatarUrl)
                    .drop_column(User::DisplayName)
                    .to_owned(),
            )
            .await?;

        Ok(())
    }
}

/// 用户表字段标识符
#[derive(DeriveIden)]
enum User {
    #[sea_orm(iden = "users")]
    Table,
    DisplayName,
    AvatarUrl,
    Bio,
    Location,
    Website,
    LastLoginAt,
    Status,
}
