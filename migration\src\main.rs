use anyhow::Result;
use clap::{Parser, Subcommand};
use sea_orm_migration::prelude::*;
use tracing::{error, info, warn};

/// Axum Tutorial 数据库迁移工具
///
/// 这是一个独立的数据库迁移工具，用于管理 Axum Tutorial 项目的数据库架构变更。
/// 支持迁移执行、回滚、状态检查等功能。
#[derive(Parser)]
#[command(name = "migration")]
#[command(about = "Axum Tutorial 数据库迁移工具")]
#[command(version = "1.0.0")]
struct Cli {
    #[command(subcommand)]
    command: Option<Commands>,

    /// 数据库连接URL (可通过环境变量 DATABASE_URL 设置)
    #[arg(long, env = "DATABASE_URL")]
    database_url: Option<String>,

    /// 启用详细日志输出
    #[arg(short, long)]
    verbose: bool,
}

#[derive(Subcommand)]
enum Commands {
    /// 执行所有待执行的迁移
    Up,
    /// 回滚最后一次迁移
    Down,
    /// 显示迁移状态
    Status,
    /// 重置数据库（危险操作）
    Reset {
        /// 确认重置操作
        #[arg(long)]
        confirm: bool,
    },
    /// 生成新的迁移文件
    Generate {
        /// 迁移名称
        name: String,
    },
    /// 检查数据库连接
    Check,
}

#[tokio::main]
async fn main() -> Result<()> {
    // 加载环境变量
    let _ = dotenvy::dotenv();

    // 设置数据库URL环境变量
    if std::env::var("DATABASE_URL").is_err() {
        unsafe {
            std::env::set_var(
                "DATABASE_URL",
                "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial",
            );
        }
    }

    // 直接使用SeaORM的CLI
    cli::run_cli(migration::Migrator).await;
    Ok(())
}

/// 初始化日志系统
#[allow(dead_code)]
fn init_logging(verbose: bool) {
    let level = if verbose {
        tracing::Level::DEBUG
    } else {
        tracing::Level::INFO
    };

    // 尝试初始化，如果已经初始化则忽略错误
    let _ = tracing_subscriber::fmt()
        .with_max_level(level)
        .with_target(false)
        .with_thread_ids(false)
        .with_file(false)
        .with_line_number(false)
        .try_init();
}

/// 隐藏数据库URL中的密码
#[allow(dead_code)]
fn mask_password(url: &str) -> String {
    if let Some(at_pos) = url.find('@') {
        if let Some(colon_pos) = url[..at_pos].rfind(':') {
            let mut masked = url.to_string();
            masked.replace_range(colon_pos + 1..at_pos, "****");
            return masked;
        }
    }
    url.to_string()
}

/// 检查迁移状态
#[allow(dead_code)]
async fn check_migration_status(database_url: &str) -> Result<()> {
    use sea_orm::{ConnectOptions, Database};
    use std::time::Duration;

    let mut opt = ConnectOptions::new(database_url);
    opt.max_connections(1)
        .connect_timeout(Duration::from_secs(10));

    let db = Database::connect(opt).await?;

    // 检查迁移表是否存在
    use sea_orm_migration::MigratorTrait;
    let applied = migration::Migrator::get_applied_migrations(&db).await?;
    let pending = migration::Migrator::get_pending_migrations(&db).await?;

    info!("📊 迁移状态报告:");
    info!("   已应用迁移: {} 个", applied.len());
    info!("   待执行迁移: {} 个", pending.len());

    if !applied.is_empty() {
        info!("📋 已应用的迁移:");
        for (i, _migration) in applied.iter().enumerate() {
            info!("   ✅ 迁移 #{}", i + 1);
        }
    }

    if !pending.is_empty() {
        warn!("⏳ 待执行的迁移:");
        for (i, _migration) in pending.iter().enumerate() {
            warn!("   ⏸️  待执行迁移 #{}", i + 1);
        }
    } else {
        info!("✅ 所有迁移都已应用");
    }

    Ok(())
}

/// 重置数据库
#[allow(dead_code)]
async fn reset_database(database_url: &str) -> Result<()> {
    use sea_orm::{ConnectOptions, Database};
    use std::time::Duration;

    warn!("🔥 警告: 即将重置数据库，所有数据将被删除！");

    let mut opt = ConnectOptions::new(database_url);
    opt.max_connections(1)
        .connect_timeout(Duration::from_secs(10));

    let db = Database::connect(opt).await?;

    // 执行重置
    use sea_orm_migration::MigratorTrait;
    migration::Migrator::reset(&db).await?;

    info!("✅ 数据库重置完成");
    Ok(())
}

/// 生成新的迁移文件
#[allow(dead_code)]
async fn generate_migration(name: &str) -> Result<()> {
    use std::process::Command;

    info!("📝 生成迁移文件: {}", name);

    let output = Command::new("sea-orm-cli")
        .args(["migrate", "generate", name])
        .current_dir("migration")
        .output();

    match output {
        Ok(output) => {
            if output.status.success() {
                info!("✅ 迁移文件生成成功");
                if !output.stdout.is_empty() {
                    info!("{}", String::from_utf8_lossy(&output.stdout));
                }
            } else {
                error!("❌ 迁移文件生成失败");
                if !output.stderr.is_empty() {
                    error!("{}", String::from_utf8_lossy(&output.stderr));
                }
            }
        }
        Err(e) => {
            error!("❌ 无法执行 sea-orm-cli: {}", e);
            error!("请确保已安装 sea-orm-cli: cargo install sea-orm-cli");
        }
    }

    Ok(())
}

/// 检查数据库连接
#[allow(dead_code)]
async fn check_database_connection(database_url: &str) -> Result<()> {
    use sea_orm::{ConnectOptions, ConnectionTrait, Database, Statement};
    use std::time::Duration;

    info!("🔍 测试数据库连接...");

    let mut opt = ConnectOptions::new(database_url);
    opt.max_connections(1)
        .connect_timeout(Duration::from_secs(10));

    let db = Database::connect(opt).await?;

    // 执行简单查询测试连接
    let result = db
        .execute(Statement::from_string(
            sea_orm::DatabaseBackend::Postgres,
            "SELECT version()".to_string(),
        ))
        .await?;

    info!("✅ 数据库连接成功");
    info!("📊 连接信息: 影响行数 {}", result.rows_affected());

    Ok(())
}
