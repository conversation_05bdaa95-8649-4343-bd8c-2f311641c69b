//! `user_entity.rs`
//!
//! 这个文件专门定义了与 `users` 表相关联的 SeaORM 实体。
//! 它被设计为 `migration` crate 的一部分，以便迁移脚本可以直接访问它，
//! 同时也可以被主应用 `axum-tutorial` 导入，实现代码的复用和单向依赖。

use sea_orm::entity::prelude::*;

#[derive(Clone, Debug, PartialEq, Eq, DeriveEntityModel)]
#[sea_orm(table_name = "users")]
pub struct Model {
    #[sea_orm(primary_key, auto_increment = false)]
    pub id: Uuid,
    #[sea_orm(unique)]
    pub username: String,
    pub email: Option<String>,
    pub password_hash: String,
    /// 用户显示名称（昵称）
    pub display_name: Option<String>,
    /// 用户头像URL
    pub avatar_url: Option<String>,
    /// 用户个人简介
    pub bio: Option<String>,
    /// 用户位置信息
    pub location: Option<String>,
    /// 用户个人网站
    pub website: Option<String>,
    /// 最后登录时间
    pub last_login_at: Option<DateTimeUtc>,
    /// 账户状态 (active, inactive, suspended)
    pub status: String,
    pub created_at: DateTimeUtc,
    pub updated_at: DateTimeUtc,
}

#[derive(Copy, Clone, Debug, EnumIter, DeriveRelation)]
pub enum Relation {
    #[sea_orm(has_many = "super::task_entity::Entity")]
    Task,
}

impl Related<super::task_entity::Entity> for Entity {
    fn to() -> RelationDef {
        Relation::Task.def()
    }
}

impl ActiveModelBehavior for ActiveModel {}
