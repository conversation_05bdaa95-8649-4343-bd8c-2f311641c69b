# Prometheus + Grafana 监控系统

基于Context7 MCP最佳实践的Axum应用监控解决方案。

## 🎯 功能特性

- **Prometheus监控**: 收集和存储应用指标数据
- **Grafana可视化**: 提供丰富的仪表板和图表
- **实时监控**: 15秒间隔的指标收集
- **自动配置**: 预配置的数据源和仪表板
- **容器化部署**: 使用Docker Compose一键部署

## 📊 监控指标

### 应用指标
- HTTP请求总数 (`http_requests_total`)
- HTTP请求持续时间 (`http_request_duration_seconds`)
- 内存使用率 (`memory_usage_ratio`)
- CPU使用率 (`cpu_usage_ratio`)
- 活跃连接数 (`active_connections`)
- 数据库连接数 (`database_connections`)

### 系统指标
- 容器资源使用情况
- 网络I/O统计
- 磁盘使用情况

## 🚀 快速开始

### 1. 启动监控系统

```powershell
# 使用启动脚本（推荐）
.\scripts\start-monitoring.ps1

# 或者直接使用Docker Compose
docker-compose up -d prometheus grafana
```

### 2. 访问服务

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3001
  - 用户名: `admin`
  - 密码: `admin123`
- **Axum应用**: http://localhost:3000
- **应用指标**: http://localhost:3000/metrics

### 3. 启动Axum应用

```powershell
# 启动应用以开始收集指标
cargo run -p server
```

## 📈 Grafana仪表板

### 预配置仪表板
- **Axum Tutorial 应用监控**: 应用性能和业务指标
- **系统监控**: 系统资源使用情况

### 自定义仪表板
1. 登录Grafana (http://localhost:3001)
2. 点击 "+" -> "Dashboard"
3. 添加面板并配置查询
4. 使用Prometheus作为数据源

## 🔧 配置文件

### Prometheus配置 (`monitoring/prometheus.yml`)
- 全局抓取间隔: 15秒
- 目标配置: Axum应用 (host.docker.internal:3000)
- 数据保留: 200小时

### Grafana配置
- 数据源: `monitoring/grafana/provisioning/datasources/`
- 仪表板: `monitoring/grafana/provisioning/dashboards/`
- 预制仪表板: `monitoring/grafana/dashboards/`

## 📝 常用操作

### 查看服务状态
```powershell
.\scripts\start-monitoring.ps1 -Status
```

### 查看日志
```powershell
.\scripts\start-monitoring.ps1 -Logs
```

### 重启服务
```powershell
.\scripts\start-monitoring.ps1 -Restart
```

### 停止服务
```powershell
.\scripts\start-monitoring.ps1 -Stop
```

## 🔍 故障排除

### 常见问题

1. **Prometheus无法抓取指标**
   - 检查Axum应用是否在3000端口运行
   - 确认 `/metrics` 端点可访问
   - 检查防火墙设置

2. **Grafana无法连接Prometheus**
   - 确认Prometheus容器正在运行
   - 检查网络连接
   - 验证数据源配置

3. **容器启动失败**
   - 检查端口是否被占用
   - 确认Docker服务正在运行
   - 查看容器日志

### 调试命令

```powershell
# 检查容器状态
docker-compose ps

# 查看容器日志
docker-compose logs prometheus
docker-compose logs grafana

# 测试指标端点
curl http://localhost:3000/metrics

# 测试Prometheus查询
curl http://localhost:9090/api/v1/query?query=up
```

## 🎨 自定义配置

### 添加新的监控目标
编辑 `monitoring/prometheus.yml`:

```yaml
scrape_configs:
  - job_name: 'my-service'
    static_configs:
      - targets: ['host.docker.internal:8080']
```

### 创建自定义仪表板
1. 在Grafana中创建仪表板
2. 导出JSON配置
3. 保存到 `monitoring/grafana/dashboards/`

## 📚 参考资料

- [Prometheus官方文档](https://prometheus.io/docs/)
- [Grafana官方文档](https://grafana.com/docs/)
- [metrics-exporter-prometheus文档](https://docs.rs/metrics-exporter-prometheus/)
- [Context7 MCP最佳实践](https://context7.ai/)

## 🔒 安全注意事项

- 生产环境请修改默认密码
- 配置适当的网络访问控制
- 定期更新容器镜像
- 监控敏感数据的访问权限
