# DragonflyDB 告警规则配置
# 针对企业级聊天室应用的关键指标监控

groups:
  - name: dragonflydb.rules
    rules:
      # DragonflyDB服务可用性告警
      - alert: DragonflyDBDown
        expr: up{job="dragonflydb"} == 0
        for: 1m
        labels:
          severity: critical
          service: dragonflydb
          component: cache
        annotations:
          summary: "DragonflyDB服务不可用"
          description: "DragonflyDB实例 {{ $labels.instance }} 已经停止响应超过1分钟"
          runbook_url: "https://docs.dragonflydb.io/troubleshooting"

      # 内存使用率告警
      - alert: DragonflyDBHighMemoryUsage
        expr: (redis_memory_used_bytes{job="dragonflydb"} / redis_memory_max_bytes{job="dragonflydb"}) * 100 > 85
        for: 5m
        labels:
          severity: warning
          service: dragonflydb
          component: memory
        annotations:
          summary: "DragonflyDB内存使用率过高"
          description: "DragonflyDB实例 {{ $labels.instance }} 内存使用率为 {{ $value }}%，超过85%阈值"
          runbook_url: "https://docs.dragonflydb.io/memory-optimization"

      # 内存使用率严重告警
      - alert: DragonflyDBCriticalMemoryUsage
        expr: (redis_memory_used_bytes{job="dragonflydb"} / redis_memory_max_bytes{job="dragonflydb"}) * 100 > 95
        for: 2m
        labels:
          severity: critical
          service: dragonflydb
          component: memory
        annotations:
          summary: "DragonflyDB内存使用率严重过高"
          description: "DragonflyDB实例 {{ $labels.instance }} 内存使用率为 {{ $value }}%，超过95%阈值，可能导致OOM"
          runbook_url: "https://docs.dragonflydb.io/memory-optimization"

      # 连接数告警
      - alert: DragonflyDBHighConnections
        expr: redis_connected_clients{job="dragonflydb"} > 80000
        for: 5m
        labels:
          severity: warning
          service: dragonflydb
          component: connections
        annotations:
          summary: "DragonflyDB连接数过高"
          description: "DragonflyDB实例 {{ $labels.instance }} 当前连接数为 {{ $value }}，超过80000阈值"
          runbook_url: "https://docs.dragonflydb.io/connection-management"

      # 缓存命中率告警
      - alert: DragonflyDBLowHitRate
        expr: (redis_keyspace_hits_total{job="dragonflydb"} / (redis_keyspace_hits_total{job="dragonflydb"} + redis_keyspace_misses_total{job="dragonflydb"})) * 100 < 80
        for: 10m
        labels:
          severity: warning
          service: dragonflydb
          component: performance
        annotations:
          summary: "DragonflyDB缓存命中率过低"
          description: "DragonflyDB实例 {{ $labels.instance }} 缓存命中率为 {{ $value }}%，低于80%阈值"
          runbook_url: "https://docs.dragonflydb.io/cache-optimization"

      # 操作延迟告警
      - alert: DragonflyDBHighLatency
        expr: redis_slowlog_length{job="dragonflydb"} > 10
        for: 5m
        labels:
          severity: warning
          service: dragonflydb
          component: performance
        annotations:
          summary: "DragonflyDB操作延迟过高"
          description: "DragonflyDB实例 {{ $labels.instance }} 慢查询日志长度为 {{ $value }}，存在性能问题"
          runbook_url: "https://docs.dragonflydb.io/performance-tuning"

      # 键空间变化率告警
      - alert: DragonflyDBHighKeyspaceChanges
        expr: rate(redis_keyspace_hits_total{job="dragonflydb"}[5m]) + rate(redis_keyspace_misses_total{job="dragonflydb"}[5m]) > 10000
        for: 5m
        labels:
          severity: info
          service: dragonflydb
          component: performance
        annotations:
          summary: "DragonflyDB键空间操作频率过高"
          description: "DragonflyDB实例 {{ $labels.instance }} 键空间操作频率为 {{ $value }} ops/sec，可能需要优化"
          runbook_url: "https://docs.dragonflydb.io/performance-tuning"

      # 复制延迟告警 (如果启用了复制)
      - alert: DragonflyDBReplicationLag
        expr: redis_master_repl_offset{job="dragonflydb"} - redis_slave_repl_offset{job="dragonflydb"} > 1000000
        for: 5m
        labels:
          severity: warning
          service: dragonflydb
          component: replication
        annotations:
          summary: "DragonflyDB复制延迟过高"
          description: "DragonflyDB实例 {{ $labels.instance }} 复制延迟为 {{ $value }} bytes，超过1MB阈值"
          runbook_url: "https://docs.dragonflydb.io/replication"

      # 驱逐键告警
      - alert: DragonflyDBHighEvictions
        expr: rate(redis_evicted_keys_total{job="dragonflydb"}[5m]) > 100
        for: 5m
        labels:
          severity: warning
          service: dragonflydb
          component: memory
        annotations:
          summary: "DragonflyDB键驱逐率过高"
          description: "DragonflyDB实例 {{ $labels.instance }} 键驱逐率为 {{ $value }} keys/sec，可能需要增加内存"
          runbook_url: "https://docs.dragonflydb.io/memory-optimization"

      # 网络I/O告警
      - alert: DragonflyDBHighNetworkIO
        expr: rate(redis_net_input_bytes_total{job="dragonflydb"}[5m]) + rate(redis_net_output_bytes_total{job="dragonflydb"}[5m]) > 100000000
        for: 5m
        labels:
          severity: info
          service: dragonflydb
          component: network
        annotations:
          summary: "DragonflyDB网络I/O过高"
          description: "DragonflyDB实例 {{ $labels.instance }} 网络I/O为 {{ $value }} bytes/sec，可能存在网络瓶颈"
          runbook_url: "https://docs.dragonflydb.io/network-optimization"

      # 持久化告警
      - alert: DragonflyDBPersistenceFailure
        expr: redis_rdb_last_save_timestamp_seconds{job="dragonflydb"} < (time() - 3600)
        for: 5m
        labels:
          severity: warning
          service: dragonflydb
          component: persistence
        annotations:
          summary: "DragonflyDB持久化失败"
          description: "DragonflyDB实例 {{ $labels.instance }} 超过1小时未成功保存快照"
          runbook_url: "https://docs.dragonflydb.io/persistence"

  - name: dragonflydb.aggregation
    rules:
      # 聚合规则：5分钟平均内存使用率
      - record: dragonflydb:memory_usage_percent:5m
        expr: (redis_memory_used_bytes{job="dragonflydb"} / redis_memory_max_bytes{job="dragonflydb"}) * 100

      # 聚合规则：5分钟平均操作率
      - record: dragonflydb:ops_per_sec:5m
        expr: rate(redis_commands_processed_total{job="dragonflydb"}[5m])

      # 聚合规则：5分钟平均缓存命中率
      - record: dragonflydb:hit_rate_percent:5m
        expr: (rate(redis_keyspace_hits_total{job="dragonflydb"}[5m]) / (rate(redis_keyspace_hits_total{job="dragonflydb"}[5m]) + rate(redis_keyspace_misses_total{job="dragonflydb"}[5m]))) * 100

      # 聚合规则：5分钟平均网络吞吐量
      - record: dragonflydb:network_throughput_bytes:5m
        expr: rate(redis_net_input_bytes_total{job="dragonflydb"}[5m]) + rate(redis_net_output_bytes_total{job="dragonflydb"}[5m])

      # 聚合规则：每小时键驱逐数
      - record: dragonflydb:evicted_keys:1h
        expr: increase(redis_evicted_keys_total{job="dragonflydb"}[1h])
