# Grafana数据源配置
# 自动配置Prometheus数据源

apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    # 版本
    version: 1
    # 组织ID
    orgId: 1
    # 数据源UID（可选）
    uid: prometheus-uid
    # JSON数据配置（合并所有配置项）
    jsonData:
      # HTTP方法
      httpMethod: POST
      # 查询超时时间
      queryTimeout: '60s'
      # 时间间隔
      timeInterval: '15s'
      # 保持Cookie
      keepCookies: []
      # 自定义HTTP头
      customQueryParameters: ''
      # 默认编辑器
      defaultEditor: 'code'
      # 禁用指标查找
      disableMetricsLookup: false
      # 启用增量查询
      incrementalQuerying: true
      # 增量查询覆盖时间
      incrementalQueryOverlapWindow: '10m'
      # 启用示例查询
      exemplarTraceIdDestinations:
        - name: trace_id
          datasourceUid: jaeger
    # 基本认证（如果需要）
    # basicAuth: false
    # basicAuthUser: admin
    # secureJsonData:
    #   basicAuthPassword: password
