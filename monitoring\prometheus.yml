# Prometheus 配置文件
# 基于Context7 MCP最佳实践配置

global:
  # 全局抓取间隔：15秒
  scrape_interval: 15s
  # 评估间隔：15秒
  evaluation_interval: 15s
  # 外部标签
  external_labels:
    monitor: 'axum-tutorial-monitor'
    environment: 'development'

# 规则文件配置
rule_files:
  - "dragonflydb-alerts.yml"
  - "axum-alerts.yml"

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 15s
    metrics_path: /metrics

  # Axum应用监控
  - job_name: 'axum-tutorial'
    static_configs:
      # 本机Windows环境的Axum应用
      - targets: ['host.docker.internal:3000']
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 10s
    # 添加标签
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'axum-tutorial-app'
      - target_label: service
        replacement: 'axum-tutorial'
      - target_label: environment
        replacement: 'development'

  # PostgreSQL监控（如果启用了postgres_exporter）
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s
    # 暂时禁用，因为没有postgres_exporter
    # metrics_path: /metrics

  # DragonflyDB监控 (通过redis_exporter)
  - job_name: 'dragonflydb'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 15s
    metrics_path: /metrics
    scrape_timeout: 10s
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        replacement: 'dragonflydb-cache'
      - target_label: service
        replacement: 'dragonflydb'
      - target_label: environment
        replacement: 'development'

  # DragonflyDB直接HTTP监控 (如果支持)
  - job_name: 'dragonflydb-direct'
    static_configs:
      - targets: ['dragonflydb:6379']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s
    # DragonflyDB原生HTTP指标支持
    scheme: http

  # 系统监控（如果启用了node_exporter）
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['host.docker.internal:9100']
    scrape_interval: 15s
    # 暂时禁用，因为没有node_exporter
    # metrics_path: /metrics

# 告警管理器配置（可选）
# alerting:
#   alertmanagers:
#     - static_configs:
#         - targets:
#           - alertmanager:9093

# 注意：存储和Web配置通过命令行参数设置
# --storage.tsdb.retention.time=200h
# --web.enable-lifecycle
# --web.console.libraries=/etc/prometheus/console_libraries
# --web.console.templates=/etc/prometheus/consoles
