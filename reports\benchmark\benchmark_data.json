{"groups": {"json操作": {"benchmarks": {"json反序列化": {"formatted_time": "2.80 μs", "mean_time_ns": 2797.5600878194236, "name": "json反序列化", "performance_rating": "🟡 良好", "std_deviation_ns": 545.1620458673727, "throughput": null, "unit": "ns"}, "json序列化": {"formatted_time": "833.06 ns", "mean_time_ns": 833.0581111457726, "name": "json序列化", "performance_rating": "🟢 优秀", "std_deviation_ns": 161.60886213334317, "throughput": null, "unit": "ns"}}, "description": "json操作相关性能测试", "name": "json操作"}, "websocket内存使用模拟": {"benchmarks": {"消息缓冲区分配": {"formatted_time": "0.00 ns", "mean_time_ns": 0.0, "name": "消息缓冲区分配", "performance_rating": "🟢 优秀", "std_deviation_ns": 0.0, "throughput": null, "unit": "ns"}}, "description": "WebSocket相关功能性能测试", "name": "websocket内存使用模拟"}, "websocket并发处理模拟": {"benchmarks": {"并发消息处理": {"formatted_time": "0.00 ns", "mean_time_ns": 0.0, "name": "并发消息处理", "performance_rating": "🟢 优秀", "std_deviation_ns": 0.0, "throughput": null, "unit": "ns"}}, "description": "WebSocket相关功能性能测试", "name": "websocket并发处理模拟"}, "websocket心跳机制模拟": {"benchmarks": {"心跳响应处理": {"formatted_time": "39.01 μs", "mean_time_ns": 39008.39028850227, "name": "心跳响应处理", "performance_rating": "🟡 良好", "std_deviation_ns": 18638.27188898046, "throughput": null, "unit": "ns"}, "心跳消息生成": {"formatted_time": "1.56 μs", "mean_time_ns": 1562.9418678905183, "name": "心跳消息生成", "performance_rating": "🟡 良好", "std_deviation_ns": 136.41783340688227, "throughput": null, "unit": "ns"}, "连接状态检查": {"formatted_time": "192.99 ns", "mean_time_ns": 192.98722440057628, "name": "连接状态检查", "performance_rating": "🟢 优秀", "std_deviation_ns": 6.419736076668527, "throughput": null, "unit": "ns"}}, "description": "WebSocket相关功能性能测试", "name": "websocket心跳机制模拟"}, "websocket消息处理模拟": {"benchmarks": {"消息反序列化": {"formatted_time": "1.26 μs", "mean_time_ns": 1258.1976435940776, "name": "消息反序列化", "performance_rating": "🟡 良好", "std_deviation_ns": 268.0218853096806, "throughput": null, "unit": "ns"}, "消息序列化": {"formatted_time": "2.19 μs", "mean_time_ns": 2185.5453062097736, "name": "消息序列化", "performance_rating": "🟡 良好", "std_deviation_ns": 475.3799990686205, "throughput": null, "unit": "ns"}, "消息路由模拟": {"formatted_time": "421.76 μs", "mean_time_ns": 421762.7970832404, "name": "消息路由模拟", "performance_rating": "🟡 良好", "std_deviation_ns": 244770.53559858364, "throughput": null, "unit": "ns"}}, "description": "WebSocket相关功能性能测试", "name": "websocket消息处理模拟"}, "websocket连接模拟": {"benchmarks": {"websocket url构建": {"formatted_time": "479.49 ns", "mean_time_ns": 479.4913831307561, "name": "websocket url构建", "performance_rating": "🟢 优秀", "std_deviation_ns": 57.62766896488738, "throughput": null, "unit": "ns"}, "认证验证模拟": {"formatted_time": "185.73 ns", "mean_time_ns": 185.72584120841307, "name": "认证验证模拟", "performance_rating": "🟢 优秀", "std_deviation_ns": 18.318431066504683, "throughput": null, "unit": "ns"}, "连接握手模拟": {"formatted_time": "535.70 μs", "mean_time_ns": 535698.2257609249, "name": "连接握手模拟", "performance_rating": "🟡 良好", "std_deviation_ns": 40047.180380569174, "throughput": null, "unit": "ns"}}, "description": "WebSocket相关功能性能测试", "name": "websocket连接模拟"}, "向量操作": {"benchmarks": {"向量创建和填充": {"formatted_time": "3.49 μs", "mean_time_ns": 3489.797062200631, "name": "向量创建和填充", "performance_rating": "🟡 良好", "std_deviation_ns": 339.29566983434887, "throughput": null, "unit": "ns"}, "向量排序": {"formatted_time": "1.03 μs", "mean_time_ns": 1030.8744951438682, "name": "向量排序", "performance_rating": "🟡 良好", "std_deviation_ns": 198.01471063472556, "throughput": null, "unit": "ns"}}, "description": "向量数据结构操作性能测试，包括创建、排序等", "name": "向量操作"}, "字符串操作": {"benchmarks": {"字符串格式化": {"formatted_time": "134.18 ns", "mean_time_ns": 134.17993140804978, "name": "字符串格式化", "performance_rating": "🟢 优秀", "std_deviation_ns": 18.26474364149842, "throughput": null, "unit": "ns"}, "字符串连接": {"formatted_time": "12.49 μs", "mean_time_ns": 12489.96309035943, "name": "字符串连接", "performance_rating": "🟡 良好", "std_deviation_ns": 1216.1731967394428, "throughput": null, "unit": "ns"}}, "description": "字符串处理性能测试，包括连接、格式化等操作", "name": "字符串操作"}, "异步操作": {"benchmarks": {"同步睡眠": {"formatted_time": "1.88 ms", "mean_time_ns": 1880679.336616502, "name": "同步睡眠", "performance_rating": "🔴 需优化", "std_deviation_ns": 128752.56907691911, "throughput": null, "unit": "ns"}, "简单计算": {"formatted_time": "0.71 ns", "mean_time_ns": 0.7076181453811242, "name": "简单计算", "performance_rating": "🟢 优秀", "std_deviation_ns": 0.09962567074738278, "throughput": null, "unit": "ns"}}, "description": "异步编程相关操作性能测试", "name": "异步操作"}, "简单数学运算": {"benchmarks": {"乘法运算": {"formatted_time": "1.46 ns", "mean_time_ns": 1.4582794724602823, "name": "乘法运算", "performance_rating": "🟢 优秀", "std_deviation_ns": 0.3712019383057802, "throughput": null, "unit": "ns"}, "加法运算": {"formatted_time": "1.42 ns", "mean_time_ns": 1.4166724812464455, "name": "加法运算", "performance_rating": "🟢 优秀", "std_deviation_ns": 0.33266230340345554, "throughput": null, "unit": "ns"}, "斐波那契数列(n=20)": {"formatted_time": "28.81 μs", "mean_time_ns": 28809.164245810065, "name": "斐波那契数列(n=20)", "performance_rating": "🟡 良好", "std_deviation_ns": 6162.463130810897, "throughput": null, "unit": "ns"}}, "description": "基础数学运算性能测试，包括加法、乘法和递归算法", "name": "简单数学运算"}}, "metadata": {"axum_version": "0.8.4", "generated_at": "2025-07-15T02:06:19.980006300+00:00", "rust_edition": "2024", "test_framework": "Criterion.rs", "total_benchmarks": 22, "total_groups": 10}}