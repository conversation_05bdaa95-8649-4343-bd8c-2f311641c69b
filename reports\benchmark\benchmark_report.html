
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Axum项目基准测试报告</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }
        .summary {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .benchmark-group {
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .group-header {
            background: #3498db;
            color: white;
            padding: 15px;
            font-weight: bold;
        }
        .group-content {
            padding: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .performance-good { color: #27ae60; }
        .performance-average { color: #f39c12; }
        .performance-poor { color: #e74c3c; }
        .timestamp {
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Axum项目基准测试报告</h1>

        <div class="summary">
            <h2>📊 测试概览</h2>
            <p><strong>生成时间:</strong> 2025-07-15 02:06:19 UTC</p>
            <p><strong>测试组数量:</strong> 10</p>
            <p><strong>总测试数量:</strong> 22</p>
            <p><strong>Axum版本:</strong> 0.8.4</p>
            <p><strong>Rust版本:</strong> edition = "2024"</p>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                简单数学运算 - 基础数学运算性能测试，包括加法、乘法和递归算法
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>斐波那契数列(n=20)</td>
                            <td>28.81 μs</td>
                            <td>6.16 μs</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                        <tr>
                            <td>乘法运算</td>
                            <td>1.46 ns</td>
                            <td>0.37 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                        <tr>
                            <td>加法运算</td>
                            <td>1.42 ns</td>
                            <td>0.33 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                json操作 - json操作相关性能测试
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>json序列化</td>
                            <td>833.06 ns</td>
                            <td>161.61 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                        <tr>
                            <td>json反序列化</td>
                            <td>2.80 μs</td>
                            <td>545.16 ns</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                向量操作 - 向量数据结构操作性能测试，包括创建、排序等
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>向量排序</td>
                            <td>1.03 μs</td>
                            <td>198.01 ns</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                        <tr>
                            <td>向量创建和填充</td>
                            <td>3.49 μs</td>
                            <td>339.30 ns</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                websocket心跳机制模拟 - WebSocket相关功能性能测试
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>连接状态检查</td>
                            <td>192.99 ns</td>
                            <td>6.42 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                        <tr>
                            <td>心跳响应处理</td>
                            <td>39.01 μs</td>
                            <td>18.64 μs</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                        <tr>
                            <td>心跳消息生成</td>
                            <td>1.56 μs</td>
                            <td>136.42 ns</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                websocket内存使用模拟 - WebSocket相关功能性能测试
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>消息缓冲区分配</td>
                            <td>0.00 ns</td>
                            <td>0.00 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                websocket消息处理模拟 - WebSocket相关功能性能测试
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>消息反序列化</td>
                            <td>1.26 μs</td>
                            <td>268.02 ns</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                        <tr>
                            <td>消息序列化</td>
                            <td>2.19 μs</td>
                            <td>475.38 ns</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                        <tr>
                            <td>消息路由模拟</td>
                            <td>421.76 μs</td>
                            <td>244.77 μs</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                websocket并发处理模拟 - WebSocket相关功能性能测试
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>并发消息处理</td>
                            <td>0.00 ns</td>
                            <td>0.00 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                字符串操作 - 字符串处理性能测试，包括连接、格式化等操作
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>字符串格式化</td>
                            <td>134.18 ns</td>
                            <td>18.26 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                        <tr>
                            <td>字符串连接</td>
                            <td>12.49 μs</td>
                            <td>1.22 μs</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                异步操作 - 异步编程相关操作性能测试
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>同步睡眠</td>
                            <td>1.88 ms</td>
                            <td>128.75 μs</td>
                            <td class="performance-poor">🔴 需优化</td>
                        </tr>

                        <tr>
                            <td>简单计算</td>
                            <td>0.71 ns</td>
                            <td>0.10 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="benchmark-group">
            <div class="group-header">
                websocket连接模拟 - WebSocket相关功能性能测试
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>

                        <tr>
                            <td>认证验证模拟</td>
                            <td>185.73 ns</td>
                            <td>18.32 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                        <tr>
                            <td>连接握手模拟</td>
                            <td>535.70 μs</td>
                            <td>40.05 μs</td>
                            <td class="performance-average">🟡 良好</td>
                        </tr>

                        <tr>
                            <td>websocket url构建</td>
                            <td>479.49 ns</td>
                            <td>57.63 ns</td>
                            <td class="performance-good">🟢 优秀</td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </div>

        <div class="timestamp">
            <p>此报告由Axum项目基准测试系统自动生成</p>
            <p>基于Criterion.rs基准测试框架</p>
        </div>
    </div>
</body>
</html>
