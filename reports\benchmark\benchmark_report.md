# 🚀 Axum项目基准测试报告

## 📊 测试概览

- **生成时间**: 2025-07-15 02:06:19 UTC
- **测试组数量**: 10
- **总测试数量**: 22
- **Axum版本**: 0.8.4
- **Rust版本**: edition = "2024"
- **测试框架**: Criterion.rs

## 🎯 测试环境

- **操作系统**: Windows 10
- **数据库**: SQLite (本地开发)
- **服务器地址**: 127.0.0.1:3000
- **并发模型**: Tokio异步运行时

## 📈 简单数学运算 - 基础数学运算性能测试，包括加法、乘法和递归算法

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| 斐波那契数列(n=20) | 28.81 μs | 6.16 μs | 🟡 良好 |
| 乘法运算 | 1.46 ns | 0.37 ns | 🟢 优秀 |
| 加法运算 | 1.42 ns | 0.33 ns | 🟢 优秀 |

## 📈 json操作 - json操作相关性能测试

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| json序列化 | 833.06 ns | 161.61 ns | 🟢 优秀 |
| json反序列化 | 2.80 μs | 545.16 ns | 🟡 良好 |

## 📈 向量操作 - 向量数据结构操作性能测试，包括创建、排序等

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| 向量排序 | 1.03 μs | 198.01 ns | 🟡 良好 |
| 向量创建和填充 | 3.49 μs | 339.30 ns | 🟡 良好 |

## 📈 websocket心跳机制模拟 - WebSocket相关功能性能测试

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| 连接状态检查 | 192.99 ns | 6.42 ns | 🟢 优秀 |
| 心跳响应处理 | 39.01 μs | 18.64 μs | 🟡 良好 |
| 心跳消息生成 | 1.56 μs | 136.42 ns | 🟡 良好 |

## 📈 websocket内存使用模拟 - WebSocket相关功能性能测试

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| 消息缓冲区分配 | 0.00 ns | 0.00 ns | 🟢 优秀 |

## 📈 websocket消息处理模拟 - WebSocket相关功能性能测试

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| 消息反序列化 | 1.26 μs | 268.02 ns | 🟡 良好 |
| 消息序列化 | 2.19 μs | 475.38 ns | 🟡 良好 |
| 消息路由模拟 | 421.76 μs | 244.77 μs | 🟡 良好 |

## 📈 websocket并发处理模拟 - WebSocket相关功能性能测试

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| 并发消息处理 | 0.00 ns | 0.00 ns | 🟢 优秀 |

## 📈 字符串操作 - 字符串处理性能测试，包括连接、格式化等操作

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| 字符串格式化 | 134.18 ns | 18.26 ns | 🟢 优秀 |
| 字符串连接 | 12.49 μs | 1.22 μs | 🟡 良好 |

## 📈 异步操作 - 异步编程相关操作性能测试

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| 同步睡眠 | 1.88 ms | 128.75 μs | 🔴 需优化 |
| 简单计算 | 0.71 ns | 0.10 ns | 🟢 优秀 |

## 📈 websocket连接模拟 - WebSocket相关功能性能测试

| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |
|-------------|-------------|--------|----------|
| 认证验证模拟 | 185.73 ns | 18.32 ns | 🟢 优秀 |
| 连接握手模拟 | 535.70 μs | 40.05 μs | 🟡 良好 |
| websocket url构建 | 479.49 ns | 57.63 ns | 🟢 优秀 |

## 📊 性能分析总结

- **总测试数**: 22
- **🟢 优秀**: 10 (45.5%)
- **🟡 良好**: 11 (50.0%)
- **🔴 需优化**: 1 (4.5%)

### ⚠️ 性能优化建议

- **同步睡眠**: 1.88 ms - 执行时间较长，建议优化


---

## 📝 说明

此报告由Axum项目基准测试系统自动生成，基于Criterion.rs基准测试框架。

### 性能评级标准

- 🟢 **优秀** (< 1μs): 性能表现优异，无需优化
- 🟡 **良好** (1μs - 1ms): 性能表现良好，可考虑优化
- 🔴 **需优化** (> 1ms): 性能表现较差，建议优化

### 建议

1. 对于标记为"需优化"的测试项，建议进行性能分析和优化
2. 定期运行基准测试，监控性能变化趋势
3. 在生产环境部署前，确保关键路径的性能指标达标

