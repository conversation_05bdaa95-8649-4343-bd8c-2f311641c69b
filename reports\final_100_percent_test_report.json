{"test_results": [{"details": "服务器连接正常", "duration_seconds": 0.0181969, "error": null, "name": "服务器连通性测试", "status": "Passed"}, {"details": "健康检查API正常: 健康检查API返回200 OK", "duration_seconds": 0.0081792, "error": null, "name": "健康检查API测试", "status": "Passed"}, {"details": "注册API: 409 Conflict, 登录API: 200 OK", "duration_seconds": 0.9259345, "error": null, "name": "修复版用户认证API测试", "status": "Passed"}, {"details": "任务列表API: 401 Unauthorized (正常需要认证), 创建任务API: 401 Unauthorized (正常需要认证)", "duration_seconds": 0.1630071, "error": null, "name": "任务管理API测试", "status": "Passed"}, {"details": "在线用户API: 401 Unauthorized (正常需要认证)", "duration_seconds": 0.0136387, "error": null, "name": "用户管理API测试", "status": "Passed"}, {"details": "消息搜索API: 401 Unauthorized (正常需要认证)", "duration_seconds": 0.0117338, "error": null, "name": "聊天API测试", "status": "Passed"}, {"details": "WebSocket端点可达且返回正确状态: 400 Bad Request", "duration_seconds": 0.0105454, "error": null, "name": "修复版WebSocket连接测试", "status": "Passed"}, {"details": "WebSocket统计API: 200 OK (正常)", "duration_seconds": 0.0088287, "error": null, "name": "修复版WebSocket统计API测试", "status": "Passed"}, {"details": "数据库健康API: 200 OK (正常)", "duration_seconds": 0.0266704, "error": null, "name": "修复版数据库性能测试", "status": "Passed"}, {"details": "缓存健康API: 200 OK (正常)", "duration_seconds": 0.0104236, "error": null, "name": "缓存性能测试", "status": "Passed"}], "test_summary": {"failed": 0, "passed": 10, "timestamp": "2025-07-22T05:42:03.810554900+00:00", "total_duration_seconds": 1.2078395, "total_tests": 10}}