{"test_results": [{"details": "PostgreSQL和DragonflyDB容器正常运行", "duration_seconds": 1.6830142000000001, "error": null, "name": "容器环境状态验证", "status": "Passed"}, {"details": "项目编译成功", "duration_seconds": 74.2984037, "error": null, "name": "项目编译状态验证", "status": "Passed"}, {"details": ".env配置文件存在", "duration_seconds": 0.0003136, "error": null, "name": "基础配置验证", "status": "Passed"}, {"details": "需要服务器运行", "duration_seconds": 0.0001748, "error": "服务器未启动", "name": "认证API功能测试", "status": "Skipped"}, {"details": "需要服务器运行", "duration_seconds": 0.0001783, "error": "服务器未启动", "name": "任务管理API功能测试", "status": "Skipped"}, {"details": "需要服务器运行", "duration_seconds": 0.0001633, "error": "服务器未启动", "name": "用户管理API功能测试", "status": "Skipped"}, {"details": "需要服务器运行", "duration_seconds": 0.0001658, "error": "服务器未启动", "name": "聊天功能API测试", "status": "Skipped"}, {"details": "需要服务器运行", "duration_seconds": 0.0001512, "error": "服务器未启动", "name": "WebSocket功能测试", "status": "Skipped"}, {"details": "需要服务器运行", "duration_seconds": 0.000168, "error": "服务器未启动", "name": "数据库性能测试", "status": "Skipped"}, {"details": "需要服务器运行", "duration_seconds": 0.0001562, "error": "服务器未启动", "name": "缓存性能测试", "status": "Skipped"}, {"details": "需要服务器运行", "duration_seconds": 0.0001712, "error": "服务器未启动", "name": "并发API性能测试", "status": "Skipped"}, {"details": "需要服务器运行", "duration_seconds": 0.0001913, "error": "服务器未启动", "name": "WebSocket并发测试", "status": "Skipped"}, {"details": "Clippy检查完成，发现1个警告", "duration_seconds": 16.1924247, "error": null, "name": "代码质量检查", "status": "Passed"}, {"details": "测试覆盖率验证失败", "duration_seconds": 170.4944735, "error": "测试执行失败: warning: D:\\ceshi\\ceshi\\axum-tutorial\\Cargo.toml: file `D:\\ceshi\\ceshi\\axum-tutorial\\tests\\final_acceptance_test.rs` found to be present in multiple build targets:\n  * `bin` target `final_acceptance_test`\n  * `integration-test` target `final_acceptance_test`\n   Compiling ring v0.17.14\n   Compiling env_filter v0.1.3\n   Compiling aho-corasick v1.1.3\n   Compiling pathfinder_simd v0.5.5\n   Compiling fdeflate v0.3.7\n   Compiling color_quant v1.1.0\n   Compiling wio v0.2.2\n   Compiling weezl v0.1.10\n   Compiling env_logger v0.11.8\n   Compiling bytemuck v1.23.1\n   Compiling png v0.17.16\n   Compiling jpeg-decoder v0.3.2\n   Compiling gif v0.12.0\n   Compiling dwrote v0.11.3\n   Compiling float-ord v0.3.2\n   Compiling ttf-parser v0.20.0\n   Compiling regex-automata v0.4.9\n   Compiling image v0.24.9\n   Compiling rustls v0.21.12\n   Compiling rustls v0.23.29\n   Compiling pathfinder_geometry v0.5.1\n   Compiling hyper-named-pipe v0.1.0\n   Compiling env_logger v0.8.4\n   Compiling font-kit v0.14.3\n   Compiling mockito v1.7.0\n   Compiling quickcheck v1.0.3\n   Compiling regex v1.11.1\n   Compiling jsonwebtoken v9.3.1\n   Compiling rustls-webpki v0.101.7\n   Compiling sct v0.7.1\n   Compiling rustls-webpki v0.103.4\n   Compiling app_common v0.1.0 (D:\\ceshi\\ceshi\\axum-tutorial\\crates\\app_common)\n   Compiling plotters-bitmap v0.3.7\n   Compiling parse-display-derive v0.9.1\n   Compiling plotters v0.3.7\n   Compiling tokio-rustls v0.24.1\n   Compiling fred v6.3.2\n   Compiling app_domain v0.1.0 (D:\\ceshi\\ceshi\\axum-tutorial\\crates\\app_domain)\n   Compiling rstest_macros v0.23.0\n   Compiling test-log v0.2.18\n   Compiling tracing-test v0.2.5\n   Compiling parse-display v0.9.1\nwarning: unused import: `std::sync::Arc`\n --> crates\\app_domain\\src\\events\\event_publisher.rs:8:5\n  |\n8 | use std::sync::Arc;\n  |     ^^^^^^^^^^^^^^\n  |\n  = note: `#[warn(unused_imports)]` on by default\n\nwarning: unused imports: `error` and `warn`\n --> crates\\app_domain\\src\\events\\event_publisher.rs:9:35\n  |\n9 | use app_common::tracing::{ debug, error, info, warn };\n  |                                   ^^^^^        ^^^^\n\nwarning: unused imports: `ColumnTrait`, `Condition`, `EntityTrait`, `Order`, `QueryFilter as SeaOrmQueryFilter`, and `Select`\n  --> crates\\app_domain\\src\\repositories\\query_builder.rs:31:15\n   |\n31 | use sea_orm::{ColumnTrait, Condition, EntityTrait, Order, QueryFilter as SeaOrmQueryFilter, QueryOrder, Select};\n   |               ^^^^^^^^^^^  ^^^^^^^^^  ^^^^^^^^^^^  ^^^^^  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^              ^^^^^^\n\nwarning: unused import: `std::collections::HashMap`\n  --> crates\\app_domain\\src\\repositories\\query_builder.rs:33:5\n   |\n33 | use std::collections::HashMap;\n   |     ^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: unused import: `SortOrder`\n  --> crates\\app_domain\\src\\repositories\\pagination.rs:32:5\n   |\n32 |     SortOrder,\n   |     ^^^^^^^^^\n\nwarning: unused imports: `EntityTrait` and `Select`\n  --> crates\\app_domain\\src\\repositories\\pagination.rs:35:23\n   |\n35 | use sea_orm::{ DbErr, EntityTrait, PaginatorTrait, Select };\n   |                       ^^^^^^^^^^^                  ^^^^^^\n\nwarning: unused import: `uuid::Uuid`\n  --> crates\\app_domain\\src\\repositories\\pagination.rs:38:5\n   |\n38 | use uuid::Uuid;\n   |     ^^^^^^^^^^\n\n   Compiling criterion v0.6.0\n   Compiling wiremock v0.6.4\n   Compiling siphasher v1.0.1\nwarning: unused import: `QueryOrder`\n  --> crates\\app_domain\\src\\repositories\\query_builder.rs:31:93\n   |\n31 | use sea_orm::{ColumnTrait, Condition, EntityTrait, Order, QueryFilter as SeaOrmQueryFilter, QueryOrder, Select};\n   |                                                                                             ^^^^^^^^^^\n\nwarning: unused import: `PaginatorTrait`\n  --> crates\\app_domain\\src\\repositories\\pagination.rs:35:36\n   |\n35 | use sea_orm::{ DbErr, EntityTrait, PaginatorTrait, Select };\n   |                                    ^^^^^^^^^^^^^^\n\nwarning: field `joins` is never read\n  --> crates\\app_domain\\src\\repositories\\query_builder.rs:53:5\n   |\n41 | pub struct QueryBuilder {\n   |            ------------ field in this struct\n...\n53 |     joins: Vec<JoinConfig>,\n   |     ^^^^^\n   |\n   = note: `QueryBuilder` has derived impls for the traits `Clone` and `Debug`, but these are intentionally ignored during dead code analysis\n   = note: `#[warn(dead_code)]` on by default\n\nwarning: method `validate_password_strength` is never used\n   --> crates\\app_domain\\src\\services\\user_service.rs:204:8\n    |\n145 | impl UserDomainServiceImpl {\n    | -------------------------- method in this implementation\n...\n204 |     fn validate_password_strength(&self, password: &str) -> Result<()> {\n    |        ^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: use of `async fn` in public traits is discouraged as auto trait bounds cannot be specified\n  --> crates\\app_domain\\src\\events\\event_publisher.rs:58:5\n   |\n58 |     async fn publish<T>(&self, event: T, metadata: Option<EventMetadata>) -> Result<(), AppError>\n   |     ^^^^^\n   |\n   = note: you can suppress this lint if you plan to use the trait only in your own code, or do not care about auto traits like `Send` on the `Future`\n   = note: `#[warn(async_fn_in_trait)]` on by default\nhelp: you can alternatively desugar to a normal `fn` that returns `impl Future` and add any desired bounds such as `Send`, but these cannot be relaxed without a breaking API change\n   |\n58 ~     fn publish<T>(&self, event: T, metadata: Option<EventMetadata>) -> impl std::future::Future<Output = Result<(), AppError>> + Send\n59 |         where T: DomainEvent + serde::Serialize + Send\n60 ~     {async {\n61 |         let wrapper = EventWrapper::from_domain_event(&event, metadata).map_err(|e|\n...\n64 |         self.publish_wrapper(wrapper).await\n65 ~     } }\n   |\n\nwarning: use of `async fn` in public traits is discouraged as auto trait bounds cannot be specified\n  --> crates\\app_domain\\src\\events\\event_publisher.rs:76:5\n   |\n76 |     async fn publish_and_wait_typed<T>(\n   |     ^^^^^\n   |\n   = note: you can suppress this lint if you plan to use the trait only in your own code, or do not care about auto traits like `Send` on the `Future`\nhelp: you can alternatively desugar to a normal `fn` that returns `impl Future` and add any desired bounds such as `Send`, but these cannot be relaxed without a breaking API change\n   |\n76 ~     fn publish_and_wait_typed<T>(\n77 |         &self,\n78 |         event: T,\n79 |         metadata: Option<EventMetadata>\n80 ~     ) -> impl std::future::Future<Output = Result<(), AppError>> + Send\n81 |         where T: DomainEvent + serde::Serialize + Send\n82 ~     {async {\n83 |         let wrapper = EventWrapper::from_domain_event(&event, metadata).map_err(|e|\n...\n86 |         self.publish_and_wait(wrapper).await\n87 ~     } }\n   |\n\n   Compiling phf_shared v0.11.3\n   Compiling phf_generator v0.11.3\n   Compiling app_application v0.1.0 (D:\\ceshi\\ceshi\\axum-tutorial\\crates\\app_application)\n   Compiling phf_codegen v0.11.3\n   Compiling tokio-rustls v0.26.2\nwarning: `app_domain` (lib) generated 13 warnings (run `cargo fix --lib -p app_domain` to apply 7 suggestions)\n   Compiling hyper-rustls v0.27.7\n   Compiling phf v0.11.3\n   Compiling metrics-exporter-prometheus v0.15.3\n   Compiling bollard v0.18.1\n   Compiling rstest v0.23.0\n   Compiling parse-zoneinfo v0.3.1\n   Compiling ucd-trie v0.1.7\n   Compiling chrono-tz-build v0.3.0\n   Compiling pest v2.8.1\n   Compiling bstr v1.12.0\n   Compiling unic-common v0.9.0\n   Compiling unic-char-range v0.9.0\n   Compiling globset v0.4.16\n   Compiling unic-char-property v0.9.0\n   Compiling unic-ucd-version v0.9.0\n   Compiling chrono-tz v0.9.0\n   Compiling pest_meta v2.8.1\n   Compiling ignore v0.4.23\n   Compiling testcontainers v0.23.3\n   Compiling pest_generator v2.8.1\n   Compiling unic-ucd-segment v0.9.0\n   Compiling pest_derive v2.8.1\n   Compiling unic-segment v0.9.0\n   Compiling globwalk v0.9.1\n   Compiling slug v0.1.6\n   Compiling humansize v2.1.3\nerror[E0407]: method `validate_password_strength` is not a member of trait `UserDomainService`\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:77:5\n   |\n77 | /     async fn validate_password_strength(&self, _password: &str) -> Result<bool> {\n78 | |         Ok(true)\n79 | |     }\n   | |_____^ not a member of trait `UserDomainService`\n\nerror[E0407]: method `hash_password` is not a member of trait `UserDomainService`\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:81:5\n   |\n81 | /     async fn hash_password(&self, password: &str) -> Result<String> {\n82 | |         Ok(format!(\"hashed_{}\", password))\n83 | |     }\n   | |_____^ not a member of trait `UserDomainService`\n\nerror[E0407]: method `verify_password` is not a member of trait `UserDomainService`\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:85:5\n   |\n85 | /     async fn verify_password(&self, _password: &str, _hash: &str) -> Result<bool> {\n86 | |         Ok(true)\n87 | |     }\n   | |_____^ not a member of trait `UserDomainService`\n\nwarning: unused imports: `LoginRequest` and `RegisterRequest`\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:15:29\n   |\n15 | use app_interfaces::auth::{ RegisterRequest, LoginRequest };\n   |                             ^^^^^^^^^^^^^^^  ^^^^^^^^^^^^\n   |\n   = note: `#[warn(unused_imports)]` on by default\n\nerror[E0107]: type alias takes 1 generic argument but 2 generic arguments were supplied\n   --> crates\\app_application\\tests\\user_service_integration_test.rs:42:57\n    |\n42  |     async fn find_by_username(&self, username: &str) -> Result<Option<User>, DbErr> {\n    |                                                         ^^^^^^             ------- help: remove the unnecessary generic argument\n    |                                                         |\n    |                                                         expected 1 generic argument\n    |\nnote: type alias defined here, with 1 generic parameter: `T`\n   --> D:\\ceshi\\ceshi\\axum-tutorial\\crates\\app_common\\src\\error.rs:155:10\n    |\n155 | pub type Result<T> = std::result::Result<T, AppError>;\n    |          ^^^^^^ -\n\nerror[E0053]: method `find_by_username` has an incompatible type for trait\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:40:1\n   |\n40 | #[async_trait]\n   | ^^^^^^^^^^^^^^ expected `sea_orm::DbErr`, found `AppError`\n   |\n   = note: expected signature `fn(&'life0 MockUserRepository, &'life1 _) -> Pin<Box<(dyn std::future::Future<Output = Result<std::option::Option<User>, sea_orm::DbErr>> + Send + 'async_trait)>>`\n              found signature `fn(&'life0 MockUserRepository, &'life1 _) -> Pin<Box<(dyn std::future::Future<Output = Result<std::option::Option<User>, AppError>> + Send + 'async_trait)>>`\n   = note: this error originates in the attribute macro `async_trait` (in Nightly builds, run with -Z macro-backtrace for more info)\nhelp: change the output type to match the trait\n   |\n40 - #[async_trait]\n40 + Pin<Box<(dyn std::future::Future<Output = Result<std::option::Option<User>, sea_orm::DbErr>> + Send + 'async_trait)>>\n   |\n\nerror[E0107]: type alias takes 1 generic argument but 2 generic arguments were supplied\n   --> crates\\app_application\\tests\\user_service_integration_test.rs:46:45\n    |\n46  |     async fn find_by_id(&self, id: Uuid) -> Result<Option<User>, DbErr> {\n    |                                             ^^^^^^             ------- help: remove the unnecessary generic argument\n    |                                             |\n    |                                             expected 1 generic argument\n    |\nnote: type alias defined here, with 1 generic parameter: `T`\n   --> D:\\ceshi\\ceshi\\axum-tutorial\\crates\\app_common\\src\\error.rs:155:10\n    |\n155 | pub type Result<T> = std::result::Result<T, AppError>;\n    |          ^^^^^^ -\n\nerror[E0053]: method `find_by_id` has an incompatible type for trait\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:40:1\n   |\n40 | #[async_trait]\n   | ^^^^^^^^^^^^^^ expected `sea_orm::DbErr`, found `AppError`\n   |\n   = note: expected signature `fn(&'life0 MockUserRepository, uuid::Uuid) -> Pin<Box<(dyn std::future::Future<Output = Result<std::option::Option<User>, sea_orm::DbErr>> + Send + 'async_trait)>>`\n              found signature `fn(&'life0 MockUserRepository, uuid::Uuid) -> Pin<Box<(dyn std::future::Future<Output = Result<std::option::Option<User>, AppError>> + Send + 'async_trait)>>`\n   = note: this error originates in the attribute macro `async_trait` (in Nightly builds, run with -Z macro-backtrace for more info)\nhelp: change the output type to match the trait\n   |\n40 - #[async_trait]\n40 + Pin<Box<(dyn std::future::Future<Output = Result<std::option::Option<User>, sea_orm::DbErr>> + Send + 'async_trait)>>\n   |\n\nerror[E0107]: type alias takes 1 generic argument but 2 generic arguments were supplied\n   --> crates\\app_application\\tests\\user_service_integration_test.rs:50:43\n    |\n50  |     async fn create(&self, user: User) -> Result<User, DbErr> {\n    |                                           ^^^^^^     ------- help: remove the unnecessary generic argument\n    |                                           |\n    |                                           expected 1 generic argument\n    |\nnote: type alias defined here, with 1 generic parameter: `T`\n   --> D:\\ceshi\\ceshi\\axum-tutorial\\crates\\app_common\\src\\error.rs:155:10\n    |\n155 | pub type Result<T> = std::result::Result<T, AppError>;\n    |          ^^^^^^ -\n\nerror[E0053]: method `create` has an incompatible type for trait\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:40:1\n   |\n40 | #[async_trait]\n   | ^^^^^^^^^^^^^^ expected `sea_orm::DbErr`, found `AppError`\n   |\n   = note: expected signature `fn(&'life0 MockUserRepository, User) -> Pin<Box<(dyn std::future::Future<Output = Result<User, sea_orm::DbErr>> + Send + 'async_trait)>>`\n              found signature `fn(&'life0 MockUserRepository, User) -> Pin<Box<(dyn std::future::Future<Output = Result<User, AppError>> + Send + 'async_trait)>>`\n   = note: this error originates in the attribute macro `async_trait` (in Nightly builds, run with -Z macro-backtrace for more info)\nhelp: change the output type to match the trait\n   |\n40 - #[async_trait]\n40 + Pin<Box<(dyn std::future::Future<Output = Result<User, sea_orm::DbErr>> + Send + 'async_trait)>>\n   |\n\nerror[E0107]: type alias takes 1 generic argument but 2 generic arguments were supplied\n   --> crates\\app_application\\tests\\user_service_integration_test.rs:54:43\n    |\n54  |     async fn update(&self, user: User) -> Result<User, DbErr> {\n    |                                           ^^^^^^     ------- help: remove the unnecessary generic argument\n    |                                           |\n    |                                           expected 1 generic argument\n    |\nnote: type alias defined here, with 1 generic parameter: `T`\n   --> D:\\ceshi\\ceshi\\axum-tutorial\\crates\\app_common\\src\\error.rs:155:10\n    |\n155 | pub type Result<T> = std::result::Result<T, AppError>;\n    |          ^^^^^^ -\n\nerror[E0053]: method `update` has an incompatible type for trait\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:40:1\n   |\n40 | #[async_trait]\n   | ^^^^^^^^^^^^^^ expected `sea_orm::DbErr`, found `AppError`\n   |\n   = note: expected signature `fn(&'life0 MockUserRepository, User) -> Pin<Box<(dyn std::future::Future<Output = Result<User, sea_orm::DbErr>> + Send + 'async_trait)>>`\n              found signature `fn(&'life0 MockUserRepository, User) -> Pin<Box<(dyn std::future::Future<Output = Result<User, AppError>> + Send + 'async_trait)>>`\n   = note: this error originates in the attribute macro `async_trait` (in Nightly builds, run with -Z macro-backtrace for more info)\nhelp: change the output type to match the trait\n   |\n40 - #[async_trait]\n40 + Pin<Box<(dyn std::future::Future<Output = Result<User, sea_orm::DbErr>> + Send + 'async_trait)>>\n   |\n\nerror[E0107]: type alias takes 1 generic argument but 2 generic arguments were supplied\n   --> crates\\app_application\\tests\\user_service_integration_test.rs:58:47\n    |\n58  |     async fn delete(&self, _user_id: Uuid) -> Result<u64, DbErr> {\n    |                                               ^^^^^^    ------- help: remove the unnecessary generic argument\n    |                                               |\n    |                                               expected 1 generic argument\n    |\nnote: type alias defined here, with 1 generic parameter: `T`\n   --> D:\\ceshi\\ceshi\\axum-tutorial\\crates\\app_common\\src\\error.rs:155:10\n    |\n155 | pub type Result<T> = std::result::Result<T, AppError>;\n    |          ^^^^^^ -\n\nerror[E0053]: method `delete` has an incompatible type for trait\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:40:1\n   |\n40 | #[async_trait]\n   | ^^^^^^^^^^^^^^ expected `sea_orm::DbErr`, found `AppError`\n   |\n   = note: expected signature `fn(&'life0 MockUserRepository, uuid::Uuid) -> Pin<Box<(dyn std::future::Future<Output = Result<u64, sea_orm::DbErr>> + Send + 'async_trait)>>`\n              found signature `fn(&'life0 MockUserRepository, uuid::Uuid) -> Pin<Box<(dyn std::future::Future<Output = Result<u64, AppError>> + Send + 'async_trait)>>`\n   = note: this error originates in the attribute macro `async_trait` (in Nightly builds, run with -Z macro-backtrace for more info)\nhelp: change the output type to match the trait\n   |\n40 - #[async_trait]\n40 + Pin<Box<(dyn std::future::Future<Output = Result<u64, sea_orm::DbErr>> + Send + 'async_trait)>>\n   |\n\nerror[E0107]: type alias takes 1 generic argument but 2 generic arguments were supplied\n   --> crates\\app_application\\tests\\user_service_integration_test.rs:62:34\n    |\n62  |     async fn count_all(&self) -> Result<u64, DbErr> {\n    |                                  ^^^^^^    ------- help: remove the unnecessary generic argument\n    |                                  |\n    |                                  expected 1 generic argument\n    |\nnote: type alias defined here, with 1 generic parameter: `T`\n   --> D:\\ceshi\\ceshi\\axum-tutorial\\crates\\app_common\\src\\error.rs:155:10\n    |\n155 | pub type Result<T> = std::result::Result<T, AppError>;\n    |          ^^^^^^ -\n\nerror[E0053]: method `count_all` has an incompatible type for trait\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:40:1\n   |\n40 | #[async_trait]\n   | ^^^^^^^^^^^^^^ expected `sea_orm::DbErr`, found `AppError`\n   |\n   = note: expected signature `fn(&'life0 MockUserRepository) -> Pin<Box<(dyn std::future::Future<Output = Result<u64, sea_orm::DbErr>> + Send + 'async_trait)>>`\n              found signature `fn(&'life0 MockUserRepository) -> Pin<Box<(dyn std::future::Future<Output = Result<u64, AppError>> + Send + 'async_trait)>>`\n   = note: this error originates in the attribute macro `async_trait` (in Nightly builds, run with -Z macro-backtrace for more info)\nhelp: change the output type to match the trait\n   |\n40 - #[async_trait]\n40 + Pin<Box<(dyn std::future::Future<Output = Result<u64, sea_orm::DbErr>> + Send + 'async_trait)>>\n   |\n\nerror[E0046]: not all trait items implemented, missing: `validate_user`, `get_user_by_id`, `get_user_by_username`, `create_user`, `update_user`, `delete_user`, `has_permission`\n  --> crates\\app_application\\tests\\user_service_integration_test.rs:72:1\n   |\n72 | impl UserDomainService for MockUserDomainService {\n   | ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^ missing `validate_user`, `get_user_by_id`, `get_user_by_username`, `create_user`, `update_user`, `delete_user`, `has_permission` in implementation\n   |\n   = help: implement the missing item: `fn validate_user(&'life0 self, _: &'life1 User) -> Pin<Box<(dyn std::future::Future<Output = Result<(), AppError>> + Send + 'async_trait)>> { todo!() }`\n   = help: implement the missing item: `fn get_user_by_id(&'life0 self, _: uuid::Uuid) -> Pin<Box<(dyn std::future::Future<Output = Result<std::option::Option<User>, AppError>> + Send + 'async_trait)>> { todo!() }`\n   = help: implement the missing item: `fn get_user_by_username(&'life0 self, _: &'life1 str) -> Pin<Box<(dyn std::future::Future<Output = Result<std::option::Option<User>, AppError>> + Send + 'async_trait)>> { todo!() }`\n   = help: implement the missing item: `fn create_user(&'life0 self, _: User) -> Pin<Box<(dyn std::future::Future<Output = Result<User, AppError>> + Send + 'async_trait)>> { todo!() }`\n   = help: implement the missing item: `fn update_user(&'life0 self, _: User) -> Pin<Box<(dyn std::future::Future<Output = Result<User, AppError>> + Send + 'async_trait)>> { todo!() }`\n   = help: implement the missing item: `fn delete_user(&'life0 self, _: uuid::Uuid) -> Pin<Box<(dyn std::future::Future<Output = Result<(), AppError>> + Send + 'async_trait)>> { todo!() }`\n   = help: implement the missing item: `fn has_permission(&'life0 self, _: uuid::Uuid, _: uuid::Uuid, _: &'life1 str) -> Pin<Box<(dyn std::future::Future<Output = Result<bool, AppError>> + Send + 'async_trait)>> { todo!() }`\n\nSome errors have detailed explanations: E0046, E0053, E0107, E0407.\nFor more information about an error, try `rustc --explain E0046`.\nwarning: `app_application` (test \"user_service_integration_test\") generated 1 warning\nerror: could not compile `app_application` (test \"user_service_integration_test\") due to 16 previous errors; 1 warning emitted\nwarning: build failed, waiting for other jobs to finish...\nwarning: unused import: `message_repository::MessageRepositoryContract`\n   --> crates\\app_application\\src\\chat_service.rs:458:9\n    |\n458 |         message_repository::MessageRepositoryContract,\n    |         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n    |\n    = note: `#[warn(unused_imports)]` on by default\n\nwarning: unused import: `chrono::Duration`\n   --> crates\\app_domain\\src\\entities\\chat.rs:310:9\n    |\n310 |     use chrono::Duration;\n    |         ^^^^^^^^^^^^^^^^\n    |\n    = note: `#[warn(unused_imports)]` on by default\n\nwarning: unused import: `std::sync::Arc`\n --> crates\\app_domain\\src\\events\\event_publisher.rs:8:5\n  |\n8 | use std::sync::Arc;\n  |     ^^^^^^^^^^^^^^\n\nwarning: unused import: `basic_tests::*`\n  --> crates\\app_domain\\src\\repositories\\tests\\mod.rs:14:9\n   |\n14 | pub use basic_tests::*;\n   |         ^^^^^^^^^^^^^^\n\nwarning: unused import: `super::*`\n  --> crates\\app_domain\\src\\repositories\\tests\\mod.rs:18:9\n   |\n18 |     use super::*;\n   |         ^^^^^^^^\n\nwarning: unused import: `TestAssertions`\n  --> crates\\app_domain\\src\\repositories\\tests\\mod.rs:19:59\n   |\n19 |     use app_common::test_config::{ init_test_environment, TestAssertions };\n   |                                                           ^^^^^^^^^^^^^^\n\nwarning: function `check_user_repository_bounds` is never used\n   --> crates\\app_domain\\src\\repositories\\tests\\basic_tests.rs:221:12\n    |\n221 |         fn check_user_repository_bounds<T>()\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: function `check_task_repository_bounds` is never used\n   --> crates\\app_domain\\src\\repositories\\tests\\basic_tests.rs:227:12\n    |\n227 |         fn check_task_repository_bounds<T>()\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: function `check_chat_repository_bounds` is never used\n   --> crates\\app_domain\\src\\repositories\\tests\\basic_tests.rs:233:12\n    |\n233 |         fn check_chat_repository_bounds<T>()\n    |            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\nwarning: struct `FailingEventPublisher` is never constructed\n   --> crates\\app_domain\\src\\services\\domain_events_tests.rs:629:16\n    |\n629 |         struct FailingEventPublisher;\n    |                ^^^^^^^^^^^^^^^^^^^^^\n\nwarning: `app_domain` (lib test) generated 21 warnings (12 duplicates) (run `cargo fix --lib -p app_domain --tests` to apply 5 suggestions)\nwarning: `app_application` (lib test) generated 1 warning (run `cargo fix --lib -p app_application --tests` to apply 1 suggestion)\n", "name": "测试覆盖率验证", "status": "Failed"}, {"details": "架构合规性评分: 100%", "duration_seconds": 0.0004122, "error": null, "name": "架构合规性验证", "status": "Passed"}, {"details": "发现0个安全漏洞", "duration_seconds": 7.6806423, "error": null, "name": "安全性检查", "status": "Passed"}], "test_summary": {"failed": 1, "passed": 6, "skipped": 9, "timestamp": "2025-07-22T05:15:54.925990100+00:00", "total_duration_seconds": 270.4346936, "total_tests": 16, "warnings": 0}}