{"test_results": [{"details": "服务器连接失败", "duration_seconds": 2.1114223, "error": "服务器返回错误状态: 502 Bad Gateway", "name": "服务器连通性测试", "status": "Failed"}, {"details": "健康检查API失败", "duration_seconds": 3.2338435, "error": "健康检查API返回错误: 502 Bad Gateway", "name": "健康检查API测试", "status": "Failed"}, {"details": "注册API: 502 Bad Gateway, 登录API: 502 Bad Gateway", "duration_seconds": 6.2091184, "error": null, "name": "用户认证API测试", "status": "Passed"}, {"details": "任务列表API: 502 Bad Gateway, 创建任务API: 502 Bad Gateway", "duration_seconds": 6.3689529, "error": null, "name": "任务管理API测试", "status": "Passed"}, {"details": "在线用户API: 502 Bad Gateway, 用户资料API: 502 Bad Gateway", "duration_seconds": 5.6072988, "error": null, "name": "用户管理API测试", "status": "Passed"}, {"details": "消息列表API: 502 Bad Gateway, 消息搜索API: 502 Bad Gateway", "duration_seconds": 6.187304, "error": null, "name": "聊天API测试", "status": "Passed"}, {"details": "WebSocket连接测试跳过", "duration_seconds": 2.695373, "error": "WebSocket端点返回意外状态: 502 Bad Gateway", "name": "WebSocket连接测试", "status": "Warning"}, {"details": "WebSocket统计API: 502 Bad Gateway, 连接信息API: 502 Bad Gateway", "duration_seconds": 5.7789503, "error": null, "name": "WebSocket统计API测试", "status": "Passed"}, {"details": "数据库健康API: 502 Bad Gateway, 性能指标API: 502 Bad Gateway", "duration_seconds": 6.4225214, "error": null, "name": "数据库性能测试", "status": "Passed"}, {"details": "缓存健康API: 502 Bad Gateway, 缓存统计API: 502 Bad Gateway", "duration_seconds": 6.9830872, "error": null, "name": "缓存性能测试", "status": "Passed"}], "test_summary": {"failed": 2, "passed": 7, "skipped": 0, "timestamp": "2025-07-22T05:30:27.138966600+00:00", "total_duration_seconds": 51.6213865, "total_tests": 10, "warnings": 1}}