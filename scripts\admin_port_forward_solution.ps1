# WSL2 DragonflyDB 管理员端口转发解决方案
# 需要管理员权限运行

param(
    [ValidateSet("install", "remove", "status", "test")]
    [string]$Action = "install",
    [switch]$Permanent
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ 此脚本需要管理员权限运行" -ForegroundColor Red
    Write-Host "请以管理员身份运行PowerShell，然后重新执行此脚本" -ForegroundColor Yellow
    exit 1
}

Write-Host "🔧 WSL2 DragonflyDB 管理员端口转发解决方案" -ForegroundColor Green

function Get-WSL2IP {
    try {
        $ip = (wsl hostname -I).Trim()
        if ($ip -and $ip -ne "") {
            return $ip
        }
    } catch {
        Write-Host "⚠️ 无法获取WSL2 IP地址: $_" -ForegroundColor Yellow
    }
    return $null
}

function Install-PortForward {
    param([string]$WSL2IP)
    
    Write-Host "📡 配置端口转发: 127.0.0.1:6379 -> $WSL2IP:6379" -ForegroundColor Cyan
    
    try {
        # 删除现有规则（如果存在）
        netsh interface portproxy delete v4tov4 listenport=6379 listenaddress=127.0.0.1 2>$null
        
        # 添加新的端口转发规则
        $result = netsh interface portproxy add v4tov4 listenport=6379 listenaddress=127.0.0.1 connectport=6379 connectaddress=$WSL2IP
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 端口转发规则添加成功" -ForegroundColor Green
        } else {
            Write-Host "❌ 端口转发规则添加失败: $result" -ForegroundColor Red
            return $false
        }
        
        # 添加防火墙规则
        Write-Host "🔥 配置防火墙规则..." -ForegroundColor Cyan
        
        # 删除现有防火墙规则（如果存在）
        Remove-NetFirewallRule -DisplayName "WSL2 DragonflyDB" -ErrorAction SilentlyContinue
        
        # 添加新的防火墙规则
        New-NetFirewallRule -DisplayName "WSL2 DragonflyDB" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow | Out-Null
        Write-Host "✅ 防火墙规则添加成功" -ForegroundColor Green
        
        return $true
    } catch {
        Write-Host "❌ 配置失败: $_" -ForegroundColor Red
        return $false
    }
}

function Remove-PortForward {
    Write-Host "🗑️ 删除端口转发配置..." -ForegroundColor Yellow
    
    try {
        # 删除端口转发规则
        netsh interface portproxy delete v4tov4 listenport=6379 listenaddress=127.0.0.1 2>$null
        Write-Host "✅ 端口转发规则已删除" -ForegroundColor Green
        
        # 删除防火墙规则
        Remove-NetFirewallRule -DisplayName "WSL2 DragonflyDB" -ErrorAction SilentlyContinue
        Write-Host "✅ 防火墙规则已删除" -ForegroundColor Green
        
        return $true
    } catch {
        Write-Host "❌ 删除失败: $_" -ForegroundColor Red
        return $false
    }
}

function Show-Status {
    Write-Host "📊 当前配置状态:" -ForegroundColor Cyan
    
    # 显示端口转发规则
    Write-Host "`n🔗 端口转发规则:" -ForegroundColor Yellow
    $portProxy = netsh interface portproxy show all
    if ($portProxy -match "6379") {
        Write-Host $portProxy -ForegroundColor Green
    } else {
        Write-Host "❌ 未找到6379端口转发规则" -ForegroundColor Red
    }
    
    # 显示防火墙规则
    Write-Host "`n🔥 防火墙规则:" -ForegroundColor Yellow
    $firewallRule = Get-NetFirewallRule -DisplayName "WSL2 DragonflyDB" -ErrorAction SilentlyContinue
    if ($firewallRule) {
        Write-Host "✅ WSL2 DragonflyDB 防火墙规则存在" -ForegroundColor Green
    } else {
        Write-Host "❌ WSL2 DragonflyDB 防火墙规则不存在" -ForegroundColor Red
    }
    
    # 显示WSL2状态
    Write-Host "`n🐧 WSL2状态:" -ForegroundColor Yellow
    $wsl2IP = Get-WSL2IP
    if ($wsl2IP) {
        Write-Host "✅ WSL2 IP地址: $wsl2IP" -ForegroundColor Green
    } else {
        Write-Host "❌ 无法获取WSL2 IP地址" -ForegroundColor Red
    }
}

function Test-Connection {
    Write-Host "🧪 测试连接..." -ForegroundColor Cyan
    
    # 测试端口连接
    try {
        $testResult = Test-NetConnection -ComputerName 127.0.0.1 -Port 6379 -WarningAction SilentlyContinue
        if ($testResult.TcpTestSucceeded) {
            Write-Host "✅ 端口连接测试成功" -ForegroundColor Green
        } else {
            Write-Host "❌ 端口连接测试失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 连接测试失败: $_" -ForegroundColor Red
    }
    
    # 测试Redis连接
    Write-Host "🔍 运行Redis连接测试..." -ForegroundColor Cyan
    try {
        $testOutput = & cargo run --bin test_cache_connection 2>&1
        if ($testOutput -match "成功连接到DragonflyDB") {
            Write-Host "✅ Redis连接测试成功" -ForegroundColor Green
        } else {
            Write-Host "❌ Redis连接测试失败" -ForegroundColor Red
            Write-Host "详细信息: $testOutput" -ForegroundColor Gray
        }
    } catch {
        Write-Host "❌ 无法运行Redis连接测试: $_" -ForegroundColor Red
    }
}

function Install-PermanentSolution {
    Write-Host "⚙️ 安装永久解决方案..." -ForegroundColor Cyan
    
    # 创建自动配置脚本
    $autoScript = @"
# WSL2 DragonflyDB 自动端口转发脚本
`$wsl2IP = (wsl hostname -I).Trim()
if (`$wsl2IP) {
    netsh interface portproxy delete v4tov4 listenport=6379 listenaddress=127.0.0.1 2>`$null
    netsh interface portproxy add v4tov4 listenport=6379 listenaddress=127.0.0.1 connectport=6379 connectaddress=`$wsl2IP
    Write-Host "WSL2 DragonflyDB 端口转发已更新: `$wsl2IP"
}
"@
    
    $scriptPath = "$env:USERPROFILE\wsl2_dragonfly_autofix.ps1"
    Set-Content -Path $scriptPath -Value $autoScript -Encoding UTF8
    
    # 创建计划任务
    try {
        $action = New-ScheduledTaskAction -Execute "PowerShell" -Argument "-ExecutionPolicy Bypass -File `"$scriptPath`""
        $trigger = New-ScheduledTaskTrigger -AtStartup
        $principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest
        $settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries
        
        Register-ScheduledTask -TaskName "WSL2-DragonflyDB-PortForward" -Action $action -Trigger $trigger -Principal $principal -Settings $settings -Force | Out-Null
        
        Write-Host "✅ 永久解决方案安装成功" -ForegroundColor Green
        Write-Host "📝 自动脚本位置: $scriptPath" -ForegroundColor Gray
        Write-Host "⏰ 计划任务: WSL2-DragonflyDB-PortForward" -ForegroundColor Gray
    } catch {
        Write-Host "❌ 永久解决方案安装失败: $_" -ForegroundColor Red
    }
}

# 主逻辑
switch ($Action) {
    "install" {
        $wsl2IP = Get-WSL2IP
        if (-not $wsl2IP) {
            Write-Host "❌ 无法获取WSL2 IP地址，请确保WSL2正在运行" -ForegroundColor Red
            exit 1
        }
        
        Write-Host "🔍 检测到WSL2 IP地址: $wsl2IP" -ForegroundColor Green
        
        if (Install-PortForward -WSL2IP $wsl2IP) {
            Write-Host "🎉 端口转发配置成功！" -ForegroundColor Green
            
            if ($Permanent) {
                Install-PermanentSolution
            }
            
            Write-Host "`n📋 下一步操作:" -ForegroundColor Cyan
            Write-Host "1. 运行测试: .\scripts\admin_port_forward_solution.ps1 -Action test" -ForegroundColor White
            Write-Host "2. 启动服务器: cargo run -p axum-server" -ForegroundColor White
            Write-Host "3. 如需删除配置: .\scripts\admin_port_forward_solution.ps1 -Action remove" -ForegroundColor White
        }
    }
    
    "remove" {
        if (Remove-PortForward) {
            Write-Host "🎉 端口转发配置已删除！" -ForegroundColor Green
            
            # 删除计划任务
            Unregister-ScheduledTask -TaskName "WSL2-DragonflyDB-PortForward" -Confirm:$false -ErrorAction SilentlyContinue
            
            # 删除自动脚本
            $scriptPath = "$env:USERPROFILE\wsl2_dragonfly_autofix.ps1"
            Remove-Item -Path $scriptPath -ErrorAction SilentlyContinue
        }
    }
    
    "status" {
        Show-Status
    }
    
    "test" {
        Test-Connection
    }
}

Write-Host "`n💡 提示:" -ForegroundColor Cyan
Write-Host "• 如果问题持续存在，建议考虑升级到Windows 11或使用Docker Desktop" -ForegroundColor Gray
Write-Host "• 详细解决方案请查看: docs\WSL2_COMPREHENSIVE_SOLUTION_RESEARCH.md" -ForegroundColor Gray
