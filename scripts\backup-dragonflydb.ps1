# DragonflyDB 备份和恢复脚本
# 支持手动备份、定时备份和数据恢复

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("backup", "restore", "list", "cleanup")]
    [string]$Action,
    
    [string]$Host = "127.0.0.1",
    [int]$Port = 6379,
    [string]$BackupUser = "backup_user",
    [string]$BackupPassword = "backup_password_2025",
    [string]$BackupDir = "./backups/dragonflydb",
    [string]$RestoreFile = "",
    [int]$RetentionDays = 7,
    [switch]$Compress = $true,
    [switch]$Verbose = $false
)

# 确保备份目录存在
if (-not (Test-Path $BackupDir)) {
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
    Write-Host "创建备份目录: $BackupDir" -ForegroundColor Green
}

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        "ERROR" { Write-Host $logMessage -ForegroundColor Red }
        "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
        default { Write-Host $logMessage -ForegroundColor White }
    }
    
    # 写入日志文件
    $logFile = Join-Path $BackupDir "backup.log"
    $logMessage | Add-Content -Path $logFile
}

function Test-DragonflyConnection {
    try {
        $result = & redis-cli -h $Host -p $Port --user $BackupUser -a $BackupPassword ping 2>&1
        return $result -eq "PONG"
    } catch {
        return $false
    }
}

function Backup-DragonflyDB {
    Write-Log "开始DragonflyDB备份..." "INFO"
    
    # 检查连接
    if (-not (Test-DragonflyConnection)) {
        Write-Log "无法连接到DragonflyDB服务器" "ERROR"
        return $false
    }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupFileName = "dragonflydb_backup_$timestamp"
    $backupPath = Join-Path $BackupDir $backupFileName
    
    try {
        # 执行BGSAVE命令
        Write-Log "执行后台保存命令..." "INFO"
        $saveResult = & redis-cli -h $Host -p $Port --user $BackupUser -a $BackupPassword bgsave 2>&1
        
        if ($saveResult -match "Background saving started") {
            Write-Log "后台保存已启动，等待完成..." "INFO"
            
            # 等待保存完成
            do {
                Start-Sleep -Seconds 2
                $lastSave = & redis-cli -h $Host -p $Port --user $BackupUser -a $BackupPassword lastsave 2>&1
                $info = & redis-cli -h $Host -p $Port --user $BackupUser -a $BackupPassword info persistence 2>&1
                
                if ($Verbose) {
                    Write-Log "等待保存完成... (最后保存时间: $lastSave)" "INFO"
                }
            } while ($info -match "rdb_bgsave_in_progress:1")
            
            Write-Log "后台保存完成" "SUCCESS"
            
            # 获取RDB文件信息
            $dbDir = & redis-cli -h $Host -p $Port --user $BackupUser -a $BackupPassword config get dir 2>&1
            $dbFilename = & redis-cli -h $Host -p $Port --user $BackupUser -a $BackupPassword config get dbfilename 2>&1
            
            if ($dbDir -is [array] -and $dbDir.Length -ge 2 -and $dbFilename -is [array] -and $dbFilename.Length -ge 2) {
                $sourceFile = Join-Path $dbDir[1] $dbFilename[1]
                Write-Log "源文件路径: $sourceFile" "INFO"
                
                # 由于是容器环境，我们需要使用podman cp来复制文件
                $containerName = "axum_dragonflydb"
                $tempFile = "$backupPath.rdb"
                
                Write-Log "从容器复制备份文件..." "INFO"
                & podman cp "$containerName`:$sourceFile" $tempFile 2>&1
                
                if (Test-Path $tempFile) {
                    Write-Log "备份文件复制成功: $tempFile" "SUCCESS"
                    
                    # 压缩备份文件
                    if ($Compress) {
                        Write-Log "压缩备份文件..." "INFO"
                        $compressedFile = "$tempFile.gz"
                        & gzip $tempFile 2>&1
                        
                        if (Test-Path $compressedFile) {
                            Write-Log "备份文件压缩完成: $compressedFile" "SUCCESS"
                            $finalBackupFile = $compressedFile
                        } else {
                            Write-Log "压缩失败，使用未压缩文件" "WARN"
                            $finalBackupFile = $tempFile
                        }
                    } else {
                        $finalBackupFile = $tempFile
                    }
                    
                    # 创建备份元数据
                    $metadata = @{
                        timestamp = $timestamp
                        host = $Host
                        port = $Port
                        file = $finalBackupFile
                        size = (Get-Item $finalBackupFile).Length
                        compressed = $Compress
                    }
                    
                    $metadataFile = "$backupPath.json"
                    $metadata | ConvertTo-Json | Set-Content $metadataFile
                    
                    Write-Log "备份完成: $finalBackupFile" "SUCCESS"
                    Write-Log "元数据文件: $metadataFile" "INFO"
                    
                    return $true
                } else {
                    Write-Log "无法复制备份文件" "ERROR"
                    return $false
                }
            } else {
                Write-Log "无法获取数据库文件路径" "ERROR"
                return $false
            }
        } else {
            Write-Log "后台保存启动失败: $saveResult" "ERROR"
            return $false
        }
    } catch {
        Write-Log "备份过程中发生异常: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function List-Backups {
    Write-Log "列出可用备份..." "INFO"
    
    $backupFiles = Get-ChildItem -Path $BackupDir -Filter "*.json" | Sort-Object LastWriteTime -Descending
    
    if ($backupFiles.Count -eq 0) {
        Write-Log "未找到备份文件" "WARN"
        return
    }
    
    Write-Host "`n可用备份列表:" -ForegroundColor Cyan
    Write-Host "=" * 80 -ForegroundColor Cyan
    
    foreach ($metadataFile in $backupFiles) {
        try {
            $metadata = Get-Content $metadataFile.FullName | ConvertFrom-Json
            $backupFile = $metadata.file
            $fileExists = Test-Path $backupFile
            $sizeKB = [math]::Round($metadata.size / 1024, 2)
            
            Write-Host "时间戳: $($metadata.timestamp)" -ForegroundColor Yellow
            Write-Host "文件: $backupFile" -ForegroundColor White
            Write-Host "大小: $sizeKB KB" -ForegroundColor White
            Write-Host "压缩: $($metadata.compressed)" -ForegroundColor White
            Write-Host "状态: $(if ($fileExists) { '✓ 存在' } else { '✗ 缺失' })" -ForegroundColor $(if ($fileExists) { 'Green' } else { 'Red' })
            Write-Host "-" * 40
        } catch {
            Write-Log "无法读取元数据文件: $($metadataFile.Name)" "ERROR"
        }
    }
}

function Cleanup-OldBackups {
    Write-Log "清理旧备份文件..." "INFO"
    
    $cutoffDate = (Get-Date).AddDays(-$RetentionDays)
    $metadataFiles = Get-ChildItem -Path $BackupDir -Filter "*.json"
    $deletedCount = 0
    
    foreach ($metadataFile in $metadataFiles) {
        if ($metadataFile.LastWriteTime -lt $cutoffDate) {
            try {
                $metadata = Get-Content $metadataFile.FullName | ConvertFrom-Json
                $backupFile = $metadata.file
                
                # 删除备份文件
                if (Test-Path $backupFile) {
                    Remove-Item $backupFile -Force
                    Write-Log "删除旧备份文件: $backupFile" "INFO"
                }
                
                # 删除元数据文件
                Remove-Item $metadataFile.FullName -Force
                Write-Log "删除元数据文件: $($metadataFile.Name)" "INFO"
                
                $deletedCount++
            } catch {
                Write-Log "删除备份文件时发生错误: $($_.Exception.Message)" "ERROR"
            }
        }
    }
    
    Write-Log "清理完成，删除了 $deletedCount 个旧备份" "SUCCESS"
}

# 主逻辑
Write-Host "=== DragonflyDB 备份管理工具 ===" -ForegroundColor Green
Write-Host "操作: $Action" -ForegroundColor Yellow
Write-Host "目标: $Host`:$Port" -ForegroundColor Yellow
Write-Host "备份目录: $BackupDir" -ForegroundColor Yellow

switch ($Action) {
    "backup" {
        $success = Backup-DragonflyDB
        if ($success) {
            Write-Log "备份操作成功完成" "SUCCESS"
            exit 0
        } else {
            Write-Log "备份操作失败" "ERROR"
            exit 1
        }
    }
    
    "list" {
        List-Backups
    }
    
    "cleanup" {
        Cleanup-OldBackups
    }
    
    "restore" {
        Write-Log "恢复功能需要手动操作，请参考文档" "WARN"
        Write-Log "1. 停止DragonflyDB服务" "INFO"
        Write-Log "2. 解压备份文件到数据目录" "INFO"
        Write-Log "3. 重启DragonflyDB服务" "INFO"
    }
}

Write-Log "操作完成" "SUCCESS"
