# Podman Compose配置检查脚本

Write-Host "检查Podman Compose配置..." -ForegroundColor Cyan

# 检查必需文件
$files = @(
    "podman-compose.yml",
    "config/postgresql.conf",
    "config/pg_hba.conf",
    "scripts/init-db.sql",
    "monitoring/prometheus.yml"
)

$allFilesExist = $true
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (缺失)" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if ($allFilesExist) {
    Write-Host "✅ 所有配置文件存在" -ForegroundColor Green
} else {
    Write-Host "❌ 部分配置文件缺失" -ForegroundColor Red
    exit 1
}

# 检查数据目录
$dataDirs = @("data", "data/postgres", "data/dragonflydb", "data/prometheus", "data/grafana")
foreach ($dir in $dataDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ 创建数据目录: $dir" -ForegroundColor Green
    } else {
        Write-Host "✅ 数据目录存在: $dir" -ForegroundColor Green
    }
}

# 验证YAML语法
Write-Host "验证YAML语法..." -ForegroundColor Blue
try {
    $command = "cd /mnt/d/ceshi/ceshi/axum-tutorial; podman-compose config"
    $result = wsl -d Ubuntu bash -c $command 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ YAML语法正确" -ForegroundColor Green
    } else {
        Write-Host "❌ YAML语法错误" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "⚠️  无法验证YAML语法: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "🎉 配置验证完成！" -ForegroundColor Green
Write-Host "可以使用以下命令启动服务:" -ForegroundColor Cyan
$startCommand = "cd /mnt/d/ceshi/ceshi/axum-tutorial; podman-compose up -d"
Write-Host "wsl -d Ubuntu bash -c `"$startCommand`"" -ForegroundColor White
