# CI/CD 覆盖率检查脚本 (PowerShell版本)
# 用于自动化代码覆盖率分析和报告生成

param(
    [switch]$StrictMode = $false,
    [int]$LineThreshold = 90,
    [int]$BranchThreshold = 85,
    [int]$FunctionThreshold = 90
)

Write-Host "🚀 开始CI/CD覆盖率检查流程..." -ForegroundColor Green

# 配置参数
$ProjectRoot = Get-Location
$CoverageDir = "target\cov"
$HtmlDir = "$CoverageDir\html"
$LcovFile = "$CoverageDir\lcov.info"
$SummaryFile = "$CoverageDir\coverage_summary.md"

Write-Host "📋 项目根目录: $ProjectRoot" -ForegroundColor Cyan
Write-Host "📁 覆盖率输出目录: $CoverageDir" -ForegroundColor Cyan

# 步骤1: 清理之前的覆盖率数据
Write-Host "🧹 清理之前的覆盖率数据..." -ForegroundColor Yellow
if (Test-Path $CoverageDir) {
    Remove-Item -Recurse -Force $CoverageDir
}
New-Item -ItemType Directory -Force -Path $CoverageDir | Out-Null
New-Item -ItemType Directory -Force -Path $HtmlDir | Out-Null

# 步骤2: 检查cargo-llvm-cov安装状态
Write-Host "🔧 检查cargo-llvm-cov安装状态..." -ForegroundColor Yellow
try {
    $null = cargo llvm-cov --version 2>$null
    Write-Host "✅ cargo-llvm-cov已安装" -ForegroundColor Green
} catch {
    Write-Host "📦 安装cargo-llvm-cov..." -ForegroundColor Yellow
    cargo install cargo-llvm-cov
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ cargo-llvm-cov安装失败" -ForegroundColor Red
        exit 1
    }
}

# 步骤3: 运行覆盖率测试 (仅server包)
Write-Host "🧪 运行覆盖率测试..." -ForegroundColor Yellow
Write-Host "   目标包: server" -ForegroundColor Gray
Write-Host "   输出格式: HTML + LCOV" -ForegroundColor Gray

# 生成HTML报告
Write-Host "📊 生成HTML覆盖率报告..." -ForegroundColor Yellow
cargo llvm-cov --html --output-dir $HtmlDir -p server --ignore-run-fail
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️  HTML覆盖率测试完成，可能有部分测试失败" -ForegroundColor Yellow
}

# 生成LCOV报告
Write-Host "📋 生成LCOV覆盖率报告..." -ForegroundColor Yellow
cargo llvm-cov --lcov --output-path $LcovFile -p server --ignore-run-fail
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️  LCOV覆盖率测试完成，可能有部分测试失败" -ForegroundColor Yellow
}

# 步骤4: 解析覆盖率数据
Write-Host "🔍 解析覆盖率数据..." -ForegroundColor Yellow
if (Test-Path $LcovFile) {
    cargo run --bin parse_lcov_coverage
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ 覆盖率数据解析失败" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ LCOV文件不存在: $LcovFile" -ForegroundColor Red
    exit 1
}

# 步骤5: 验证覆盖率阈值
Write-Host "🎯 验证覆盖率阈值..." -ForegroundColor Yellow

if (Test-Path $LcovFile) {
    # 解析LCOV文件获取覆盖率数据
    $lcovContent = Get-Content $LcovFile
    
    # 计算总的行覆盖率
    $linesHit = ($lcovContent | Where-Object { $_ -match "^LH:" } | ForEach-Object { [int]($_ -split ":")[1] } | Measure-Object -Sum).Sum
    $linesFound = ($lcovContent | Where-Object { $_ -match "^LF:" } | ForEach-Object { [int]($_ -split ":")[1] } | Measure-Object -Sum).Sum
    
    # 计算总的分支覆盖率
    $branchesHit = ($lcovContent | Where-Object { $_ -match "^BRH:" } | ForEach-Object { [int]($_ -split ":")[1] } | Measure-Object -Sum).Sum
    $branchesFound = ($lcovContent | Where-Object { $_ -match "^BRF:" } | ForEach-Object { [int]($_ -split ":")[1] } | Measure-Object -Sum).Sum
    
    # 计算总的函数覆盖率
    $functionsHit = ($lcovContent | Where-Object { $_ -match "^FNH:" } | ForEach-Object { [int]($_ -split ":")[1] } | Measure-Object -Sum).Sum
    $functionsFound = ($lcovContent | Where-Object { $_ -match "^FNF:" } | ForEach-Object { [int]($_ -split ":")[1] } | Measure-Object -Sum).Sum
    
    # 计算百分比
    $lineCoverage = if ($linesFound -gt 0) { [math]::Round(($linesHit / $linesFound) * 100, 2) } else { 0 }
    $branchCoverage = if ($branchesFound -gt 0) { [math]::Round(($branchesHit / $branchesFound) * 100, 2) } else { 0 }
    $functionCoverage = if ($functionsFound -gt 0) { [math]::Round(($functionsHit / $functionsFound) * 100, 2) } else { 0 }
    
    Write-Host "📊 覆盖率统计:" -ForegroundColor Cyan
    Write-Host "   行覆盖率: $lineCoverage% ($linesHit/$linesFound)" -ForegroundColor White
    Write-Host "   分支覆盖率: $branchCoverage% ($branchesHit/$branchesFound)" -ForegroundColor White
    Write-Host "   函数覆盖率: $functionCoverage% ($functionsHit/$functionsFound)" -ForegroundColor White
    
    # 检查阈值
    $thresholdFailed = $false
    
    if ($lineCoverage -lt $LineThreshold) {
        Write-Host "❌ 行覆盖率 $lineCoverage% 低于阈值 $LineThreshold%" -ForegroundColor Red
        $thresholdFailed = $true
    } else {
        Write-Host "✅ 行覆盖率 $lineCoverage% 达到阈值 $LineThreshold%" -ForegroundColor Green
    }
    
    if ($branchCoverage -lt $BranchThreshold) {
        Write-Host "❌ 分支覆盖率 $branchCoverage% 低于阈值 $BranchThreshold%" -ForegroundColor Red
        $thresholdFailed = $true
    } else {
        Write-Host "✅ 分支覆盖率 $branchCoverage% 达到阈值 $BranchThreshold%" -ForegroundColor Green
    }
    
    if ($functionCoverage -lt $FunctionThreshold) {
        Write-Host "❌ 函数覆盖率 $functionCoverage% 低于阈值 $FunctionThreshold%" -ForegroundColor Red
        $thresholdFailed = $true
    } else {
        Write-Host "✅ 函数覆盖率 $functionCoverage% 达到阈值 $FunctionThreshold%" -ForegroundColor Green
    }
    
    # 步骤6: 生成最终报告
    Write-Host "📝 生成最终报告..." -ForegroundColor Yellow
    Write-Host "   HTML报告: $HtmlDir\index.html" -ForegroundColor Gray
    Write-Host "   LCOV报告: $LcovFile" -ForegroundColor Gray
    Write-Host "   总结报告: $SummaryFile" -ForegroundColor Gray
    
    # 步骤7: 根据阈值检查结果决定CI状态
    if ($thresholdFailed) {
        Write-Host ""
        Write-Host "🔴 CI/CD覆盖率检查失败！" -ForegroundColor Red
        Write-Host "   部分覆盖率指标未达到要求的阈值" -ForegroundColor Red
        Write-Host "   请增加测试用例以提高代码覆盖率" -ForegroundColor Red
        Write-Host ""
        Write-Host "📋 改进建议:" -ForegroundColor Yellow
        Write-Host "   1. 为未覆盖的代码行添加单元测试" -ForegroundColor White
        Write-Host "   2. 增加条件分支的测试用例" -ForegroundColor White
        Write-Host "   3. 确保所有公共函数都有对应的测试" -ForegroundColor White
        Write-Host ""
        
        if ($StrictMode -or $env:CI_STRICT_COVERAGE -eq "true") {
            Write-Host "🚫 严格模式：由于覆盖率不足，构建失败" -ForegroundColor Red
            exit 1
        } else {
            Write-Host "⚠️  宽松模式：覆盖率不足，但构建继续" -ForegroundColor Yellow
        }
    } else {
        Write-Host ""
        Write-Host "🟢 CI/CD覆盖率检查通过！" -ForegroundColor Green
        Write-Host "   所有覆盖率指标均达到要求的阈值" -ForegroundColor Green
        Write-Host ""
    }
    
} else {
    Write-Host "❌ 无法找到LCOV文件进行阈值验证" -ForegroundColor Red
    exit 1
}

Write-Host "🎉 CI/CD覆盖率检查流程完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📊 报告位置:" -ForegroundColor Cyan
Write-Host "   - HTML可视化报告: file:///$ProjectRoot/$HtmlDir/index.html" -ForegroundColor White
Write-Host "   - Markdown总结报告: $ProjectRoot\$SummaryFile" -ForegroundColor White
Write-Host "   - LCOV原始数据: $ProjectRoot\$LcovFile" -ForegroundColor White
