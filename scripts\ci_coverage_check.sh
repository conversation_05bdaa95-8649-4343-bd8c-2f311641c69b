#!/bin/bash

# CI/CD 覆盖率检查脚本
# 用于自动化代码覆盖率分析和报告生成

set -e  # 遇到错误时退出

echo "🚀 开始CI/CD覆盖率检查流程..."

# 配置参数
PROJECT_ROOT="$(pwd)"
COVERAGE_DIR="target/cov"
HTML_DIR="${COVERAGE_DIR}/html"
LCOV_FILE="${COVERAGE_DIR}/lcov.info"
SUMMARY_FILE="${COVERAGE_DIR}/coverage_summary.md"

# 覆盖率阈值
LINE_THRESHOLD=90
BRANCH_THRESHOLD=85
FUNCTION_THRESHOLD=90

echo "📋 项目根目录: ${PROJECT_ROOT}"
echo "📁 覆盖率输出目录: ${COVERAGE_DIR}"

# 步骤1: 清理之前的覆盖率数据
echo "🧹 清理之前的覆盖率数据..."
if [ -d "${COVERAGE_DIR}" ]; then
    rm -rf "${COVERAGE_DIR}"
fi
mkdir -p "${COVERAGE_DIR}"
mkdir -p "${HTML_DIR}"

# 步骤2: 安装cargo-llvm-cov (如果未安装)
echo "🔧 检查cargo-llvm-cov安装状态..."
if ! command -v cargo-llvm-cov &> /dev/null; then
    echo "📦 安装cargo-llvm-cov..."
    cargo install cargo-llvm-cov
else
    echo "✅ cargo-llvm-cov已安装"
fi

# 步骤3: 运行覆盖率测试 (仅server包)
echo "🧪 运行覆盖率测试..."
echo "   目标包: server"
echo "   输出格式: HTML + LCOV"

# 生成HTML报告
echo "📊 生成HTML覆盖率报告..."
cargo llvm-cov --html --output-dir "${HTML_DIR}" -p server --ignore-run-fail || {
    echo "⚠️  HTML覆盖率测试完成，可能有部分测试失败"
}

# 生成LCOV报告
echo "📋 生成LCOV覆盖率报告..."
cargo llvm-cov --lcov --output-path "${LCOV_FILE}" -p server --ignore-run-fail || {
    echo "⚠️  LCOV覆盖率测试完成，可能有部分测试失败"
}

# 步骤4: 解析覆盖率数据
echo "🔍 解析覆盖率数据..."
if [ -f "${LCOV_FILE}" ]; then
    cargo run --bin parse_lcov_coverage
else
    echo "❌ LCOV文件不存在: ${LCOV_FILE}"
    exit 1
fi

# 步骤5: 验证覆盖率阈值
echo "🎯 验证覆盖率阈值..."

# 从LCOV文件中提取覆盖率数据
if [ -f "${LCOV_FILE}" ]; then
    # 计算总的行覆盖率
    LINES_HIT=$(grep "^LH:" "${LCOV_FILE}" | cut -d: -f2 | awk '{sum += $1} END {print sum}')
    LINES_FOUND=$(grep "^LF:" "${LCOV_FILE}" | cut -d: -f2 | awk '{sum += $1} END {print sum}')
    
    # 计算总的分支覆盖率
    BRANCHES_HIT=$(grep "^BRH:" "${LCOV_FILE}" | cut -d: -f2 | awk '{sum += $1} END {print sum}')
    BRANCHES_FOUND=$(grep "^BRF:" "${LCOV_FILE}" | cut -d: -f2 | awk '{sum += $1} END {print sum}')
    
    # 计算总的函数覆盖率
    FUNCTIONS_HIT=$(grep "^FNH:" "${LCOV_FILE}" | cut -d: -f2 | awk '{sum += $1} END {print sum}')
    FUNCTIONS_FOUND=$(grep "^FNF:" "${LCOV_FILE}" | cut -d: -f2 | awk '{sum += $1} END {print sum}')
    
    # 计算百分比
    if [ "${LINES_FOUND}" -gt 0 ]; then
        LINE_COVERAGE=$(echo "scale=2; ${LINES_HIT} * 100 / ${LINES_FOUND}" | bc)
    else
        LINE_COVERAGE=0
    fi
    
    if [ "${BRANCHES_FOUND}" -gt 0 ]; then
        BRANCH_COVERAGE=$(echo "scale=2; ${BRANCHES_HIT} * 100 / ${BRANCHES_FOUND}" | bc)
    else
        BRANCH_COVERAGE=0
    fi
    
    if [ "${FUNCTIONS_FOUND}" -gt 0 ]; then
        FUNCTION_COVERAGE=$(echo "scale=2; ${FUNCTIONS_HIT} * 100 / ${FUNCTIONS_FOUND}" | bc)
    else
        FUNCTION_COVERAGE=0
    fi
    
    echo "📊 覆盖率统计:"
    echo "   行覆盖率: ${LINE_COVERAGE}% (${LINES_HIT}/${LINES_FOUND})"
    echo "   分支覆盖率: ${BRANCH_COVERAGE}% (${BRANCHES_HIT}/${BRANCHES_FOUND})"
    echo "   函数覆盖率: ${FUNCTION_COVERAGE}% (${FUNCTIONS_HIT}/${FUNCTIONS_FOUND})"
    
    # 检查阈值
    THRESHOLD_FAILED=0
    
    if (( $(echo "${LINE_COVERAGE} < ${LINE_THRESHOLD}" | bc -l) )); then
        echo "❌ 行覆盖率 ${LINE_COVERAGE}% 低于阈值 ${LINE_THRESHOLD}%"
        THRESHOLD_FAILED=1
    else
        echo "✅ 行覆盖率 ${LINE_COVERAGE}% 达到阈值 ${LINE_THRESHOLD}%"
    fi
    
    if (( $(echo "${BRANCH_COVERAGE} < ${BRANCH_THRESHOLD}" | bc -l) )); then
        echo "❌ 分支覆盖率 ${BRANCH_COVERAGE}% 低于阈值 ${BRANCH_THRESHOLD}%"
        THRESHOLD_FAILED=1
    else
        echo "✅ 分支覆盖率 ${BRANCH_COVERAGE}% 达到阈值 ${BRANCH_THRESHOLD}%"
    fi
    
    if (( $(echo "${FUNCTION_COVERAGE} < ${FUNCTION_THRESHOLD}" | bc -l) )); then
        echo "❌ 函数覆盖率 ${FUNCTION_COVERAGE}% 低于阈值 ${FUNCTION_THRESHOLD}%"
        THRESHOLD_FAILED=1
    else
        echo "✅ 函数覆盖率 ${FUNCTION_COVERAGE}% 达到阈值 ${FUNCTION_THRESHOLD}%"
    fi
    
    # 步骤6: 生成最终报告
    echo "📝 生成最终报告..."
    echo "   HTML报告: ${HTML_DIR}/index.html"
    echo "   LCOV报告: ${LCOV_FILE}"
    echo "   总结报告: ${SUMMARY_FILE}"
    
    # 步骤7: 根据阈值检查结果决定CI状态
    if [ "${THRESHOLD_FAILED}" -eq 1 ]; then
        echo ""
        echo "🔴 CI/CD覆盖率检查失败！"
        echo "   部分覆盖率指标未达到要求的阈值"
        echo "   请增加测试用例以提高代码覆盖率"
        echo ""
        echo "📋 改进建议:"
        echo "   1. 为未覆盖的代码行添加单元测试"
        echo "   2. 增加条件分支的测试用例"
        echo "   3. 确保所有公共函数都有对应的测试"
        echo ""
        
        # 在CI环境中，可以选择是否让构建失败
        if [ "${CI_STRICT_COVERAGE:-false}" = "true" ]; then
            echo "🚫 严格模式：由于覆盖率不足，构建失败"
            exit 1
        else
            echo "⚠️  宽松模式：覆盖率不足，但构建继续"
        fi
    else
        echo ""
        echo "🟢 CI/CD覆盖率检查通过！"
        echo "   所有覆盖率指标均达到要求的阈值"
        echo ""
    fi
    
else
    echo "❌ 无法找到LCOV文件进行阈值验证"
    exit 1
fi

echo "🎉 CI/CD覆盖率检查流程完成！"
echo ""
echo "📊 报告位置:"
echo "   - HTML可视化报告: file://${PROJECT_ROOT}/${HTML_DIR}/index.html"
echo "   - Markdown总结报告: ${PROJECT_ROOT}/${SUMMARY_FILE}"
echo "   - LCOV原始数据: ${PROJECT_ROOT}/${LCOV_FILE}"
