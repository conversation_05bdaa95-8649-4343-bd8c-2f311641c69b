# 代码质量检查脚本 - 2025年最新最佳实践
# 适用于Axum企业级项目的自动化质量检查

param(
    [switch]$Fix = $false,
    [switch]$Strict = $false,
    [switch]$InstallTools = $false,
    [switch]$UpdateTools = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "Cyan" = [ConsoleColor]::<PERSON>an
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

# 检查工具是否安装
function Test-CargoTool {
    param([string]$ToolName)

    try {
        $null = & cargo $ToolName --version 2>$null
        return $true
    }
    catch {
        return $false
    }
}
}

# 安装必需的工具
function Install-RequiredTools {
    Write-ColorOutput "🔧 安装代码质量检查工具..." "Blue"
    
    $tools = @(
        "cargo-deny",
        "cargo-audit", 
        "cargo-outdated",
        "cargo-machete",
        "cargo-udeps"
    )
    
    foreach ($tool in $tools) {
        if (-not (Test-CargoTool $tool.Replace("cargo-", ""))) {
            Write-ColorOutput "📦 安装 $tool..." "Yellow"
            cargo install $tool
        } else {
            Write-ColorOutput "✅ $tool 已安装" "Green"
        }
    }
}

# 更新工具
function Update-Tools {
    Write-ColorOutput "🔄 更新代码质量检查工具..." "Blue"
    
    $tools = @(
        "cargo-deny",
        "cargo-audit",
        "cargo-outdated", 
        "cargo-machete",
        "cargo-udeps"
    )
    
    foreach ($tool in $tools) {
        Write-ColorOutput "🔄 更新 $tool..." "Yellow"
        cargo install $tool --force
    }
}

# 主要质量检查函数
function Invoke-QualityChecks {
    Write-ColorOutput "🚀 开始代码质量检查（2025年最新标准）..." "Blue"
    
    # 1. 编译检查
    Write-ColorOutput "🔨 执行编译检查..." "Cyan"
    cargo check --all-targets --all-features --workspace
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "❌ 编译检查失败" "Red"
        exit 1
    }
    Write-ColorOutput "✅ 编译检查通过" "Green"
    
    # 2. 代码格式化检查
    Write-ColorOutput "🎨 执行代码格式化检查..." "Cyan"
    if ($Fix) {
        cargo fmt --all
        Write-ColorOutput "✅ 代码格式化已修复" "Green"
    } else {
        cargo fmt --all -- --check
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "❌ 代码格式化检查失败，请运行 'cargo fmt --all' 修复" "Red"
            exit 1
        }
        Write-ColorOutput "✅ 代码格式化检查通过" "Green"
    }
    
    # 3. Clippy静态分析
    Write-ColorOutput "🔍 执行Clippy静态分析..." "Cyan"
    $clippyArgs = @(
        "clippy",
        "--all-targets",
        "--all-features", 
        "--workspace",
        "--"
    )
    
    if ($Strict) {
        $clippyArgs += @(
            "-D", "warnings",
            "-D", "clippy::all",
            "-D", "clippy::pedantic",
            "-W", "clippy::nursery",
            "-D", "clippy::cognitive_complexity",
            "-D", "clippy::too_many_arguments",
            "-D", "clippy::type_complexity"
        )
    } else {
        $clippyArgs += @("-D", "warnings")
    }
    
    if ($Fix) {
        $clippyArgs += "--fix"
    }
    
    & cargo @clippyArgs
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "❌ Clippy检查失败" "Red"
        exit 1
    }
    Write-ColorOutput "✅ Clippy检查通过" "Green"
    
    # 4. 安全漏洞检查
    if (Test-CargoTool "deny") {
        Write-ColorOutput "🛡️ 执行安全检查..." "Cyan"
        cargo deny check --all-features
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "❌ 安全检查失败" "Red"
            exit 1
        }
        Write-ColorOutput "✅ 安全检查通过" "Green"
    } else {
        Write-ColorOutput "⚠️ cargo-deny未安装，跳过安全检查" "Yellow"
    }
    
    # 5. 漏洞审计
    if (Test-CargoTool "audit") {
        Write-ColorOutput "🔒 执行漏洞审计..." "Cyan"
        cargo audit --deny warnings
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "❌ 漏洞审计失败" "Red"
            exit 1
        }
        Write-ColorOutput "✅ 漏洞审计通过" "Green"
    } else {
        Write-ColorOutput "⚠️ cargo-audit未安装，跳过漏洞审计" "Yellow"
    }
    
    # 6. 未使用依赖检查
    if (Test-CargoTool "machete") {
        Write-ColorOutput "🔧 检查未使用的依赖..." "Cyan"
        cargo machete
        Write-ColorOutput "✅ 未使用依赖检查完成" "Green"
    } else {
        Write-ColorOutput "⚠️ cargo-machete未安装，跳过未使用依赖检查" "Yellow"
    }
    
    # 7. 过时依赖检查
    if (Test-CargoTool "outdated") {
        Write-ColorOutput "📦 检查过时的依赖..." "Cyan"
        cargo outdated --workspace
        Write-ColorOutput "✅ 过时依赖检查完成" "Green"
    } else {
        Write-ColorOutput "⚠️ cargo-outdated未安装，跳过过时依赖检查" "Yellow"
    }
    
    # 8. 单元测试
    Write-ColorOutput "🧪 执行单元测试..." "Cyan"
    cargo test --workspace --lib
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "❌ 单元测试失败" "Red"
        exit 1
    }
    Write-ColorOutput "✅ 单元测试通过" "Green"
}

# 主程序
try {
    Write-ColorOutput "🎯 Axum企业级项目代码质量检查工具 v2025.1" "Blue"
    Write-ColorOutput "================================================" "Blue"
    
    if ($InstallTools) {
        Install-RequiredTools
    }
    
    if ($UpdateTools) {
        Update-Tools
    }
    
    Invoke-QualityChecks
    
    Write-ColorOutput "🎉 所有代码质量检查通过！" "Green"
    Write-ColorOutput "================================================" "Green"
    
} catch {
    Write-ColorOutput "💥 代码质量检查过程中发生错误: $($_.Exception.Message)" "Red"
    exit 1
}
