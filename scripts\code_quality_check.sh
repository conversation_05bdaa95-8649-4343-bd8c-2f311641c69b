#!/bin/bash
# 代码质量检查脚本 - 2025年最新最佳实践
# 适用于Axum企业级项目的自动化质量检查

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 参数解析
FIX=false
STRICT=false
INSTALL_TOOLS=false
UPDATE_TOOLS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --fix)
            FIX=true
            shift
            ;;
        --strict)
            STRICT=true
            shift
            ;;
        --install-tools)
            INSTALL_TOOLS=true
            shift
            ;;
        --update-tools)
            UPDATE_TOOLS=true
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --fix           自动修复可修复的问题"
            echo "  --strict        启用严格模式检查"
            echo "  --install-tools 安装必需的工具"
            echo "  --update-tools  更新工具到最新版本"
            echo "  -h, --help      显示此帮助信息"
            exit 0
            ;;
        *)
            echo "未知选项: $1"
            exit 1
            ;;
    esac
done

# 颜色输出函数
print_color() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检查工具是否安装
check_cargo_tool() {
    local tool_name=$1
    if cargo "$tool_name" --version >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 安装必需的工具
install_required_tools() {
    print_color "$BLUE" "🔧 安装代码质量检查工具..."
    
    local tools=(
        "cargo-deny"
        "cargo-audit"
        "cargo-outdated"
        "cargo-machete"
        "cargo-udeps"
    )
    
    for tool in "${tools[@]}"; do
        local tool_cmd=${tool#cargo-}
        if ! check_cargo_tool "$tool_cmd"; then
            print_color "$YELLOW" "📦 安装 $tool..."
            cargo install "$tool"
        else
            print_color "$GREEN" "✅ $tool 已安装"
        fi
    done
}

# 更新工具
update_tools() {
    print_color "$BLUE" "🔄 更新代码质量检查工具..."
    
    local tools=(
        "cargo-deny"
        "cargo-audit"
        "cargo-outdated"
        "cargo-machete"
        "cargo-udeps"
    )
    
    for tool in "${tools[@]}"; do
        print_color "$YELLOW" "🔄 更新 $tool..."
        cargo install "$tool" --force
    done
}

# 主要质量检查函数
run_quality_checks() {
    print_color "$BLUE" "🚀 开始代码质量检查（2025年最新标准）..."
    
    # 1. 编译检查
    print_color "$CYAN" "🔨 执行编译检查..."
    if ! cargo check --all-targets --all-features --workspace; then
        print_color "$RED" "❌ 编译检查失败"
        exit 1
    fi
    print_color "$GREEN" "✅ 编译检查通过"
    
    # 2. 代码格式化检查
    print_color "$CYAN" "🎨 执行代码格式化检查..."
    if [ "$FIX" = true ]; then
        cargo fmt --all
        print_color "$GREEN" "✅ 代码格式化已修复"
    else
        if ! cargo fmt --all -- --check; then
            print_color "$RED" "❌ 代码格式化检查失败，请运行 'cargo fmt --all' 修复"
            exit 1
        fi
        print_color "$GREEN" "✅ 代码格式化检查通过"
    fi
    
    # 3. Clippy静态分析
    print_color "$CYAN" "🔍 执行Clippy静态分析..."
    local clippy_args=(
        "clippy"
        "--all-targets"
        "--all-features"
        "--workspace"
        "--"
    )
    
    if [ "$STRICT" = true ]; then
        clippy_args+=(
            "-D" "warnings"
            "-D" "clippy::all"
            "-D" "clippy::pedantic"
            "-W" "clippy::nursery"
            "-D" "clippy::cognitive_complexity"
            "-D" "clippy::too_many_arguments"
            "-D" "clippy::type_complexity"
        )
    else
        clippy_args+=("-D" "warnings")
    fi
    
    if [ "$FIX" = true ]; then
        clippy_args+=("--fix")
    fi
    
    if ! cargo "${clippy_args[@]}"; then
        print_color "$RED" "❌ Clippy检查失败"
        exit 1
    fi
    print_color "$GREEN" "✅ Clippy检查通过"
    
    # 4. 安全漏洞检查
    if check_cargo_tool "deny"; then
        print_color "$CYAN" "🛡️ 执行安全检查..."
        if ! cargo deny check --all-features; then
            print_color "$RED" "❌ 安全检查失败"
            exit 1
        fi
        print_color "$GREEN" "✅ 安全检查通过"
    else
        print_color "$YELLOW" "⚠️ cargo-deny未安装，跳过安全检查"
    fi
    
    # 5. 漏洞审计
    if check_cargo_tool "audit"; then
        print_color "$CYAN" "🔒 执行漏洞审计..."
        if ! cargo audit --deny warnings; then
            print_color "$RED" "❌ 漏洞审计失败"
            exit 1
        fi
        print_color "$GREEN" "✅ 漏洞审计通过"
    else
        print_color "$YELLOW" "⚠️ cargo-audit未安装，跳过漏洞审计"
    fi
    
    # 6. 未使用依赖检查
    if check_cargo_tool "machete"; then
        print_color "$CYAN" "🔧 检查未使用的依赖..."
        cargo machete || true  # 不因为未使用依赖而失败
        print_color "$GREEN" "✅ 未使用依赖检查完成"
    else
        print_color "$YELLOW" "⚠️ cargo-machete未安装，跳过未使用依赖检查"
    fi
    
    # 7. 过时依赖检查
    if check_cargo_tool "outdated"; then
        print_color "$CYAN" "📦 检查过时的依赖..."
        cargo outdated --workspace || true  # 不因为过时依赖而失败
        print_color "$GREEN" "✅ 过时依赖检查完成"
    else
        print_color "$YELLOW" "⚠️ cargo-outdated未安装，跳过过时依赖检查"
    fi
    
    # 8. 单元测试
    print_color "$CYAN" "🧪 执行单元测试..."
    if ! cargo test --workspace --lib; then
        print_color "$RED" "❌ 单元测试失败"
        exit 1
    fi
    print_color "$GREEN" "✅ 单元测试通过"
}

# 主程序
main() {
    print_color "$BLUE" "🎯 Axum企业级项目代码质量检查工具 v2025.1"
    print_color "$BLUE" "================================================"
    
    if [ "$INSTALL_TOOLS" = true ]; then
        install_required_tools
    fi
    
    if [ "$UPDATE_TOOLS" = true ]; then
        update_tools
    fi
    
    run_quality_checks
    
    print_color "$GREEN" "🎉 所有代码质量检查通过！"
    print_color "$GREEN" "================================================"
}

# 错误处理
trap 'print_color "$RED" "💥 代码质量检查过程中发生错误"; exit 1' ERR

# 执行主程序
main "$@"
