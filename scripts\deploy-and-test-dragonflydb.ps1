# DragonflyDB 部署和测试脚本
# 自动化部署、配置验证和性能测试

param(
    [ValidateSet("deploy", "test", "restart", "stop", "status", "full")]
    [string]$Action = "full",
    [switch]$SkipBuild = $false,
    [switch]$Verbose = $false,
    [switch]$Force = $false
)

$ErrorActionPreference = "Stop"

# 配置变量
$ComposeFile = "podman-compose.yml"
$ProjectName = "axum-tutorial"
$DragonflyService = "dragonflydb"
$RedisExporterService = "redis-exporter"
$PrometheusService = "prometheus"

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        "INFO" { "White" }
        default { "White" }
    }
    Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
}

function Test-PodmanAvailable {
    try {
        $version = & podman --version 2>&1
        Write-Log "Podman版本: $version" "SUCCESS"
        return $true
    } catch {
        Write-Log "Podman未安装或不可用" "ERROR"
        return $false
    }
}

function Test-ComposeFileExists {
    if (Test-Path $ComposeFile) {
        Write-Log "找到compose文件: $ComposeFile" "SUCCESS"
        return $true
    } else {
        Write-Log "未找到compose文件: $ComposeFile" "ERROR"
        return $false
    }
}

function Deploy-Services {
    Write-Log "开始部署DragonflyDB服务..." "INFO"
    
    try {
        # 停止现有服务 (如果存在)
        if ($Force) {
            Write-Log "强制停止现有服务..." "INFO"
            & podman-compose -f $ComposeFile down --remove-orphans 2>&1 | Out-Null
        }
        
        # 拉取最新镜像
        if (-not $SkipBuild) {
            Write-Log "拉取最新镜像..." "INFO"
            & podman-compose -f $ComposeFile pull $DragonflyService $RedisExporterService
        }
        
        # 启动服务
        Write-Log "启动DragonflyDB服务..." "INFO"
        $deployResult = & podman-compose -f $ComposeFile up -d $DragonflyService 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "DragonflyDB服务启动成功" "SUCCESS"
        } else {
            Write-Log "DragonflyDB服务启动失败: $deployResult" "ERROR"
            return $false
        }
        
        # 启动Redis Exporter
        Write-Log "启动Redis Exporter..." "INFO"
        $exporterResult = & podman-compose -f $ComposeFile up -d $RedisExporterService 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Redis Exporter启动成功" "SUCCESS"
        } else {
            Write-Log "Redis Exporter启动失败: $exporterResult" "WARN"
        }
        
        # 等待服务就绪
        Write-Log "等待服务就绪..." "INFO"
        Start-Sleep -Seconds 30
        
        return $true
    } catch {
        Write-Log "部署过程中发生异常: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-ServiceStatus {
    Write-Log "检查服务状态..." "INFO"
    
    try {
        $services = & podman-compose -f $ComposeFile ps --format json 2>&1 | ConvertFrom-Json
        
        foreach ($service in $services) {
            $serviceName = $service.Name
            $status = $service.Status
            $health = $service.Health
            
            if ($serviceName -match "dragonflydb|redis-exporter") {
                if ($status -match "Up") {
                    Write-Log "服务 $serviceName 状态: $status" "SUCCESS"
                } else {
                    Write-Log "服务 $serviceName 状态异常: $status" "ERROR"
                }
                
                if ($health) {
                    Write-Log "服务 $serviceName 健康状态: $health" "INFO"
                }
            }
        }
        
        return $true
    } catch {
        Write-Log "无法获取服务状态: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-DragonflyConnection {
    Write-Log "测试DragonflyDB连接..." "INFO"
    
    $maxRetries = 5
    $retryCount = 0
    
    while ($retryCount -lt $maxRetries) {
        try {
            $result = & redis-cli -h 127.0.0.1 -p 6379 --user monitor -a monitor_password_2025 ping 2>&1
            
            if ($result -eq "PONG") {
                Write-Log "DragonflyDB连接测试成功" "SUCCESS"
                return $true
            } else {
                Write-Log "DragonflyDB连接测试失败: $result" "WARN"
            }
        } catch {
            Write-Log "DragonflyDB连接异常: $($_.Exception.Message)" "WARN"
        }
        
        $retryCount++
        if ($retryCount -lt $maxRetries) {
            Write-Log "等待10秒后重试... ($retryCount/$maxRetries)" "INFO"
            Start-Sleep -Seconds 10
        }
    }
    
    Write-Log "DragonflyDB连接测试失败，已达到最大重试次数" "ERROR"
    return $false
}

function Test-MonitoringEndpoints {
    Write-Log "测试监控端点..." "INFO"
    
    # 测试Redis Exporter
    try {
        $response = Invoke-WebRequest -Uri "http://127.0.0.1:9121/metrics" -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Log "Redis Exporter指标端点正常" "SUCCESS"
        } else {
            Write-Log "Redis Exporter指标端点异常: $($response.StatusCode)" "ERROR"
        }
    } catch {
        Write-Log "无法访问Redis Exporter指标端点: $($_.Exception.Message)" "ERROR"
    }
    
    # 测试DragonflyDB HTTP端点 (如果支持)
    try {
        $response = Invoke-WebRequest -Uri "http://127.0.0.1:6379/metrics" -TimeoutSec 10 -UseBasicParsing
        if ($response.StatusCode -eq 200) {
            Write-Log "DragonflyDB HTTP指标端点正常" "SUCCESS"
        } else {
            Write-Log "DragonflyDB HTTP指标端点异常: $($response.StatusCode)" "WARN"
        }
    } catch {
        Write-Log "DragonflyDB HTTP指标端点不可用 (这是正常的)" "INFO"
    }
}

function Run-BasicFunctionalTests {
    Write-Log "运行基础功能测试..." "INFO"
    
    $testUser = "axum_app"
    $testPassword = "dragonfly_secure_password_2025"
    
    try {
        # 测试基础SET/GET操作
        $testKey = "test:functional:$(Get-Date -Format 'yyyyMMddHHmmss')"
        $testValue = "DragonflyDB功能测试"
        
        & redis-cli -h 127.0.0.1 -p 6379 --user $testUser -a $testPassword set $testKey $testValue | Out-Null
        $retrievedValue = & redis-cli -h 127.0.0.1 -p 6379 --user $testUser -a $testPassword get $testKey
        
        if ($retrievedValue -eq $testValue) {
            Write-Log "基础SET/GET测试通过" "SUCCESS"
        } else {
            Write-Log "基础SET/GET测试失败" "ERROR"
            return $false
        }
        
        # 测试Hash操作
        $hashKey = "test:hash:$(Get-Date -Format 'yyyyMMddHHmmss')"
        & redis-cli -h 127.0.0.1 -p 6379 --user $testUser -a $testPassword hset $hashKey field1 value1 field2 value2 | Out-Null
        $hashLen = & redis-cli -h 127.0.0.1 -p 6379 --user $testUser -a $testPassword hlen $hashKey
        
        if ($hashLen -eq "2") {
            Write-Log "Hash操作测试通过" "SUCCESS"
        } else {
            Write-Log "Hash操作测试失败" "ERROR"
            return $false
        }
        
        # 测试List操作
        $listKey = "test:list:$(Get-Date -Format 'yyyyMMddHHmmss')"
        & redis-cli -h 127.0.0.1 -p 6379 --user $testUser -a $testPassword lpush $listKey item1 item2 item3 | Out-Null
        $listLen = & redis-cli -h 127.0.0.1 -p 6379 --user $testUser -a $testPassword llen $listKey
        
        if ($listLen -eq "3") {
            Write-Log "List操作测试通过" "SUCCESS"
        } else {
            Write-Log "List操作测试失败" "ERROR"
            return $false
        }
        
        # 清理测试数据
        & redis-cli -h 127.0.0.1 -p 6379 --user $testUser -a $testPassword del $testKey $hashKey $listKey | Out-Null
        
        Write-Log "所有基础功能测试通过" "SUCCESS"
        return $true
    } catch {
        Write-Log "功能测试过程中发生异常: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Run-PerformanceTest {
    Write-Log "运行性能测试..." "INFO"
    
    try {
        # 检查redis-benchmark是否可用
        $benchmarkPath = Get-Command redis-benchmark -ErrorAction SilentlyContinue
        if (-not $benchmarkPath) {
            Write-Log "redis-benchmark未找到，跳过性能测试" "WARN"
            return $true
        }
        
        Write-Log "执行SET操作性能测试..." "INFO"
        $setResult = & redis-benchmark -h 127.0.0.1 -p 6379 -a dragonfly_secure_password_2025 -t set -n 10000 -q 2>&1
        Write-Log "SET性能测试结果: $setResult" "INFO"
        
        Write-Log "执行GET操作性能测试..." "INFO"
        $getResult = & redis-benchmark -h 127.0.0.1 -p 6379 -a dragonfly_secure_password_2025 -t get -n 10000 -q 2>&1
        Write-Log "GET性能测试结果: $getResult" "INFO"
        
        Write-Log "性能测试完成" "SUCCESS"
        return $true
    } catch {
        Write-Log "性能测试过程中发生异常: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Show-ServiceLogs {
    Write-Log "显示服务日志..." "INFO"
    
    Write-Host "`n=== DragonflyDB 日志 ===" -ForegroundColor Cyan
    & podman-compose -f $ComposeFile logs --tail=20 $DragonflyService
    
    Write-Host "`n=== Redis Exporter 日志 ===" -ForegroundColor Cyan
    & podman-compose -f $ComposeFile logs --tail=10 $RedisExporterService
}

# 主执行逻辑
Write-Host "=== DragonflyDB 部署和测试工具 ===" -ForegroundColor Green
Write-Host "操作: $Action" -ForegroundColor Yellow
Write-Host "时间: $(Get-Date)" -ForegroundColor Yellow

# 前置检查
if (-not (Test-PodmanAvailable)) {
    exit 1
}

if (-not (Test-ComposeFileExists)) {
    exit 1
}

$success = $true

switch ($Action) {
    "deploy" {
        $success = Deploy-Services
    }
    
    "test" {
        $success = Test-DragonflyConnection
        if ($success) {
            $success = Run-BasicFunctionalTests
        }
        if ($success) {
            Test-MonitoringEndpoints
        }
    }
    
    "restart" {
        Write-Log "重启DragonflyDB服务..." "INFO"
        & podman-compose -f $ComposeFile restart $DragonflyService $RedisExporterService
        Start-Sleep -Seconds 20
        $success = Test-DragonflyConnection
    }
    
    "stop" {
        Write-Log "停止DragonflyDB服务..." "INFO"
        & podman-compose -f $ComposeFile stop $DragonflyService $RedisExporterService
    }
    
    "status" {
        Test-ServiceStatus
        if ($Verbose) {
            Show-ServiceLogs
        }
    }
    
    "full" {
        # 完整的部署和测试流程
        Write-Log "开始完整的部署和测试流程..." "INFO"
        
        # 1. 部署服务
        $success = Deploy-Services
        if (-not $success) {
            Write-Log "部署失败，终止流程" "ERROR"
            exit 1
        }
        
        # 2. 检查服务状态
        Test-ServiceStatus
        
        # 3. 测试连接
        $success = Test-DragonflyConnection
        if (-not $success) {
            Write-Log "连接测试失败，显示日志..." "ERROR"
            Show-ServiceLogs
            exit 1
        }
        
        # 4. 运行功能测试
        $success = Run-BasicFunctionalTests
        if (-not $success) {
            Write-Log "功能测试失败" "ERROR"
            exit 1
        }
        
        # 5. 测试监控端点
        Test-MonitoringEndpoints
        
        # 6. 运行性能测试
        Run-PerformanceTest
        
        # 7. 运行健康检查
        if (Test-Path "scripts/health-check-dragonflydb.ps1") {
            Write-Log "运行健康检查..." "INFO"
            & .\scripts\health-check-dragonflydb.ps1
        }
        
        Write-Log "完整测试流程完成" "SUCCESS"
    }
}

if ($success) {
    Write-Log "操作成功完成" "SUCCESS"
    exit 0
} else {
    Write-Log "操作失败" "ERROR"
    exit 1
}
