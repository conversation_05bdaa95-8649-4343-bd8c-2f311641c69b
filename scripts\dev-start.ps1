# Axum Tutorial 开发启动脚本 (PowerShell)
# 提供便捷的开发环境启动和管理功能

param(
    [Parameter(Position=0)]
    [string]$Command = "help"
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot
Set-Location $ProjectRoot

# 日志函数
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# 检查依赖
function Test-Dependencies {
    Write-Info "检查依赖..."
    
    if (-not (Get-Command cargo -ErrorAction SilentlyContinue)) {
        Write-Error "Cargo 未安装，请先安装 Rust 工具链"
        exit 1
    }
    
    if (-not (Get-Command sea-orm-cli -ErrorAction SilentlyContinue)) {
        Write-Warning "sea-orm-cli 未安装，正在安装..."
        cargo install sea-orm-cli
    }
    
    Write-Success "依赖检查完成"
}

# 设置环境变量
function Initialize-Environment {
    Write-Info "设置环境变量..."
    
    if (-not (Test-Path ".env")) {
        if (Test-Path ".env.example") {
            Copy-Item ".env.example" ".env"
            Write-Success "已从 .env.example 创建 .env 文件"
        } else {
            Write-Warning ".env.example 文件不存在，请手动创建 .env 文件"
        }
    }
    
    # 加载环境变量
    if (Test-Path ".env") {
        Get-Content ".env" | ForEach-Object {
            if ($_ -match "^([^#][^=]+)=(.*)$") {
                [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
            }
        }
        Write-Success "环境变量加载完成"
    }
}

# 数据库初始化
function Initialize-Database {
    Write-Info "初始化数据库..."
    
    cargo run -p migration
    
    Write-Success "数据库初始化完成"
}

# 构建项目
function Build-Project {
    Write-Info "构建项目..."
    
    cargo build --workspace
    
    Write-Success "项目构建完成"
}

# 运行测试
function Invoke-Tests {
    Write-Info "运行测试..."
    
    cargo test --workspace
    
    Write-Success "测试运行完成"
}

# 代码质量检查
function Test-CodeQuality {
    Write-Info "进行代码质量检查..."
    
    # 格式化代码
    cargo fmt --all
    Write-Success "代码格式化完成"
    
    # Clippy 检查
    cargo clippy --all-targets --workspace -- -D warnings
    Write-Success "Clippy 检查完成"
}

# 启动服务器
function Start-Server {
    param([string]$Mode = "dev")
    
    Write-Info "启动服务器 (模式: $Mode)..."
    
    switch ($Mode) {
        "dev" {
            $env:RUST_LOG = "debug"
            cargo run -p server
        }
        "prod" {
            cargo build -p server --release
            & ".\target\release\server.exe"
        }
        default {
            cargo run -p server
        }
    }
}

# 重置数据库
function Reset-Database {
    Write-Info "重置数据库..."
    
    if (Test-Path "task_manager.db") {
        Remove-Item "task_manager.db" -Force
        Write-Success "已删除现有数据库文件"
    }
    
    Initialize-Database
}

# 清理构建文件
function Clear-Build {
    Write-Info "清理构建文件..."
    
    cargo clean
    
    Write-Success "构建文件清理完成"
}

# 显示帮助信息
function Show-Help {
    Write-Host "Axum Tutorial 开发启动脚本 (PowerShell)"
    Write-Host ""
    Write-Host "用法: .\dev-start.ps1 [命令]"
    Write-Host ""
    Write-Host "命令:"
    Write-Host "  init        初始化开发环境（检查依赖、设置环境、初始化数据库）"
    Write-Host "  build       构建项目"
    Write-Host "  test        运行测试"
    Write-Host "  check       代码质量检查（格式化 + Clippy）"
    Write-Host "  start       启动服务器（默认开发模式）"
    Write-Host "  start-dev   启动服务器（开发模式，详细日志）"
    Write-Host "  start-prod  启动服务器（生产模式）"
    Write-Host "  db-reset    重置数据库"
    Write-Host "  clean       清理构建文件"
    Write-Host "  help        显示此帮助信息"
    Write-Host ""
    Write-Host "示例:"
    Write-Host "  .\dev-start.ps1 init           # 初始化开发环境"
    Write-Host "  .\dev-start.ps1 start-dev      # 启动开发服务器"
    Write-Host "  .\dev-start.ps1 test           # 运行测试"
    Write-Host "  .\dev-start.ps1 check          # 代码质量检查"
}

# 主函数
function Main {
    switch ($Command.ToLower()) {
        "init" {
            Test-Dependencies
            Initialize-Environment
            Initialize-Database
            Build-Project
            Write-Success "开发环境初始化完成！"
            Write-Info "运行 '.\dev-start.ps1 start-dev' 启动开发服务器"
        }
        "build" {
            Build-Project
        }
        "test" {
            Invoke-Tests
        }
        "check" {
            Test-CodeQuality
        }
        "start" {
            Start-Server
        }
        "start-dev" {
            Start-Server "dev"
        }
        "start-prod" {
            Start-Server "prod"
        }
        "db-reset" {
            Reset-Database
        }
        "clean" {
            Clear-Build
        }
        "help" {
            Show-Help
        }
        default {
            Write-Error "未知命令: $Command"
            Write-Host ""
            Show-Help
            exit 1
        }
    }
}

# 执行主函数
try {
    Main
} catch {
    Write-Error "执行失败: $($_.Exception.Message)"
    exit 1
}
