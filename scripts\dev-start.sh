#!/bin/bash

# Axum Tutorial 开发启动脚本
# 提供便捷的开发环境启动和管理功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v cargo &> /dev/null; then
        log_error "Cargo 未安装，请先安装 Rust 工具链"
        exit 1
    fi
    
    if ! command -v sea-orm-cli &> /dev/null; then
        log_warning "sea-orm-cli 未安装，正在安装..."
        cargo install sea-orm-cli
    fi
    
    log_success "依赖检查完成"
}

# 设置环境变量
setup_env() {
    log_info "设置环境变量..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.example" ]; then
            cp .env.example .env
            log_success "已从 .env.example 创建 .env 文件"
        else
            log_warning ".env.example 文件不存在，请手动创建 .env 文件"
        fi
    fi
    
    # 加载环境变量
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
        log_success "环境变量加载完成"
    fi
}

# 数据库初始化
init_database() {
    log_info "初始化数据库..."
    
    # 运行数据库迁移
    cargo run -p migration
    
    log_success "数据库初始化完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    cargo build --workspace
    
    log_success "项目构建完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    cargo test --workspace
    
    log_success "测试运行完成"
}

# 代码质量检查
quality_check() {
    log_info "进行代码质量检查..."
    
    # 格式化代码
    cargo fmt --all
    log_success "代码格式化完成"
    
    # Clippy 检查
    cargo clippy --all-targets --workspace -- -D warnings
    log_success "Clippy 检查完成"
}

# 启动服务器
start_server() {
    local mode=${1:-"dev"}
    
    log_info "启动服务器 (模式: $mode)..."
    
    case $mode in
        "dev")
            RUST_LOG=debug cargo run -p server
            ;;
        "prod")
            cargo build -p server --release
            ./target/release/server
            ;;
        *)
            cargo run -p server
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "Axum Tutorial 开发启动脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  init        初始化开发环境（检查依赖、设置环境、初始化数据库）"
    echo "  build       构建项目"
    echo "  test        运行测试"
    echo "  check       代码质量检查（格式化 + Clippy）"
    echo "  start       启动服务器（默认开发模式）"
    echo "  start-dev   启动服务器（开发模式，详细日志）"
    echo "  start-prod  启动服务器（生产模式）"
    echo "  db-reset    重置数据库"
    echo "  clean       清理构建文件"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 init           # 初始化开发环境"
    echo "  $0 start-dev      # 启动开发服务器"
    echo "  $0 test           # 运行测试"
    echo "  $0 check          # 代码质量检查"
}

# 重置数据库
reset_database() {
    log_info "重置数据库..."
    
    if [ -f "task_manager.db" ]; then
        rm -f task_manager.db
        log_success "已删除现有数据库文件"
    fi
    
    init_database
}

# 清理构建文件
clean_build() {
    log_info "清理构建文件..."
    
    cargo clean
    
    log_success "构建文件清理完成"
}

# 主函数
main() {
    case "${1:-help}" in
        "init")
            check_dependencies
            setup_env
            init_database
            build_project
            log_success "开发环境初始化完成！"
            log_info "运行 '$0 start-dev' 启动开发服务器"
            ;;
        "build")
            build_project
            ;;
        "test")
            run_tests
            ;;
        "check")
            quality_check
            ;;
        "start")
            start_server
            ;;
        "start-dev")
            start_server "dev"
            ;;
        "start-prod")
            start_server "prod"
            ;;
        "db-reset")
            reset_database
            ;;
        "clean")
            clean_build
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
