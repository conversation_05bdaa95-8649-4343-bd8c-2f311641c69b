#!/bin/bash
# Podman网络优化脚本 - 解决WSL2 + Podman网络连接问题
# 适用于Windows 10 + WSL2 + Podman环境

set -e

echo "🔧 Podman网络优化工具"
echo "================================"

# 检查Podman状态
echo "📋 检查Podman状态..."
if ! command -v podman &> /dev/null; then
    echo "❌ Podman未安装或不在PATH中"
    exit 1
fi

echo "✅ Podman版本: $(podman --version)"

# 停止相关容器
echo "🛑 停止DragonflyDB容器..."
podman stop axum_dragonflydb || true

# 删除现有网络（如果存在）
NETWORK_NAME="axum_optimized_network"
if podman network exists $NETWORK_NAME; then
    echo "🗑️ 删除现有网络: $NETWORK_NAME"
    podman network rm $NETWORK_NAME || true
fi

# 创建专用网络
echo "🌐 创建优化网络: $NETWORK_NAME"
podman network create \
    --driver bridge \
    --subnet *********/24 \
    --gateway ********* \
    $NETWORK_NAME

# 重新创建DragonflyDB容器，使用host网络模式（关键优化）
echo "🐉 重新创建DragonflyDB容器（使用host网络模式）..."
podman rm -f axum_dragonflydb || true

podman run -d \
    --name axum_dragonflydb \
    --network host \
    -v dragonflydb_data:/data \
    --restart unless-stopped \
    --health-cmd="redis-cli -a dragonfly_secure_password_2025 ping" \
    --health-interval=30s \
    --health-timeout=10s \
    --health-retries=3 \
    docker.dragonflydb.io/dragonflydb/dragonfly:latest \
    dragonfly \
    --logtostderr \
    --requirepass=dragonfly_secure_password_2025 \
    --cache_mode=true \
    --dbnum=16 \
    --bind=127.0.0.1 \
    --port=6379 \
    --maxmemory=2gb

echo "⏳ 等待容器启动..."
sleep 10

# 验证容器状态
echo "🔍 验证容器状态..."
if podman ps | grep -q axum_dragonflydb; then
    echo "✅ DragonflyDB容器运行正常"
else
    echo "❌ DragonflyDB容器启动失败"
    podman logs axum_dragonflydb
    exit 1
fi

# 测试连接
echo "🧪 测试连接..."
if echo -e "AUTH dragonfly_secure_password_2025\nPING" | nc localhost 6379 | grep -q PONG; then
    echo "✅ DragonflyDB连接测试成功"
else
    echo "❌ DragonflyDB连接测试失败"
    exit 1
fi

echo "🎉 Podman网络优化完成！"
echo ""
echo "📋 配置信息:"
echo "• 网络模式: host（直接使用WSL2网络栈）"
echo "• 容器绑定: 127.0.0.1:6379"
echo "• 连接URL: redis://:dragonfly_secure_password_2025@localhost:6379"
echo ""
echo "💡 优势: 绕过Podman网络层，直接连接，性能最佳"
echo "💡 下一步: 更新.env文件使用localhost"
