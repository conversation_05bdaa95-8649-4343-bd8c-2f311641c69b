# WSL2 DragonflyDB IP Address Auto-Update Script
# Fixes WSL2 network IP address changes that cause connection issues

param(
    [string]$EnvFile = ".env"
)

Write-Host "WSL2 DragonflyDB IP Address Auto-Update Tool" -ForegroundColor Green

# Get WSL2 IP address
Write-Host "Getting WSL2 IP address..." -ForegroundColor Yellow
try {
    $wsl2IP = (wsl hostname -I).Trim()
    if ([string]::IsNullOrEmpty($wsl2IP)) {
        Write-Host "ERROR: Cannot get WSL2 IP address" -ForegroundColor Red
        exit 1
    }
    Write-Host "WSL2 IP address: $wsl2IP" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to get WSL2 IP address: $_" -ForegroundColor Red
    exit 1
}

# Check if .env file exists
if (-not (Test-Path $EnvFile)) {
    Write-Host "ERROR: Environment file $EnvFile does not exist" -ForegroundColor Red
    exit 1
}

# Read current .env file content
$envContent = Get-Content $EnvFile -Raw

# Update IP address in CACHE_URL
$pattern = 'CACHE_URL=redis://([^@]*@)?([^:]+):6379'
$replacement = "CACHE_URL=redis://`$1${wsl2IP}:6379"

if ($envContent -match $pattern) {
    $currentIP = $matches[2]
    if ($currentIP -eq $wsl2IP) {
        Write-Host "IP address is already up to date ($wsl2IP)" -ForegroundColor Green
        exit 0
    }
    
    Write-Host "Updating IP address: $currentIP -> $wsl2IP" -ForegroundColor Yellow
    $envContent = $envContent -replace $pattern, $replacement
    
    # Write updated content
    try {
        Set-Content -Path $EnvFile -Value $envContent -NoNewline
        Write-Host "Environment file updated successfully" -ForegroundColor Green
    } catch {
        Write-Host "ERROR: Failed to write environment file: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "ERROR: CACHE_URL configuration not found in environment file" -ForegroundColor Red
    exit 1
}

# Test DragonflyDB connection
Write-Host "Testing DragonflyDB connection..." -ForegroundColor Yellow
try {
    $testResult = Test-NetConnection -ComputerName $wsl2IP -Port 6379 -WarningAction SilentlyContinue
    if ($testResult.TcpTestSucceeded) {
        Write-Host "DragonflyDB connection test successful" -ForegroundColor Green
    } else {
        Write-Host "WARNING: DragonflyDB connection test failed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "WARNING: Cannot test DragonflyDB connection: $_" -ForegroundColor Yellow
}

Write-Host "WSL2 IP address update completed!" -ForegroundColor Green
Write-Host "You can now run: cargo run -p axum-server" -ForegroundColor Cyan
