#!/bin/bash
# PostgreSQL SSL证书生成脚本
# 用于企业级生产环境的安全配置

set -e

# 配置变量
CERT_DIR="./config/ssl"
COUNTRY="CN"
STATE="Beijing"
CITY="Beijing"
ORGANIZATION="Axum Enterprise"
ORGANIZATIONAL_UNIT="IT Department"
COMMON_NAME="axum-postgres"
EMAIL="<EMAIL>"

# 创建证书目录
mkdir -p "$CERT_DIR"
cd "$CERT_DIR"

echo "🔐 开始生成PostgreSQL SSL证书..."

# 生成CA私钥
echo "📝 生成CA私钥..."
openssl genrsa -out ca.key 4096

# 生成CA证书
echo "📝 生成CA证书..."
openssl req -new -x509 -days 3650 -key ca.key -out ca.crt -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORGANIZATION/OU=$ORGANIZATIONAL_UNIT/CN=CA-$COMMON_NAME/emailAddress=$EMAIL"

# 生成服务器私钥
echo "📝 生成服务器私钥..."
openssl genrsa -out server.key 4096

# 生成服务器证书签名请求
echo "📝 生成服务器证书签名请求..."
openssl req -new -key server.key -out server.csr -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORGANIZATION/OU=$ORGANIZATIONAL_UNIT/CN=$COMMON_NAME/emailAddress=$EMAIL"

# 使用CA签名服务器证书
echo "📝 使用CA签名服务器证书..."
openssl x509 -req -days 365 -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out server.crt

# 生成客户端私钥
echo "📝 生成客户端私钥..."
openssl genrsa -out client.key 4096

# 生成客户端证书签名请求
echo "📝 生成客户端证书签名请求..."
openssl req -new -key client.key -out client.csr -subj "/C=$COUNTRY/ST=$STATE/L=$CITY/O=$ORGANIZATION/OU=$ORGANIZATIONAL_UNIT/CN=client-$COMMON_NAME/emailAddress=$EMAIL"

# 使用CA签名客户端证书
echo "📝 使用CA签名客户端证书..."
openssl x509 -req -days 365 -in client.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out client.crt

# 设置正确的权限
echo "🔒 设置证书文件权限..."
chmod 600 *.key
chmod 644 *.crt
chmod 644 *.csr

# 清理临时文件
rm -f *.csr *.srl

echo "✅ SSL证书生成完成！"
echo "📁 证书文件位置: $CERT_DIR"
echo "📋 生成的文件:"
echo "   - ca.crt (CA证书)"
echo "   - ca.key (CA私钥)"
echo "   - server.crt (服务器证书)"
echo "   - server.key (服务器私钥)"
echo "   - client.crt (客户端证书)"
echo "   - client.key (客户端私钥)"
echo ""
echo "🔧 要启用SSL，请在podman-compose.yml中取消注释SSL配置行"
echo "💡 生产环境建议使用受信任的CA签发的证书"
