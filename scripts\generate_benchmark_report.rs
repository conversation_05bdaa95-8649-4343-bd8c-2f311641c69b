use std::fs;
use std::path::Path;
use chrono::{ DateTime, Utc };
use serde_json::Value;
use std::collections::HashMap;

/// Axum项目基准测试报告生成器
///
/// 此模块负责整合所有Criterion.rs基准测试结果，生成统一的HTML和Markdown报告
/// 遵循rust_axum_Rules.md规范，使用清晰的命名和详细的中文注释
pub struct BenchmarkReportGenerator {
    /// Criterion报告根目录路径
    criterion_dir: String,
    /// 输出报告目录路径
    output_dir: String,
    /// 报告生成时间戳
    timestamp: DateTime<Utc>,
}

impl BenchmarkReportGenerator {
    /// 创建新的基准测试报告生成器实例
    ///
    /// # 参数
    /// * `criterion_dir` - Criterion.rs生成的报告目录路径
    /// * `output_dir` - 输出报告的目标目录路径
    ///
    /// # 返回值
    /// 返回配置好的BenchmarkReportGenerator实例
    pub fn new(criterion_dir: &str, output_dir: &str) -> Self {
        Self {
            criterion_dir: criterion_dir.to_string(),
            output_dir: output_dir.to_string(),
            timestamp: Utc::now(),
        }
    }

    /// 生成完整的基准测试报告
    ///
    /// 此方法会扫描Criterion目录，提取所有基准测试结果，
    /// 并生成HTML和Markdown格式的综合报告
    ///
    /// # 错误处理
    /// 如果目录不存在或文件读取失败，返回相应的错误信息
    pub fn generate_comprehensive_report(&self) -> Result<(), Box<dyn std::error::Error>> {
        // 确保输出目录存在
        fs::create_dir_all(&self.output_dir)?;

        // 扫描并收集所有基准测试结果
        let benchmark_results = self.collect_benchmark_results()?;

        // 生成HTML报告
        self.generate_html_report(&benchmark_results)?;

        // 生成Markdown报告
        self.generate_markdown_report(&benchmark_results)?;

        // 生成JSON格式的原始数据
        self.generate_json_report(&benchmark_results)?;

        println!("✅ 基准测试报告生成完成！");
        println!("📁 报告位置: {}", self.output_dir);
        println!("📄 HTML报告: {}/benchmark_report.html", self.output_dir);
        println!("📄 Markdown报告: {}/benchmark_report.md", self.output_dir);
        println!("📄 JSON数据: {}/benchmark_data.json", self.output_dir);

        Ok(())
    }

    /// 收集所有基准测试结果数据
    ///
    /// 扫描Criterion目录结构，提取每个基准测试的性能数据
    ///
    /// # 返回值
    /// 返回包含所有基准测试结果的HashMap
    fn collect_benchmark_results(
        &self
    ) -> Result<HashMap<String, BenchmarkGroup>, Box<dyn std::error::Error>> {
        let mut results = HashMap::new();
        let criterion_path = Path::new(&self.criterion_dir);

        if !criterion_path.exists() {
            return Err(format!("Criterion目录不存在: {}", self.criterion_dir).into());
        }

        // 遍历Criterion目录中的所有基准测试组
        for entry in fs::read_dir(criterion_path)? {
            let entry = entry?;
            let path = entry.path();

            if path.is_dir() && path.file_name().unwrap() != "report" {
                let group_name = path.file_name().unwrap().to_string_lossy().to_string();

                // 跳过隐藏目录和特殊目录
                if group_name.starts_with('.') {
                    continue;
                }

                if let Ok(group_data) = self.parse_benchmark_group(&path) {
                    results.insert(group_name, group_data);
                }
            }
        }

        Ok(results)
    }

    /// 解析单个基准测试组的数据
    ///
    /// # 参数
    /// * `group_path` - 基准测试组目录路径
    ///
    /// # 返回值
    /// 返回解析后的基准测试组数据
    fn parse_benchmark_group(
        &self,
        group_path: &Path
    ) -> Result<BenchmarkGroup, Box<dyn std::error::Error>> {
        let group_name = group_path.file_name().unwrap().to_string_lossy().to_string();
        let mut benchmarks = HashMap::new();

        // 遍历组内的所有基准测试
        for entry in fs::read_dir(group_path)? {
            let entry = entry?;
            let path = entry.path();

            if path.is_dir() && path.file_name().unwrap() != "report" {
                let benchmark_name = path.file_name().unwrap().to_string_lossy().to_string();

                if let Ok(benchmark_data) = self.parse_individual_benchmark(&path) {
                    benchmarks.insert(benchmark_name, benchmark_data);
                }
            }
        }

        Ok(BenchmarkGroup {
            name: group_name.clone(),
            benchmarks,
            description: self.get_group_description(&group_name),
        })
    }

    /// 解析单个基准测试的详细数据
    ///
    /// # 参数
    /// * `benchmark_path` - 单个基准测试目录路径
    ///
    /// # 返回值
    /// 返回解析后的基准测试数据
    fn parse_individual_benchmark(
        &self,
        benchmark_path: &Path
    ) -> Result<BenchmarkData, Box<dyn std::error::Error>> {
        let benchmark_name = benchmark_path.file_name().unwrap().to_string_lossy().to_string();

        // 尝试读取estimates.json文件获取性能数据
        let estimates_path = benchmark_path.join("base").join("estimates.json");

        let (mean_time, std_dev) = if estimates_path.exists() {
            let estimates_content = fs::read_to_string(&estimates_path)?;
            let estimates: Value = serde_json::from_str(&estimates_content)?;

            let mean = estimates["mean"]["point_estimate"].as_f64().unwrap_or(0.0);
            let std_dev = estimates["std_dev"]["point_estimate"].as_f64().unwrap_or(0.0);

            (mean, std_dev)
        } else {
            (0.0, 0.0)
        };

        Ok(BenchmarkData {
            name: benchmark_name,
            mean_time_ns: mean_time,
            std_deviation_ns: std_dev,
            unit: "ns".to_string(),
            throughput: self.extract_throughput_info(benchmark_path),
        })
    }

    /// 提取吞吐量信息
    ///
    /// # 参数
    /// * `benchmark_path` - 基准测试目录路径
    ///
    /// # 返回值
    /// 返回吞吐量信息字符串，如果没有则返回None
    fn extract_throughput_info(&self, _benchmark_path: &Path) -> Option<String> {
        // 这里可以根据需要实现吞吐量信息提取逻辑
        // 目前返回None作为占位符
        None
    }

    /// 获取基准测试组的描述信息
    ///
    /// # 参数
    /// * `group_name` - 基准测试组名称
    ///
    /// # 返回值
    /// 返回该组的中文描述
    fn get_group_description(&self, group_name: &str) -> String {
        match group_name {
            "简单数学运算" => "基础数学运算性能测试，包括加法、乘法和递归算法".to_string(),
            "字符串操作" => "字符串处理性能测试，包括连接、格式化等操作".to_string(),
            "向量操作" => "向量数据结构操作性能测试，包括创建、排序等".to_string(),
            "JSON操作" => "JSON序列化和反序列化性能测试".to_string(),
            "异步操作" => "异步编程相关操作性能测试".to_string(),
            name if name.starts_with("websocket") => "WebSocket相关功能性能测试".to_string(),
            _ => format!("{}相关性能测试", group_name),
        }
    }
}

/// 基准测试组数据结构
#[derive(Debug, Clone)]
pub struct BenchmarkGroup {
    /// 组名称
    pub name: String,
    /// 组内所有基准测试
    pub benchmarks: HashMap<String, BenchmarkData>,
    /// 组描述
    pub description: String,
}

/// 单个基准测试数据结构
#[derive(Debug, Clone)]
pub struct BenchmarkData {
    /// 基准测试名称
    pub name: String,
    /// 平均执行时间（纳秒）
    pub mean_time_ns: f64,
    /// 标准差（纳秒）
    pub std_deviation_ns: f64,
    /// 时间单位
    pub unit: String,
    /// 吞吐量信息
    pub throughput: Option<String>,
}

impl BenchmarkReportGenerator {
    /// 生成HTML格式的基准测试报告
    ///
    /// # 参数
    /// * `results` - 所有基准测试结果数据
    ///
    /// # 错误处理
    /// 如果文件写入失败，返回相应错误
    fn generate_html_report(
        &self,
        results: &HashMap<String, BenchmarkGroup>
    ) -> Result<(), Box<dyn std::error::Error>> {
        let html_content = self.build_html_content(results);
        let output_path = format!("{}/benchmark_report.html", self.output_dir);
        fs::write(output_path, html_content)?;
        Ok(())
    }

    /// 构建HTML报告内容
    ///
    /// # 参数
    /// * `results` - 基准测试结果数据
    ///
    /// # 返回值
    /// 返回完整的HTML字符串
    fn build_html_content(&self, results: &HashMap<String, BenchmarkGroup>) -> String {
        let mut html = String::new();

        // HTML头部
        html.push_str(
            &format!(
                r#"
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Axum项目基准测试报告</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }}
        .summary {{
            background: #ecf0f1;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }}
        .benchmark-group {{
            margin: 30px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }}
        .group-header {{
            background: #3498db;
            color: white;
            padding: 15px;
            font-weight: bold;
        }}
        .group-content {{
            padding: 20px;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        .performance-good {{ color: #27ae60; }}
        .performance-average {{ color: #f39c12; }}
        .performance-poor {{ color: #e74c3c; }}
        .timestamp {{
            text-align: center;
            color: #7f8c8d;
            font-style: italic;
            margin-top: 30px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Axum项目基准测试报告</h1>

        <div class="summary">
            <h2>📊 测试概览</h2>
            <p><strong>生成时间:</strong> {}</p>
            <p><strong>测试组数量:</strong> {}</p>
            <p><strong>总测试数量:</strong> {}</p>
            <p><strong>Axum版本:</strong> 0.8.4</p>
            <p><strong>Rust版本:</strong> edition = "2024"</p>
        </div>
"#,
                self.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
                results.len(),
                results
                    .values()
                    .map(|g| g.benchmarks.len())
                    .sum::<usize>()
            )
        );

        // 生成每个基准测试组的HTML内容
        for (group_name, group_data) in results {
            html.push_str(&self.build_group_html(group_name, group_data));
        }

        // HTML尾部
        html.push_str(
            r#"
        <div class="timestamp">
            <p>此报告由Axum项目基准测试系统自动生成</p>
            <p>基于Criterion.rs基准测试框架</p>
        </div>
    </div>
</body>
</html>
"#
        );

        html
    }

    /// 构建单个基准测试组的HTML内容
    ///
    /// # 参数
    /// * `group_name` - 组名称
    /// * `group_data` - 组数据
    ///
    /// # 返回值
    /// 返回该组的HTML字符串
    fn build_group_html(&self, group_name: &str, group_data: &BenchmarkGroup) -> String {
        let mut html = String::new();

        html.push_str(
            &format!(
                r#"
        <div class="benchmark-group">
            <div class="group-header">
                {} - {}
            </div>
            <div class="group-content">
                <table>
                    <thead>
                        <tr>
                            <th>基准测试名称</th>
                            <th>平均执行时间</th>
                            <th>标准差</th>
                            <th>性能评级</th>
                        </tr>
                    </thead>
                    <tbody>
"#,
                group_name,
                group_data.description
            )
        );

        // 添加每个基准测试的数据行
        for (benchmark_name, benchmark_data) in &group_data.benchmarks {
            let formatted_time = self.format_time(benchmark_data.mean_time_ns);
            let formatted_std_dev = self.format_time(benchmark_data.std_deviation_ns);
            let performance_class = self.get_performance_class(benchmark_data.mean_time_ns);
            let performance_rating = self.get_performance_rating(benchmark_data.mean_time_ns);

            html.push_str(
                &format!(
                    r#"
                        <tr>
                            <td>{}</td>
                            <td>{}</td>
                            <td>{}</td>
                            <td class="{}">{}</td>
                        </tr>
"#,
                    benchmark_name,
                    formatted_time,
                    formatted_std_dev,
                    performance_class,
                    performance_rating
                )
            );
        }

        html.push_str(
            r#"
                    </tbody>
                </table>
            </div>
        </div>
"#
        );

        html
    }

    /// 格式化时间显示
    ///
    /// # 参数
    /// * `time_ns` - 纳秒时间
    ///
    /// # 返回值
    /// 返回格式化后的时间字符串
    fn format_time(&self, time_ns: f64) -> String {
        if time_ns >= 1_000_000_000.0 {
            format!("{:.2} s", time_ns / 1_000_000_000.0)
        } else if time_ns >= 1_000_000.0 {
            format!("{:.2} ms", time_ns / 1_000_000.0)
        } else if time_ns >= 1_000.0 {
            format!("{:.2} μs", time_ns / 1_000.0)
        } else {
            format!("{:.2} ns", time_ns)
        }
    }

    /// 获取性能等级CSS类名
    ///
    /// # 参数
    /// * `time_ns` - 纳秒时间
    ///
    /// # 返回值
    /// 返回对应的CSS类名
    fn get_performance_class(&self, time_ns: f64) -> &'static str {
        if time_ns < 1_000.0 {
            "performance-good"
        } else if time_ns < 1_000_000.0 {
            "performance-average"
        } else {
            "performance-poor"
        }
    }

    /// 获取性能评级文本
    ///
    /// # 参数
    /// * `time_ns` - 纳秒时间
    ///
    /// # 返回值
    /// 返回性能评级文本
    fn get_performance_rating(&self, time_ns: f64) -> &'static str {
        if time_ns < 1_000.0 {
            "🟢 优秀"
        } else if time_ns < 1_000_000.0 {
            "🟡 良好"
        } else {
            "🔴 需优化"
        }
    }

    /// 生成Markdown格式的基准测试报告
    ///
    /// # 参数
    /// * `results` - 所有基准测试结果数据
    ///
    /// # 错误处理
    /// 如果文件写入失败，返回相应错误
    fn generate_markdown_report(
        &self,
        results: &HashMap<String, BenchmarkGroup>
    ) -> Result<(), Box<dyn std::error::Error>> {
        let markdown_content = self.build_markdown_content(results);
        let output_path = format!("{}/benchmark_report.md", self.output_dir);
        fs::write(output_path, markdown_content)?;
        Ok(())
    }

    /// 构建Markdown报告内容
    ///
    /// # 参数
    /// * `results` - 基准测试结果数据
    ///
    /// # 返回值
    /// 返回完整的Markdown字符串
    fn build_markdown_content(&self, results: &HashMap<String, BenchmarkGroup>) -> String {
        let mut markdown = String::new();

        // Markdown头部
        markdown.push_str(
            &format!(
                r#"# 🚀 Axum项目基准测试报告

## 📊 测试概览

- **生成时间**: {}
- **测试组数量**: {}
- **总测试数量**: {}
- **Axum版本**: 0.8.4
- **Rust版本**: edition = "2024"
- **测试框架**: Criterion.rs

## 🎯 测试环境

- **操作系统**: Windows 10
- **数据库**: SQLite (本地开发)
- **服务器地址**: 127.0.0.1:3000
- **并发模型**: Tokio异步运行时

"#,
                self.timestamp.format("%Y-%m-%d %H:%M:%S UTC"),
                results.len(),
                results
                    .values()
                    .map(|g| g.benchmarks.len())
                    .sum::<usize>()
            )
        );

        // 生成每个基准测试组的Markdown内容
        for (group_name, group_data) in results {
            markdown.push_str(&self.build_group_markdown(group_name, group_data));
        }

        // 添加性能分析总结
        markdown.push_str(&self.build_performance_summary(results));

        // Markdown尾部
        markdown.push_str(
            r#"
---

## 📝 说明

此报告由Axum项目基准测试系统自动生成，基于Criterion.rs基准测试框架。

### 性能评级标准

- 🟢 **优秀** (< 1μs): 性能表现优异，无需优化
- 🟡 **良好** (1μs - 1ms): 性能表现良好，可考虑优化
- 🔴 **需优化** (> 1ms): 性能表现较差，建议优化

### 建议

1. 对于标记为"需优化"的测试项，建议进行性能分析和优化
2. 定期运行基准测试，监控性能变化趋势
3. 在生产环境部署前，确保关键路径的性能指标达标

"#
        );

        markdown
    }

    /// 构建单个基准测试组的Markdown内容
    ///
    /// # 参数
    /// * `group_name` - 组名称
    /// * `group_data` - 组数据
    ///
    /// # 返回值
    /// 返回该组的Markdown字符串
    fn build_group_markdown(&self, group_name: &str, group_data: &BenchmarkGroup) -> String {
        let mut markdown = String::new();

        markdown.push_str(&format!("## 📈 {} - {}\n\n", group_name, group_data.description));

        markdown.push_str("| 基准测试名称 | 平均执行时间 | 标准差 | 性能评级 |\n");
        markdown.push_str("|-------------|-------------|--------|----------|\n");

        // 添加每个基准测试的数据行
        for (benchmark_name, benchmark_data) in &group_data.benchmarks {
            let formatted_time = self.format_time(benchmark_data.mean_time_ns);
            let formatted_std_dev = self.format_time(benchmark_data.std_deviation_ns);
            let performance_rating = self.get_performance_rating(benchmark_data.mean_time_ns);

            markdown.push_str(
                &format!(
                    "| {} | {} | {} | {} |\n",
                    benchmark_name,
                    formatted_time,
                    formatted_std_dev,
                    performance_rating
                )
            );
        }

        markdown.push_str("\n");
        markdown
    }

    /// 构建性能分析总结
    ///
    /// # 参数
    /// * `results` - 所有基准测试结果
    ///
    /// # 返回值
    /// 返回性能总结的Markdown字符串
    fn build_performance_summary(&self, results: &HashMap<String, BenchmarkGroup>) -> String {
        let mut summary = String::new();
        let mut total_tests = 0;
        let mut excellent_count = 0;
        let mut good_count = 0;
        let mut poor_count = 0;

        // 统计各性能等级的测试数量
        for group_data in results.values() {
            for benchmark_data in group_data.benchmarks.values() {
                total_tests += 1;
                if benchmark_data.mean_time_ns < 1_000.0 {
                    excellent_count += 1;
                } else if benchmark_data.mean_time_ns < 1_000_000.0 {
                    good_count += 1;
                } else {
                    poor_count += 1;
                }
            }
        }

        summary.push_str("## 📊 性能分析总结\n\n");
        summary.push_str(&format!("- **总测试数**: {}\n", total_tests));
        summary.push_str(
            &format!(
                "- **🟢 优秀**: {} ({:.1}%)\n",
                excellent_count,
                ((excellent_count as f64) / (total_tests as f64)) * 100.0
            )
        );
        summary.push_str(
            &format!(
                "- **🟡 良好**: {} ({:.1}%)\n",
                good_count,
                ((good_count as f64) / (total_tests as f64)) * 100.0
            )
        );
        summary.push_str(
            &format!(
                "- **🔴 需优化**: {} ({:.1}%)\n\n",
                poor_count,
                ((poor_count as f64) / (total_tests as f64)) * 100.0
            )
        );

        // 添加性能建议
        if poor_count > 0 {
            summary.push_str("### ⚠️ 性能优化建议\n\n");
            for (_group_name, group_data) in results {
                for (benchmark_name, benchmark_data) in &group_data.benchmarks {
                    if benchmark_data.mean_time_ns >= 1_000_000.0 {
                        summary.push_str(
                            &format!(
                                "- **{}**: {} - 执行时间较长，建议优化\n",
                                benchmark_name,
                                self.format_time(benchmark_data.mean_time_ns)
                            )
                        );
                    }
                }
            }
            summary.push_str("\n");
        }

        summary
    }

    /// 生成JSON格式的基准测试报告
    ///
    /// # 参数
    /// * `results` - 所有基准测试结果数据
    ///
    /// # 错误处理
    /// 如果文件写入失败，返回相应错误
    fn generate_json_report(
        &self,
        results: &HashMap<String, BenchmarkGroup>
    ) -> Result<(), Box<dyn std::error::Error>> {
        let json_data = self.build_json_data(results);
        let output_path = format!("{}/benchmark_data.json", self.output_dir);
        fs::write(output_path, json_data)?;
        Ok(())
    }

    /// 构建JSON报告数据
    ///
    /// # 参数
    /// * `results` - 基准测试结果数据
    ///
    /// # 返回值
    /// 返回格式化的JSON字符串
    fn build_json_data(&self, results: &HashMap<String, BenchmarkGroup>) -> String {
        use serde_json::json;

        let mut groups = serde_json::Map::new();

        for (group_name, group_data) in results {
            let mut benchmarks = serde_json::Map::new();

            for (benchmark_name, benchmark_data) in &group_data.benchmarks {
                benchmarks.insert(
                    benchmark_name.clone(),
                    json!({
                    "name": benchmark_data.name,
                    "mean_time_ns": benchmark_data.mean_time_ns,
                    "std_deviation_ns": benchmark_data.std_deviation_ns,
                    "unit": benchmark_data.unit,
                    "formatted_time": self.format_time(benchmark_data.mean_time_ns),
                    "performance_rating": self.get_performance_rating(benchmark_data.mean_time_ns),
                    "throughput": benchmark_data.throughput
                })
                );
            }

            groups.insert(
                group_name.clone(),
                json!({
                "name": group_data.name,
                "description": group_data.description,
                "benchmarks": benchmarks
            })
            );
        }

        let report_data =
            json!({
            "metadata": {
                "generated_at": self.timestamp.to_rfc3339(),
                "axum_version": "0.8.4",
                "rust_edition": "2024",
                "test_framework": "Criterion.rs",
                "total_groups": results.len(),
                "total_benchmarks": results.values().map(|g| g.benchmarks.len()).sum::<usize>()
            },
            "groups": groups
        });

        serde_json::to_string_pretty(&report_data).unwrap_or_else(|_| "{}".to_string())
    }
}

/// 主函数 - 基准测试报告生成器入口点
///
/// 此函数创建报告生成器实例并执行完整的报告生成流程
/// 遵循rust_axum_Rules.md规范，包含详细的错误处理
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🚀 启动Axum项目基准测试报告生成器...");

    // 设置输入和输出目录路径
    let criterion_dir = "target/criterion";
    let output_dir = "reports/benchmark";

    // 检查Criterion目录是否存在
    if !Path::new(criterion_dir).exists() {
        eprintln!("❌ 错误: Criterion目录不存在: {}", criterion_dir);
        eprintln!("请先运行基准测试: cargo bench");
        std::process::exit(1);
    }

    // 创建报告生成器实例
    let generator = BenchmarkReportGenerator::new(criterion_dir, output_dir);

    // 生成完整报告
    match generator.generate_comprehensive_report() {
        Ok(()) => {
            println!("✅ 基准测试报告生成成功！");
            println!("📂 查看报告:");
            println!("   HTML: {}/benchmark_report.html", output_dir);
            println!("   Markdown: {}/benchmark_report.md", output_dir);
            println!("   JSON: {}/benchmark_data.json", output_dir);
        }
        Err(e) => {
            eprintln!("❌ 报告生成失败: {}", e);
            std::process::exit(1);
        }
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::fs;

    /// 测试基准测试报告生成器的基本功能
    #[test]
    fn test_benchmark_report_generator_creation() {
        let generator = BenchmarkReportGenerator::new("test_criterion", "test_output");
        assert_eq!(generator.criterion_dir, "test_criterion");
        assert_eq!(generator.output_dir, "test_output");
    }

    /// 测试时间格式化功能
    #[test]
    fn test_format_time() {
        let generator = BenchmarkReportGenerator::new("", "");

        assert_eq!(generator.format_time(500.0), "500.00 ns");
        assert_eq!(generator.format_time(1500.0), "1.50 μs");
        assert_eq!(generator.format_time(1500000.0), "1.50 ms");
        assert_eq!(generator.format_time(1500000000.0), "1.50 s");
    }

    /// 测试性能评级功能
    #[test]
    fn test_performance_rating() {
        let generator = BenchmarkReportGenerator::new("", "");

        assert_eq!(generator.get_performance_rating(500.0), "🟢 优秀");
        assert_eq!(generator.get_performance_rating(50000.0), "🟡 良好");
        assert_eq!(generator.get_performance_rating(5000000.0), "🔴 需优化");
    }

    /// 测试组描述获取功能
    #[test]
    fn test_group_description() {
        let generator = BenchmarkReportGenerator::new("", "");

        assert!(generator.get_group_description("简单数学运算").contains("数学运算"));
        assert!(generator.get_group_description("websocket连接").contains("WebSocket"));
        assert!(generator.get_group_description("未知组").contains("未知组"));
    }
}
