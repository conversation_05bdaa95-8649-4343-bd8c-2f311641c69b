//! # 代码覆盖率报告生成器
//!
//! 用于生成Axum项目的代码覆盖率报告
//!
//! 【功能特性】:
//! - 生成HTML格式的覆盖率报告
//! - 支持多种输出格式 (HTML/LCOV/JSON)
//! - 提供覆盖率阈值检查
//! - 生成详细的覆盖率分析报告

use anyhow::{Context, Result};
use serde_json::Value;
use std::fs;
use std::io::Write;
use std::path::Path;
use std::process::Command;

/// 覆盖率报告生成器
pub struct CoverageReportGenerator {
    /// 项目根目录
    project_root: String,
    /// 输出目录
    output_dir: String,
    /// 覆盖率阈值配置
    thresholds: CoverageThresholds,
}

/// 覆盖率阈值配置
#[derive(Debug, Clone)]
pub struct CoverageThresholds {
    /// 行覆盖率阈值 (%)
    pub line_threshold: f64,
    /// 分支覆盖率阈值 (%)
    pub branch_threshold: f64,
    /// 函数覆盖率阈值 (%)
    pub function_threshold: f64,
}

impl Default for CoverageThresholds {
    fn default() -> Self {
        Self {
            line_threshold: 90.0,
            branch_threshold: 85.0,
            function_threshold: 90.0,
        }
    }
}

/// 覆盖率统计信息
#[derive(Debug, Clone)]
pub struct CoverageStats {
    /// 行覆盖率
    pub line_coverage: f64,
    /// 分支覆盖率
    pub branch_coverage: f64,
    /// 函数覆盖率
    pub function_coverage: f64,
    /// 总覆盖的行数
    pub lines_covered: u32,
    /// 总行数
    pub lines_total: u32,
    /// 覆盖的分支数
    pub branches_covered: u32,
    /// 总分支数
    pub branches_total: u32,
    /// 覆盖的函数数
    pub functions_covered: u32,
    /// 总函数数
    pub functions_total: u32,
}

impl CoverageReportGenerator {
    /// 创建新的覆盖率报告生成器
    pub fn new(project_root: &str, output_dir: &str) -> Self {
        Self {
            project_root: project_root.to_string(),
            output_dir: output_dir.to_string(),
            thresholds: CoverageThresholds::default(),
        }
    }

    /// 设置覆盖率阈值
    pub fn with_thresholds(mut self, thresholds: CoverageThresholds) -> Self {
        self.thresholds = thresholds;
        self
    }

    /// 生成覆盖率报告
    pub async fn generate_coverage_report(&self) -> Result<CoverageStats> {
        println!("🔍 开始生成代码覆盖率报告...");

        // 确保输出目录存在
        self.ensure_output_dir()?;

        // 运行覆盖率测试
        let coverage_stats = self.run_coverage_tests().await?;

        // 生成HTML报告
        self.generate_html_report(&coverage_stats)?;

        // 生成JSON报告
        self.generate_json_report(&coverage_stats)?;

        // 生成LCOV报告
        self.generate_lcov_report()?;

        // 检查覆盖率阈值
        self.check_coverage_thresholds(&coverage_stats)?;

        // 生成总结报告
        self.generate_summary_report(&coverage_stats)?;

        println!("✅ 覆盖率报告生成完成！");
        println!("📊 HTML报告: {}/html/index.html", self.output_dir);
        println!("📄 JSON报告: {}/coverage.json", self.output_dir);
        println!("📋 LCOV报告: {}/lcov.info", self.output_dir);

        Ok(coverage_stats)
    }

    /// 确保输出目录存在
    fn ensure_output_dir(&self) -> Result<()> {
        let output_path = Path::new(&self.output_dir);
        if !output_path.exists() {
            fs::create_dir_all(output_path)
                .with_context(|| format!("创建输出目录失败: {}", self.output_dir))?;
        }

        // 创建子目录
        let html_dir = output_path.join("html");
        if !html_dir.exists() {
            fs::create_dir_all(&html_dir).with_context(|| "创建HTML输出目录失败")?;
        }

        Ok(())
    }

    /// 运行覆盖率测试
    async fn run_coverage_tests(&self) -> Result<CoverageStats> {
        println!("🧪 运行覆盖率测试...");

        // 首先运行HTML覆盖率报告
        let html_output = Command::new("cargo")
            .args(&["llvm-cov", "--html", "--ignore-run-fail", "--workspace"])
            .current_dir(&self.project_root)
            .output()
            .with_context(|| "执行cargo llvm-cov HTML命令失败")?;

        if !html_output.status.success() {
            let stderr = String::from_utf8_lossy(&html_output.stderr);
            println!("⚠️  HTML覆盖率测试警告: {}", stderr);
        }

        // 然后运行LCOV覆盖率报告
        let lcov_output = Command::new("cargo")
            .args(&["llvm-cov", "--lcov", "--ignore-run-fail", "--workspace"])
            .current_dir(&self.project_root)
            .output()
            .with_context(|| "执行cargo llvm-cov LCOV命令失败")?;

        if !lcov_output.status.success() {
            let stderr = String::from_utf8_lossy(&lcov_output.stderr);
            println!("⚠️  LCOV覆盖率测试警告: {}", stderr);
        }

        // 解析覆盖率统计信息
        self.parse_coverage_stats().await
    }

    /// 解析覆盖率统计信息
    async fn parse_coverage_stats(&self) -> Result<CoverageStats> {
        // 尝试从LCOV文件读取覆盖率数据
        let lcov_path = format!("{}/lcov.info", self.output_dir);

        if Path::new(&lcov_path).exists() {
            self.parse_lcov_file(&lcov_path)
        } else {
            // 如果LCOV文件不存在，返回默认统计信息
            println!("⚠️  未找到LCOV覆盖率文件，使用默认统计信息");
            Ok(CoverageStats {
                line_coverage: 0.0,
                branch_coverage: 0.0,
                function_coverage: 0.0,
                lines_covered: 0,
                lines_total: 0,
                branches_covered: 0,
                branches_total: 0,
                functions_covered: 0,
                functions_total: 0,
            })
        }
    }

    /// 解析LCOV文件获取覆盖率统计信息
    fn parse_lcov_file(&self, lcov_path: &str) -> Result<CoverageStats> {
        let content = fs::read_to_string(lcov_path)
            .with_context(|| format!("读取LCOV文件失败: {}", lcov_path))?;

        let mut total_lines_found = 0u32;
        let mut total_lines_hit = 0u32;
        let mut total_branches_found = 0u32;
        let mut total_branches_hit = 0u32;
        let mut total_functions_found = 0u32;
        let mut total_functions_hit = 0u32;

        for line in content.lines() {
            if line.starts_with("LF:") {
                // Lines Found (总行数)
                if let Ok(count) = line[3..].parse::<u32>() {
                    total_lines_found += count;
                }
            } else if line.starts_with("LH:") {
                // Lines Hit (覆盖的行数)
                if let Ok(count) = line[3..].parse::<u32>() {
                    total_lines_hit += count;
                }
            } else if line.starts_with("BRF:") {
                // Branches Found (总分支数)
                if let Ok(count) = line[4..].parse::<u32>() {
                    total_branches_found += count;
                }
            } else if line.starts_with("BRH:") {
                // Branches Hit (覆盖的分支数)
                if let Ok(count) = line[4..].parse::<u32>() {
                    total_branches_hit += count;
                }
            } else if line.starts_with("FNF:") {
                // Functions Found (总函数数)
                if let Ok(count) = line[4..].parse::<u32>() {
                    total_functions_found += count;
                }
            } else if line.starts_with("FNH:") {
                // Functions Hit (覆盖的函数数)
                if let Ok(count) = line[4..].parse::<u32>() {
                    total_functions_hit += count;
                }
            }
        }

        // 计算覆盖率百分比
        let line_coverage = if total_lines_found > 0 {
            ((total_lines_hit as f64) / (total_lines_found as f64)) * 100.0
        } else {
            0.0
        };

        let branch_coverage = if total_branches_found > 0 {
            ((total_branches_hit as f64) / (total_branches_found as f64)) * 100.0
        } else {
            0.0
        };

        let function_coverage = if total_functions_found > 0 {
            ((total_functions_hit as f64) / (total_functions_found as f64)) * 100.0
        } else {
            0.0
        };

        println!("📊 LCOV解析结果:");
        println!(
            "   行覆盖率: {}/{} ({:.2}%)",
            total_lines_hit, total_lines_found, line_coverage
        );
        println!(
            "   分支覆盖率: {}/{} ({:.2}%)",
            total_branches_hit, total_branches_found, branch_coverage
        );
        println!(
            "   函数覆盖率: {}/{} ({:.2}%)",
            total_functions_hit, total_functions_found, function_coverage
        );

        Ok(CoverageStats {
            line_coverage,
            branch_coverage,
            function_coverage,
            lines_covered: total_lines_hit,
            lines_total: total_lines_found,
            branches_covered: total_branches_hit,
            branches_total: total_branches_found,
            functions_covered: total_functions_hit,
            functions_total: total_functions_found,
        })
    }

    /// 从JSON数据中提取统计信息
    fn extract_stats_from_json(&self, data: &Value) -> Result<CoverageStats> {
        // 这里需要根据实际的JSON格式来解析
        // 由于cargo-llvm-cov的JSON格式可能会变化，这里提供一个基本的解析逻辑

        let lines_covered = data["data"][0]["totals"]["lines"]["covered"]
            .as_u64()
            .unwrap_or(0) as u32;
        let lines_total = data["data"][0]["totals"]["lines"]["count"]
            .as_u64()
            .unwrap_or(1) as u32;
        let line_coverage = if lines_total > 0 {
            ((lines_covered as f64) / (lines_total as f64)) * 100.0
        } else {
            0.0
        };

        let branches_covered = data["data"][0]["totals"]["branches"]["covered"]
            .as_u64()
            .unwrap_or(0) as u32;
        let branches_total = data["data"][0]["totals"]["branches"]["count"]
            .as_u64()
            .unwrap_or(1) as u32;
        let branch_coverage = if branches_total > 0 {
            ((branches_covered as f64) / (branches_total as f64)) * 100.0
        } else {
            0.0
        };

        let functions_covered = data["data"][0]["totals"]["functions"]["covered"]
            .as_u64()
            .unwrap_or(0) as u32;
        let functions_total = data["data"][0]["totals"]["functions"]["count"]
            .as_u64()
            .unwrap_or(1) as u32;
        let function_coverage = if functions_total > 0 {
            ((functions_covered as f64) / (functions_total as f64)) * 100.0
        } else {
            0.0
        };

        Ok(CoverageStats {
            line_coverage,
            branch_coverage,
            function_coverage,
            lines_covered,
            lines_total,
            branches_covered,
            branches_total,
            functions_covered,
            functions_total,
        })
    }

    /// 生成HTML报告
    fn generate_html_report(&self, _stats: &CoverageStats) -> Result<()> {
        println!("📊 生成HTML覆盖率报告...");
        // HTML报告由cargo llvm-cov自动生成
        Ok(())
    }

    /// 生成JSON报告
    fn generate_json_report(&self, _stats: &CoverageStats) -> Result<()> {
        println!("📄 生成JSON覆盖率报告...");
        // JSON报告由cargo llvm-cov自动生成
        Ok(())
    }

    /// 生成LCOV报告
    fn generate_lcov_report(&self) -> Result<()> {
        println!("📋 生成LCOV覆盖率报告...");
        // LCOV报告由cargo llvm-cov自动生成
        Ok(())
    }

    /// 检查覆盖率阈值
    fn check_coverage_thresholds(&self, stats: &CoverageStats) -> Result<()> {
        println!("🎯 检查覆盖率阈值...");

        let mut failed_checks = Vec::new();

        if stats.line_coverage < self.thresholds.line_threshold {
            failed_checks.push(format!(
                "行覆盖率 {:.2}% 低于阈值 {:.2}%",
                stats.line_coverage, self.thresholds.line_threshold
            ));
        }

        if stats.branch_coverage < self.thresholds.branch_threshold {
            failed_checks.push(format!(
                "分支覆盖率 {:.2}% 低于阈值 {:.2}%",
                stats.branch_coverage, self.thresholds.branch_threshold
            ));
        }

        if stats.function_coverage < self.thresholds.function_threshold {
            failed_checks.push(format!(
                "函数覆盖率 {:.2}% 低于阈值 {:.2}%",
                stats.function_coverage, self.thresholds.function_threshold
            ));
        }

        if !failed_checks.is_empty() {
            println!("⚠️  覆盖率阈值检查失败:");
            for check in &failed_checks {
                println!("   - {}", check);
            }
        } else {
            println!("✅ 所有覆盖率阈值检查通过！");
        }

        Ok(())
    }

    /// 生成总结报告
    fn generate_summary_report(&self, stats: &CoverageStats) -> Result<()> {
        let summary_path = format!("{}/coverage_summary.md", self.output_dir);
        let mut file = fs::File::create(&summary_path).with_context(|| "创建覆盖率总结文件失败")?;

        writeln!(file, "# 代码覆盖率报告总结")?;
        writeln!(file)?;
        writeln!(file, "## 📊 覆盖率统计")?;
        writeln!(file)?;
        writeln!(file, "| 类型 | 覆盖率 | 覆盖数量 | 总数量 | 状态 |")?;
        writeln!(file, "|------|--------|----------|--------|------|")?;

        let line_status = if stats.line_coverage >= self.thresholds.line_threshold {
            "✅"
        } else {
            "❌"
        };
        let branch_status = if stats.branch_coverage >= self.thresholds.branch_threshold {
            "✅"
        } else {
            "❌"
        };
        let function_status = if stats.function_coverage >= self.thresholds.function_threshold {
            "✅"
        } else {
            "❌"
        };

        writeln!(
            file,
            "| 行覆盖率 | {:.2}% | {} | {} | {} |",
            stats.line_coverage, stats.lines_covered, stats.lines_total, line_status
        )?;
        writeln!(
            file,
            "| 分支覆盖率 | {:.2}% | {} | {} | {} |",
            stats.branch_coverage, stats.branches_covered, stats.branches_total, branch_status
        )?;
        writeln!(
            file,
            "| 函数覆盖率 | {:.2}% | {} | {} | {} |",
            stats.function_coverage,
            stats.functions_covered,
            stats.functions_total,
            function_status
        )?;

        writeln!(file)?;
        writeln!(file, "## 🎯 阈值配置")?;
        writeln!(file)?;
        writeln!(
            file,
            "- 行覆盖率阈值: {:.2}%",
            self.thresholds.line_threshold
        )?;
        writeln!(
            file,
            "- 分支覆盖率阈值: {:.2}%",
            self.thresholds.branch_threshold
        )?;
        writeln!(
            file,
            "- 函数覆盖率阈值: {:.2}%",
            self.thresholds.function_threshold
        )?;

        writeln!(file)?;
        writeln!(file, "## 📁 报告文件")?;
        writeln!(file)?;
        writeln!(file, "- HTML报告: `{}/html/index.html`", self.output_dir)?;
        writeln!(file, "- JSON报告: `{}/coverage.json`", self.output_dir)?;
        writeln!(file, "- LCOV报告: `{}/lcov.info`", self.output_dir)?;

        println!("📝 覆盖率总结报告已生成: {}", summary_path);

        Ok(())
    }
}

/// 主函数 - 用于独立运行覆盖率报告生成
#[tokio::main]
async fn main() -> Result<()> {
    let generator =
        CoverageReportGenerator::new(".", "target/cov").with_thresholds(CoverageThresholds {
            line_threshold: 90.0,
            branch_threshold: 85.0,
            function_threshold: 90.0,
        });

    let stats = generator.generate_coverage_report().await?;

    println!("\n🎉 覆盖率报告生成完成！");
    println!("📊 行覆盖率: {:.2}%", stats.line_coverage);
    println!("🌿 分支覆盖率: {:.2}%", stats.branch_coverage);
    println!("🔧 函数覆盖率: {:.2}%", stats.function_coverage);

    Ok(())
}
