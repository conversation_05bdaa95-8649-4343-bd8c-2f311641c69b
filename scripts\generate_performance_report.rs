// 性能基准测试报告生成器 - 任务17专用
// 解析Criterion.rs、wrk、ab的测试结果并生成综合HTML报告

use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

#[derive(Debug, Serialize, Deserialize)]
struct PerformanceReport {
    timestamp: String,
    test_environment: TestEnvironment,
    criterion_results: Vec<CriterionResult>,
    wrk_results: Vec<WrkResult>,
    ab_results: Vec<AbResult>,
    summary: PerformanceSummary,
}

#[derive(Debug, Serialize, Deserialize)]
struct TestEnvironment {
    rust_version: String,
    axum_version: String,
    database_type: String,
    cache_type: String,
    system_info: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct CriterionResult {
    benchmark_name: String,
    mean_time_ns: f64,
    std_dev_ns: f64,
    throughput_ops_per_sec: Option<f64>,
    samples: usize,
}

#[derive(Debug, Serialize, Deserialize)]
struct WrkResult {
    endpoint: String,
    duration_sec: u32,
    threads: u32,
    connections: u32,
    requests_per_sec: f64,
    avg_latency_ms: f64,
    max_latency_ms: f64,
    total_requests: u64,
    total_errors: u64,
}

#[derive(Debug, Serialize, Deserialize)]
struct AbResult {
    endpoint: String,
    total_requests: u64,
    concurrency: u32,
    requests_per_sec: f64,
    time_per_request_ms: f64,
    transfer_rate_kb_per_sec: f64,
    failed_requests: u64,
}

#[derive(Debug, Serialize, Deserialize)]
struct PerformanceSummary {
    best_api_response_time_ms: f64,
    best_throughput_rps: f64,
    database_performance_score: f64,
    overall_performance_grade: String,
    recommendations: Vec<String>,
}

/// 解析Criterion.rs JSON结果
fn parse_criterion_results(criterion_dir: &Path) -> Vec<CriterionResult> {
    let mut results = Vec::new();

    if !criterion_dir.exists() {
        return results;
    }

    // 遍历Criterion输出目录
    if let Ok(entries) = fs::read_dir(criterion_dir) {
        for entry in entries.flatten() {
            if entry.file_type().map(|ft| ft.is_dir()).unwrap_or(false) {
                let benchmark_dir = entry.path();
                let estimates_file = benchmark_dir.join("base/estimates.json");

                if estimates_file.exists() {
                    if let Ok(content) = fs::read_to_string(&estimates_file) {
                        if let Ok(estimates) = serde_json::from_str::<serde_json::Value>(&content) {
                            let benchmark_name = benchmark_dir
                                .file_name()
                                .and_then(|n| n.to_str())
                                .unwrap_or("unknown")
                                .to_string();

                            let mean_time_ns =
                                estimates["mean"]["point_estimate"].as_f64().unwrap_or(0.0);

                            let std_dev_ns = estimates["std_dev"]["point_estimate"]
                                .as_f64()
                                .unwrap_or(0.0);

                            results.push(CriterionResult {
                                benchmark_name,
                                mean_time_ns,
                                std_dev_ns,
                                throughput_ops_per_sec: None,
                                samples: 100, // 默认值
                            });
                        }
                    }
                }
            }
        }
    }

    results
}

/// 解析wrk结果
fn parse_wrk_results(reports_dir: &Path) -> Vec<WrkResult> {
    let mut results = Vec::new();

    if !reports_dir.exists() {
        return results;
    }

    if let Ok(entries) = fs::read_dir(reports_dir) {
        for entry in entries.flatten() {
            let path = entry.path();
            if path.extension().and_then(|s| s.to_str()) == Some("txt")
                && path
                    .file_name()
                    .and_then(|s| s.to_str())
                    .unwrap_or("")
                    .starts_with("wrk_")
            {
                if let Ok(content) = fs::read_to_string(&path) {
                    if let Some(result) = parse_wrk_content(&content) {
                        results.push(result);
                    }
                }
            }
        }
    }

    results
}

/// 解析wrk输出内容
fn parse_wrk_content(content: &str) -> Option<WrkResult> {
    let endpoint = "unknown".to_string();
    let mut requests_per_sec = 0.0;
    let mut avg_latency_ms = 0.0;
    let max_latency_ms = 0.0;
    let mut total_requests = 0;

    for line in content.lines() {
        if line.contains("Requests/sec:") {
            if let Some(rps_str) = line.split_whitespace().nth(1) {
                requests_per_sec = rps_str.parse().unwrap_or(0.0);
            }
        } else if line.contains("Latency") && line.contains("avg") {
            // 解析延迟信息
            let parts: Vec<&str> = line.split_whitespace().collect();
            if parts.len() >= 2 {
                if let Ok(latency) = parts[1].trim_end_matches("ms").parse::<f64>() {
                    avg_latency_ms = latency;
                }
            }
        } else if line.contains("requests in") {
            // 解析总请求数
            if let Some(req_str) = line.split_whitespace().next() {
                total_requests = req_str.parse().unwrap_or(0);
            }
        }
    }

    Some(WrkResult {
        endpoint,
        duration_sec: 30, // 默认值
        threads: 4,       // 默认值
        connections: 100, // 默认值
        requests_per_sec,
        avg_latency_ms,
        max_latency_ms,
        total_requests,
        total_errors: 0,
    })
}

/// 解析ab结果
fn parse_ab_results(reports_dir: &Path) -> Vec<AbResult> {
    let mut results = Vec::new();

    if !reports_dir.exists() {
        return results;
    }

    if let Ok(entries) = fs::read_dir(reports_dir) {
        for entry in entries.flatten() {
            let path = entry.path();
            if path.extension().and_then(|s| s.to_str()) == Some("txt")
                && path
                    .file_name()
                    .and_then(|s| s.to_str())
                    .unwrap_or("")
                    .starts_with("ab_")
            {
                if let Ok(content) = fs::read_to_string(&path) {
                    if let Some(result) = parse_ab_content(&content) {
                        results.push(result);
                    }
                }
            }
        }
    }

    results
}

/// 解析ab输出内容
fn parse_ab_content(content: &str) -> Option<AbResult> {
    let endpoint = "unknown".to_string();
    let mut total_requests = 0;
    let mut requests_per_sec = 0.0;
    let mut time_per_request_ms = 0.0;
    let mut failed_requests = 0;

    for line in content.lines() {
        if line.contains("Requests per second:") {
            if let Some(rps_str) = line.split_whitespace().nth(3) {
                requests_per_sec = rps_str.parse().unwrap_or(0.0);
            }
        } else if line.contains("Time per request:") && line.contains("mean") {
            if let Some(time_str) = line.split_whitespace().nth(3) {
                time_per_request_ms = time_str.parse().unwrap_or(0.0);
            }
        } else if line.contains("Complete requests:") {
            if let Some(req_str) = line.split_whitespace().nth(2) {
                total_requests = req_str.parse().unwrap_or(0);
            }
        } else if line.contains("Failed requests:") {
            if let Some(fail_str) = line.split_whitespace().nth(2) {
                failed_requests = fail_str.parse().unwrap_or(0);
            }
        }
    }

    Some(AbResult {
        endpoint,
        total_requests,
        concurrency: 100, // 默认值
        requests_per_sec,
        time_per_request_ms,
        transfer_rate_kb_per_sec: 0.0,
        failed_requests,
    })
}

/// 生成性能摘要
fn generate_performance_summary(
    criterion_results: &[CriterionResult],
    wrk_results: &[WrkResult],
    ab_results: &[AbResult],
) -> PerformanceSummary {
    let best_api_response_time_ms = criterion_results
        .iter()
        .map(|r| r.mean_time_ns / 1_000_000.0)
        .fold(f64::INFINITY, f64::min);

    let best_throughput_rps = {
        let wrk_max = wrk_results
            .iter()
            .map(|r| r.requests_per_sec)
            .fold(0.0, f64::max);

        let ab_max = ab_results
            .iter()
            .map(|r| r.requests_per_sec)
            .fold(0.0, f64::max);

        wrk_max.max(ab_max)
    };

    let database_performance_score = if best_throughput_rps > 1000.0 {
        9.0
    } else if best_throughput_rps > 500.0 {
        8.0
    } else if best_throughput_rps > 100.0 {
        7.0
    } else {
        6.0
    };

    let overall_performance_grade = (if database_performance_score >= 9.0 {
        "A+"
    } else if database_performance_score >= 8.0 {
        "A"
    } else if database_performance_score >= 7.0 {
        "B"
    } else {
        "C"
    })
    .to_string();

    let mut recommendations = Vec::new();

    if best_throughput_rps < 500.0 {
        recommendations.push("考虑优化数据库查询和连接池配置".to_string());
    }

    if best_api_response_time_ms > 100.0 {
        recommendations.push("API响应时间较高，建议添加缓存层".to_string());
    }

    recommendations.push("定期监控性能指标并设置告警".to_string());

    PerformanceSummary {
        best_api_response_time_ms,
        best_throughput_rps,
        database_performance_score,
        overall_performance_grade,
        recommendations,
    }
}

/// 生成HTML报告
fn generate_html_report(report: &PerformanceReport) -> String {
    format!(
        r#"<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Axum项目性能基准测试报告 - 任务17</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; }}
        h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        .summary {{ background: #ecf0f1; padding: 20px; border-radius: 8px; margin: 20px 0; }}
        .metric {{ display: inline-block; margin: 10px 20px; text-align: center; }}
        .metric-value {{ font-size: 2em; font-weight: bold; color: #e74c3c; }}
        .metric-label {{ color: #7f8c8d; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }}
        th {{ background-color: #3498db; color: white; }}
        .grade-A {{ color: #27ae60; font-weight: bold; }}
        .grade-B {{ color: #f39c12; font-weight: bold; }}
        .grade-C {{ color: #e74c3c; font-weight: bold; }}
        .recommendations {{ background: #d5f4e6; padding: 15px; border-radius: 5px; margin: 20px 0; }}
        .timestamp {{ text-align: center; color: #7f8c8d; margin-top: 30px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Axum项目性能基准测试报告</h1>
        <p style="text-align: center; color: #7f8c8d;">任务17 - SQLite vs PostgreSQL+DragonflyDB 性能对比</p>
        
        <div class="summary">
            <h2>📊 性能摘要</h2>
            <div class="metric">
                <div class="metric-value">{:.2}</div>
                <div class="metric-label">最佳响应时间 (ms)</div>
            </div>
            <div class="metric">
                <div class="metric-value">{:.0}</div>
                <div class="metric-label">最高吞吐量 (RPS)</div>
            </div>
            <div class="metric">
                <div class="metric-value grade-{}">{}</div>
                <div class="metric-label">综合性能等级</div>
            </div>
        </div>
        
        <h2>🔧 测试环境</h2>
        <table>
            <tr><th>项目</th><th>版本/信息</th></tr>
            <tr><td>Rust版本</td><td>{}</td></tr>
            <tr><td>Axum版本</td><td>{}</td></tr>
            <tr><td>数据库类型</td><td>{}</td></tr>
            <tr><td>缓存类型</td><td>{}</td></tr>
        </table>
        
        <h2>📈 Criterion.rs 基准测试结果</h2>
        <table>
            <tr><th>测试名称</th><th>平均时间 (ms)</th><th>标准差 (ms)</th><th>样本数</th></tr>
            {}
        </table>
        
        <h2>⚡ wrk 负载测试结果</h2>
        <table>
            <tr><th>端点</th><th>RPS</th><th>平均延迟 (ms)</th><th>总请求数</th><th>错误数</th></tr>
            {}
        </table>
        
        <h2>🎯 Apache Bench 测试结果</h2>
        <table>
            <tr><th>端点</th><th>RPS</th><th>每请求时间 (ms)</th><th>总请求数</th><th>失败请求数</th></tr>
            {}
        </table>
        
        <div class="recommendations">
            <h2>💡 性能优化建议</h2>
            <ul>
                {}
            </ul>
        </div>
        
        <div class="timestamp">
            <p>报告生成时间: {}</p>
        </div>
    </div>
</body>
</html>"#,
        report.summary.best_api_response_time_ms,
        report.summary.best_throughput_rps,
        report
            .summary
            .overall_performance_grade
            .chars()
            .next()
            .unwrap_or('C'),
        report.summary.overall_performance_grade,
        report.test_environment.rust_version,
        report.test_environment.axum_version,
        report.test_environment.database_type,
        report.test_environment.cache_type,
        report
            .criterion_results
            .iter()
            .map(|r| format!(
                "<tr><td>{}</td><td>{:.2}</td><td>{:.2}</td><td>{}</td></tr>",
                r.benchmark_name,
                r.mean_time_ns / 1_000_000.0,
                r.std_dev_ns / 1_000_000.0,
                r.samples
            ))
            .collect::<Vec<_>>()
            .join(""),
        report
            .wrk_results
            .iter()
            .map(|r| format!(
                "<tr><td>{}</td><td>{:.0}</td><td>{:.2}</td><td>{}</td><td>{}</td></tr>",
                r.endpoint, r.requests_per_sec, r.avg_latency_ms, r.total_requests, r.total_errors
            ))
            .collect::<Vec<_>>()
            .join(""),
        report
            .ab_results
            .iter()
            .map(|r| format!(
                "<tr><td>{}</td><td>{:.0}</td><td>{:.2}</td><td>{}</td><td>{}</td></tr>",
                r.endpoint,
                r.requests_per_sec,
                r.time_per_request_ms,
                r.total_requests,
                r.failed_requests
            ))
            .collect::<Vec<_>>()
            .join(""),
        report
            .summary
            .recommendations
            .iter()
            .map(|r| format!("<li>{}</li>", r))
            .collect::<Vec<_>>()
            .join(""),
        report.timestamp
    )
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 生成性能基准测试报告...");

    let criterion_dir = Path::new("target/criterion");
    let reports_dir = Path::new("reports/benchmark");

    // 解析测试结果
    let criterion_results = parse_criterion_results(criterion_dir);
    let wrk_results = parse_wrk_results(reports_dir);
    let ab_results = parse_ab_results(reports_dir);

    println!("📊 找到 {} 个Criterion结果", criterion_results.len());
    println!("⚡ 找到 {} 个wrk结果", wrk_results.len());
    println!("🎯 找到 {} 个ab结果", ab_results.len());

    // 生成性能摘要
    let summary = generate_performance_summary(&criterion_results, &wrk_results, &ab_results);

    // 创建完整报告
    let report = PerformanceReport {
        timestamp: std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_secs()
            .to_string(),
        test_environment: TestEnvironment {
            rust_version: "2024 Edition".to_string(),
            axum_version: "0.8.4".to_string(),
            database_type: "PostgreSQL 17 + DragonflyDB".to_string(),
            cache_type: "DragonflyDB (Redis兼容)".to_string(),
            system_info: "Windows 10 + WSL2".to_string(),
        },
        criterion_results,
        wrk_results,
        ab_results,
        summary,
    };

    // 生成HTML报告
    let html_content = generate_html_report(&report);

    // 确保报告目录存在
    fs::create_dir_all("reports/benchmark")?;

    // 保存HTML报告
    let report_path = "reports/benchmark/performance_report.html";
    fs::write(report_path, html_content)?;

    // 保存JSON报告
    let json_path = "reports/benchmark/performance_report.json";
    let json_content = serde_json::to_string_pretty(&report)?;
    fs::write(json_path, json_content)?;

    println!("✅ 报告生成完成!");
    println!("📄 HTML报告: {}", report_path);
    println!("📋 JSON报告: {}", json_path);
    println!(
        "🏆 综合性能等级: {}",
        report.summary.overall_performance_grade
    );

    Ok(())
}
