# DragonflyDB 综合健康检查脚本
# 检查服务状态、性能指标、安全配置和监控状态

param(
    [string]$Host = "127.0.0.1",
    [int]$Port = 6379,
    [string]$MonitorUser = "monitor",
    [string]$MonitorPassword = "monitor_password_2025",
    [int]$PrometheusPort = 9090,
    [int]$RedisExporterPort = 9121,
    [switch]$Detailed = $false,
    [switch]$Json = $false
)

# 健康检查结果对象
$healthCheck = @{
    timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    overall_status = "UNKNOWN"
    checks = @{}
    metrics = @{}
    recommendations = @()
}

function Test-ServiceConnection {
    param([string]$ServiceHost, [int]$ServicePort, [string]$ServiceName)
    
    try {
        $tcpClient = New-Object System.Net.Sockets.TcpClient
        $tcpClient.ConnectAsync($ServiceHost, $ServicePort).Wait(5000)
        $connected = $tcpClient.Connected
        $tcpClient.Close()
        return $connected
    } catch {
        return $false
    }
}

function Get-DragonflyInfo {
    param([string]$InfoSection = "")
    
    try {
        if ($InfoSection) {
            $result = & redis-cli -h $Host -p $Port --user $MonitorUser -a $MonitorPassword info $InfoSection 2>&1
        } else {
            $result = & redis-cli -h $Host -p $Port --user $MonitorUser -a $MonitorPassword info 2>&1
        }
        return $result
    } catch {
        return $null
    }
}

function Parse-InfoOutput {
    param([string[]]$InfoOutput)
    
    $parsed = @{}
    foreach ($line in $InfoOutput) {
        if ($line -match "^([^#][^:]+):(.+)$") {
            $key = $matches[1].Trim()
            $value = $matches[2].Trim()
            $parsed[$key] = $value
        }
    }
    return $parsed
}

Write-Host "=== DragonflyDB 综合健康检查 ===" -ForegroundColor Green
Write-Host "检查时间: $(Get-Date)" -ForegroundColor Yellow
Write-Host "目标服务器: $Host`:$Port" -ForegroundColor Yellow

# 1. 基础连接检查
Write-Host "`n[1/8] 基础连接检查..." -ForegroundColor Cyan
$connectionTest = Test-ServiceConnection -ServiceHost $Host -ServicePort $Port -ServiceName "DragonflyDB"
$healthCheck.checks.connection = @{
    status = if ($connectionTest) { "PASS" } else { "FAIL" }
    message = if ($connectionTest) { "DragonflyDB连接正常" } else { "无法连接到DragonflyDB" }
}

if (-not $connectionTest) {
    Write-Host "✗ 无法连接到DragonflyDB，终止检查" -ForegroundColor Red
    $healthCheck.overall_status = "CRITICAL"
    if ($Json) {
        $healthCheck | ConvertTo-Json -Depth 3
    }
    exit 1
}

Write-Host "✓ DragonflyDB连接正常" -ForegroundColor Green

# 2. 认证检查
Write-Host "`n[2/8] 认证和权限检查..." -ForegroundColor Cyan
try {
    $pingResult = & redis-cli -h $Host -p $Port --user $MonitorUser -a $MonitorPassword ping 2>&1
    $authSuccess = $pingResult -eq "PONG"
    
    $healthCheck.checks.authentication = @{
        status = if ($authSuccess) { "PASS" } else { "FAIL" }
        message = if ($authSuccess) { "监控用户认证成功" } else { "监控用户认证失败: $pingResult" }
    }
    
    if ($authSuccess) {
        Write-Host "✓ 监控用户认证成功" -ForegroundColor Green
    } else {
        Write-Host "✗ 监控用户认证失败" -ForegroundColor Red
    }
} catch {
    $healthCheck.checks.authentication = @{
        status = "FAIL"
        message = "认证检查异常: $($_.Exception.Message)"
    }
    Write-Host "✗ 认证检查异常" -ForegroundColor Red
}

# 3. 服务器信息检查
Write-Host "`n[3/8] 服务器信息检查..." -ForegroundColor Cyan
$serverInfo = Get-DragonflyInfo -InfoSection "server"
if ($serverInfo) {
    $serverData = Parse-InfoOutput -InfoOutput $serverInfo
    
    $healthCheck.checks.server_info = @{
        status = "PASS"
        version = $serverData["redis_version"]
        uptime = $serverData["uptime_in_seconds"]
        process_id = $serverData["process_id"]
    }
    
    Write-Host "✓ 服务器版本: $($serverData['redis_version'])" -ForegroundColor Green
    Write-Host "✓ 运行时间: $($serverData['uptime_in_seconds']) 秒" -ForegroundColor Green
} else {
    $healthCheck.checks.server_info = @{
        status = "FAIL"
        message = "无法获取服务器信息"
    }
    Write-Host "✗ 无法获取服务器信息" -ForegroundColor Red
}

# 4. 内存使用检查
Write-Host "`n[4/8] 内存使用检查..." -ForegroundColor Cyan
$memoryInfo = Get-DragonflyInfo -InfoSection "memory"
if ($memoryInfo) {
    $memoryData = Parse-InfoOutput -InfoOutput $memoryInfo
    
    $usedMemory = [long]$memoryData["used_memory"]
    $maxMemory = [long]$memoryData["maxmemory"]
    $memoryUsagePercent = if ($maxMemory -gt 0) { ($usedMemory / $maxMemory) * 100 } else { 0 }
    
    $memoryStatus = if ($memoryUsagePercent -lt 85) { "PASS" } elseif ($memoryUsagePercent -lt 95) { "WARN" } else { "FAIL" }
    
    $healthCheck.checks.memory = @{
        status = $memoryStatus
        used_memory_human = $memoryData["used_memory_human"]
        max_memory_human = $memoryData["maxmemory_human"]
        usage_percent = [math]::Round($memoryUsagePercent, 2)
        fragmentation_ratio = $memoryData["mem_fragmentation_ratio"]
    }
    
    $healthCheck.metrics.memory_usage_percent = $memoryUsagePercent
    
    Write-Host "✓ 内存使用: $($memoryData['used_memory_human']) / $($memoryData['maxmemory_human']) ($([math]::Round($memoryUsagePercent, 2))%)" -ForegroundColor Green
    
    if ($memoryUsagePercent -gt 85) {
        $healthCheck.recommendations += "内存使用率超过85%，建议优化内存使用或增加内存限制"
        Write-Host "⚠ 内存使用率较高: $([math]::Round($memoryUsagePercent, 2))%" -ForegroundColor Yellow
    }
} else {
    $healthCheck.checks.memory = @{
        status = "FAIL"
        message = "无法获取内存信息"
    }
    Write-Host "✗ 无法获取内存信息" -ForegroundColor Red
}

# 5. 客户端连接检查
Write-Host "`n[5/8] 客户端连接检查..." -ForegroundColor Cyan
$clientsInfo = Get-DragonflyInfo -InfoSection "clients"
if ($clientsInfo) {
    $clientsData = Parse-InfoOutput -InfoOutput $clientsInfo
    
    $connectedClients = [int]$clientsData["connected_clients"]
    $maxClients = 100000  # 从配置中获取
    $connectionUsagePercent = ($connectedClients / $maxClients) * 100
    
    $connectionStatus = if ($connectionUsagePercent -lt 80) { "PASS" } elseif ($connectionUsagePercent -lt 90) { "WARN" } else { "FAIL" }
    
    $healthCheck.checks.connections = @{
        status = $connectionStatus
        connected_clients = $connectedClients
        max_clients = $maxClients
        usage_percent = [math]::Round($connectionUsagePercent, 2)
    }
    
    $healthCheck.metrics.connected_clients = $connectedClients
    
    Write-Host "✓ 连接数: $connectedClients / $maxClients ($([math]::Round($connectionUsagePercent, 2))%)" -ForegroundColor Green
    
    if ($connectionUsagePercent -gt 80) {
        $healthCheck.recommendations += "客户端连接数超过80%，建议监控连接池使用情况"
        Write-Host "⚠ 客户端连接数较高: $([math]::Round($connectionUsagePercent, 2))%" -ForegroundColor Yellow
    }
} else {
    $healthCheck.checks.connections = @{
        status = "FAIL"
        message = "无法获取客户端信息"
    }
    Write-Host "✗ 无法获取客户端信息" -ForegroundColor Red
}

# 6. 性能统计检查
Write-Host "`n[6/8] 性能统计检查..." -ForegroundColor Cyan
$statsInfo = Get-DragonflyInfo -InfoSection "stats"
if ($statsInfo) {
    $statsData = Parse-InfoOutput -InfoOutput $statsInfo
    
    $totalCommands = [long]$statsData["total_commands_processed"]
    $opsPerSec = [double]$statsData["instantaneous_ops_per_sec"]
    $keyspaceHits = [long]$statsData["keyspace_hits"]
    $keyspaceMisses = [long]$statsData["keyspace_misses"]
    
    $hitRate = if (($keyspaceHits + $keyspaceMisses) -gt 0) { 
        ($keyspaceHits / ($keyspaceHits + $keyspaceMisses)) * 100 
    } else { 
        100 
    }
    
    $performanceStatus = if ($hitRate -gt 80 -and $opsPerSec -lt 50000) { "PASS" } elseif ($hitRate -gt 60) { "WARN" } else { "FAIL" }
    
    $healthCheck.checks.performance = @{
        status = $performanceStatus
        total_commands = $totalCommands
        ops_per_sec = $opsPerSec
        hit_rate_percent = [math]::Round($hitRate, 2)
        keyspace_hits = $keyspaceHits
        keyspace_misses = $keyspaceMisses
    }
    
    $healthCheck.metrics.hit_rate_percent = $hitRate
    $healthCheck.metrics.ops_per_sec = $opsPerSec
    
    Write-Host "✓ 缓存命中率: $([math]::Round($hitRate, 2))%" -ForegroundColor Green
    Write-Host "✓ 当前操作率: $opsPerSec ops/sec" -ForegroundColor Green
    
    if ($hitRate -lt 80) {
        $healthCheck.recommendations += "缓存命中率低于80%，建议优化缓存策略"
        Write-Host "⚠ 缓存命中率较低: $([math]::Round($hitRate, 2))%" -ForegroundColor Yellow
    }
} else {
    $healthCheck.checks.performance = @{
        status = "FAIL"
        message = "无法获取性能统计"
    }
    Write-Host "✗ 无法获取性能统计" -ForegroundColor Red
}

# 7. 监控服务检查
Write-Host "`n[7/8] 监控服务检查..." -ForegroundColor Cyan

# 检查Prometheus
$prometheusConnection = Test-ServiceConnection -ServiceHost $Host -ServicePort $PrometheusPort -ServiceName "Prometheus"
$redisExporterConnection = Test-ServiceConnection -ServiceHost $Host -ServicePort $RedisExporterPort -ServiceName "Redis Exporter"

$healthCheck.checks.monitoring = @{
    prometheus = @{
        status = if ($prometheusConnection) { "PASS" } else { "FAIL" }
        message = if ($prometheusConnection) { "Prometheus连接正常" } else { "无法连接到Prometheus" }
    }
    redis_exporter = @{
        status = if ($redisExporterConnection) { "PASS" } else { "FAIL" }
        message = if ($redisExporterConnection) { "Redis Exporter连接正常" } else { "无法连接到Redis Exporter" }
    }
}

if ($prometheusConnection) {
    Write-Host "✓ Prometheus监控正常" -ForegroundColor Green
} else {
    Write-Host "✗ Prometheus监控异常" -ForegroundColor Red
}

if ($redisExporterConnection) {
    Write-Host "✓ Redis Exporter正常" -ForegroundColor Green
} else {
    Write-Host "✗ Redis Exporter异常" -ForegroundColor Red
}

# 8. 整体状态评估
Write-Host "`n[8/8] 整体状态评估..." -ForegroundColor Cyan

$failedChecks = ($healthCheck.checks.Values | Where-Object { $_.status -eq "FAIL" }).Count
$warnChecks = ($healthCheck.checks.Values | Where-Object { $_.status -eq "WARN" }).Count

if ($failedChecks -gt 0) {
    $healthCheck.overall_status = "CRITICAL"
    Write-Host "✗ 整体状态: 严重 ($failedChecks 个失败检查)" -ForegroundColor Red
} elseif ($warnChecks -gt 0) {
    $healthCheck.overall_status = "WARNING"
    Write-Host "⚠ 整体状态: 警告 ($warnChecks 个警告检查)" -ForegroundColor Yellow
} else {
    $healthCheck.overall_status = "HEALTHY"
    Write-Host "✓ 整体状态: 健康" -ForegroundColor Green
}

# 输出建议
if ($healthCheck.recommendations.Count -gt 0) {
    Write-Host "`n=== 优化建议 ===" -ForegroundColor Cyan
    foreach ($recommendation in $healthCheck.recommendations) {
        Write-Host "• $recommendation" -ForegroundColor Yellow
    }
}

# 输出结果
if ($Json) {
    Write-Host "`n=== JSON输出 ===" -ForegroundColor Cyan
    $healthCheck | ConvertTo-Json -Depth 4
} elseif ($Detailed) {
    Write-Host "`n=== 详细信息 ===" -ForegroundColor Cyan
    $healthCheck | ConvertTo-Json -Depth 4 | ConvertFrom-Json | Format-List
}

Write-Host "`n=== 健康检查完成 ===" -ForegroundColor Green
exit $(if ($healthCheck.overall_status -eq "HEALTHY") { 0 } elseif ($healthCheck.overall_status -eq "WARNING") { 1 } else { 2 })
