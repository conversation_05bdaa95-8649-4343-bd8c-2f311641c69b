# 混合网络解决方案
# 结合多种技术解决WSL2 + Podman网络问题

param(
    [switch]$Install,
    [switch]$Configure,
    [switch]$Test
)

Write-Host "🌐 混合网络解决方案" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

function Test-AdminRights {
    return ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
}

function Install-Prerequisites {
    Write-Host "📦 安装前置组件..." -ForegroundColor Cyan
    
    # 检查并安装Chocolatey
    if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
        Write-Host "🍫 安装Chocolatey..." -ForegroundColor Yellow
        Set-ExecutionPolicy Bypass -Scope Process -Force
        [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
        iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))
    }
    
    # 安装网络工具
    Write-Host "🔧 安装网络工具..." -ForegroundColor Yellow
    choco install nmap netcat -y
    
    Write-Host "✅ 前置组件安装完成" -ForegroundColor Green
}

function Configure-HybridNetwork {
    Write-Host "⚙️ 配置混合网络..." -ForegroundColor Cyan
    
    # 1. 配置WSL2网络
    Write-Host "🔧 配置WSL2网络..." -ForegroundColor Yellow
    
    $wslConfig = @"
[wsl2]
# 混合网络配置
localhostForwarding=true
memory=4GB
processors=2
swap=2GB

# 网络优化
kernelCommandLine=cgroup_no_v1=all systemd.unified_cgroup_hierarchy=1

[experimental]
sparseVhd=true
autoMemoryReclaim=gradual
networkingMode=NAT
"@
    
    Set-Content -Path "$env:USERPROFILE\.wslconfig" -Value $wslConfig -Encoding UTF8
    
    # 2. 重启WSL2
    Write-Host "🔄 重启WSL2..." -ForegroundColor Yellow
    wsl --shutdown
    Start-Sleep 10
    wsl echo "WSL2 restarted"
    
    # 3. 配置Podman网络
    Write-Host "🐳 配置Podman网络..." -ForegroundColor Yellow
    
    $podmanScript = @"
#!/bin/bash
# Podman网络配置脚本

echo "🔧 配置Podman网络..."

# 创建专用网络
podman network create --driver bridge --subnet *********/24 --gateway ********* axum_network || true

# 停止现有容器
podman stop axum_dragonflydb || true
podman rm axum_dragonflydb || true

# 使用host网络模式重新创建容器
podman run -d \
    --name axum_dragonflydb \
    --network host \
    -v dragonflydb_data:/data \
    --restart unless-stopped \
    docker.dragonflydb.io/dragonflydb/dragonfly:latest \
    dragonfly \
    --logtostderr \
    --requirepass=dragonfly_secure_password_2025 \
    --cache_mode=true \
    --dbnum=16 \
    --bind=127.0.0.1 \
    --port=6379 \
    --maxmemory=2gb

echo "✅ Podman网络配置完成"
"@
    
    # 写入脚本到WSL2
    $podmanScript | wsl tee /tmp/configure_podman.sh > $null
    wsl chmod +x /tmp/configure_podman.sh
    wsl /tmp/configure_podman.sh
    
    # 4. 配置Windows防火墙
    if (Test-AdminRights) {
        Write-Host "🛡️ 配置Windows防火墙..." -ForegroundColor Yellow
        
        try {
            # 允许WSL2网络通信
            New-NetFirewallRule -DisplayName "WSL2-DragonflyDB" -Direction Inbound -Protocol TCP -LocalPort 6379 -Action Allow -ErrorAction SilentlyContinue
            Write-Host "✅ 防火墙规则已添加" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ 防火墙配置失败: $_" -ForegroundColor Yellow
        }
    }
    
    Write-Host "✅ 混合网络配置完成" -ForegroundColor Green
}

function Test-NetworkConnectivity {
    Write-Host "🧪 测试网络连接..." -ForegroundColor Cyan
    
    # 测试WSL2内部连接
    Write-Host "📡 测试WSL2内部连接..." -ForegroundColor Yellow
    $wslTest = wsl -e bash -c "echo -e 'AUTH dragonfly_secure_password_2025\nPING' | timeout 5 nc localhost 6379 2>/dev/null | grep PONG"
    
    if ($wslTest -match "PONG") {
        Write-Host "✅ WSL2内部连接成功" -ForegroundColor Green
    } else {
        Write-Host "❌ WSL2内部连接失败" -ForegroundColor Red
    }
    
    # 测试Windows主机连接
    Write-Host "🖥️ 测试Windows主机连接..." -ForegroundColor Yellow
    
    try {
        $testResult = Test-NetConnection -ComputerName localhost -Port 6379 -WarningAction SilentlyContinue
        if ($testResult.TcpTestSucceeded) {
            Write-Host "✅ Windows主机端口连接成功" -ForegroundColor Green
        } else {
            Write-Host "❌ Windows主机端口连接失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ 连接测试失败: $_" -ForegroundColor Red
    }
    
    # 测试Redis协议
    Write-Host "🔗 测试Redis协议连接..." -ForegroundColor Yellow
    
    $env:CACHE_URL = "redis://:dragonfly_secure_password_2025@localhost:6379"
    $testOutput = cargo run --bin test_cache_connection 2>&1
    
    if ($testOutput -match "DragonflyDB连接测试完全成功") {
        Write-Host "✅ Redis协议连接成功" -ForegroundColor Green
    } else {
        Write-Host "❌ Redis协议连接失败" -ForegroundColor Red
        Write-Host "详细信息: $testOutput" -ForegroundColor Gray
    }
}

# 主逻辑
if ($Install) {
    if (-not (Test-AdminRights)) {
        Write-Host "❌ 需要管理员权限安装组件" -ForegroundColor Red
        exit 1
    }
    Install-Prerequisites
    exit 0
}

if ($Configure) {
    Configure-HybridNetwork
    exit 0
}

if ($Test) {
    Test-NetworkConnectivity
    exit 0
}

# 默认显示帮助
Write-Host "📖 使用方法:" -ForegroundColor Cyan
Write-Host "• 安装组件: -Install (需要管理员权限)" -ForegroundColor White
Write-Host "• 配置网络: -Configure" -ForegroundColor White
Write-Host "• 测试连接: -Test" -ForegroundColor White
Write-Host ""
Write-Host "🚀 推荐执行顺序:" -ForegroundColor Yellow
Write-Host "1. powershell -ExecutionPolicy Bypass -File scripts/hybrid_network_solution.ps1 -Install" -ForegroundColor White
Write-Host "2. powershell -ExecutionPolicy Bypass -File scripts/hybrid_network_solution.ps1 -Configure" -ForegroundColor White
Write-Host "3. powershell -ExecutionPolicy Bypass -File scripts/hybrid_network_solution.ps1 -Test" -ForegroundColor White
