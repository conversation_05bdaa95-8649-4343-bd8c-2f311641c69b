-- PostgreSQL 17 数据库初始化脚本
-- Axum企业级聊天室应用数据库设置

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";
CREATE EXTENSION IF NOT EXISTS "btree_gin";

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建应用用户 (如果不存在)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'axum_user') THEN
        CREATE ROLE axum_user WITH LOGIN PASSWORD 'axum_secure_password_2025';
    END IF;
END
$$;

-- 授予权限
GRANT CONNECT ON DATABASE axum_tutorial TO axum_user;
GRANT USAGE ON SCHEMA public TO axum_user;
GRANT CREATE ON SCHEMA public TO axum_user;

-- 创建性能监控视图
CREATE OR REPLACE VIEW pg_stat_activity_summary AS
SELECT 
    state,
    COUNT(*) as connection_count,
    AVG(EXTRACT(EPOCH FROM (now() - state_change))) as avg_duration_seconds
FROM pg_stat_activity 
WHERE state IS NOT NULL
GROUP BY state;

-- 创建慢查询监控视图
CREATE OR REPLACE VIEW slow_queries AS
SELECT 
    query,
    calls,
    total_exec_time,
    mean_exec_time,
    max_exec_time,
    rows
FROM pg_stat_statements 
WHERE mean_exec_time > 1000  -- 超过1秒的查询
ORDER BY mean_exec_time DESC;

-- 创建数据库大小监控视图
CREATE OR REPLACE VIEW database_size_info AS
SELECT 
    datname as database_name,
    pg_size_pretty(pg_database_size(datname)) as size,
    pg_database_size(datname) as size_bytes
FROM pg_database 
WHERE datname NOT IN ('template0', 'template1', 'postgres')
ORDER BY pg_database_size(datname) DESC;

-- 创建表空间使用情况视图
CREATE OR REPLACE VIEW table_size_info AS
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as total_size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size,
    pg_total_relation_size(schemaname||'.'||tablename) as total_size_bytes
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 创建索引使用情况视图
CREATE OR REPLACE VIEW index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes 
ORDER BY idx_scan DESC;

-- 授予视图查询权限
GRANT SELECT ON pg_stat_activity_summary TO axum_user;
GRANT SELECT ON slow_queries TO axum_user;
GRANT SELECT ON database_size_info TO axum_user;
GRANT SELECT ON table_size_info TO axum_user;
GRANT SELECT ON index_usage_stats TO axum_user;

-- 输出初始化完成信息
\echo '数据库初始化完成!'
\echo '已创建扩展: uuid-ossp, pg_stat_statements, pg_trgm, btree_gin'
\echo '已创建用户: axum_user'
\echo '已创建监控视图: pg_stat_activity_summary, slow_queries, database_size_info, table_size_info, index_usage_stats'
\echo '时区设置: Asia/Shanghai'
