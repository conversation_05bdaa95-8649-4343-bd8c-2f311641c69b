# Axum服务器内存泄漏检测脚本
# 监控服务器内存使用情况并检测潜在的内存泄漏

param(
    [string]$ServerUrl = "http://127.0.0.1:3000",
    [int]$TestDuration = 300,  # 5分钟
    [int]$RequestInterval = 1,  # 每秒发送请求
    [string]$ProcessName = "axum-tutorial"
)

Write-Host "🔍 Axum服务器内存泄漏检测开始..." -ForegroundColor Green
Write-Host "服务器地址: $ServerUrl" -ForegroundColor Cyan
Write-Host "测试持续时间: $TestDuration 秒" -ForegroundColor Cyan
Write-Host "请求间隔: $RequestInterval 秒" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Yellow

# 内存监控数据存储
$memoryData = @()
$startTime = Get-Date

# 检查服务器进程是否运行
function Get-ServerProcess {
    param([string]$ProcessName)
    
    $processes = Get-Process | Where-Object { $_.ProcessName -like "*$ProcessName*" -or $_.ProcessName -like "*cargo*" -or $_.ProcessName -like "*axum*" }
    
    if ($processes.Count -eq 0) {
        Write-Host "❌ 未找到服务器进程" -ForegroundColor Red
        Write-Host "请确保Axum服务器正在运行" -ForegroundColor Yellow
        return $null
    }
    
    # 选择内存使用最大的进程（通常是主进程）
    $mainProcess = $processes | Sort-Object WorkingSet64 -Descending | Select-Object -First 1
    Write-Host "✅ 找到服务器进程: $($mainProcess.ProcessName) (PID: $($mainProcess.Id))" -ForegroundColor Green
    return $mainProcess
}

# 获取进程内存使用情况
function Get-ProcessMemoryUsage {
    param([System.Diagnostics.Process]$Process)
    
    try {
        $Process.Refresh()
        return @{
            WorkingSet = [math]::Round($Process.WorkingSet64 / 1MB, 2)
            PrivateMemory = [math]::Round($Process.PrivateMemorySize64 / 1MB, 2)
            VirtualMemory = [math]::Round($Process.VirtualMemorySize64 / 1MB, 2)
            PagedMemory = [math]::Round($Process.PagedMemorySize64 / 1MB, 2)
            Timestamp = Get-Date
        }
    }
    catch {
        Write-Host "❌ 无法获取进程内存信息: $_" -ForegroundColor Red
        return $null
    }
}

# 发送HTTP请求
function Send-TestRequest {
    param([string]$Url)
    
    try {
        $response = Invoke-WebRequest -Uri "$Url/api/tasks" -Method GET -TimeoutSec 5
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# 分析内存使用趋势
function Analyze-MemoryTrend {
    param([array]$MemoryData)
    
    if ($MemoryData.Count -lt 10) {
        Write-Host "⚠️ 数据点不足，无法进行趋势分析" -ForegroundColor Yellow
        return
    }
    
    Write-Host "`n📊 内存使用趋势分析:" -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray
    
    $initialMemory = $MemoryData[0].WorkingSet
    $finalMemory = $MemoryData[-1].WorkingSet
    $maxMemory = ($MemoryData | Measure-Object -Property WorkingSet -Maximum).Maximum
    $minMemory = ($MemoryData | Measure-Object -Property WorkingSet -Minimum).Minimum
    $avgMemory = [math]::Round(($MemoryData | Measure-Object -Property WorkingSet -Average).Average, 2)
    
    Write-Host "初始内存使用: $initialMemory MB" -ForegroundColor White
    Write-Host "最终内存使用: $finalMemory MB" -ForegroundColor White
    Write-Host "最大内存使用: $maxMemory MB" -ForegroundColor White
    Write-Host "最小内存使用: $minMemory MB" -ForegroundColor White
    Write-Host "平均内存使用: $avgMemory MB" -ForegroundColor White
    
    $memoryIncrease = $finalMemory - $initialMemory
    $memoryIncreasePercent = if ($initialMemory -gt 0) { [math]::Round(($memoryIncrease / $initialMemory) * 100, 2) } else { 0 }
    
    Write-Host "`n内存变化: $memoryIncrease MB ($memoryIncreasePercent%)" -ForegroundColor $(if ($memoryIncrease -gt 50) { "Red" } elseif ($memoryIncrease -gt 20) { "Yellow" } else { "Green" })
    
    # 内存泄漏判断
    if ($memoryIncrease -gt 100) {
        Write-Host "🚨 警告: 可能存在严重内存泄漏！" -ForegroundColor Red
    }
    elseif ($memoryIncrease -gt 50) {
        Write-Host "⚠️ 注意: 内存使用增长较大，需要关注" -ForegroundColor Yellow
    }
    elseif ($memoryIncrease -gt 20) {
        Write-Host "ℹ️ 信息: 内存使用有所增长，属于正常范围" -ForegroundColor Cyan
    }
    else {
        Write-Host "✅ 良好: 内存使用稳定" -ForegroundColor Green
    }
}

# 生成内存使用报告
function New-MemoryReport {
    param([array]$MemoryData, [int]$TotalRequests, [int]$SuccessfulRequests)
    
    $reportPath = "memory_leak_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
    
    $initialMemory = if ($MemoryData.Count -gt 0) { $MemoryData[0].WorkingSet } else { 0 }
    $finalMemory = if ($MemoryData.Count -gt 0) { $MemoryData[-1].WorkingSet } else { 0 }
    $memoryIncrease = $finalMemory - $initialMemory
    
    $report = @"
# Axum服务器内存泄漏检测报告

## 测试配置
- 服务器地址: $ServerUrl
- 测试时间: $(Get-Date)
- 测试持续时间: $TestDuration 秒
- 请求间隔: $RequestInterval 秒
- 总请求数: $TotalRequests
- 成功请求数: $SuccessfulRequests

## 内存使用统计
- 初始内存使用: $initialMemory MB
- 最终内存使用: $finalMemory MB
- 内存变化: $memoryIncrease MB
- 数据采样点: $($MemoryData.Count)

## 详细内存数据
| 时间 | 工作集(MB) | 私有内存(MB) | 虚拟内存(MB) | 分页内存(MB) |
|------|------------|--------------|--------------|--------------|
"@
    
    foreach ($data in $MemoryData) {
        $timeStr = $data.Timestamp.ToString("HH:mm:ss")
        $report += "`n| $timeStr | $($data.WorkingSet) | $($data.PrivateMemory) | $($data.VirtualMemory) | $($data.PagedMemory) |"
    }
    
    $report += @"

## 结论
$(if ($memoryIncrease -gt 100) { "🚨 检测到可能的内存泄漏，建议进一步调查" } 
  elseif ($memoryIncrease -gt 50) { "⚠️ 内存使用增长较大，需要持续监控" }
  elseif ($memoryIncrease -gt 20) { "ℹ️ 内存使用轻微增长，属于正常范围" }
  else { "✅ 内存使用稳定，未发现明显泄漏" })

## 建议
1. 如果发现内存泄漏，使用Valgrind等工具进行详细分析
2. 检查代码中的资源管理，确保正确释放内存
3. 监控长时间运行的服务器实例
4. 考虑实施内存使用限制和监控告警

生成时间: $(Get-Date)
"@
    
    $report | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "`n📄 内存检测报告已生成: $reportPath" -ForegroundColor Green
}

# 主执行逻辑
$serverProcess = Get-ServerProcess -ProcessName $ProcessName
if ($null -eq $serverProcess) {
    exit 1
}

Write-Host "`n开始内存监控..." -ForegroundColor Green
$totalRequests = 0
$successfulRequests = 0
$testEndTime = $startTime.AddSeconds($TestDuration)

while ((Get-Date) -lt $testEndTime) {
    # 获取内存使用情况
    $memoryUsage = Get-ProcessMemoryUsage -Process $serverProcess
    if ($null -ne $memoryUsage) {
        $memoryData += $memoryUsage
        
        # 显示当前内存使用情况
        $elapsed = [math]::Round(((Get-Date) - $startTime).TotalSeconds, 0)
        Write-Host "[$elapsed s] 内存使用: $($memoryUsage.WorkingSet) MB" -ForegroundColor White
    }
    
    # 发送测试请求
    if (Send-TestRequest -Url $ServerUrl) {
        $successfulRequests++
    }
    $totalRequests++
    
    Start-Sleep -Seconds $RequestInterval
}

Write-Host "`n🔍 内存监控完成" -ForegroundColor Green
Write-Host "总请求数: $totalRequests" -ForegroundColor Cyan
Write-Host "成功请求数: $successfulRequests" -ForegroundColor Cyan
Write-Host "成功率: $([math]::Round(($successfulRequests / $totalRequests) * 100, 2))%" -ForegroundColor Cyan

# 分析结果
Analyze-MemoryTrend -MemoryData $memoryData

# 生成报告
New-MemoryReport -MemoryData $memoryData -TotalRequests $totalRequests -SuccessfulRequests $successfulRequests

Write-Host "`n🎉 内存泄漏检测完成！" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Yellow
