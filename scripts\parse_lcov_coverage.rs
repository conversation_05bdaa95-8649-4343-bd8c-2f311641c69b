//! # LCOV覆盖率解析器
//!
//! 用于解析LCOV文件并生成覆盖率统计报告

use std::fs;
use std::io::Write;

/// 覆盖率统计信息
#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct CoverageStats {
    /// 行覆盖率
    pub line_coverage: f64,
    /// 分支覆盖率
    pub branch_coverage: f64,
    /// 函数覆盖率
    pub function_coverage: f64,
    /// 覆盖的行数
    pub lines_covered: u32,
    /// 总行数
    pub lines_total: u32,
    /// 覆盖的分支数
    pub branches_covered: u32,
    /// 总分支数
    pub branches_total: u32,
    /// 覆盖的函数数
    pub functions_covered: u32,
    /// 总函数数
    pub functions_total: u32,
}

/// 解析LCOV文件获取覆盖率统计信息
fn parse_lcov_file(lcov_path: &str) -> Result<CoverageStats, Box<dyn std::error::Error>> {
    let content = fs::read_to_string(lcov_path)?;

    let mut total_lines_found = 0u32;
    let mut total_lines_hit = 0u32;
    let mut total_branches_found = 0u32;
    let mut total_branches_hit = 0u32;
    let mut total_functions_found = 0u32;
    let mut total_functions_hit = 0u32;

    for line in content.lines() {
        if line.starts_with("LF:") {
            // Lines Found (总行数)
            if let Ok(count) = line[3..].parse::<u32>() {
                total_lines_found += count;
            }
        } else if line.starts_with("LH:") {
            // Lines Hit (覆盖的行数)
            if let Ok(count) = line[3..].parse::<u32>() {
                total_lines_hit += count;
            }
        } else if line.starts_with("BRF:") {
            // Branches Found (总分支数)
            if let Ok(count) = line[4..].parse::<u32>() {
                total_branches_found += count;
            }
        } else if line.starts_with("BRH:") {
            // Branches Hit (覆盖的分支数)
            if let Ok(count) = line[4..].parse::<u32>() {
                total_branches_hit += count;
            }
        } else if line.starts_with("FNF:") {
            // Functions Found (总函数数)
            if let Ok(count) = line[4..].parse::<u32>() {
                total_functions_found += count;
            }
        } else if line.starts_with("FNH:") {
            // Functions Hit (覆盖的函数数)
            if let Ok(count) = line[4..].parse::<u32>() {
                total_functions_hit += count;
            }
        }
    }

    // 计算覆盖率百分比
    let line_coverage = if total_lines_found > 0 {
        (total_lines_hit as f64 / total_lines_found as f64) * 100.0
    } else {
        0.0
    };

    let branch_coverage = if total_branches_found > 0 {
        (total_branches_hit as f64 / total_branches_found as f64) * 100.0
    } else {
        0.0
    };

    let function_coverage = if total_functions_found > 0 {
        (total_functions_hit as f64 / total_functions_found as f64) * 100.0
    } else {
        0.0
    };

    Ok(CoverageStats {
        line_coverage,
        branch_coverage,
        function_coverage,
        lines_covered: total_lines_hit,
        lines_total: total_lines_found,
        branches_covered: total_branches_hit,
        branches_total: total_branches_found,
        functions_covered: total_functions_hit,
        functions_total: total_functions_found,
    })
}

/// 生成覆盖率总结报告
fn generate_summary_report(
    stats: &CoverageStats,
    output_path: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    let mut file = fs::File::create(output_path)?;

    writeln!(file, "# 代码覆盖率报告总结")?;
    writeln!(file)?;
    writeln!(file, "## 📊 覆盖率统计")?;
    writeln!(file)?;
    writeln!(file, "| 类型 | 覆盖率 | 覆盖数量 | 总数量 | 状态 |")?;
    writeln!(file, "|------|--------|----------|--------|------|")?;

    let line_status = if stats.line_coverage >= 90.0 {
        "✅"
    } else {
        "❌"
    };
    let branch_status = if stats.branch_coverage >= 85.0 {
        "✅"
    } else {
        "❌"
    };
    let function_status = if stats.function_coverage >= 90.0 {
        "✅"
    } else {
        "❌"
    };

    writeln!(
        file,
        "| 行覆盖率 | {:.2}% | {} | {} | {} |",
        stats.line_coverage, stats.lines_covered, stats.lines_total, line_status
    )?;
    writeln!(
        file,
        "| 分支覆盖率 | {:.2}% | {} | {} | {} |",
        stats.branch_coverage, stats.branches_covered, stats.branches_total, branch_status
    )?;
    writeln!(
        file,
        "| 函数覆盖率 | {:.2}% | {} | {} | {} |",
        stats.function_coverage, stats.functions_covered, stats.functions_total, function_status
    )?;

    writeln!(file)?;
    writeln!(file, "## 🎯 阈值配置")?;
    writeln!(file)?;
    writeln!(file, "- 行覆盖率阈值: 90.00%")?;
    writeln!(file, "- 分支覆盖率阈值: 85.00%")?;
    writeln!(file, "- 函数覆盖率阈值: 90.00%")?;

    writeln!(file)?;
    writeln!(file, "## 📁 报告文件")?;
    writeln!(file)?;
    writeln!(file, "- HTML报告: `target/cov/html/html/index.html`")?;
    writeln!(file, "- LCOV报告: `target/cov/lcov.info`")?;

    writeln!(file)?;
    writeln!(file, "## 📈 覆盖率评级")?;
    writeln!(file)?;

    let overall_score =
        (stats.line_coverage + stats.branch_coverage + stats.function_coverage) / 3.0;
    let grade = if overall_score >= 90.0 {
        "🟢 优秀"
    } else if overall_score >= 75.0 {
        "🟡 良好"
    } else {
        "🔴 需要改进"
    };

    writeln!(file, "- 总体覆盖率: {:.2}%", overall_score)?;
    writeln!(file, "- 评级: {}", grade)?;

    writeln!(file)?;
    writeln!(file, "## 🔧 改进建议")?;
    writeln!(file)?;

    if stats.line_coverage < 90.0 {
        writeln!(
            file,
            "- ❌ 行覆盖率 ({:.2}%) 低于目标阈值 (90.0%)，建议增加单元测试",
            stats.line_coverage
        )?;
    }

    if stats.branch_coverage < 85.0 {
        writeln!(
            file,
            "- ❌ 分支覆盖率 ({:.2}%) 低于目标阈值 (85.0%)，建议增加条件分支测试",
            stats.branch_coverage
        )?;
    }

    if stats.function_coverage < 90.0 {
        writeln!(
            file,
            "- ❌ 函数覆盖率 ({:.2}%) 低于目标阈值 (90.0%)，建议为未测试函数添加测试用例",
            stats.function_coverage
        )?;
    }

    if stats.line_coverage >= 90.0
        && stats.branch_coverage >= 85.0
        && stats.function_coverage >= 90.0
    {
        writeln!(file, "- ✅ 所有覆盖率指标均达到目标阈值，代码质量良好！")?;
    }

    Ok(())
}

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔍 开始解析LCOV覆盖率数据...");

    let lcov_path = "target/cov/lcov.info";
    let output_path = "target/cov/coverage_summary.md";

    // 解析LCOV文件
    let stats = parse_lcov_file(lcov_path)?;

    // 显示统计信息
    println!("📊 LCOV解析结果:");
    println!(
        "   行覆盖率: {}/{} ({:.2}%)",
        stats.lines_covered, stats.lines_total, stats.line_coverage
    );
    println!(
        "   分支覆盖率: {}/{} ({:.2}%)",
        stats.branches_covered, stats.branches_total, stats.branch_coverage
    );
    println!(
        "   函数覆盖率: {}/{} ({:.2}%)",
        stats.functions_covered, stats.functions_total, stats.function_coverage
    );

    // 生成总结报告
    generate_summary_report(&stats, output_path)?;

    println!("✅ 覆盖率报告生成完成！");
    println!("📊 HTML报告: target/cov/html/html/index.html");
    println!("📄 总结报告: {}", output_path);
    println!("📋 LCOV报告: {}", lcov_path);

    // 显示总体评级
    let overall_score =
        (stats.line_coverage + stats.branch_coverage + stats.function_coverage) / 3.0;
    let grade = if overall_score >= 90.0 {
        "🟢 优秀"
    } else if overall_score >= 75.0 {
        "🟡 良好"
    } else {
        "🔴 需要改进"
    };

    println!("\n🎉 覆盖率分析完成！");
    println!("📊 行覆盖率: {:.2}%", stats.line_coverage);
    println!("🌿 分支覆盖率: {:.2}%", stats.branch_coverage);
    println!("🔧 函数覆盖率: {:.2}%", stats.function_coverage);
    println!("🏆 总体评级: {} ({:.2}%)", grade, overall_score);

    Ok(())
}
