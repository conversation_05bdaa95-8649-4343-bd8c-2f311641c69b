#!/bin/bash
# PostgreSQL性能基准测试脚本
# 用于验证企业级性能优化效果

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"
POSTGRES_USER="axum_user"
POSTGRES_DB="axum_tutorial"
POSTGRES_PASSWORD="axum_secure_password_2025"
SCALE_FACTOR=10
DURATION=60
CLIENTS=10

echo -e "${BLUE}🚀 PostgreSQL性能基准测试开始...${NC}"
echo -e "${YELLOW}📋 测试配置:${NC}"
echo -e "   主机: $POSTGRES_HOST:$POSTGRES_PORT"
echo -e "   数据库: $POSTGRES_DB"
echo -e "   用户: $POSTGRES_USER"
echo -e "   规模因子: $SCALE_FACTOR"
echo -e "   测试时长: ${DURATION}秒"
echo -e "   并发客户端: $CLIENTS"
echo ""

# 设置密码环境变量
export PGPASSWORD=$POSTGRES_PASSWORD

# 检查pgbench是否可用
if ! command -v pgbench &> /dev/null; then
    echo -e "${RED}❌ pgbench未安装，请先安装postgresql-contrib包${NC}"
    exit 1
fi

# 检查数据库连接
echo -e "${YELLOW}🔍 检查数据库连接...${NC}"
if ! psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${RED}❌ 无法连接到数据库${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 数据库连接成功${NC}"
echo ""

# 初始化pgbench数据
echo -e "${YELLOW}📊 初始化pgbench测试数据 (规模因子: $SCALE_FACTOR)...${NC}"
pgbench -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -i -s $SCALE_FACTOR --quiet

echo -e "${GREEN}✅ 测试数据初始化完成${NC}"
echo ""

# 获取初始统计信息
echo -e "${YELLOW}📈 获取初始性能统计...${NC}"
INITIAL_STATS=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "
SELECT 
    'Connections: ' || numbackends || ', ' ||
    'Commits: ' || xact_commit || ', ' ||
    'Rollbacks: ' || xact_rollback || ', ' ||
    'Blocks Read: ' || blks_read || ', ' ||
    'Blocks Hit: ' || blks_hit
FROM pg_stat_database 
WHERE datname = '$POSTGRES_DB';
" | xargs)

echo -e "${BLUE}📊 初始统计: $INITIAL_STATS${NC}"
echo ""

# 运行基准测试
echo -e "${YELLOW}🏃 运行性能基准测试...${NC}"
echo -e "${BLUE}测试类型: TPC-B (类似银行交易)${NC}"
echo -e "${BLUE}持续时间: ${DURATION}秒，并发客户端: $CLIENTS${NC}"
echo ""

BENCHMARK_RESULT=$(pgbench -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -c $CLIENTS -j 2 -T $DURATION --progress=10 2>&1)

echo "$BENCHMARK_RESULT"
echo ""

# 提取关键性能指标
TPS=$(echo "$BENCHMARK_RESULT" | grep "tps =" | tail -1 | awk '{print $3}')
LATENCY_AVG=$(echo "$BENCHMARK_RESULT" | grep "latency average" | awk '{print $4}')
LATENCY_STDDEV=$(echo "$BENCHMARK_RESULT" | grep "latency stddev" | awk '{print $4}')

echo -e "${GREEN}🎯 关键性能指标:${NC}"
echo -e "${BLUE}   TPS (每秒事务数): $TPS${NC}"
echo -e "${BLUE}   平均延迟: $LATENCY_AVG ms${NC}"
echo -e "${BLUE}   延迟标准差: $LATENCY_STDDEV ms${NC}"
echo ""

# 获取最终统计信息
echo -e "${YELLOW}📈 获取最终性能统计...${NC}"
FINAL_STATS=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "
SELECT 
    'Connections: ' || numbackends || ', ' ||
    'Commits: ' || xact_commit || ', ' ||
    'Rollbacks: ' || xact_rollback || ', ' ||
    'Blocks Read: ' || blks_read || ', ' ||
    'Blocks Hit: ' || blks_hit
FROM pg_stat_database 
WHERE datname = '$POSTGRES_DB';
" | xargs)

echo -e "${BLUE}📊 最终统计: $FINAL_STATS${NC}"
echo ""

# 缓存命中率分析
echo -e "${YELLOW}📊 缓存命中率分析...${NC}"
CACHE_HIT_RATIO=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "
SELECT 
    ROUND(
        100.0 * blks_hit / NULLIF(blks_hit + blks_read, 0), 2
    ) as cache_hit_ratio
FROM pg_stat_database 
WHERE datname = '$POSTGRES_DB';
" | xargs)

echo -e "${BLUE}🎯 缓存命中率: ${CACHE_HIT_RATIO}%${NC}"

if (( $(echo "$CACHE_HIT_RATIO > 95" | bc -l) )); then
    echo -e "${GREEN}✅ 缓存命中率优秀 (>95%)${NC}"
elif (( $(echo "$CACHE_HIT_RATIO > 90" | bc -l) )); then
    echo -e "${YELLOW}⚠️  缓存命中率良好 (90-95%)${NC}"
else
    echo -e "${RED}❌ 缓存命中率需要优化 (<90%)${NC}"
fi
echo ""

# 连接统计
echo -e "${YELLOW}📊 连接统计...${NC}"
CONNECTION_STATS=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "
SELECT 
    'Active: ' || COUNT(*) FILTER (WHERE state = 'active') || ', ' ||
    'Idle: ' || COUNT(*) FILTER (WHERE state = 'idle') || ', ' ||
    'Total: ' || COUNT(*)
FROM pg_stat_activity;
" | xargs)

echo -e "${BLUE}🔗 连接状态: $CONNECTION_STATS${NC}"
echo ""

# 性能评估
echo -e "${YELLOW}📋 性能评估...${NC}"
if (( $(echo "$TPS > 1000" | bc -l) )); then
    echo -e "${GREEN}✅ TPS优秀 (>1000)${NC}"
elif (( $(echo "$TPS > 500" | bc -l) )); then
    echo -e "${YELLOW}⚠️  TPS良好 (500-1000)${NC}"
else
    echo -e "${RED}❌ TPS需要优化 (<500)${NC}"
fi

if (( $(echo "$LATENCY_AVG < 10" | bc -l) )); then
    echo -e "${GREEN}✅ 延迟优秀 (<10ms)${NC}"
elif (( $(echo "$LATENCY_AVG < 50" | bc -l) )); then
    echo -e "${YELLOW}⚠️  延迟良好 (10-50ms)${NC}"
else
    echo -e "${RED}❌ 延迟需要优化 (>50ms)${NC}"
fi
echo ""

# 清理测试数据
echo -e "${YELLOW}🧹 清理测试数据...${NC}"
psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -c "
DROP TABLE IF EXISTS pgbench_accounts, pgbench_branches, pgbench_history, pgbench_tellers;
" > /dev/null 2>&1

echo -e "${GREEN}✅ 测试数据清理完成${NC}"
echo ""

echo -e "${BLUE}🎉 PostgreSQL性能基准测试完成！${NC}"
echo -e "${GREEN}📊 测试报告已生成${NC}"
echo ""
echo -e "${YELLOW}💡 性能优化建议:${NC}"
echo -e "   1. 如果TPS较低，考虑增加shared_buffers"
echo -e "   2. 如果延迟较高，检查work_mem设置"
echo -e "   3. 如果缓存命中率低，增加effective_cache_size"
echo -e "   4. 监控慢查询并优化索引"
echo -e "   5. 考虑使用连接池减少连接开销"
