# DragonflyDB 快速验证脚本
# 用于快速检查DragonflyDB配置和基本功能

param(
    [string]$Host = "127.0.0.1",
    [int]$Port = 6379,
    [switch]$ShowConfig = $false
)

Write-Host "=== DragonflyDB 快速验证 ===" -ForegroundColor Green
Write-Host "目标: $Host`:$Port" -ForegroundColor Yellow

# 测试用户配置
$testUsers = @(
    @{ Name = "monitor"; Password = "monitor_password_2025"; Description = "监控用户" },
    @{ Name = "axum_app"; Password = "dragonfly_secure_password_2025"; Description = "应用用户" },
    @{ Name = "readonly"; Password = "readonly_password_2025"; Description = "只读用户" }
)

$allTestsPassed = $true

foreach ($user in $testUsers) {
    Write-Host "`n--- 测试 $($user.Description) ---" -ForegroundColor Cyan
    
    try {
        # 基础连接测试
        $pingResult = & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password ping 2>&1
        
        if ($pingResult -eq "PONG") {
            Write-Host "✓ $($user.Description) 连接成功" -ForegroundColor Green
            
            # 根据用户类型执行不同的测试
            switch ($user.Name) {
                "monitor" {
                    # 监控用户测试
                    $info = & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password info server 2>&1
                    if ($info -match "redis_version") {
                        Write-Host "✓ 监控用户可以获取服务器信息" -ForegroundColor Green
                    } else {
                        Write-Host "✗ 监控用户无法获取服务器信息" -ForegroundColor Red
                        $allTestsPassed = $false
                    }
                }
                
                "axum_app" {
                    # 应用用户测试
                    $testKey = "test:quick:$(Get-Date -Format 'HHmmss')"
                    & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password set $testKey "test_value" | Out-Null
                    $getValue = & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password get $testKey 2>&1
                    
                    if ($getValue -eq "test_value") {
                        Write-Host "✓ 应用用户可以执行读写操作" -ForegroundColor Green
                        & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password del $testKey | Out-Null
                    } else {
                        Write-Host "✗ 应用用户无法执行读写操作" -ForegroundColor Red
                        $allTestsPassed = $false
                    }
                }
                
                "readonly" {
                    # 只读用户测试
                    $info = & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password info memory 2>&1
                    if ($info -match "used_memory") {
                        Write-Host "✓ 只读用户可以获取信息" -ForegroundColor Green
                    } else {
                        Write-Host "✗ 只读用户无法获取信息" -ForegroundColor Red
                        $allTestsPassed = $false
                    }
                    
                    # 测试写操作应该被拒绝
                    $setResult = & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password set test:readonly:forbidden value 2>&1
                    if ($setResult -match "NOPERM|Permission denied") {
                        Write-Host "✓ 只读用户写操作被正确拒绝" -ForegroundColor Green
                    } else {
                        Write-Host "✗ 安全风险: 只读用户可以执行写操作" -ForegroundColor Red
                        $allTestsPassed = $false
                    }
                }
            }
        } else {
            Write-Host "✗ $($user.Description) 连接失败: $pingResult" -ForegroundColor Red
            $allTestsPassed = $false
        }
    } catch {
        Write-Host "✗ $($user.Description) 测试异常: $($_.Exception.Message)" -ForegroundColor Red
        $allTestsPassed = $false
    }
}

# 显示关键配置信息
if ($ShowConfig) {
    Write-Host "`n=== 关键配置信息 ===" -ForegroundColor Cyan
    
    try {
        $adminUser = "admin"
        $adminPassword = "admin_secure_password_2025"
        
        # 尝试使用管理员用户获取配置
        $configs = @("maxmemory", "maxclients", "tcp-keepalive", "databases")
        
        foreach ($config in $configs) {
            $value = & redis-cli -h $Host -p $Port --user $adminUser -a $adminPassword config get $config 2>&1
            if ($value -is [array] -and $value.Length -ge 2) {
                Write-Host "$($value[0]): $($value[1])" -ForegroundColor Yellow
            }
        }
        
        # 显示内存使用情况
        $memInfo = & redis-cli -h $Host -p $Port --user monitor -a monitor_password_2025 info memory 2>&1
        $memInfo | Select-String "used_memory_human|maxmemory_human|mem_fragmentation_ratio" | ForEach-Object {
            Write-Host $_.Line -ForegroundColor Yellow
        }
        
        # 显示客户端连接数
        $clientInfo = & redis-cli -h $Host -p $Port --user monitor -a monitor_password_2025 info clients 2>&1
        $clientInfo | Select-String "connected_clients" | ForEach-Object {
            Write-Host $_.Line -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "无法获取配置信息: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 测试数据结构性能
Write-Host "`n=== 数据结构性能测试 ===" -ForegroundColor Cyan
$appUser = "axum_app"
$appPassword = "dragonfly_secure_password_2025"

try {
    # 测试String性能
    $start = Get-Date
    for ($i = 1; $i -le 100; $i++) {
        & redis-cli -h $Host -p $Port --user $appUser -a $appPassword set "perf:string:$i" "value$i" | Out-Null
    }
    $stringTime = (Get-Date) - $start
    Write-Host "✓ String操作 (100次): $($stringTime.TotalMilliseconds) ms" -ForegroundColor Green
    
    # 测试Hash性能
    $start = Get-Date
    for ($i = 1; $i -le 100; $i++) {
        & redis-cli -h $Host -p $Port --user $appUser -a $appPassword hset "perf:hash:$i" field1 value1 field2 value2 | Out-Null
    }
    $hashTime = (Get-Date) - $start
    Write-Host "✓ Hash操作 (100次): $($hashTime.TotalMilliseconds) ms" -ForegroundColor Green
    
    # 测试List性能
    $start = Get-Date
    for ($i = 1; $i -le 100; $i++) {
        & redis-cli -h $Host -p $Port --user $appUser -a $appPassword lpush "perf:list:$i" item1 item2 item3 | Out-Null
    }
    $listTime = (Get-Date) - $start
    Write-Host "✓ List操作 (100次): $($listTime.TotalMilliseconds) ms" -ForegroundColor Green
    
    # 清理性能测试数据
    & redis-cli -h $Host -p $Port --user $appUser -a $appPassword eval "
        local keys = redis.call('keys', 'perf:*')
        if #keys > 0 then
            return redis.call('del', unpack(keys))
        end
        return 0
    " 0 | Out-Null
    
} catch {
    Write-Host "性能测试失败: $($_.Exception.Message)" -ForegroundColor Red
    $allTestsPassed = $false
}

# 测试监控端点
Write-Host "`n=== 监控端点测试 ===" -ForegroundColor Cyan

# 测试Redis Exporter
try {
    $response = Invoke-WebRequest -Uri "http://$Host`:9121/metrics" -TimeoutSec 5 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        $metricsCount = ($response.Content -split "`n" | Where-Object { $_ -match "^redis_" }).Count
        Write-Host "✓ Redis Exporter正常 ($metricsCount 个指标)" -ForegroundColor Green
    } else {
        Write-Host "✗ Redis Exporter异常: HTTP $($response.StatusCode)" -ForegroundColor Red
        $allTestsPassed = $false
    }
} catch {
    Write-Host "✗ Redis Exporter不可访问: $($_.Exception.Message)" -ForegroundColor Red
    $allTestsPassed = $false
}

# 最终结果
Write-Host "`n=== 验证结果 ===" -ForegroundColor Cyan
if ($allTestsPassed) {
    Write-Host "✓ 所有测试通过 - DragonflyDB配置正常" -ForegroundColor Green
    Write-Host "DragonflyDB已准备好用于生产环境" -ForegroundColor Green
    exit 0
} else {
    Write-Host "✗ 部分测试失败 - 请检查配置" -ForegroundColor Red
    Write-Host "建议运行详细的健康检查脚本进行诊断" -ForegroundColor Yellow
    exit 1
}

Write-Host "`n提示: 使用 -ShowConfig 参数查看详细配置信息" -ForegroundColor Cyan
