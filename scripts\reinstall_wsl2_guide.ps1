# WSL2重新安装指南
# 当网络问题无法通过其他方式解决时的终极方案

param(
    [switch]$ShowSteps,
    [switch]$Backup,
    [switch]$Execute
)

Write-Host "🔧 WSL2重新安装指南" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

if ($ShowSteps) {
    Write-Host "📋 WSL2重新安装步骤:" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "阶段1: 备份数据" -ForegroundColor Yellow
    Write-Host "1. 备份WSL2发行版: wsl --export Ubuntu backup_ubuntu.tar" -ForegroundColor White
    Write-Host "2. 备份项目数据: 复制整个项目目录" -ForegroundColor White
    Write-Host "3. 备份Podman数据: podman volume ls && podman volume export" -ForegroundColor White
    Write-Host ""
    
    Write-Host "阶段2: 卸载WSL2" -ForegroundColor Yellow
    Write-Host "1. 停止所有WSL实例: wsl --shutdown" -ForegroundColor White
    Write-Host "2. 注销发行版: wsl --unregister Ubuntu" -ForegroundColor White
    Write-Host "3. 禁用WSL功能: dism.exe /online /disable-feature /featurename:Microsoft-Windows-Subsystem-Linux" -ForegroundColor White
    Write-Host "4. 禁用虚拟机平台: dism.exe /online /disable-feature /featurename:VirtualMachinePlatform" -ForegroundColor White
    Write-Host "5. 重启计算机" -ForegroundColor White
    Write-Host ""
    
    Write-Host "阶段3: 重新安装WSL2" -ForegroundColor Yellow
    Write-Host "1. 启用WSL功能: dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux" -ForegroundColor White
    Write-Host "2. 启用虚拟机平台: dism.exe /online /enable-feature /featurename:VirtualMachinePlatform" -ForegroundColor White
    Write-Host "3. 重启计算机" -ForegroundColor White
    Write-Host "4. 下载WSL2内核更新: https://aka.ms/wsl2kernel" -ForegroundColor White
    Write-Host "5. 设置WSL2为默认: wsl --set-default-version 2" -ForegroundColor White
    Write-Host "6. 安装Ubuntu: wsl --install -d Ubuntu" -ForegroundColor White
    Write-Host ""
    
    Write-Host "阶段4: 恢复环境" -ForegroundColor Yellow
    Write-Host "1. 恢复发行版: wsl --import Ubuntu C:\WSL\Ubuntu backup_ubuntu.tar" -ForegroundColor White
    Write-Host "2. 安装Podman: 运行安装脚本" -ForegroundColor White
    Write-Host "3. 恢复项目: 复制项目文件" -ForegroundColor White
    Write-Host "4. 重新配置网络: 运行网络优化脚本" -ForegroundColor White
    Write-Host ""
    
    Write-Host "⚠️ 注意事项:" -ForegroundColor Red
    Write-Host "• 这是破坏性操作，会删除所有WSL2数据" -ForegroundColor White
    Write-Host "• 需要管理员权限" -ForegroundColor White
    Write-Host "• 需要重启计算机多次" -ForegroundColor White
    Write-Host "• 建议在非工作时间进行" -ForegroundColor White
    
    exit 0
}

if ($Backup) {
    Write-Host "📁 开始备份WSL2数据..." -ForegroundColor Cyan
    
    # 创建备份目录
    $backupDir = "C:\WSL_Backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    New-Item -ItemType Directory -Path $backupDir -Force
    Write-Host "📂 备份目录: $backupDir" -ForegroundColor Green
    
    # 备份WSL发行版
    Write-Host "💾 备份Ubuntu发行版..." -ForegroundColor Yellow
    wsl --export Ubuntu "$backupDir\ubuntu_backup.tar"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Ubuntu发行版备份成功" -ForegroundColor Green
    } else {
        Write-Host "❌ Ubuntu发行版备份失败" -ForegroundColor Red
    }
    
    # 备份WSL配置
    if (Test-Path "$env:USERPROFILE\.wslconfig") {
        Copy-Item "$env:USERPROFILE\.wslconfig" "$backupDir\.wslconfig" -Force
        Write-Host "✅ WSL配置文件备份成功" -ForegroundColor Green
    }
    
    Write-Host "🎉 备份完成!" -ForegroundColor Green
    Write-Host "📂 备份位置: $backupDir" -ForegroundColor Cyan
    
    exit 0
}

if ($Execute) {
    Write-Host "⚠️ 这将执行WSL2重新安装过程" -ForegroundColor Red
    Write-Host "❗ 这是破坏性操作，将删除所有WSL2数据" -ForegroundColor Red
    
    $confirm = Read-Host "确认继续? 请输入 'CONFIRM' 以继续"
    if ($confirm -ne "CONFIRM") {
        Write-Host "❌ 操作已取消" -ForegroundColor Red
        exit 0
    }
    
    # 检查管理员权限
    $isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
    if (-not $isAdmin) {
        Write-Host "❌ 需要管理员权限执行此操作" -ForegroundColor Red
        Write-Host "💡 请以管理员身份重新运行此脚本" -ForegroundColor Yellow
        exit 1
    }
    
    Write-Host "🛑 停止WSL2..." -ForegroundColor Yellow
    wsl --shutdown
    
    Write-Host "🗑️ 注销Ubuntu发行版..." -ForegroundColor Yellow
    wsl --unregister Ubuntu
    
    Write-Host "🔧 禁用WSL功能..." -ForegroundColor Yellow
    dism.exe /online /disable-feature /featurename:Microsoft-Windows-Subsystem-Linux /norestart
    dism.exe /online /disable-feature /featurename:VirtualMachinePlatform /norestart
    
    Write-Host "🔄 需要重启计算机以完成卸载" -ForegroundColor Red
    Write-Host "💡 重启后请手动执行重新安装步骤" -ForegroundColor Yellow
    
    $restart = Read-Host "现在重启计算机? (y/N)"
    if ($restart -eq "y" -or $restart -eq "Y") {
        Restart-Computer -Force
    }
    
    exit 0
}

# 默认显示帮助
Write-Host "📖 使用方法:" -ForegroundColor Cyan
Write-Host "• 查看步骤: -ShowSteps" -ForegroundColor White
Write-Host "• 备份数据: -Backup" -ForegroundColor White
Write-Host "• 执行重装: -Execute" -ForegroundColor White
Write-Host ""
Write-Host "💡 建议先运行其他解决方案，重装是最后手段" -ForegroundColor Yellow
