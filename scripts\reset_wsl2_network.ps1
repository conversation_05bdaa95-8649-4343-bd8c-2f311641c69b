# WSL2网络完全重置和优化脚本
# 解决WSL2网络连接问题的终极方案

param(
    [switch]$FullReset,
    [switch]$Force
)

Write-Host "🔧 WSL2网络重置和优化工具" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Cyan

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if ($FullReset) {
    Write-Host "⚠️ 完全重置模式 - 这将重置所有WSL2网络配置" -ForegroundColor Yellow
    
    if (-not $Force) {
        $confirm = Read-Host "确认继续? (y/N)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Host "❌ 操作已取消" -ForegroundColor Red
            exit 0
        }
    }
    
    Write-Host "🛑 关闭WSL2..." -ForegroundColor Yellow
    wsl --shutdown
    Start-Sleep 5
    
    Write-Host "🧹 清理网络配置..." -ForegroundColor Yellow
    
    # 清理端口转发规则
    if ($isAdmin) {
        try {
            netsh interface portproxy reset
            Write-Host "✅ 端口转发规则已清理" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ 清理端口转发规则失败: $_" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️ 需要管理员权限清理端口转发规则" -ForegroundColor Yellow
    }
    
    # 重置网络适配器
    try {
        Get-NetAdapter -Name "*WSL*" | Restart-NetAdapter -ErrorAction SilentlyContinue
        Write-Host "✅ WSL网络适配器已重置" -ForegroundColor Green
    } catch {
        Write-Host "⚠️ 重置网络适配器失败: $_" -ForegroundColor Yellow
    }
}

# 创建优化的WSL2配置
Write-Host "⚙️ 创建优化的WSL2配置..." -ForegroundColor Cyan

$wslConfigPath = "$env:USERPROFILE\.wslconfig"
$backupPath = "$env:USERPROFILE\.wslconfig.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"

# 备份现有配置
if (Test-Path $wslConfigPath) {
    Copy-Item $wslConfigPath $backupPath -Force
    Write-Host "📁 已备份现有配置: $backupPath" -ForegroundColor Yellow
}

# Windows 10优化配置
$wslConfig = @"
[wsl2]
# 网络配置优化
localhostForwarding=true

# 性能优化
memory=4GB
processors=2
swap=2GB

# 网络优化
kernelCommandLine=cgroup_no_v1=all systemd.unified_cgroup_hierarchy=1

# 实验性功能
[experimental]
sparseVhd=true
autoMemoryReclaim=gradual
networkingMode=NAT
dnsTunneling=false
firewall=false
autoProxy=false
"@

Set-Content -Path $wslConfigPath -Value $wslConfig -Encoding UTF8
Write-Host "✅ WSL2配置已更新" -ForegroundColor Green

# 重启WSL2
Write-Host "🔄 重启WSL2..." -ForegroundColor Cyan
wsl --shutdown
Start-Sleep 10

Write-Host "🚀 启动WSL2..." -ForegroundColor Cyan
wsl echo "WSL2 restarted successfully"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ WSL2重启成功" -ForegroundColor Green
} else {
    Write-Host "❌ WSL2重启失败" -ForegroundColor Red
    exit 1
}

# 获取新的WSL2 IP
Write-Host "🔍 获取WSL2 IP地址..." -ForegroundColor Cyan
$wsl2IP = (wsl hostname -I).Trim()
Write-Host "📡 WSL2 IP: $wsl2IP" -ForegroundColor Green

# 配置端口转发（如果有管理员权限）
if ($isAdmin) {
    Write-Host "🔗 配置端口转发..." -ForegroundColor Cyan
    
    try {
        # 删除现有规则
        netsh interface portproxy delete v4tov4 listenport=6379 listenaddress=127.0.0.1 2>$null
        
        # 添加新规则
        netsh interface portproxy add v4tov4 listenport=6379 listenaddress=127.0.0.1 connectport=6379 connectaddress=$wsl2IP
        
        Write-Host "✅ 端口转发配置成功: 127.0.0.1:6379 -> $wsl2IP:6379" -ForegroundColor Green
        
        # 验证规则
        $rules = netsh interface portproxy show all
        if ($rules -match "6379") {
            Write-Host "✅ 端口转发规则验证成功" -ForegroundColor Green
        }
    } catch {
        Write-Host "❌ 端口转发配置失败: $_" -ForegroundColor Red
    }
} else {
    Write-Host "⚠️ 需要管理员权限配置端口转发" -ForegroundColor Yellow
    Write-Host "💡 请以管理员身份运行以下命令:" -ForegroundColor Cyan
    Write-Host "netsh interface portproxy add v4tov4 listenport=6379 listenaddress=127.0.0.1 connectport=6379 connectaddress=$wsl2IP" -ForegroundColor Gray
}

Write-Host "`n🎉 WSL2网络重置完成!" -ForegroundColor Green
Write-Host "`n📋 配置摘要:" -ForegroundColor Cyan
Write-Host "• WSL2 IP: $wsl2IP" -ForegroundColor White
Write-Host "• 配置文件: $wslConfigPath" -ForegroundColor White
Write-Host "• 端口转发: $(if ($isAdmin) { '已配置' } else { '需要管理员权限' })" -ForegroundColor White

Write-Host "`n🔄 下一步操作:" -ForegroundColor Yellow
Write-Host "1. 运行Podman网络优化脚本" -ForegroundColor White
Write-Host "2. 更新.env文件使用localhost" -ForegroundColor White
Write-Host "3. 重启容器服务" -ForegroundColor White
Write-Host "4. 测试连接" -ForegroundColor White
