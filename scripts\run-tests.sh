#!/bin/bash

# Axum Tutorial 测试运行脚本
# 提供全面的测试执行和覆盖率报告功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 运行单元测试
run_unit_tests() {
    log_info "运行单元测试..."
    
    # 运行所有workspace的单元测试
    cargo test --workspace --lib
    
    log_success "单元测试完成"
}

# 运行集成测试
run_integration_tests() {
    log_info "运行集成测试..."
    
    # 运行集成测试
    cargo test --workspace --test '*'
    
    log_success "集成测试完成"
}

# 运行特定包的测试
run_package_tests() {
    local package=$1
    
    log_info "运行 $package 包的测试..."
    
    cargo test -p "$package"
    
    log_success "$package 包测试完成"
}

# 运行所有测试
run_all_tests() {
    log_info "运行所有测试..."
    
    cargo test --workspace
    
    log_success "所有测试完成"
}

# 生成测试覆盖率报告
generate_coverage() {
    log_info "生成测试覆盖率报告..."
    
    # 检查是否安装了 cargo-tarpaulin
    if ! command -v cargo-tarpaulin &> /dev/null; then
        log_warning "cargo-tarpaulin 未安装，正在安装..."
        cargo install cargo-tarpaulin
    fi
    
    # 生成HTML覆盖率报告
    cargo tarpaulin --workspace --out Html --output-dir coverage
    
    log_success "覆盖率报告已生成到 coverage/ 目录"
    
    # 如果是在支持的环境中，尝试打开报告
    if command -v xdg-open &> /dev/null; then
        xdg-open coverage/tarpaulin-report.html
    elif command -v open &> /dev/null; then
        open coverage/tarpaulin-report.html
    else
        log_info "请手动打开 coverage/tarpaulin-report.html 查看覆盖率报告"
    fi
}

# 运行性能基准测试
run_benchmarks() {
    log_info "运行性能基准测试..."
    
    cargo bench
    
    log_success "性能基准测试完成"
}

# 运行文档测试
run_doc_tests() {
    log_info "运行文档测试..."
    
    cargo test --workspace --doc
    
    log_success "文档测试完成"
}

# 运行安全审计
run_security_audit() {
    log_info "运行安全审计..."
    
    # 检查是否安装了 cargo-audit
    if ! command -v cargo-audit &> /dev/null; then
        log_warning "cargo-audit 未安装，正在安装..."
        cargo install cargo-audit
    fi
    
    cargo audit
    
    log_success "安全审计完成"
}

# 运行依赖检查
check_dependencies() {
    log_info "检查依赖更新..."
    
    # 检查是否安装了 cargo-outdated
    if ! command -v cargo-outdated &> /dev/null; then
        log_warning "cargo-outdated 未安装，正在安装..."
        cargo install cargo-outdated
    fi
    
    cargo outdated
    
    log_success "依赖检查完成"
}

# 运行完整的CI流水线
run_ci_pipeline() {
    log_info "运行完整的CI流水线..."
    
    # 1. 代码格式检查
    log_info "1. 检查代码格式..."
    cargo fmt --all -- --check
    
    # 2. Clippy检查
    log_info "2. 运行Clippy检查..."
    cargo clippy --all-targets --workspace -- -D warnings
    
    # 3. 运行所有测试
    log_info "3. 运行所有测试..."
    run_all_tests
    
    # 4. 生成覆盖率报告
    log_info "4. 生成覆盖率报告..."
    generate_coverage
    
    # 5. 安全审计
    log_info "5. 运行安全审计..."
    run_security_audit
    
    # 6. 构建检查
    log_info "6. 检查构建..."
    cargo build --workspace --release
    
    log_success "CI流水线执行完成！"
}

# 显示帮助信息
show_help() {
    echo "Axum Tutorial 测试运行脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  all         运行所有测试"
    echo "  unit        运行单元测试"
    echo "  integration 运行集成测试"
    echo "  doc         运行文档测试"
    echo "  package     运行特定包的测试 (需要指定包名)"
    echo "  coverage    生成测试覆盖率报告"
    echo "  bench       运行性能基准测试"
    echo "  audit       运行安全审计"
    echo "  deps        检查依赖更新"
    echo "  ci          运行完整的CI流水线"
    echo "  help        显示此帮助信息"
    echo ""
    echo "包名选项 (用于 package 命令):"
    echo "  server              主服务器包"
    echo "  app_application     应用层包"
    echo "  app_domain          领域层包"
    echo "  app_infrastructure  基础设施层包"
    echo "  app_common          公共模块包"
    echo "  migration           数据库迁移包"
    echo ""
    echo "示例:"
    echo "  $0 all                    # 运行所有测试"
    echo "  $0 unit                   # 运行单元测试"
    echo "  $0 package server         # 运行server包的测试"
    echo "  $0 coverage               # 生成覆盖率报告"
    echo "  $0 ci                     # 运行完整CI流水线"
}

# 主函数
main() {
    case "${1:-help}" in
        "all")
            run_all_tests
            ;;
        "unit")
            run_unit_tests
            ;;
        "integration")
            run_integration_tests
            ;;
        "doc")
            run_doc_tests
            ;;
        "package")
            if [ -z "$2" ]; then
                log_error "请指定包名"
                echo ""
                show_help
                exit 1
            fi
            run_package_tests "$2"
            ;;
        "coverage")
            generate_coverage
            ;;
        "bench")
            run_benchmarks
            ;;
        "audit")
            run_security_audit
            ;;
        "deps")
            check_dependencies
            ;;
        "ci")
            run_ci_pipeline
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
