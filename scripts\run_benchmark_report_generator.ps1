# Axum项目基准测试报告生成脚本
# 用于生成综合的基准测试报告（HTML、Markdown、JSON格式）

param(
    [switch]$OpenReport = $false,  # 是否自动打开HTML报告
    [switch]$Force = $false,       # 是否强制重新生成报告
    [string]$OutputDir = "reports/benchmark"  # 输出目录
)

Write-Host "=== Axum项目基准测试报告生成器 ===" -ForegroundColor Green
Write-Host "输出目录: $OutputDir" -ForegroundColor Yellow
Write-Host "项目路径: $(Get-Location)" -ForegroundColor Yellow

# 检查Cargo项目
function Test-CargoProject {
    if (-not (Test-Path "Cargo.toml")) {
        Write-Host "错误: 当前目录不是Cargo项目根目录" -ForegroundColor Red
        return $false
    }
    return $true
}

# 检查Criterion基准测试结果
function Test-CriterionResults {
    $criterionDir = "target/criterion"
    if (-not (Test-Path $criterionDir)) {
        Write-Host "错误: 未找到Criterion基准测试结果目录: $criterionDir" -ForegroundColor Red
        Write-Host "请先运行基准测试: cargo bench" -ForegroundColor Yellow
        return $false
    }
    
    # 检查是否有基准测试结果
    $reportFiles = Get-ChildItem -Path $criterionDir -Recurse -Name "*.html" | Where-Object { $_ -like "*report*" }
    if ($reportFiles.Count -eq 0) {
        Write-Host "警告: 未找到基准测试报告文件，可能需要重新运行基准测试" -ForegroundColor Yellow
    }
    
    return $true
}

# 编译报告生成器
function Build-ReportGenerator {
    Write-Host "`n=== 编译基准测试报告生成器 ===" -ForegroundColor Cyan
    
    try {
        # 编译报告生成器
        $compileResult = cargo build --bin generate_benchmark_report --release 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "编译失败:" -ForegroundColor Red
            Write-Host $compileResult -ForegroundColor Red
            return $false
        }
        
        Write-Host "✅ 报告生成器编译成功" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "编译过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 运行报告生成器
function Invoke-ReportGenerator {
    Write-Host "`n=== 生成基准测试报告 ===" -ForegroundColor Cyan
    
    try {
        # 检查输出目录是否存在，如果不存在则创建
        if (-not (Test-Path $OutputDir)) {
            New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
            Write-Host "创建输出目录: $OutputDir" -ForegroundColor Yellow
        }
        
        # 如果强制重新生成，清理旧报告
        if ($Force) {
            Write-Host "清理旧报告文件..." -ForegroundColor Yellow
            Remove-Item -Path "$OutputDir/*" -Force -ErrorAction SilentlyContinue
        }
        
        # 运行报告生成器
        $generateResult = & "target/release/generate_benchmark_report.exe" 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "报告生成失败:" -ForegroundColor Red
            Write-Host $generateResult -ForegroundColor Red
            return $false
        }
        
        Write-Host "✅ 基准测试报告生成成功" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "报告生成过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 显示报告信息
function Show-ReportInfo {
    Write-Host "`n=== 报告文件信息 ===" -ForegroundColor Cyan
    
    $htmlReport = "$OutputDir/benchmark_report.html"
    $markdownReport = "$OutputDir/benchmark_report.md"
    $jsonReport = "$OutputDir/benchmark_data.json"
    
    if (Test-Path $htmlReport) {
        $htmlSize = (Get-Item $htmlReport).Length
        Write-Host "📄 HTML报告: $htmlReport ($([math]::Round($htmlSize/1KB, 2)) KB)" -ForegroundColor Green
    }
    
    if (Test-Path $markdownReport) {
        $mdSize = (Get-Item $markdownReport).Length
        Write-Host "📄 Markdown报告: $markdownReport ($([math]::Round($mdSize/1KB, 2)) KB)" -ForegroundColor Green
    }
    
    if (Test-Path $jsonReport) {
        $jsonSize = (Get-Item $jsonReport).Length
        Write-Host "📄 JSON数据: $jsonReport ($([math]::Round($jsonSize/1KB, 2)) KB)" -ForegroundColor Green
    }
    
    # 如果需要，自动打开HTML报告
    if ($OpenReport -and (Test-Path $htmlReport)) {
        Write-Host "`n打开HTML报告..." -ForegroundColor Yellow
        Start-Process $htmlReport
    }
}

# 主执行流程
try {
    # 检查项目环境
    if (-not (Test-CargoProject)) {
        exit 1
    }
    
    # 检查基准测试结果
    if (-not (Test-CriterionResults)) {
        Write-Host "`n建议先运行基准测试:" -ForegroundColor Yellow
        Write-Host "  cargo bench" -ForegroundColor Cyan
        Write-Host "或者运行特定的基准测试脚本:" -ForegroundColor Yellow
        Write-Host "  .\scripts\run_benchmarks.ps1" -ForegroundColor Cyan
        exit 1
    }
    
    # 编译报告生成器
    if (-not (Build-ReportGenerator)) {
        exit 1
    }
    
    # 运行报告生成器
    if (-not (Invoke-ReportGenerator)) {
        exit 1
    }
    
    # 显示报告信息
    Show-ReportInfo
    
    Write-Host "`n🎉 基准测试报告生成完成！" -ForegroundColor Green
    Write-Host "📂 报告位置: $OutputDir" -ForegroundColor White
    
    if (-not $OpenReport) {
        Write-Host "`n💡 提示: 使用 -OpenReport 参数可自动打开HTML报告" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "脚本执行失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
