# Axum项目性能基准测试运行脚本
# 用于执行Criterion.rs基准测试并生成报告

param(
    [string]$TestType = "all",  # all, api, websocket
    [switch]$OpenReport = $false,  # 是否自动打开HTML报告
    [switch]$SaveBaseline = $false,  # 是否保存基线
    [string]$BaselineName = "main"  # 基线名称
)

Write-Host "=== Axum项目性能基准测试 ===" -ForegroundColor Green
Write-Host "测试类型: $TestType" -ForegroundColor Yellow
Write-Host "项目路径: $(Get-Location)" -ForegroundColor Yellow

# 检查服务器是否运行
function Test-ServerRunning {
    try {
        $response = Invoke-WebRequest -Uri "http://127.0.0.1:3000/health" -TimeoutSec 5 -ErrorAction Stop
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# 启动服务器
function Start-Server {
    Write-Host "启动Axum服务器..." -ForegroundColor Yellow
    
    # 检查是否已经运行
    if (Test-ServerRunning) {
        Write-Host "服务器已在运行中" -ForegroundColor Green
        return
    }
    
    # 启动服务器进程
    $serverProcess = Start-Process -FilePath "cargo" -ArgumentList "run", "-p", "server" -PassThru -WindowStyle Hidden
    
    # 等待服务器启动
    $maxWaitTime = 30
    $waitTime = 0
    
    while (-not (Test-ServerRunning) -and $waitTime -lt $maxWaitTime) {
        Start-Sleep -Seconds 1
        $waitTime++
        Write-Host "等待服务器启动... ($waitTime/$maxWaitTime)" -ForegroundColor Yellow
    }
    
    if (Test-ServerRunning) {
        Write-Host "服务器启动成功!" -ForegroundColor Green
        return $serverProcess
    } else {
        Write-Host "服务器启动失败或超时" -ForegroundColor Red
        exit 1
    }
}

# 运行基准测试
function Run-Benchmarks {
    param(
        [string]$BenchmarkName,
        [string]$DisplayName
    )
    
    Write-Host "`n=== 运行 $DisplayName 基准测试 ===" -ForegroundColor Cyan
    
    $benchArgs = @("bench", "--bench", $BenchmarkName)
    
    if ($SaveBaseline) {
        $benchArgs += "--save-baseline"
        $benchArgs += $BaselineName
        Write-Host "保存基线: $BaselineName" -ForegroundColor Yellow
    }
    
    try {
        & cargo @benchArgs
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "$DisplayName 基准测试完成!" -ForegroundColor Green
        } else {
            Write-Host "$DisplayName 基准测试失败 (退出代码: $LASTEXITCODE)" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "运行 $DisplayName 基准测试时发生错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
    
    return $true
}

# 生成基准测试报告
function Generate-BenchmarkReport {
    Write-Host "`n=== 生成基准测试报告 ===" -ForegroundColor Cyan
    
    $reportDir = "target/criterion"
    $reportFile = "benchmark_report.html"
    
    if (Test-Path $reportDir) {
        Write-Host "基准测试报告目录: $reportDir" -ForegroundColor Yellow
        
        # 列出生成的报告
        $reports = Get-ChildItem -Path $reportDir -Recurse -Name "*.html" | Where-Object { $_ -like "*report*" }
        
        if ($reports.Count -gt 0) {
            Write-Host "生成的报告文件:" -ForegroundColor Green
            foreach ($report in $reports) {
                Write-Host "  - $report" -ForegroundColor White
            }
            
            if ($OpenReport) {
                $mainReport = Join-Path $reportDir "report/index.html"
                if (Test-Path $mainReport) {
                    Write-Host "打开主报告: $mainReport" -ForegroundColor Yellow
                    Start-Process $mainReport
                }
            }
        } else {
            Write-Host "未找到HTML报告文件" -ForegroundColor Yellow
        }
    } else {
        Write-Host "基准测试报告目录不存在: $reportDir" -ForegroundColor Yellow
    }
}

# 清理旧的基准测试数据
function Clear-OldBenchmarkData {
    Write-Host "清理旧的基准测试数据..." -ForegroundColor Yellow
    
    $criterionDir = "target/criterion"
    if (Test-Path $criterionDir) {
        try {
            Remove-Item -Path $criterionDir -Recurse -Force
            Write-Host "已清理旧的基准测试数据" -ForegroundColor Green
        }
        catch {
            Write-Host "清理基准测试数据失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 主执行流程
try {
    # 检查Cargo项目
    if (-not (Test-Path "Cargo.toml")) {
        Write-Host "错误: 当前目录不是Cargo项目根目录" -ForegroundColor Red
        exit 1
    }
    
    # 启动服务器
    $serverProcess = Start-Server
    
    # 等待服务器完全启动
    Start-Sleep -Seconds 3
    
    # 运行基准测试
    $success = $true
    
    switch ($TestType.ToLower()) {
        "api" {
            $success = Run-Benchmarks "api_performance_benchmarks" "API性能"
        }
        "websocket" {
            $success = Run-Benchmarks "websocket_latency_benchmarks" "WebSocket延迟"
        }
        "all" {
            $success = (Run-Benchmarks "api_performance_benchmarks" "API性能") -and 
                      (Run-Benchmarks "websocket_latency_benchmarks" "WebSocket延迟")
        }
        default {
            Write-Host "无效的测试类型: $TestType. 支持的类型: all, api, websocket" -ForegroundColor Red
            exit 1
        }
    }
    
    if ($success) {
        Write-Host "`n=== 所有基准测试完成! ===" -ForegroundColor Green
        Generate-BenchmarkReport
        
        Write-Host "`n基准测试结果摘要:" -ForegroundColor Cyan
        Write-Host "- 测试类型: $TestType" -ForegroundColor White
        Write-Host "- 报告位置: target/criterion/" -ForegroundColor White
        Write-Host "- HTML报告: target/criterion/report/index.html" -ForegroundColor White
        
        if ($SaveBaseline) {
            Write-Host "- 基线已保存: $BaselineName" -ForegroundColor White
        }
    } else {
        Write-Host "`n基准测试执行失败!" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "基准测试脚本执行失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    # 清理：停止服务器进程（如果是我们启动的）
    if ($serverProcess -and -not $serverProcess.HasExited) {
        Write-Host "停止服务器进程..." -ForegroundColor Yellow
        try {
            $serverProcess.Kill()
            $serverProcess.WaitForExit(5000)
        }
        catch {
            Write-Host "停止服务器进程失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "`n基准测试脚本执行完成!" -ForegroundColor Green
