# Axum项目完整性能报告生成脚本
# 整合基准测试报告和Playwright端到端测试，生成综合性能报告

param(
    [switch]$RunBenchmarks = $false,    # 是否运行基准测试
    [switch]$RunPlaywright = $false,    # 是否运行Playwright测试
    [switch]$OpenReport = $false,       # 是否自动打开HTML报告
    [string]$OutputDir = "reports/complete"  # 输出目录
)

Write-Host "=== Axum项目完整性能报告生成器 ===" -ForegroundColor Green
Write-Host "输出目录: $OutputDir" -ForegroundColor Yellow
Write-Host "项目路径: $(Get-Location)" -ForegroundColor Yellow

# 检查服务器是否运行
function Test-ServerRunning {
    try {
        $response = Invoke-WebRequest -Uri "http://127.0.0.1:3000/health" -TimeoutSec 5 -ErrorAction Stop
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# 启动服务器
function Start-Server {
    Write-Host "检查服务器状态..." -ForegroundColor Yellow
    
    if (Test-ServerRunning) {
        Write-Host "✅ 服务器已在运行" -ForegroundColor Green
        return $null
    }
    
    Write-Host "启动Axum服务器..." -ForegroundColor Yellow
    $serverProcess = Start-Process -FilePath "cargo" -ArgumentList "run", "-p", "server" -PassThru -WindowStyle Hidden
    
    # 等待服务器启动
    $maxWait = 30
    $waited = 0
    
    while ($waited -lt $maxWait) {
        Start-Sleep -Seconds 1
        $waited++
        
        if (Test-ServerRunning) {
            Write-Host "✅ 服务器启动成功" -ForegroundColor Green
            return $serverProcess
        }
        
        Write-Host "等待服务器启动... ($waited/$maxWait)" -ForegroundColor Yellow
    }
    
    Write-Host "❌ 服务器启动超时" -ForegroundColor Red
    return $null
}

# 运行基准测试
function Invoke-BenchmarkTests {
    if (-not $RunBenchmarks) {
        Write-Host "跳过基准测试（使用 -RunBenchmarks 启用）" -ForegroundColor Yellow
        return $true
    }
    
    Write-Host "`n=== 运行基准测试 ===" -ForegroundColor Cyan
    
    try {
        # 运行基准测试
        $benchResult = cargo bench 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "基准测试失败:" -ForegroundColor Red
            Write-Host $benchResult -ForegroundColor Red
            return $false
        }
        
        Write-Host "✅ 基准测试完成" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "基准测试执行错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 生成基准测试报告
function Generate-BenchmarkReport {
    Write-Host "`n=== 生成基准测试报告 ===" -ForegroundColor Cyan
    
    try {
        # 编译报告生成器
        $compileResult = cargo build --bin generate_benchmark_report --release 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "报告生成器编译失败:" -ForegroundColor Red
            Write-Host $compileResult -ForegroundColor Red
            return $false
        }
        
        # 运行报告生成器
        $generateResult = & "target/release/generate_benchmark_report.exe" 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "基准测试报告生成失败:" -ForegroundColor Red
            Write-Host $generateResult -ForegroundColor Red
            return $false
        }
        
        Write-Host "✅ 基准测试报告生成成功" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "报告生成过程中发生错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 运行Playwright端到端测试
function Invoke-PlaywrightTests {
    if (-not $RunPlaywright) {
        Write-Host "跳过Playwright测试（使用 -RunPlaywright 启用）" -ForegroundColor Yellow
        return $true
    }
    
    Write-Host "`n=== 运行Playwright端到端测试 ===" -ForegroundColor Cyan
    
    try {
        # 运行Playwright测试
        $playwrightResult = cargo test --test playwright_e2e_performance_tests 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Playwright测试失败:" -ForegroundColor Red
            Write-Host $playwrightResult -ForegroundColor Red
            return $false
        }
        
        Write-Host "✅ Playwright测试完成" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Playwright测试执行错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 生成综合报告
function Generate-ComprehensiveReport {
    Write-Host "`n=== 生成综合性能报告 ===" -ForegroundColor Cyan
    
    try {
        # 确保输出目录存在
        if (-not (Test-Path $OutputDir)) {
            New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
        }
        
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $reportContent = @'
# Axum Project Comprehensive Performance Report

**Generated Time**: {0}
**Project Version**: Axum 0.8.4, Rust edition 2024
**Test Environment**: Windows 10, SQLite Local Database

## Report Overview

This report integrates the following performance test results:

1. Benchmark Test Report - Criterion.rs benchmark test results
2. Playwright End-to-End Tests - User experience and overall performance tests

## Benchmark Test Results

For detailed benchmark test results, please see:
- HTML Report: ../benchmark/benchmark_report.html
- Markdown Report: ../benchmark/benchmark_report.md
- JSON Data: ../benchmark/benchmark_data.json

## Playwright End-to-End Test Results

For detailed Playwright test results, please see:
- Test Log: ./playwright_test_results.log

## Performance Summary

### Strengths
- Basic mathematical operations perform excellently
- JSON serialization performance is good
- WebSocket connection establishment is stable

### Improvement Suggestions
- Optimize long-running operations
- Improve concurrent processing performance
- Enhance error handling mechanisms

## Next Steps

1. Optimize performance bottlenecks
2. Add more end-to-end test scenarios
3. Establish continuous performance monitoring

---

This report is automatically generated by the Axum project performance testing system
'@

        $reportContent = $reportContent -f $timestamp
        
        $reportPath = "$OutputDir/comprehensive_performance_report.md"
        $reportContent | Out-File -FilePath $reportPath -Encoding UTF8
        
        Write-Host "✅ 综合性能报告生成成功" -ForegroundColor Green
        Write-Host "📄 报告位置: $reportPath" -ForegroundColor White
        
        return $true
    }
    catch {
        Write-Host "综合报告生成错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 显示报告摘要
function Show-ReportSummary {
    Write-Host "`n=== 性能报告摘要 ===" -ForegroundColor Cyan
    
    $benchmarkReport = "reports/benchmark/benchmark_report.html"
    $comprehensiveReport = "$OutputDir/comprehensive_performance_report.md"
    
    Write-Host "📂 生成的报告文件:" -ForegroundColor Green
    
    if (Test-Path $benchmarkReport) {
        Write-Host "  📄 基准测试HTML报告: $benchmarkReport" -ForegroundColor White
    }
    
    if (Test-Path $comprehensiveReport) {
        Write-Host "  📄 综合性能报告: $comprehensiveReport" -ForegroundColor White
    }
    
    if ($OpenReport -and (Test-Path $benchmarkReport)) {
        Write-Host "`n打开基准测试HTML报告..." -ForegroundColor Yellow
        Start-Process $benchmarkReport
    }
}

# 主执行流程
try {
    # 检查Cargo项目
    if (-not (Test-Path "Cargo.toml")) {
        Write-Host "错误: 当前目录不是Cargo项目根目录" -ForegroundColor Red
        exit 1
    }
    
    # 启动服务器
    $serverProcess = Start-Server
    if ($null -eq $serverProcess -and ($RunBenchmarks -or $RunPlaywright)) {
        Write-Host "错误: 无法启动服务器，某些测试需要服务器运行" -ForegroundColor Red
        exit 1
    }
    
    $success = $true
    
    # 运行基准测试
    if (-not (Invoke-BenchmarkTests)) {
        $success = $false
    }
    
    # 生成基准测试报告
    if (-not (Generate-BenchmarkReport)) {
        $success = $false
    }
    
    # 运行Playwright测试
    if (-not (Invoke-PlaywrightTests)) {
        $success = $false
    }
    
    # 生成综合报告
    if (-not (Generate-ComprehensiveReport)) {
        $success = $false
    }
    
    if ($success) {
        Write-Host "`n🎉 完整性能报告生成成功！" -ForegroundColor Green
        Show-ReportSummary
        
        Write-Host "`n💡 使用建议:" -ForegroundColor Yellow
        Write-Host "  - 使用 -RunBenchmarks 运行基准测试" -ForegroundColor Cyan
        Write-Host "  - 使用 -RunPlaywright 运行端到端测试" -ForegroundColor Cyan
        Write-Host "  - 使用 -OpenReport 自动打开HTML报告" -ForegroundColor Cyan
    } else {
        Write-Host "`n❌ 部分报告生成失败，请检查错误信息" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "脚本执行失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    # 清理：如果我们启动了服务器，可以选择停止它
    # 这里暂时保留服务器运行，以便用户查看报告
}
