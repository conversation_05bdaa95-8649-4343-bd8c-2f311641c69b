# 综合性能基准测试运行脚本 - 任务17专用
# 支持Criterion.rs、wrk、ab工具进行全面的性能基准测试

param(
    [string]$TestType = "all",  # all, criterion, wrk, ab
    [switch]$OpenReport = $false,  # Auto open HTML report
    [switch]$SaveBaseline = $false,  # Save baseline
    [string]$BaselineName = "main",  # Baseline name
    [int]$Duration = 30,  # wrk/ab test duration in seconds
    [int]$Connections = 100,  # Concurrent connections
    [int]$Threads = 4  # Thread count
)

Write-Host "=== Axum Project Comprehensive Performance Benchmarks - Task 17 ===" -ForegroundColor Green
Write-Host "Test Type: $TestType" -ForegroundColor Yellow
Write-Host "Project Path: $(Get-Location)" -ForegroundColor Yellow

# Check if server is running
function Test-ServerRunning {
    try {
        $response = Invoke-WebRequest -Uri "http://127.0.0.1:3000/health" -TimeoutSec 5 -ErrorAction Stop
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# 启动服务器
function Start-Server {
    Write-Host "启动Axum服务器..." -ForegroundColor Yellow
    
    if (Test-ServerRunning) {
        Write-Host "服务器已在运行中" -ForegroundColor Green
        return $null
    }
    
    $serverProcess = Start-Process -FilePath "cargo" -ArgumentList "run", "-p", "server" -PassThru -WindowStyle Hidden
    
    $maxWaitTime = 30
    $waitTime = 0
    
    while (-not (Test-ServerRunning) -and $waitTime -lt $maxWaitTime) {
        Start-Sleep -Seconds 1
        $waitTime++
        Write-Host "等待服务器启动... ($waitTime/$maxWaitTime)" -ForegroundColor Yellow
    }
    
    if (Test-ServerRunning) {
        Write-Host "Server started successfully!" -ForegroundColor Green
        return $serverProcess
    } else {
        Write-Host "Server startup failed or timeout" -ForegroundColor Red
        exit 1
    }
}

# 获取JWT Token
function Get-AuthToken {
    try {
        $loginData = @{
            email = "<EMAIL>"
            password = "password123"
        } | ConvertTo-Json

        $response = Invoke-RestMethod -Uri "http://127.0.0.1:3000/api/auth/login" -Method Post -Body $loginData -ContentType "application/json"
        return $response.token
    }
    catch {
        Write-Host "获取认证Token失败: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# 运行Criterion.rs基准测试
function Run-CriterionBenchmarks {
    Write-Host "`n=== 运行Criterion.rs基准测试 ===" -ForegroundColor Cyan
    
    $benchmarks = @(
        @{Name = "comprehensive_performance_benchmarks"; Display = "综合性能基准测试"}
        @{Name = "api_performance_benchmarks"; Display = "API性能基准测试"}
        @{Name = "websocket_latency_benchmarks"; Display = "WebSocket延迟基准测试"}
    )
    
    $success = $true
    
    foreach ($benchmark in $benchmarks) {
        Write-Host "运行 $($benchmark.Display)..." -ForegroundColor Yellow
        
        $benchArgs = @("bench", "--bench", $benchmark.Name)
        
        if ($SaveBaseline) {
            $benchArgs += "--save-baseline"
            $benchArgs += $BaselineName
        }
        
        try {
            & cargo @benchArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "$($benchmark.Display) 完成!" -ForegroundColor Green
            } else {
                Write-Host "$($benchmark.Display) 失败 (退出代码: $LASTEXITCODE)" -ForegroundColor Red
                $success = $false
            }
        }
        catch {
            Write-Host "运行 $($benchmark.Display) 时发生错误: $($_.Exception.Message)" -ForegroundColor Red
            $success = $false
        }
    }
    
    return $success
}

# 运行wrk基准测试
function Run-WrkBenchmarks {
    Write-Host "`n=== 运行wrk基准测试 ===" -ForegroundColor Cyan
    
    # 检查wrk是否安装
    try {
        $null = Get-Command wrk -ErrorAction Stop
    }
    catch {
        Write-Host "wrk未安装，跳过wrk基准测试" -ForegroundColor Yellow
        Write-Host "安装方法: 下载wrk并添加到PATH环境变量" -ForegroundColor Yellow
        return $true
    }
    
    $token = Get-AuthToken
    if (-not $token) {
        Write-Host "无法获取认证Token，跳过wrk测试" -ForegroundColor Red
        return $false
    }
    
    $reportDir = "reports/benchmark"
    if (-not (Test-Path $reportDir)) {
        New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
    }
    
    # 测试不同的API端点
    $endpoints = @(
        @{Name = "GET_tasks"; Url = "http://127.0.0.1:3000/api/tasks"; Method = "GET"}
        @{Name = "GET_profile"; Url = "http://127.0.0.1:3000/api/auth/profile"; Method = "GET"}
        @{Name = "GET_health"; Url = "http://127.0.0.1:3000/health"; Method = "GET"}
    )
    
    foreach ($endpoint in $endpoints) {
        Write-Host "测试端点: $($endpoint.Name)" -ForegroundColor Yellow
        
        $outputFile = Join-Path $reportDir "wrk_$($endpoint.Name)_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
        
        $wrkArgs = @(
            "-t$Threads"
            "-c$Connections"
            "-d${Duration}s"
            "--timeout", "30s"
            "-H", "Authorization: Bearer $token"
            $endpoint.Url
        )
        
        try {
            $result = & wrk @wrkArgs 2>&1
            $result | Out-File -FilePath $outputFile -Encoding UTF8
            
            Write-Host "wrk测试完成: $($endpoint.Name)" -ForegroundColor Green
            Write-Host "报告保存至: $outputFile" -ForegroundColor White
            
            # 显示关键指标
            $latencyLine = $result | Where-Object { $_ -match "Latency" } | Select-Object -First 1
            $requestsLine = $result | Where-Object { $_ -match "Requests/sec" } | Select-Object -First 1
            
            if ($latencyLine) { Write-Host "  $latencyLine" -ForegroundColor Cyan }
            if ($requestsLine) { Write-Host "  $requestsLine" -ForegroundColor Cyan }
        }
        catch {
            Write-Host "wrk测试失败: $($endpoint.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    return $true
}

# 运行ab基准测试
function Run-AbBenchmarks {
    Write-Host "`n=== 运行Apache Bench (ab) 基准测试 ===" -ForegroundColor Cyan
    
    # 检查ab是否安装
    try {
        $null = Get-Command ab -ErrorAction Stop
    }
    catch {
        Write-Host "ab未安装，跳过ab基准测试" -ForegroundColor Yellow
        Write-Host "安装方法: 安装Apache HTTP Server或单独安装ab工具" -ForegroundColor Yellow
        return $true
    }
    
    $token = Get-AuthToken
    if (-not $token) {
        Write-Host "无法获取认证Token，跳过ab测试" -ForegroundColor Red
        return $false
    }
    
    $reportDir = "reports/benchmark"
    if (-not (Test-Path $reportDir)) {
        New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
    }
    
    # 测试配置
    $totalRequests = $Connections * 10
    $concurrency = $Connections
    
    $endpoints = @(
        @{Name = "GET_tasks"; Url = "http://127.0.0.1:3000/api/tasks"}
        @{Name = "GET_health"; Url = "http://127.0.0.1:3000/health"}
    )
    
    foreach ($endpoint in $endpoints) {
        Write-Host "测试端点: $($endpoint.Name)" -ForegroundColor Yellow
        
        $outputFile = Join-Path $reportDir "ab_$($endpoint.Name)_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
        
        $abArgs = @(
            "-n", $totalRequests
            "-c", $concurrency
            "-H", "Authorization: Bearer $token"
            "-g", ($outputFile -replace ".txt", ".tsv")
            $endpoint.Url
        )
        
        try {
            $result = & ab @abArgs 2>&1
            $result | Out-File -FilePath $outputFile -Encoding UTF8
            
            Write-Host "ab测试完成: $($endpoint.Name)" -ForegroundColor Green
            Write-Host "报告保存至: $outputFile" -ForegroundColor White
            
            # 显示关键指标
            $rpsLine = $result | Where-Object { $_ -match "Requests per second" } | Select-Object -First 1
            $timeLine = $result | Where-Object { $_ -match "Time per request.*mean" } | Select-Object -First 1
            
            if ($rpsLine) { Write-Host "  $rpsLine" -ForegroundColor Cyan }
            if ($timeLine) { Write-Host "  $timeLine" -ForegroundColor Cyan }
        }
        catch {
            Write-Host "ab测试失败: $($endpoint.Name) - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    return $true
}

# 生成综合报告
function Generate-ComprehensiveReport {
    Write-Host "`n=== 生成综合基准测试报告 ===" -ForegroundColor Cyan
    
    $reportDir = "reports/benchmark"
    $criterionDir = "target/criterion"
    
    Write-Host "基准测试报告位置:" -ForegroundColor Green
    
    if (Test-Path $criterionDir) {
        Write-Host "  - Criterion.rs报告: $criterionDir/report/index.html" -ForegroundColor White
        
        if ($OpenReport) {
            $mainReport = Join-Path $criterionDir "report/index.html"
            if (Test-Path $mainReport) {
                Start-Process $mainReport
            }
        }
    }
    
    if (Test-Path $reportDir) {
        $reports = Get-ChildItem -Path $reportDir -Filter "*.txt" | Sort-Object LastWriteTime -Descending
        if ($reports.Count -gt 0) {
            Write-Host "  - wrk/ab报告目录: $reportDir" -ForegroundColor White
            foreach ($report in $reports | Select-Object -First 5) {
                Write-Host "    * $($report.Name)" -ForegroundColor Gray
            }
        }
    }
}

# 主执行流程
try {
    if (-not (Test-Path "Cargo.toml")) {
        Write-Host "错误: 当前目录不是Cargo项目根目录" -ForegroundColor Red
        exit 1
    }
    
    $serverProcess = Start-Server
    Start-Sleep -Seconds 3
    
    $success = $true
    
    switch ($TestType.ToLower()) {
        "criterion" {
            $success = Run-CriterionBenchmarks
        }
        "wrk" {
            $success = Run-WrkBenchmarks
        }
        "ab" {
            $success = Run-AbBenchmarks
        }
        "all" {
            $success = (Run-CriterionBenchmarks) -and (Run-WrkBenchmarks) -and (Run-AbBenchmarks)
        }
        default {
            Write-Host "无效的测试类型: $TestType. 支持的类型: all, criterion, wrk, ab" -ForegroundColor Red
            exit 1
        }
    }
    
    if ($success) {
        Write-Host "`n=== 所有基准测试完成! ===" -ForegroundColor Green
        Generate-ComprehensiveReport
    } else {
        Write-Host "`n基准测试执行失败!" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "基准测试脚本执行失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    if ($serverProcess -and -not $serverProcess.HasExited) {
        Write-Host "停止服务器进程..." -ForegroundColor Yellow
        try {
            $serverProcess.Kill()
            $serverProcess.WaitForExit(5000)
        }
        catch {
            Write-Host "Failed to stop server process: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host "`nComprehensive benchmark script execution completed!" -ForegroundColor Green
