# 任务11.4：并发负载基准测试执行脚本
# 结合Prometheus+Grafana监控系统观察资源使用情况

param(
    [string]$TestType = "all",
    [int]$Duration = 300,
    [switch]$WithMonitoring = $true,
    [switch]$GenerateReport = $true
)

Write-Host "=== Axum项目并发负载基准测试 ===" -ForegroundColor Green
Write-Host "测试类型: $TestType" -ForegroundColor Yellow
Write-Host "测试持续时间: $Duration 秒" -ForegroundColor Yellow
Write-Host "监控集成: $WithMonitoring" -ForegroundColor Yellow

# 检查服务器是否运行
function Test-ServerRunning {
    try {
        $response = Invoke-WebRequest -Uri "http://127.0.0.1:3000/health" -TimeoutSec 5 -ErrorAction Stop
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# 启动监控系统
function Start-Monitoring {
    if ($WithMonitoring) {
        Write-Host "启动Prometheus+Grafana监控系统..." -ForegroundColor Blue
        
        # 检查Docker是否运行
        try {
            docker --version | Out-Null
            Write-Host "Docker已安装" -ForegroundColor Green
        }
        catch {
            Write-Host "警告: Docker未安装或未运行，跳过监控系统启动" -ForegroundColor Yellow
            return $false
        }
        
        # 启动监控容器
        Push-Location monitoring
        try {
            docker-compose up -d
            Start-Sleep -Seconds 10
            Write-Host "监控系统已启动" -ForegroundColor Green
            return $true
        }
        catch {
            Write-Host "警告: 监控系统启动失败" -ForegroundColor Yellow
            return $false
        }
        finally {
            Pop-Location
        }
    }
    return $false
}

# 停止监控系统
function Stop-Monitoring {
    if ($WithMonitoring) {
        Write-Host "停止监控系统..." -ForegroundColor Blue
        Push-Location monitoring
        try {
            docker-compose down
            Write-Host "监控系统已停止" -ForegroundColor Green
        }
        catch {
            Write-Host "警告: 监控系统停止失败" -ForegroundColor Yellow
        }
        finally {
            Pop-Location
        }
    }
}

# 运行基准测试
function Run-BenchmarkTest {
    param([string]$BenchName)
    
    Write-Host "运行基准测试: $BenchName" -ForegroundColor Cyan
    
    try {
        # 运行基准测试并捕获输出
        $output = cargo bench --bench $BenchName 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "基准测试 $BenchName 完成" -ForegroundColor Green
            return $output
        } else {
            Write-Host "基准测试 $BenchName 失败" -ForegroundColor Red
            Write-Host $output -ForegroundColor Red
            return $null
        }
    }
    catch {
        Write-Host "运行基准测试时发生错误: $_" -ForegroundColor Red
        return $null
    }
}

# 生成测试报告
function Generate-TestReport {
    param([array]$TestResults)
    
    if (-not $GenerateReport) {
        return
    }
    
    Write-Host "生成测试报告..." -ForegroundColor Blue
    
    $reportPath = "target/criterion/concurrent_load_test_report.md"
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    
    $report = @"
# 并发负载基准测试报告

**生成时间**: $timestamp
**测试类型**: $TestType
**测试持续时间**: $Duration 秒
**监控集成**: $WithMonitoring

## 测试环境
- 操作系统: Windows 10
- Axum版本: 0.8.4
- Tokio版本: 1.45.1
- 数据库: SQLite (本地)
- 服务器地址: 127.0.0.1:3000

## 测试结果摘要

"@
    
    foreach ($result in $TestResults) {
        if ($result) {
            $report += "`n### 测试输出`n"
            $report += "``````"
            $report += "`n$result`n"
            $report += "``````"
            $report += "`n"
        }
    }
    
    $report += @"

## 监控数据访问

如果启用了监控系统，可以通过以下地址访问：

- **Grafana仪表板**: http://localhost:3001
  - 用户名: admin
  - 密码: admin
- **Prometheus指标**: http://localhost:9090

## 建议

1. 观察Grafana中的CPU、内存和网络使用情况
2. 检查响应时间趋势和错误率
3. 分析不同并发级别下的性能表现
4. 根据测试结果调整服务器配置

## 下一步

- 分析性能瓶颈
- 优化数据库查询
- 调整连接池配置
- 考虑负载均衡策略
"@
    
    # 确保目录存在
    $reportDir = Split-Path $reportPath -Parent
    if (-not (Test-Path $reportDir)) {
        New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
    }
    
    # 写入报告
    $report | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "测试报告已生成: $reportPath" -ForegroundColor Green
}

# 主执行流程
try {
    # 检查服务器状态
    Write-Host "检查Axum服务器状态..." -ForegroundColor Blue
    if (-not (Test-ServerRunning)) {
        Write-Host "错误: Axum服务器未运行，请先启动服务器" -ForegroundColor Red
        Write-Host "运行命令: cargo run -p server" -ForegroundColor Yellow
        exit 1
    }
    Write-Host "Axum服务器运行正常" -ForegroundColor Green
    
    # 启动监控系统
    $monitoringStarted = Start-Monitoring
    
    # 等待监控系统稳定
    if ($monitoringStarted) {
        Write-Host "等待监控系统稳定..." -ForegroundColor Blue
        Start-Sleep -Seconds 15
    }
    
    # 运行基准测试
    $testResults = @()
    
    switch ($TestType.ToLower()) {
        "all" {
            Write-Host "运行所有并发负载基准测试..." -ForegroundColor Cyan
            $testResults += Run-BenchmarkTest "concurrent_load_benchmarks"
            $testResults += Run-BenchmarkTest "api_performance_benchmarks"
        }
        "concurrent" {
            Write-Host "运行并发负载基准测试..." -ForegroundColor Cyan
            $testResults += Run-BenchmarkTest "concurrent_load_benchmarks"
        }
        "api" {
            Write-Host "运行API性能基准测试..." -ForegroundColor Cyan
            $testResults += Run-BenchmarkTest "api_performance_benchmarks"
        }
        default {
            Write-Host "未知测试类型: $TestType" -ForegroundColor Red
            Write-Host "支持的类型: all, concurrent, api" -ForegroundColor Yellow
            exit 1
        }
    }
    
    # 生成报告
    Generate-TestReport $testResults
    
    # 显示监控信息
    if ($monitoringStarted) {
        Write-Host "`n=== 监控系统信息 ===" -ForegroundColor Green
        Write-Host "Grafana仪表板: http://localhost:3001 (admin/admin)" -ForegroundColor Yellow
        Write-Host "Prometheus指标: http://localhost:9090" -ForegroundColor Yellow
        Write-Host "建议在Grafana中观察测试期间的系统资源使用情况" -ForegroundColor Cyan
    }
    
    Write-Host "`n=== 并发负载基准测试完成 ===" -ForegroundColor Green
    Write-Host "查看详细结果: target/criterion/" -ForegroundColor Yellow
    
}
catch {
    Write-Host "执行过程中发生错误: $_" -ForegroundColor Red
    exit 1
}
finally {
    # 清理资源
    if ($WithMonitoring) {
        Write-Host "`n是否停止监控系统? (y/N): " -ForegroundColor Yellow -NoNewline
        $response = Read-Host
        if ($response -eq "y" -or $response -eq "Y") {
            Stop-Monitoring
        } else {
            Write-Host "监控系统继续运行，可手动停止: docker-compose down" -ForegroundColor Cyan
        }
    }
}
