#!/bin/bash

# 任务11.4：并发负载基准测试执行脚本 (Linux版本)
# 结合Prometheus+Grafana监控系统观察资源使用情况

set -e

# 默认参数
TEST_TYPE="all"
DURATION=300
WITH_MONITORING=true
GENERATE_REPORT=true

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --test-type)
            TEST_TYPE="$2"
            shift 2
            ;;
        --duration)
            DURATION="$2"
            shift 2
            ;;
        --no-monitoring)
            WITH_MONITORING=false
            shift
            ;;
        --no-report)
            GENERATE_REPORT=false
            shift
            ;;
        -h|--help)
            echo "用法: $0 [选项]"
            echo "选项:"
            echo "  --test-type TYPE    测试类型 (all|concurrent|api) [默认: all]"
            echo "  --duration SECONDS  测试持续时间 [默认: 300]"
            echo "  --no-monitoring     禁用监控系统"
            echo "  --no-report         禁用报告生成"
            echo "  -h, --help          显示帮助信息"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            exit 1
            ;;
    esac
done

echo "=== Axum项目并发负载基准测试 ==="
echo "测试类型: $TEST_TYPE"
echo "测试持续时间: $DURATION 秒"
echo "监控集成: $WITH_MONITORING"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 检查服务器是否运行
test_server_running() {
    if curl -s --max-time 5 http://127.0.0.1:3000/health > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# 启动监控系统
start_monitoring() {
    if [ "$WITH_MONITORING" = true ]; then
        echo -e "${BLUE}启动Prometheus+Grafana监控系统...${NC}"
        
        # 检查Docker是否运行
        if ! command -v docker &> /dev/null; then
            echo -e "${YELLOW}警告: Docker未安装，跳过监控系统启动${NC}"
            return 1
        fi
        
        if ! docker info &> /dev/null; then
            echo -e "${YELLOW}警告: Docker未运行，跳过监控系统启动${NC}"
            return 1
        fi
        
        # 启动监控容器
        pushd monitoring > /dev/null
        if docker-compose up -d; then
            sleep 10
            echo -e "${GREEN}监控系统已启动${NC}"
            popd > /dev/null
            return 0
        else
            echo -e "${YELLOW}警告: 监控系统启动失败${NC}"
            popd > /dev/null
            return 1
        fi
    fi
    return 1
}

# 停止监控系统
stop_monitoring() {
    if [ "$WITH_MONITORING" = true ]; then
        echo -e "${BLUE}停止监控系统...${NC}"
        pushd monitoring > /dev/null
        if docker-compose down; then
            echo -e "${GREEN}监控系统已停止${NC}"
        else
            echo -e "${YELLOW}警告: 监控系统停止失败${NC}"
        fi
        popd > /dev/null
    fi
}

# 运行基准测试
run_benchmark_test() {
    local bench_name=$1
    echo -e "${CYAN}运行基准测试: $bench_name${NC}"
    
    if cargo bench --bench "$bench_name"; then
        echo -e "${GREEN}基准测试 $bench_name 完成${NC}"
        return 0
    else
        echo -e "${RED}基准测试 $bench_name 失败${NC}"
        return 1
    fi
}

# 生成测试报告
generate_test_report() {
    if [ "$GENERATE_REPORT" = false ]; then
        return
    fi
    
    echo -e "${BLUE}生成测试报告...${NC}"
    
    local report_path="target/criterion/concurrent_load_test_report.md"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 确保目录存在
    mkdir -p "$(dirname "$report_path")"
    
    cat > "$report_path" << EOF
# 并发负载基准测试报告

**生成时间**: $timestamp
**测试类型**: $TEST_TYPE
**测试持续时间**: $DURATION 秒
**监控集成**: $WITH_MONITORING

## 测试环境
- 操作系统: $(uname -s) $(uname -r)
- Axum版本: 0.8.4
- Tokio版本: 1.45.1
- 数据库: SQLite (本地)
- 服务器地址: 127.0.0.1:3000

## 测试结果摘要

基准测试结果已保存在 \`target/criterion/\` 目录中。

## 监控数据访问

如果启用了监控系统，可以通过以下地址访问：

- **Grafana仪表板**: http://localhost:3001
  - 用户名: admin
  - 密码: admin
- **Prometheus指标**: http://localhost:9090

## 建议

1. 观察Grafana中的CPU、内存和网络使用情况
2. 检查响应时间趋势和错误率
3. 分析不同并发级别下的性能表现
4. 根据测试结果调整服务器配置

## 下一步

- 分析性能瓶颈
- 优化数据库查询
- 调整连接池配置
- 考虑负载均衡策略
EOF
    
    echo -e "${GREEN}测试报告已生成: $report_path${NC}"
}

# 清理函数
cleanup() {
    if [ "$WITH_MONITORING" = true ]; then
        echo -e "\n${YELLOW}是否停止监控系统? (y/N): ${NC}"
        read -r response
        if [[ "$response" =~ ^[Yy]$ ]]; then
            stop_monitoring
        else
            echo -e "${CYAN}监控系统继续运行，可手动停止: docker-compose down${NC}"
        fi
    fi
}

# 设置信号处理
trap cleanup EXIT

# 主执行流程
main() {
    # 检查服务器状态
    echo -e "${BLUE}检查Axum服务器状态...${NC}"
    if ! test_server_running; then
        echo -e "${RED}错误: Axum服务器未运行，请先启动服务器${NC}"
        echo -e "${YELLOW}运行命令: cargo run -p server${NC}"
        exit 1
    fi
    echo -e "${GREEN}Axum服务器运行正常${NC}"
    
    # 启动监控系统
    monitoring_started=false
    if start_monitoring; then
        monitoring_started=true
        echo -e "${BLUE}等待监控系统稳定...${NC}"
        sleep 15
    fi
    
    # 运行基准测试
    case "$TEST_TYPE" in
        "all")
            echo -e "${CYAN}运行所有并发负载基准测试...${NC}"
            run_benchmark_test "concurrent_load_benchmarks"
            run_benchmark_test "api_performance_benchmarks"
            ;;
        "concurrent")
            echo -e "${CYAN}运行并发负载基准测试...${NC}"
            run_benchmark_test "concurrent_load_benchmarks"
            ;;
        "api")
            echo -e "${CYAN}运行API性能基准测试...${NC}"
            run_benchmark_test "api_performance_benchmarks"
            ;;
        *)
            echo -e "${RED}未知测试类型: $TEST_TYPE${NC}"
            echo -e "${YELLOW}支持的类型: all, concurrent, api${NC}"
            exit 1
            ;;
    esac
    
    # 生成报告
    generate_test_report
    
    # 显示监控信息
    if [ "$monitoring_started" = true ]; then
        echo -e "\n${GREEN}=== 监控系统信息 ===${NC}"
        echo -e "${YELLOW}Grafana仪表板: http://localhost:3001 (admin/admin)${NC}"
        echo -e "${YELLOW}Prometheus指标: http://localhost:9090${NC}"
        echo -e "${CYAN}建议在Grafana中观察测试期间的系统资源使用情况${NC}"
    fi
    
    echo -e "\n${GREEN}=== 并发负载基准测试完成 ===${NC}"
    echo -e "${YELLOW}查看详细结果: target/criterion/${NC}"
}

# 执行主函数
main "$@"
