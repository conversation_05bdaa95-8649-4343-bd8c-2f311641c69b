//! # 端到端测试执行脚本
//!
//! 这个脚本用于执行完整的API端到端测试套件，包括：
//! - 启动服务器
//! - 运行所有测试
//! - 生成测试报告
//! - 清理资源
//!
//! ## 使用方法
//! ```bash
//! cargo run --bin run_e2e_tests
//! ```

use std::process::{Command, Stdio};
use std::thread;
use std::time::{Duration, Instant};
// use std::io::{ BufRead, BufReader }; // 暂时未使用
use anyhow::{Context, Result};

/// 测试执行器配置
#[derive(Debug)]
struct TestConfig {
    /// 服务器启动超时时间（秒）
    server_timeout: u64,
    /// 测试执行超时时间（秒）
    test_timeout: u64,
    /// 服务器端口
    server_port: u16,
    /// 是否启用详细输出
    verbose: bool,
}

impl Default for TestConfig {
    fn default() -> Self {
        Self {
            server_timeout: 30,
            test_timeout: 300, // 5分钟
            server_port: 3000,
            verbose: true,
        }
    }
}

/// 测试执行器
struct E2ETestRunner {
    config: TestConfig,
}

impl E2ETestRunner {
    /// 创建新的测试执行器
    fn new() -> Self {
        Self {
            config: TestConfig::default(),
        }
    }

    /// 创建带自定义配置的测试执行器
    fn with_config(config: TestConfig) -> Self {
        Self { config }
    }

    /// 执行完整的端到端测试流程
    async fn run_full_test_suite(&self) -> Result<()> {
        println!("🚀 开始执行完整的端到端测试套件");
        println!("📋 测试配置: {:?}", self.config);

        let start_time = Instant::now();

        // 1. 检查环境
        self.check_environment()?;

        // 2. 启动服务器
        let server_process = self.start_server().await?;

        // 3. 等待服务器就绪
        self.wait_for_server_ready().await?;

        // 4. 运行测试
        let test_result = self.run_tests().await;

        // 5. 停止服务器
        self.stop_server(server_process)?;

        // 6. 生成报告
        let duration = start_time.elapsed();
        self.generate_report(test_result.is_ok(), duration)?;

        test_result
    }

    /// 检查测试环境
    fn check_environment(&self) -> Result<()> {
        println!("🔍 检查测试环境...");

        // 检查Cargo是否可用
        let cargo_version = Command::new("cargo")
            .arg("--version")
            .output()
            .context("无法执行cargo命令，请确保Rust已正确安装")?;

        if !cargo_version.status.success() {
            anyhow::bail!("Cargo不可用");
        }

        if self.config.verbose {
            println!(
                "✅ Cargo版本: {}",
                String::from_utf8_lossy(&cargo_version.stdout).trim()
            );
        }

        // 检查端口是否可用
        if self.is_port_in_use(self.config.server_port)? {
            anyhow::bail!(
                "端口 {} 已被占用，请停止相关服务或更改端口",
                self.config.server_port
            );
        }

        println!("✅ 环境检查通过");
        Ok(())
    }

    /// 检查端口是否被占用
    fn is_port_in_use(&self, port: u16) -> Result<bool> {
        use std::net::{SocketAddr, TcpListener};

        let addr = SocketAddr::from(([127, 0, 0, 1], port));
        match TcpListener::bind(addr) {
            Ok(_) => Ok(false),
            Err(_) => Ok(true),
        }
    }

    /// 启动服务器
    async fn start_server(&self) -> Result<std::process::Child> {
        println!("🚀 启动Axum服务器...");

        let mut server_process = Command::new("cargo")
            .args(&["run", "--bin", "axum-tutorial"])
            .stdout(Stdio::piped())
            .stderr(Stdio::piped())
            .spawn()
            .context("无法启动服务器进程")?;

        // 给服务器一些时间启动
        thread::sleep(Duration::from_secs(2));

        // 检查进程是否还在运行
        match server_process.try_wait()? {
            Some(status) => {
                anyhow::bail!("服务器进程意外退出，状态码: {:?}", status);
            }
            None => {
                if self.config.verbose {
                    println!("✅ 服务器进程已启动，PID: {}", server_process.id());
                }
            }
        }

        Ok(server_process)
    }

    /// 等待服务器就绪
    async fn wait_for_server_ready(&self) -> Result<()> {
        println!("⏳ 等待服务器就绪...");

        let start_time = Instant::now();
        let timeout = Duration::from_secs(self.config.server_timeout);

        while start_time.elapsed() < timeout {
            if self.check_server_health().await.is_ok() {
                println!("✅ 服务器已就绪");
                return Ok(());
            }

            thread::sleep(Duration::from_secs(1));

            if self.config.verbose {
                print!(".");
                use std::io::{self, Write};
                io::stdout().flush().unwrap();
            }
        }

        anyhow::bail!("服务器启动超时（{}秒）", self.config.server_timeout);
    }

    /// 检查服务器健康状态
    async fn check_server_health(&self) -> Result<()> {
        let client = reqwest::Client::new();
        let url = format!("http://127.0.0.1:{}/api/health", self.config.server_port);

        let response = client
            .get(&url)
            .timeout(Duration::from_secs(5))
            .send()
            .await?;

        if response.status().is_success() {
            Ok(())
        } else {
            anyhow::bail!("健康检查失败，状态码: {}", response.status());
        }
    }

    /// 运行测试
    async fn run_tests(&self) -> Result<()> {
        println!("🧪 运行端到端测试...");

        let start_time = Instant::now();

        // 运行完整的测试套件
        let test_output = Command::new("cargo")
            .args(&[
                "test",
                "--test",
                "comprehensive_e2e_api_test",
                "--",
                "--nocapture",
            ])
            .output()
            .context("无法执行测试命令")?;

        let duration = start_time.elapsed();

        if self.config.verbose {
            println!("📤 测试输出:");
            println!("{}", String::from_utf8_lossy(&test_output.stdout));

            if !test_output.stderr.is_empty() {
                println!("⚠️  测试错误输出:");
                println!("{}", String::from_utf8_lossy(&test_output.stderr));
            }
        }

        if test_output.status.success() {
            println!("✅ 所有测试通过！耗时: {:.2}秒", duration.as_secs_f64());
            Ok(())
        } else {
            anyhow::bail!("测试失败，退出码: {:?}", test_output.status.code());
        }
    }

    /// 停止服务器
    fn stop_server(&self, mut server_process: std::process::Child) -> Result<()> {
        println!("🛑 停止服务器...");

        // 尝试优雅关闭
        server_process.kill().context("无法终止服务器进程")?;

        // 等待进程结束
        let status = server_process.wait().context("等待服务器进程结束失败")?;
        if self.config.verbose {
            println!("✅ 服务器已停止，状态码: {:?}", status);
        }

        Ok(())
    }

    /// 生成测试报告
    fn generate_report(&self, success: bool, duration: Duration) -> Result<()> {
        println!("\n📊 测试报告");
        println!("{}", "=".repeat(50));
        println!("测试结果: {}", if success { "✅ 通过" } else { "❌ 失败" });
        println!("总耗时: {:.2}秒", duration.as_secs_f64());
        println!("服务器端口: {}", self.config.server_port);
        println!(
            "测试时间: {}",
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_secs()
        );
        println!("{}", "=".repeat(50));

        if success {
            println!("🎉 恭喜！所有端到端测试都通过了！");
            println!("✨ API服务器功能正常，可以进行下一步开发或部署");
        } else {
            println!("💥 测试失败，请检查上面的错误信息");
            println!("🔧 建议检查服务器日志和测试输出");
        }

        Ok(())
    }
}

/// 主函数
#[tokio::main]
async fn main() -> Result<()> {
    // 设置日志
    println!("🔧 初始化端到端测试环境...");

    // 解析命令行参数
    let args: Vec<String> = std::env::args().collect();
    let verbose = args.contains(&"--verbose".to_string()) || args.contains(&"-v".to_string());

    let config = TestConfig {
        verbose,
        ..TestConfig::default()
    };

    // 创建测试执行器并运行
    let runner = E2ETestRunner::with_config(config);

    match runner.run_full_test_suite().await {
        Ok(_) => {
            println!("\n🎯 端到端测试执行完成！");
            std::process::exit(0);
        }
        Err(e) => {
            eprintln!("\n💥 端到端测试执行失败: {}", e);
            std::process::exit(1);
        }
    }
}
