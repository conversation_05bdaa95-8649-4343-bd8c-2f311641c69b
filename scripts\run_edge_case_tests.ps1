# Axum项目边缘情况测试执行脚本
# 任务10: 实现边缘情况测试的完整执行脚本

param(
    [string]$ServerUrl = "http://127.0.0.1:3000",
    [string]$TestType = "all",
    [int]$Duration = 30
)

Write-Host "🚀 Axum项目边缘情况测试套件" -ForegroundColor Green
Write-Host "任务10: 实现边缘情况测试" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Yellow

# 检查服务器是否运行
function Test-ServerRunning {
    param([string]$Url)
    
    Write-Host "🔍 检查服务器状态..." -ForegroundColor Cyan
    try {
        $response = Invoke-WebRequest -Uri "$Url/api/tasks" -Method GET -TimeoutSec 10
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 服务器运行正常" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ 服务器未运行或无法访问: $Url" -ForegroundColor Red
        Write-Host "请先启动Axum服务器:" -ForegroundColor Yellow
        Write-Host "  cargo run -p server" -ForegroundColor Gray
        return $false
    }
    return $false
}

# 执行任务10.1: 网络中断恢复测试
function Invoke-NetworkInterruptionTest {
    Write-Host "`n📡 任务10.1: 网络中断恢复测试" -ForegroundColor Cyan
    Write-Host "-" * 50 -ForegroundColor Gray
    
    try {
        # 使用Rust测试运行器
        Write-Host "执行网络中断恢复测试..." -ForegroundColor White
        $result = cargo test --test edge_case_test_runner network_interruption --release 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 网络中断恢复测试通过" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ 网络中断恢复测试失败" -ForegroundColor Red
            Write-Host $result -ForegroundColor Gray
            return $false
        }
    }
    catch {
        Write-Host "❌ 网络中断恢复测试执行失败: $_" -ForegroundColor Red
        return $false
    }
}

# 执行任务10.2: 大数据量处理测试
function Invoke-LargeDataTest {
    Write-Host "`n📊 任务10.2: 大数据量处理测试" -ForegroundColor Cyan
    Write-Host "-" * 50 -ForegroundColor Gray
    
    try {
        Write-Host "执行大数据量处理测试..." -ForegroundColor White
        $result = cargo test --test edge_case_test_runner large_data --release 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 大数据量处理测试通过" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ 大数据量处理测试失败" -ForegroundColor Red
            Write-Host $result -ForegroundColor Gray
            return $false
        }
    }
    catch {
        Write-Host "❌ 大数据量处理测试执行失败: $_" -ForegroundColor Red
        return $false
    }
}

# 执行任务10.3: 高并发压力测试
function Invoke-ConcurrencyStressTest {
    Write-Host "`n⚡ 任务10.3: 高并发压力测试" -ForegroundColor Cyan
    Write-Host "-" * 50 -ForegroundColor Gray
    
    # 首先执行Rust并发测试
    try {
        Write-Host "执行Rust高并发测试..." -ForegroundColor White
        $result = cargo test --test edge_case_test_runner concurrency_stress --release 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ Rust高并发测试失败" -ForegroundColor Red
            Write-Host $result -ForegroundColor Gray
            return $false
        }
    }
    catch {
        Write-Host "❌ Rust高并发测试执行失败: $_" -ForegroundColor Red
        return $false
    }
    
    # 然后执行wrk压力测试（如果可用）
    try {
        Write-Host "执行wrk压力测试..." -ForegroundColor White
        $wrkAvailable = Get-Command wrk -ErrorAction SilentlyContinue
        
        if ($wrkAvailable) {
            & "$PSScriptRoot\stress_test.ps1" -ServerUrl $ServerUrl -Duration $Duration -TestType "high"
            Write-Host "✅ 高并发压力测试完成" -ForegroundColor Green
            return $true
        } else {
            Write-Host "⚠️ wrk工具未安装，跳过wrk压力测试" -ForegroundColor Yellow
            Write-Host "✅ Rust高并发测试通过" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ wrk压力测试失败: $_" -ForegroundColor Red
        return $false
    }
}

# 执行任务10.4: 内存泄漏检测
function Invoke-MemoryLeakDetection {
    Write-Host "`n🔍 任务10.4: 内存泄漏检测" -ForegroundColor Cyan
    Write-Host "-" * 50 -ForegroundColor Gray
    
    try {
        Write-Host "执行内存泄漏检测..." -ForegroundColor White
        & "$PSScriptRoot\memory_leak_test.ps1" -ServerUrl $ServerUrl -TestDuration 120  # 2分钟测试
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 内存泄漏检测完成" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ 内存泄漏检测失败" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ 内存泄漏检测执行失败: $_" -ForegroundColor Red
        return $false
    }
}

# 执行任务10.5: 长时间稳定性测试
function Invoke-StabilityTest {
    Write-Host "`n⏰ 任务10.5: 长时间稳定性测试" -ForegroundColor Cyan
    Write-Host "-" * 50 -ForegroundColor Gray
    
    try {
        Write-Host "执行长时间稳定性测试（5分钟）..." -ForegroundColor White
        $result = cargo test --test edge_case_test_runner stability --release 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 长时间稳定性测试通过" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ 长时间稳定性测试失败" -ForegroundColor Red
            Write-Host $result -ForegroundColor Gray
            return $false
        }
    }
    catch {
        Write-Host "❌ 长时间稳定性测试执行失败: $_" -ForegroundColor Red
        return $false
    }
}

# 生成测试报告
function New-EdgeCaseTestReport {
    param([hashtable]$TestResults)
    
    $reportPath = "edge_case_test_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
    
    $passedTests = ($TestResults.Values | Where-Object { $_ -eq $true }).Count
    $totalTests = $TestResults.Count
    $successRate = [math]::Round(($passedTests / $totalTests) * 100, 2)
    
    $report = @"
# Axum项目边缘情况测试报告

## 任务10: 实现边缘情况测试

### 测试配置
- 服务器地址: $ServerUrl
- 测试时间: $(Get-Date)
- 测试持续时间: $Duration 秒

### 测试结果概览
- 总测试数: $totalTests
- 通过测试数: $passedTests
- 成功率: $successRate%

### 详细测试结果

#### 10.1 网络中断恢复测试
状态: $(if ($TestResults["NetworkInterruption"]) { "✅ 通过" } else { "❌ 失败" })
描述: 测试系统在网络中断后的恢复能力

#### 10.2 大数据量处理测试
状态: $(if ($TestResults["LargeData"]) { "✅ 通过" } else { "❌ 失败" })
描述: 测试系统处理大量数据时的性能和稳定性

#### 10.3 高并发压力测试
状态: $(if ($TestResults["ConcurrencyStress"]) { "✅ 通过" } else { "❌ 失败" })
描述: 评估系统在高并发请求下的表现

#### 10.4 内存泄漏检测
状态: $(if ($TestResults["MemoryLeak"]) { "✅ 通过" } else { "❌ 失败" })
描述: 识别并检测潜在的内存泄漏问题

#### 10.5 长时间稳定性测试
状态: $(if ($TestResults["Stability"]) { "✅ 通过" } else { "❌ 失败" })
描述: 验证系统在长时间运行下的稳定性

### 总体评估
$(if ($successRate -eq 100) { "🎉 所有边缘情况测试通过！系统表现优秀。" }
  elseif ($successRate -ge 80) { "✅ 大部分测试通过，系统基本稳定，需要关注失败的测试项。" }
  else { "❌ 多项测试失败，系统需要优化和修复。" })

### 建议
1. 对于失败的测试项，需要进一步调查和修复
2. 持续监控生产环境的性能指标
3. 定期执行边缘情况测试以确保系统稳定性
4. 考虑实施自动化监控和告警机制

生成时间: $(Get-Date)
"@
    
    $report | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "`n📄 边缘情况测试报告已生成: $reportPath" -ForegroundColor Green
}

# 主执行逻辑
if (-not (Test-ServerRunning -Url $ServerUrl)) {
    Write-Host "❌ 服务器检查失败，退出测试" -ForegroundColor Red
    exit 1
}

$testResults = @{}

Write-Host "`n🚀 开始执行边缘情况测试..." -ForegroundColor Green

switch ($TestType.ToLower()) {
    "network" {
        $testResults["NetworkInterruption"] = Invoke-NetworkInterruptionTest
    }
    "data" {
        $testResults["LargeData"] = Invoke-LargeDataTest
    }
    "concurrency" {
        $testResults["ConcurrencyStress"] = Invoke-ConcurrencyStressTest
    }
    "memory" {
        $testResults["MemoryLeak"] = Invoke-MemoryLeakDetection
    }
    "stability" {
        $testResults["Stability"] = Invoke-StabilityTest
    }
    "all" {
        $testResults["NetworkInterruption"] = Invoke-NetworkInterruptionTest
        Start-Sleep -Seconds 3
        $testResults["LargeData"] = Invoke-LargeDataTest
        Start-Sleep -Seconds 3
        $testResults["ConcurrencyStress"] = Invoke-ConcurrencyStressTest
        Start-Sleep -Seconds 3
        $testResults["MemoryLeak"] = Invoke-MemoryLeakDetection
        Start-Sleep -Seconds 3
        $testResults["Stability"] = Invoke-StabilityTest
    }
    default {
        Write-Host "❌ 未知的测试类型: $TestType" -ForegroundColor Red
        Write-Host "支持的测试类型: network, data, concurrency, memory, stability, all" -ForegroundColor Yellow
        exit 1
    }
}

# 生成测试报告
New-EdgeCaseTestReport -TestResults $testResults

# 显示最终结果
$passedTests = ($testResults.Values | Where-Object { $_ -eq $true }).Count
$totalTests = $testResults.Count

Write-Host "`n" + "=" * 60 -ForegroundColor Yellow
if ($passedTests -eq $totalTests) {
    Write-Host "🎉 所有边缘情况测试通过！" -ForegroundColor Green
    Write-Host "✅ 任务10: 边缘情况测试 - 完成" -ForegroundColor Green
} else {
    Write-Host "❌ $($totalTests - $passedTests) 个测试失败" -ForegroundColor Red
    Write-Host "❌ 任务10: 边缘情况测试 - 需要修复" -ForegroundColor Red
}
Write-Host "测试通过率: $([math]::Round(($passedTests / $totalTests) * 100, 2))%" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Yellow
