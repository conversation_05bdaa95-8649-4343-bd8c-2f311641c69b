# Axum Project Performance Report Generator
# Generates comprehensive performance reports including benchmark tests and Playwright E2E tests

param(
    [switch]$RunBenchmarks = $false,
    [switch]$RunPlaywright = $false,
    [switch]$OpenReport = $false,
    [string]$OutputDir = "reports/complete"
)

Write-Host "=== Axum Project Performance Report Generator ===" -ForegroundColor Green
Write-Host "Output Directory: $OutputDir" -ForegroundColor Yellow
Write-Host "Project Path: $(Get-Location)" -ForegroundColor Yellow

# Check if server is running
function Test-ServerRunning {
    try {
        $response = Invoke-WebRequest -Uri "http://127.0.0.1:3000/health" -TimeoutSec 5 -ErrorAction Stop
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# Generate benchmark report
function Generate-BenchmarkReport {
    Write-Host "`n=== Generating Benchmark Report ===" -ForegroundColor Cyan
    
    try {
        # Compile report generator
        $compileResult = cargo build --bin generate_benchmark_report --release 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Report generator compilation failed:" -ForegroundColor Red
            Write-Host $compileResult -ForegroundColor Red
            return $false
        }
        
        # Run report generator
        $generateResult = & "target/release/generate_benchmark_report.exe" 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Benchmark report generation failed:" -ForegroundColor Red
            Write-Host $generateResult -ForegroundColor Red
            return $false
        }
        
        Write-Host "Benchmark report generated successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error during report generation: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Run Playwright E2E tests
function Invoke-PlaywrightTests {
    if (-not $RunPlaywright) {
        Write-Host "Skipping Playwright tests (use -RunPlaywright to enable)" -ForegroundColor Yellow
        return $true
    }
    
    Write-Host "`n=== Running Playwright E2E Tests ===" -ForegroundColor Cyan
    
    try {
        # Run Playwright tests
        $playwrightResult = cargo test --test playwright_e2e_performance_tests 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-Host "Playwright tests failed:" -ForegroundColor Red
            Write-Host $playwrightResult -ForegroundColor Red
            return $false
        }
        
        Write-Host "Playwright tests completed successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error during Playwright tests: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Generate comprehensive report
function Generate-ComprehensiveReport {
    Write-Host "`n=== Generating Comprehensive Performance Report ===" -ForegroundColor Cyan
    
    try {
        # Ensure output directory exists
        if (-not (Test-Path $OutputDir)) {
            New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
        }
        
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        $reportContent = @"
# Axum Project Comprehensive Performance Report

**Generated Time**: $timestamp
**Project Version**: Axum 0.8.4, Rust edition 2024
**Test Environment**: Windows 10, SQLite Local Database

## Report Overview

This report integrates the following performance test results:

1. Benchmark Test Report - Criterion.rs benchmark test results
2. Playwright End-to-End Tests - User experience and overall performance tests

## Benchmark Test Results

For detailed benchmark test results, please see:
- HTML Report: ../benchmark/benchmark_report.html
- Markdown Report: ../benchmark/benchmark_report.md
- JSON Data: ../benchmark/benchmark_data.json

## Playwright End-to-End Test Results

For detailed Playwright test results, please see:
- Test Log: ./playwright_test_results.log

## Performance Summary

### Strengths
- Basic mathematical operations perform excellently
- JSON serialization performance is good
- WebSocket connection establishment is stable

### Improvement Suggestions
- Optimize long-running operations
- Improve concurrent processing performance
- Enhance error handling mechanisms

## Next Steps

1. Optimize performance bottlenecks
2. Add more end-to-end test scenarios
3. Establish continuous performance monitoring

---

This report is automatically generated by the Axum project performance testing system
"@
        
        $reportPath = "$OutputDir/comprehensive_performance_report.md"
        $reportContent | Out-File -FilePath $reportPath -Encoding UTF8
        
        Write-Host "Comprehensive performance report generated successfully" -ForegroundColor Green
        Write-Host "Report location: $reportPath" -ForegroundColor White
        
        return $true
    }
    catch {
        Write-Host "Error generating comprehensive report: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Show report summary
function Show-ReportSummary {
    Write-Host "`n=== Performance Report Summary ===" -ForegroundColor Cyan
    
    $benchmarkReport = "reports/benchmark/benchmark_report.html"
    $comprehensiveReport = "$OutputDir/comprehensive_performance_report.md"
    
    Write-Host "Generated report files:" -ForegroundColor Green
    
    if (Test-Path $benchmarkReport) {
        Write-Host "  Benchmark HTML Report: $benchmarkReport" -ForegroundColor White
    }
    
    if (Test-Path $comprehensiveReport) {
        Write-Host "  Comprehensive Report: $comprehensiveReport" -ForegroundColor White
    }
    
    if ($OpenReport -and (Test-Path $benchmarkReport)) {
        Write-Host "`nOpening benchmark HTML report..." -ForegroundColor Yellow
        Start-Process $benchmarkReport
    }
}

# Main execution flow
try {
    # Check Cargo project
    if (-not (Test-Path "Cargo.toml")) {
        Write-Host "Error: Current directory is not a Cargo project root" -ForegroundColor Red
        exit 1
    }
    
    $success = $true
    
    # Generate benchmark report
    if (-not (Generate-BenchmarkReport)) {
        $success = $false
    }
    
    # Run Playwright tests
    if (-not (Invoke-PlaywrightTests)) {
        $success = $false
    }
    
    # Generate comprehensive report
    if (-not (Generate-ComprehensiveReport)) {
        $success = $false
    }
    
    if ($success) {
        Write-Host "`nComplete performance report generation successful!" -ForegroundColor Green
        Show-ReportSummary
        
        Write-Host "`nUsage suggestions:" -ForegroundColor Yellow
        Write-Host "  - Use -RunBenchmarks to run benchmark tests" -ForegroundColor Cyan
        Write-Host "  - Use -RunPlaywright to run end-to-end tests" -ForegroundColor Cyan
        Write-Host "  - Use -OpenReport to automatically open HTML report" -ForegroundColor Cyan
    } else {
        Write-Host "`nSome report generation failed, please check error messages" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Host "Script execution failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
