# Axum 高并发压力测试执行脚本
# 自动化执行压力测试并生成报告

param(
    [string]$ServerUrl = "http://127.0.0.1:3000",
    [int]$MaxConnections = 10000,
    [int]$TestDurationMinutes = 20,
    [string]$TestType = "all",
    [switch]$SkipServerCheck = $false,
    [switch]$GenerateReport = $true
)

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "🎯 Axum 高并发压力测试自动化脚本" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Yellow
Write-Host "服务器地址: $ServerUrl" -ForegroundColor Cyan
Write-Host "最大连接数: $MaxConnections" -ForegroundColor Cyan
Write-Host "测试持续时间: $TestDurationMinutes 分钟" -ForegroundColor Cyan
Write-Host "测试类型: $TestType" -ForegroundColor Cyan

# 检查Rust和Cargo是否安装
function Test-RustInstallation {
    try {
        $null = Get-Command cargo -ErrorAction Stop
        $cargoVersion = cargo --version
        Write-Host "✅ Cargo已安装: $cargoVersion" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ 错误: Cargo未安装或不在PATH中" -ForegroundColor Red
        Write-Host "请安装Rust: https://rustup.rs/" -ForegroundColor Yellow
        return $false
    }
}

# 检查服务器是否运行
function Test-ServerRunning {
    param([string]$Url)
    
    if ($SkipServerCheck) {
        Write-Host "⏭️ 跳过服务器检查" -ForegroundColor Yellow
        return $true
    }
    
    try {
        $healthEndpoints = @(
            "$Url/api/health",
            "$Url/api/performance/health",
            "$Url/metrics"
        )
        
        foreach ($endpoint in $healthEndpoints) {
            try {
                $response = Invoke-WebRequest -Uri $endpoint -Method GET -TimeoutSec 10
                if ($response.StatusCode -eq 200) {
                    Write-Host "✅ 服务器运行正常: $endpoint" -ForegroundColor Green
                    return $true
                }
            }
            catch {
                Write-Host "⚠️ 端点检查失败: $endpoint" -ForegroundColor Yellow
            }
        }
        
        Write-Host "❌ 服务器未运行或无法访问: $Url" -ForegroundColor Red
        Write-Host "请确保服务器在 $Url 上运行" -ForegroundColor Yellow
        return $false
    }
    catch {
        Write-Host "❌ 服务器连接失败: $_" -ForegroundColor Red
        return $false
    }
}

# 执行基准测试
function Invoke-BenchmarkTests {
    Write-Host "`n⚡ 执行Criterion.rs基准测试..." -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray
    
    try {
        # 执行压力测试基准
        Write-Host "🔥 执行压力测试基准..." -ForegroundColor Yellow
        $benchOutput = cargo bench --bench stress_test_benchmarks 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 压力测试基准完成" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 压力测试基准部分失败" -ForegroundColor Yellow
        }
        
        # 执行综合性能基准
        Write-Host "📊 执行综合性能基准..." -ForegroundColor Yellow
        $comprehensiveOutput = cargo bench --bench comprehensive_performance_benchmarks 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 综合性能基准完成" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 综合性能基准部分失败" -ForegroundColor Yellow
        }
        
        return @{
            StressBench = $benchOutput
            ComprehensiveBench = $comprehensiveOutput
        }
    }
    catch {
        Write-Host "❌ 基准测试执行失败: $_" -ForegroundColor Red
        return $null
    }
}

# 执行集成压力测试
function Invoke-IntegrationStressTests {
    Write-Host "`n🚀 执行集成压力测试..." -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray
    
    try {
        # 执行压力测试用例
        Write-Host "🔥 执行压力测试用例..." -ForegroundColor Yellow
        $testOutput = cargo test --test stress_test_cases -- --ignored 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 集成压力测试完成" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 集成压力测试部分失败" -ForegroundColor Yellow
        }
        
        return $testOutput
    }
    catch {
        Write-Host "❌ 集成压力测试执行失败: $_" -ForegroundColor Red
        return $null
    }
}

# 执行自动化压力测试脚本
function Invoke-AutomatedStressTest {
    Write-Host "`n🤖 执行自动化压力测试脚本..." -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray

    try {
        $automatedOutput = cargo run --bin stress_test_runner 2>&1

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 自动化压力测试完成" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 自动化压力测试部分失败" -ForegroundColor Yellow
        }

        return $automatedOutput
    }
    catch {
        Write-Host "❌ 自动化压力测试执行失败: $_" -ForegroundColor Red
        return $null
    }
}

# 执行综合测试报告生成
function Invoke-ComprehensiveReport {
    Write-Host "`n📋 生成综合测试报告..." -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray

    try {
        $reportOutput = cargo run --bin comprehensive_test_report 2>&1

        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 综合测试报告生成完成" -ForegroundColor Green
        } else {
            Write-Host "⚠️ 综合测试报告生成部分失败" -ForegroundColor Yellow
        }

        return $reportOutput
    }
    catch {
        Write-Host "❌ 综合测试报告生成失败: $_" -ForegroundColor Red
        return $null
    }
}

# 生成汇总报告
function New-SummaryReport {
    param(
        [hashtable]$BenchmarkResults,
        [string]$IntegrationResults,
        [string]$AutomatedResults,
        [string]$ComprehensiveResults
    )
    
    if (-not $GenerateReport) {
        Write-Host "⏭️ 跳过报告生成" -ForegroundColor Yellow
        return
    }
    
    Write-Host "`n📊 生成汇总报告..." -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray
    
    $reportTime = Get-Date -Format "yyyyMMdd_HHmmss"
    $reportDir = "reports/stress_test"
    $reportFile = "$reportDir/stress_test_summary_$reportTime.md"
    
    # 确保报告目录存在
    if (-not (Test-Path $reportDir)) {
        New-Item -ItemType Directory -Path $reportDir -Force | Out-Null
    }
    
    $reportContent = @"
# Axum 高并发压力测试汇总报告

## 测试概览

- **测试时间**: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")
- **服务器地址**: $ServerUrl
- **最大并发连接数**: $MaxConnections
- **测试持续时间**: $TestDurationMinutes 分钟
- **测试类型**: $TestType

## 基准测试结果

### 压力测试基准

``````
$($BenchmarkResults.StressBench)
``````

### 综合性能基准

``````
$($BenchmarkResults.ComprehensiveBench)
``````

## 集成测试结果

``````
$IntegrationResults
``````

## 自动化测试结果

``````
$AutomatedResults
``````

## 综合测试报告

``````
$ComprehensiveResults
``````

## 测试结论

### 性能表现
- 待分析基准测试结果
- 待分析集成测试结果
- 待分析自动化测试结果

### 发现的问题
- 待分析测试输出

### 优化建议
- 根据测试结果提出优化建议

---

*报告生成时间: $(Get-Date -Format "yyyy-MM-dd HH:mm:ss")*
"@
    
    try {
        $reportContent | Out-File -FilePath $reportFile -Encoding UTF8
        Write-Host "📄 汇总报告已生成: $reportFile" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 报告生成失败: $_" -ForegroundColor Red
    }
}

# 主执行逻辑
try {
    # 检查Rust安装
    if (-not (Test-RustInstallation)) {
        exit 1
    }
    
    # 检查服务器状态
    if (-not (Test-ServerRunning -Url $ServerUrl)) {
        exit 1
    }
    
    Write-Host "`n🚀 开始执行压力测试..." -ForegroundColor Green
    $startTime = Get-Date
    
    # 根据测试类型执行相应测试
    $benchmarkResults = $null
    $integrationResults = $null
    $automatedResults = $null
    $comprehensiveResults = $null

    switch ($TestType.ToLower()) {
        "benchmark" {
            $benchmarkResults = Invoke-BenchmarkTests
        }
        "integration" {
            $integrationResults = Invoke-IntegrationStressTests
        }
        "automated" {
            $automatedResults = Invoke-AutomatedStressTest
        }
        "comprehensive" {
            $comprehensiveResults = Invoke-ComprehensiveReport
        }
        "all" {
            $benchmarkResults = Invoke-BenchmarkTests
            $integrationResults = Invoke-IntegrationStressTests
            $automatedResults = Invoke-AutomatedStressTest
            $comprehensiveResults = Invoke-ComprehensiveReport
        }
        default {
            Write-Host "❌ 未知的测试类型: $TestType" -ForegroundColor Red
            Write-Host "支持的测试类型: benchmark, integration, automated, comprehensive, all" -ForegroundColor Yellow
            exit 1
        }
    }
    
    # 生成汇总报告
    New-SummaryReport -BenchmarkResults $benchmarkResults -IntegrationResults $integrationResults -AutomatedResults $automatedResults -ComprehensiveResults $comprehensiveResults
    
    $endTime = Get-Date
    $duration = $endTime - $startTime
    
    Write-Host "`n🎉 压力测试执行完成！" -ForegroundColor Green
    Write-Host "总耗时: $($duration.TotalMinutes.ToString("F2")) 分钟" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Yellow
}
catch {
    Write-Host "`n❌ 压力测试执行失败: $_" -ForegroundColor Red
    exit 1
}
