# WSL2 + Podman 设置脚本
# 替代Docker Desktop的完整解决方案
# 适用于Windows 10 x86 64位系统

param(
    [switch]$CheckOnly,
    [switch]$InstallPodman,
    [switch]$ConfigureNetwork,
    [switch]$StartContainers
)

Write-Host "=== WSL2 + Podman 设置脚本 ===" -ForegroundColor Green
Write-Host "当前时间: $(Get-Date)" -ForegroundColor Gray

# 检查WSL2状态
function Test-WSL2Status {
    Write-Host "`n1. 检查WSL2状态..." -ForegroundColor Yellow
    
    try {
        $wslList = wsl --list --verbose
        Write-Host "WSL2发行版状态:" -ForegroundColor Cyan
        $wslList | ForEach-Object { Write-Host "  $_" }
        
        # 检查Ubuntu是否运行
        $ubuntuStatus = wsl -d Ubuntu -- echo "Ubuntu WSL2 运行正常"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Ubuntu WSL2 运行正常" -ForegroundColor Green
            return $true
        } else {
            Write-Host "✗ Ubuntu WSL2 未运行或未安装" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ WSL2 检查失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 检查网络配置
function Test-NetworkConfiguration {
    Write-Host "`n2. 检查网络配置..." -ForegroundColor Yellow
    
    try {
        # 检查WSL网络适配器
        $wslAdapter = Get-NetAdapter | Where-Object {$_.Name -like "*WSL*"}
        if ($wslAdapter) {
            Write-Host "✓ WSL网络适配器已启用: $($wslAdapter.Name)" -ForegroundColor Green
            
            # 获取WSL网络IP
            $wslIP = Get-NetIPAddress -InterfaceAlias $wslAdapter.Name -AddressFamily IPv4 | Select-Object -First 1
            Write-Host "  Windows主机WSL IP: $($wslIP.IPAddress)" -ForegroundColor Cyan
            
            # 检查WSL内部IP
            $ubuntuIP = wsl -d Ubuntu -- hostname -I
            Write-Host "  Ubuntu WSL2 IP: $ubuntuIP" -ForegroundColor Cyan
            
            return $true
        } else {
            Write-Host "✗ WSL网络适配器未找到" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ 网络配置检查失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 检查Podman安装状态
function Test-PodmanInstallation {
    Write-Host "`n3. 检查Podman安装状态..." -ForegroundColor Yellow
    
    try {
        # 检查podman
        $podmanPath = wsl -d Ubuntu -- which podman 2>$null
        if ($LASTEXITCODE -eq 0) {
            $podmanVersion = wsl -d Ubuntu -- podman --version
            Write-Host "✓ Podman已安装: $podmanVersion" -ForegroundColor Green
        } else {
            Write-Host "✗ Podman未安装" -ForegroundColor Red
            return $false
        }
        
        # 检查podman-compose
        $composeePath = wsl -d Ubuntu -- which podman-compose 2>$null
        if ($LASTEXITCODE -eq 0) {
            $composeVersion = wsl -d Ubuntu -- podman-compose --version
            Write-Host "✓ Podman-compose已安装: $composeVersion" -ForegroundColor Green
        } else {
            Write-Host "✗ Podman-compose未安装" -ForegroundColor Red
            return $false
        }
        
        return $true
    } catch {
        Write-Host "✗ Podman检查失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 安装Podman (如果需要)
function Install-PodmanInWSL {
    Write-Host "`n4. 在WSL2中安装Podman..." -ForegroundColor Yellow
    
    try {
        # 更新包管理器
        Write-Host "更新包管理器..." -ForegroundColor Cyan
        wsl -d Ubuntu -- sudo apt update
        
        # 安装必要的依赖
        Write-Host "安装依赖包..." -ForegroundColor Cyan
        wsl -d Ubuntu -- sudo apt install -y curl wget gnupg2 software-properties-common
        
        # 添加Podman仓库
        Write-Host "添加Podman仓库..." -ForegroundColor Cyan
        wsl -d Ubuntu -- bash -c "echo 'deb https://download.opensuse.org/repositories/devel:/kubic:/libcontainers:/stable/xUbuntu_22.04/ /' | sudo tee /etc/apt/sources.list.d/devel:kubic:libcontainers:stable.list"
        wsl -d Ubuntu -- curl -L https://download.opensuse.org/repositories/devel:kubic:libcontainers:stable/xUbuntu_22.04/Release.key | sudo apt-key add -
        
        # 更新并安装Podman
        Write-Host "安装Podman..." -ForegroundColor Cyan
        wsl -d Ubuntu -- sudo apt update
        wsl -d Ubuntu -- sudo apt install -y podman
        
        # 安装podman-compose
        Write-Host "安装podman-compose..." -ForegroundColor Cyan
        wsl -d Ubuntu -- sudo pip3 install podman-compose
        
        Write-Host "✓ Podman安装完成" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "✗ Podman安装失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 配置Podman网络
function Set-PodmanNetworkConfiguration {
    Write-Host "`n5. 配置Podman网络..." -ForegroundColor Yellow
    
    try {
        # 启用Podman socket服务
        Write-Host "启用Podman socket服务..." -ForegroundColor Cyan
        wsl -d Ubuntu -- systemctl --user enable podman.socket
        wsl -d Ubuntu -- systemctl --user start podman.socket
        
        # 创建用户配置目录
        wsl -d Ubuntu -- mkdir -p ~/.config/containers
        
        # 配置容器网络
        $networkConfig = @"
[containers]
netns="host"
userns="host"
ipcns="host"
utsns="host"
cgroupns="host"
cgroups="disabled"
log_driver = "k8s-file"
[engine]
cgroup_manager = "cgroupfs"
events_logger="file"
runtime="crun"
"@
        
        # 写入配置文件
        $networkConfig | wsl -d Ubuntu -- tee ~/.config/containers/containers.conf
        
        Write-Host "✓ Podman网络配置完成" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "✗ Podman网络配置失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 启动容器服务
function Start-ContainerServices {
    Write-Host "`n6. 启动容器服务..." -ForegroundColor Yellow
    
    try {
        # 切换到项目目录
        $projectPath = "/mnt/d/ceshi/ceshi/axum-tutorial"
        
        # 停止现有容器
        Write-Host "停止现有容器..." -ForegroundColor Cyan
        wsl -d Ubuntu -- bash -c "cd $projectPath && podman-compose -f podman-compose.yml down" 2>$null
        
        # 启动新容器
        Write-Host "启动容器服务..." -ForegroundColor Cyan
        wsl -d Ubuntu -- bash -c "cd $projectPath && podman-compose -f podman-compose.yml up -d"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ 容器服务启动成功" -ForegroundColor Green
            
            # 显示容器状态
            Write-Host "`n容器状态:" -ForegroundColor Cyan
            wsl -d Ubuntu -- bash -c "cd $projectPath && podman-compose -f podman-compose.yml ps"
            
            return $true
        } else {
            Write-Host "✗ 容器服务启动失败" -ForegroundColor Red
            return $false
        }
    } catch {
        Write-Host "✗ 容器服务启动失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 测试连接
function Test-ContainerConnectivity {
    Write-Host "`n7. 测试容器连接..." -ForegroundColor Yellow
    
    try {
        # 等待服务启动
        Start-Sleep -Seconds 10
        
        # 测试PostgreSQL连接
        Write-Host "测试PostgreSQL连接..." -ForegroundColor Cyan
        $pgTest = Test-NetConnection -ComputerName localhost -Port 5432 -WarningAction SilentlyContinue
        if ($pgTest.TcpTestSucceeded) {
            Write-Host "✓ PostgreSQL (5432) 连接成功" -ForegroundColor Green
        } else {
            Write-Host "✗ PostgreSQL (5432) 连接失败" -ForegroundColor Red
        }
        
        # 测试DragonflyDB连接
        Write-Host "测试DragonflyDB连接..." -ForegroundColor Cyan
        $redisTest = Test-NetConnection -ComputerName localhost -Port 6379 -WarningAction SilentlyContinue
        if ($redisTest.TcpTestSucceeded) {
            Write-Host "✓ DragonflyDB (6379) 连接成功" -ForegroundColor Green
        } else {
            Write-Host "✗ DragonflyDB (6379) 连接失败" -ForegroundColor Red
        }
        
        # 测试Prometheus连接
        Write-Host "测试Prometheus连接..." -ForegroundColor Cyan
        $promTest = Test-NetConnection -ComputerName localhost -Port 9090 -WarningAction SilentlyContinue
        if ($promTest.TcpTestSucceeded) {
            Write-Host "✓ Prometheus (9090) 连接成功" -ForegroundColor Green
        } else {
            Write-Host "✗ Prometheus (9090) 连接失败" -ForegroundColor Red
        }
        
        return $true
    } catch {
        Write-Host "✗ 连接测试失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 主执行逻辑
Write-Host "开始WSL2 + Podman设置..." -ForegroundColor Green

# 检查WSL2状态
$wsl2OK = Test-WSL2Status
if (-not $wsl2OK) {
    Write-Host "`n请先安装并启动WSL2 Ubuntu" -ForegroundColor Red
    exit 1
}

# 检查网络配置
$networkOK = Test-NetworkConfiguration

# 检查Podman安装
$podmanOK = Test-PodmanInstallation

# 根据参数执行相应操作
if ($CheckOnly) {
    Write-Host "`n=== 检查完成 ===" -ForegroundColor Green
    Write-Host "WSL2状态: $(if($wsl2OK){'✓'}else{'✗'})" -ForegroundColor $(if($wsl2OK){'Green'}else{'Red'})
    Write-Host "网络配置: $(if($networkOK){'✓'}else{'✗'})" -ForegroundColor $(if($networkOK){'Green'}else{'Red'})
    Write-Host "Podman安装: $(if($podmanOK){'✓'}else{'✗'})" -ForegroundColor $(if($podmanOK){'Green'}else{'Red'})
    exit 0
}

if ($InstallPodman -and -not $podmanOK) {
    Install-PodmanInWSL
    Set-PodmanNetworkConfiguration
}

if ($ConfigureNetwork) {
    Set-PodmanNetworkConfiguration
}

if ($StartContainers) {
    Start-ContainerServices
    Test-ContainerConnectivity
}

# 如果没有指定参数，执行完整设置
if (-not ($CheckOnly -or $InstallPodman -or $ConfigureNetwork -or $StartContainers)) {
    if (-not $podmanOK) {
        Install-PodmanInWSL
        Set-PodmanNetworkConfiguration
    }
    Start-ContainerServices
    Test-ContainerConnectivity
}

Write-Host "`n=== WSL2 + Podman 设置完成 ===" -ForegroundColor Green
Write-Host "数据库连接信息:" -ForegroundColor Cyan
Write-Host "  PostgreSQL: localhost:5432" -ForegroundColor White
Write-Host "  DragonflyDB: localhost:6379" -ForegroundColor White
Write-Host "  Prometheus: localhost:9090" -ForegroundColor White
Write-Host "  Grafana: localhost:3001" -ForegroundColor White
