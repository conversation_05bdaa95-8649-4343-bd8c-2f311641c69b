# Smart Environment Setup Script
# Auto-detect and configure the best development environment

param(
    [ValidateSet("auto", "wsl2_podman", "docker_desktop", "native")]
    [string]$Environment = "auto",
    [switch]$Force
)

Write-Host "Smart Environment Setup Tool" -ForegroundColor Green

# Detection functions
function Test-WSL2 {
    try {
        $wslStatus = wsl --status 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

function Test-DockerDesktop {
    try {
        $dockerStatus = docker version 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

function Test-Podman {
    try {
        $podmanStatus = podman version 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

function Get-WSL2IP {
    try {
        $ip = (wsl hostname -I).Trim()
        if ($ip -and $ip -ne "") {
            return $ip
        }
    } catch {}
    return $null
}

# Environment detection
if ($Environment -eq "auto") {
    Write-Host "Auto-detecting environment..." -ForegroundColor Yellow
    
    if (Test-WSL2 -and Test-Podman) {
        $Environment = "wsl2_podman"
        Write-Host "Detected: WSL2 + Podman environment" -ForegroundColor Green
    } elseif (Test-DockerDesktop) {
        $Environment = "docker_desktop"
        Write-Host "Detected: Docker Desktop environment" -ForegroundColor Green
    } else {
        $Environment = "native"
        Write-Host "Detected: Native environment" -ForegroundColor Green
    }
}

# Configure environment
$envFile = ".env"

# Backup existing config
if ((Test-Path $envFile) -and -not $Force) {
    $backup = ".env.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $envFile $backup
    Write-Host "Backed up existing config: $backup" -ForegroundColor Yellow
}

# Generate configuration
Write-Host "Configuring environment: $Environment" -ForegroundColor Cyan

switch ($Environment) {
    "wsl2_podman" {
        $wsl2IP = Get-WSL2IP
        if ($wsl2IP) {
            $cacheUrl = "redis://:dragonfly_secure_password_2025@${wsl2IP}:6379"
            Write-Host "WSL2 IP address: $wsl2IP" -ForegroundColor Green
        } else {
            $cacheUrl = "redis://:dragonfly_secure_password_2025@localhost:6379"
            Write-Host "WARNING: Cannot get WSL2 IP, using localhost" -ForegroundColor Yellow
        }
    }
    "docker_desktop" {
        $cacheUrl = "redis://:dragonfly_secure_password_2025@localhost:6379"
        Write-Host "Using Docker Desktop network" -ForegroundColor Green
    }
    "native" {
        $cacheUrl = "redis://:dragonfly_secure_password_2025@localhost:6379"
        Write-Host "Using native network configuration" -ForegroundColor Green
    }
}

# Generate .env file
$envContent = @"
# Axum Tutorial Environment Configuration
# Auto-generated: $(Get-Date)
# Environment type: $Environment

# HTTP Server Configuration
HTTP_ADDR=127.0.0.1:3000

# Database Configuration - PostgreSQL 17
DATABASE_URL=postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial

# PostgreSQL Connection Pool
MAX_CONNECTIONS=1000
MIN_CONNECTIONS=10
CONNECTION_TIMEOUT=30
IDLE_TIMEOUT=600
ACQUIRE_TIMEOUT=30

# DragonflyDB Cache Configuration ($Environment)
CACHE_URL=$cacheUrl
CACHE_DEFAULT_TTL=3600
CACHE_KEY_PREFIX=axum_tutorial:

# JWT Secret (Development)
JWT_SECRET=your-secret-key-change-in-production

# Runtime Environment
ENVIRONMENT=development

# Log Level
RUST_LOG=info
"@

Set-Content -Path $envFile -Value $envContent -Encoding UTF8
Write-Host "Environment configuration generated: $envFile" -ForegroundColor Green

# Verify configuration
Write-Host "`nVerifying configuration..." -ForegroundColor Yellow

if ($Environment -eq "wsl2_podman") {
    Write-Host "WSL2 + Podman Environment Checklist:" -ForegroundColor Cyan
    $wsl2Status = if (Test-WSL2) { "Running" } else { "Not Running" }
    $podmanStatus = if (Test-Podman) { "Available" } else { "Not Available" }
    Write-Host "• WSL2 Status: $wsl2Status" -ForegroundColor White
    Write-Host "• Podman Status: $podmanStatus" -ForegroundColor White
    Write-Host "• DragonflyDB Container: Run 'podman ps' to check" -ForegroundColor White
} elseif ($Environment -eq "docker_desktop") {
    Write-Host "Docker Desktop Environment Checklist:" -ForegroundColor Cyan
    $dockerStatus = if (Test-DockerDesktop) { "Running" } else { "Not Running" }
    Write-Host "• Docker Status: $dockerStatus" -ForegroundColor White
    Write-Host "• Container Status: Run 'docker ps' to check" -ForegroundColor White
}

Write-Host "`nNext Steps:" -ForegroundColor Green
Write-Host "1. Start container services (if not already running)" -ForegroundColor White
Write-Host "2. Run: cargo run -p axum-server" -ForegroundColor White
Write-Host "3. Visit: http://127.0.0.1:3000" -ForegroundColor White

Write-Host "`nTips:" -ForegroundColor Cyan
Write-Host "• To reconfigure: powershell -ExecutionPolicy Bypass -File scripts/setup_env_simple.ps1 -Force" -ForegroundColor Gray
Write-Host "• To update WSL2 IP: powershell -ExecutionPolicy Bypass -File scripts/fix_wsl2_ip.ps1" -ForegroundColor Gray
