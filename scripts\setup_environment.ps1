# 智能环境设置脚本
# 自动检测并配置最佳的开发环境

param(
    [ValidateSet("auto", "wsl2_podman", "docker_desktop", "native")]
    [string]$Environment = "auto",
    [switch]$Force
)

Write-Host "🚀 Axum Tutorial 智能环境设置工具" -ForegroundColor Green

# 检测函数
function Test-WSL2 {
    try {
        $wslStatus = wsl --status 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

function Test-DockerDesktop {
    try {
        $dockerStatus = docker version 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

function Test-Podman {
    try {
        $podmanStatus = podman version 2>$null
        return $LASTEXITCODE -eq 0
    } catch {
        return $false
    }
}

function Get-WSL2IP {
    try {
        $ip = (wsl hostname -I).Trim()
        if ($ip -and $ip -ne "") {
            return $ip
        }
    } catch {}
    return $null
}

# 环境检测
if ($Environment -eq "auto") {
    Write-Host "🔍 自动检测环境..." -ForegroundColor Yellow
    
    if (Test-WSL2 -and Test-Podman) {
        $Environment = "wsl2_podman"
        Write-Host "✅ 检测到: WSL2 + Podman 环境" -ForegroundColor Green
    } elseif (Test-DockerDesktop) {
        $Environment = "docker_desktop"
        Write-Host "✅ 检测到: Docker Desktop 环境" -ForegroundColor Green
    } else {
        $Environment = "native"
        Write-Host "✅ 检测到: 原生环境" -ForegroundColor Green
    }
}

# 配置环境
$envFile = ".env"
$templateFile = ".env.template"

# 备份现有配置
if ((Test-Path $envFile) -and -not $Force) {
    $backup = ".env.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $envFile $backup
    Write-Host "📁 已备份现有配置: $backup" -ForegroundColor Yellow
}

# 生成配置
Write-Host "⚙️ 配置环境: $Environment" -ForegroundColor Cyan

switch ($Environment) {
    "wsl2_podman" {
        $wsl2IP = Get-WSL2IP
        if ($wsl2IP) {
            $cacheUrl = "redis://:dragonfly_secure_password_2025@${wsl2IP}:6379"
            Write-Host "🔗 WSL2 IP地址: $wsl2IP" -ForegroundColor Green
        } else {
            $cacheUrl = "redis://:dragonfly_secure_password_2025@localhost:6379"
            Write-Host "⚠️ 无法获取WSL2 IP，使用localhost" -ForegroundColor Yellow
        }
    }
    "docker_desktop" {
        $cacheUrl = "redis://:dragonfly_secure_password_2025@localhost:6379"
        Write-Host "🐳 使用Docker Desktop网络" -ForegroundColor Green
    }
    "native" {
        $cacheUrl = "redis://:dragonfly_secure_password_2025@localhost:6379"
        Write-Host "💻 使用原生网络配置" -ForegroundColor Green
    }
}

# 生成.env文件
$envContent = @"
# Axum Tutorial 环境配置文件
# 自动生成时间: $(Get-Date)
# 环境类型: $Environment

# HTTP 服务器配置
HTTP_ADDR=127.0.0.1:3000

# 数据库配置 - PostgreSQL 17
DATABASE_URL=postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial

# PostgreSQL连接池配置
MAX_CONNECTIONS=1000
MIN_CONNECTIONS=10
CONNECTION_TIMEOUT=30
IDLE_TIMEOUT=600
ACQUIRE_TIMEOUT=30

# DragonflyDB缓存配置 ($Environment)
CACHE_URL=$cacheUrl
CACHE_DEFAULT_TTL=3600
CACHE_KEY_PREFIX=axum_tutorial:

# JWT 密钥配置（开发环境）
JWT_SECRET=your-secret-key-change-in-production

# 运行环境
ENVIRONMENT=development

# 日志级别
RUST_LOG=info
"@

Set-Content -Path $envFile -Value $envContent -Encoding UTF8
Write-Host "✅ 环境配置已生成: $envFile" -ForegroundColor Green

# 验证配置
Write-Host "`n🔍 验证配置..." -ForegroundColor Yellow

if ($Environment -eq "wsl2_podman") {
    Write-Host "📋 WSL2 + Podman 环境检查清单:" -ForegroundColor Cyan
    Write-Host "• WSL2状态: $(if (Test-WSL2) { '✅ 运行中' } else { '❌ 未运行' })" -ForegroundColor White
    Write-Host "• Podman状态: $(if (Test-Podman) { '✅ 可用' } else { '❌ 不可用' })" -ForegroundColor White
    Write-Host "• DragonflyDB容器: 请运行 'podman ps' 检查" -ForegroundColor White
} elseif ($Environment -eq "docker_desktop") {
    Write-Host "📋 Docker Desktop 环境检查清单:" -ForegroundColor Cyan
    Write-Host "• Docker状态: $(if (Test-DockerDesktop) { '✅ 运行中' } else { '❌ 未运行' })" -ForegroundColor White
    Write-Host "• 容器状态: 请运行 'docker ps' 检查" -ForegroundColor White
}

Write-Host "`n🎯 下一步操作:" -ForegroundColor Green
Write-Host "1. 启动容器服务 (如果尚未启动)" -ForegroundColor White
Write-Host "2. 运行: cargo run -p axum-server" -ForegroundColor White
Write-Host "3. 访问: http://127.0.0.1:3000" -ForegroundColor White

Write-Host "`n💡 提示:" -ForegroundColor Cyan
Write-Host "• 如需重新配置: powershell -ExecutionPolicy Bypass -File scripts/setup_environment.ps1 -Force" -ForegroundColor Gray
Write-Host "• 如需更新WSL2 IP: powershell -ExecutionPolicy Bypass -File scripts/fix_wsl2_ip.ps1" -ForegroundColor Gray
