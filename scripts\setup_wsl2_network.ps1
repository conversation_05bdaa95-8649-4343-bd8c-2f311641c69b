# WSL2网络优化配置脚本
# 使用镜像网络模式解决localhost连接问题

param(
    [switch]$Apply,
    [switch]$Restore
)

$wslConfigPath = "$env:USERPROFILE\.wslconfig"
$backupPath = "$env:USERPROFILE\.wslconfig.backup"

Write-Host "🔧 WSL2网络优化配置工具" -ForegroundColor Green

if ($Restore) {
    # 恢复原始配置
    if (Test-Path $backupPath) {
        Copy-Item $backupPath $wslConfigPath -Force
        Write-Host "✅ 已恢复原始WSL2配置" -ForegroundColor Green
    } else {
        if (Test-Path $wslConfigPath) {
            Remove-Item $wslConfigPath -Force
        }
        Write-Host "✅ 已删除WSL2配置文件" -ForegroundColor Green
    }
    Write-Host "⚠️ 请重启WSL2以应用更改: wsl --shutdown" -ForegroundColor Yellow
    exit 0
}

# 备份现有配置
if (Test-Path $wslConfigPath) {
    Copy-Item $wslConfigPath $backupPath -Force
    Write-Host "📁 已备份现有配置到: $backupPath" -ForegroundColor Yellow
}

# 创建优化的WSL2配置
$wslConfig = @"
[wsl2]
# 网络配置 - 使用镜像模式解决localhost连接问题
networkingMode=mirrored
dnsTunneling=true
firewall=true
autoProxy=true

# 性能优化
memory=4GB
processors=2
swap=2GB

# 实验性功能
[experimental]
sparseVhd=true
autoMemoryReclaim=gradual
"@

if ($Apply) {
    # 应用配置
    Set-Content -Path $wslConfigPath -Value $wslConfig -Encoding UTF8
    Write-Host "✅ WSL2网络优化配置已应用" -ForegroundColor Green
    Write-Host "📝 配置文件位置: $wslConfigPath" -ForegroundColor Cyan
    
    Write-Host "`n🔄 配置内容:" -ForegroundColor Cyan
    Write-Host $wslConfig -ForegroundColor Gray
    
    Write-Host "`n⚠️ 重要: 请执行以下步骤应用配置:" -ForegroundColor Yellow
    Write-Host "1. 关闭WSL2: wsl --shutdown" -ForegroundColor White
    Write-Host "2. 等待10秒" -ForegroundColor White
    Write-Host "3. 重启WSL2: wsl" -ForegroundColor White
    Write-Host "4. 重启容器: podman restart axum_dragonflydb" -ForegroundColor White
    Write-Host "5. 更新.env文件使用localhost" -ForegroundColor White
} else {
    # 预览模式
    Write-Host "📋 将要应用的WSL2配置:" -ForegroundColor Cyan
    Write-Host $wslConfig -ForegroundColor Gray
    
    Write-Host "`n💡 优势:" -ForegroundColor Green
    Write-Host "• 解决localhost连接问题" -ForegroundColor White
    Write-Host "• 提升网络性能" -ForegroundColor White
    Write-Host "• 简化开发环境配置" -ForegroundColor White
    Write-Host "• 无需手动更新IP地址" -ForegroundColor White
    
    Write-Host "`n🚀 要应用此配置，请运行:" -ForegroundColor Yellow
    Write-Host "powershell -ExecutionPolicy Bypass -File scripts/setup_wsl2_network.ps1 -Apply" -ForegroundColor Cyan
}
