# Simple server health test
param(
    [string]$BaseUrl = "http://127.0.0.1:3000"
)

Write-Host "Testing server health at: $BaseUrl"

try {
    $response = Invoke-WebRequest -Uri "$BaseUrl/health" -TimeoutSec 10 -ErrorAction Stop
    
    if ($response.StatusCode -eq 200) {
        Write-Host "SUCCESS: Server is running - Status: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "Response: $($response.Content.Substring(0, [Math]::Min(200, $response.Content.Length)))" -ForegroundColor Gray
        exit 0
    } else {
        Write-Host "WARNING: Unexpected status code: $($response.StatusCode)" -ForegroundColor Yellow
        exit 1
    }
}
catch {
    Write-Host "ERROR: Cannot connect to server - $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please make sure the server is running: cargo run -p server" -ForegroundColor Yellow
    exit 2
}
