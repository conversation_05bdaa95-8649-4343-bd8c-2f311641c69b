# 简化的Podman Compose配置验证脚本
# 验证podman-compose.yml语法和基本配置

param(
    [switch]$Verbose
)

$ErrorActionPreference = "Stop"

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "Cyan" = [ConsoleColor]::<PERSON><PERSON>
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

function Test-ConfigurationFiles {
    Write-ColorOutput "检查配置文件..." "Blue"
    
    $requiredFiles = @(
        "podman-compose.yml",
        "config/postgresql.conf",
        "config/pg_hba.conf", 
        "scripts/init-db.sql",
        "monitoring/prometheus.yml"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (Test-Path $file) {
            Write-ColorOutput "  ✅ $file" "Green"
        } else {
            $missingFiles += $file
            Write-ColorOutput "  ❌ $file (缺失)" "Red"
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-ColorOutput "❌ 缺少必需的配置文件" "Red"
        return $false
    }
    
    Write-ColorOutput "✅ 所有配置文件完整" "Green"
    return $true
}

function Test-YamlSyntax {
    Write-ColorOutput "验证YAML语法..." "Blue"
    
    try {
        $validateResult = wsl -d Ubuntu bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman-compose config" 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "❌ YAML语法错误:" "Red"
            Write-ColorOutput $validateResult "Red"
            return $false
        }
        
        Write-ColorOutput "✅ YAML语法正确" "Green"
        
        if ($Verbose) {
            Write-ColorOutput "配置验证输出:" "Cyan"
            Write-ColorOutput $validateResult "White"
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "❌ YAML验证失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

function Test-PortAvailability {
    Write-ColorOutput "验证端口可用性..." "Blue"
    
    $ports = @(5432, 6379, 6380, 9090, 3001, 9121)
    $busyPorts = @()
    
    foreach ($port in $ports) {
        $connection = Test-NetConnection -ComputerName "localhost" -Port $port -InformationLevel Quiet -WarningAction SilentlyContinue
        
        if ($connection) {
            $busyPorts += $port
            Write-ColorOutput "  ⚠️  端口 $port 已被占用" "Yellow"
        } else {
            Write-ColorOutput "  ✅ 端口 $port 可用" "Green"
        }
    }
    
    if ($busyPorts.Count -gt 0) {
        Write-ColorOutput "⚠️  以下端口已被占用: $($busyPorts -join ', ')" "Yellow"
        Write-ColorOutput "   这可能导致服务启动冲突" "Yellow"
        return $false
    }
    
    Write-ColorOutput "✅ 所有端口可用" "Green"
    return $true
}

function Test-DataDirectories {
    Write-ColorOutput "验证数据目录..." "Blue"
    
    $dataDirectories = @("data", "data/postgres", "data/dragonflydb", "data/prometheus", "data/grafana")
    
    foreach ($dir in $dataDirectories) {
        if (-not (Test-Path $dir)) {
            try {
                New-Item -ItemType Directory -Path $dir -Force | Out-Null
                Write-ColorOutput "  ✅ 创建数据目录: $dir" "Green"
            }
            catch {
                Write-ColorOutput "  ❌ 无法创建数据目录: $dir" "Red"
                return $false
            }
        } else {
            Write-ColorOutput "  ✅ 数据目录存在: $dir" "Green"
        }
    }
    
    Write-ColorOutput "✅ 数据目录验证完成" "Green"
    return $true
}

function Invoke-MainValidation {
    Write-ColorOutput "🎯 开始Podman Compose配置验证" "Cyan"
    Write-ColorOutput "=" * 50 "Cyan"
    
    $validationResults = @{
        "配置文件完整性" = $false
        "数据目录" = $false
        "YAML语法" = $false
        "端口可用性" = $false
    }
    
    $validationResults["配置文件完整性"] = Test-ConfigurationFiles
    $validationResults["数据目录"] = Test-DataDirectories
    $validationResults["YAML语法"] = Test-YamlSyntax
    $validationResults["端口可用性"] = Test-PortAvailability
    
    return $validationResults
}

function Show-ValidationResults {
    param([hashtable]$Results)
    
    Write-ColorOutput "`n📊 验证结果摘要" "Cyan"
    Write-ColorOutput "=" * 50 "Cyan"
    
    $passedCount = 0
    $totalCount = $Results.Count
    
    foreach ($validation in $Results.GetEnumerator()) {
        $status = if ($validation.Value) { "✅ 通过" } else { "❌ 失败" }
        $color = if ($validation.Value) { "Green" } else { "Red" }
        
        Write-ColorOutput "$($validation.Key): $status" $color
        
        if ($validation.Value) { $passedCount++ }
    }
    
    Write-ColorOutput "`n总计: $passedCount/$totalCount 验证通过" "Cyan"
    
    if ($passedCount -eq $totalCount) {
        Write-ColorOutput "🎉 所有验证通过！配置就绪" "Green"
        Write-ColorOutput "💡 可以使用以下命令启动服务:" "Cyan"
        Write-ColorOutput "   wsl -d Ubuntu bash -c 'cd /mnt/d/ceshi/ceshi/axum-tutorial && podman-compose up -d'" "White"
        return $true
    } else {
        Write-ColorOutput "⚠️  部分验证失败，请修复后重试" "Yellow"
        return $false
    }
}

try {
    $results = Invoke-MainValidation
    $success = Show-ValidationResults -Results $results
    
    if ($success) {
        exit 0
    } else {
        exit 1
    }
}
catch {
    Write-ColorOutput "💥 验证过程中发生错误: $($_.Exception.Message)" "Red"
    exit 1
}
