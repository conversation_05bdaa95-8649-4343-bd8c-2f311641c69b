# 启动WSL2 + Podman容器服务
# 简化的容器管理脚本

param(
    [switch]$Stop,
    [switch]$Restart,
    [switch]$Status,
    [switch]$Logs
)

$projectPath = "/mnt/d/ceshi/ceshi/axum-tutorial"

Write-Host "=== Axum项目容器管理 ===" -ForegroundColor Green

if ($Stop) {
    Write-Host "停止所有容器..." -ForegroundColor Yellow
    wsl -d Ubuntu -- bash -c "cd $projectPath && podman-compose -f podman-compose.yml down"
    Write-Host "✓ 容器已停止" -ForegroundColor Green
    exit 0
}

if ($Status) {
    Write-Host "容器状态:" -ForegroundColor Yellow
    wsl -d Ubuntu -- bash -c "cd $projectPath && podman-compose -f podman-compose.yml ps"
    
    Write-Host "`n端口连接测试:" -ForegroundColor Yellow
    $ports = @(5432, 6379, 9090, 3001, 9121)
    foreach ($port in $ports) {
        $test = Test-NetConnection -ComputerName localhost -Port $port -WarningAction SilentlyContinue
        if ($test.TcpTestSucceeded) {
            Write-Host "  端口 $port : ✓" -ForegroundColor Green
        } else {
            Write-Host "  端口 $port : ✗" -ForegroundColor Red
        }
    }
    exit 0
}

if ($Logs) {
    Write-Host "显示容器日志..." -ForegroundColor Yellow
    wsl -d Ubuntu -- bash -c "cd $projectPath && podman-compose -f podman-compose.yml logs -f"
    exit 0
}

if ($Restart) {
    Write-Host "重启容器服务..." -ForegroundColor Yellow
    wsl -d Ubuntu -- bash -c "cd $projectPath && podman-compose -f podman-compose.yml down"
    Start-Sleep -Seconds 3
}

# 默认启动容器
Write-Host "启动容器服务..." -ForegroundColor Yellow
wsl -d Ubuntu -- bash -c "cd $projectPath && podman-compose -f podman-compose.yml up -d"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ 容器启动成功" -ForegroundColor Green
    
    Write-Host "`n等待服务就绪..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # 显示状态
    Write-Host "`n容器状态:" -ForegroundColor Cyan
    wsl -d Ubuntu -- bash -c "cd $projectPath && podman-compose -f podman-compose.yml ps"
    
    Write-Host "`n服务地址:" -ForegroundColor Cyan
    Write-Host "  PostgreSQL: localhost:5432" -ForegroundColor White
    Write-Host "  DragonflyDB: localhost:6379" -ForegroundColor White
    Write-Host "  Prometheus: localhost:9090" -ForegroundColor White
    Write-Host "  Grafana: localhost:3001 (admin/grafana_admin_2025)" -ForegroundColor White
    Write-Host "  Redis Exporter: localhost:9121" -ForegroundColor White
} else {
    Write-Host "✗ 容器启动失败" -ForegroundColor Red
    Write-Host "查看日志: .\scripts\start-containers.ps1 -Logs" -ForegroundColor Yellow
}
