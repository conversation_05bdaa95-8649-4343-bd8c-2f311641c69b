# Prometheus + Grafana 监控系统启动脚本
# 基于Context7 MCP最佳实践

param(
    [switch]$Stop,
    [switch]$Restart,
    [switch]$Status,
    [switch]$Logs
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot

Write-Host "🔍 Prometheus + Grafana 监控系统管理脚本" -ForegroundColor Cyan
Write-Host "📁 项目根目录: $ProjectRoot" -ForegroundColor Gray

# 检查Docker Compose文件
$DockerComposePath = Join-Path $ProjectRoot "docker-compose.yml"
if (-not (Test-Path $DockerComposePath)) {
    Write-Host "❌ 错误: 找不到 docker-compose.yml 文件" -ForegroundColor Red
    exit 1
}

# 检查监控配置目录
$MonitoringPath = Join-Path $ProjectRoot "monitoring"
if (-not (Test-Path $MonitoringPath)) {
    Write-Host "❌ 错误: 找不到 monitoring 配置目录" -ForegroundColor Red
    exit 1
}

# 切换到项目根目录
Set-Location $ProjectRoot

function Show-Status {
    Write-Host "📊 检查监控服务状态..." -ForegroundColor Yellow

    try {
        # 检查容器状态（使用WSL2和Podman）
        Write-Host "`n🐳 Podman Compose 服务状态:" -ForegroundColor Cyan
        wsl -e bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman-compose ps"

        Write-Host "`n🌐 服务访问地址:" -ForegroundColor Green
        Write-Host "  • Prometheus: http://localhost:9090" -ForegroundColor White
        Write-Host "  • Grafana: http://localhost:3001 (admin/admin123)" -ForegroundColor White
        Write-Host "  • Axum应用: http://localhost:3000" -ForegroundColor White
        Write-Host "  • Axum指标: http://localhost:3000/metrics" -ForegroundColor White

    } catch {
        Write-Host "❌ 获取状态失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Start-Monitoring {
    Write-Host "🚀 启动 Prometheus + Grafana 监控系统..." -ForegroundColor Green

    try {
        # 启动监控服务（使用WSL2和Podman）
        Write-Host "📦 启动 Prometheus 和 Grafana 容器..." -ForegroundColor Yellow
        wsl -e bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman-compose up -d prometheus grafana"

        # 等待服务启动
        Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
        Start-Sleep -Seconds 15

        # 检查服务状态
        Show-Status

        Write-Host "`n✅ 监控系统启动完成!" -ForegroundColor Green
        Write-Host "🔗 请访问以下地址:" -ForegroundColor Cyan
        Write-Host "  • Prometheus: http://localhost:9090" -ForegroundColor White
        Write-Host "  • Grafana: http://localhost:3001" -ForegroundColor White
        Write-Host "  • 默认登录: admin / admin123" -ForegroundColor Gray

    } catch {
        Write-Host "❌ 启动失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

function Stop-Monitoring {
    Write-Host "🛑 停止 Prometheus + Grafana 监控系统..." -ForegroundColor Yellow

    try {
        # 停止监控服务（使用WSL2和Podman）
        wsl -e bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman-compose stop prometheus grafana"

        Write-Host "✅ 监控系统已停止" -ForegroundColor Green

    } catch {
        Write-Host "❌ 停止失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

function Restart-Monitoring {
    Write-Host "🔄 重启 Prometheus + Grafana 监控系统..." -ForegroundColor Yellow
    Stop-Monitoring
    Start-Sleep -Seconds 5
    Start-Monitoring
}

function Show-Logs {
    Write-Host "📋 显示监控服务日志..." -ForegroundColor Yellow

    try {
        Write-Host "🔍 Prometheus 日志:" -ForegroundColor Cyan
        wsl -e bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman-compose logs --tail=20 prometheus"

        Write-Host "`n🔍 Grafana 日志:" -ForegroundColor Cyan
        wsl -e bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman-compose logs --tail=20 grafana"

    } catch {
        Write-Host "❌ 获取日志失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 主逻辑
if ($Stop) {
    Stop-Monitoring
} elseif ($Restart) {
    Restart-Monitoring
} elseif ($Status) {
    Show-Status
} elseif ($Logs) {
    Show-Logs
} else {
    Start-Monitoring
}

Write-Host "`n🎯 脚本执行完成!" -ForegroundColor Green
