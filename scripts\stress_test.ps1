# Axum服务器压力测试脚本
# 使用wrk2工具进行高并发压力测试

param(
    [string]$ServerUrl = "http://127.0.0.1:3000",
    [int]$Duration = 30,
    [string]$TestType = "all"
)

Write-Host "🚀 Axum服务器压力测试开始..." -ForegroundColor Green
Write-Host "服务器地址: $ServerUrl" -ForegroundColor Cyan
Write-Host "测试持续时间: $Duration 秒" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Yellow

# 检查wrk是否安装
function Test-WrkInstalled {
    try {
        $null = Get-Command wrk -ErrorAction Stop
        return $true
    }
    catch {
        Write-Host "❌ 错误: wrk工具未安装" -ForegroundColor Red
        Write-Host "请安装wrk工具: https://github.com/wg/wrk" -ForegroundColor Yellow
        return $false
    }
}

# 检查服务器是否运行
function Test-ServerRunning {
    param([string]$Url)
    
    try {
        $response = Invoke-WebRequest -Uri "$Url/api/tasks" -Method GET -TimeoutSec 5
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 服务器运行正常" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ 服务器未运行或无法访问: $Url" -ForegroundColor Red
        Write-Host "请确保服务器在 $Url 上运行" -ForegroundColor Yellow
        return $false
    }
    return $false
}

# 执行基础压力测试
function Invoke-BasicStressTest {
    param([string]$Url, [int]$Duration)
    
    Write-Host "`n📊 基础压力测试 (10并发, 100连接)" -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray
    
    $cmd = "wrk -t10 -c100 -d${Duration}s --latency $Url/api/tasks"
    Write-Host "执行命令: $cmd" -ForegroundColor Gray
    
    try {
        Invoke-Expression $cmd
        Write-Host "✅ 基础压力测试完成" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 基础压力测试失败: $_" -ForegroundColor Red
    }
}

# 执行高并发压力测试
function Invoke-HighConcurrencyTest {
    param([string]$Url, [int]$Duration)
    
    Write-Host "`n⚡ 高并发压力测试 (20并发, 400连接)" -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray
    
    $cmd = "wrk -t20 -c400 -d${Duration}s --latency $Url/api/tasks"
    Write-Host "执行命令: $cmd" -ForegroundColor Gray
    
    try {
        Invoke-Expression $cmd
        Write-Host "✅ 高并发压力测试完成" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 高并发压力测试失败: $_" -ForegroundColor Red
    }
}

# 执行极限压力测试
function Invoke-ExtremeStressTest {
    param([string]$Url, [int]$Duration)
    
    Write-Host "`n🔥 极限压力测试 (50并发, 1000连接)" -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray
    
    $cmd = "wrk -t50 -c1000 -d${Duration}s --latency $Url/api/tasks"
    Write-Host "执行命令: $cmd" -ForegroundColor Gray
    
    try {
        Invoke-Expression $cmd
        Write-Host "✅ 极限压力测试完成" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ 极限压力测试失败: $_" -ForegroundColor Red
    }
}

# 执行POST请求压力测试
function Invoke-PostStressTest {
    param([string]$Url, [int]$Duration)
    
    Write-Host "`n📝 POST请求压力测试" -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray
    
    # 创建POST请求的Lua脚本
    $luaScript = @"
wrk.method = "POST"
wrk.body   = '{"title":"压力测试任务","description":"这是一个压力测试任务","priority":"medium","status":"pending"}'
wrk.headers["Content-Type"] = "application/json"
"@
    
    $scriptPath = "post_test.lua"
    $luaScript | Out-File -FilePath $scriptPath -Encoding UTF8
    
    $cmd = "wrk -t12 -c200 -d${Duration}s --script=$scriptPath --latency $Url/api/tasks"
    Write-Host "执行命令: $cmd" -ForegroundColor Gray
    
    try {
        Invoke-Expression $cmd
        Remove-Item $scriptPath -ErrorAction SilentlyContinue
        Write-Host "✅ POST请求压力测试完成" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ POST请求压力测试失败: $_" -ForegroundColor Red
        Remove-Item $scriptPath -ErrorAction SilentlyContinue
    }
}

# 执行WebSocket压力测试（使用自定义脚本）
function Invoke-WebSocketStressTest {
    param([string]$Url, [int]$Duration)
    
    Write-Host "`n🔌 WebSocket压力测试" -ForegroundColor Cyan
    Write-Host "-" * 40 -ForegroundColor Gray
    
    # 这里可以调用专门的WebSocket压力测试工具
    # 或者使用Node.js脚本进行WebSocket测试
    Write-Host "WebSocket压力测试需要专门的工具，跳过..." -ForegroundColor Yellow
}

# 生成测试报告
function New-TestReport {
    param([string]$Url, [int]$Duration)
    
    $reportPath = "stress_test_report_$(Get-Date -Format 'yyyyMMdd_HHmmss').md"
    
    $report = @"
# Axum服务器压力测试报告

## 测试配置
- 服务器地址: $Url
- 测试时间: $(Get-Date)
- 测试持续时间: $Duration 秒

## 测试结果
详细结果请查看上方控制台输出。

## 性能指标分析
- 吞吐量 (Requests/sec): 查看wrk输出
- 延迟分布: 查看wrk --latency输出
- 错误率: 查看wrk输出中的错误统计

## 建议
1. 监控服务器资源使用情况
2. 根据测试结果调整服务器配置
3. 优化数据库查询性能
4. 考虑使用连接池和缓存

生成时间: $(Get-Date)
"@
    
    $report | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "`n📄 测试报告已生成: $reportPath" -ForegroundColor Green
}

# 主执行逻辑
if (-not (Test-WrkInstalled)) {
    exit 1
}

if (-not (Test-ServerRunning -Url $ServerUrl)) {
    exit 1
}

Write-Host "`n开始执行压力测试..." -ForegroundColor Green

switch ($TestType.ToLower()) {
    "basic" {
        Invoke-BasicStressTest -Url $ServerUrl -Duration $Duration
    }
    "high" {
        Invoke-HighConcurrencyTest -Url $ServerUrl -Duration $Duration
    }
    "extreme" {
        Invoke-ExtremeStressTest -Url $ServerUrl -Duration $Duration
    }
    "post" {
        Invoke-PostStressTest -Url $ServerUrl -Duration $Duration
    }
    "websocket" {
        Invoke-WebSocketStressTest -Url $ServerUrl -Duration $Duration
    }
    "all" {
        Invoke-BasicStressTest -Url $ServerUrl -Duration $Duration
        Start-Sleep -Seconds 5
        Invoke-HighConcurrencyTest -Url $ServerUrl -Duration $Duration
        Start-Sleep -Seconds 5
        Invoke-PostStressTest -Url $ServerUrl -Duration $Duration
        Start-Sleep -Seconds 5
        Invoke-WebSocketStressTest -Url $ServerUrl -Duration $Duration
    }
    default {
        Write-Host "❌ 未知的测试类型: $TestType" -ForegroundColor Red
        Write-Host "支持的测试类型: basic, high, extreme, post, websocket, all" -ForegroundColor Yellow
        exit 1
    }
}

New-TestReport -Url $ServerUrl -Duration $Duration

Write-Host "`n🎉 压力测试完成！" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Yellow
