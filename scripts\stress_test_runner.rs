// 压力测试自动化执行脚本
// 使用Criterion.rs或类似工具进行自动化压力测试

use std::fs;
use std::process::Command;
use std::time::Duration;
// use std::path::Path; // 暂时未使用
use chrono::{DateTime, Utc};
use reqwest::Client;
use serde_json::json;

/// 压力测试配置
#[derive(Debug, Clone)]
struct StressTestConfig {
    pub server_url: String,
    pub max_concurrent_connections: usize,
    pub test_duration_minutes: u64,
    pub warmup_duration_seconds: u64,
    pub output_dir: String,
}

impl Default for StressTestConfig {
    fn default() -> Self {
        Self {
            server_url: "http://127.0.0.1:3000".to_string(),
            max_concurrent_connections: 10000,
            test_duration_minutes: 20,
            warmup_duration_seconds: 60,
            output_dir: "reports/stress_test".to_string(),
        }
    }
}

/// 测试结果汇总
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
struct TestSummary {
    pub test_name: String,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub duration_minutes: f64,
    pub total_tests: usize,
    pub passed_tests: usize,
    pub failed_tests: usize,
    pub benchmark_results: Vec<BenchmarkResult>,
}

#[derive(Debug, Clone)]
struct BenchmarkResult {
    pub name: String,
    pub avg_time_ms: f64,
    pub p95_time_ms: f64,
    pub throughput_ops_per_sec: f64,
    pub success_rate: f64,
}

/// 检查服务器是否运行
async fn check_server_health(url: &str) -> Result<bool, Box<dyn std::error::Error>> {
    let client = Client::builder().timeout(Duration::from_secs(10)).build()?;

    let health_endpoints = vec![
        format!("{}/api/health", url),
        format!("{}/api/performance/health", url),
        format!("{}/metrics", url),
    ];

    for endpoint in health_endpoints {
        match client.get(&endpoint).send().await {
            Ok(response) if response.status().is_success() => {
                println!("✅ 服务器健康检查通过: {}", endpoint);
                return Ok(true);
            }
            Ok(response) => {
                println!(
                    "⚠️ 端点响应异常: {} - 状态码: {}",
                    endpoint,
                    response.status()
                );
            }
            Err(e) => {
                println!("❌ 端点连接失败: {} - 错误: {}", endpoint, e);
            }
        }
    }

    Ok(false)
}

/// 执行基准测试
fn run_benchmark(benchmark_name: &str) -> Result<String, Box<dyn std::error::Error>> {
    println!("🚀 执行基准测试: {}", benchmark_name);

    let output = Command::new("cargo")
        .args(&["bench", "--bench", benchmark_name])
        .output()?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        return Err(format!("基准测试失败: {}", stderr).into());
    }

    let stdout = String::from_utf8_lossy(&output.stdout);
    Ok(stdout.to_string())
}

/// 执行压力测试
fn run_stress_test() -> Result<String, Box<dyn std::error::Error>> {
    println!("🔥 执行压力测试...");

    let output = Command::new("cargo")
        .args(&["test", "--test", "stress_test_cases", "--", "--ignored"])
        .output()?;

    if !output.status.success() {
        let stderr = String::from_utf8_lossy(&output.stderr);
        println!("⚠️ 压力测试部分失败: {}", stderr);
    }

    let stdout = String::from_utf8_lossy(&output.stdout);
    Ok(stdout.to_string())
}

/// 生成测试报告
fn generate_test_report(
    config: &StressTestConfig,
    summary: &TestSummary,
    benchmark_output: &str,
    stress_test_output: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    // 确保输出目录存在
    fs::create_dir_all(&config.output_dir)?;

    let report_time = Utc::now().format("%Y%m%d_%H%M%S");
    let report_file = format!(
        "{}/stress_test_report_{}.md",
        config.output_dir, report_time
    );

    let report_content = format!(
        r#"# Axum 高并发压力测试报告

## 测试概览

- **测试时间**: {} 至 {}
- **测试持续时间**: {:.2} 分钟
- **服务器地址**: {}
- **最大并发连接数**: {}
- **总测试数**: {}
- **通过测试数**: {}
- **失败测试数**: {}

## 测试配置

```json
{}
```

## 基准测试结果

### Criterion.rs 基准测试输出

```
{}
```

## 压力测试结果

### 集成测试输出

```
{}
```

## 性能指标汇总

### 连接性能
- **平均连接建立时间**: 待分析
- **P95连接建立时间**: 待分析
- **连接成功率**: 待分析

### 消息传输性能
- **平均消息延迟**: 待分析
- **P95消息延迟**: 待分析
- **消息传输成功率**: 待分析

### 系统资源使用
- **峰值CPU使用率**: 待监控
- **峰值内存使用**: 待监控
- **网络I/O**: 待监控

## 测试结论

### 性能表现
1. **连接处理能力**: 系统能够处理的最大并发连接数
2. **消息吞吐量**: 系统每秒能够处理的消息数量
3. **响应时间**: 在高负载下的响应时间表现
4. **稳定性**: 长时间高负载下的系统稳定性

### 发现的问题
1. 待分析基准测试结果
2. 待分析压力测试结果
3. 待分析系统监控数据

### 优化建议
1. **连接池优化**: 根据测试结果调整数据库连接池大小
2. **内存管理**: 优化WebSocket连接的内存使用
3. **并发控制**: 调整Tokio运行时配置
4. **缓存策略**: 优化DragonflyDB缓存使用

## 附录

### 测试环境
- **操作系统**: Windows 10 + WSL2
- **Rust版本**: 2024 Edition
- **Axum版本**: 0.8.4
- **Tokio版本**: 1.45.1
- **数据库**: PostgreSQL 17
- **缓存**: DragonflyDB

### 测试工具
- **基准测试**: Criterion.rs
- **压力测试**: 自定义Rust测试
- **监控工具**: Prometheus + Grafana

---

*报告生成时间: {}*
"#,
        summary.start_time.format("%Y-%m-%d %H:%M:%S UTC"),
        summary.end_time.format("%Y-%m-%d %H:%M:%S UTC"),
        summary.duration_minutes,
        config.server_url,
        config.max_concurrent_connections,
        summary.total_tests,
        summary.passed_tests,
        summary.failed_tests,
        serde_json::to_string_pretty(&json!({
            "server_url": config.server_url,
            "max_concurrent_connections": config.max_concurrent_connections,
            "test_duration_minutes": config.test_duration_minutes,
            "warmup_duration_seconds": config.warmup_duration_seconds,
            "output_dir": config.output_dir
        }))?,
        benchmark_output,
        stress_test_output,
        Utc::now().format("%Y-%m-%d %H:%M:%S UTC")
    );

    fs::write(&report_file, report_content)?;
    println!("📄 测试报告已生成: {}", report_file);

    Ok(())
}

/// 主函数
#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🎯 Axum 高并发压力测试自动化脚本");
    println!("{}", "=".repeat(60));

    let config = StressTestConfig::default();
    let start_time = Utc::now();

    // 1. 检查服务器健康状态
    println!("\n📋 步骤1: 检查服务器健康状态");
    if !check_server_health(&config.server_url).await? {
        eprintln!("❌ 服务器未运行或不健康，请先启动服务器");
        std::process::exit(1);
    }

    // 2. 预热阶段
    println!(
        "\n🔥 步骤2: 系统预热 ({} 秒)",
        config.warmup_duration_seconds
    );
    tokio::time::sleep(Duration::from_secs(config.warmup_duration_seconds)).await;

    // 3. 执行基准测试
    println!("\n⚡ 步骤3: 执行Criterion.rs基准测试");
    let benchmark_output = match run_benchmark("stress_test_benchmarks") {
        Ok(output) => {
            println!("✅ 基准测试完成");
            output
        }
        Err(e) => {
            println!("⚠️ 基准测试失败: {}", e);
            format!("基准测试失败: {}", e)
        }
    };

    // 4. 执行压力测试
    println!("\n🚀 步骤4: 执行集成压力测试");
    let stress_test_output = match run_stress_test() {
        Ok(output) => {
            println!("✅ 压力测试完成");
            output
        }
        Err(e) => {
            println!("⚠️ 压力测试失败: {}", e);
            format!("压力测试失败: {}", e)
        }
    };

    // 5. 生成测试报告
    println!("\n📊 步骤5: 生成测试报告");
    let end_time = Utc::now();
    let duration = end_time.signed_duration_since(start_time);

    let summary = TestSummary {
        test_name: "Axum高并发压力测试".to_string(),
        start_time,
        end_time,
        duration_minutes: (duration.num_milliseconds() as f64) / 60000.0,
        total_tests: 2,            // 基准测试 + 压力测试
        passed_tests: 1,           // 根据实际结果调整
        failed_tests: 1,           // 根据实际结果调整
        benchmark_results: vec![], // 简化版本
    };

    generate_test_report(&config, &summary, &benchmark_output, &stress_test_output)?;

    println!("\n🎉 压力测试自动化执行完成！");
    println!("总耗时: {:.2} 分钟", summary.duration_minutes);
    println!("报告目录: {}", config.output_dir);

    Ok(())
}
