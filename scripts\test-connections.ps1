# 测试WSL2 + Podman容器连接
# 验证所有服务是否正常工作

Write-Host "=== 容器连接测试 ===" -ForegroundColor Green

# 测试PostgreSQL连接
Write-Host "`n1. 测试PostgreSQL连接..." -ForegroundColor Yellow
try {
    $pgResult = wsl -d Ubuntu -- bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman exec axum_postgres_17 psql -U axum_user -d axum_tutorial -c 'SELECT version();'"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ PostgreSQL连接成功" -ForegroundColor Green
        Write-Host "  版本信息: $($pgResult -split "`n" | Select-Object -Skip 2 -First 1)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ PostgreSQL连接失败" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ PostgreSQL测试异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试DragonflyDB连接
Write-Host "`n2. 测试DragonflyDB连接..." -ForegroundColor Yellow
try {
    $redisResult = wsl -d Ubuntu -- bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman exec axum_dragonflydb redis-cli -a dragonfly_secure_password_2025 ping"
    if ($LASTEXITCODE -eq 0 -and $redisResult -match "PONG") {
        Write-Host "✓ DragonflyDB连接成功" -ForegroundColor Green
        
        # 获取DragonflyDB信息
        $infoResult = wsl -d Ubuntu -- bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman exec axum_dragonflydb redis-cli -a dragonfly_secure_password_2025 info server"
        $version = ($infoResult -split "`n" | Where-Object { $_ -match "redis_version" }) -replace "redis_version:", ""
        Write-Host "  版本信息: DragonflyDB $version" -ForegroundColor Cyan
    } else {
        Write-Host "✗ DragonflyDB连接失败" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ DragonflyDB测试异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试Prometheus连接
Write-Host "`n3. 测试Prometheus连接..." -ForegroundColor Yellow
try {
    $promTest = Invoke-WebRequest -Uri "http://localhost:9090/api/v1/status/config" -UseBasicParsing -TimeoutSec 10
    if ($promTest.StatusCode -eq 200) {
        Write-Host "✓ Prometheus连接成功" -ForegroundColor Green
        Write-Host "  状态码: $($promTest.StatusCode)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Prometheus连接失败 (状态码: $($promTest.StatusCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Prometheus连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试Grafana连接
Write-Host "`n4. 测试Grafana连接..." -ForegroundColor Yellow
try {
    $grafanaTest = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 10
    if ($grafanaTest.StatusCode -eq 200) {
        Write-Host "✓ Grafana连接成功" -ForegroundColor Green
        Write-Host "  状态码: $($grafanaTest.StatusCode)" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Grafana连接失败 (状态码: $($grafanaTest.StatusCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Grafana连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试Redis Exporter连接
Write-Host "`n5. 测试Redis Exporter连接..." -ForegroundColor Yellow
try {
    $exporterTest = Invoke-WebRequest -Uri "http://localhost:9121/metrics" -UseBasicParsing -TimeoutSec 10
    if ($exporterTest.StatusCode -eq 200) {
        Write-Host "✓ Redis Exporter连接成功" -ForegroundColor Green
        Write-Host "  状态码: $($exporterTest.StatusCode)" -ForegroundColor Cyan
        
        # 检查指标数量
        $metricsCount = ($exporterTest.Content -split "`n" | Where-Object { $_ -match "^redis_" }).Count
        Write-Host "  导出指标数量: $metricsCount" -ForegroundColor Cyan
    } else {
        Write-Host "✗ Redis Exporter连接失败 (状态码: $($exporterTest.StatusCode))" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Redis Exporter连接失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试数据库写入操作
Write-Host "`n6. 测试数据库写入操作..." -ForegroundColor Yellow
try {
    # 创建测试表
    $createTable = wsl -d Ubuntu -- bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman exec axum_postgres_17 psql -U axum_user -d axum_tutorial -c 'CREATE TABLE IF NOT EXISTS test_connection (id SERIAL PRIMARY KEY, message TEXT, created_at TIMESTAMP DEFAULT NOW());'"
    
    # 插入测试数据
    $insertData = wsl -d Ubuntu -- bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman exec axum_postgres_17 psql -U axum_user -d axum_tutorial -c 'INSERT INTO test_connection (message) VALUES (''WSL2 + Podman 连接测试'');'"
    
    # 查询测试数据
    $selectData = wsl -d Ubuntu -- bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman exec axum_postgres_17 psql -U axum_user -d axum_tutorial -c 'SELECT COUNT(*) FROM test_connection;'"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 数据库写入测试成功" -ForegroundColor Green
        $count = ($selectData -split "`n" | Select-Object -Skip 2 -First 1).Trim()
        Write-Host "  测试记录数量: $count" -ForegroundColor Cyan
    } else {
        Write-Host "✗ 数据库写入测试失败" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 数据库写入测试异常: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试缓存写入操作
Write-Host "`n7. 测试缓存写入操作..." -ForegroundColor Yellow
try {
    # 设置缓存值
    $setCache = wsl -d Ubuntu -- bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman exec axum_dragonflydb redis-cli -a dragonfly_secure_password_2025 set test:connection 'WSL2+Podman缓存测试'"
    
    # 获取缓存值
    $getCache = wsl -d Ubuntu -- bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman exec axum_dragonflydb redis-cli -a dragonfly_secure_password_2025 get test:connection"
    
    if ($LASTEXITCODE -eq 0 -and $getCache -match "WSL2") {
        Write-Host "✓ 缓存写入测试成功" -ForegroundColor Green
        Write-Host "  缓存值: $getCache" -ForegroundColor Cyan
    } else {
        Write-Host "✗ 缓存写入测试失败" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 缓存写入测试异常: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 连接测试完成 ===" -ForegroundColor Green
Write-Host "所有服务已就绪，可以启动Axum服务器:" -ForegroundColor Cyan
Write-Host "  cargo run -p axum-server" -ForegroundColor White
Write-Host "`n监控面板地址:" -ForegroundColor Cyan
Write-Host "  Grafana: http://localhost:3001 (admin/grafana_admin_2025)" -ForegroundColor White
Write-Host "  Prometheus: http://localhost:9090" -ForegroundColor White
