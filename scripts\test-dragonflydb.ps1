# DragonflyDB 连接和性能测试脚本
# 用于验证DragonflyDB容器配置和性能

param(
    [string]$Host = "127.0.0.1",
    [int]$Port = 6379,
    [string]$Password = "dragonfly_secure_password_2025",
    [switch]$PerformanceTest = $false,
    [switch]$Verbose = $false
)

Write-Host "=== DragonflyDB 连接和配置测试 ===" -ForegroundColor Green
Write-Host "目标服务器: $Host`:$Port" -ForegroundColor Yellow

# 检查redis-cli是否可用
$redisCliPath = Get-Command redis-cli -ErrorAction SilentlyContinue
if (-not $redisCliPath) {
    Write-Host "错误: 未找到redis-cli命令。请确保Redis客户端已安装。" -ForegroundColor Red
    Write-Host "可以通过以下方式安装:" -ForegroundColor Yellow
    Write-Host "1. 下载Redis for Windows" -ForegroundColor Yellow
    Write-Host "2. 或使用WSL2中的redis-cli" -ForegroundColor Yellow
    exit 1
}

Write-Host "✓ 找到redis-cli: $($redisCliPath.Source)" -ForegroundColor Green

# 基础连接测试
Write-Host "`n=== 基础连接测试 ===" -ForegroundColor Cyan
try {
    $pingResult = & redis-cli -h $Host -p $Port -a $Password ping 2>&1
    if ($pingResult -eq "PONG") {
        Write-Host "✓ DragonflyDB连接成功" -ForegroundColor Green
    } else {
        Write-Host "✗ DragonflyDB连接失败: $pingResult" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ 连接异常: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 获取服务器信息
Write-Host "`n=== 服务器信息 ===" -ForegroundColor Cyan
$serverInfo = & redis-cli -h $Host -p $Port -a $Password info server 2>&1
if ($Verbose) {
    Write-Host $serverInfo
} else {
    $serverInfo | Select-String "redis_version|dragonfly_version|os|arch|process_id|tcp_port|uptime_in_seconds" | ForEach-Object {
        Write-Host $_.Line -ForegroundColor Yellow
    }
}

# 内存信息
Write-Host "`n=== 内存使用情况 ===" -ForegroundColor Cyan
$memoryInfo = & redis-cli -h $Host -p $Port -a $Password info memory 2>&1
$memoryInfo | Select-String "used_memory_human|used_memory_peak_human|maxmemory_human|mem_fragmentation_ratio" | ForEach-Object {
    Write-Host $_.Line -ForegroundColor Yellow
}

# 客户端连接信息
Write-Host "`n=== 客户端连接信息 ===" -ForegroundColor Cyan
$clientsInfo = & redis-cli -h $Host -p $Port -a $Password info clients 2>&1
$clientsInfo | Select-String "connected_clients|client_recent_max_input_buffer|client_recent_max_output_buffer" | ForEach-Object {
    Write-Host $_.Line -ForegroundColor Yellow
}

# 统计信息
Write-Host "`n=== 操作统计 ===" -ForegroundColor Cyan
$statsInfo = & redis-cli -h $Host -p $Port -a $Password info stats 2>&1
$statsInfo | Select-String "total_commands_processed|instantaneous_ops_per_sec|total_net_input_bytes|total_net_output_bytes" | ForEach-Object {
    Write-Host $_.Line -ForegroundColor Yellow
}

# 配置验证
Write-Host "`n=== 关键配置验证 ===" -ForegroundColor Cyan
$configs = @(
    "maxmemory",
    "maxclients", 
    "tcp-keepalive",
    "databases"
)

foreach ($config in $configs) {
    try {
        $value = & redis-cli -h $Host -p $Port -a $Password config get $config 2>&1
        if ($value -is [array] -and $value.Length -ge 2) {
            Write-Host "✓ $($value[0]): $($value[1])" -ForegroundColor Green
        }
    } catch {
        Write-Host "✗ 无法获取配置 $config" -ForegroundColor Red
    }
}

# 基础功能测试
Write-Host "`n=== 基础功能测试 ===" -ForegroundColor Cyan

# 测试SET/GET
$testKey = "test:dragonflydb:$(Get-Date -Format 'yyyyMMddHHmmss')"
$testValue = "DragonflyDB企业级配置测试"

try {
    & redis-cli -h $Host -p $Port -a $Password set $testKey $testValue | Out-Null
    $retrievedValue = & redis-cli -h $Host -p $Port -a $Password get $testKey
    
    if ($retrievedValue -eq $testValue) {
        Write-Host "✓ SET/GET操作正常" -ForegroundColor Green
    } else {
        Write-Host "✗ SET/GET操作异常" -ForegroundColor Red
    }
    
    # 清理测试数据
    & redis-cli -h $Host -p $Port -a $Password del $testKey | Out-Null
} catch {
    Write-Host "✗ SET/GET测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 测试Hash操作
try {
    $hashKey = "test:hash:$(Get-Date -Format 'yyyyMMddHHmmss')"
    & redis-cli -h $Host -p $Port -a $Password hset $hashKey field1 value1 field2 value2 | Out-Null
    $hashLen = & redis-cli -h $Host -p $Port -a $Password hlen $hashKey
    
    if ($hashLen -eq "2") {
        Write-Host "✓ Hash操作正常" -ForegroundColor Green
    } else {
        Write-Host "✗ Hash操作异常" -ForegroundColor Red
    }
    
    # 清理测试数据
    & redis-cli -h $Host -p $Port -a $Password del $hashKey | Out-Null
} catch {
    Write-Host "✗ Hash测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 性能测试 (可选)
if ($PerformanceTest) {
    Write-Host "`n=== 性能基准测试 ===" -ForegroundColor Cyan
    Write-Host "执行redis-benchmark测试..." -ForegroundColor Yellow
    
    try {
        # 检查redis-benchmark是否可用
        $benchmarkPath = Get-Command redis-benchmark -ErrorAction SilentlyContinue
        if ($benchmarkPath) {
            Write-Host "执行SET操作基准测试 (10000次)..." -ForegroundColor Yellow
            & redis-benchmark -h $Host -p $Port -a $Password -t set -n 10000 -q
            
            Write-Host "执行GET操作基准测试 (10000次)..." -ForegroundColor Yellow  
            & redis-benchmark -h $Host -p $Port -a $Password -t get -n 10000 -q
            
            Write-Host "执行混合操作基准测试 (5000次)..." -ForegroundColor Yellow
            & redis-benchmark -h $Host -p $Port -a $Password -n 5000 -q
        } else {
            Write-Host "redis-benchmark未找到，跳过性能测试" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "性能测试失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "DragonflyDB配置验证完成。如需详细信息，请使用 -Verbose 参数。" -ForegroundColor Yellow
Write-Host "如需性能测试，请使用 -PerformanceTest 参数。" -ForegroundColor Yellow
