# 监控系统测试脚本
# 测试Prometheus+Grafana监控系统的基本功能

param(
    [switch]$SkipContainers
)

Write-Host "🧪 Prometheus + Grafana 监控系统测试" -ForegroundColor Cyan

# 1. 测试Axum应用metrics端点
Write-Host "`n📊 测试Axum应用metrics端点..." -ForegroundColor Yellow

try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000/metrics" -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Axum应用metrics端点正常工作" -ForegroundColor Green
        Write-Host "📈 指标数据预览:" -ForegroundColor Gray
        $lines = $response.Content -split "`n" | Select-Object -First 10
        foreach ($line in $lines) {
            if ($line.Trim() -ne "") {
                Write-Host "  $line" -ForegroundColor White
            }
        }
    } else {
        Write-Host "❌ Axum应用metrics端点返回状态码: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法访问Axum应用metrics端点: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "💡 请确保Axum应用正在运行: cargo run -p server" -ForegroundColor Yellow
}

# 2. 测试健康检查端点
Write-Host "`n🏥 测试健康检查端点..." -ForegroundColor Yellow

try {
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/health" -UseBasicParsing
    if ($healthResponse.StatusCode -eq 200) {
        Write-Host "✅ 健康检查端点正常工作" -ForegroundColor Green
    } else {
        Write-Host "❌ 健康检查端点返回状态码: $($healthResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法访问健康检查端点: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 测试性能监控端点
Write-Host "`n⚡ 测试性能监控端点..." -ForegroundColor Yellow

try {
    $perfResponse = Invoke-WebRequest -Uri "http://localhost:3000/api/performance/stats" -UseBasicParsing
    if ($perfResponse.StatusCode -eq 200) {
        Write-Host "✅ 性能监控端点正常工作" -ForegroundColor Green
    } else {
        Write-Host "❌ 性能监控端点返回状态码: $($perfResponse.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 无法访问性能监控端点: $($_.Exception.Message)" -ForegroundColor Red
}

if (-not $SkipContainers) {
    # 4. 测试容器状态
    Write-Host "`n🐳 检查容器状态..." -ForegroundColor Yellow
    
    try {
        $containerStatus = wsl -e bash -c "cd /mnt/d/ceshi/ceshi/axum-tutorial && podman-compose ps"
        Write-Host "容器状态:" -ForegroundColor Gray
        Write-Host $containerStatus -ForegroundColor White
    } catch {
        Write-Host "❌ 无法获取容器状态: $($_.Exception.Message)" -ForegroundColor Red
    }

    # 5. 测试Prometheus连接（如果容器运行）
    Write-Host "`n🔍 测试Prometheus连接..." -ForegroundColor Yellow
    
    try {
        $prometheusResponse = Invoke-WebRequest -Uri "http://localhost:9090" -UseBasicParsing -TimeoutSec 5
        if ($prometheusResponse.StatusCode -eq 200) {
            Write-Host "✅ Prometheus正在运行" -ForegroundColor Green
        } else {
            Write-Host "❌ Prometheus返回状态码: $($prometheusResponse.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "⚠️ Prometheus未运行或无法访问: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "💡 使用以下命令启动监控系统: .\scripts\start-monitoring.ps1" -ForegroundColor Gray
    }

    # 6. 测试Grafana连接（如果容器运行）
    Write-Host "`n📊 测试Grafana连接..." -ForegroundColor Yellow
    
    try {
        $grafanaResponse = Invoke-WebRequest -Uri "http://localhost:3001" -UseBasicParsing -TimeoutSec 5
        if ($grafanaResponse.StatusCode -eq 200) {
            Write-Host "✅ Grafana正在运行" -ForegroundColor Green
        } else {
            Write-Host "❌ Grafana返回状态码: $($grafanaResponse.StatusCode)" -ForegroundColor Red
        }
    } catch {
        Write-Host "⚠️ Grafana未运行或无法访问: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "💡 使用以下命令启动监控系统: .\scripts\start-monitoring.ps1" -ForegroundColor Gray
    }
}

# 7. 配置文件检查
Write-Host "`n📋 检查配置文件..." -ForegroundColor Yellow

$configFiles = @(
    "monitoring/prometheus.yml",
    "monitoring/grafana/provisioning/datasources/prometheus.yml",
    "monitoring/grafana/provisioning/dashboards/dashboard.yml",
    "monitoring/grafana/dashboards/axum-tutorial-dashboard.json"
)

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $file 不存在" -ForegroundColor Red
    }
}

Write-Host "`n🎯 测试完成!" -ForegroundColor Cyan
Write-Host "📖 查看监控系统文档: monitoring/README.md" -ForegroundColor Gray
