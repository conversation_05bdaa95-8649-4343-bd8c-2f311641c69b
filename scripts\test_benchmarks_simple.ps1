# Simple benchmark test script for Task 17 verification
param(
    [string]$TestType = "criterion"
)

Write-Host "=== Simple Benchmark Test - Task 17 ===" -ForegroundColor Green
Write-Host "Test Type: $TestType" -ForegroundColor Yellow

# Check if server is running
function Test-ServerRunning {
    try {
        $response = Invoke-WebRequest -Uri "http://127.0.0.1:3000/api/performance/health" -TimeoutSec 5
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# Main execution
Write-Host "Checking server status..." -ForegroundColor Yellow
if (-not (Test-ServerRunning)) {
    Write-Host "Server is not running at http://127.0.0.1:3000" -ForegroundColor Red
    Write-Host "Please start the server first: cargo run -p axum-server --bin axum-server" -ForegroundColor Yellow
    exit 1
}

Write-Host "Server is running!" -ForegroundColor Green

if ($TestType -eq "criterion" -or $TestType -eq "all") {
    Write-Host "`nRunning Criterion benchmarks..." -ForegroundColor Yellow
    
    # Run API performance benchmarks
    Write-Host "Running API performance benchmarks..." -ForegroundColor Cyan
    cargo bench --bench api_performance_benchmarks --no-run
    if ($LASTEXITCODE -eq 0) {
        Write-Host "API performance benchmarks compiled successfully!" -ForegroundColor Green
    } else {
        Write-Host "API performance benchmarks compilation failed!" -ForegroundColor Red
    }
    
    # Run comprehensive performance benchmarks
    Write-Host "Running comprehensive performance benchmarks..." -ForegroundColor Cyan
    cargo bench --bench comprehensive_performance_benchmarks --no-run
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Comprehensive performance benchmarks compiled successfully!" -ForegroundColor Green
    } else {
        Write-Host "Comprehensive performance benchmarks compilation failed!" -ForegroundColor Red
    }
}

Write-Host "`nBenchmark test completed!" -ForegroundColor Green
