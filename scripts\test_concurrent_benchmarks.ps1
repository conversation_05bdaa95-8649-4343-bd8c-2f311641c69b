# 测试并发负载基准测试脚本
# 用于验证基准测试是否能正常运行

param(
    [switch]$QuickTest = $false,
    [switch]$SkipServerCheck = $false
)

Write-Host "=== 测试并发负载基准测试 ===" -ForegroundColor Green

# 检查服务器是否运行
function Test-ServerRunning {
    try {
        $response = Invoke-WebRequest -Uri "http://127.0.0.1:3000/health" -TimeoutSec 5 -ErrorAction Stop
        return $response.StatusCode -eq 200
    }
    catch {
        return $false
    }
}

# 主执行流程
try {
    if (-not $SkipServerCheck) {
        Write-Host "检查Axum服务器状态..." -ForegroundColor Blue
        if (-not (Test-ServerRunning)) {
            Write-Host "错误: Axum服务器未运行，请先启动服务器" -ForegroundColor Red
            Write-Host "运行命令: cargo run -p server" -ForegroundColor Yellow
            exit 1
        }
        Write-Host "Axum服务器运行正常" -ForegroundColor Green
    }
    
    # 编译基准测试
    Write-Host "编译基准测试..." -ForegroundColor Blue
    $compileResult = cargo check --bench concurrent_load_benchmarks
    if ($LASTEXITCODE -ne 0) {
        Write-Host "基准测试编译失败" -ForegroundColor Red
        exit 1
    }
    Write-Host "基准测试编译成功" -ForegroundColor Green
    
    if ($QuickTest) {
        Write-Host "运行快速基准测试验证..." -ForegroundColor Cyan
        # 运行一个非常短的基准测试来验证功能
        $env:CRITERION_SAMPLE_SIZE = "5"
        $env:CRITERION_MEASUREMENT_TIME = "2"
        $env:CRITERION_WARM_UP_TIME = "1"
        
        $testResult = cargo bench --bench concurrent_load_benchmarks -- --quick
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "快速基准测试验证成功" -ForegroundColor Green
        } else {
            Write-Host "快速基准测试验证失败" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "基准测试已准备就绪" -ForegroundColor Green
        Write-Host "要运行完整的基准测试，请使用:" -ForegroundColor Yellow
        Write-Host "  .\scripts\run_concurrent_load_benchmarks.ps1" -ForegroundColor Cyan
        Write-Host "要运行快速验证，请使用:" -ForegroundColor Yellow
        Write-Host "  .\scripts\test_concurrent_benchmarks.ps1 -QuickTest" -ForegroundColor Cyan
    }
    
    Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
    
} catch {
    Write-Host "执行过程中发生错误: $_" -ForegroundColor Red
    exit 1
}
