//! # DragonflyDB连接测试脚本
//!
//! 测试DragonflyDB服务器连接是否正常

use anyhow::Result as AnyhowResult;
use fred::prelude::*;
use std::time::Duration;
use tokio::time::timeout;

#[tokio::main]
async fn main() -> AnyhowResult<()> {
    println!("🔍 测试DragonflyDB连接...");

    // 创建Redis客户端配置
    let config = Config::from_url("redis://:dragonfly_secure_password_2025@127.0.0.1:6379")?;
    let client = Builder::from_config(config).build()?;

    // 尝试连接
    println!("📡 正在连接到 redis://127.0.0.1:6379...");

    match timeout(Duration::from_secs(5), client.connect()).await {
        Ok(Ok(_)) => {
            println!("✅ 连接成功！");

            // 测试PING命令
            match timeout(Duration::from_secs(2), client.ping::<String>(None)).await {
                Ok(Ok(_)) => {
                    println!("✅ PING测试成功！");

                    // 测试基本的SET/GET操作
                    let test_key = "test:connection:key";
                    let test_value = "Hello DragonflyDB!";

                    match client.set::<String, _, _>(test_key, test_value, None, None, false).await {
                        Ok(_) => {
                            println!("✅ SET操作成功！");

                            match client.get::<String, _>(test_key).await {
                                Ok(value) => {
                                    if value == test_value {
                                        println!("✅ GET操作成功！值匹配：{}", value);

                                        // 清理测试数据
                                        let _: Result<i64, _> = client.del(test_key).await;
                                        println!("🧹 清理测试数据完成");

                                        println!("🎉 DragonflyDB连接测试完全成功！");
                                    } else {
                                        println!(
                                            "❌ GET操作失败：值不匹配。期望：{}，实际：{}",
                                            test_value,
                                            value
                                        );
                                    }
                                }
                                Err(e) => {
                                    println!("❌ GET操作失败：{}", e);
                                }
                            }
                        }
                        Err(e) => {
                            println!("❌ SET操作失败：{}", e);
                        }
                    }
                }
                Ok(Err(e)) => {
                    println!("❌ PING测试失败：{}", e);
                }
                Err(_) => {
                    println!("❌ PING测试超时");
                }
            }

            // 断开连接
            let _ = client.quit().await;
            println!("🔌 连接已断开");
        }
        Ok(Err(e)) => {
            println!("❌ 连接失败：{}", e);
            println!("💡 请确保DragonflyDB服务器正在运行：");
            println!("   wsl -d Ubuntu -- podman start dragonflydb_test");
        }
        Err(_) => {
            println!("❌ 连接超时");
            println!("💡 请检查DragonflyDB服务器状态：");
            println!("   wsl -d Ubuntu -- podman ps -a");
        }
    }

    Ok(())
}
