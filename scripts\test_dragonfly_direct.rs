//! # 直接测试DragonflyDB连接
//!
//! 测试从Windows主机连接到WSL2中的DragonflyDB

use anyhow::Result as AnyhowResult;
use fred::prelude::*;
use std::time::Duration;
use tokio::time::timeout;

#[tokio::main]
async fn main() -> AnyhowResult<()> {
    println!("🔍 测试DragonflyDB连接...");

    // 测试不同的连接地址
    let test_urls = vec![
        "redis://127.0.0.1:6379", // localhost
        "redis://************:6379" // WSL2 IP
    ];

    for url in test_urls {
        println!("\n📡 测试连接: {}", url);

        match test_connection(url).await {
            Ok(_) => {
                println!("✅ 连接成功: {}", url);
                return Ok(());
            }
            Err(e) => {
                println!("❌ 连接失败: {} - {}", url, e);
            }
        }
    }

    println!("\n❌ 所有连接尝试都失败了");
    println!("💡 请检查:");
    println!("   1. DragonflyDB容器是否运行: wsl -d Ubuntu -- podman ps");
    println!("   2. 端口是否正确映射: wsl -d Ubuntu -- podman port dragonflydb_test");
    println!("   3. WSL2网络是否可访问");

    Err(anyhow::anyhow!("无法连接到DragonflyDB"))
}

async fn test_connection(url: &str) -> AnyhowResult<()> {
    // 创建Redis客户端配置
    let config = Config::from_url(url)?;
    let client = Builder::from_config(config).build()?;

    // 尝试连接（5秒超时）
    let _ = timeout(Duration::from_secs(5), client.connect()).await??;
    println!("  ✓ 连接建立成功");

    // 测试PING命令（2秒超时）
    let _: String = timeout(Duration::from_secs(2), client.ping::<String>(None)).await??;
    println!("  ✓ PING测试成功");

    // 测试基本的SET/GET操作
    let test_key = "test:connection:direct";
    let test_value = "Hello from Windows!";

    let _: () = client.set(test_key, test_value, None, None, false).await?;
    println!("  ✓ SET操作成功");

    let retrieved: String = client.get(test_key).await?;
    if retrieved == test_value {
        println!("  ✓ GET操作成功，值匹配");
    } else {
        return Err(anyhow::anyhow!("GET操作失败：值不匹配"));
    }

    // 清理测试数据
    let _: () = client.del(test_key).await?;
    println!("  ✓ 清理完成");

    // 断开连接
    client.quit().await?;
    println!("  ✓ 连接已断开");

    Ok(())
}
