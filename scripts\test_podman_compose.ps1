# Podman Compose 功能测试脚本
# 验证podman-compose.yml配置的实际运行情况
# 基于Context7 MCP最佳实践和TDD开发模式

param(
    [switch]$SkipCleanup,
    [switch]$Verbose,
    [int]$TimeoutSeconds = 120
)

# 设置错误处理
$ErrorActionPreference = "Stop"

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colors = @{
        "Red" = [ConsoleColor]::Red
        "Green" = [ConsoleColor]::Green
        "Yellow" = [ConsoleColor]::Yellow
        "Blue" = [ConsoleColor]::Blue
        "Cyan" = [ConsoleColor]::<PERSON><PERSON>
        "White" = [ConsoleColor]::White
    }
    
    Write-Host $Message -ForegroundColor $colors[$Color]
}

# 检查WSL2环境
function Test-WSL2Environment {
    Write-ColorOutput "🔍 检查WSL2环境..." "Blue"
    
    try {
        $wslInfo = wsl --list --verbose 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "WSL2未安装或未正确配置"
        }
        
        $ubuntuRunning = $wslInfo | Select-String "Ubuntu.*Running"
        if (-not $ubuntuRunning) {
            Write-ColorOutput "⚠️  启动Ubuntu WSL2实例..." "Yellow"
            wsl -d Ubuntu echo "WSL2 Ubuntu启动中..."
            if ($LASTEXITCODE -ne 0) {
                throw "无法启动Ubuntu WSL2实例"
            }
        }
        
        Write-ColorOutput "✅ WSL2环境检查通过" "Green"
        return $true
    }
    catch {
        Write-ColorOutput "❌ WSL2环境检查失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 检查Podman安装
function Test-PodmanInstallation {
    Write-ColorOutput "🔍 检查Podman安装..." "Blue"
    
    try {
        $podmanVersion = wsl -d Ubuntu podman --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "Podman未安装在WSL2 Ubuntu中"
        }
        
        Write-ColorOutput "✅ Podman版本: $podmanVersion" "Green"
        return $true
    }
    catch {
        Write-ColorOutput "❌ Podman检查失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 检查podman-compose安装
function Test-PodmanComposeInstallation {
    Write-ColorOutput "🔍 检查podman-compose安装..." "Blue"
    
    try {
        $composeVersion = wsl -d Ubuntu podman-compose --version 2>$null
        if ($LASTEXITCODE -ne 0) {
            throw "podman-compose未安装"
        }
        
        Write-ColorOutput "✅ podman-compose版本: $composeVersion" "Green"
        return $true
    }
    catch {
        Write-ColorOutput "❌ podman-compose检查失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 验证配置文件
function Test-ConfigurationFiles {
    Write-ColorOutput "🔍 验证配置文件..." "Blue"
    
    $requiredFiles = @(
        "podman-compose.yml",
        "config/postgresql.conf",
        "config/pg_hba.conf",
        "scripts/init-db.sql",
        "monitoring/prometheus.yml"
    )
    
    $missingFiles = @()
    foreach ($file in $requiredFiles) {
        if (-not (Test-Path $file)) {
            $missingFiles += $file
        }
    }
    
    if ($missingFiles.Count -gt 0) {
        Write-ColorOutput "❌ 缺少配置文件: $($missingFiles -join ', ')" "Red"
        return $false
    }
    
    Write-ColorOutput "✅ 所有配置文件存在" "Green"
    return $true
}

# 启动服务
function Start-Services {
    Write-ColorOutput "🚀 启动Podman Compose服务..." "Blue"
    
    try {
        # 切换到项目目录并启动服务
        $projectPath = "/mnt/d/ceshi/ceshi/axum-tutorial"
        
        Write-ColorOutput "📁 项目路径: $projectPath" "Cyan"
        
        # 启动服务（分离模式）
        $startResult = wsl -d Ubuntu bash -c "cd '$projectPath' && podman-compose up -d" 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            throw "服务启动失败: $startResult"
        }
        
        Write-ColorOutput "✅ 服务启动成功" "Green"
        if ($Verbose) {
            Write-ColorOutput "启动输出: $startResult" "Cyan"
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "❌ 服务启动失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 检查服务状态
function Test-ServicesHealth {
    Write-ColorOutput "🔍 检查服务健康状态..." "Blue"
    
    try {
        $projectPath = "/mnt/d/ceshi/ceshi/axum-tutorial"
        
        # 等待服务启动
        Write-ColorOutput "⏳ 等待服务启动..." "Yellow"
        Start-Sleep -Seconds 30
        
        # 检查容器状态
        $containerStatus = wsl -d Ubuntu bash -c "cd '$projectPath' && podman-compose ps" 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            throw "无法获取容器状态: $containerStatus"
        }
        
        Write-ColorOutput "📊 容器状态:" "Cyan"
        Write-ColorOutput $containerStatus "White"
        
        # 检查PostgreSQL连接
        Write-ColorOutput "🔍 测试PostgreSQL连接..." "Blue"
        $pgTest = wsl -d Ubuntu bash -c "cd '$projectPath' && podman exec axum_postgres_17 pg_isready -U axum_user -d axum_tutorial" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ PostgreSQL连接正常" "Green"
        } else {
            Write-ColorOutput "⚠️  PostgreSQL连接测试: $pgTest" "Yellow"
        }
        
        # 检查DragonflyDB连接
        Write-ColorOutput "🔍 测试DragonflyDB连接..." "Blue"
        $dfTest = wsl -d Ubuntu bash -c "cd '$projectPath' && podman exec axum_dragonflydb redis-cli -a dragonfly_secure_password_2025 ping" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✅ DragonflyDB连接正常" "Green"
        } else {
            Write-ColorOutput "⚠️  DragonflyDB连接测试: $dfTest" "Yellow"
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "❌ 服务健康检查失败: $($_.Exception.Message)" "Red"
        return $false
    }
}

# 清理服务
function Stop-Services {
    if ($SkipCleanup) {
        Write-ColorOutput "⏭️  跳过清理步骤" "Yellow"
        return
    }
    
    Write-ColorOutput "🧹 清理服务..." "Blue"
    
    try {
        $projectPath = "/mnt/d/ceshi/ceshi/axum-tutorial"
        
        # 停止并删除容器
        $stopResult = wsl -d Ubuntu bash -c "cd '$projectPath' && podman-compose down" 2>&1
        
        if ($LASTEXITCODE -ne 0) {
            Write-ColorOutput "⚠️  清理过程中出现警告: $stopResult" "Yellow"
        } else {
            Write-ColorOutput "✅ 服务清理完成" "Green"
        }
    }
    catch {
        Write-ColorOutput "❌ 服务清理失败: $($_.Exception.Message)" "Red"
    }
}

# 主测试流程
function Invoke-MainTest {
    Write-ColorOutput "🎯 开始Podman Compose功能测试" "Cyan"
    Write-ColorOutput "=" * 50 "Cyan"
    
    $testResults = @{
        "WSL2环境" = $false
        "Podman安装" = $false
        "podman-compose安装" = $false
        "配置文件" = $false
        "服务启动" = $false
        "服务健康检查" = $false
    }
    
    try {
        # 执行测试步骤
        $testResults["WSL2环境"] = Test-WSL2Environment
        if (-not $testResults["WSL2环境"]) { return $testResults }
        
        $testResults["Podman安装"] = Test-PodmanInstallation
        if (-not $testResults["Podman安装"]) { return $testResults }
        
        $testResults["podman-compose安装"] = Test-PodmanComposeInstallation
        if (-not $testResults["podman-compose安装"]) { return $testResults }
        
        $testResults["配置文件"] = Test-ConfigurationFiles
        if (-not $testResults["配置文件"]) { return $testResults }
        
        $testResults["服务启动"] = Start-Services
        if (-not $testResults["服务启动"]) { return $testResults }
        
        $testResults["服务健康检查"] = Test-ServicesHealth
        
        return $testResults
    }
    finally {
        # 清理资源
        Stop-Services
    }
}

# 显示测试结果
function Show-TestResults {
    param([hashtable]$Results)
    
    Write-ColorOutput "`n📊 测试结果摘要" "Cyan"
    Write-ColorOutput "=" * 50 "Cyan"
    
    $passedCount = 0
    $totalCount = $Results.Count
    
    foreach ($test in $Results.GetEnumerator()) {
        $status = if ($test.Value) { "✅ 通过" } else { "❌ 失败" }
        $color = if ($test.Value) { "Green" } else { "Red" }
        
        Write-ColorOutput "$($test.Key): $status" $color
        
        if ($test.Value) { $passedCount++ }
    }
    
    Write-ColorOutput "`n总计: $passedCount/$totalCount 测试通过" "Cyan"
    
    if ($passedCount -eq $totalCount) {
        Write-ColorOutput "🎉 所有测试通过！podman-compose.yml配置正确" "Green"
        return $true
    } else {
        Write-ColorOutput "⚠️  部分测试失败，请检查配置" "Yellow"
        return $false
    }
}

# 脚本入口点
try {
    $results = Invoke-MainTest
    $success = Show-TestResults -Results $results
    
    if ($success) {
        exit 0
    } else {
        exit 1
    }
}
catch {
    Write-ColorOutput "💥 测试过程中发生未处理的错误: $($_.Exception.Message)" "Red"
    Write-ColorOutput "堆栈跟踪: $($_.ScriptStackTrace)" "Red"
    exit 1
}
