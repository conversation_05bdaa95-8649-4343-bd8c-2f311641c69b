# Podman服务测试脚本
# 测试PostgreSQL 17 + DragonflyDB + Prometheus + Grafana服务状态
# 适用于WSL2 + Podman环境

Write-Host "🔵 开始测试Podman容器服务..." -ForegroundColor Blue

# 检查容器状态
Write-Host "`n📋 检查容器状态..." -ForegroundColor Yellow
wsl -d Ubuntu podman ps

# 测试PostgreSQL连接
Write-Host "`n🐘 测试PostgreSQL 17连接..." -ForegroundColor Green
try {
    $pgResult = wsl -d Ubuntu podman exec axum_postgres_17 psql -U axum_user -d axum_tutorial -c "SELECT 'PostgreSQL连接成功!' as status;"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ PostgreSQL 17连接成功!" -ForegroundColor Green
        Write-Host $pgResult
    } else {
        Write-Host "❌ PostgreSQL连接失败!" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ PostgreSQL连接测试异常: $_" -ForegroundColor Red
}

# 测试DragonflyDB连接
Write-Host "`n🐉 测试DragonflyDB连接..." -ForegroundColor Cyan
try {
    $dfResult = wsl -d Ubuntu podman exec axum_dragonflydb redis-cli -a dragonfly_secure_password_2025 ping
    if ($LASTEXITCODE -eq 0 -and $dfResult -eq "PONG") {
        Write-Host "✅ DragonflyDB连接成功!" -ForegroundColor Green
        Write-Host "响应: $dfResult"
    } else {
        Write-Host "❌ DragonflyDB连接失败!" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ DragonflyDB连接测试异常: $_" -ForegroundColor Red
}

# 测试Prometheus服务
Write-Host "`n📊 测试Prometheus服务..." -ForegroundColor Magenta
try {
    $promResult = Invoke-WebRequest -Uri "http://localhost:9090/-/healthy" -TimeoutSec 10
    if ($promResult.StatusCode -eq 200) {
        Write-Host "✅ Prometheus服务正常!" -ForegroundColor Green
    } else {
        Write-Host "❌ Prometheus服务异常!" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Prometheus服务测试失败: $_" -ForegroundColor Red
}

# 测试Grafana服务
Write-Host "`n📈 测试Grafana服务..." -ForegroundColor Blue
try {
    $grafanaResult = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -TimeoutSec 10
    if ($grafanaResult.StatusCode -eq 200) {
        Write-Host "✅ Grafana服务正常!" -ForegroundColor Green
    } else {
        Write-Host "❌ Grafana服务异常!" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Grafana服务测试失败: $_" -ForegroundColor Red
}

# 显示服务访问地址
Write-Host "`n🌐 服务访问地址:" -ForegroundColor Yellow
Write-Host "  PostgreSQL 17:  localhost:5432" -ForegroundColor White
Write-Host "  DragonflyDB:     localhost:6379" -ForegroundColor White
Write-Host "  Prometheus:      http://localhost:9090" -ForegroundColor White
Write-Host "  Grafana:         http://localhost:3001 (admin/grafana_admin_2025)" -ForegroundColor White

Write-Host "`n🎉 Podman服务测试完成!" -ForegroundColor Green
