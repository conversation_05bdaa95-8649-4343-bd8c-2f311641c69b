# 测试服务器健康状态脚本
# 用于验证Axum服务器是否正常运行并可以响应请求

param(
    [string]$BaseUrl = "http://127.0.0.1:3000",
    [int]$TimeoutSeconds = 10
)

Write-Host "=== 测试Axum服务器健康状态 ===" -ForegroundColor Green
Write-Host "服务器地址: $BaseUrl" -ForegroundColor Yellow
Write-Host "超时时间: $TimeoutSeconds 秒" -ForegroundColor Yellow

# Test endpoints list
$endpoints = @(
    @{Name = "Basic Health Check"; Url = "$BaseUrl/health"}
    @{Name = "Performance Health"; Url = "$BaseUrl/api/performance/health"}
    @{Name = "Prometheus Metrics"; Url = "$BaseUrl/metrics"}
    @{Name = "Deep Health Check"; Url = "$BaseUrl/health/deep"}
    @{Name = "Database Health"; Url = "$BaseUrl/api/health/database"}
)

$successCount = 0
$totalCount = $endpoints.Count

Write-Host "`nStarting endpoint tests..." -ForegroundColor Cyan

foreach ($endpoint in $endpoints) {
    Write-Host "`nTesting: $($endpoint.Name)" -ForegroundColor White
    Write-Host "URL: $($endpoint.Url)" -ForegroundColor Gray
    
    try {
        $response = Invoke-WebRequest -Uri $endpoint.Url -TimeoutSec $TimeoutSeconds -ErrorAction Stop
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ Success - Status: $($response.StatusCode)" -ForegroundColor Green
            $successCount++

            # Show first 100 characters of response
            $content = $response.Content
            if ($content.Length -gt 100) {
                $content = $content.Substring(0, 100) + "..."
            }
            Write-Host "Response: $content" -ForegroundColor DarkGray
        } else {
            Write-Host "⚠️ Warning - Status: $($response.StatusCode)" -ForegroundColor Yellow
        }
    }
    catch {
        Write-Host "❌ Failed - Error: $($_.Exception.Message)" -ForegroundColor Red

        # Provide more details for connection errors
        if ($_.Exception.Message -like "*connection*" -or $_.Exception.Message -like "*timeout*") {
            Write-Host "   Possible cause: Server not started or port occupied" -ForegroundColor Red
        }
    }
}

Write-Host "`n=== Test Results Summary ===" -ForegroundColor Green
Write-Host "Success: $successCount/$totalCount" -ForegroundColor $(if ($successCount -eq $totalCount) { "Green" } elseif ($successCount -gt 0) { "Yellow" } else { "Red" })

if ($successCount -eq $totalCount) {
    Write-Host "🎉 All endpoints passed! Server is running normally." -ForegroundColor Green
    exit 0
} elseif ($successCount -gt 0) {
    Write-Host "⚠️ Some endpoints passed, server may have issues." -ForegroundColor Yellow
    exit 1
} else {
    Write-Host "❌ All endpoints failed, server may not be started." -ForegroundColor Red
    Write-Host "Please check:" -ForegroundColor Yellow
    Write-Host "1. Is server started: cargo run -p server" -ForegroundColor Yellow
    Write-Host "2. Is port 3000 occupied" -ForegroundColor Yellow
    Write-Host "3. Is firewall blocking connections" -ForegroundColor Yellow
    exit 2
}

Write-Host "`nTest completed!" -ForegroundColor Green
