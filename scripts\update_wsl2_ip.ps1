# WSL2 DragonflyDB IP地址自动更新脚本
# 解决WSL2网络IP地址变化导致的连接问题

param(
    [string]$EnvFile = ".env",
    [switch]$Verbose
)

Write-Host "🔧 WSL2 DragonflyDB IP地址自动更新工具" -ForegroundColor Green

# 检查WSL2是否运行
try {
    $wslStatus = wsl --status 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ WSL2未运行或未安装" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ 无法检查WSL2状态" -ForegroundColor Red
    exit 1
}

# 获取WSL2的IP地址
Write-Host "🔍 正在获取WSL2 IP地址..." -ForegroundColor Yellow
try {
    $wsl2IP = (wsl hostname -I).Trim()
    if ([string]::IsNullOrEmpty($wsl2IP)) {
        Write-Host "❌ 无法获取WSL2 IP地址" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ WSL2 IP地址: $wsl2IP" -ForegroundColor Green
} catch {
    Write-Host "❌ 获取WSL2 IP地址失败: $_" -ForegroundColor Red
    exit 1
}

# 检查.env文件是否存在
if (-not (Test-Path $EnvFile)) {
    Write-Host "❌ 环境文件 $EnvFile 不存在" -ForegroundColor Red
    exit 1
}

# 读取当前.env文件内容
$envContent = Get-Content $EnvFile -Raw
$originalContent = $envContent

# 更新CACHE_URL中的IP地址
$pattern = 'CACHE_URL=redis://([^@]*@)?([^:]+):6379'
$replacement = "CACHE_URL=redis://`$1${wsl2IP}:6379"

if ($envContent -match $pattern) {
    $currentIP = $matches[2]
    if ($currentIP -eq $wsl2IP) {
        Write-Host "✅ IP地址已是最新 ($wsl2IP)，无需更新" -ForegroundColor Green
        exit 0
    }
    
    Write-Host "🔄 更新IP地址: $currentIP -> $wsl2IP" -ForegroundColor Yellow
    $envContent = $envContent -replace $pattern, $replacement
    
    # 写入更新后的内容
    try {
        Set-Content -Path $EnvFile -Value $envContent -NoNewline
        Write-Host "✅ 环境文件已更新" -ForegroundColor Green
        
        if ($Verbose) {
            Write-Host "📝 更新详情:" -ForegroundColor Cyan
            Write-Host "  文件: $EnvFile" -ForegroundColor Gray
            Write-Host "  旧IP: $currentIP" -ForegroundColor Gray
            Write-Host "  新IP: $wsl2IP" -ForegroundColor Gray
        }
    } catch {
        Write-Host "❌ 写入环境文件失败: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "❌ 在环境文件中未找到CACHE_URL配置" -ForegroundColor Red
    exit 1
}

# 验证DragonflyDB连接
Write-Host "🔍 验证DragonflyDB连接..." -ForegroundColor Yellow
try {
    $testResult = Test-NetConnection -ComputerName $wsl2IP -Port 6379 -WarningAction SilentlyContinue
    if ($testResult.TcpTestSucceeded) {
        Write-Host "✅ DragonflyDB连接测试成功" -ForegroundColor Green
    } else {
        Write-Host "⚠️ DragonflyDB连接测试失败，请检查容器状态" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️ 无法测试DragonflyDB连接: $_" -ForegroundColor Yellow
}

Write-Host "🎉 WSL2 IP地址更新完成！" -ForegroundColor Green
Write-Host "💡 提示: 现在可以运行 cargo run -p axum-server 启动服务器" -ForegroundColor Cyan
