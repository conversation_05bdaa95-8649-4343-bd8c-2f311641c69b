# Podman Compose Configuration Validation Script

Write-Host "Checking Podman Compose configuration..." -ForegroundColor Cyan

# Check required files
$files = @(
    "podman-compose.yml",
    "config/postgresql.conf",
    "config/pg_hba.conf",
    "scripts/init-db.sql",
    "monitoring/prometheus.yml"
)

$allFilesExist = $true
foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file (missing)" -ForegroundColor Red
        $allFilesExist = $false
    }
}

if ($allFilesExist) {
    Write-Host "✅ All configuration files exist" -ForegroundColor Green
} else {
    Write-Host "❌ Some configuration files are missing" -ForegroundColor Red
    exit 1
}

# Check data directories
$dataDirs = @("data", "data/postgres", "data/dragonflydb", "data/prometheus", "data/grafana")
foreach ($dir in $dataDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✅ Created data directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "✅ Data directory exists: $dir" -ForegroundColor Green
    }
}

# Validate YAML syntax
Write-Host "Validating YAML syntax..." -ForegroundColor Blue
try {
    $command = "cd /mnt/d/ceshi/ceshi/axum-tutorial; podman-compose config"
    $result = wsl -d Ubuntu bash -c $command 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ YAML syntax is correct" -ForegroundColor Green
    } else {
        Write-Host "❌ YAML syntax error" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "⚠️  Cannot validate YAML syntax: $($_.Exception.Message)" -ForegroundColor Yellow
}

Write-Host "🎉 Configuration validation completed!" -ForegroundColor Green
Write-Host "You can start services with:" -ForegroundColor Cyan
$startCommand = "cd /mnt/d/ceshi/ceshi/axum-tutorial; podman-compose up -d"
Write-Host "wsl -d Ubuntu bash -c `"$startCommand`"" -ForegroundColor White
