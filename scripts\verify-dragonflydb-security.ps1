# DragonflyDB 安全配置验证脚本
# 验证ACL用户、权限设置和安全配置

param(
    [string]$Host = "127.0.0.1",
    [int]$Port = 6379,
    [int]$AdminPort = 6380,
    [string]$AdminPassword = "admin_secure_password_2025",
    [switch]$Verbose = $false
)

Write-Host "=== DragonflyDB 安全配置验证 ===" -ForegroundColor Green
Write-Host "目标服务器: $Host`:$Port (管理端口: $AdminPort)" -ForegroundColor Yellow

# 检查redis-cli是否可用
$redisCliPath = Get-Command redis-cli -ErrorAction SilentlyContinue
if (-not $redisCliPath) {
    Write-Host "错误: 未找到redis-cli命令。" -ForegroundColor Red
    exit 1
}

# 测试用户配置
$testUsers = @(
    @{
        Name = "admin"
        Password = "admin_secure_password_2025"
        ExpectedAccess = $true
        TestCommands = @("INFO", "CONFIG GET maxmemory", "ACL LIST")
    },
    @{
        Name = "axum_app"
        Password = "dragonfly_secure_password_2025"
        ExpectedAccess = $true
        TestCommands = @("SET test:key test:value", "GET test:key", "DEL test:key")
    },
    @{
        Name = "readonly"
        Password = "readonly_password_2025"
        ExpectedAccess = $true
        TestCommands = @("GET test:readonly", "INFO memory")
        ForbiddenCommands = @("SET test:forbidden value", "FLUSHDB")
    },
    @{
        Name = "monitor"
        Password = "monitor_password_2025"
        ExpectedAccess = $true
        TestCommands = @("PING", "INFO server", "CLIENT LIST")
        ForbiddenCommands = @("SET test:forbidden value", "GET test:key")
    },
    @{
        Name = "cache_user"
        Password = "cache_password_2025"
        ExpectedAccess = $true
        TestCommands = @("SET cache:test value", "GET cache:test", "DEL cache:test")
        ForbiddenCommands = @("SET user:test value", "FLUSHDB")
    }
)

Write-Host "`n=== 基础连接测试 ===" -ForegroundColor Cyan

# 测试无密码连接 (应该失败)
Write-Host "测试无密码连接 (应该被拒绝)..." -ForegroundColor Yellow
try {
    $result = & redis-cli -h $Host -p $Port ping 2>&1
    if ($result -match "NOAUTH|Authentication required") {
        Write-Host "✓ 无密码连接被正确拒绝" -ForegroundColor Green
    } else {
        Write-Host "✗ 安全风险: 无密码连接被允许" -ForegroundColor Red
    }
} catch {
    Write-Host "✓ 无密码连接被正确拒绝" -ForegroundColor Green
}

# 测试默认用户 (应该失败)
Write-Host "测试默认用户连接 (应该被拒绝)..." -ForegroundColor Yellow
try {
    $result = & redis-cli -h $Host -p $Port --user default ping 2>&1
    if ($result -match "WRONGPASS|Authentication failed") {
        Write-Host "✓ 默认用户被正确禁用" -ForegroundColor Green
    } else {
        Write-Host "✗ 安全风险: 默认用户仍然可用" -ForegroundColor Red
    }
} catch {
    Write-Host "✓ 默认用户被正确禁用" -ForegroundColor Green
}

Write-Host "`n=== 用户权限测试 ===" -ForegroundColor Cyan

foreach ($user in $testUsers) {
    Write-Host "`n--- 测试用户: $($user.Name) ---" -ForegroundColor Yellow
    
    # 测试基础认证
    try {
        $authResult = & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password ping 2>&1
        if ($authResult -eq "PONG") {
            Write-Host "✓ 用户 $($user.Name) 认证成功" -ForegroundColor Green
        } else {
            Write-Host "✗ 用户 $($user.Name) 认证失败: $authResult" -ForegroundColor Red
            continue
        }
    } catch {
        Write-Host "✗ 用户 $($user.Name) 连接异常: $($_.Exception.Message)" -ForegroundColor Red
        continue
    }
    
    # 测试允许的命令
    if ($user.TestCommands) {
        Write-Host "  测试允许的命令..." -ForegroundColor Cyan
        foreach ($cmd in $user.TestCommands) {
            try {
                $cmdParts = $cmd -split ' '
                $result = & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password $cmdParts 2>&1
                if ($result -notmatch "NOPERM|Permission denied") {
                    Write-Host "    ✓ 命令允许: $cmd" -ForegroundColor Green
                } else {
                    Write-Host "    ✗ 命令被拒绝: $cmd ($result)" -ForegroundColor Red
                }
            } catch {
                Write-Host "    ✗ 命令执行异常: $cmd" -ForegroundColor Red
            }
        }
    }
    
    # 测试禁止的命令
    if ($user.ForbiddenCommands) {
        Write-Host "  测试禁止的命令..." -ForegroundColor Cyan
        foreach ($cmd in $user.ForbiddenCommands) {
            try {
                $cmdParts = $cmd -split ' '
                $result = & redis-cli -h $Host -p $Port --user $user.Name -a $user.Password $cmdParts 2>&1
                if ($result -match "NOPERM|Permission denied") {
                    Write-Host "    ✓ 命令被正确拒绝: $cmd" -ForegroundColor Green
                } else {
                    Write-Host "    ✗ 安全风险: 命令应被拒绝但被允许: $cmd" -ForegroundColor Red
                }
            } catch {
                Write-Host "    ✓ 命令被正确拒绝: $cmd" -ForegroundColor Green
            }
        }
    }
}

Write-Host "`n=== 管理端口测试 ===" -ForegroundColor Cyan

# 测试管理端口连接
Write-Host "测试管理端口连接..." -ForegroundColor Yellow
try {
    $adminResult = & redis-cli -h $Host -p $AdminPort --user admin -a $AdminPassword ping 2>&1
    if ($adminResult -eq "PONG") {
        Write-Host "✓ 管理端口连接成功" -ForegroundColor Green
        
        # 测试管理命令
        Write-Host "测试管理命令..." -ForegroundColor Yellow
        $configResult = & redis-cli -h $Host -p $AdminPort --user admin -a $AdminPassword config get maxmemory 2>&1
        if ($configResult -is [array] -and $configResult.Length -ge 2) {
            Write-Host "✓ 管理命令执行成功: maxmemory = $($configResult[1])" -ForegroundColor Green
        } else {
            Write-Host "✗ 管理命令执行失败" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ 管理端口连接失败: $adminResult" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 管理端口连接异常: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 危险命令保护测试 ===" -ForegroundColor Cyan

$dangerousCommands = @("FLUSHDB", "FLUSHALL", "SHUTDOWN", "DEBUG", "EVAL")
foreach ($cmd in $dangerousCommands) {
    Write-Host "测试危险命令保护: $cmd" -ForegroundColor Yellow
    try {
        $result = & redis-cli -h $Host -p $Port --user axum_app -a dragonfly_secure_password_2025 $cmd 2>&1
        if ($result -match "NOPERM|Permission denied|unknown command") {
            Write-Host "✓ 危险命令 $cmd 被正确保护" -ForegroundColor Green
        } else {
            Write-Host "✗ 安全风险: 危险命令 $cmd 未被保护" -ForegroundColor Red
        }
    } catch {
        Write-Host "✓ 危险命令 $cmd 被正确保护" -ForegroundColor Green
    }
}

Write-Host "`n=== 持久化配置检查 ===" -ForegroundColor Cyan

# 检查快照配置
try {
    $saveConfig = & redis-cli -h $Host -p $Port --user admin -a $AdminPassword config get save 2>&1
    Write-Host "快照配置: $($saveConfig -join ' ')" -ForegroundColor Yellow
    
    $dbFilename = & redis-cli -h $Host -p $Port --user admin -a $AdminPassword config get dbfilename 2>&1
    Write-Host "数据库文件名: $($dbFilename -join ' ')" -ForegroundColor Yellow
    
    $dir = & redis-cli -h $Host -p $Port --user admin -a $AdminPassword config get dir 2>&1
    Write-Host "数据目录: $($dir -join ' ')" -ForegroundColor Yellow
} catch {
    Write-Host "✗ 无法获取持久化配置" -ForegroundColor Red
}

Write-Host "`n=== 安全验证完成 ===" -ForegroundColor Green
Write-Host "请检查上述输出，确保所有安全配置都正确生效。" -ForegroundColor Yellow
Write-Host "如发现安全风险，请立即修复配置文件并重启服务。" -ForegroundColor Yellow
