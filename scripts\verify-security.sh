#!/bin/bash
# PostgreSQL安全配置验证脚本
# 验证企业级安全设置是否正确配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"
POSTGRES_USER="axum_user"
POSTGRES_DB="axum_tutorial"
POSTGRES_PASSWORD="axum_secure_password_2025"

echo -e "${BLUE}🔍 PostgreSQL安全配置验证开始...${NC}"
echo ""

# 检查1: 验证容器是否运行
echo -e "${YELLOW}📋 检查1: 验证PostgreSQL容器状态${NC}"
if podman ps | grep -q "axum_postgres_17"; then
    echo -e "${GREEN}✅ PostgreSQL容器正在运行${NC}"
else
    echo -e "${RED}❌ PostgreSQL容器未运行${NC}"
    exit 1
fi
echo ""

# 检查2: 验证端口访问
echo -e "${YELLOW}📋 检查2: 验证端口访问${NC}"
if nc -z $POSTGRES_HOST $POSTGRES_PORT; then
    echo -e "${GREEN}✅ PostgreSQL端口 $POSTGRES_PORT 可访问${NC}"
else
    echo -e "${RED}❌ PostgreSQL端口 $POSTGRES_PORT 不可访问${NC}"
    exit 1
fi
echo ""

# 检查3: 验证数据库连接
echo -e "${YELLOW}📋 检查3: 验证数据库连接${NC}"
export PGPASSWORD=$POSTGRES_PASSWORD
if psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -c "SELECT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 数据库连接成功${NC}"
else
    echo -e "${RED}❌ 数据库连接失败${NC}"
    exit 1
fi
echo ""

# 检查4: 验证用户权限
echo -e "${YELLOW}📋 检查4: 验证用户权限${NC}"
USER_PRIVILEGES=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT string_agg(privilege_type, ', ') FROM information_schema.table_privileges WHERE grantee = '$POSTGRES_USER' AND table_schema = 'public';" 2>/dev/null | xargs)
if [[ -n "$USER_PRIVILEGES" ]]; then
    echo -e "${GREEN}✅ 用户权限: $USER_PRIVILEGES${NC}"
else
    echo -e "${YELLOW}⚠️  用户权限: 基本权限 (无特定表权限)${NC}"
fi
echo ""

# 检查5: 验证数据库扩展
echo -e "${YELLOW}📋 检查5: 验证数据库扩展${NC}"
EXTENSIONS=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT string_agg(extname, ', ') FROM pg_extension WHERE extname IN ('uuid-ossp', 'pg_stat_statements', 'pg_trgm', 'btree_gin');" 2>/dev/null | xargs)
if [[ -n "$EXTENSIONS" ]]; then
    echo -e "${GREEN}✅ 已安装扩展: $EXTENSIONS${NC}"
else
    echo -e "${RED}❌ 未找到预期的扩展${NC}"
fi
echo ""

# 检查6: 验证配置参数
echo -e "${YELLOW}📋 检查6: 验证关键配置参数${NC}"
MAX_CONNECTIONS=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SHOW max_connections;" 2>/dev/null | xargs)
SHARED_BUFFERS=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SHOW shared_buffers;" 2>/dev/null | xargs)
WORK_MEM=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SHOW work_mem;" 2>/dev/null | xargs)

echo -e "${GREEN}✅ max_connections: $MAX_CONNECTIONS${NC}"
echo -e "${GREEN}✅ shared_buffers: $SHARED_BUFFERS${NC}"
echo -e "${GREEN}✅ work_mem: $WORK_MEM${NC}"
echo ""

# 检查7: 验证日志配置
echo -e "${YELLOW}📋 检查7: 验证日志配置${NC}"
LOG_STATEMENT=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SHOW log_statement;" 2>/dev/null | xargs)
LOG_MIN_DURATION=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SHOW log_min_duration_statement;" 2>/dev/null | xargs)

echo -e "${GREEN}✅ log_statement: $LOG_STATEMENT${NC}"
echo -e "${GREEN}✅ log_min_duration_statement: $LOG_MIN_DURATION${NC}"
echo ""

# 检查8: 验证SSL状态
echo -e "${YELLOW}📋 检查8: 验证SSL状态${NC}"
SSL_STATUS=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SHOW ssl;" 2>/dev/null | xargs)
if [[ "$SSL_STATUS" == "on" ]]; then
    echo -e "${GREEN}✅ SSL已启用${NC}"
else
    echo -e "${YELLOW}⚠️  SSL未启用 (开发环境可接受)${NC}"
fi
echo ""

# 检查9: 验证监控视图
echo -e "${YELLOW}📋 检查9: 验证监控视图${NC}"
VIEWS=$(psql -h $POSTGRES_HOST -p $POSTGRES_PORT -U $POSTGRES_USER -d $POSTGRES_DB -t -c "SELECT string_agg(viewname, ', ') FROM pg_views WHERE schemaname = 'public' AND viewname IN ('pg_stat_activity_summary', 'slow_queries', 'database_size_info', 'table_size_info', 'index_usage_stats');" 2>/dev/null | xargs)
if [[ -n "$VIEWS" ]]; then
    echo -e "${GREEN}✅ 监控视图: $VIEWS${NC}"
else
    echo -e "${RED}❌ 监控视图未创建${NC}"
fi
echo ""

# 检查10: 验证数据持久化
echo -e "${YELLOW}📋 检查10: 验证数据持久化${NC}"
VOLUME_INFO=$(podman volume inspect postgres_17_data --format "{{.Mountpoint}}" 2>/dev/null)
if [[ -n "$VOLUME_INFO" ]]; then
    echo -e "${GREEN}✅ 数据卷挂载点: $VOLUME_INFO${NC}"
else
    echo -e "${RED}❌ 数据卷未正确配置${NC}"
fi
echo ""

echo -e "${BLUE}🎉 PostgreSQL安全配置验证完成！${NC}"
echo -e "${GREEN}✅ 所有关键安全配置已验证${NC}"
echo ""
echo -e "${YELLOW}💡 建议:${NC}"
echo -e "   1. 生产环境启用SSL连接"
echo -e "   2. 定期更新密码"
echo -e "   3. 监控慢查询和连接数"
echo -e "   4. 定期备份数据"
echo -e "   5. 审查访问日志"
