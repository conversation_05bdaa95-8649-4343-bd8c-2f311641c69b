//! # PostgreSQL配置验证脚本
//!
//! 本脚本验证PostgreSQL连接池配置是否正确设置，包括：
//! 1. 环境变量配置检查
//! 2. 数据库连接URL验证
//! 3. 连接池参数验证
//! 4. 配置文件完整性检查

use std::env;
use std::fs;
use std::time::Duration;

/// 加载.env文件到环境变量
fn load_env_file() -> Result<(), String> {
    let env_file_path = ".env";
    if !std::path::Path::new(env_file_path).exists() {
        return Err(".env文件不存在".to_string());
    }

    let content = fs
        ::read_to_string(env_file_path)
        .map_err(|e| format!("无法读取.env文件: {}", e))?;

    for line in content.lines() {
        let line = line.trim();
        if line.is_empty() || line.starts_with('#') {
            continue;
        }

        if let Some(eq_pos) = line.find('=') {
            let key = line[..eq_pos].trim();
            let value = line[eq_pos + 1..].trim();
            unsafe {
                env::set_var(key, value);
            }
        }
    }

    println!("✅ .env文件已加载到环境变量");
    Ok(())
}

/// 验证环境变量配置
fn verify_environment_variables() -> Result<(), String> {
    println!("🔍 开始验证环境变量配置...");

    // 检查DATABASE_URL
    let database_url = env
        ::var("DATABASE_URL")
        .map_err(|_| "DATABASE_URL环境变量未设置".to_string())?;

    if database_url.is_empty() {
        return Err("DATABASE_URL不能为空".to_string());
    }

    println!("✅ DATABASE_URL: {}", database_url);

    // 验证是否为PostgreSQL URL
    if !database_url.starts_with("postgres://") && !database_url.starts_with("postgresql://") {
        println!("⚠️  警告: DATABASE_URL不是PostgreSQL格式，当前为: {}", database_url);
    } else {
        println!("✅ 数据库URL格式正确 (PostgreSQL)");
    }

    // 检查连接池配置
    let max_connections = env
        ::var("MAX_CONNECTIONS")
        .ok()
        .and_then(|s| s.parse::<u32>().ok())
        .unwrap_or(1000);

    let min_connections = env
        ::var("MIN_CONNECTIONS")
        .ok()
        .and_then(|s| s.parse::<u32>().ok())
        .unwrap_or(50);

    let connection_timeout = env
        ::var("CONNECTION_TIMEOUT")
        .ok()
        .and_then(|s| s.parse::<u64>().ok())
        .unwrap_or(30);

    let idle_timeout = env
        ::var("IDLE_TIMEOUT")
        .ok()
        .and_then(|s| s.parse::<u64>().ok())
        .unwrap_or(600);

    let acquire_timeout = env
        ::var("ACQUIRE_TIMEOUT")
        .ok()
        .and_then(|s| s.parse::<u64>().ok())
        .unwrap_or(30);

    println!("📊 连接池配置参数:");
    println!("   - 最大连接数: {}", max_connections);
    println!("   - 最小连接数: {}", min_connections);
    println!("   - 连接超时: {}秒", connection_timeout);
    println!("   - 空闲超时: {}秒", idle_timeout);
    println!("   - 获取超时: {}秒", acquire_timeout);

    // 验证配置合理性
    if max_connections >= 1000 {
        println!("✅ 最大连接数配置符合企业级要求 (>= 1000)");
    } else {
        println!("⚠️  最大连接数配置较低: {}", max_connections);
    }

    if min_connections >= 10 {
        println!("✅ 最小连接数配置合理 (>= 10)");
    } else {
        println!("⚠️  最小连接数配置较低: {}", min_connections);
    }

    if min_connections > max_connections {
        return Err("最小连接数不能大于最大连接数".to_string());
    }

    if connection_timeout >= 30 {
        println!("✅ 连接超时配置合理 (>= 30秒)");
    } else {
        println!("⚠️  连接超时配置较短: {}秒", connection_timeout);
    }

    if acquire_timeout >= 30 {
        println!("✅ 获取连接超时配置合理 (>= 30秒)");
    } else {
        println!("⚠️  获取连接超时配置较短: {}秒", acquire_timeout);
    }

    println!("🎉 环境变量配置验证完成！");
    Ok(())
}

/// 验证DragonflyDB缓存配置
fn verify_cache_configuration() -> Result<(), String> {
    println!("🔍 开始验证DragonflyDB缓存配置...");

    // 检查REDIS_URL
    let redis_url = env::var("REDIS_URL").unwrap_or_else(|_| "未设置".to_string());

    println!("📊 缓存配置:");
    println!("   - Redis URL: {}", redis_url);

    if redis_url != "未设置" {
        if redis_url.starts_with("redis://") {
            println!("✅ Redis URL格式正确");
        } else {
            println!("⚠️  Redis URL格式可能不正确: {}", redis_url);
        }
    } else {
        println!("⚠️  REDIS_URL环境变量未设置");
    }

    // 检查缓存相关配置
    let cache_ttl = env
        ::var("CACHE_TTL")
        .ok()
        .and_then(|s| s.parse::<u64>().ok())
        .unwrap_or(3600);

    let cache_prefix = env::var("CACHE_PREFIX").unwrap_or_else(|_| "axum_tutorial:".to_string());

    println!("   - 缓存TTL: {}秒", cache_ttl);
    println!("   - 缓存前缀: {}", cache_prefix);

    if cache_ttl >= 3600 {
        println!("✅ 缓存TTL配置合理 (>= 1小时)");
    } else {
        println!("⚠️  缓存TTL配置较短: {}秒", cache_ttl);
    }

    println!("🎉 缓存配置验证完成！");
    Ok(())
}

/// 验证.env文件存在性
fn verify_env_file() -> Result<(), String> {
    println!("🔍 开始验证.env文件...");

    let env_file_path = ".env";
    if std::path::Path::new(env_file_path).exists() {
        println!("✅ .env文件存在");

        // 读取.env文件内容
        match std::fs::read_to_string(env_file_path) {
            Ok(content) => {
                let lines: Vec<&str> = content.lines().collect();
                println!("📄 .env文件包含 {} 行配置", lines.len());

                // 检查关键配置项
                let required_keys = vec![
                    "DATABASE_URL",
                    "MAX_CONNECTIONS",
                    "MIN_CONNECTIONS",
                    "CONNECTION_TIMEOUT",
                    "IDLE_TIMEOUT",
                    "ACQUIRE_TIMEOUT"
                ];

                let mut missing_keys = Vec::new();
                for key in &required_keys {
                    if !content.contains(key) {
                        missing_keys.push(*key);
                    }
                }

                if missing_keys.is_empty() {
                    println!("✅ 所有必需的配置项都存在");
                } else {
                    println!("⚠️  缺少以下配置项: {:?}", missing_keys);
                }
            }
            Err(e) => {
                println!("⚠️  无法读取.env文件: {}", e);
            }
        }
    } else {
        println!("⚠️  .env文件不存在");
    }

    println!("🎉 .env文件验证完成！");
    Ok(())
}

/// 验证容器配置文件
fn verify_container_config() -> Result<(), String> {
    println!("🔍 开始验证容器配置文件...");

    let compose_file = "podman-compose.yml";
    if std::path::Path::new(compose_file).exists() {
        println!("✅ podman-compose.yml文件存在");

        match std::fs::read_to_string(compose_file) {
            Ok(content) => {
                // 检查PostgreSQL服务
                if content.contains("postgres:17") {
                    println!("✅ PostgreSQL 17服务配置存在");
                } else {
                    println!("⚠️  未找到PostgreSQL 17服务配置");
                }

                // 检查DragonflyDB服务
                if content.contains("dragonflydb/dragonfly") {
                    println!("✅ DragonflyDB服务配置存在");
                } else {
                    println!("⚠️  未找到DragonflyDB服务配置");
                }

                // 检查端口映射
                if content.contains("5432:5432") {
                    println!("✅ PostgreSQL端口映射配置正确");
                } else {
                    println!("⚠️  PostgreSQL端口映射可能不正确");
                }

                if content.contains("6379:6379") {
                    println!("✅ DragonflyDB端口映射配置正确");
                } else {
                    println!("⚠️  DragonflyDB端口映射可能不正确");
                }
            }
            Err(e) => {
                println!("⚠️  无法读取podman-compose.yml文件: {}", e);
            }
        }
    } else {
        println!("⚠️  podman-compose.yml文件不存在");
    }

    println!("🎉 容器配置文件验证完成！");
    Ok(())
}

/// 显示配置摘要
fn display_configuration_summary() {
    println!("{}", "=".repeat(60));
    println!("📋 PostgreSQL连接池配置摘要");
    println!("{}", "=".repeat(60));

    println!("🌍 环境变量:");
    println!(
        "   DATABASE_URL: {}",
        env::var("DATABASE_URL").unwrap_or_else(|_| "未设置".to_string())
    );
    println!(
        "   MAX_CONNECTIONS: {}",
        env::var("MAX_CONNECTIONS").unwrap_or_else(|_| "未设置".to_string())
    );
    println!(
        "   MIN_CONNECTIONS: {}",
        env::var("MIN_CONNECTIONS").unwrap_or_else(|_| "未设置".to_string())
    );
    println!(
        "   CONNECTION_TIMEOUT: {}",
        env::var("CONNECTION_TIMEOUT").unwrap_or_else(|_| "未设置".to_string())
    );
    println!(
        "   IDLE_TIMEOUT: {}",
        env::var("IDLE_TIMEOUT").unwrap_or_else(|_| "未设置".to_string())
    );
    println!(
        "   ACQUIRE_TIMEOUT: {}",
        env::var("ACQUIRE_TIMEOUT").unwrap_or_else(|_| "未设置".to_string())
    );
    println!(
        "   REDIS_URL: {}",
        env::var("REDIS_URL").unwrap_or_else(|_| "未设置".to_string())
    );

    println!("\n📁 配置文件:");
    println!("   .env: {}", if std::path::Path::new(".env").exists() { "存在" } else { "不存在" });
    println!("   podman-compose.yml: {}", if std::path::Path::new("podman-compose.yml").exists() {
        "存在"
    } else {
        "不存在"
    });

    println!("{}", "=".repeat(60));
}

/// 主验证函数
fn run_verification_suite() -> Result<(), String> {
    println!("🚀 开始PostgreSQL连接池配置验证套件...");
    println!("{}", "=".repeat(60));

    // 0. 加载.env文件
    load_env_file()?;
    println!("");

    // 1. 验证环境变量
    verify_environment_variables()?;
    println!("");

    // 2. 验证缓存配置
    verify_cache_configuration()?;
    println!("");

    // 3. 验证.env文件
    verify_env_file()?;
    println!("");

    // 4. 验证容器配置
    verify_container_config()?;
    println!("");

    // 5. 显示配置摘要
    display_configuration_summary();

    println!("🎉 PostgreSQL连接池配置验证套件执行完成！");
    println!("✅ 配置验证通过，可以继续进行数据库连接测试");

    Ok(())
}

/// 程序入口点
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔧 PostgreSQL连接池配置验证脚本启动");
    println!("📅 执行时间: {}", chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC"));
    println!("");

    // 执行验证套件
    match run_verification_suite() {
        Ok(()) => {
            println!("🎊 验证完成！PostgreSQL连接池配置正确。");
            println!(
                "💡 提示: 请确保PostgreSQL和DragonflyDB容器正在运行，然后可以进行实际连接测试。"
            );
            Ok(())
        }
        Err(e) => {
            eprintln!("💥 验证失败: {}", e);
            eprintln!("🔧 请检查环境变量配置和配置文件。");
            std::process::exit(1);
        }
    }
}
