//! # PostgreSQL连接池验证脚本
//!
//! 本脚本验证PostgreSQL连接池配置是否正确工作

use anyhow::Result;
use sea_orm::{ConnectOptions, ConnectionTrait, Database, DatabaseBackend, Statement};
use std::env;
use std::time::Duration;
use tracing::info;
use tracing_subscriber;

/// 验证基本数据库连接
async fn verify_basic_connection() -> Result<()> {
    info!("🔍 验证基本数据库连接...");

    let database_url = env::var("DATABASE_URL").unwrap_or_else(|_| {
        "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial".to_string()
    });

    // 创建连接选项
    let mut opt = ConnectOptions::new(database_url);
    opt.max_connections(100)
        .min_connections(5)
        .connect_timeout(Duration::from_secs(8))
        .acquire_timeout(Duration::from_secs(8))
        .idle_timeout(Duration::from_secs(8))
        .max_lifetime(Duration::from_secs(8));

    // 测试基本连接
    let db = Database::connect(opt).await?;
    info!("✅ 基本连接测试通过");

    // 测试简单查询
    let result = db
        .execute(Statement::from_string(
            DatabaseBackend::Postgres,
            "SELECT 1 as test_value".to_owned(),
        ))
        .await?;
    info!("✅ 简单查询测试通过，影响行数: {}", result.rows_affected());

    info!("🎉 基本连接功能验证完成！");
    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    tracing_subscriber::fmt()
        .with_env_filter("info")
        .with_target(false)
        .with_thread_ids(true)
        .with_file(true)
        .with_line_number(true)
        .init();

    info!("🚀 开始PostgreSQL连接池验证...");
    info!("");

    // 基本连接测试
    verify_basic_connection().await?;
    info!("");

    info!("{}", "=".repeat(60));
    info!("🎉 PostgreSQL连接池基本验证完成！");
    info!("✅ 基本连接测试通过");

    Ok(())
}
