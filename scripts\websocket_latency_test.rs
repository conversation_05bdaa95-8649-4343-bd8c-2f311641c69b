#!/usr/bin/env cargo +nightly -Zscript
//! WebSocket延迟测试脚本
//!
//! 这个脚本用于测试WebSocket连接的延迟性能，包括：
//! - 连接建立延迟
//! - 消息往返时间(RTT)
//! - 心跳延迟
//! - 并发连接性能
//!
//! 使用方法：
//! ```bash
//! cargo run --bin websocket_latency_test
//! ```

use std::{ sync::{ Arc, atomic::{ AtomicU64, Ordering } }, time::{ Duration, Instant } };
use tokio::{ net::TcpStream, sync::Mutex, time::{ sleep, timeout } };
use tokio_tungstenite::{ connect_async, tungstenite::Message, WebSocketStream, MaybeTlsStream };
use futures_util::{ SinkExt, StreamExt };
use serde_json::{ json, Value };
use anyhow::{ Result, Context };

/// WebSocket延迟测试配置
#[derive(Debug, Clone)]
pub struct LatencyTestConfig {
    /// 服务器URL
    pub server_url: String,
    /// 测试用户token
    pub auth_token: String,
    /// 连接超时时间（秒）
    pub connection_timeout: u64,
    /// 消息超时时间（秒）
    pub message_timeout: u64,
    /// 测试轮次
    pub test_rounds: usize,
    /// 并发连接数
    pub concurrent_connections: usize,
    /// 心跳间隔（秒）
    pub heartbeat_interval: u64,
}

impl Default for LatencyTestConfig {
    fn default() -> Self {
        Self {
            server_url: "ws://127.0.0.1:3000/ws".to_string(),
            auth_token: "test_token_for_latency_test".to_string(),
            connection_timeout: 10,
            message_timeout: 5,
            test_rounds: 10,
            concurrent_connections: 5,
            heartbeat_interval: 30,
        }
    }
}

/// 延迟测试统计
#[derive(Debug, Default)]
pub struct LatencyStats {
    /// 连接建立延迟（毫秒）
    pub connection_latencies: Vec<u64>,
    /// 消息往返延迟（毫秒）
    pub message_latencies: Vec<u64>,
    /// 心跳延迟（毫秒）
    pub heartbeat_latencies: Vec<u64>,
    /// 失败次数
    pub failures: AtomicU64,
    /// 成功次数
    pub successes: AtomicU64,
}

impl LatencyStats {
    /// 计算平均延迟
    pub fn average_latency(latencies: &[u64]) -> f64 {
        if latencies.is_empty() {
            0.0
        } else {
            (latencies.iter().sum::<u64>() as f64) / (latencies.len() as f64)
        }
    }

    /// 计算延迟百分位数
    pub fn percentile(latencies: &[u64], percentile: f64) -> u64 {
        if latencies.is_empty() {
            return 0;
        }

        let mut sorted = latencies.to_vec();
        sorted.sort_unstable();

        let index = ((((sorted.len() as f64) - 1.0) * percentile) / 100.0).round() as usize;
        sorted[index.min(sorted.len() - 1)]
    }

    /// 打印统计报告
    pub fn print_report(&self) {
        println!("\n=== WebSocket延迟测试报告 ===");

        // 连接延迟统计
        if !self.connection_latencies.is_empty() {
            println!("\n📡 连接建立延迟:");
            println!("  平均: {:.2}ms", Self::average_latency(&self.connection_latencies));
            println!("  P50: {}ms", Self::percentile(&self.connection_latencies, 50.0));
            println!("  P95: {}ms", Self::percentile(&self.connection_latencies, 95.0));
            println!("  P99: {}ms", Self::percentile(&self.connection_latencies, 99.0));
            println!("  最小: {}ms", self.connection_latencies.iter().min().unwrap_or(&0));
            println!("  最大: {}ms", self.connection_latencies.iter().max().unwrap_or(&0));
        }

        // 消息延迟统计
        if !self.message_latencies.is_empty() {
            println!("\n💬 消息往返延迟:");
            println!("  平均: {:.2}ms", Self::average_latency(&self.message_latencies));
            println!("  P50: {}ms", Self::percentile(&self.message_latencies, 50.0));
            println!("  P95: {}ms", Self::percentile(&self.message_latencies, 95.0));
            println!("  P99: {}ms", Self::percentile(&self.message_latencies, 99.0));
            println!("  最小: {}ms", self.message_latencies.iter().min().unwrap_or(&0));
            println!("  最大: {}ms", self.message_latencies.iter().max().unwrap_or(&0));
        }

        // 心跳延迟统计
        if !self.heartbeat_latencies.is_empty() {
            println!("\n💓 心跳延迟:");
            println!("  平均: {:.2}ms", Self::average_latency(&self.heartbeat_latencies));
            println!("  P50: {}ms", Self::percentile(&self.heartbeat_latencies, 50.0));
            println!("  P95: {}ms", Self::percentile(&self.heartbeat_latencies, 95.0));
            println!("  P99: {}ms", Self::percentile(&self.heartbeat_latencies, 99.0));
        }

        // 成功率统计
        let total = self.successes.load(Ordering::Relaxed) + self.failures.load(Ordering::Relaxed);
        let success_rate = if total > 0 {
            ((self.successes.load(Ordering::Relaxed) as f64) / (total as f64)) * 100.0
        } else {
            0.0
        };

        println!("\n📊 总体统计:");
        println!("  成功: {}", self.successes.load(Ordering::Relaxed));
        println!("  失败: {}", self.failures.load(Ordering::Relaxed));
        println!("  成功率: {:.2}%", success_rate);
        println!("  总测试数: {}", total);
    }
}

/// WebSocket延迟测试器
pub struct WebSocketLatencyTester {
    config: LatencyTestConfig,
    stats: Arc<Mutex<LatencyStats>>,
}

impl WebSocketLatencyTester {
    /// 创建新的延迟测试器
    pub fn new(config: LatencyTestConfig) -> Self {
        Self {
            config,
            stats: Arc::new(Mutex::new(LatencyStats::default())),
        }
    }

    /// 运行完整的延迟测试套件
    pub async fn run_full_test_suite(&self) -> Result<()> {
        println!("🚀 开始WebSocket延迟测试...");
        println!("服务器: {}", self.config.server_url);
        println!("测试轮次: {}", self.config.test_rounds);
        println!("并发连接: {}", self.config.concurrent_connections);

        // 1. 单连接延迟测试
        println!("\n1️⃣ 单连接延迟测试");
        self.test_single_connection_latency().await?;

        // 2. 消息往返延迟测试
        println!("\n2️⃣ 消息往返延迟测试");
        self.test_message_round_trip_latency().await?;

        // 3. 心跳延迟测试
        println!("\n3️⃣ 心跳延迟测试");
        self.test_heartbeat_latency().await?;

        // 4. 并发连接延迟测试
        println!("\n4️⃣ 并发连接延迟测试");
        self.test_concurrent_connection_latency().await?;

        // 打印最终报告
        let stats = self.stats.lock().await;
        stats.print_report();

        Ok(())
    }

    /// 测试单连接建立延迟
    async fn test_single_connection_latency(&self) -> Result<()> {
        for round in 1..=self.config.test_rounds {
            print!("  轮次 {}/{}: ", round, self.config.test_rounds);

            let start_time = Instant::now();

            match self.establish_connection().await {
                Ok(_) => {
                    let latency = start_time.elapsed().as_millis() as u64;
                    println!("{}ms ✅", latency);

                    let mut stats = self.stats.lock().await;
                    stats.connection_latencies.push(latency);
                    stats.successes.fetch_add(1, Ordering::Relaxed);
                }
                Err(e) => {
                    println!("失败 ❌ - {}", e);
                    let mut stats = self.stats.lock().await;
                    stats.failures.fetch_add(1, Ordering::Relaxed);
                }
            }

            // 短暂休息避免过载
            sleep(Duration::from_millis(100)).await;
        }

        Ok(())
    }

    /// 建立WebSocket连接
    async fn establish_connection(&self) -> Result<WebSocketStream<MaybeTlsStream<TcpStream>>> {
        let url = format!("{}?token={}", self.config.server_url, self.config.auth_token);

        let connection_future = connect_async(&url);
        let timeout_duration = Duration::from_secs(self.config.connection_timeout);

        match timeout(timeout_duration, connection_future).await {
            Ok(Ok((ws_stream, _))) => Ok(ws_stream),
            Ok(Err(e)) => Err(anyhow::anyhow!("WebSocket连接失败: {}", e)),
            Err(_) => Err(anyhow::anyhow!("连接超时")),
        }
    }

    /// 测试消息往返延迟
    async fn test_message_round_trip_latency(&self) -> Result<()> {
        for round in 1..=self.config.test_rounds {
            print!("  轮次 {}/{}: ", round, self.config.test_rounds);

            match self.measure_message_round_trip().await {
                Ok(latency) => {
                    println!("{}ms ✅", latency);

                    let mut stats = self.stats.lock().await;
                    stats.message_latencies.push(latency);
                    stats.successes.fetch_add(1, Ordering::Relaxed);
                }
                Err(e) => {
                    println!("失败 ❌ - {}", e);
                    let mut stats = self.stats.lock().await;
                    stats.failures.fetch_add(1, Ordering::Relaxed);
                }
            }

            sleep(Duration::from_millis(100)).await;
        }

        Ok(())
    }

    /// 测量消息往返时间
    async fn measure_message_round_trip(&self) -> Result<u64> {
        let mut ws_stream = self.establish_connection().await?;

        let test_message =
            json!({
            "type": "ping",
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "test_id": uuid::Uuid::new_v4().to_string()
        });

        let start_time = Instant::now();

        // 发送消息
        ws_stream
            .send(Message::Text(test_message.to_string().into())).await
            .context("发送测试消息失败")?;

        // 等待响应
        let timeout_duration = Duration::from_secs(self.config.message_timeout);
        match timeout(timeout_duration, ws_stream.next()).await {
            Ok(Some(Ok(_))) => {
                let latency = start_time.elapsed().as_millis() as u64;
                Ok(latency)
            }
            Ok(Some(Err(e))) => Err(anyhow::anyhow!("接收响应失败: {}", e)),
            Ok(None) => Err(anyhow::anyhow!("连接意外关闭")),
            Err(_) => Err(anyhow::anyhow!("消息响应超时")),
        }
    }

    /// 测试心跳延迟
    async fn test_heartbeat_latency(&self) -> Result<()> {
        println!("  测试心跳延迟...");

        for round in 1..=5 {
            // 心跳测试轮次较少
            print!("  心跳轮次 {}/5: ", round);

            match self.measure_heartbeat_latency().await {
                Ok(latency) => {
                    println!("{}ms ✅", latency);

                    let mut stats = self.stats.lock().await;
                    stats.heartbeat_latencies.push(latency);
                    stats.successes.fetch_add(1, Ordering::Relaxed);
                }
                Err(e) => {
                    println!("失败 ❌ - {}", e);
                    let mut stats = self.stats.lock().await;
                    stats.failures.fetch_add(1, Ordering::Relaxed);
                }
            }

            sleep(Duration::from_secs(1)).await;
        }

        Ok(())
    }

    /// 测量心跳延迟
    async fn measure_heartbeat_latency(&self) -> Result<u64> {
        let mut ws_stream = self.establish_connection().await?;

        let ping_message =
            json!({
            "type": "ping",
            "timestamp": chrono::Utc::now().to_rfc3339()
        });

        let start_time = Instant::now();

        // 发送ping
        ws_stream
            .send(Message::Text(ping_message.to_string().into())).await
            .context("发送ping失败")?;

        // 等待pong响应
        let timeout_duration = Duration::from_secs(self.config.message_timeout);
        match timeout(timeout_duration, ws_stream.next()).await {
            Ok(Some(Ok(Message::Text(response)))) => {
                if let Ok(parsed) = serde_json::from_str::<Value>(&response) {
                    if parsed["type"] == "pong" {
                        let latency = start_time.elapsed().as_millis() as u64;
                        return Ok(latency);
                    }
                }
                Err(anyhow::anyhow!("收到非pong响应"))
            }
            Ok(Some(Ok(_))) => Err(anyhow::anyhow!("收到非文本消息")),
            Ok(Some(Err(e))) => Err(anyhow::anyhow!("接收pong失败: {}", e)),
            Ok(None) => Err(anyhow::anyhow!("连接意外关闭")),
            Err(_) => Err(anyhow::anyhow!("pong响应超时")),
        }
    }

    /// 测试并发连接延迟
    async fn test_concurrent_connection_latency(&self) -> Result<()> {
        println!("  测试{}个并发连接...", self.config.concurrent_connections);

        let mut handles = Vec::new();
        let start_time = Instant::now();

        for i in 0..self.config.concurrent_connections {
            let config = self.config.clone();
            let stats = Arc::clone(&self.stats);

            let handle = tokio::spawn(async move {
                let connection_start = Instant::now();

                let url = format!(
                    "{}?token={}&conn_id={}",
                    config.server_url,
                    config.auth_token,
                    i
                );

                match connect_async(&url).await {
                    Ok(_) => {
                        let latency = connection_start.elapsed().as_millis() as u64;

                        let mut stats_guard = stats.lock().await;
                        stats_guard.connection_latencies.push(latency);
                        stats_guard.successes.fetch_add(1, Ordering::Relaxed);

                        println!("    连接 {} 建立: {}ms ✅", i, latency);
                    }
                    Err(e) => {
                        let mut stats_guard = stats.lock().await;
                        stats_guard.failures.fetch_add(1, Ordering::Relaxed);

                        println!("    连接 {} 失败: {} ❌", i, e);
                    }
                }
            });

            handles.push(handle);
        }

        // 等待所有连接完成
        for handle in handles {
            let _ = handle.await;
        }

        let total_time = start_time.elapsed();
        println!("  并发连接总耗时: {}ms", total_time.as_millis());

        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    println!("🔧 WebSocket延迟测试工具");
    println!("=======================");

    // 创建测试配置
    let config = LatencyTestConfig::default();

    // 创建测试器
    let tester = WebSocketLatencyTester::new(config);

    // 运行测试套件
    if let Err(e) = tester.run_full_test_suite().await {
        eprintln!("❌ 测试失败: {}", e);
        std::process::exit(1);
    }

    println!("\n✅ 延迟测试完成!");

    Ok(())
}
