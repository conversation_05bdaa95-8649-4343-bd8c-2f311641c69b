[package]
name = "axum-server"
version = "0.1.0"
edition = "2024"
license = "MIT OR Apache-2.0"
authors = ["Rust学习者"]
description = "主应用服务器"

[[bin]]
name = "axum-server"
path = "src/main.rs"

[dependencies]
# 内部依赖
app_common = { workspace = true }
app_domain = { workspace = true }
app_application = { workspace = true }
app_infrastructure = { workspace = true }
app_interfaces = { workspace = true }
migration = { path = "../migration" }

# Axum Web 框架
axum = { workspace = true }

# Tokio 异步运行时
tokio = { workspace = true }

# 异步trait
async-trait = { workspace = true }

# 序列化
serde = { workspace = true }
serde_json = { workspace = true }

# 时间处理
chrono = { workspace = true }

# UUID
uuid = { workspace = true }

# 数据库ORM
sea-orm = { workspace = true }

# HTTP 工具库
tower = { version = "0.5.2", features = ["util", "timeout"] }
tower-http = { version = "0.6.6", features = ["trace", "cors", "fs", "timeout"] }

# 日志与跟踪
tracing = { workspace = true }
tracing-subscriber = { version = "0.3", features = ["env-filter", "json", "time", "local-time"] }
tracing-error = "0.2"
tracing-appender = "0.2"

# 环境变量
dotenvy = "0.15"

# 错误处理
anyhow = { workspace = true }

# 同步原语
parking_lot = "0.12"

# 性能监控
metrics = { workspace = true }
metrics-exporter-prometheus = { workspace = true }
sysinfo = { workspace = true }

# WebSocket 支持
futures-util = "0.3"

# 其他工具
bytes = "1.10.1"
flate2 = "1.1.2"
serde_urlencoded = "0.7"

# 测试工具依赖（可选）
tempfile = { version = "3.8", optional = true }
reqwest = { version = "0.12.4", optional = true }
tokio-tungstenite = { version = "0.23", optional = true }
url = { version = "2.5", optional = true }

[dev-dependencies]
# 测试工具
axum-test = "17.3"
serde_json = "1.0"
reqwest = { version = "0.12.4", features = ["json"] }
tokio = { version = "1.45.1", features = ["full"] }

[features]
default = []
testing = ["tempfile", "reqwest", "tokio-tungstenite", "url"]
