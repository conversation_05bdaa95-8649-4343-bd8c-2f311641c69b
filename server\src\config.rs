//! # 配置管理模块
//!
//! 应用程序配置加载和管理

use anyhow::Result;

/// 应用程序配置结构体
#[derive(<PERSON><PERSON>, Debug)]
pub struct AppConfig {
    /// HTTP 服务器监听地址
    pub http_addr: std::net::SocketAddr,
    /// 数据库连接 URL
    pub database_url: String,
    /// JWT 签名密钥
    pub jwt_secret: String,
}

impl AppConfig {
    /// 从环境变量加载配置
    pub fn from_env() -> Result<Self> {
        dotenvy::dotenv().ok();

        let http_addr = std::env::var("HTTP_ADDR")
            .unwrap_or_else(|_| "127.0.0.1:3000".to_string())
            .parse()?;

        let database_url =
            std::env::var("DATABASE_URL").unwrap_or_else(|_| "sqlite:task_manager.db".to_string());

        let jwt_secret = std::env::var("JWT_SECRET")
            .unwrap_or_else(|_| "your-secret-key-change-in-production".to_string());

        Ok(Self {
            http_addr,
            database_url,
            jwt_secret,
        })
    }
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            http_addr: "127.0.0.1:3000"
                .parse()
                .expect("Invalid default HTTP address"),
            database_url: "sqlite::memory:".to_string(),
            jwt_secret: "test-secret-key".to_string(),
        }
    }
}
