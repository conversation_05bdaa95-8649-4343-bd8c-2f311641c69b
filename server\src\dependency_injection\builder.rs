//! # 服务容器构建器
//!
//! 提供流式API来构建服务容器，支持灵活的服务配置和注册

use anyhow::{Result as AnyhowResult, anyhow};
use std::sync::Arc;
use tracing::info;

use super::{
    ServiceContainerBuilder, ServiceFactory,
    container::{DefaultServiceContainer, DefaultServiceFactory},
};

// 导入领域服务
use app_domain::services::{ChatDomainService, TaskDomainService, UserDomainService};

// 导入仓库接口
use app_domain::repositories::{
    ChatRepositoryContract, TaskRepositoryContract, UserRepositoryContract,
};

// 导入WebSocket服务
use app_domain::websocket::{
    WebSocketConnectionService, WebSocketMessageService, WebSocketStatsService,
};

use sea_orm::DatabaseConnection;

/// 默认服务容器构建器
///
/// 提供流式API来构建服务容器，确保所有必需的服务都被正确注册
pub struct DefaultServiceContainerBuilder {
    /// 数据库连接
    database: Option<Arc<DatabaseConnection>>,
    /// 用户仓库
    user_repository: Option<Arc<dyn UserRepositoryContract>>,
    /// 用户领域服务
    user_domain_service: Option<Arc<dyn UserDomainService>>,
    /// 任务仓库
    task_repository: Option<Arc<dyn TaskRepositoryContract>>,
    /// 任务领域服务
    task_domain_service: Option<Arc<dyn TaskDomainService>>,
    /// 聊天仓库
    chat_repository: Option<Arc<dyn ChatRepositoryContract>>,
    /// 聊天领域服务
    chat_domain_service: Option<Arc<dyn ChatDomainService>>,
    /// WebSocket连接服务
    websocket_connection_service: Option<Arc<dyn WebSocketConnectionService>>,
    /// WebSocket消息服务
    websocket_message_service: Option<Arc<dyn WebSocketMessageService>>,
    /// WebSocket统计服务
    websocket_stats_service: Option<Arc<dyn WebSocketStatsService>>,
    /// 应用配置
    config: Option<crate::config::AppConfig>,
    /// 服务工厂
    factory: Box<dyn ServiceFactory>,
}

impl Default for DefaultServiceContainerBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl DefaultServiceContainerBuilder {
    /// 创建新的构建器实例
    pub fn new() -> Self {
        info!("🏗️ 创建服务容器构建器");
        Self {
            database: None,
            user_repository: None,
            user_domain_service: None,
            task_repository: None,
            task_domain_service: None,
            chat_repository: None,
            chat_domain_service: None,
            websocket_connection_service: None,
            websocket_message_service: None,
            websocket_stats_service: None,
            config: None,
            factory: Box::new(DefaultServiceFactory),
        }
    }

    /// 设置应用配置
    pub fn with_config(mut self, config: crate::config::AppConfig) -> Self {
        self.config = Some(config);
        self
    }

    /// 设置自定义服务工厂
    #[allow(dead_code)]
    pub fn with_factory(mut self, factory: Box<dyn ServiceFactory>) -> Self {
        self.factory = factory;
        self
    }

    /// 自动创建所有服务（便捷方法）
    ///
    /// 这个方法会自动创建所有必需的仓库、领域服务和WebSocket服务
    /// 适用于标准的应用启动流程
    pub fn with_all_services(mut self) -> Self {
        info!("🏗️ 自动创建所有服务");

        // 确保数据库连接已设置
        let database = self
            .database
            .as_ref()
            .expect("数据库连接必须在调用with_all_services之前设置");

        // 创建仓库层
        use app_infrastructure::{ChatRepository, TaskRepository, UserRepository};
        let user_repository: Arc<dyn UserRepositoryContract> =
            Arc::new(UserRepository::from_arc(database.clone()));
        let task_repository: Arc<dyn TaskRepositoryContract> =
            Arc::new(TaskRepository::from_arc(database.clone()));
        let chat_repository: Arc<dyn ChatRepositoryContract> =
            Arc::new(ChatRepository::from_arc(database.clone()));

        // 创建领域服务层（使用真正的实现）
        use super::container::EmptyChatDomainService;
        use app_domain::services::{
            chat_service::ChatDomainService, // 使用trait，因为还没有具体实现
            task_service::TaskDomainServiceImpl,
            user_service::UserDomainServiceImpl,
        }; // 聊天服务暂时使用空实现

        // 创建事件发布器
        use app_domain::events::{EventPublisherType, InMemoryEventPublisher};
        let event_publisher: Arc<EventPublisherType> =
            Arc::new(EventPublisherType::InMemory(InMemoryEventPublisher::new()));

        let user_domain_service: Arc<dyn UserDomainService> = Arc::new(UserDomainServiceImpl::new(
            user_repository.clone(),
            event_publisher.clone(),
        ));
        let task_domain_service: Arc<dyn TaskDomainService> = Arc::new(TaskDomainServiceImpl::new(
            task_repository.clone(),
            user_repository.clone(),
            event_publisher.clone(),
        ));
        let chat_domain_service: Arc<dyn ChatDomainService> = Arc::new(EmptyChatDomainService);

        // 创建WebSocket服务
        use app_infrastructure::websocket::{
            WebSocketConnectionManager, WebSocketMessageDistributor, WebSocketStatsCollector,
        };
        let connection_manager = Arc::new(WebSocketConnectionManager::new());
        let connection_service: Arc<dyn WebSocketConnectionService> = connection_manager.clone();
        let message_service: Arc<dyn WebSocketMessageService> =
            Arc::new(WebSocketMessageDistributor::new(connection_manager.clone()));
        let stats_service: Arc<dyn WebSocketStatsService> =
            Arc::new(WebSocketStatsCollector::new(connection_manager.clone()));

        // 设置所有服务
        self.user_repository = Some(user_repository);
        self.user_domain_service = Some(user_domain_service);
        self.task_repository = Some(task_repository);
        self.task_domain_service = Some(task_domain_service);
        self.chat_repository = Some(chat_repository);
        self.chat_domain_service = Some(chat_domain_service);
        self.websocket_connection_service = Some(connection_service);
        self.websocket_message_service = Some(message_service);
        self.websocket_stats_service = Some(stats_service);

        info!("✅ 所有服务创建完成");
        self
    }

    /// 验证所有必需的依赖是否已设置
    fn validate_dependencies(&self) -> AnyhowResult<()> {
        if self.database.is_none() {
            return Err(anyhow!("数据库连接未设置"));
        }

        if self.user_repository.is_none() || self.user_domain_service.is_none() {
            return Err(anyhow!("用户服务依赖未完整设置"));
        }

        if self.task_repository.is_none() || self.task_domain_service.is_none() {
            return Err(anyhow!("任务服务依赖未完整设置"));
        }

        if self.chat_repository.is_none() || self.chat_domain_service.is_none() {
            return Err(anyhow!("聊天服务依赖未完整设置"));
        }

        if self.websocket_connection_service.is_none()
            || self.websocket_message_service.is_none()
            || self.websocket_stats_service.is_none()
        {
            return Err(anyhow!("WebSocket服务依赖未完整设置"));
        }

        if self.config.is_none() {
            return Err(anyhow!("应用配置未设置"));
        }

        Ok(())
    }
}

impl ServiceContainerBuilder for DefaultServiceContainerBuilder {
    type Container = DefaultServiceContainer;

    fn with_database(mut self, database: Arc<DatabaseConnection>) -> Self {
        info!("🗄️ 设置数据库连接");
        self.database = Some(database);
        self
    }

    fn with_user_service(
        mut self,
        repository: Arc<dyn UserRepositoryContract>,
        domain_service: Arc<dyn UserDomainService>,
    ) -> Self {
        info!("👤 设置用户服务");
        self.user_repository = Some(repository);
        self.user_domain_service = Some(domain_service);
        self
    }

    fn with_task_service(
        mut self,
        repository: Arc<dyn TaskRepositoryContract>,
        domain_service: Arc<dyn TaskDomainService>,
    ) -> Self {
        info!("📋 设置任务服务");
        self.task_repository = Some(repository);
        self.task_domain_service = Some(domain_service);
        self
    }

    fn with_chat_service(
        mut self,
        repository: Arc<dyn ChatRepositoryContract>,
        domain_service: Arc<dyn ChatDomainService>,
    ) -> Self {
        info!("💬 设置聊天服务");
        self.chat_repository = Some(repository);
        self.chat_domain_service = Some(domain_service);
        self
    }

    fn with_websocket_service(
        mut self,
        connection_service: Arc<dyn WebSocketConnectionService>,
        message_service: Arc<dyn WebSocketMessageService>,
        stats_service: Arc<dyn WebSocketStatsService>,
    ) -> Self {
        info!("🔌 设置WebSocket服务");
        self.websocket_connection_service = Some(connection_service);
        self.websocket_message_service = Some(message_service);
        self.websocket_stats_service = Some(stats_service);
        self
    }

    fn build(self) -> AnyhowResult<Self::Container> {
        info!("🔨 构建服务容器");

        // 验证依赖
        self.validate_dependencies()?;

        // 创建应用服务
        let user_repository = self.user_repository.unwrap();
        let user_service = self
            .factory
            .create_user_service(user_repository.clone(), self.user_domain_service.unwrap())?;

        let task_service = self.factory.create_task_service(
            self.task_repository.unwrap(),
            self.task_domain_service.unwrap(),
        )?;

        let chat_service = self.factory.create_chat_service(
            self.chat_repository.unwrap(),
            self.chat_domain_service.unwrap(),
            user_repository.clone(),
        )?;

        let websocket_service = self.factory.create_websocket_service(
            self.websocket_connection_service.unwrap(),
            self.websocket_message_service.unwrap(),
            self.websocket_stats_service.unwrap(),
        )?;

        // 创建服务容器
        let container = DefaultServiceContainer::new(
            user_service,
            task_service,
            chat_service,
            websocket_service,
            self.database.unwrap(),
            self.config.unwrap(),
        );

        info!("✅ 服务容器构建完成");
        Ok(container)
    }
}
