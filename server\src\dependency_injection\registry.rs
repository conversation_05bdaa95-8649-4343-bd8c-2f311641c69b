//! # 声明式依赖配置注册器
//!
//! 提供声明式的服务注册和配置方式，简化依赖注入的使用
//! 支持自动服务发现和注册，减少样板代码
//!
//! ## 使用示例
//!
//! ```rust
//! use server::dependency_injection::ServiceRegistry;
//!
//! // 声明式服务注册 - 使用trait和方法链
//! let provider = ServiceRegistry::new()
//!     .with_database(database)
//!     .with_config(config)
//!     .auto_register()
//!     .build()?;
//!
//! // 或者使用配置构建器模式
//! let provider = ServiceRegistry::configure()
//!     .database(database)
//!     .config(config)
//!     .enable_auto_registration()
//!     .create_provider()?;
//! ```

use crate::config::AppConfig;
use anyhow::{Result as AnyhowResult, anyhow};
use sea_orm::DatabaseConnection;
use std::sync::Arc;
use tracing::info;

// 导入应用服务接口
use app_application::{
    ChatApplicationService, TaskApplicationService, UserApplicationService,
    websocket_service::WebSocketApplicationService,
};

// 导入基础设施层
use app_infrastructure::{ChatRepository, TaskRepository, UserRepository};

// 导入WebSocket基础设施
use app_infrastructure::websocket::{
    WebSocketConnectionManager, WebSocketMessageDistributor, WebSocketStatsCollector,
};

// 导入领域服务trait
use app_domain::services::{ChatDomainService, TaskDomainService, UserDomainService};

// 导入WebSocket领域服务trait
use app_domain::websocket::{
    WebSocketConnectionService, WebSocketMessageService, WebSocketStatsService,
};

// 导入仓库契约
use app_domain::repositories::{
    ChatRepositoryContract, TaskRepositoryContract, UserRepositoryContract,
};

/// 声明式服务注册器配置trait
///
/// 定义服务注册的核心配置接口
pub trait ServiceRegistryConfig {
    /// 设置数据库连接
    fn with_database(self, database: Arc<DatabaseConnection>) -> Self;

    /// 设置应用配置
    fn with_config(self, config: AppConfig) -> Self;

    /// 启用自动服务注册
    fn auto_register(self) -> Self;

    /// 构建服务提供者
    fn build(self) -> AnyhowResult<ServiceProvider>;
}

/// 声明式服务注册器构建器trait
///
/// 提供替代的构建器模式API
pub trait ServiceRegistryBuilder {
    /// 设置数据库连接
    fn database(self, database: Arc<DatabaseConnection>) -> Self;

    /// 设置应用配置
    fn config(self, config: AppConfig) -> Self;

    /// 启用自动服务注册
    fn enable_auto_registration(self) -> Self;

    /// 创建服务提供者
    fn create_provider(self) -> AnyhowResult<ServiceProvider>;
}

/// 声明式服务注册器
///
/// 提供流畅的API来注册和配置服务，支持自动服务发现
/// 使用trait和方法链而非宏来实现声明式配置
#[allow(dead_code)]
pub struct ServiceRegistry {
    /// 数据库连接
    database: Option<Arc<DatabaseConnection>>,
    /// 应用配置
    config: Option<AppConfig>,
    /// 是否已自动注册服务
    auto_registered: bool,
}

#[allow(dead_code)]
impl ServiceRegistry {
    /// 创建新的服务注册器
    pub fn new() -> Self {
        info!("🏗️ 创建声明式服务注册器");
        Self {
            database: None,
            config: None,
            auto_registered: false,
        }
    }

    /// 创建配置构建器
    pub fn configure() -> ServiceRegistryConfigBuilder {
        info!("🔧 创建服务注册器配置构建器");
        ServiceRegistryConfigBuilder::new()
    }

    /// 验证配置完整性
    fn validate_configuration(&self) -> AnyhowResult<()> {
        if self.database.is_none() {
            return Err(anyhow!("数据库连接未配置，请先调用 with_database()"));
        }
        if self.config.is_none() {
            return Err(anyhow!("应用配置未配置，请先调用 with_config()"));
        }
        if !self.auto_registered {
            return Err(anyhow!("服务未启用自动注册，请先调用 auto_register()"));
        }
        Ok(())
    }
}

impl Default for ServiceRegistry {
    fn default() -> Self {
        Self::new()
    }
}

// 实现ServiceRegistryConfig trait
impl ServiceRegistryConfig for ServiceRegistry {
    fn with_database(mut self, database: Arc<DatabaseConnection>) -> Self {
        info!("🗄️ 配置数据库连接");
        self.database = Some(database);
        self
    }

    fn with_config(mut self, config: AppConfig) -> Self {
        info!("⚙️ 配置应用设置");
        self.config = Some(config);
        self
    }

    fn auto_register(mut self) -> Self {
        info!("🔍 启用自动服务注册");
        self.auto_registered = true;
        self
    }

    fn build(self) -> AnyhowResult<ServiceProvider> {
        info!("🔨 构建声明式服务提供者");

        // 验证配置完整性
        self.validate_configuration()?;

        // 获取已验证的配置
        let database = self.database.unwrap();
        let config = self.config.unwrap();

        // 自动创建所有服务
        let provider = ServiceProvider::create_with_auto_registration(database, config)?;

        info!("✅ 声明式服务提供者构建完成");
        Ok(provider)
    }
}

/// 服务注册器配置构建器
///
/// 提供替代的构建器模式API，使用不同的方法名称
#[allow(dead_code)]
pub struct ServiceRegistryConfigBuilder {
    /// 数据库连接
    database: Option<Arc<DatabaseConnection>>,
    /// 应用配置
    config: Option<AppConfig>,
    /// 是否已自动注册服务
    auto_registration_enabled: bool,
}

#[allow(dead_code)]
impl ServiceRegistryConfigBuilder {
    /// 创建新的配置构建器
    pub fn new() -> Self {
        Self {
            database: None,
            config: None,
            auto_registration_enabled: false,
        }
    }

    /// 验证配置完整性
    fn validate_configuration(&self) -> AnyhowResult<()> {
        if self.database.is_none() {
            return Err(anyhow!("数据库连接未配置，请先调用 database()"));
        }
        if self.config.is_none() {
            return Err(anyhow!("应用配置未配置，请先调用 config()"));
        }
        if !self.auto_registration_enabled {
            return Err(anyhow!(
                "服务未启用自动注册，请先调用 enable_auto_registration()"
            ));
        }
        Ok(())
    }
}

impl Default for ServiceRegistryConfigBuilder {
    fn default() -> Self {
        Self::new()
    }
}

// 实现ServiceRegistryBuilder trait
impl ServiceRegistryBuilder for ServiceRegistryConfigBuilder {
    fn database(mut self, database: Arc<DatabaseConnection>) -> Self {
        info!("🗄️ 设置数据库连接");
        self.database = Some(database);
        self
    }

    fn config(mut self, config: AppConfig) -> Self {
        info!("⚙️ 设置应用配置");
        self.config = Some(config);
        self
    }

    fn enable_auto_registration(mut self) -> Self {
        info!("🔍 启用自动服务注册");
        self.auto_registration_enabled = true;
        self
    }

    fn create_provider(self) -> AnyhowResult<ServiceProvider> {
        info!("🏭 创建服务提供者");

        // 验证配置完整性
        self.validate_configuration()?;

        // 获取已验证的配置
        let database = self.database.unwrap();
        let config = self.config.unwrap();

        // 自动创建所有服务
        let provider = ServiceProvider::create_with_auto_registration(database, config)?;

        info!("✅ 服务提供者创建完成");
        Ok(provider)
    }
}

/// 声明式服务提供者
///
/// 提供类型安全的服务解析功能，支持可选和必需的服务获取
#[allow(dead_code)]
pub struct ServiceProvider {
    /// 用户应用服务
    user_service: Option<Arc<dyn UserApplicationService>>,
    /// 任务应用服务
    task_service: Option<Arc<dyn TaskApplicationService>>,
    /// 聊天应用服务
    chat_service: Option<Arc<dyn ChatApplicationService>>,
    /// WebSocket应用服务
    websocket_service: Option<Arc<dyn WebSocketApplicationService>>,
    /// 数据库连接
    database: Arc<DatabaseConnection>,
}

#[allow(dead_code)]
impl ServiceProvider {
    /// 通过自动注册创建服务提供者
    pub(crate) fn create_with_auto_registration(
        database: Arc<DatabaseConnection>,
        _config: AppConfig,
    ) -> AnyhowResult<Self> {
        info!("🏭 开始自动创建所有服务");

        // 创建仓库层
        let user_repository: Arc<dyn UserRepositoryContract> =
            Arc::new(UserRepository::from_arc(database.clone()));
        let task_repository: Arc<dyn TaskRepositoryContract> =
            Arc::new(TaskRepository::from_arc(database.clone()));
        let chat_repository: Arc<dyn ChatRepositoryContract> =
            Arc::new(ChatRepository::from_arc(database.clone()));

        // 创建领域服务层（使用空实现）
        use super::container::{
            EmptyChatDomainService, EmptyTaskDomainService, EmptyUserDomainService,
        };
        let user_domain_service: Arc<dyn UserDomainService> = Arc::new(EmptyUserDomainService);
        let task_domain_service: Arc<dyn TaskDomainService> = Arc::new(EmptyTaskDomainService);
        let chat_domain_service: Arc<dyn ChatDomainService> = Arc::new(EmptyChatDomainService);

        // 创建WebSocket服务层
        let websocket_connection_manager = Arc::new(WebSocketConnectionManager::new());
        let websocket_connection_service: Arc<dyn WebSocketConnectionService> =
            websocket_connection_manager.clone();
        let websocket_message_service: Arc<dyn WebSocketMessageService> = Arc::new(
            WebSocketMessageDistributor::new(websocket_connection_manager.clone()),
        );
        let websocket_stats_service: Arc<dyn WebSocketStatsService> = Arc::new(
            WebSocketStatsCollector::new(websocket_connection_manager.clone()),
        );

        // 创建应用服务层
        use app_application::{
            ChatApplicationServiceImpl, TaskApplicationServiceImpl, UserApplicationServiceImpl,
            WebSocketApplicationServiceImpl,
        };

        let user_service: Arc<dyn UserApplicationService> = Arc::new(
            UserApplicationServiceImpl::new(user_repository.clone(), user_domain_service),
        );

        let task_service: Arc<dyn TaskApplicationService> = Arc::new(
            TaskApplicationServiceImpl::new(task_repository, task_domain_service),
        );

        let chat_service: Arc<dyn ChatApplicationService> =
            Arc::new(ChatApplicationServiceImpl::new(
                chat_repository,
                chat_domain_service,
                user_repository.clone(),
            ));

        let websocket_service: Arc<dyn WebSocketApplicationService> =
            Arc::new(WebSocketApplicationServiceImpl::new(
                websocket_connection_service,
                websocket_message_service,
                websocket_stats_service,
            ));

        info!("✅ 所有服务自动创建完成");

        Ok(Self {
            user_service: Some(user_service),
            task_service: Some(task_service),
            chat_service: Some(chat_service),
            websocket_service: Some(websocket_service),
            database,
        })
    }

    /// 获取用户服务（可选）
    pub fn get_user_service(&self) -> Option<Arc<dyn UserApplicationService>> {
        self.user_service.clone()
    }

    /// 获取任务服务（可选）
    pub fn get_task_service(&self) -> Option<Arc<dyn TaskApplicationService>> {
        self.task_service.clone()
    }

    /// 获取聊天服务（可选）
    pub fn get_chat_service(&self) -> Option<Arc<dyn ChatApplicationService>> {
        self.chat_service.clone()
    }

    /// 获取WebSocket服务（可选）
    pub fn get_websocket_service(&self) -> Option<Arc<dyn WebSocketApplicationService>> {
        self.websocket_service.clone()
    }

    /// 获取数据库连接
    pub fn get_database(&self) -> Arc<DatabaseConnection> {
        self.database.clone()
    }

    /// 获取用户服务（必需）
    pub fn require_user_service(&self) -> AnyhowResult<Arc<dyn UserApplicationService>> {
        self.user_service
            .clone()
            .ok_or_else(|| anyhow!("用户服务未注册"))
    }

    /// 获取任务服务（必需）
    pub fn require_task_service(&self) -> AnyhowResult<Arc<dyn TaskApplicationService>> {
        self.task_service
            .clone()
            .ok_or_else(|| anyhow!("任务服务未注册"))
    }

    /// 获取聊天服务（必需）
    pub fn require_chat_service(&self) -> AnyhowResult<Arc<dyn ChatApplicationService>> {
        self.chat_service
            .clone()
            .ok_or_else(|| anyhow!("聊天服务未注册"))
    }

    /// 获取WebSocket服务（必需）
    pub fn require_websocket_service(&self) -> AnyhowResult<Arc<dyn WebSocketApplicationService>> {
        self.websocket_service
            .clone()
            .ok_or_else(|| anyhow!("WebSocket服务未注册"))
    }
}

impl std::fmt::Debug for ServiceProvider {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("ServiceProvider")
            .field("user_service", &self.user_service.is_some())
            .field("task_service", &self.task_service.is_some())
            .field("chat_service", &self.chat_service.is_some())
            .field("websocket_service", &self.websocket_service.is_some())
            .field("database", &"Arc<DatabaseConnection>")
            .finish()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::config::AppConfig;
    use sea_orm::{Database, DatabaseConnection};
    use std::sync::Arc;

    /// 创建测试用的数据库连接
    async fn create_test_database() -> Arc<DatabaseConnection> {
        use sea_orm::ConnectOptions;
        use std::time::Duration;

        let database_url = std::env::var("DATABASE_URL").unwrap_or_else(|_| {
            "postgres://axum_user:axum_secure_password_2025@localhost:5432/axum_tutorial"
                .to_string()
        });

        let mut opt = ConnectOptions::new(database_url);
        opt.max_connections(5)
            .min_connections(1)
            .connect_timeout(Duration::from_secs(8))
            .acquire_timeout(Duration::from_secs(8))
            .idle_timeout(Duration::from_secs(8))
            .max_lifetime(Duration::from_secs(8));

        let db = Database::connect(opt)
            .await
            .expect("Failed to create test database");
        Arc::new(db)
    }

    /// 创建测试用的应用配置
    fn create_test_config() -> AppConfig {
        AppConfig::default()
    }

    #[tokio::test]
    async fn test_service_registry_config_trait_api() {
        // 测试使用ServiceRegistryConfig trait的API
        let database = create_test_database().await;
        let config = create_test_config();

        let provider = ServiceRegistry::new()
            .with_database(database)
            .with_config(config)
            .auto_register()
            .build()
            .expect("Failed to build service provider");

        // 验证服务可以正常获取
        assert!(provider.get_user_service().is_some());
        assert!(provider.get_task_service().is_some());
        assert!(provider.get_chat_service().is_some());
        assert!(provider.get_websocket_service().is_some());
    }

    #[tokio::test]
    async fn test_service_registry_builder_trait_api() {
        // 测试使用ServiceRegistryBuilder trait的API
        let database = create_test_database().await;
        let config = create_test_config();

        let provider = ServiceRegistry::configure()
            .database(database)
            .config(config)
            .enable_auto_registration()
            .create_provider()
            .expect("Failed to create service provider");

        // 验证服务可以正常获取
        assert!(provider.get_user_service().is_some());
        assert!(provider.get_task_service().is_some());
        assert!(provider.get_chat_service().is_some());
        assert!(provider.get_websocket_service().is_some());
    }

    #[tokio::test]
    async fn test_service_registry_validation_errors() {
        // 测试配置验证错误

        // 缺少数据库连接
        let result = ServiceRegistry::new()
            .with_config(create_test_config())
            .auto_register()
            .build();
        assert!(result.is_err());
        let error_msg = result.err().unwrap().to_string();
        assert!(error_msg.contains("数据库连接未配置"));

        // 缺少应用配置
        let database = create_test_database().await;
        let result = ServiceRegistry::new()
            .with_database(database)
            .auto_register()
            .build();
        assert!(result.is_err());
        let error_msg = result.err().unwrap().to_string();
        assert!(error_msg.contains("应用配置未配置"));

        // 缺少自动注册
        let database = create_test_database().await;
        let result = ServiceRegistry::new()
            .with_database(database)
            .with_config(create_test_config())
            .build();
        assert!(result.is_err());
        let error_msg = result.err().unwrap().to_string();
        assert!(error_msg.contains("服务未启用自动注册"));
    }

    #[tokio::test]
    async fn test_service_registry_builder_validation_errors() {
        // 测试构建器模式的配置验证错误

        // 缺少数据库连接
        let result = ServiceRegistry::configure()
            .config(create_test_config())
            .enable_auto_registration()
            .create_provider();
        assert!(result.is_err());
        let error_msg = result.err().unwrap().to_string();
        assert!(error_msg.contains("数据库连接未配置"));

        // 缺少应用配置
        let database = create_test_database().await;
        let result = ServiceRegistry::configure()
            .database(database)
            .enable_auto_registration()
            .create_provider();
        assert!(result.is_err());
        let error_msg = result.err().unwrap().to_string();
        assert!(error_msg.contains("应用配置未配置"));

        // 缺少自动注册
        let database = create_test_database().await;
        let result = ServiceRegistry::configure()
            .database(database)
            .config(create_test_config())
            .create_provider();
        assert!(result.is_err());
        let error_msg = result.err().unwrap().to_string();
        assert!(error_msg.contains("服务未启用自动注册"));
    }

    #[tokio::test]
    async fn test_service_provider_required_services() {
        // 测试必需服务的获取
        let database = create_test_database().await;
        let config = create_test_config();

        let provider = ServiceRegistry::new()
            .with_database(database)
            .with_config(config)
            .auto_register()
            .build()
            .expect("Failed to build service provider");

        // 测试必需服务获取
        assert!(provider.require_user_service().is_ok());
        assert!(provider.require_task_service().is_ok());
        assert!(provider.require_chat_service().is_ok());
        assert!(provider.require_websocket_service().is_ok());
    }

    #[tokio::test]
    async fn test_service_registry_method_chaining() {
        // 测试方法链的流畅性
        let database = create_test_database().await;
        let config = create_test_config();

        // 测试第一种API风格
        let _provider1 = ServiceRegistry::new()
            .with_database(database.clone())
            .with_config(config.clone())
            .auto_register()
            .build()
            .expect("Failed to build with first API style");

        // 测试第二种API风格
        let _provider2 = ServiceRegistry::configure()
            .database(database)
            .config(config)
            .enable_auto_registration()
            .create_provider()
            .expect("Failed to build with second API style");
    }
}
