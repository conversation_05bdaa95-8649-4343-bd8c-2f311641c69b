//! # Server Library
//!
//! 服务器库模块，导出公共接口供测试和其他模块使用
//!
//! 表示层（Presentation Layer）- 负责HTTP请求处理和响应

// ============================================================================
// 模块声明 - 按服务器功能分组
// ============================================================================

// 配置管理
pub mod config;

// 路由和处理器
pub mod routes;

// 启动和依赖注入
pub mod startup;

// 依赖注入容器
pub mod dependency_injection;

// 主函数模块
mod main_module;

// 导出主函数
pub use main_module::main;

// ============================================================================
// 核心类型重新导出 - 按使用频率排序
// ============================================================================

// 配置类型
pub use config::AppConfig;

// 路由相关类型
pub use routes::AppState;

// 启动和服务容器类型
pub use startup::{
    // 链式调用构建器
    AppBuilder,
    AppBuilderTrait,
    BuiltApp,
    // 启动函数
    run,
    run_fluent,
};

// 依赖注入相关类型
pub use dependency_injection::{
    // 具体实现
    DefaultServiceContainer,
    DefaultServiceContainerBuilder,
    ServiceConfiguration,
    // 容器接口和实现
    ServiceContainer as ServiceContainerTrait,
    ServiceContainerBuilder,
    ServiceFactory,
    ServiceLifecycleManager,
    ServiceRegistrar,
    ServiceResolver,
};
