//! # Axum Tutorial Server Main Module
//!
//! 主应用程序逻辑
//!
//! 这是一个基于模块化领域驱动设计(Modular DDD)结合整洁架构的
//! Axum Web 服务器应用程序。

use crate::startup;
use anyhow::Result;

/// 应用程序主入口点
///
/// 负责：
/// 1. 启动服务器（日志系统在startup模块中初始化）
///
/// 支持两种启动方式：
/// - 传统方式：startup::run()
/// - 链式调用方式：startup::run_fluent()
pub async fn main() -> Result<()> {
    // 检查环境变量来决定使用哪种启动方式
    let use_fluent = std::env::var("USE_FLUENT_STARTUP")
        .unwrap_or_else(|_| "false".to_string())
        .parse::<bool>()
        .unwrap_or(false);

    if use_fluent {
        println!("🚀 使用链式调用风格启动应用...");
        startup::run_fluent().await
    } else {
        println!("🚀 使用传统方式启动应用...");
        startup::run().await
    }
}
