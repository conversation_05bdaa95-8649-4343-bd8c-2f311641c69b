//! # 认证处理器
//!
//! 处理用户注册和登录相关的HTTP请求

use super::*;
use crate::routes::AppState;
use app_common::middleware::AuthenticatedUser;
use tracing::info;

/// 用户注册处理器
///
/// 处理 POST /api/auth/register 请求
pub async fn register(
    State(state): State<AppState>,
    payload: std::result::Result<Json<RegisterRequest>, axum::extract::rejection::JsonRejection>,
) -> std::result::Result<impl IntoResponse, axum::response::Response> {
    // 处理JSON反序列化错误
    let Json(payload) = match payload {
        Ok(json) => json,
        Err(rejection) => {
            let error_message = match rejection {
                axum::extract::rejection::JsonRejection::JsonDataError(_) => {
                    "JSON数据格式错误".to_string()
                }
                axum::extract::rejection::JsonRejection::JsonSyntaxError(_) => {
                    "JSON语法错误".to_string()
                }
                axum::extract::rejection::JsonRejection::MissingJsonContentType(_) => {
                    "缺少Content-Type: application/json头".to_string()
                }
                _ => "JSON解析失败".to_string(),
            };
            return Err(
                ErrorHandler::handle_app_error(&AppError::BadRequest(error_message)).build(),
            );
        }
    };

    info!(username = %payload.username, "收到用户注册请求");

    // 调用用户应用服务进行注册
    let auth_response = match state
        .user_service
        .register_user(payload, &state.jwt_secret)
        .await
    {
        Ok(auth_response) => auth_response,
        Err(e) => {
            return Err(app_error_to_response(e));
        }
    };

    info!(user_id = %auth_response.user.id, username = %auth_response.user.username, "用户注册成功");

    Ok((
        StatusCode::CREATED,
        success_response(auth_response, "用户注册成功"),
    )
        .into_response())
}

/// 用户登录处理器
///
/// 处理 POST /api/auth/login 请求
pub async fn login(
    State(state): State<AppState>,
    payload: std::result::Result<Json<LoginRequest>, axum::extract::rejection::JsonRejection>,
) -> std::result::Result<impl IntoResponse, axum::response::Response> {
    // 处理JSON反序列化错误
    let Json(payload) = match payload {
        Ok(json) => json,
        Err(rejection) => {
            let error_message = match rejection {
                axum::extract::rejection::JsonRejection::JsonDataError(_) => {
                    "JSON数据格式错误".to_string()
                }
                axum::extract::rejection::JsonRejection::JsonSyntaxError(_) => {
                    "JSON语法错误".to_string()
                }
                axum::extract::rejection::JsonRejection::MissingJsonContentType(_) => {
                    "缺少Content-Type: application/json头".to_string()
                }
                _ => "JSON解析失败".to_string(),
            };
            return Err(
                ErrorHandler::handle_app_error(&AppError::BadRequest(error_message)).build(),
            );
        }
    };

    info!(username = %payload.username, "收到用户登录请求");

    // 调用用户应用服务进行登录
    let auth_response = match state
        .user_service
        .login_user(payload, &state.jwt_secret)
        .await
    {
        Ok(response) => response,
        Err(e) => {
            return Err(app_error_to_response(e));
        }
    };

    info!(username = %auth_response.user.username, "用户登录成功");

    Ok(success_response(auth_response, "登录成功").into_response())
}

/// 用户登出处理器
///
/// 处理 POST /api/auth/logout 请求
/// 需要JWT认证，从AuthenticatedUser中间件获取用户信息
pub async fn logout(
    State(_state): State<AppState>,
    user: AuthenticatedUser,
) -> Result<impl IntoResponse> {
    info!(user_id = %user.user_id, username = %user.username, "收到用户登出请求");

    // 注意：由于JWT是无状态的，我们无法在服务端使token失效
    // 在实际生产环境中，可以考虑以下策略：
    // 1. 使用Redis维护token黑名单
    // 2. 使用短期token + refresh token机制
    // 3. 在客户端清除token

    // 这里我们只是记录登出事件，实际的token失效由客户端处理
    info!(user_id = %user.user_id, username = %user.username, "用户登出成功");

    // 返回成功响应
    Ok(success_response(
        serde_json::json!({
            "user_id": user.user_id,
            "username": user.username,
            "logout_time": chrono::Utc::now().to_rfc3339()
        }),
        "登出成功",
    ))
}

#[cfg(test)]
mod tests {
    // 注意：这里需要实际的测试实现，需要mock服务
    // 这是一个示例测试结构

    #[tokio::test]
    async fn test_register_success() {
        // TODO: 实现注册成功测试
        // 需要创建mock的UserApplicationService
    }

    #[tokio::test]
    async fn test_login_success() {
        // TODO: 实现登录成功测试
        // 需要创建mock的UserApplicationService
    }

    #[tokio::test]
    async fn test_register_validation_error() {
        // TODO: 实现注册验证错误测试
    }

    #[tokio::test]
    async fn test_login_invalid_credentials() {
        // TODO: 实现登录凭据错误测试
    }
}
