//! # 聊天处理器
//!
//! 处理聊天相关的HTTP请求

use super::*;
use crate::routes::AppState;
use app_common::middleware::AuthenticatedUser;
use chrono::{ DateTime, Utc };
use tracing::{ error, info, warn };

/// 发送聊天消息处理器（符合HTTP动词规范）
///
/// 处理 POST /api/chat/send 请求
pub async fn post_chat_message(
    State(state): State<AppState>,
    Json(payload): Json<SimpleChatMessage>
) -> Result<impl IntoResponse> {
    info!("收到简单聊天消息发送请求: {:?}", payload);

    // 创建广播请求
    let broadcast_request = app_interfaces::BroadcastRequest {
        content: format!("{}: {}", payload.sender.username, payload.content),
        target_users: None, // 广播给所有用户
        target_session_type: None, // 所有会话类型
        exclude_sender: false, // 不排除发送者
        sender_id: Some(payload.sender.user_id.clone()),
        priority: Some("normal".to_string()),
    };

    // 广播消息到所有连接的WebSocket客户端
    let sent_count = state.websocket_service
        .broadcast_message(broadcast_request).await
        .map_err(app_common::AppError::InternalServerError)?;

    info!("简单聊天消息广播成功，发送到 {} 个连接", sent_count);

    let response = ApiResponse::success(
        serde_json::json!({
        "message": "消息已广播到所有连接的客户端",
        "content": payload.content,
        "sent_count": sent_count
    })
    );

    Ok((StatusCode::OK, Json(response)))
}

/// 获取消息查询参数
#[derive(Debug, Deserialize)]
pub struct GetMessagesQuery {
    /// 限制返回的消息数量
    #[serde(default = "default_limit")]
    pub limit: Option<i64>,
    /// 偏移量，用于分页
    #[serde(default)]
    #[allow(dead_code)]
    pub offset: Option<i64>,
    /// 获取此时间之前的消息（用于分页）
    #[serde(default)]
    pub before: Option<String>,
}

fn default_limit() -> Option<i64> {
    Some(50)
}

/// 获取聊天搜索结果处理器（符合HTTP动词规范）
///
/// 处理 GET /api/messages/search 请求
pub async fn get_chat_search(
    State(state): State<AppState>,
    user: AuthenticatedUser,
    Query(params): Query<SearchMessagesQuery>
) -> Result<impl IntoResponse> {
    info!(
        user_id = %user.user_id,
        keyword = %params.keyword.as_deref().unwrap_or(""),
        "收到消息搜索请求"
    );

    // 验证搜索关键词
    let keyword = match &params.keyword {
        Some(k) if !k.trim().is_empty() => k.trim(),
        _ => {
            warn!("搜索关键词为空");
            return Ok(
                success_response(
                    SearchMessagesResponse {
                        messages: vec![],
                        total_count: 0,
                        page: params.page.unwrap_or(1),
                        limit: params.limit.unwrap_or(20),
                        keyword: params.keyword.clone(),
                    },
                    "搜索关键词为空，返回空结果"
                )
            );
        }
    };

    // 构建搜索请求
    let search_request = app_domain::entities::chat::SearchGlobalChatRoomMessagesRequest {
        query: keyword.to_string(),
        limit: Some(params.limit.unwrap_or(20)),
        start_time: params.start_date.as_ref().and_then(|s| {
            DateTime::parse_from_rfc3339(s)
                .map(|dt| dt.with_timezone(&Utc))
                .inspect_err(|&e| {
                    warn!("解析开始时间失败: {}, 参数值: {}", e, s);
                })
                .ok()
        }),
        end_time: params.end_date.as_ref().and_then(|s| {
            DateTime::parse_from_rfc3339(s)
                .map(|dt| dt.with_timezone(&Utc))
                .inspect_err(|&e| {
                    warn!("解析结束时间失败: {}, 参数值: {}", e, s);
                })
                .ok()
        }),
        sender_id: params.sender.as_ref().and_then(|s| {
            Uuid::parse_str(s)
                .map_err(|e| {
                    warn!("解析发送者ID失败: {}, 参数值: {}", e, s);
                    e
                })
                .ok()
        }),
    };

    // 优先使用企业级弹性搜索服务（TASK_52搜索系统）
    let search_results = if let Some(resilient_service) = &state.resilient_chat_service {
        info!("使用企业级弹性搜索服务进行搜索");
        resilient_service
            .search_messages_with_resilience(user.user_id, search_request).await
            .map_err(|e| {
                error!("企业级搜索失败: {}", e);
                app_common::AppError::InternalServerError(format!("企业级搜索失败: {e}"))
            })?
    } else {
        info!("使用基础搜索服务进行搜索");
        // 降级到基础聊天应用服务
        state.chat_service
            .search_messages_in_global_room(user.user_id, search_request).await
            .map_err(|e| {
                error!("基础搜索失败: {}", e);
                app_common::AppError::InternalServerError(format!("基础搜索失败: {e}"))
            })?
    };

    // 转换为响应格式
    let messages: Vec<SearchMessageResult> = search_results
        .into_iter()
        .map(|msg| SearchMessageResult {
            id: msg.id,
            content: msg.content,
            sender_username: format!("用户_{}", &msg.sender_id.to_string()[..8]), // 临时用户名，后续可以关联用户表
            room_name: "全局聊天室".to_string(), // 临时房间名，后续可以关联聊天室表
            created_at: msg.created_at,
            message_type: match msg.message_type {
                app_domain::entities::message::MessageType::Text => "text".to_string(),
                app_domain::entities::message::MessageType::Image => "image".to_string(),
                app_domain::entities::message::MessageType::File => "file".to_string(),
                app_domain::entities::message::MessageType::System => "system".to_string(),
                app_domain::entities::message::MessageType::Voice => "voice".to_string(),
                app_domain::entities::message::MessageType::Video => "video".to_string(),
            },
        })
        .collect();

    let response = SearchMessagesResponse {
        total_count: messages.len(),
        messages,
        page: params.page.unwrap_or(1),
        limit: params.limit.unwrap_or(20),
        keyword: params.keyword.clone(),
    };

    info!(
        user_id = %user.user_id,
        keyword = keyword,
        total_results = response.total_count,
        "消息搜索完成"
    );

    Ok(success_response(response, "消息搜索完成"))
}

/// 获取聊天历史处理器（符合HTTP动词规范）
///
/// 处理 GET /api/messages/chat-room/:id 请求
pub async fn get_chat_history(
    State(state): State<AppState>,
    user: AuthenticatedUser,
    Path(room_id): Path<Uuid>,
    Query(params): Query<GetChatRoomMessagesQuery>
) -> Result<impl IntoResponse> {
    info!(
        user_id = %user.user_id,
        room_id = %room_id,
        "收到获取聊天室消息请求"
    );

    // 解析时间分页参数
    let before = if let Some(before_str) = &params.before {
        match DateTime::parse_from_rfc3339(before_str) {
            Ok(dt) => Some(dt.with_timezone(&Utc)),
            Err(e) => {
                warn!("解析before时间参数失败: {}, 参数值: {}", e, before_str);
                None
            }
        }
    } else {
        None
    };

    let after = if let Some(after_str) = &params.after {
        match DateTime::parse_from_rfc3339(after_str) {
            Ok(dt) => Some(dt.with_timezone(&Utc)),
            Err(e) => {
                warn!("解析after时间参数失败: {}, 参数值: {}", e, after_str);
                None
            }
        }
    } else {
        None
    };

    // 构建获取全局聊天室历史消息的请求
    let request = app_domain::entities::chat::GetGlobalChatRoomHistoryRequest {
        limit: params.limit,
        before,
        after,
    };

    // 调用聊天应用服务获取历史消息
    let message_responses = state.chat_service.get_global_room_history(
        user.user_id,
        request
    ).await?;

    // 转换为API响应格式，并获取用户名
    let mut messages: Vec<ChatRoomMessage> = Vec::new();
    for msg in message_responses {
        // 从用户服务获取发送者用户名
        let sender_username = match state.user_service.find_user_by_id(msg.sender_id).await {
            Ok(Some(user_response)) => user_response.username,
            Ok(None) => {
                warn!("未找到用户ID为{}的用户信息", msg.sender_id);
                "未知用户".to_string()
            }
            Err(e) => {
                warn!("获取用户ID为{}的用户信息失败: {}", msg.sender_id, e);
                "未知用户".to_string()
            }
        };

        // MessageResponse中没有status字段，使用默认的"sent"状态
        // TODO: 未来可以从消息状态服务获取实际状态
        let status = "sent".to_string();

        let chat_message = ChatRoomMessage {
            id: msg.id,
            content: msg.content,
            sender_id: msg.sender_id,
            sender_username,
            created_at: msg.created_at,
            message_type: format!("{:?}", msg.message_type).to_lowercase(),
            status,
        };

        messages.push(chat_message);
    }

    let response = ChatRoomMessagesResponse {
        messages: messages.clone(),
        room_id,
        total_count: messages.len(),
        page: params.page.unwrap_or(1),
        limit: params.limit.unwrap_or(50),
    };

    info!(
        user_id = %user.user_id,
        room_id = %room_id,
        message_count = response.total_count,
        "成功获取聊天室消息"
    );

    Ok(success_response(response, "获取聊天室消息成功"))
}

/// 消息搜索查询参数
#[derive(Debug, Deserialize)]
pub struct SearchMessagesQuery {
    /// 搜索关键词
    pub keyword: Option<String>,
    /// 消息类型过滤
    pub message_type: Option<String>,
    /// 发送者ID过滤
    pub sender: Option<String>,
    /// 聊天室ID过滤
    pub room_id: Option<Uuid>,
    /// 开始时间过滤（RFC3339格式）
    pub start_date: Option<String>,
    /// 结束时间过滤（RFC3339格式）
    pub end_date: Option<String>,
    /// 页码
    pub page: Option<u32>,
    /// 每页限制数量
    pub limit: Option<u32>,
}

/// 搜索消息结果
#[derive(Debug, Serialize)]
pub struct SearchMessageResult {
    pub id: Uuid,
    pub content: String,
    pub sender_username: String,
    pub room_name: String,
    pub created_at: DateTime<Utc>,
    pub message_type: String,
}

/// 搜索消息响应
#[derive(Debug, Serialize)]
pub struct SearchMessagesResponse {
    pub messages: Vec<SearchMessageResult>,
    pub total_count: usize,
    pub page: u32,
    pub limit: u32,
    pub keyword: Option<String>,
}

/// 获取聊天室消息查询参数
#[derive(Debug, Deserialize)]
pub struct GetChatRoomMessagesQuery {
    pub page: Option<u32>,
    pub limit: Option<u32>,
    /// 获取此时间之前的消息（用于分页）
    pub before: Option<String>,
    /// 获取此时间之后的消息（用于分页）
    pub after: Option<String>,
    #[allow(dead_code)]
    pub message_type: Option<String>,
    #[allow(dead_code)]
    pub status: Option<String>,
}

/// 聊天室消息
#[derive(Debug, Clone, Serialize)]
pub struct ChatRoomMessage {
    pub id: Uuid,
    pub content: String,
    pub sender_id: Uuid,
    pub sender_username: String,
    pub created_at: DateTime<Utc>,
    pub message_type: String,
    pub status: String,
}

/// 聊天室消息响应
#[derive(Debug, Serialize)]
pub struct ChatRoomMessagesResponse {
    pub messages: Vec<ChatRoomMessage>,
    pub room_id: Uuid,
    pub total_count: usize,
    pub page: u32,
    pub limit: u32,
}

/// 简单聊天消息请求结构体（用于WebSocket广播测试）
#[derive(Debug, Deserialize)]
pub struct SimpleChatMessage {
    pub message_type: String,
    pub content: String,
    pub sender: SimpleSender,
    pub timestamp: String,
}

/// 简单发送者信息
#[derive(Debug, Deserialize)]
pub struct SimpleSender {
    pub username: String,
    pub user_id: String,
}

#[cfg(test)]
mod tests {
    // 注意：这里需要实际的测试实现，需要mock服务
    // 这是一个示例测试结构

    #[tokio::test]
    async fn test_get_chat_rooms_success() {
        // TODO: 实现获取聊天室列表成功测试
        // 需要创建mock的ChatApplicationService
    }

    #[tokio::test]
    async fn test_create_chat_room_success() {
        // TODO: 实现创建聊天室成功测试
    }

    #[tokio::test]
    async fn test_get_messages_success() {
        // TODO: 实现获取消息成功测试
    }

    #[tokio::test]
    async fn test_send_message_success() {
        // TODO: 实现发送消息成功测试
    }

    #[tokio::test]
    async fn test_chat_room_not_found() {
        // TODO: 实现聊天室不存在测试
    }
}
