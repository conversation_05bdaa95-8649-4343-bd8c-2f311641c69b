//! # 数据库连接池健康检查处理器
//!
//! 提供数据库连接池监控和健康检查的API端点
//!
//! ## 架构设计
//!
//! ### DDD架构合规性
//! - 通过应用服务层访问数据库健康状态
//! - 不直接依赖基础设施层的数据库连接
//! - 遵循依赖倒置原则

use axum::{extract::State, response::Json};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{info, instrument, warn};

use crate::routes::AppState;
use app_common::error::Result;

/// 数据库连接池健康状态响应
#[derive(Debug, Serialize, Deserialize)]
pub struct DatabaseHealthResponse {
    /// 连接池是否健康
    pub healthy: bool,
    /// 健康检查时间戳
    pub timestamp: chrono::DateTime<chrono::Utc>,
    /// 连接池指标
    pub metrics: DatabasePoolMetrics,
    /// 详细状态信息
    pub details: HashMap<String, serde_json::Value>,
}

/// 数据库连接池指标
#[derive(Debug, Serialize, Deserialize)]
pub struct DatabasePoolMetrics {
    /// 总连接数
    pub total_connections: u64,
    /// 活跃连接数
    pub active_connections: u64,
    /// 空闲连接数
    pub idle_connections: u64,
    /// 连接获取成功次数
    pub acquire_success_count: u64,
    /// 连接获取失败次数
    pub acquire_failure_count: u64,
    /// 平均连接获取时间（微秒）
    pub avg_acquire_time_us: u64,
    /// 连接成功率
    pub success_rate: f64,
    /// 连接池是否健康
    pub is_healthy: bool,
}

/// 数据库连接池配置信息响应
#[derive(Debug, Serialize, Deserialize)]
pub struct DatabaseConfigResponse {
    /// 数据库URL（隐藏敏感信息）
    pub database_url_masked: String,
    /// 连接池配置
    pub pool_config: DatabasePoolConfigInfo,
}

/// 数据库连接池配置信息
#[derive(Debug, Serialize, Deserialize)]
pub struct DatabasePoolConfigInfo {
    /// 最大连接数
    pub max_connections: u32,
    /// 最小连接数
    pub min_connections: u32,
    /// 连接超时时间（秒）
    pub connect_timeout_secs: u64,
    /// 空闲超时时间（秒）
    pub idle_timeout_secs: u64,
    /// 获取连接超时时间（秒）
    pub acquire_timeout_secs: u64,
    /// 连接最大生命周期（秒，可选）
    pub max_lifetime_secs: Option<u64>,
    /// 是否启用TCP_NODELAY
    pub tcp_nodelay: bool,
    /// 是否启用TCP_KEEPALIVE
    pub tcp_keepalive: bool,
}

/// 获取数据库连接池健康状态
///
/// 【功能】: 检查数据库连接池的健康状态和性能指标
/// 【路径】: GET /api/health/database
/// 【返回】: 数据库连接池健康状态和指标信息
///
/// ## DDD架构合规性
/// - 通过用户应用服务进行健康检查，避免直接访问数据库
/// - 使用轻量级的用户查询作为健康检查指标
#[instrument(skip(state))]
pub async fn get_database_health(
    State(state): State<AppState>,
) -> Result<Json<DatabaseHealthResponse>> {
    info!("🔍 检查数据库连接池健康状态");

    // 通过应用服务执行数据库健康检查
    // 使用轻量级的用户查询作为健康检查指标
    let db_health = match state.user_service.fetch_user_count().await {
        Ok(_) => {
            info!("✅ 数据库连接健康检查通过");
            true
        }
        Err(e) => {
            warn!("❌ 数据库连接健康检查失败: {}", e);
            false
        }
    };

    // 创建模拟的连接池指标（实际项目中应该从连接池管理器获取）
    let metrics = DatabasePoolMetrics {
        total_connections: 20,       // 从配置获取
        active_connections: 5,       // 从连接池获取
        idle_connections: 15,        // 从连接池获取
        acquire_success_count: 1000, // 从指标获取
        acquire_failure_count: 5,    // 从指标获取
        avg_acquire_time_us: 150,    // 从指标获取
        success_rate: 99.5,          // 计算得出
        is_healthy: db_health,
    };

    // 创建详细状态信息
    let mut details = HashMap::new();
    details.insert(
        "database_type".to_string(),
        serde_json::Value::String("SQLite".to_string()),
    );
    details.insert(
        "connection_pool_type".to_string(),
        serde_json::Value::String("SeaORM".to_string()),
    );
    details.insert(
        "monitoring_enabled".to_string(),
        serde_json::Value::Bool(true),
    );
    details.insert(
        "last_health_check".to_string(),
        serde_json::Value::String(chrono::Utc::now().to_rfc3339()),
    );

    let response = DatabaseHealthResponse {
        healthy: db_health,
        timestamp: chrono::Utc::now(),
        metrics,
        details,
    };

    info!("📊 数据库健康检查完成: healthy={}", response.healthy);
    Ok(Json(response))
}

/// 获取数据库连接池配置信息
///
/// 【功能】: 返回当前数据库连接池的配置信息
/// 【路径】: GET /api/health/database/config
/// 【返回】: 数据库连接池配置信息
#[instrument(skip(state))]
pub async fn get_database_config(
    State(state): State<AppState>,
) -> Result<Json<DatabaseConfigResponse>> {
    info!("📋 获取数据库连接池配置信息");

    // 从环境变量获取数据库URL并隐藏敏感信息
    let database_url = std::env::var("DATABASE_URL")
        .unwrap_or_else(|_| "sqlite:task_manager.db?mode=rwc".to_string());

    let database_url_masked = mask_database_url(&database_url);

    // 检测运行环境
    let is_production = std::env::var("ENVIRONMENT")
        .unwrap_or_else(|_| "development".to_string())
        .to_lowercase()
        == "production";

    // 根据环境创建配置信息
    let pool_config = if is_production {
        DatabasePoolConfigInfo {
            max_connections: 100,
            min_connections: 10,
            connect_timeout_secs: 30,
            idle_timeout_secs: 600,
            acquire_timeout_secs: 30,
            max_lifetime_secs: Some(3600),
            tcp_nodelay: true,
            tcp_keepalive: true,
        }
    } else {
        DatabasePoolConfigInfo {
            max_connections: 20,
            min_connections: 5,
            connect_timeout_secs: 10,
            idle_timeout_secs: 300,
            acquire_timeout_secs: 10,
            max_lifetime_secs: Some(1800),
            tcp_nodelay: true,
            tcp_keepalive: true,
        }
    };

    let response = DatabaseConfigResponse {
        database_url_masked,
        pool_config,
    };

    info!("📋 数据库配置信息获取完成");
    Ok(Json(response))
}

/// 执行数据库连接池压力测试
///
/// 【功能】: 对数据库连接池进行压力测试，验证高并发性能
/// 【路径】: POST /api/health/database/stress-test
/// 【返回】: 压力测试结果
///
/// ## DDD架构合规性
/// - 通过应用服务层执行压力测试，避免直接访问数据库
/// - 使用真实的业务操作作为压力测试指标
#[instrument(skip(state))]
pub async fn run_database_stress_test(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>> {
    info!("🚀 开始数据库连接池压力测试");

    // 执行多个并发数据库操作
    let concurrent_tasks = 50;
    let operations_per_task = 20;

    let start_time = std::time::Instant::now();
    let mut tasks = Vec::new();

    for task_id in 0..concurrent_tasks {
        let user_service = state.user_service.clone();
        let task = tokio::spawn(async move {
            let mut success_count = 0;
            let mut failure_count = 0;

            for _op in 0..operations_per_task {
                // 使用轻量级的用户计数查询作为压力测试操作
                match user_service.fetch_user_count().await {
                    Ok(_) => {
                        success_count += 1;
                    }
                    Err(_) => {
                        failure_count += 1;
                    }
                }

                // 短暂延迟模拟真实负载
                tokio::time::sleep(std::time::Duration::from_millis(10)).await;
            }

            (task_id, success_count, failure_count)
        });
        tasks.push(task);
    }

    // 等待所有任务完成
    let mut total_success = 0;
    let mut total_failure = 0;

    for task in tasks {
        match task.await {
            Ok((task_id, success, failure)) => {
                total_success += success;
                total_failure += failure;
                info!(
                    "✅ 任务 {} 完成: 成功={}, 失败={}",
                    task_id, success, failure
                );
            }
            Err(e) => {
                tracing::error!("❌ 任务执行失败: {}", e);
                total_failure += operations_per_task;
            }
        }
    }

    let test_duration = start_time.elapsed();
    let total_operations = total_success + total_failure;
    let success_rate = if total_operations > 0 {
        ((total_success as f64) / (total_operations as f64)) * 100.0
    } else {
        0.0
    };

    let throughput = if test_duration.as_secs_f64() > 0.0 {
        (total_success as f64) / test_duration.as_secs_f64()
    } else {
        0.0
    };

    let result = serde_json::json!({
        "test_completed": true,
        "test_duration_ms": test_duration.as_millis(),
        "concurrent_tasks": concurrent_tasks,
        "operations_per_task": operations_per_task,
        "total_operations": total_operations,
        "successful_operations": total_success,
        "failed_operations": total_failure,
        "success_rate_percent": success_rate,
        "throughput_ops_per_sec": throughput,
        "timestamp": chrono::Utc::now().to_rfc3339(),
    });

    info!(
        "📈 压力测试完成: 成功率={:.2}%, 吞吐量={:.2} ops/sec",
        success_rate, throughput
    );

    Ok(Json(result))
}

/// 隐藏数据库URL中的敏感信息
fn mask_database_url(url: &str) -> String {
    if url.contains("://") {
        let parts: Vec<&str> = url.split("://").collect();
        if parts.len() == 2 {
            let protocol = parts[0];
            let rest = parts[1];

            if rest.contains('@') {
                let auth_parts: Vec<&str> = rest.split('@').collect();
                if auth_parts.len() == 2 {
                    return format!("{}://***:***@{}", protocol, auth_parts[1]);
                }
            }
        }
    }

    // 对于SQLite等本地数据库，只显示文件名
    if url.starts_with("sqlite:") {
        return "sqlite:***".to_string();
    }

    url.to_string()
}

/// 获取数据库连接池状态处理器
///
/// 处理 GET /api/db/pool 请求
/// 返回数据库连接池的详细状态信息
#[instrument(skip(state))]
pub async fn get_database_pool_status(
    State(state): State<AppState>,
) -> Result<Json<serde_json::Value>> {
    info!("📊 收到数据库连接池状态请求");

    // TODO: 从实际的数据库连接池管理器获取状态
    // 这里先返回模拟数据，后续会连接到真实的连接池管理器
    let pool_status = create_mock_database_pool_status();

    info!(
        total_connections = %pool_status.total_connections,
        active_connections = %pool_status.active_connections,
        idle_connections = %pool_status.idle_connections,
        success_rate = %pool_status.success_rate,
        "成功返回数据库连接池状态"
    );

    Ok(Json(serde_json::json!({
        "success": true,
        "message": "获取数据库连接池状态成功",
        "data": pool_status
    })))
}

/// 创建模拟的数据库连接池状态数据
///
/// 【注意】: 这是临时的模拟数据，后续会替换为真实的连接池状态
fn create_mock_database_pool_status() -> DatabasePoolStatus {
    // 模拟连接池配置（基于生产环境的典型配置）
    let max_connections = 100u64;
    let min_connections = 10u64;
    let active_connections = 25u64;
    let idle_connections = max_connections - active_connections;

    // 模拟性能指标
    let acquire_success_count = 15420u64;
    let acquire_failure_count = 23u64;
    let total_operations = acquire_success_count + acquire_failure_count;
    let success_rate = if total_operations > 0 {
        (acquire_success_count as f64) / (total_operations as f64)
    } else {
        1.0
    };

    DatabasePoolStatus {
        total_connections: max_connections,
        active_connections,
        idle_connections,
        max_connections,
        min_connections,
        acquire_success_count,
        acquire_failure_count,
        avg_acquire_time_ms: 2.5,
        success_rate,
        is_healthy: success_rate > 0.95,
        last_health_check: chrono::Utc::now(),
        connection_errors: acquire_failure_count,
        pool_utilization: (active_connections as f64) / (max_connections as f64),
        config: DatabasePoolConfig {
            max_connections: max_connections as u32,
            min_connections: min_connections as u32,
            connect_timeout_secs: 30,
            idle_timeout_secs: 600,
            acquire_timeout_secs: 30,
            max_lifetime_secs: Some(3600),
            tcp_nodelay: true,
            tcp_keepalive: true,
        },
    }
}

/// 数据库连接池状态信息
#[derive(Debug, Serialize, Deserialize)]
pub struct DatabasePoolStatus {
    /// 总连接数
    pub total_connections: u64,
    /// 活跃连接数
    pub active_connections: u64,
    /// 空闲连接数
    pub idle_connections: u64,
    /// 最大连接数
    pub max_connections: u64,
    /// 最小连接数
    pub min_connections: u64,
    /// 连接获取成功次数
    pub acquire_success_count: u64,
    /// 连接获取失败次数
    pub acquire_failure_count: u64,
    /// 平均连接获取时间（毫秒）
    pub avg_acquire_time_ms: f64,
    /// 连接成功率
    pub success_rate: f64,
    /// 连接池是否健康
    pub is_healthy: bool,
    /// 最后健康检查时间
    pub last_health_check: chrono::DateTime<chrono::Utc>,
    /// 连接错误次数
    pub connection_errors: u64,
    /// 连接池利用率
    pub pool_utilization: f64,
    /// 连接池配置信息
    pub config: DatabasePoolConfig,
}

/// 数据库连接池配置信息（用于API响应）
#[derive(Debug, Serialize, Deserialize)]
pub struct DatabasePoolConfig {
    /// 最大连接数
    pub max_connections: u32,
    /// 最小连接数
    pub min_connections: u32,
    /// 连接超时时间（秒）
    pub connect_timeout_secs: u64,
    /// 空闲超时时间（秒）
    pub idle_timeout_secs: u64,
    /// 获取连接超时时间（秒）
    pub acquire_timeout_secs: u64,
    /// 连接最大生命周期（秒）
    pub max_lifetime_secs: Option<u64>,
    /// 启用TCP_NODELAY
    pub tcp_nodelay: bool,
    /// 启用TCP保活
    pub tcp_keepalive: bool,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_mock_database_pool_status() {
        let status = create_mock_database_pool_status();

        // 验证基本字段
        assert!(status.total_connections > 0);
        assert!(status.active_connections <= status.total_connections);
        assert!(status.idle_connections <= status.total_connections);
        assert_eq!(
            status.active_connections + status.idle_connections,
            status.total_connections
        );

        // 验证成功率计算
        assert!(status.success_rate >= 0.0 && status.success_rate <= 1.0);
        let expected_success_rate = (status.acquire_success_count as f64)
            / ((status.acquire_success_count + status.acquire_failure_count) as f64);
        assert!((status.success_rate - expected_success_rate).abs() < 0.001);

        // 验证健康状态逻辑
        assert_eq!(status.is_healthy, status.success_rate > 0.95);

        // 验证利用率计算
        let expected_utilization =
            (status.active_connections as f64) / (status.max_connections as f64);
        assert!((status.pool_utilization - expected_utilization).abs() < 0.001);

        // 验证配置合理性
        assert!(status.config.max_connections >= status.config.min_connections);
        assert!(status.config.connect_timeout_secs > 0);
        assert!(status.config.acquire_timeout_secs > 0);
    }

    #[test]
    fn test_database_pool_status_serialization() {
        let status = create_mock_database_pool_status();

        // 测试序列化
        let json = serde_json::to_string(&status).expect("序列化失败");
        assert!(!json.is_empty());

        // 测试反序列化
        let deserialized: DatabasePoolStatus = serde_json::from_str(&json).expect("反序列化失败");

        assert_eq!(status.total_connections, deserialized.total_connections);
        assert_eq!(status.active_connections, deserialized.active_connections);
        // 使用近似比较来处理浮点数精度问题
        assert!((status.success_rate - deserialized.success_rate).abs() < 0.0001);
    }

    #[test]
    fn test_database_pool_config_serialization() {
        let config = DatabasePoolConfig {
            max_connections: 100,
            min_connections: 10,
            connect_timeout_secs: 30,
            idle_timeout_secs: 600,
            acquire_timeout_secs: 30,
            max_lifetime_secs: Some(3600),
            tcp_nodelay: true,
            tcp_keepalive: true,
        };

        // 测试序列化
        let json = serde_json::to_string(&config).expect("序列化失败");
        assert!(!json.is_empty());

        // 测试反序列化
        let deserialized: DatabasePoolConfig = serde_json::from_str(&json).expect("反序列化失败");

        assert_eq!(config.max_connections, deserialized.max_connections);
        assert_eq!(config.min_connections, deserialized.min_connections);
        assert_eq!(config.tcp_nodelay, deserialized.tcp_nodelay);
        assert_eq!(config.tcp_keepalive, deserialized.tcp_keepalive);
    }
}
