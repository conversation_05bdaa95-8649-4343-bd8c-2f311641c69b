//! # 监控面板处理器
//!
//! 提供WebSocket实时监控面板的HTTP处理器

use axum::{
    http::StatusCode,
    response::{Html, IntoResponse, Redirect},
};
use tracing::{Level, info, span};

/// WebSocket监控面板页面处理器
///
/// 【功能】: 返回WebSocket实时监控面板的HTML页面
/// 【路径】: GET /websocket-stats.html
/// 【认证】: 无需认证（公开访问）
pub async fn websocket_stats_page() -> impl IntoResponse {
    let span = span!(Level::INFO, "websocket_stats_page");
    let _enter = span.enter();

    info!("收到WebSocket监控面板页面请求");

    // 读取监控面板HTML文件
    match tokio::fs::read_to_string("static/websocket-stats.html").await {
        Ok(html_content) => {
            info!("成功加载WebSocket监控面板页面");
            Html(html_content)
        }
        Err(e) => {
            tracing::error!("加载WebSocket监控面板页面失败: {}", e);
            Html(format!(
                r#"
                <!DOCTYPE html>
                <html lang="zh-CN">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>监控面板加载失败</title>
                    <style>
                        body {{
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 100vh;
                            margin: 0;
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                        }}
                        .error-container {{
                            text-align: center;
                            padding: 40px;
                            background: rgba(255, 255, 255, 0.1);
                            border-radius: 15px;
                            backdrop-filter: blur(10px);
                            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
                        }}
                        .error-title {{
                            font-size: 2.5em;
                            margin-bottom: 20px;
                            font-weight: 300;
                        }}
                        .error-message {{
                            font-size: 1.2em;
                            margin-bottom: 30px;
                            opacity: 0.9;
                        }}
                        .retry-button {{
                            background: rgba(255, 255, 255, 0.2);
                            color: white;
                            border: 2px solid rgba(255, 255, 255, 0.3);
                            padding: 12px 24px;
                            border-radius: 8px;
                            cursor: pointer;
                            font-size: 1em;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            text-decoration: none;
                            display: inline-block;
                        }}
                        .retry-button:hover {{
                            background: rgba(255, 255, 255, 0.3);
                            border-color: rgba(255, 255, 255, 0.5);
                            transform: translateY(-2px);
                        }}
                    </style>
                </head>
                <body>
                    <div class="error-container">
                        <div class="error-title">🚫 监控面板加载失败</div>
                        <div class="error-message">
                            无法加载WebSocket监控面板页面。<br>
                            错误信息: {e}
                        </div>
                        <a href="/websocket-stats.html" class="retry-button">重新加载</a>
                    </div>
                </body>
                </html>
                "#
            ))
        }
    }
}

/// 监控面板首页重定向处理器
///
/// 【功能】: 将 /monitoring 请求重定向到 WebSocket 监控面板
/// 【路径】: GET /monitoring
/// 【认证】: 无需认证（公开访问）
pub async fn monitoring_dashboard_redirect() -> impl IntoResponse {
    let span = span!(Level::INFO, "monitoring_dashboard_redirect");
    let _enter = span.enter();

    info!("收到监控面板重定向请求，重定向到WebSocket监控面板");

    Redirect::permanent("/websocket-stats.html")
}

/// 监控面板健康检查处理器
///
/// 【功能】: 检查监控面板相关服务的健康状态
/// 【路径】: GET /monitoring/health
/// 【认证】: 无需认证（公开访问）
pub async fn monitoring_health_check() -> impl IntoResponse {
    let span = span!(Level::INFO, "monitoring_health_check");
    let _enter = span.enter();

    info!("收到监控面板健康检查请求");

    // 检查监控面板HTML文件是否存在
    let html_file_exists = tokio::fs::metadata("static/websocket-stats.html")
        .await
        .is_ok();

    // 检查JavaScript模块文件是否存在
    let js_module_exists = tokio::fs::metadata("static/js/modules/websocket-monitoring.js")
        .await
        .is_ok();

    let health_status = if html_file_exists && js_module_exists {
        "healthy"
    } else {
        "unhealthy"
    };

    let status_code = if health_status == "healthy" {
        StatusCode::OK
    } else {
        StatusCode::SERVICE_UNAVAILABLE
    };

    let health_response = serde_json::json!({
        "status": health_status,
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "components": {
            "html_file": {
                "status": if html_file_exists { "available" } else { "missing" },
                "path": "static/websocket-stats.html"
            },
            "js_module": {
                "status": if js_module_exists { "available" } else { "missing" },
                "path": "static/js/modules/websocket-monitoring.js"
            }
        },
        "endpoints": {
            "dashboard": "/websocket-stats.html",
            "redirect": "/monitoring",
            "health": "/monitoring/health"
        }
    });

    info!(
        "监控面板健康检查完成: 状态={}, HTML文件={}, JS模块={}",
        health_status,
        if html_file_exists { "存在" } else { "缺失" },
        if js_module_exists { "存在" } else { "缺失" }
    );

    (status_code, axum::Json(health_response))
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{
        Router,
        body::Body,
        http::{Request, StatusCode},
        routing::get,
    };
    use tower::ServiceExt;

    /// 创建测试路由
    fn create_test_router() -> Router {
        Router::new()
            .route("/websocket-stats.html", get(websocket_stats_page))
            .route("/monitoring", get(monitoring_dashboard_redirect))
            .route("/monitoring/health", get(monitoring_health_check))
    }

    #[tokio::test]
    async fn test_websocket_stats_page() {
        let app = create_test_router();

        let response = app
            .oneshot(
                Request::builder()
                    .uri("/websocket-stats.html")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::OK);

        let content_type = response
            .headers()
            .get("content-type")
            .unwrap()
            .to_str()
            .unwrap();

        assert!(content_type.contains("text/html"));
    }

    #[tokio::test]
    async fn test_monitoring_dashboard_redirect() {
        let app = create_test_router();

        let response = app
            .oneshot(
                Request::builder()
                    .uri("/monitoring")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();

        assert_eq!(response.status(), StatusCode::PERMANENT_REDIRECT);

        let location = response
            .headers()
            .get("location")
            .unwrap()
            .to_str()
            .unwrap();

        assert_eq!(location, "/websocket-stats.html");
    }

    #[tokio::test]
    async fn test_monitoring_health_check() {
        let app = create_test_router();

        let response = app
            .oneshot(
                Request::builder()
                    .uri("/monitoring/health")
                    .body(Body::empty())
                    .unwrap(),
            )
            .await
            .unwrap();

        // 健康检查应该返回200或503状态码
        assert!(
            response.status() == StatusCode::OK
                || response.status() == StatusCode::SERVICE_UNAVAILABLE
        );

        let content_type = response
            .headers()
            .get("content-type")
            .unwrap()
            .to_str()
            .unwrap();

        assert!(content_type.contains("application/json"));
    }
}
