//! # 查询优化API处理器
//!
//! 提供数据库查询性能优化相关的API端点

use axum::{
    extract::{Query, State},
    response::Json,
};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tracing::{error, info, instrument};

use crate::AppState;
use app_common::error::{AppError, Result};
use app_infrastructure::database::{
    DatabaseStats, IndexRecommendation, OptimizationStrategy, QueryOptimizer,
    QueryPerformanceMetrics,
};

/// 查询优化请求参数
#[derive(Debug, Deserialize)]
pub struct OptimizeQueryRequest {
    /// 要优化的SQL查询语句
    pub query_sql: String,
    /// 是否应用推荐的索引
    #[serde(default)]
    pub apply_recommendations: bool,
}

/// 查询优化响应
#[derive(Debug, Serialize)]
pub struct OptimizeQueryResponse {
    /// 查询性能指标
    pub performance_metrics: QueryPerformanceMetrics,
    /// 优化策略列表
    pub optimization_strategies: Vec<OptimizationStrategy>,
    /// 优化建议摘要
    pub summary: String,
    /// 是否为慢查询
    pub is_slow_query: bool,
}

/// 批量查询优化请求
#[derive(Debug, Deserialize)]
pub struct BatchOptimizeRequest {
    /// 要优化的查询列表
    pub queries: Vec<String>,
    /// 最大并发优化数量
    #[serde(default = "default_max_concurrent")]
    pub max_concurrent: usize,
}

fn default_max_concurrent() -> usize {
    10
}

/// 批量查询优化响应
#[derive(Debug, Serialize)]
pub struct BatchOptimizeResponse {
    /// 每个查询的优化结果
    pub results: HashMap<String, Vec<OptimizationStrategy>>,
    /// 总体优化摘要
    pub summary: String,
    /// 处理的查询数量
    pub processed_queries: usize,
    /// 成功优化的查询数量
    pub successful_optimizations: usize,
}

/// 数据库统计信息查询参数
#[derive(Debug, Deserialize)]
pub struct DatabaseStatsQuery {
    /// 是否包含详细信息
    #[serde(default)]
    pub include_details: bool,
}

/// 数据库统计信息响应
#[derive(Debug, Serialize)]
pub struct DatabaseStatsResponse {
    /// 数据库统计信息
    pub stats: DatabaseStats,
    /// 统计信息收集时间
    pub collected_at: chrono::DateTime<chrono::Utc>,
    /// 额外的详细信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<HashMap<String, serde_json::Value>>,
}

/// 索引推荐响应
#[derive(Debug, Serialize)]
pub struct IndexRecommendationsResponse {
    /// 索引推荐列表
    pub recommendations: Vec<IndexRecommendation>,
    /// 推荐总数
    pub total_recommendations: usize,
    /// 预期总体性能提升
    pub expected_total_improvement: f64,
}

/// 优化单个查询
///
/// # 路径
/// POST /api/query/optimize
///
/// # 功能
/// 分析单个SQL查询的性能并提供优化建议
#[instrument(skip(state))]
pub async fn optimize_query(
    State(state): State<AppState>,
    Json(request): Json<OptimizeQueryRequest>,
) -> Result<Json<OptimizeQueryResponse>> {
    info!("🔍 开始查询优化分析");
    info!("查询SQL: {}", request.query_sql);

    // 创建查询优化器
    let db_connection = state.db.clone();
    let mut optimizer = QueryOptimizer::new(db_connection, 1000); // 1秒慢查询阈值

    // 执行查询优化
    let optimization_strategies =
        optimizer
            .optimize_query(&request.query_sql)
            .await
            .map_err(|e| {
                error!("查询优化失败: {}", e);
                AppError::DatabaseError(format!("查询优化失败: {e}"))
            })?;

    // 创建模拟的性能指标（实际应用中应该从分析器获取）
    let performance_metrics = QueryPerformanceMetrics {
        query_id: uuid::Uuid::new_v4().to_string(),
        query_sql: request.query_sql.clone(),
        execution_time_ms: 150, // 模拟值
        rows_returned: 100,     // 模拟值
        index_used: true,       // 模拟值
        estimated_cost: 10.5,   // 模拟值
        actual_cost: 12.3,      // 模拟值
        buffer_hit_ratio: 0.95, // 模拟值
        executed_at: chrono::Utc::now(),
    };

    let is_slow_query = performance_metrics.execution_time_ms > 1000;
    let summary = optimizer.get_optimization_summary();

    // 如果请求应用推荐，则应用索引推荐
    if request.apply_recommendations {
        let index_recommendations: Vec<IndexRecommendation> = optimization_strategies
            .iter()
            .filter_map(|strategy| {
                if let OptimizationStrategy::AddIndex(rec) = strategy {
                    Some(rec.clone())
                } else {
                    None
                }
            })
            .collect();

        if !index_recommendations.is_empty() {
            match optimizer
                .apply_index_recommendations(&index_recommendations)
                .await
            {
                Ok(created_indexes) => {
                    info!("成功创建索引: {:?}", created_indexes);
                }
                Err(e) => {
                    error!("应用索引推荐失败: {}", e);
                }
            }
        }
    }

    let response = OptimizeQueryResponse {
        performance_metrics,
        optimization_strategies,
        summary,
        is_slow_query,
    };

    info!("✅ 查询优化分析完成");
    Ok(Json(response))
}

/// 批量优化查询
///
/// # 路径
/// POST /api/query/batch-optimize
///
/// # 功能
/// 批量分析多个SQL查询的性能并提供优化建议
#[instrument(skip(state))]
pub async fn batch_optimize_queries(
    State(state): State<AppState>,
    Json(request): Json<BatchOptimizeRequest>,
) -> Result<Json<BatchOptimizeResponse>> {
    info!("🔍 开始批量查询优化，查询数量: {}", request.queries.len());

    if request.queries.is_empty() {
        return Err(AppError::ValidationError("查询列表不能为空".to_string()));
    }

    if request.queries.len() > 100 {
        return Err(AppError::ValidationError(
            "批量查询数量不能超过100个".to_string(),
        ));
    }

    // 创建查询优化器
    let db_connection = state.db.clone();
    let mut optimizer = QueryOptimizer::new(db_connection, 1000);

    // 执行批量优化
    let results = optimizer
        .batch_optimize_queries(&request.queries)
        .await
        .map_err(|e| {
            error!("批量查询优化失败: {}", e);
            AppError::DatabaseError(format!("批量查询优化失败: {e}"))
        })?;

    let processed_queries = request.queries.len();
    let successful_optimizations = results.len();
    let summary = format!(
        "批量优化完成: 处理 {processed_queries} 个查询，成功优化 {successful_optimizations} 个查询"
    );

    let response = BatchOptimizeResponse {
        results,
        summary,
        processed_queries,
        successful_optimizations,
    };

    info!("✅ 批量查询优化完成");
    Ok(Json(response))
}

/// 获取数据库统计信息
///
/// # 路径
/// GET /api/query/database-stats
///
/// # 功能
/// 获取当前数据库的性能统计信息
#[instrument(skip(state))]
pub async fn get_database_stats(
    State(state): State<AppState>,
    Query(params): Query<DatabaseStatsQuery>,
) -> Result<Json<DatabaseStatsResponse>> {
    info!("📊 获取数据库统计信息");

    // 创建查询优化器
    let db_connection = state.db.clone();
    let optimizer = QueryOptimizer::new(db_connection, 1000);

    // 获取数据库统计信息
    let stats = optimizer.get_database_stats().await.map_err(|e| {
        error!("获取数据库统计信息失败: {}", e);
        AppError::DatabaseError(format!("获取数据库统计信息失败: {e}"))
    })?;

    let mut details = None;
    if params.include_details {
        let mut detail_map = HashMap::new();
        detail_map.insert(
            "cache_efficiency".to_string(),
            serde_json::json!(stats.cache_hit_ratio),
        );
        detail_map.insert(
            "query_performance".to_string(),
            serde_json::json!({
                "total_queries": stats.total_queries,
                "slow_queries": stats.slow_queries,
                "slow_query_ratio": if stats.total_queries > 0 {
                    stats.slow_queries as f64 / stats.total_queries as f64
                } else { 0.0 }
            }),
        );
        details = Some(detail_map);
    }

    let response = DatabaseStatsResponse {
        stats,
        collected_at: chrono::Utc::now(),
        details,
    };

    info!("✅ 数据库统计信息获取完成");
    Ok(Json(response))
}

/// 获取索引推荐
///
/// # 路径
/// GET /api/query/index-recommendations
///
/// # 功能
/// 基于查询模式生成索引推荐
#[instrument(skip(state))]
pub async fn get_index_recommendations(
    State(state): State<AppState>,
) -> Result<Json<IndexRecommendationsResponse>> {
    info!("💡 生成索引推荐");

    // 创建查询优化器
    let db_connection = state.db.clone();
    let optimizer = QueryOptimizer::new(db_connection, 1000);

    // 模拟常见查询模式
    let common_query_patterns = vec![
        "SELECT * FROM messages WHERE chat_room_id = ? ORDER BY created_at DESC".to_string(),
        "SELECT * FROM messages WHERE sender_id = ? AND created_at > ?".to_string(),
        "SELECT * FROM users WHERE status = 'active' ORDER BY last_login_at DESC".to_string(),
    ];

    // 生成索引推荐
    let recommendations =
        optimizer.generate_index_recommendations("messages", &common_query_patterns);

    let total_recommendations = recommendations.len();
    let expected_total_improvement: f64 =
        recommendations.iter().map(|r| r.expected_improvement).sum();

    let response = IndexRecommendationsResponse {
        recommendations,
        total_recommendations,
        expected_total_improvement,
    };

    info!("✅ 索引推荐生成完成，共 {} 个推荐", total_recommendations);
    Ok(Json(response))
}
