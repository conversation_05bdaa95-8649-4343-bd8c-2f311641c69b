//! # 用户处理器
//!
//! 处理用户相关的HTTP请求

use super::*;
use crate::routes::AppState;
use app_common::middleware::AuthenticatedUser;
use tracing::{error, info};

/// 根据ID获取用户处理器
///
/// 处理 GET /api/users/:id 请求
/// 需要JWT认证，支持查看任何用户的公开资料信息
pub async fn get_user_by_id(
    State(state): State<AppState>,
    Path(user_id): Path<Uuid>,
    current_user: AuthenticatedUser,
) -> Result<impl IntoResponse> {
    info!(
        user_id = %user_id,
        current_user_id = %current_user.user_id,
        current_username = %current_user.username,
        "收到获取用户信息请求"
    );

    // 调用用户应用服务查找用户
    let user = state
        .user_service
        .find_user_by_id(user_id)
        .await?
        .ok_or_else(|| AppError::NotFound("用户不存在".to_string()))?;

    info!(
        user_id = %user.id,
        username = %user.username,
        requested_by = %current_user.username,
        "成功获取用户信息"
    );

    Ok(success_response(user, "获取用户信息成功"))
}

/// 获取当前用户信息处理器
///
/// 处理 GET /api/users/me 请求
#[allow(dead_code)]
pub async fn get_current_user(
    State(state): State<AppState>, // TODO: 添加认证中间件后，从请求中提取用户信息
                                   // Extension(current_user): Extension<AuthenticatedUser>,
) -> Result<impl IntoResponse> {
    // TODO: 从认证中间件获取当前用户ID
    let user_id = Uuid::new_v4(); // 临时占位符

    info!(user_id = %user_id, "收到获取当前用户信息请求");

    // 调用用户应用服务查找用户
    let user = state
        .user_service
        .find_user_by_id(user_id)
        .await?
        .ok_or_else(|| AppError::NotFound("用户不存在".to_string()))?;

    info!(user_id = %user.id, username = %user.username, "成功获取当前用户信息");

    Ok(success_response(user, "获取当前用户信息成功"))
}

/// 检查用户名是否可用处理器
///
/// 处理 GET /api/users/check-username?username=xxx 请求
#[allow(dead_code)]
pub async fn check_username_availability(
    State(state): State<AppState>,
    Query(params): Query<CheckUsernameQuery>,
) -> Result<impl IntoResponse> {
    info!(username = %params.username, "收到检查用户名可用性请求");

    // 调用用户应用服务检查用户名可用性
    let is_available = state
        .user_service
        .is_username_available(&params.username)
        .await?;

    info!(username = %params.username, is_available = is_available, "用户名可用性检查完成");

    let response = CheckUsernameResponse {
        username: params.username,
        available: is_available,
    };

    Ok(success_response(response, "用户名可用性检查完成"))
}

/// 检查用户名查询参数
#[derive(Debug, Deserialize)]
pub struct CheckUsernameQuery {
    #[allow(dead_code)]
    pub username: String,
}

/// 检查用户名响应
#[derive(Debug, Serialize)]
pub struct CheckUsernameResponse {
    pub username: String,
    pub available: bool,
}

/// 获取用户个人资料处理器
///
/// 处理 GET /api/users/profile 请求
pub async fn get_profile(State(state): State<AppState>) -> Result<impl IntoResponse> {
    info!("收到获取用户个人资料请求");

    // TODO: 从认证中间件获取当前用户ID
    let user_id = Uuid::new_v4(); // 临时占位符

    // 调用用户应用服务获取用户信息
    match state.user_service.find_user_by_id(user_id).await {
        Ok(user) => {
            info!(user_id = %user_id, "成功获取用户个人资料");
            Ok(success_response(user, "获取用户个人资料成功"))
        }
        Err(e) => {
            error!(user_id = %user_id, error = %e, "获取用户个人资料失败");
            Err(e)
        }
    }
}

/// 更新用户个人资料处理器
///
/// 处理 PUT /api/users/profile 请求
pub async fn update_profile(
    State(_state): State<AppState>,
    Json(_update_request): Json<UpdateUserRequest>,
) -> Result<impl IntoResponse> {
    info!("收到更新用户个人资料请求");

    // TODO: 实现用户个人资料更新逻辑
    // 当前UserApplicationService中没有update_user_profile方法
    // 需要在后续迭代中添加此功能

    let response = serde_json::json!({
        "message": "用户个人资料更新功能正在开发中",
        "status": "not_implemented"
    });

    Ok(Json(response))
}

/// 获取在线用户列表处理器
///
/// 处理 GET /api/users/online 请求
/// 返回当前在线的用户列表，基于WebSocket连接状态
pub async fn get_online_users(
    State(state): State<AppState>,
    user: AuthenticatedUser,
) -> Result<impl IntoResponse> {
    info!(current_user = %user.username, "收到获取在线用户列表请求");

    // 从WebSocket服务获取活跃连接列表
    let active_connections = state
        .websocket_service
        .get_connection_service()
        .get_active_connections()
        .await;

    // 将WebSocket连接信息转换为在线用户信息
    let mut online_users = Vec::new();
    let mut user_connection_counts = std::collections::HashMap::new();

    // 统计每个用户的连接数
    for connection in &active_connections {
        *user_connection_counts
            .entry(connection.user_id)
            .or_insert(0) += 1;
    }

    // 为每个唯一用户创建在线用户记录
    for connection in active_connections {
        // 避免重复添加同一用户
        if !online_users
            .iter()
            .any(|u: &OnlineUser| u.id == connection.user_id)
        {
            let connection_count = user_connection_counts
                .get(&connection.user_id)
                .unwrap_or(&0);

            online_users.push(OnlineUser {
                id: connection.user_id,
                username: connection.username.clone(),
                status: "online".to_string(),
                last_seen: connection.last_activity,
                connection_count: *connection_count,
            });
        }
    }

    let response = OnlineUsersResponse {
        users: online_users,
        total_count: user_connection_counts.len(),
        timestamp: Utc::now(),
    };

    info!(
        total_users = response.total_count,
        requesting_user = %user.username,
        "成功获取在线用户列表"
    );

    Ok(success_response(response, "获取在线用户列表成功"))
}

/// 在线用户信息
#[derive(Debug, Serialize)]
pub struct OnlineUser {
    pub id: Uuid,
    pub username: String,
    pub status: String,
    pub last_seen: DateTime<Utc>,
    pub connection_count: u32,
}

/// 在线用户列表响应
#[derive(Debug, Serialize)]
pub struct OnlineUsersResponse {
    pub users: Vec<OnlineUser>,
    pub total_count: usize,
    pub timestamp: DateTime<Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_online_user_response_serialization() {
        // 测试OnlineUser结构体的序列化
        let user = OnlineUser {
            id: Uuid::new_v4(),
            username: "test_user".to_string(),
            status: "online".to_string(),
            last_seen: Utc::now(),
            connection_count: 2,
        };

        // 验证序列化不会出错
        let serialized = serde_json::to_string(&user);
        assert!(serialized.is_ok());
    }

    #[tokio::test]
    async fn test_online_users_response_serialization() {
        // 测试OnlineUsersResponse结构体的序列化
        let response = OnlineUsersResponse {
            users: vec![
                OnlineUser {
                    id: Uuid::new_v4(),
                    username: "user1".to_string(),
                    status: "online".to_string(),
                    last_seen: Utc::now(),
                    connection_count: 1,
                },
                OnlineUser {
                    id: Uuid::new_v4(),
                    username: "user2".to_string(),
                    status: "online".to_string(),
                    last_seen: Utc::now(),
                    connection_count: 2,
                },
            ],
            total_count: 2,
            timestamp: Utc::now(),
        };

        // 验证序列化不会出错
        let serialized = serde_json::to_string(&response);
        assert!(serialized.is_ok());

        // 验证包含预期字段
        let json_str = serialized.unwrap();
        assert!(json_str.contains("users"));
        assert!(json_str.contains("total_count"));
        assert!(json_str.contains("timestamp"));
        assert!(json_str.contains("user1"));
        assert!(json_str.contains("user2"));
    }
}
