//! # WebSocket 路由处理器
//!
//! 处理WebSocket连接和消息的HTTP路由

use crate::routes::AppState;
use app_application::websocket_service::*;
use app_interfaces::{ BroadcastRequest, WebSocketConnectionRequest, WebSocketStatsResponse };

use app_common::utils::jwt_utils::JwtUtils;
use app_domain::websocket::*;
use axum::{
    Json,
    extract::{ Query, State, ws::{ Message, WebSocket, WebSocketUpgrade } },
    http::{ HeaderMap, StatusCode },
    response::IntoResponse,
};
use futures_util::{ sink::SinkExt, stream::StreamExt };
use std::{ collections::HashMap, sync::Arc };
use tokio::sync::mpsc;
use tracing::{ Span, debug, error, info, instrument, trace, warn };
use uuid::Uuid;

/// WebSocket升级处理器（带JWT认证）
///
/// 【功能】: 处理WebSocket连接升级请求，集成JWT认证
#[instrument(
    name = "websocket_upgrade",
    skip(ws, state, headers),
    fields(
        token_source = tracing::field::Empty,
        user_id = tracing::field::Empty,
        username = tracing::field::Empty,
        auth_result = tracing::field::Empty
    )
)]
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(state): State<AppState>,
    Query(params): Query<HashMap<String, String>>,
    headers: HeaderMap
) -> impl IntoResponse {
    let span = Span::current();
    info!("收到WebSocket升级请求");

    // 从查询参数或请求头中提取JWT token
    let token = params
        .get("token")
        .map(|s| s.as_str())
        .or_else(|| {
            headers
                .get("authorization")
                .and_then(|h| h.to_str().ok())
                .and_then(|h| h.strip_prefix("Bearer "))
        });

    let token = match token {
        Some(t) => {
            let token_source = if params.contains_key("token") {
                "query_param"
            } else {
                "auth_header"
            };
            span.record("token_source", token_source);
            info!("JWT token提取成功，来源: {}", token_source);
            t
        }
        None => {
            span.record("auth_result", "missing_token");
            warn!("WebSocket连接缺少JWT token");
            return (StatusCode::UNAUTHORIZED, "缺少认证token").into_response();
        }
    };

    // 验证JWT token
    let jwt_utils = JwtUtils::new(state.jwt_secret.clone());
    let claims = match jwt_utils.validate_token(token) {
        Ok(claims) => {
            info!("JWT token验证成功");
            claims
        }
        Err(e) => {
            span.record("auth_result", "token_invalid");
            warn!("WebSocket JWT认证失败: {:?}", e);
            return (StatusCode::UNAUTHORIZED, "认证失败").into_response();
        }
    };

    // 从JWT claims中提取用户信息
    let user_id = match Uuid::parse_str(&claims.sub) {
        Ok(id) => {
            span.record("user_id", id.to_string().as_str());
            id
        }
        Err(e) => {
            span.record("auth_result", "invalid_user_id");
            error!("解析用户ID失败: {}", e);
            return (StatusCode::BAD_REQUEST, "无效的用户ID").into_response();
        }
    };
    let username = claims.username.clone();
    span.record("username", username.as_str());
    span.record("auth_result", "success");

    info!("WebSocket JWT认证成功: 用户ID={}, 用户名={}", user_id, username);

    ws.on_upgrade(move |socket| handle_websocket_connection(socket, state, user_id, username))
}

/// 处理WebSocket连接
///
/// 【功能】: 管理单个WebSocket连接的生命周期
#[instrument(
    name = "websocket_connection",
    skip(socket, state),
    fields(
        user_id = %user_id,
        username = %username,
        connection_id = tracing::field::Empty,
        connection_status = tracing::field::Empty,
        messages_processed = 0u64,
        connection_duration_ms = tracing::field::Empty
    )
)]
async fn handle_websocket_connection(
    socket: WebSocket,
    state: AppState,
    user_id: Uuid,
    username: String
) {
    let span = Span::current();
    let connection_start = std::time::Instant::now();
    info!("建立WebSocket连接: 用户ID={}, 用户名={}", user_id, username);

    // 分割WebSocket为发送器和接收器
    let (mut ws_sender, mut ws_receiver) = socket.split();

    // 创建消息通道用于接收广播消息
    let (message_tx, mut message_rx) = mpsc::unbounded_channel::<Message>();

    // 通过应用服务建立连接
    let connection_request = WebSocketConnectionRequest {
        user_id,
        username: username.clone(),
        session_type: SessionType::Chat.to_string(),
        metadata: None,
    };

    let connection_id = match
        state.websocket_service.establish_connection(connection_request).await
    {
        Ok(id) => {
            span.record("connection_id", id.as_uuid().to_string().as_str());
            span.record("connection_status", "established");
            info!("WebSocket连接建立成功: 连接ID={}", id.as_uuid());
            id
        }
        Err(e) => {
            span.record("connection_status", "failed");
            error!("建立WebSocket连接失败: {}", e);
            return;
        }
    };

    info!("WebSocket连接已建立: 连接ID={}", connection_id);

    // 更新连接的发送通道
    if let Err(e) = update_connection_sender(&state, &connection_id, message_tx.clone()).await {
        error!("更新连接发送通道失败: {}", e);

        // 如果发送通道更新失败，尝试断开连接以避免状态不一致
        if let Err(disconnect_err) = state.websocket_service.disconnect(&connection_id).await {
            error!("断开连接失败: {}", disconnect_err);
        }
        return;
    }

    // 启动发送任务：从通道接收消息并发送到WebSocket
    let send_task = tokio::spawn(async move {
        while let Some(message) = message_rx.recv().await {
            if let Err(e) = ws_sender.send(message).await {
                // 检查是否是连接关闭错误，如果是则正常退出，否则记录错误
                let error_msg = e.to_string();
                if
                    error_msg.contains("closed connection") ||
                    error_msg.contains("Connection reset")
                {
                    info!("WebSocket连接已关闭，发送任务正常退出");
                } else {
                    error!("发送WebSocket消息失败: {}", e);
                }
                break;
            }
        }
        info!("WebSocket发送任务结束");
    });

    // 启动心跳任务：定期发送ping消息检测连接状态
    // 优化：集成稳定性管理，增强心跳机制
    let heartbeat_tx = message_tx.clone();
    let heartbeat_connection_id = connection_id;
    let heartbeat_websocket_service = state.websocket_service.clone();
    let heartbeat_task = tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(30)); // 优化为30秒
        interval.set_missed_tick_behavior(tokio::time::MissedTickBehavior::Skip);
        // 跳过第一次tick，避免立即发送心跳
        interval.tick().await;

        loop {
            interval.tick().await;

            // 检查发送通道是否仍然有效
            if heartbeat_tx.is_closed() {
                info!("发送通道已关闭，心跳任务退出: 连接ID={}", heartbeat_connection_id);
                break;
            }

            // 发送心跳ping消息
            if let Err(e) = heartbeat_tx.send(Message::Ping(vec![].into())) {
                info!("发送心跳失败，心跳任务退出: 连接ID={}, 错误={}", heartbeat_connection_id, e);
                break;
            }

            // 🔧 修复：更新连接活跃状态，现在update_activity总是返回Ok()
            // 连接不存在时会在debug日志中记录，但不会导致心跳任务退出
            if
                let Err(e) = heartbeat_websocket_service.update_activity(
                    &heartbeat_connection_id
                ).await
            {
                // 这种情况现在不应该发生，因为update_activity已修复为总是返回Ok()
                warn!("更新连接活跃状态失败: 连接ID={}, 错误={}", heartbeat_connection_id, e);
            } else {
                debug!("心跳发送成功，已更新活跃状态: 连接ID={}", heartbeat_connection_id);
            }
        }
        info!("心跳任务结束: 连接ID={}", heartbeat_connection_id);
    });

    // 启动接收任务：从WebSocket接收消息并处理
    let websocket_service = state.websocket_service.clone();
    let recv_username = username.clone();
    let recv_state = state.clone();
    let recv_span = span.clone();
    let recv_task = tokio::spawn(async move {
        let mut message_count = 0u64;

        while let Some(msg_result) = ws_receiver.next().await {
            match msg_result {
                Ok(msg) => {
                    message_count += 1;
                    recv_span.record("messages_processed", message_count);

                    // 更新连接活跃状态
                    if let Err(e) = websocket_service.update_activity(&connection_id).await {
                        warn!("更新连接活跃状态失败: {}", e);
                    }

                    // 处理消息
                    if
                        let Err(e) = handle_websocket_message(
                            msg,
                            &connection_id,
                            &user_id,
                            &recv_username,
                            &websocket_service,
                            &recv_state
                        ).await
                    {
                        error!("处理WebSocket消息失败: {}", e);
                    }
                }
                Err(e) => {
                    // 在Axum 0.8.4中，WebSocket错误处理已简化
                    // 检查错误字符串来判断错误类型
                    let error_str = e.to_string();
                    if
                        error_str.contains("Connection closed") ||
                        error_str.contains("connection closed")
                    {
                        info!("WebSocket连接正常关闭: 用户={}", recv_username);
                    } else if error_str.contains("Protocol") || error_str.contains("protocol") {
                        warn!("WebSocket协议错误: {} (用户={})", e, recv_username);
                    } else {
                        error!("接收WebSocket消息失败: {} (用户={})", e, recv_username);
                    }
                    break;
                }
            }
        }
    });

    // 等待任一任务完成（连接断开）
    // 修复：改进任务管理，确保所有任务正确清理
    let connection_cleanup_id = connection_id;
    let cleanup_username = username.clone();

    tokio::select! {
        result = send_task => {
            info!("WebSocket发送任务结束: 用户={}", cleanup_username);
            if let Err(e) = result {
                error!("发送任务异常结束: {}", e);
            }
        },
        result = recv_task => {
            info!("WebSocket接收任务结束: 用户={}", cleanup_username);
            if let Err(e) = result {
                error!("接收任务异常结束: {}", e);
            }
        },
        result = heartbeat_task => {
            info!("WebSocket心跳任务结束: 用户={}", cleanup_username);
            if let Err(e) = result {
                error!("心跳任务异常结束: {}", e);
            }
        },
    }

    // 记录连接持续时间
    let connection_duration = connection_start.elapsed();
    span.record("connection_duration_ms", connection_duration.as_millis() as u64);
    span.record("connection_status", "disconnected");

    // 修复：确保连接清理操作的可靠性
    match state.websocket_service.disconnect(&connection_cleanup_id).await {
        Ok(_) => {
            info!(
                "WebSocket连接已成功断开: 连接ID={}, 用户={}, 持续时间={}ms",
                connection_cleanup_id,
                cleanup_username,
                connection_duration.as_millis()
            );
        }
        Err(e) => {
            error!(
                "断开WebSocket连接失败: 连接ID={}, 用户={}, 错误={}, 持续时间={}ms",
                connection_cleanup_id,
                cleanup_username,
                e,
                connection_duration.as_millis()
            );
        }
    }
}

/// 处理WebSocket消息
///
/// 【功能】: 处理从客户端接收到的WebSocket消息，包括持久化到数据库
#[instrument(
    name = "websocket_message_handler",
    skip(message, websocket_service, state),
    fields(
        connection_id = %connection_id.as_uuid(),
        user_id = %user_id,
        username = %username,
        message_type = tracing::field::Empty,
        message_size = tracing::field::Empty,
        message_id = tracing::field::Empty,
        persistence_status = tracing::field::Empty,
        broadcast_count = tracing::field::Empty,
        processing_duration_ms = tracing::field::Empty
    )
)]
async fn handle_websocket_message(
    message: Message,
    connection_id: &ConnectionId,
    user_id: &Uuid,
    username: &str,
    websocket_service: &Arc<dyn WebSocketApplicationService>,
    state: &AppState
) -> Result<(), String> {
    let span = Span::current();
    let processing_start = std::time::Instant::now();
    match message {
        Message::Text(text) => {
            span.record("message_type", "text");
            span.record("message_size", text.len());
            info!("收到文本消息: 用户={}, 内容长度={}字节", username, text.len());

            // 将Utf8Bytes转换为字符串
            let text_str = text.to_string();

            // 尝试解析为结构化消息
            if let Ok(parsed_message) = serde_json::from_str::<serde_json::Value>(&text_str) {
                // 检查消息类型
                if let Some(message_type) = parsed_message.get("type").and_then(|t| t.as_str()) {
                    debug!("收到结构化消息，类型: {}", message_type);

                    // 处理不同类型的消息
                    match message_type {
                        "get_users" => {
                            // 处理获取在线用户请求
                            return handle_get_users_request(
                                connection_id,
                                user_id,
                                username,
                                websocket_service,
                                state
                            ).await;
                        }
                        "chat" => {
                            // 处理聊天消息 - 继续原有逻辑
                            if
                                let Some(content) = parsed_message
                                    .get("content")
                                    .and_then(|c| c.as_str())
                            {
                                debug!("解析聊天消息成功，内容: {}", content);
                                // 继续处理聊天消息
                                return handle_chat_message(
                                    content.to_string(),
                                    connection_id,
                                    user_id,
                                    username,
                                    websocket_service,
                                    state,
                                    span
                                ).await;
                            }
                        }
                        _ => {
                            debug!("未知消息类型: {}", message_type);
                        }
                    }
                }
            }

            // 如果不是结构化消息，按原有逻辑处理为聊天消息
            let message_content = text_str.clone();
            info!("处理为普通聊天消息，内容长度={}字节", message_content.len());

            // 调用聊天消息处理函数
            return handle_chat_message(
                message_content,
                connection_id,
                user_id,
                username,
                websocket_service,
                state,
                span
            ).await;
        }
        Message::Binary(data) => {
            span.record("message_type", "binary");
            span.record("message_size", data.len());
            let total_duration = processing_start.elapsed();
            span.record("processing_duration_ms", total_duration.as_millis() as u64);
            info!(
                "收到二进制消息: 用户={}, 大小={}字节, 处理耗时={}ms",
                username,
                data.len(),
                total_duration.as_millis()
            );
            // 二进制消息处理逻辑
        }
        Message::Ping(data) => {
            span.record("message_type", "ping");
            span.record("message_size", data.len());
            let total_duration = processing_start.elapsed();
            span.record("processing_duration_ms", total_duration.as_millis() as u64);
            info!(
                "收到Ping消息: 用户={}, 数据长度={}字节, 处理耗时={}ms",
                username,
                data.len(),
                total_duration.as_millis()
            );
            // Ping消息由Axum框架自动响应Pong，无需手动处理
        }
        Message::Pong(data) => {
            span.record("message_type", "pong");
            span.record("message_size", data.len());
            let total_duration = processing_start.elapsed();
            span.record("processing_duration_ms", total_duration.as_millis() as u64);

            // 优化：集成稳定性管理，处理Pong响应
            // 更新连接活跃状态，表明连接健康
            if let Err(e) = websocket_service.update_activity(connection_id).await {
                warn!("处理Pong响应时更新活跃状态失败: {}", e);
            }

            // 降低Pong响应日志级别，避免日志噪音
            if !data.is_empty() {
                debug!(
                    "收到Pong响应: 用户={}, 数据长度={}字节, 处理耗时={}ms",
                    username,
                    data.len(),
                    total_duration.as_millis()
                );
            } else {
                // 对于空的Pong响应，使用trace级别记录
                trace!(
                    "收到心跳Pong响应: 用户={}, 连接ID={}, 处理耗时={}ms",
                    username,
                    connection_id,
                    total_duration.as_millis()
                );
            }
            // Pong响应表明连接活跃，更新活跃状态已在上层处理
        }
        Message::Close(close_frame) => {
            span.record("message_type", "close");
            let total_duration = processing_start.elapsed();
            span.record("processing_duration_ms", total_duration.as_millis() as u64);
            info!(
                "收到关闭消息: 用户={}, 原因={:?}, 处理耗时={}ms",
                username,
                close_frame,
                total_duration.as_millis()
            );
            return Err("连接关闭".to_string());
        }
    }

    Ok(())
}

/// 获取WebSocket统计信息
///
/// 【功能】: 返回WebSocket连接和消息的统计信息
pub async fn get_websocket_stats(State(state): State<AppState>) -> Result<
    Json<WebSocketStatsResponse>,
    StatusCode
> {
    let stats = state.websocket_service.get_stats().await;
    Ok(Json(stats))
}

/// 获取活跃连接信息
///
/// 【功能】: 返回当前活跃的WebSocket连接信息
pub async fn get_active_connections(State(state): State<AppState>) -> Result<
    Json<Vec<WsSession>>,
    StatusCode
> {
    let span = Span::current();
    let start_time = std::time::Instant::now();
    info!("收到获取活跃WebSocket连接请求");

    // 从WebSocket服务获取活跃连接
    match state.websocket_service.get_active_connections().await {
        Ok(connections) => {
            let processing_time = start_time.elapsed();
            span.record("processing_duration_ms", processing_time.as_millis() as u64);
            span.record("connections_count", connections.len() as u64);

            info!(
                "成功获取活跃连接信息: 连接数={}, 处理耗时={}ms",
                connections.len(),
                processing_time.as_millis()
            );

            Ok(Json(connections))
        }
        Err(e) => {
            let processing_time = start_time.elapsed();
            span.record("processing_duration_ms", processing_time.as_millis() as u64);
            span.record("error", &e);

            error!("获取活跃连接信息失败: {}, 处理耗时={}ms", e, processing_time.as_millis());

            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_send_message_request_deserialization() {
        let json =
            r#"
        {
            "user_id": "550e8400-e29b-41d4-a716-446655440000",
            "content": "Hello, World!",
            "priority": "High"
        }
        "#;

        let request: SendMessageRequest = serde_json::from_str(json).unwrap();
        assert_eq!(request.content, "Hello, World!");
        assert_eq!(request.priority, Some(MessagePriority::High));
    }

    #[test]
    fn test_broadcast_message_request_deserialization() {
        let json =
            r#"
        {
            "content": "Broadcast message",
            "session_type": "Chat",
            "priority": "Normal"
        }
        "#;

        let request: BroadcastMessageRequest = serde_json::from_str(json).unwrap();
        assert_eq!(request.content, "Broadcast message");
        assert_eq!(request.session_type, Some(SessionType::Chat));
        assert_eq!(request.priority, Some(MessagePriority::Normal));
    }
}

/// 获取WebSocket连接信息
///
/// 【功能】: 返回WebSocket连接的详细信息
#[instrument(
    name = "get_websocket_connections",
    skip(_state),
    fields(response_generation_ms = tracing::field::Empty)
)]
pub async fn get_websocket_connections(State(_state): State<AppState>) -> Result<
    Json<serde_json::Value>,
    StatusCode
> {
    let span = Span::current();
    let start_time = std::time::Instant::now();
    info!("收到获取WebSocket连接信息请求");

    // TODO: 从WebSocket服务获取实际连接信息
    let connections_info =
        serde_json::json!({
        "active_connections": 45,
        "total_connections_today": 1250,
        "connections_by_type": {
            "chat": 30,
            "notification": 10,
            "system": 5
        },
        "connection_quality": {
            "excellent": 35,
            "good": 8,
            "poor": 2
        },
        "geographic_distribution": {
            "asia": 25,
            "europe": 12,
            "america": 8
        },
        "timestamp": chrono::Utc::now()
    });

    let response_duration = start_time.elapsed();
    span.record("response_generation_ms", response_duration.as_millis() as u64);
    info!("WebSocket连接信息响应生成完成，耗时={}ms", response_duration.as_millis());

    Ok(Json(connections_info))
}

/// 获取WebSocket性能指标
///
/// 【功能】: 返回WebSocket性能相关的详细指标
#[instrument(
    name = "get_websocket_metrics",
    skip(_state),
    fields(metrics_generation_ms = tracing::field::Empty)
)]
pub async fn get_websocket_metrics(State(_state): State<AppState>) -> Result<
    Json<serde_json::Value>,
    StatusCode
> {
    let span = Span::current();
    let start_time = std::time::Instant::now();
    info!("收到获取WebSocket性能指标请求");

    // TODO: 从WebSocket服务获取实际性能指标
    let metrics =
        serde_json::json!({
        "performance": {
            "message_throughput_per_second": 150,
            "average_latency_ms": 25,
            "p95_latency_ms": 45,
            "p99_latency_ms": 80
        },
        "reliability": {
            "connection_success_rate": 0.98,
            "message_delivery_rate": 0.995,
            "reconnection_rate": 0.02
        },
        "resource_usage": {
            "memory_usage_mb": 128,
            "cpu_usage_percent": 15,
            "network_bandwidth_mbps": 5.2
        },
        "error_statistics": {
            "connection_errors": 5,
            "message_errors": 2,
            "timeout_errors": 1
        },
        "timestamp": chrono::Utc::now()
    });

    let metrics_duration = start_time.elapsed();
    span.record("metrics_generation_ms", metrics_duration.as_millis() as u64);
    info!("WebSocket性能指标响应生成完成，耗时={}ms", metrics_duration.as_millis());

    Ok(Json(metrics))
}

/// 发送消息到指定用户
///
/// 【功能】: 向指定用户发送消息
#[derive(serde::Deserialize)]
#[allow(dead_code)]
pub struct SendMessageRequest {
    #[allow(dead_code)]
    pub user_id: Uuid,
    #[allow(dead_code)]
    pub content: String,
    #[allow(dead_code)]
    pub priority: Option<MessagePriority>,
}

#[allow(dead_code)]
pub async fn send_message_to_user(
    State(state): State<AppState>,
    Json(request): Json<SendMessageRequest>
) -> Result<Json<serde_json::Value>, StatusCode> {
    let broadcast_request = BroadcastRequest {
        content: request.content,
        target_users: Some(vec![request.user_id]),
        target_session_type: None,
        exclude_sender: false,
        sender_id: None,
        priority: request.priority.map(|p| p.to_string()),
    };

    match state.websocket_service.broadcast_message(broadcast_request).await {
        Ok(sent_count) =>
            Ok(
                Json(
                    serde_json::json!({
            "success": true,
            "sent_count": sent_count,
            "message": "消息发送成功"
        })
                )
            ),
        Err(e) => {
            error!("发送消息失败: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 广播消息到所有用户
///
/// 【功能】: 向所有连接的用户广播消息
#[derive(serde::Deserialize)]
#[allow(dead_code)]
pub struct BroadcastMessageRequest {
    #[allow(dead_code)]
    pub content: String,
    #[allow(dead_code)]
    pub session_type: Option<SessionType>,
    #[allow(dead_code)]
    pub priority: Option<MessagePriority>,
}

#[allow(dead_code)]
#[instrument(
    name = "broadcast_message_api",
    skip(state, request),
    fields(
        content_length = request.content.len(),
        session_type = ?request.session_type,
        priority = ?request.priority,
        sent_count = tracing::field::Empty,
        broadcast_duration_ms = tracing::field::Empty
    )
)]
pub async fn broadcast_message(
    State(state): State<AppState>,
    Json(request): Json<BroadcastMessageRequest>
) -> Result<Json<serde_json::Value>, StatusCode> {
    let span = Span::current();
    let broadcast_start = std::time::Instant::now();

    info!("收到广播消息请求: 内容长度={}字节", request.content.len());

    let broadcast_request = BroadcastRequest {
        content: request.content,
        target_users: None,
        target_session_type: request.session_type.map(|s| s.to_string()),
        exclude_sender: false,
        sender_id: None,
        priority: request.priority.map(|p| p.to_string()),
    };

    match state.websocket_service.broadcast_message(broadcast_request).await {
        Ok(sent_count) => {
            let broadcast_duration = broadcast_start.elapsed();
            span.record("sent_count", sent_count);
            span.record("broadcast_duration_ms", broadcast_duration.as_millis() as u64);

            info!(
                "消息广播成功: 发送给{}个连接, 耗时={}ms",
                sent_count,
                broadcast_duration.as_millis()
            );

            Ok(
                Json(
                    serde_json::json!({
                "success": true,
                "sent_count": sent_count,
                "message": "消息广播成功",
                "duration_ms": broadcast_duration.as_millis()
            })
                )
            )
        }
        Err(e) => {
            let broadcast_duration = broadcast_start.elapsed();
            span.record("sent_count", 0u64);
            span.record("broadcast_duration_ms", broadcast_duration.as_millis() as u64);

            error!("广播消息失败: {}, 耗时={}ms", e, broadcast_duration.as_millis());
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 注册连接的消息发送通道
///
/// 【功能】: 将WebSocket连接的消息发送通道注册到连接管理器中
async fn update_connection_sender(
    state: &AppState,
    connection_id: &ConnectionId,
    sender: mpsc::UnboundedSender<Message>
) -> Result<(), String> {
    info!("更新连接发送通道: 连接ID={}", connection_id);

    // 首先验证连接是否存在
    use app_infrastructure::websocket::connection_manager::WebSocketConnectionManager;

    if
        let Some(connection_manager) = state.websocket_service
            .get_connection_service()
            .as_any()
            .downcast_ref::<WebSocketConnectionManager>()
    {
        // 验证连接状态
        if !connection_manager.validate_connection_state(connection_id).await {
            warn!("连接状态无效，尝试清理无效连接: 连接ID={}", connection_id);
            connection_manager.cleanup_stale_connections().await;
        }

        // 更新发送通道
        match connection_manager.update_connection_sender(connection_id, sender).await {
            Ok(()) => {
                info!("连接发送通道更新成功: 连接ID={}", connection_id);
                Ok(())
            }
            Err(e) => {
                error!("连接发送通道更新失败: 连接ID={}, 错误={}", connection_id, e);
                Err(e)
            }
        }
    } else {
        let error_msg = "无法获取连接管理器实例，发送通道更新失败";
        error!("{}: 连接ID={}", error_msg, connection_id);
        Err(error_msg.to_string())
    }
}

/// 获取WebSocket稳定性状态
///
/// 【功能】: 返回WebSocket连接稳定性相关信息
pub async fn get_websocket_stability(State(state): State<AppState>) -> Result<
    Json<serde_json::Value>,
    StatusCode
> {
    let span = Span::current();
    span.record("handler", "get_websocket_stability");

    // 获取连接统计信息
    let stats_response = state.websocket_service.get_stats().await;
    let total_connections = stats_response.connection_stats.total_connections;
    let active_connections = stats_response.connection_stats.active_connections;

    // 获取活跃连接列表
    let active_connection_list = state.websocket_service
        .get_active_connections().await
        .unwrap_or_else(|_| Vec::new());

    // 计算健康连接数（假设60秒内有活动的连接为健康）
    let healthy_connections = active_connection_list.len(); // 简化逻辑：所有活跃连接都视为健康

    // 构建稳定性响应
    let stability =
        serde_json::json!({
        "stability": {
            "total_connections": total_connections,
            "active_connections": active_connections,
            "healthy_connections": healthy_connections,
            "unhealthy_connections": active_connections.saturating_sub(healthy_connections as u64),
            "health_rate": if active_connections > 0 {
                (healthy_connections as f64 / active_connections as f64) * 100.0
            } else {
                100.0
            }
        },
        "heartbeat": {
            "interval_seconds": 30,
            "timeout_seconds": 60,
            "enabled": true
        },
        "reconnection": {
            "max_attempts": 5,
            "base_delay_ms": 1000,
            "max_delay_ms": 30000,
            "exponential_backoff": true
        },
        "timestamp": chrono::Utc::now().to_rfc3339()
    });

    span.record("total_connections", total_connections);
    span.record("healthy_connections", healthy_connections);

    info!(
        "WebSocket稳定性状态查询完成: 总连接={}, 健康连接={}",
        total_connections,
        healthy_connections
    );
    Ok(Json(stability))
}

/// WebSocket实时监控数据推送处理器
///
/// 【功能】: 建立WebSocket连接，实时推送监控数据到监控面板
/// 【路径】: GET /websocket/monitoring
/// 【认证】: 无需认证（监控专用）
#[instrument(
    name = "websocket_monitoring_handler",
    skip(ws, state),
    fields(connection_id = tracing::field::Empty, monitoring_session = tracing::field::Empty)
)]
pub async fn websocket_monitoring_handler(
    ws: WebSocketUpgrade,
    State(state): State<AppState>
) -> impl IntoResponse {
    let span = Span::current();
    let connection_id = Uuid::new_v4();
    span.record("connection_id", connection_id.to_string());
    span.record("monitoring_session", "true");

    info!("收到WebSocket实时监控连接请求，连接ID: {}", connection_id);

    ws.on_upgrade(move |socket| handle_monitoring_websocket(socket, state, connection_id))
}

/// 处理WebSocket实时监控连接
///
/// 【功能】: 处理监控面板的WebSocket连接，定期推送统计数据
async fn handle_monitoring_websocket(socket: WebSocket, state: AppState, connection_id: Uuid) {
    let (mut sender, mut receiver) = socket.split();

    info!("WebSocket实时监控连接已建立，连接ID: {}", connection_id);

    // 创建定时器，每5秒推送一次监控数据
    let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(5));

    // 监控数据推送循环
    loop {
        tokio::select! {
            // 定时推送监控数据
            _ = interval.tick() => {
                match fetch_monitoring_data(&state).await {
                    Ok(monitoring_data) => {
                        let message = Message::Text(monitoring_data.into());
                        if let Err(e) = sender.send(message).await {
                            error!("发送监控数据失败，连接ID: {}, 错误: {}", connection_id, e);
                            break;
                        }
                        trace!("成功推送监控数据，连接ID: {}", connection_id);
                    }
                    Err(e) => {
                        error!("获取监控数据失败，连接ID: {}, 错误: {}", connection_id, e);
                    }
                }
            }

            // 处理客户端消息
            msg = receiver.next() => {
                match msg {
                    Some(Ok(Message::Text(text))) => {
                        debug!("收到监控客户端消息，连接ID: {}, 内容: {}", connection_id, text);

                        // 处理客户端请求（如请求特定数据）
                        if text == "ping" {
                            let pong_msg = Message::Text("pong".to_string().into());
                            if let Err(e) = sender.send(pong_msg).await {
                                error!("发送pong响应失败，连接ID: {}, 错误: {}", connection_id, e);
                                break;
                            }
                        }
                    }
                    Some(Ok(Message::Close(_))) => {
                        info!("监控客户端主动关闭连接，连接ID: {}", connection_id);
                        break;
                    }
                    Some(Err(e)) => {
                        error!("监控WebSocket连接错误，连接ID: {}, 错误: {}", connection_id, e);
                        break;
                    }
                    None => {
                        info!("监控WebSocket连接已断开，连接ID: {}", connection_id);
                        break;
                    }
                    _ => {
                        // 忽略其他类型的消息
                    }
                }
            }
        }
    }

    info!("WebSocket实时监控连接已关闭，连接ID: {}", connection_id);
}

/// 获取监控数据
///
/// 【功能】: 收集所有监控相关的数据并格式化为JSON
async fn fetch_monitoring_data(
    state: &AppState
) -> Result<String, Box<dyn std::error::Error + Send + Sync>> {
    // 获取WebSocket统计信息
    let stats = state.websocket_service.get_stats().await;

    // 获取活跃连接信息
    let active_connections = state.websocket_service
        .get_active_connections().await
        .unwrap_or_else(|_| Vec::new());

    // 构建监控数据
    let monitoring_data =
        serde_json::json!({
        "timestamp": chrono::Utc::now().to_rfc3339(),
        "type": "monitoring_update",
        "data": {
            "connection_stats": {
                "active_connections": stats.connection_stats.active_connections,
                "total_connections": stats.connection_stats.total_connections,
                "unique_users": stats.connection_stats.unique_active_users,
                "connections_by_type": {
                    "chat": active_connections.iter().filter(|c| c.session_type.to_string() == "chat").count(),
                    "monitoring": active_connections.iter().filter(|c| c.session_type.to_string() == "monitoring").count(),
                    "other": active_connections.iter().filter(|c| !["chat", "monitoring"].contains(&c.session_type.to_string().as_str())).count()
                }
            },
            "message_stats": {
                "total_messages": stats.message_stats.total_messages,
                "messages_per_minute": stats.message_stats.message_throughput * 60.0,
                "average_message_size": stats.message_stats.avg_message_size
            },
            "performance": {
                "message_throughput_per_second": 150, // TODO: 从实际服务获取
                "average_latency_ms": 25,
                "p95_latency_ms": 45,
                "memory_usage_mb": 128,
                "cpu_usage_percent": 15.5
            },
            "stability": {
                "uptime_seconds": 3600, // TODO: 从实际服务获取
                "error_rate_percent": 0.1,
                "reconnection_rate": 2.3,
                "stability_score": 98.7
            }
        }
    });

    Ok(monitoring_data.to_string())
}

/// 处理获取在线用户请求
///
/// 【功能】: 处理客户端发送的get_users请求，返回当前在线用户列表
#[tracing::instrument(
    skip(websocket_service, state),
    fields(
        connection_id = %connection_id.as_uuid(),
        user_id = %user_id,
        username = %username,
        response_size = tracing::field::Empty,
        user_count = tracing::field::Empty,
        processing_duration_ms = tracing::field::Empty
    )
)]
async fn handle_get_users_request(
    connection_id: &ConnectionId,
    user_id: &Uuid,
    username: &str,
    websocket_service: &Arc<dyn WebSocketApplicationService>,
    state: &AppState
) -> Result<(), String> {
    let span = Span::current();
    let processing_start = std::time::Instant::now();

    info!("处理获取在线用户请求: 用户={}", username);

    // 获取当前所有活跃连接
    let active_connections = match websocket_service.get_active_connections().await {
        Ok(connections) => connections,
        Err(e) => {
            error!("获取活跃连接失败: {}", e);
            return Err(format!("获取在线用户失败: {e}"));
        }
    };

    // 构建在线用户列表，去重并按用户分组
    let mut users_map: std::collections::HashMap<
        Uuid,
        app_interfaces::OnlineUserInfo
    > = std::collections::HashMap::new();

    for connection in active_connections {
        if connection.is_active() {
            // 如果用户已存在，更新为最新的连接时间
            let user_info = app_interfaces::OnlineUserInfo {
                user_id: connection.user_id,
                username: connection.username.clone(),
                connected_at: connection.connected_at,
                session_type: connection.session_type.to_string(),
            };

            // 保留最新的连接信息
            users_map
                .entry(connection.user_id)
                .and_modify(|existing| {
                    if connection.connected_at > existing.connected_at {
                        *existing = user_info.clone();
                    }
                })
                .or_insert(user_info);
        }
    }

    let online_users: Vec<app_interfaces::OnlineUserInfo> = users_map.into_values().collect();
    let user_count = online_users.len();

    span.record("user_count", user_count);
    info!("获取到{}个在线用户", user_count);

    // 构建响应消息
    let users_list_message = app_interfaces::WebSocketMessage::UsersList {
        users: online_users,
    };

    let response_content = match serde_json::to_string(&users_list_message) {
        Ok(content) => content,
        Err(e) => {
            error!("序列化用户列表失败: {}", e);
            return Err(format!("序列化响应失败: {e}"));
        }
    };

    span.record("response_size", response_content.len());

    // 发送响应给请求的用户
    let send_request = app_interfaces::SendMessageRequest {
        user_id: *user_id,
        content: response_content,
        priority: Some(app_interfaces::MessagePriority::High),
    };

    match websocket_service.send_message_to_user(send_request).await {
        Ok(_) => {
            let total_duration = processing_start.elapsed();
            span.record("processing_duration_ms", total_duration.as_millis() as u64);
            info!(
                "在线用户列表已发送给用户: {}, 用户数量={}, 处理耗时={}ms",
                username,
                user_count,
                total_duration.as_millis()
            );
            Ok(())
        }
        Err(e) => {
            let total_duration = processing_start.elapsed();
            span.record("processing_duration_ms", total_duration.as_millis() as u64);
            error!("发送在线用户列表失败: {}, 处理耗时={}ms", e, total_duration.as_millis());
            Err(format!("发送响应失败: {e}"))
        }
    }
}

/// 处理聊天消息
///
/// 【功能】: 处理聊天消息的持久化和广播
#[tracing::instrument(
    skip(websocket_service, state, span),
    fields(
        connection_id = %connection_id.as_uuid(),
        user_id = %user_id,
        username = %username,
        message_size = message_content.len(),
        message_id = tracing::field::Empty,
        persistence_status = tracing::field::Empty,
        broadcast_count = tracing::field::Empty,
        processing_duration_ms = tracing::field::Empty
    )
)]
async fn handle_chat_message(
    message_content: String,
    connection_id: &ConnectionId,
    user_id: &Uuid,
    username: &str,
    websocket_service: &Arc<dyn WebSocketApplicationService>,
    state: &AppState,
    span: Span
) -> Result<(), String> {
    let processing_start = std::time::Instant::now();

    info!("处理聊天消息: 用户={}, 内容长度={}字节", username, message_content.len());

    // 1. 首先将消息持久化到数据库
    let send_request = app_application::chat_service::SendMessageToGlobalRoomRequest {
        content: message_content.clone(),
        message_type: app_domain::entities::message::MessageType::Text,
        reply_to_id: None,
        metadata: None,
        priority: Some(1), // 普通优先级
        expires_at: None,
    };

    // 调用聊天应用服务保存消息到数据库
    info!("开始将消息持久化到数据库");
    let persistence_start = std::time::Instant::now();

    let (message_id, message_timestamp, is_persisted) = match
        state.chat_service.send_message_to_global_room(*user_id, send_request.clone()).await
    {
        Ok(msg) => {
            let persistence_duration = persistence_start.elapsed();
            span.record("message_id", msg.id.to_string().as_str());
            span.record("persistence_status", "success");
            info!(
                "消息已保存到数据库: 消息ID={}, 耗时={}ms",
                msg.id,
                persistence_duration.as_millis()
            );
            (msg.id, msg.created_at.to_rfc3339(), true)
        }
        Err(e) => {
            let persistence_duration = persistence_start.elapsed();
            span.record("persistence_status", "failed");
            error!(
                "保存消息到数据库失败: {} - 将继续广播消息但使用临时ID, 耗时={}ms",
                e,
                persistence_duration.as_millis()
            );
            warn!("数据库持久化失败，消息将仅在内存中广播");

            // 生成临时消息ID和时间戳，确保消息仍能广播
            let temp_id = uuid::Uuid::new_v4();
            let temp_timestamp = chrono::Utc::now().to_rfc3339();
            span.record("message_id", temp_id.to_string().as_str());

            (temp_id, temp_timestamp, false)
        }
    };

    // 记录消息持久化状态
    if is_persisted {
        info!("消息持久化成功: 消息ID={}", message_id);
    } else {
        warn!("消息未持久化，使用临时ID进行广播: 临时ID={}", message_id);
    }

    // 2. 构建广播消息格式（包含消息ID和时间戳）
    let broadcast_content =
        serde_json::json!({
        "message_id": message_id,
        "message_type": "Text",
        "content": message_content.clone(),
        "sender": {
            "username": username,
            "user_id": user_id.to_string()
        },
        "timestamp": message_timestamp,
        "is_persisted": is_persisted
    }).to_string();

    // 创建广播请求
    let broadcast_request = BroadcastRequest {
        content: broadcast_content,
        target_users: None, // 广播给所有用户
        target_session_type: Some(SessionType::Chat.to_string()),
        exclude_sender: true, // 排除发送者
        sender_id: Some(connection_id.as_uuid().to_string()),
        priority: Some(MessagePriority::Normal.to_string()),
    };

    // 广播消息
    info!("开始广播消息到其他连接");
    let broadcast_start = std::time::Instant::now();

    match websocket_service.broadcast_message(broadcast_request).await {
        Ok(sent_count) => {
            let broadcast_duration = broadcast_start.elapsed();
            span.record("broadcast_count", sent_count);
            info!("消息已广播给{}个连接, 耗时={}ms", sent_count, broadcast_duration.as_millis());
        }
        Err(e) => {
            let broadcast_duration = broadcast_start.elapsed();
            span.record("broadcast_count", 0u64);
            error!("广播消息失败: {}, 耗时={}ms", e, broadcast_duration.as_millis());
        }
    }

    // 记录总处理时间
    let total_duration = processing_start.elapsed();
    span.record("processing_duration_ms", total_duration.as_millis() as u64);
    info!("聊天消息处理完成，总耗时={}ms", total_duration.as_millis());

    Ok(())
}
