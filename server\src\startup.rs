//! # 应用程序启动模块
//!
//! 负责应用程序的初始化和启动，采用模块化DDD架构
//! 实现依赖注入模式，统一管理所有应用服务

use crate::config::AppConfig;
use crate::routes::{ self, AppState };
use anyhow::Result as AnyhowResult;
use axum::Router;
use std::sync::Arc;
use tracing::{ debug, error, info, warn };

// 导入日志系统
use app_common::logging::{ LoggerConfig, LoggerGuard, setup_logger_with_config };

// 导入依赖注入容器
use crate::dependency_injection::DefaultServiceContainer;

use sea_orm::DatabaseConnection;

// 导入数据库连接池管理器
use app_infrastructure::database::{ DatabaseConfig, DatabasePoolManager };

/// 运行应用程序
///
/// 使用新的依赖注入容器模式初始化和启动应用程序
pub async fn run() -> AnyhowResult<()> {
    // 0. 初始化日志系统
    let _logger_guard = init_logging()?;

    info!("🚀 开始启动 Axum Tutorial Server (使用新的IoC容器)...");

    // 1. 加载配置
    let config = AppConfig::from_env()?;
    info!("📋 配置加载完成: {:?}", config);

    // 2. 初始化数据库连接
    let database = init_database_connection(&config.database_url).await?;

    // 3. 创建服务容器
    let container = create_service_container(&config, database)?;

    // 4. 创建应用状态
    let app_state = create_app_state(&container, &config).await;

    // 5. 创建路由
    info!("🛣️ 正在创建路由...");
    let app = routes::create_routes(app_state);
    info!("✅ 路由创建完成");

    // 6. 启动服务器
    info!("🌐 服务器启动在: {}", config.http_addr);
    let listener = tokio::net::TcpListener::bind(config.http_addr).await?;
    info!("🎯 开始监听请求...");
    axum::serve(listener, app).await?;

    Ok(())
}

/// 链式调用风格的应用构建器特征
///
/// 定义流畅接口（Fluent Interface）的方法签名
#[allow(async_fn_in_trait)]
pub trait AppBuilderTrait {
    /// 初始化日志系统
    fn with_logging(self) -> AnyhowResult<Self> where Self: Sized;

    /// 从环境变量加载配置
    fn with_config_from_env(self) -> AnyhowResult<Self> where Self: Sized;

    /// 初始化数据库连接
    async fn with_database(self) -> AnyhowResult<Self> where Self: Sized;

    /// 初始化服务容器
    fn with_services(self) -> AnyhowResult<Self> where Self: Sized;

    /// 初始化路由
    async fn with_routes(self) -> AnyhowResult<Self> where Self: Sized;

    /// 检查构建器是否准备就绪
    fn is_ready(&self) -> bool;

    /// 构建最终的应用实例
    fn build(self) -> AnyhowResult<BuiltApp> where Self: Sized;
}

/// 构建完成的应用实例
///
/// 包含所有初始化完成的组件
pub struct BuiltApp {
    /// Axum路由器
    pub router: Router,
    /// 应用配置
    pub config: AppConfig,
}

impl BuiltApp {
    /// 启动应用服务器
    ///
    /// 这是链式调用的最终步骤，实际启动HTTP服务器
    pub async fn run(self) -> AnyhowResult<()> {
        info!("🌐 服务器启动在: {}", self.config.http_addr);
        let listener = tokio::net::TcpListener::bind(self.config.http_addr).await?;
        info!("🎯 开始监听请求...");
        axum::serve(listener, self.router).await?;
        Ok(())
    }
}

/// 链式调用风格的应用构建器
///
/// 实现流畅接口模式，支持链式调用来构建应用
pub struct AppBuilder {
    /// 日志守护器（可选）
    logger_guard: Option<LoggerGuard>,
    /// 应用配置（可选）
    config: Option<AppConfig>,
    /// 数据库连接（可选）
    database: Option<Arc<DatabaseConnection>>,
    /// 服务容器（可选）
    container: Option<DefaultServiceContainer>,
    /// 路由器（可选）
    router: Option<Router>,
}

impl Default for AppBuilder {
    fn default() -> Self {
        Self::new()
    }
}

impl AppBuilder {
    /// 创建新的应用构建器实例
    ///
    /// # 返回
    /// - `AppBuilder`: 新的构建器实例
    pub fn new() -> Self {
        info!("🏗️ 创建应用构建器");
        Self {
            logger_guard: None,
            config: None,
            database: None,
            container: None,
            router: None,
        }
    }
}

impl AppBuilderTrait for AppBuilder {
    /// 初始化日志系统
    fn with_logging(mut self) -> AnyhowResult<Self> {
        info!("📝 初始化日志系统...");
        let logger_guard = init_logging()?;
        self.logger_guard = Some(logger_guard);
        info!("✅ 日志系统初始化完成");
        Ok(self)
    }

    /// 从环境变量加载配置
    fn with_config_from_env(mut self) -> AnyhowResult<Self> {
        info!("📋 从环境变量加载配置...");
        let config = AppConfig::from_env()?;
        info!("📋 配置加载完成: {:?}", config);
        self.config = Some(config);
        Ok(self)
    }

    /// 初始化数据库连接
    async fn with_database(mut self) -> AnyhowResult<Self> {
        let config = self.config
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("配置未加载，请先调用 with_config_from_env()"))?;

        // 使用共享的数据库连接初始化函数
        let database = init_database_connection(&config.database_url).await?;

        self.database = Some(database);
        Ok(self)
    }

    /// 初始化服务容器
    fn with_services(mut self) -> AnyhowResult<Self> {
        let config = self.config
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("配置未加载，请先调用 with_config_from_env()"))?;
        let database = self.database
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("数据库未初始化，请先调用 with_database()"))?;

        // 使用共享的服务容器创建函数
        let container = create_service_container(config, database.clone())?;

        self.container = Some(container);
        Ok(self)
    }

    /// 初始化路由
    async fn with_routes(mut self) -> AnyhowResult<Self> {
        info!("🛣️ 初始化路由...");
        let config = self.config
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("配置未加载，请先调用 with_config_from_env()"))?;
        let container = self.container
            .as_ref()
            .ok_or_else(|| anyhow::anyhow!("服务容器未初始化，请先调用 with_services()"))?;

        // 使用共享的应用状态创建函数
        let app_state = create_app_state(container, config).await;

        // 创建路由
        let router = crate::routes::create_routes(app_state);
        self.router = Some(router);
        info!("✅ 路由初始化完成");
        Ok(self)
    }

    /// 检查构建器是否准备就绪
    fn is_ready(&self) -> bool {
        self.logger_guard.is_some() &&
            self.config.is_some() &&
            self.database.is_some() &&
            self.container.is_some() &&
            self.router.is_some()
    }

    /// 构建最终的应用实例
    fn build(self) -> AnyhowResult<BuiltApp> {
        info!("🔨 构建最终应用实例...");

        if !self.is_ready() {
            return Err(anyhow::anyhow!("构建器未准备就绪，请确保所有组件都已初始化"));
        }

        let built_app = BuiltApp {
            router: self.router.unwrap(),
            config: self.config.unwrap(),
        };

        info!("✅ 应用实例构建完成");
        Ok(built_app)
    }
}

/// 初始化数据库连接池
///
/// 使用企业级数据库连接池管理器创建优化的数据库连接
async fn init_database_connection(database_url: &str) -> AnyhowResult<Arc<DatabaseConnection>> {
    info!("🔗 正在初始化数据库连接池...");

    // 从环境变量创建数据库配置
    let database_config = DatabaseConfig::from_env().map_err(|e|
        anyhow::anyhow!("数据库配置创建失败: {}", e)
    )?;

    // 创建数据库连接池管理器
    let pool_manager = DatabasePoolManager::new(database_config).await.map_err(|e|
        anyhow::anyhow!("数据库连接池创建失败: {}", e)
    )?;

    // 启动连接池监控
    pool_manager.start_monitoring();

    // 执行健康检查
    if !pool_manager.health_check().await {
        return Err(anyhow::anyhow!("数据库连接池健康检查失败"));
    }

    // 获取数据库连接
    let database = pool_manager.get_connection();

    info!("🗄️ 数据库连接池初始化成功");
    info!(
        "📊 连接池配置: 最大连接数={}, 最小连接数={}",
        pool_manager.get_config().pool_config.max_connections,
        pool_manager.get_config().pool_config.min_connections
    );

    Ok(database)
}

/// 创建服务容器
///
/// 使用依赖注入容器构建器创建服务容器的共享函数
fn create_service_container(
    config: &AppConfig,
    database: Arc<DatabaseConnection>
) -> AnyhowResult<DefaultServiceContainer> {
    info!("🔧 正在创建服务容器...");
    use crate::dependency_injection::{ DefaultServiceContainerBuilder, ServiceContainerBuilder };

    let container = DefaultServiceContainerBuilder::new()
        .with_config(config.clone())
        .with_database(database)
        .with_all_services()
        .build()
        .map_err(|e| anyhow::anyhow!("服务容器构建失败: {}", e))?;

    info!("🔧 服务容器创建完成");
    Ok(container)
}

/// 使用新的声明式依赖配置API创建服务提供者
///
/// 演示新的trait-based声明式配置方式
#[allow(dead_code)]
fn create_service_provider_declarative(
    config: &AppConfig,
    database: Arc<DatabaseConnection>
) -> AnyhowResult<crate::dependency_injection::ServiceProvider> {
    info!("🔧 正在使用声明式API创建服务提供者...");
    use crate::dependency_injection::{ ServiceRegistry, ServiceRegistryConfig };

    // 方式1: 使用ServiceRegistryConfig trait
    let provider = ServiceRegistry::new()
        .with_database(database)
        .with_config(config.clone())
        .auto_register()
        .build()
        .map_err(|e| anyhow::anyhow!("声明式服务提供者构建失败: {}", e))?;

    info!("🔧 声明式服务提供者创建完成");
    Ok(provider)
}

/// 使用构建器模式的声明式依赖配置API创建服务提供者
///
/// 演示替代的构建器模式API
#[allow(dead_code)]
fn create_service_provider_builder(
    config: &AppConfig,
    database: Arc<DatabaseConnection>
) -> AnyhowResult<crate::dependency_injection::ServiceProvider> {
    info!("🔧 正在使用构建器模式API创建服务提供者...");
    use crate::dependency_injection::{ ServiceRegistry, ServiceRegistryBuilder };

    // 方式2: 使用ServiceRegistryBuilder trait
    let provider = ServiceRegistry::configure()
        .database(database)
        .config(config.clone())
        .enable_auto_registration()
        .create_provider()
        .map_err(|e| anyhow::anyhow!("构建器模式服务提供者构建失败: {}", e))?;

    info!("🔧 构建器模式服务提供者创建完成");
    Ok(provider)
}

/// 创建应用状态
///
/// 从服务容器创建应用状态的共享函数
async fn create_app_state(container: &DefaultServiceContainer, config: &AppConfig) -> AppState {
    info!("🔧 正在创建应用状态...");
    use crate::dependency_injection::ServiceContainer as ServiceContainerTrait;

    // 尝试创建企业级弹性搜索服务（TASK_52搜索系统）
    let resilient_chat_service = create_resilient_chat_service(container, config).await.ok();

    if resilient_chat_service.is_some() {
        info!("✅ 企业级弹性搜索服务创建成功");
    } else {
        warn!("⚠️ 企业级弹性搜索服务创建失败，将使用基础搜索服务");
    }

    let app_state = AppState {
        user_service: container.get_user_service(),
        task_service: container.get_task_service(),
        chat_service: container.get_chat_service(),
        resilient_chat_service,
        websocket_service: container.get_websocket_service(),
        db: container.get_database(),
        jwt_secret: config.jwt_secret.clone(),
    };

    info!("🔧 应用状态创建完成");
    app_state
}

/// 创建企业级弹性搜索服务
///
/// 创建弹性缓存服务
async fn create_resilient_cache_service() -> anyhow::Result<Arc<app_infrastructure::ResilientMessageSearchCacheService>> {
    use app_infrastructure::cache::{ CacheConfig, create_multi_tier_cache_service };
    use app_infrastructure::{ ResilienceConfig, ResilientMessageSearchCacheService };

    info!("🔧 创建弹性缓存服务...");

    // 创建缓存配置 - 优先从环境变量加载
    let cache_config = match CacheConfig::from_env() {
        Ok(config) => {
            info!("✅ 从环境变量加载缓存配置成功");
            config
        }
        Err(e) => {
            warn!("⚠️ 从环境变量加载缓存配置失败: {}, 使用开发环境默认配置", e);
            CacheConfig::development()
        }
    };

    // 创建多层缓存配置
    let multi_tier_config = app_infrastructure::cache::MultiTierCacheConfig::default();

    // 创建多层缓存服务
    let multi_tier_cache = create_multi_tier_cache_service(cache_config, multi_tier_config).await?;

    // 创建基础消息搜索缓存服务
    let base_cache_service = Arc::new(
        app_infrastructure::cache::MessageSearchCacheService::new(multi_tier_cache)
    );

    // 创建弹性配置
    let resilience_config = ResilienceConfig::default();

    // 创建弹性消息搜索缓存服务
    let resilient_cache_service = Arc::new(
        ResilientMessageSearchCacheService::new(base_cache_service, resilience_config).await?
    );

    info!("✅ 弹性缓存服务创建完成");
    Ok(resilient_cache_service)
}

/// 启动预计算调度器
async fn start_precompute_scheduler(
    cache_service: Option<Arc<app_infrastructure::ResilientMessageSearchCacheService>>
) -> anyhow::Result<()> {
    use app_application::{ PrecomputeScheduler, PrecomputeSchedulerConfig };

    info!("🚀 启动预计算调度器...");

    // 创建预计算调度器配置
    let scheduler_config = PrecomputeSchedulerConfig {
        max_concurrent_tasks: 5,
        hot_query_analysis_interval: 30, // 30秒分析一次热门搜索词
        precompute_cache_ttl: 1800, // 30分钟缓存TTL
        min_search_frequency: 3, // 最少搜索3次才成为热门词
        max_hot_queries: 50, // 最多50个热门搜索词
        task_timeout_seconds: 60, // 任务超时60秒
        stats_retention_days: 7, // 统计数据保留7天
    };

    // 创建并启动预计算调度器
    let scheduler = PrecomputeScheduler::new(scheduler_config);

    // 在后台启动调度器
    tokio::spawn(async move {
        if let Err(e) = scheduler.start().await {
            error!("预计算调度器运行失败: {}", e);
        }
    });

    // 如果有缓存服务，启动缓存预热
    if let Some(cache_service) = cache_service {
        tokio::spawn(async move {
            if let Err(e) = start_cache_warmup(cache_service).await {
                warn!("缓存预热启动失败: {}", e);
            }
        });
    }

    info!("✅ 预计算调度器启动完成");
    Ok(())
}

/// 启动缓存预热
async fn start_cache_warmup(
    cache_service: Arc<app_infrastructure::ResilientMessageSearchCacheService>
) -> anyhow::Result<()> {
    info!("🔥 启动缓存预热...");

    // 预热常见搜索词
    let common_queries = vec![
        "企业级",
        "搜索",
        "消息",
        "用户",
        "系统",
        "架构",
        "设计",
        "开发",
        "测试",
        "部署"
    ];

    // 模拟预热这些常见搜索词的结果
    for query in common_queries {
        // 这里可以预先计算一些常见搜索的结果并缓存
        // 暂时只记录预热操作
        debug!("预热搜索词: {}", query);
    }

    info!("✅ 缓存预热完成");
    Ok(())
}

/// 集成TASK_52企业级搜索系统的所有组件
async fn create_resilient_chat_service(
    container: &DefaultServiceContainer,
    config: &AppConfig
) -> anyhow::Result<Arc<app_application::ResilientChatApplicationService>> {
    use crate::dependency_injection::ServiceContainer as ServiceContainerTrait;
    use app_infrastructure::{ ResilienceConfig, ResilientMessageSearchCacheService };

    info!("🛡️ 正在创建企业级弹性搜索服务...");

    // 获取数据库连接
    let database = container.get_database();

    // 创建仓库实例
    let chat_repository: Arc<dyn app_domain::repositories::ChatRepositoryContract> = Arc::new(
        app_infrastructure::domains::chat::ChatRepository::from_arc(database.clone())
    );

    let user_repository: Arc<dyn app_domain::repositories::UserRepositoryContract> = Arc::new(
        app_infrastructure::domains::user::UserRepository::from_arc(database.clone())
    );

    // 创建领域服务实例（使用空实现）
    let chat_domain_service: Arc<dyn app_domain::services::ChatDomainService> = Arc::new(
        crate::dependency_injection::container::EmptyChatDomainService
    );

    // 创建弹性配置
    let resilience_config = ResilienceConfig::default();

    // 创建弹性缓存服务
    let resilient_cache_service = match create_resilient_cache_service().await {
        Ok(cache_service) => {
            info!("✅ 弹性缓存服务创建成功");
            Some(cache_service)
        }
        Err(e) => {
            warn!("⚠️ 弹性缓存服务创建失败，将使用降级模式: {}", e);
            None
        }
    };

    // 创建弹性聊天应用服务
    let resilient_service = app_application::ResilientChatApplicationService::new(
        chat_repository,
        chat_domain_service,
        user_repository,
        resilient_cache_service.clone(),
        resilience_config
    ).await?;

    // 启动预计算调度器
    if let Err(e) = start_precompute_scheduler(resilient_cache_service).await {
        warn!("⚠️ 预计算调度器启动失败: {}", e);
    }

    info!("✅ 企业级弹性搜索服务创建完成");
    Ok(Arc::new(resilient_service))
}

/// 从服务提供者创建应用状态
///
/// 使用新的声明式服务提供者创建应用状态
#[allow(dead_code)]
fn create_app_state_from_provider(
    provider: &crate::dependency_injection::ServiceProvider,
    config: &AppConfig
) -> AppState {
    info!("🔧 正在从服务提供者创建应用状态...");

    let app_state = AppState {
        user_service: provider.require_user_service().expect("用户服务未注册"),
        task_service: provider.require_task_service().expect("任务服务未注册"),
        chat_service: provider.require_chat_service().expect("聊天服务未注册"),
        resilient_chat_service: None, // 服务提供者模式暂不支持弹性搜索服务
        websocket_service: provider.require_websocket_service().expect("WebSocket服务未注册"),
        db: provider.get_database(),
        jwt_secret: config.jwt_secret.clone(),
    };

    info!("🔧 应用状态创建完成");
    app_state
}

/// 初始化日志系统
///
/// 根据环境配置初始化日志记录系统
fn init_logging() -> AnyhowResult<LoggerGuard> {
    // 根据环境变量决定日志配置
    let config = if
        std::env::var("RUST_ENV").unwrap_or_else(|_| "development".to_string()) == "production"
    {
        LoggerConfig::production().with_log_directory("logs").with_file_prefix("axum-tutorial")
    } else {
        LoggerConfig::development()
    };

    let guard = setup_logger_with_config(config)?;
    Ok(guard)
}

/// 链式调用风格的应用启动函数
///
/// 使用流畅接口模式，支持链式调用来构建和启动应用
///
/// # 示例
/// ```rust
/// // 链式调用启动应用
/// run_fluent().await?;
/// ```
pub async fn run_fluent() -> AnyhowResult<()> {
    info!("🚀 开始启动 Axum Tutorial Server (链式调用风格)...");

    AppBuilder::new()
        .with_logging()?
        .with_config_from_env()?
        .with_database().await?
        .with_services()?
        .with_routes().await?
        .build()?
        .run().await
}

/// 使用新的声明式依赖配置API启动应用程序
///
/// 演示新的trait-based声明式配置方式，减少宏的使用
///
/// # 示例
/// ```rust
/// // 使用声明式API启动应用
/// run_with_declarative_di().await?;
/// ```
#[allow(dead_code)]
pub async fn run_with_declarative_di() -> AnyhowResult<()> {
    // 0. 初始化日志系统
    let _logger_guard = init_logging()?;

    info!("🚀 开始启动 Axum Tutorial Server (声明式依赖注入)...");

    // 1. 加载配置
    let config = AppConfig::from_env()?;
    info!("📋 配置加载完成: {:?}", config);

    // 2. 初始化数据库连接
    let database = init_database_connection(&config.database_url).await?;

    // 3. 使用新的声明式API创建服务提供者
    let provider = create_service_provider_declarative(&config, database)?;

    // 4. 创建应用状态（从服务提供者）
    let app_state = create_app_state_from_provider(&provider, &config);

    // 5. 创建路由
    info!("🛣️ 正在创建路由...");
    let app = routes::create_routes(app_state);
    info!("✅ 路由创建完成");

    // 6. 启动服务器
    info!("🌐 服务器启动在: {}", config.http_addr);
    let listener = tokio::net::TcpListener::bind(config.http_addr).await?;
    info!("🎯 开始监听请求...");
    axum::serve(listener, app).await?;

    Ok(())
}

/// 使用构建器模式的声明式依赖配置API启动应用程序
///
/// 演示替代的构建器模式API
///
/// # 示例
/// ```rust
/// // 使用构建器模式API启动应用
/// run_with_builder_di().await?;
/// ```
#[allow(dead_code)]
pub async fn run_with_builder_di() -> AnyhowResult<()> {
    // 0. 初始化日志系统
    let _logger_guard = init_logging()?;

    info!("🚀 开始启动 Axum Tutorial Server (构建器模式依赖注入)...");

    // 1. 加载配置
    let config = AppConfig::from_env()?;
    info!("📋 配置加载完成: {:?}", config);

    // 2. 初始化数据库连接
    let database = init_database_connection(&config.database_url).await?;

    // 3. 使用构建器模式API创建服务提供者
    let provider = create_service_provider_builder(&config, database)?;

    // 4. 创建应用状态（从服务提供者）
    let app_state = create_app_state_from_provider(&provider, &config);

    // 5. 创建路由
    info!("🛣️ 正在创建路由...");
    let app = routes::create_routes(app_state);
    info!("✅ 路由创建完成");

    // 6. 启动服务器
    info!("🌐 服务器启动在: {}", config.http_addr);
    let listener = tokio::net::TcpListener::bind(config.http_addr).await?;
    info!("🎯 开始监听请求...");
    axum::serve(listener, app).await?;

    Ok(())
}
