//! # 依赖注入系统测试
//!
//! 测试服务容器（ServiceContainer）的依赖注入功能
//! 验证服务生命周期管理和单例行为
//! 测试新的模块导出和依赖注入模式优化

use app_application::{
    ChatApplicationService, ChatApplicationServiceImpl, TaskApplicationService,
    TaskApplicationServiceImpl, UserApplicationService, UserApplicationServiceImpl,
};
use app_common::error::{AppError, Result};
use app_domain::entities::{ChatRoom, Message, Task, User, UserSession};
use app_domain::repositories::{
    ChatRepositoryContract, TaskRepositoryContract, UserRepositoryContract,
};
use app_domain::services::{ChatDomainService, TaskDomainService, UserDomainService};
use app_infrastructure::{ChatRepository, TaskRepository, UserRepository};
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use sea_orm::{Database, DatabaseConnection};
use server::AppConfig;
use std::sync::Arc;
use uuid::Uuid;

/// 服务容器结构体
///
/// 统一管理所有应用服务，实现依赖注入模式
/// 提供服务注册、解析和生命周期管理功能
#[derive(Clone)]
pub struct ServiceContainer {
    /// 用户应用服务
    pub user_service: Arc<dyn UserApplicationService>,
    /// 任务应用服务
    pub task_service: Arc<dyn TaskApplicationService>,
    /// 聊天应用服务
    pub chat_service: Arc<dyn ChatApplicationService>,
    /// 数据库连接
    pub database: Arc<DatabaseConnection>,
}

impl ServiceContainer {
    /// 创建新的服务容器实例
    ///
    /// # 参数
    /// - `config`: 应用配置
    ///
    /// # 返回
    /// - `Result<Self>`: 服务容器实例或错误
    pub async fn new(config: &AppConfig) -> Result<Self> {
        // 1. 初始化数据库连接
        let db = Database::connect(&config.database_url)
            .await
            .map_err(|e| AppError::DatabaseError(format!("数据库连接失败: {e}")))?;
        let db_arc = Arc::new(db);

        // 2. 初始化仓库层
        let user_repository: Arc<dyn UserRepositoryContract> =
            Arc::new(UserRepository::from_arc(db_arc.clone()));
        let task_repository: Arc<dyn TaskRepositoryContract> =
            Arc::new(TaskRepository::from_arc(db_arc.clone()));
        let chat_repository: Arc<dyn ChatRepositoryContract> =
            Arc::new(ChatRepository::from_arc(db_arc.clone()));

        // 3. 初始化领域服务层（使用测试实现）
        let user_domain_service: Arc<dyn UserDomainService> = Arc::new(TestUserDomainService);
        let task_domain_service: Arc<dyn TaskDomainService> = Arc::new(TestTaskDomainService);
        let chat_domain_service: Arc<dyn ChatDomainService> = Arc::new(TestChatDomainService);

        // 4. 初始化应用服务层
        let user_service: Arc<dyn UserApplicationService> = Arc::new(
            UserApplicationServiceImpl::new(user_repository, user_domain_service),
        );
        let task_service: Arc<dyn TaskApplicationService> = Arc::new(
            TaskApplicationServiceImpl::new(task_repository, task_domain_service),
        );
        let chat_service: Arc<dyn ChatApplicationService> = Arc::new(
            ChatApplicationServiceImpl::new(chat_repository, chat_domain_service),
        );

        Ok(Self {
            user_service,
            task_service,
            chat_service,
            database: db_arc,
        })
    }

    /// 获取用户服务
    pub fn get_user_service(&self) -> Arc<dyn UserApplicationService> {
        self.user_service.clone()
    }

    /// 获取任务服务
    pub fn get_task_service(&self) -> Arc<dyn TaskApplicationService> {
        self.task_service.clone()
    }

    /// 获取聊天服务
    pub fn get_chat_service(&self) -> Arc<dyn ChatApplicationService> {
        self.chat_service.clone()
    }

    /// 获取数据库连接
    pub fn get_database(&self) -> Arc<DatabaseConnection> {
        self.database.clone()
    }
}

// 测试用的领域服务实现
struct TestUserDomainService;

#[async_trait]
impl UserDomainService for TestUserDomainService {
    async fn is_username_available(&self, _username: &str) -> Result<bool> {
        Ok(true)
    }
    async fn validate_user(&self, _user: &User) -> Result<()> {
        Ok(())
    }
    async fn create_user(&self, user: User) -> Result<User> {
        Ok(user)
    }
    async fn get_user_by_username(&self, _username: &str) -> Result<Option<User>> {
        Ok(None)
    }
    async fn get_user_by_id(&self, _user_id: Uuid) -> Result<Option<User>> {
        Ok(None)
    }
    async fn update_user(&self, user: User) -> Result<User> {
        Ok(user)
    }
    async fn delete_user(&self, _user_id: Uuid) -> Result<()> {
        Ok(())
    }
    async fn has_permission(
        &self,
        _user_id: Uuid,
        _resource_id: Uuid,
        _permission: &str,
    ) -> Result<bool> {
        Ok(true)
    }
}

struct TestTaskDomainService;

#[async_trait]
impl TaskDomainService for TestTaskDomainService {
    async fn validate_task(&self, _task: &Task) -> Result<()> {
        Ok(())
    }
    async fn create_task(&self, task: Task, _user_id: Uuid) -> Result<Task> {
        Ok(task)
    }
    async fn update_task(&self, task: Task, _user_id: Uuid) -> Result<Task> {
        Ok(task)
    }
    async fn complete_task(&self, task_id: Uuid, user_id: Uuid) -> Result<Task> {
        Ok(Task {
            id: task_id,
            title: "完成".to_string(),
            description: None,
            completed: true,
            user_id: Some(user_id),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        })
    }
    async fn reopen_task(&self, task_id: Uuid, user_id: Uuid) -> Result<Task> {
        Ok(Task {
            id: task_id,
            title: "重开".to_string(),
            description: None,
            completed: false,
            user_id: Some(user_id),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        })
    }
    async fn delete_task(&self, _task_id: Uuid, _user_id: Uuid) -> Result<()> {
        Ok(())
    }
    async fn get_user_tasks(&self, _user_id: Uuid, _include_completed: bool) -> Result<Vec<Task>> {
        Ok(Vec::new())
    }
    async fn get_task_by_id(&self, _task_id: Uuid, _user_id: Uuid) -> Result<Option<Task>> {
        Ok(None)
    }
    async fn has_task_permission(&self, _task_id: Uuid, _user_id: Uuid) -> Result<bool> {
        Ok(true)
    }
    async fn get_task_statistics(
        &self,
        _user_id: Uuid,
    ) -> Result<app_domain::services::task_service::TaskStatistics> {
        Ok(app_domain::services::task_service::TaskStatistics {
            total_tasks: 0,
            completed_tasks: 0,
            pending_tasks: 0,
            completion_rate: 0.0,
        })
    }
}

struct TestChatDomainService;

#[async_trait]
impl ChatDomainService for TestChatDomainService {
    async fn create_chat_room(&self, chat_room: ChatRoom, _creator_id: Uuid) -> Result<ChatRoom> {
        Ok(chat_room)
    }
    async fn send_message(&self, message: Message, _sender_id: Uuid) -> Result<Message> {
        Ok(message)
    }
    async fn get_message_history(
        &self,
        _room_id: Uuid,
        _user_id: Uuid,
        _limit: u32,
        _before: Option<DateTime<Utc>>,
    ) -> Result<Vec<Message>> {
        Ok(Vec::new())
    }
    async fn get_online_users(&self, _room_id: Uuid, _user_id: Uuid) -> Result<Vec<UserSession>> {
        Ok(Vec::new())
    }
    async fn create_user_session(&self, session: UserSession) -> Result<UserSession> {
        Ok(session)
    }
    async fn join_chat_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<()> {
        Ok(())
    }
    async fn leave_chat_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<()> {
        Ok(())
    }
    async fn update_session_status(&self, _session_id: Uuid, _is_online: bool) -> Result<()> {
        Ok(())
    }
    async fn delete_user_session(&self, _session_id: Uuid) -> Result<()> {
        Ok(())
    }
    async fn is_user_in_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<bool> {
        Ok(true)
    }
    async fn get_chat_room(&self, _room_id: Uuid, _user_id: Uuid) -> Result<Option<ChatRoom>> {
        Ok(None)
    }
}

/// 测试服务容器的基本功能
#[tokio::test]
async fn test_service_container_creation() {
    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3001");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");
    let container = ServiceContainer::new(&config)
        .await
        .expect("服务容器创建失败");

    // 验证服务容器正确创建
    // 验证不同服务实例的指针地址不同
    let user_ptr = container.user_service.as_ref() as *const _ as *const u8;
    let task_ptr = container.task_service.as_ref() as *const _ as *const u8;
    let chat_ptr = container.chat_service.as_ref() as *const _ as *const u8;

    assert_ne!(user_ptr, task_ptr);
    assert_ne!(task_ptr, chat_ptr);
    assert_ne!(user_ptr, chat_ptr);
}

/// 测试服务容器的单例行为
#[tokio::test]
async fn test_service_container_singleton_behavior() {
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3002");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");
    let container = ServiceContainer::new(&config)
        .await
        .expect("服务容器创建失败");

    // 多次获取同一服务，应该返回相同的实例
    let user_service1 = container.get_user_service();
    let user_service2 = container.get_user_service();
    assert!(Arc::ptr_eq(&user_service1, &user_service2));

    let task_service1 = container.get_task_service();
    let task_service2 = container.get_task_service();
    assert!(Arc::ptr_eq(&task_service1, &task_service2));

    let chat_service1 = container.get_chat_service();
    let chat_service2 = container.get_chat_service();
    assert!(Arc::ptr_eq(&chat_service1, &chat_service2));
}

/// 测试服务容器的克隆行为
#[tokio::test]
async fn test_service_container_clone() {
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3003");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");
    let container = ServiceContainer::new(&config)
        .await
        .expect("服务容器创建失败");

    // 克隆容器
    let cloned_container = container.clone();

    // 验证克隆后的容器持有相同的服务实例
    assert!(Arc::ptr_eq(
        &container.user_service,
        &cloned_container.user_service
    ));
    assert!(Arc::ptr_eq(
        &container.task_service,
        &cloned_container.task_service
    ));
    assert!(Arc::ptr_eq(
        &container.chat_service,
        &cloned_container.chat_service
    ));
    assert!(Arc::ptr_eq(&container.database, &cloned_container.database));
}

/// 测试服务容器的生命周期管理
#[tokio::test]
async fn test_service_container_lifecycle() {
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3005");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");

    // 创建多个容器实例
    let container1 = ServiceContainer::new(&config)
        .await
        .expect("服务容器1创建失败");
    let container2 = ServiceContainer::new(&config)
        .await
        .expect("服务容器2创建失败");

    // 验证不同容器实例持有不同的服务实例
    assert!(!Arc::ptr_eq(
        &container1.user_service,
        &container2.user_service
    ));
    assert!(!Arc::ptr_eq(
        &container1.task_service,
        &container2.task_service
    ));
    assert!(!Arc::ptr_eq(
        &container1.chat_service,
        &container2.chat_service
    ));
    assert!(!Arc::ptr_eq(&container1.database, &container2.database));
}

/// 测试服务容器的错误处理
#[tokio::test]
#[ignore] // 跳过此测试，因为SQLite可能接受无效URL
async fn test_service_container_error_handling() {
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3006");
        std::env::set_var(
            "DATABASE_URL",
            "mysql://invalid:invalid@invalid:3306/invalid",
        );
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");

    // 尝试创建服务容器，应该失败
    let result = ServiceContainer::new(&config).await;
    assert!(result.is_err());

    // 验证错误类型
    match result {
        Err(AppError::DatabaseError(_)) => {
            // 预期的错误类型
        }
        _ => panic!("期望数据库错误"),
    }
}

/// 测试服务容器的依赖解析
#[tokio::test]
async fn test_service_container_dependency_resolution() {
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3007");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");
    let container = ServiceContainer::new(&config)
        .await
        .expect("服务容器创建失败");

    // 验证服务依赖正确解析
    let user_service = container.get_user_service();
    let task_service = container.get_task_service();
    let chat_service = container.get_chat_service();
    let database = container.get_database();

    // 验证服务实例不为空
    // 验证服务实例存在（Arc引用计数大于0）
    assert!(Arc::strong_count(&user_service) > 0);
    assert!(Arc::strong_count(&task_service) > 0);
    assert!(Arc::strong_count(&chat_service) > 0);
    assert!(Arc::strong_count(&database) > 0);
}

/// 测试服务容器的并发安全性
#[tokio::test]
async fn test_service_container_concurrent_access() {
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3008");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");
    let container = Arc::new(
        ServiceContainer::new(&config)
            .await
            .expect("服务容器创建失败"),
    );

    // 创建多个并发任务
    let mut handles = Vec::new();
    for i in 0..10 {
        let container_clone = container.clone();
        let handle = tokio::spawn(async move {
            let user_service = container_clone.get_user_service();
            let task_service = container_clone.get_task_service();
            let chat_service = container_clone.get_chat_service();

            // 验证服务获取成功
            // 验证服务实例存在（Arc引用计数大于0）
            assert!(Arc::strong_count(&user_service) > 0);
            assert!(Arc::strong_count(&task_service) > 0);
            assert!(Arc::strong_count(&chat_service) > 0);

            i
        });
        handles.push(handle);
    }

    // 等待所有任务完成
    for handle in handles {
        handle.await.expect("并发任务执行失败");
    }
}

/// 测试新的依赖注入容器功能
#[tokio::test]
async fn test_new_dependency_injection_container() {
    use server::{DefaultServiceContainerBuilder, ServiceContainerBuilder, ServiceContainerTrait};

    // 创建测试数据库
    let database = Database::connect("sqlite::memory:")
        .await
        .expect("创建测试数据库失败");
    let database = Arc::new(database);

    // 创建测试配置
    let config = AppConfig {
        http_addr: "127.0.0.1:3000".parse().unwrap(),
        database_url: "sqlite::memory:".to_string(),
        jwt_secret: "test-secret".to_string(),
    };

    // 创建仓库
    let user_repository: Arc<dyn UserRepositoryContract> =
        Arc::new(UserRepository::from_arc(database.clone()));
    let task_repository: Arc<dyn TaskRepositoryContract> =
        Arc::new(TaskRepository::from_arc(database.clone()));
    let chat_repository: Arc<dyn ChatRepositoryContract> =
        Arc::new(ChatRepository::from_arc(database.clone()));

    // 创建领域服务
    let user_domain_service: Arc<dyn UserDomainService> = Arc::new(TestUserDomainService);
    let task_domain_service: Arc<dyn TaskDomainService> = Arc::new(TestTaskDomainService);
    let chat_domain_service: Arc<dyn ChatDomainService> = Arc::new(TestChatDomainService);

    // 创建WebSocket服务
    use app_domain::websocket::{
        WebSocketConnectionService, WebSocketMessageService, WebSocketStatsService,
    };
    use app_infrastructure::websocket::{
        WebSocketConnectionManager, WebSocketMessageDistributor, WebSocketStatsCollector,
    };

    let connection_manager = Arc::new(WebSocketConnectionManager::new());
    let connection_service: Arc<dyn WebSocketConnectionService> = connection_manager.clone();
    let message_service: Arc<dyn WebSocketMessageService> =
        Arc::new(WebSocketMessageDistributor::new(connection_manager.clone()));
    let stats_service: Arc<dyn WebSocketStatsService> =
        Arc::new(WebSocketStatsCollector::new(connection_manager.clone()));

    // 使用新的构建器模式创建服务容器
    let container = DefaultServiceContainerBuilder::new()
        .with_config(config)
        .with_database(database.clone())
        .with_user_service(user_repository, user_domain_service)
        .with_task_service(task_repository, task_domain_service)
        .with_chat_service(chat_repository, chat_domain_service)
        .with_websocket_service(connection_service, message_service, stats_service)
        .build()
        .expect("创建服务容器失败");

    // 验证服务容器功能
    let user_service = container.get_user_service();
    let task_service = container.get_task_service();
    let chat_service = container.get_chat_service();
    let websocket_service = container.get_websocket_service();
    let db = container.get_database();

    // 验证服务不为空
    assert!(Arc::strong_count(&user_service) > 0);
    assert!(Arc::strong_count(&task_service) > 0);
    assert!(Arc::strong_count(&chat_service) > 0);
    assert!(Arc::strong_count(&websocket_service) > 0);
    assert!(Arc::strong_count(&db) > 0);

    println!("✅ 新的依赖注入容器测试通过");
}

/// 测试重构后的启动流程
#[tokio::test]
async fn test_refactored_startup_flow() {
    use server::{DefaultServiceContainerBuilder, ServiceContainerBuilder, ServiceContainerTrait};

    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3009");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");

    // 创建数据库连接
    let database = Database::connect(&config.database_url)
        .await
        .expect("数据库连接失败");
    let database = Arc::new(database);

    // 使用构建器模式创建完整的服务容器
    let container = DefaultServiceContainerBuilder::new()
        .with_config(config)
        .with_database(database)
        .with_all_services() // 这个方法应该内部处理所有服务的创建
        .build()
        .expect("创建服务容器失败");

    // 验证所有服务都正确创建
    let user_service = container.get_user_service();
    let task_service = container.get_task_service();
    let chat_service = container.get_chat_service();
    let websocket_service = container.get_websocket_service();
    let db = container.get_database();

    // 验证服务不为空
    assert!(Arc::strong_count(&user_service) > 0);
    assert!(Arc::strong_count(&task_service) > 0);
    assert!(Arc::strong_count(&chat_service) > 0);
    assert!(Arc::strong_count(&websocket_service) > 0);
    assert!(Arc::strong_count(&db) > 0);

    println!("✅ 重构后的启动流程测试通过");
}

/// 测试模块导出优化
#[test]
fn test_module_export_optimization() {
    // 验证app_common模块导出
    use app_common::{AppError, Result};
    let _error: AppError = AppError::ValidationError("test".to_string());
    let _result: Result<()> = Ok(());

    // 验证app_domain模块导出 - 只测试实际存在的导出
    // 这些类型应该可以正常访问，如果编译通过说明导出正确

    // 验证app_infrastructure模块导出 - 只测试实际存在的导出
    // 这些类型应该可以正常访问，如果编译通过说明导出正确

    // 验证app_application模块导出 - 只测试实际存在的导出
    // 这些类型应该可以正常访问，如果编译通过说明导出正确

    // 验证server模块导出 - 只测试实际存在的导出
    // 这些类型应该可以正常访问，如果编译通过说明导出正确

    println!("✅ 模块导出优化测试通过");
}
