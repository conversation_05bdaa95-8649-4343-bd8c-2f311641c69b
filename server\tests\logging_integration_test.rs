//! # 日志系统集成测试
//!
//! 测试日志记录系统在实际HTTP请求中的工作情况

use app_common::logging::{
    LoggerConfig, request_logging_middleware, setup_logger_with_config, trace_layer,
};
use axum::{
    Router,
    body::Body,
    extract::Request,
    http::{Method, StatusCode},
    middleware,
    response::Response,
    routing::get,
};
use std::convert::Infallible;
use tower::ServiceExt;
use tower_http::cors::CorsLayer;

/// 简单的测试处理器
async fn test_handler() -> Result<Response<Body>, Infallible> {
    tracing::info!("处理测试请求");
    Ok(Response::new(Body::from("Hello, World!")))
}

/// 错误处理器，用于测试错误日志
async fn error_handler() -> Result<Response<Body>, Infallible> {
    tracing::error!("模拟错误情况");
    Ok(Response::builder()
        .status(StatusCode::INTERNAL_SERVER_ERROR)
        .body(Body::from("Internal Server Error"))
        .unwrap())
}

/// 创建测试应用
fn create_test_app() -> Router {
    Router::new()
        .route("/test", get(test_handler))
        .route("/error", get(error_handler))
        .layer(middleware::from_fn(request_logging_middleware))
        .layer(trace_layer())
        .layer(CorsLayer::permissive())
}

#[tokio::test]
async fn test_request_logging_middleware_success() {
    // 初始化测试日志系统
    let config = LoggerConfig::testing();
    let _guard = setup_logger_with_config(config).expect("Failed to setup logger");

    // 创建测试应用
    let app = create_test_app();

    // 创建测试请求
    let request = Request::builder()
        .method(Method::GET)
        .uri("/test")
        .header("user-agent", "test-agent")
        .header("x-forwarded-for", "192.168.1.1")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应
    assert_eq!(response.status(), StatusCode::OK);

    // 读取响应体
    let body = axum::body::to_bytes(response.into_body(), usize::MAX)
        .await
        .unwrap();
    assert_eq!(body, "Hello, World!");
}

#[tokio::test]
async fn test_request_logging_middleware_error() {
    // 初始化测试日志系统
    let config = LoggerConfig::testing();
    let _guard = setup_logger_with_config(config).expect("Failed to setup logger");

    // 创建测试应用
    let app = create_test_app();

    // 创建测试请求
    let request = Request::builder()
        .method(Method::GET)
        .uri("/error")
        .header("user-agent", "test-agent")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应
    assert_eq!(response.status(), StatusCode::INTERNAL_SERVER_ERROR);
}

#[tokio::test]
async fn test_request_logging_with_query_params() {
    // 初始化测试日志系统
    let config = LoggerConfig::testing();
    let _guard = setup_logger_with_config(config).expect("Failed to setup logger");

    // 创建测试应用
    let app = create_test_app();

    // 创建带查询参数的测试请求
    let request = Request::builder()
        .method(Method::GET)
        .uri("/test?param1=value1&param2=value2")
        .header("user-agent", "test-agent")
        .header("content-type", "application/json")
        .header("content-length", "0")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_request_logging_post_request() {
    // 初始化测试日志系统
    let config = LoggerConfig::testing();
    let _guard = setup_logger_with_config(config).expect("Failed to setup logger");

    // 创建支持POST的测试应用
    let app = Router::new()
        .route("/test", get(test_handler).post(test_handler))
        .layer(middleware::from_fn(request_logging_middleware))
        .layer(trace_layer());

    // 创建POST请求
    let request = Request::builder()
        .method(Method::POST)
        .uri("/test")
        .header("user-agent", "test-agent")
        .header("content-type", "application/json")
        .header("content-length", "13")
        .body(Body::from(r#"{"test":true}"#))
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_trace_layer_functionality() {
    // 初始化测试日志系统
    let config = LoggerConfig::testing();
    let _guard = setup_logger_with_config(config).expect("Failed to setup logger");

    // 创建只使用trace_layer的测试应用
    let app = Router::new()
        .route("/test", get(test_handler))
        .layer(trace_layer());

    // 创建测试请求
    let request = Request::builder()
        .method(Method::GET)
        .uri("/test")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();

    // 验证响应
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_logging_with_different_status_codes() {
    // 初始化测试日志系统
    let config = LoggerConfig::testing();
    let _guard = setup_logger_with_config(config).expect("Failed to setup logger");

    // 创建返回不同状态码的处理器
    async fn not_found_handler() -> Result<Response<Body>, Infallible> {
        Ok(Response::builder()
            .status(StatusCode::NOT_FOUND)
            .body(Body::from("Not Found"))
            .unwrap())
    }

    async fn redirect_handler() -> Result<Response<Body>, Infallible> {
        Ok(Response::builder()
            .status(StatusCode::MOVED_PERMANENTLY)
            .header("location", "/new-location")
            .body(Body::empty())
            .unwrap())
    }

    let app = Router::new()
        .route("/success", get(test_handler))
        .route("/not-found", get(not_found_handler))
        .route("/redirect", get(redirect_handler))
        .route("/error", get(error_handler))
        .layer(middleware::from_fn(request_logging_middleware));

    // 测试成功响应 (2xx)
    let request = Request::builder()
        .method(Method::GET)
        .uri("/success")
        .body(Body::empty())
        .unwrap();
    let response = app.clone().oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::OK);

    // 测试重定向响应 (3xx)
    let request = Request::builder()
        .method(Method::GET)
        .uri("/redirect")
        .body(Body::empty())
        .unwrap();
    let response = app.clone().oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::MOVED_PERMANENTLY);

    // 测试客户端错误响应 (4xx)
    let request = Request::builder()
        .method(Method::GET)
        .uri("/not-found")
        .body(Body::empty())
        .unwrap();
    let response = app.clone().oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::NOT_FOUND);

    // 测试服务器错误响应 (5xx)
    let request = Request::builder()
        .method(Method::GET)
        .uri("/error")
        .body(Body::empty())
        .unwrap();
    let response = app.oneshot(request).await.unwrap();
    assert_eq!(response.status(), StatusCode::INTERNAL_SERVER_ERROR);
}
