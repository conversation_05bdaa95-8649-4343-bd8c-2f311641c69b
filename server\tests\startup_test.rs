//! # 启动逻辑测试
//!
//! 测试依赖注入和服务初始化流程

use server::AppConfig;

/// 测试配置加载
#[tokio::test]
async fn test_config_loading() {
    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3104");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");

    assert_eq!(config.http_addr.to_string(), "127.0.0.1:3104");
    assert_eq!(config.database_url, "sqlite::memory:");
    assert_eq!(config.jwt_secret, "test_secret_key");
}

/// 测试startup.rs中的重复代码清理
/// 验证数据库连接初始化功能
#[tokio::test]
async fn test_database_connection_initialization() {
    use sea_orm::{ConnectionTrait, Database};
    use std::sync::Arc;

    // 设置测试环境变量
    unsafe {
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
    }

    let database_url = "sqlite::memory:";

    // 测试数据库连接创建
    let db = Database::connect(database_url)
        .await
        .expect("数据库连接失败");
    let database = Arc::new(db);

    // 验证连接有效性（检查数据库后端类型）
    use sea_orm::DatabaseBackend;
    match database.get_database_backend() {
        DatabaseBackend::Sqlite => {
            // SQLite数据库后端验证成功
        }
        _ => panic!("期望SQLite数据库后端"),
    }
}

/// 测试服务容器构建功能
#[tokio::test]
async fn test_service_container_building() {
    use sea_orm::Database;
    use server::{DefaultServiceContainerBuilder, ServiceContainerBuilder, ServiceContainerTrait};
    use std::sync::Arc;

    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3103");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");
    let database = Arc::new(
        Database::connect(&config.database_url)
            .await
            .expect("数据库连接失败"),
    );

    // 测试服务容器构建
    let container = DefaultServiceContainerBuilder::new()
        .with_config(config)
        .with_database(database)
        .with_all_services()
        .build()
        .expect("服务容器构建失败");

    // 验证服务可用性（检查Arc指针不为空）
    assert!(Arc::strong_count(&container.get_user_service()) > 0);
    assert!(Arc::strong_count(&container.get_task_service()) > 0);
    assert!(Arc::strong_count(&container.get_chat_service()) > 0);
    assert!(Arc::strong_count(&container.get_websocket_service()) > 0);
}

/// 测试应用状态创建功能
#[tokio::test]
async fn test_app_state_creation() {
    use sea_orm::Database;
    use server::routes::AppState;
    use server::{DefaultServiceContainerBuilder, ServiceContainerBuilder, ServiceContainerTrait};
    use std::sync::Arc;

    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3104");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");
    let database = Arc::new(
        Database::connect(&config.database_url)
            .await
            .expect("数据库连接失败"),
    );

    let container = DefaultServiceContainerBuilder::new()
        .with_config(config.clone())
        .with_database(database)
        .with_all_services()
        .build()
        .expect("服务容器构建失败");

    // 测试应用状态创建
    let app_state = AppState {
        user_service: container.get_user_service(),
        task_service: container.get_task_service(),
        chat_service: container.get_chat_service(),
        websocket_service: container.get_websocket_service(),
        db: container.get_database(),
        jwt_secret: config.jwt_secret.clone(),
    };

    // 验证应用状态（检查Arc指针不为空）
    assert!(Arc::strong_count(&app_state.user_service) > 0);
    assert!(Arc::strong_count(&app_state.task_service) > 0);
    assert!(Arc::strong_count(&app_state.chat_service) > 0);
    assert!(Arc::strong_count(&app_state.websocket_service) > 0);
    assert!(Arc::strong_count(&app_state.db) > 0);
    assert_eq!(app_state.jwt_secret, "test_secret_key");
}

/// 测试声明式依赖配置方式
/// 验证通过trait简化服务注册过程（移除宏使用）
#[tokio::test]
async fn test_declarative_service_configuration() {
    use sea_orm::Database;
    use server::dependency_injection::{ServiceRegistry, ServiceRegistryConfig};
    use std::sync::Arc;

    // 设置测试环境变量
    unsafe {
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = server::AppConfig::from_env().expect("配置加载失败");
    let database = Arc::new(
        Database::connect(&config.database_url)
            .await
            .expect("数据库连接失败"),
    );

    // 测试声明式服务注册 - 使用trait方法
    let provider = ServiceRegistry::new()
        .with_database(database)
        .with_config(config)
        .auto_register()
        .build()
        .expect("服务提供者构建失败");

    // 验证服务可以被解析
    assert!(provider.get_user_service().is_some());
    assert!(provider.get_task_service().is_some());
    assert!(provider.get_chat_service().is_some());
    assert!(provider.get_websocket_service().is_some());
}

/// 测试声明式服务注册宏
/// 验证宏简化的服务注册过程
#[tokio::test]
async fn test_declarative_service_macro() {
    use sea_orm::Database;
    use std::sync::Arc;

    // 设置测试环境变量
    unsafe {
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = server::AppConfig::from_env().expect("配置加载失败");
    let database = Arc::new(
        Database::connect(&config.database_url)
            .await
            .expect("数据库连接失败"),
    );

    // 使用构建器模式进行声明式服务注册（替代宏）
    use server::dependency_injection::{ServiceRegistry, ServiceRegistryBuilder};
    let provider = ServiceRegistry::configure()
        .database(database)
        .config(config)
        .enable_auto_registration()
        .create_provider()
        .expect("服务提供者构建失败");

    // 验证服务可以被解析
    assert!(provider.get_user_service().is_some());
    assert!(provider.get_task_service().is_some());
    assert!(provider.get_chat_service().is_some());
    assert!(provider.get_websocket_service().is_some());
}

/// 测试新的依赖注入启动流程（替代已删除的Services测试）
#[tokio::test]
async fn test_dependency_injection_container() {
    use sea_orm::Database;
    use server::{DefaultServiceContainerBuilder, ServiceContainerBuilder, ServiceContainerTrait};
    use std::sync::Arc;

    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3102");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    // 加载配置
    let config = AppConfig::from_env().expect("配置加载失败");

    // 初始化数据库连接
    let database = Arc::new(
        Database::connect(&config.database_url)
            .await
            .expect("数据库连接失败"),
    );

    // 使用新的构建器模式创建服务容器
    let container = DefaultServiceContainerBuilder::new()
        .with_config(config)
        .with_database(database)
        .with_all_services()
        .build()
        .expect("创建服务容器失败");

    // 验证所有服务都正确创建
    let user_service = container.get_user_service();
    let task_service = container.get_task_service();
    let chat_service = container.get_chat_service();
    let websocket_service = container.get_websocket_service();
    let database = container.get_database();

    // 验证服务不为空（通过Arc强引用计数检查）
    assert!(Arc::strong_count(&user_service) > 0);
    assert!(Arc::strong_count(&task_service) > 0);
    assert!(Arc::strong_count(&chat_service) > 0);
    assert!(Arc::strong_count(&websocket_service) > 0);
    assert!(Arc::strong_count(&database) > 0);

    println!("✅ 依赖注入容器测试通过");
}

/// 测试新的依赖注入启动流程
#[tokio::test]
async fn test_new_dependency_injection_startup() {
    use server::{DefaultServiceContainerBuilder, ServiceContainerBuilder, ServiceContainerTrait};

    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3010");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    let config = AppConfig::from_env().expect("配置加载失败");

    // 创建数据库连接
    let database = sea_orm::Database::connect(&config.database_url)
        .await
        .expect("数据库连接失败");
    let database = std::sync::Arc::new(database);

    // 使用新的构建器模式创建服务容器
    let container = DefaultServiceContainerBuilder::new()
        .with_config(config)
        .with_database(database)
        .with_all_services()
        .build()
        .expect("创建服务容器失败");

    // 验证所有服务都正确创建
    let user_service = container.get_user_service();
    let task_service = container.get_task_service();
    let chat_service = container.get_chat_service();
    let websocket_service = container.get_websocket_service();
    let db = container.get_database();

    // 验证服务不为空
    assert!(std::sync::Arc::strong_count(&user_service) > 0);
    assert!(std::sync::Arc::strong_count(&task_service) > 0);
    assert!(std::sync::Arc::strong_count(&chat_service) > 0);
    assert!(std::sync::Arc::strong_count(&websocket_service) > 0);
    assert!(std::sync::Arc::strong_count(&db) > 0);

    println!("✅ 新的依赖注入启动流程测试通过");
}

/// 测试链式调用风格的应用构建器
#[tokio::test]
async fn test_fluent_app_builder() {
    use server::startup::{AppBuilder, AppBuilderTrait};

    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3020");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    // 测试链式调用构建应用
    let app_builder = AppBuilder::new()
        .with_logging()
        .expect("日志初始化失败")
        .with_config_from_env()
        .expect("配置加载失败")
        .with_database()
        .await
        .expect("数据库初始化失败")
        .with_services()
        .expect("服务初始化失败")
        .with_routes()
        .expect("路由初始化失败");

    // 验证构建器状态
    assert!(app_builder.is_ready());

    println!("✅ 链式调用风格的应用构建器测试通过");
}

/// 测试链式调用风格的应用启动流程
#[tokio::test]
async fn test_fluent_app_startup_flow() {
    use server::startup::{AppBuilder, AppBuilderTrait};
    use std::time::Duration;
    use tokio::time::timeout;

    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3030");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    // 测试完整的链式调用启动流程（但不实际启动服务器）
    let result = timeout(Duration::from_secs(5), async {
        AppBuilder::new()
            .with_logging()
            .expect("日志初始化失败")
            .with_config_from_env()
            .expect("配置加载失败")
            .with_database()
            .await
            .expect("数据库初始化失败")
            .with_services()
            .expect("服务初始化失败")
            .with_routes()
            .expect("路由初始化失败")
            .build()
            .expect("应用构建失败")
    })
    .await;

    assert!(result.is_ok(), "链式调用启动流程应该在5秒内完成");
    println!("✅ 链式调用风格的应用启动流程测试通过");
}

/// 测试新的run_fluent函数（不实际启动服务器）
#[tokio::test]
async fn test_run_fluent_function() {
    use server::run_fluent;
    use std::time::Duration;
    use tokio::time::timeout;

    // 设置测试环境变量
    unsafe {
        std::env::set_var("HTTP_ADDR", "127.0.0.1:3040");
        std::env::set_var("DATABASE_URL", "sqlite::memory:");
        std::env::set_var("JWT_SECRET", "test_secret_key");
    }

    // 测试run_fluent函数的构建阶段（不实际启动服务器）
    // 我们通过超时来模拟测试，因为实际启动会阻塞
    let result = timeout(Duration::from_millis(100), async { run_fluent().await }).await;

    // 预期会超时，因为run_fluent会尝试启动服务器
    assert!(result.is_err(), "run_fluent应该会尝试启动服务器并超时");
    println!("✅ run_fluent函数测试通过（预期超时）");
}
