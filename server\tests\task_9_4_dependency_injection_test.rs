//! # 任务9.4 - 依赖注入更新测试
//!
//! 测试应用服务层的依赖注入是否正确更新，确保：
//! 1. 应用服务正确使用新的仓储接口
//! 2. 应用服务正确使用新的领域服务
//! 3. 依赖注入容器正确创建和管理服务
//! 4. 服务之间的依赖关系正确建立

use anyhow::Result;
use std::sync::Arc;
use tokio;

// 导入测试所需的模块
use axum_server::{
    config::AppConfig,
    dependency_injection::{
        DefaultServiceContainerBuilder, ServiceContainer as ServiceContainerTrait,
        ServiceContainerBuilder,
    },
};

// 导入应用服务trait
use app_application::{
    ChatApplicationService, TaskApplicationService, UserApplicationService,
    WebSocketApplicationService,
};

/// 测试依赖注入容器的创建和服务解析
#[tokio::test]
async fn test_dependency_injection_container_creation() -> Result<()> {
    println!("🧪 测试依赖注入容器的创建和服务解析");

    // 1. 创建测试配置
    let config = create_test_config();

    // 2. 创建测试数据库连接
    let database = create_test_database_connection().await?;

    // 3. 创建服务容器
    let container = DefaultServiceContainerBuilder::new()
        .with_config(config)
        .with_database(database)
        .with_all_services()
        .build()?;

    // 4. 验证服务容器可以解析所有服务
    let user_service = container.get_user_service();
    let task_service = container.get_task_service();
    let chat_service = container.get_chat_service();
    let websocket_service = container.get_websocket_service();
    let database = container.get_database();

    // 5. 验证服务不为空（通过检查Arc的强引用计数）
    assert!(Arc::strong_count(&user_service) >= 1);
    assert!(Arc::strong_count(&task_service) >= 1);
    assert!(Arc::strong_count(&chat_service) >= 1);
    assert!(Arc::strong_count(&websocket_service) >= 1);
    assert!(Arc::strong_count(&database) >= 1);

    println!("✅ 依赖注入容器创建和服务解析测试通过");
    Ok(())
}

/// 测试用户应用服务的依赖注入
#[tokio::test]
async fn test_user_service_dependency_injection() -> Result<()> {
    println!("🧪 测试用户应用服务的依赖注入");

    // 1. 创建服务容器
    let container = create_test_container().await?;

    // 2. 获取用户服务
    let user_service = container.get_user_service();

    // 3. 测试用户名可用性检查（这会调用领域服务）
    let is_available = user_service
        .is_username_available("test_user_12345")
        .await?;
    assert!(is_available, "新用户名应该可用");

    println!("✅ 用户应用服务依赖注入测试通过");
    Ok(())
}

/// 测试任务应用服务的依赖注入
#[tokio::test]
async fn test_task_service_dependency_injection() -> Result<()> {
    println!("🧪 测试任务应用服务的依赖注入");

    // 1. 创建服务容器
    let container = create_test_container().await?;

    // 2. 获取任务服务
    let task_service = container.get_task_service();

    // 3. 创建测试用户ID
    let test_user_id = uuid::Uuid::new_v4();

    // 4. 测试获取用户任务（这会调用仓储和领域服务）
    let tasks = task_service.get_user_tasks(test_user_id).await?;
    assert!(tasks.is_empty(), "新用户应该没有任务");

    println!("✅ 任务应用服务依赖注入测试通过");
    Ok(())
}

/// 测试聊天应用服务的依赖注入
#[tokio::test]
async fn test_chat_service_dependency_injection() -> Result<()> {
    println!("🧪 测试聊天应用服务的依赖注入");

    // 1. 创建服务容器
    let container = create_test_container().await?;

    // 2. 获取聊天服务
    let chat_service = container.get_chat_service();

    // 3. 创建测试用户ID和房间ID
    let test_user_id = uuid::Uuid::new_v4();
    let test_room_id = uuid::Uuid::new_v4();

    // 4. 测试获取在线用户（这会调用仓储和领域服务）
    let online_users = chat_service
        .get_online_users(test_room_id, test_user_id)
        .await?;
    assert!(online_users.is_empty(), "新房间应该没有在线用户");

    println!("✅ 聊天应用服务依赖注入测试通过");
    Ok(())
}

/// 测试WebSocket应用服务的依赖注入
#[tokio::test]
async fn test_websocket_service_dependency_injection() -> Result<()> {
    println!("🧪 测试WebSocket应用服务的依赖注入");

    // 1. 创建服务容器
    let container = create_test_container().await?;

    // 2. 获取WebSocket服务
    let websocket_service = container.get_websocket_service();

    // 3. 测试获取连接统计（这会调用WebSocket服务）
    let stats = websocket_service.get_connection_stats().await?;
    assert_eq!(stats.total_connections, 0, "初始连接数应该为0");

    println!("✅ WebSocket应用服务依赖注入测试通过");
    Ok(())
}

/// 创建测试配置
fn create_test_config() -> AppConfig {
    AppConfig {
        database_url: "sqlite::memory:".to_string(),
        http_addr: "127.0.0.1:0".parse().unwrap(),
        jwt_secret: "test_secret_key_for_testing_only".to_string(),
    }
}

/// 创建测试数据库连接
async fn create_test_database_connection() -> Result<Arc<sea_orm::DatabaseConnection>> {
    use sea_orm::Database;

    // 使用内存SQLite数据库进行测试
    let database = Database::connect("sqlite::memory:").await?;
    Ok(Arc::new(database))
}

/// 创建测试服务容器
async fn create_test_container() -> Result<impl ServiceContainerTrait> {
    let config = create_test_config();
    let database = create_test_database_connection().await?;

    DefaultServiceContainerBuilder::new()
        .with_config(config)
        .with_database(database)
        .with_all_services()
        .build()
}

/// 集成测试：完整的依赖注入流程
#[tokio::test]
async fn test_complete_dependency_injection_flow() -> Result<()> {
    println!("🧪 测试完整的依赖注入流程");

    // 1. 创建服务容器
    let container = create_test_container().await?;

    // 2. 验证所有服务都可以正常获取
    let user_service = container.get_user_service();
    let task_service = container.get_task_service();
    let chat_service = container.get_chat_service();
    let websocket_service = container.get_websocket_service();

    // 3. 验证服务之间的协作（模拟真实业务场景）
    let test_user_id = uuid::Uuid::new_v4();

    // 检查用户名可用性
    let is_available = user_service
        .is_username_available("integration_test_user")
        .await?;
    assert!(is_available);

    // 获取用户任务
    let tasks = task_service.get_user_tasks(test_user_id).await?;
    assert!(tasks.is_empty());

    // 获取WebSocket统计
    let stats = websocket_service.get_connection_stats().await?;
    assert_eq!(stats.total_connections, 0);

    println!("✅ 完整的依赖注入流程测试通过");
    Ok(())
}
