use fred::prelude::*;
use std::time::Duration;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🐉 测试DragonflyDB连接...");

    // 加载.env文件
    match dotenvy::dotenv() {
        Ok(path) => println!("✅ 成功加载.env文件: {:?}", path),
        Err(e) => println!("⚠️ 无法加载.env文件: {}", e),
    }

    // 检查环境变量
    println!("🔍 检查环境变量:");
    if let Ok(cache_url_env) = std::env::var("CACHE_URL") {
        println!("  CACHE_URL = {}", cache_url_env);
    } else {
        println!("  CACHE_URL = 未设置");
    }

    if let Ok(redis_url_env) = std::env::var("REDIS_URL") {
        println!("  REDIS_URL = {}", redis_url_env);
    } else {
        println!("  REDIS_URL = 未设置");
    }

    // 从环境变量读取连接URL
    let cache_url = std::env
        ::var("CACHE_URL")
        .or_else(|_| std::env::var("REDIS_URL"))
        .unwrap_or_else(|_| "redis://:dragonfly_secure_password_2025@localhost:6379".to_string());

    println!("📡 最终使用的连接URL: {}", cache_url);

    // 创建Redis配置
    let config = RedisConfig::from_url(&cache_url)?;
    println!("⚙️ Redis配置创建成功");

    // 创建客户端
    let client = RedisClient::new(config, None, None, None);
    println!("🔧 Redis客户端创建成功");

    // 尝试连接
    println!("🔗 正在连接到DragonflyDB...");
    let connect_result = tokio::time::timeout(Duration::from_secs(10), client.connect()).await;

    match connect_result {
        Ok(Ok(_)) => {
            println!("✅ 成功连接到DragonflyDB!");

            // 测试PING命令
            println!("📡 测试PING命令...");
            let ping_result: String = client.ping(None).await?;
            println!("🏓 PING响应: {}", ping_result);

            // 测试SET/GET命令
            println!("📝 测试SET/GET命令...");
            let _: () = client.set("test_key", "test_value", None, None, false).await?;
            let value: String = client.get("test_key").await?;
            println!("📖 GET结果: {}", value);

            // 清理测试数据
            let _: i64 = client.del("test_key").await?;
            println!("🧹 清理测试数据完成");

            println!("🎉 DragonflyDB连接测试完全成功!");
        }
        Ok(Err(e)) => {
            println!("❌ 连接DragonflyDB失败: {}", e);
            return Err(e.into());
        }
        Err(_) => {
            println!("❌ 连接DragonflyDB超时 (10秒)");
            return Err("连接超时".into());
        }
    }

    Ok(())
}
