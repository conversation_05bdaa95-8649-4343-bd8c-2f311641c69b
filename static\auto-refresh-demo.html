<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务21：自动刷新机制演示</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="css/common.css">
    <link rel="stylesheet" href="css/auto-refresh.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .demo-panel {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .demo-panel h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-active {
            background: #16a34a;
            animation: pulse 2s infinite;
        }
        
        .status-inactive {
            background: #6b7280;
        }
        
        .status-error {
            background: #dc2626;
        }
        
        .log-container {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-timestamp {
            color: #9ca3af;
        }
        
        .log-success {
            color: #10b981;
        }
        
        .log-error {
            color: #ef4444;
        }
        
        .log-info {
            color: #3b82f6;
        }
        
        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 任务21：自动刷新机制演示</h1>
            <p>展示统一的自动刷新管理器功能，包括页面可见性检测、错误处理和性能优化</p>
        </div>

        <!-- 自动刷新控制面板 -->
        <div id="autoRefreshControls"></div>

        <!-- 演示面板 -->
        <div class="demo-grid">
            <!-- 健康检查演示 -->
            <div class="demo-panel">
                <h3>
                    <span id="healthStatus" class="status-indicator status-inactive"></span>
                    健康检查监控
                </h3>
                <div class="status-grid">
                    <div class="status-item">
                        <span class="label">系统状态:</span>
                        <span id="systemStatus" class="value">检查中...</span>
                    </div>
                    <div class="status-item">
                        <span class="label">响应时间:</span>
                        <span id="responseTime" class="value">0ms</span>
                    </div>
                    <div class="status-item">
                        <span class="label">成功率:</span>
                        <span id="successRate" class="value">0%</span>
                    </div>
                    <div class="status-item">
                        <span class="label">最后检查:</span>
                        <span id="lastHealthCheck" class="value">从未</span>
                    </div>
                </div>
                <div id="healthLog" class="log-container"></div>
            </div>

            <!-- WebSocket监控演示 -->
            <div class="demo-panel">
                <h3>
                    <span id="websocketStatus" class="status-indicator status-inactive"></span>
                    WebSocket监控
                </h3>
                <div class="status-grid">
                    <div class="status-item">
                        <span class="label">连接状态:</span>
                        <span id="connectionStatus" class="value">断开</span>
                    </div>
                    <div class="status-item">
                        <span class="label">活跃连接:</span>
                        <span id="activeConnections" class="value">0</span>
                    </div>
                    <div class="status-item">
                        <span class="label">消息数量:</span>
                        <span id="messageCount" class="value">0</span>
                    </div>
                    <div class="status-item">
                        <span class="label">最后更新:</span>
                        <span id="lastWebsocketUpdate" class="value">从未</span>
                    </div>
                </div>
                <div id="websocketLog" class="log-container"></div>
            </div>

            <!-- 缓存监控演示 -->
            <div class="demo-panel">
                <h3>
                    <span id="cacheStatus" class="status-indicator status-inactive"></span>
                    缓存监控
                </h3>
                <div class="status-grid">
                    <div class="status-item">
                        <span class="label">缓存命中率:</span>
                        <span id="cacheHitRate" class="value">0%</span>
                    </div>
                    <div class="status-item">
                        <span class="label">内存使用:</span>
                        <span id="memoryUsage" class="value">0MB</span>
                    </div>
                    <div class="status-item">
                        <span class="label">键数量:</span>
                        <span id="keyCount" class="value">0</span>
                    </div>
                    <div class="status-item">
                        <span class="label">最后更新:</span>
                        <span id="lastCacheUpdate" class="value">从未</span>
                    </div>
                </div>
                <div id="cacheLog" class="log-container"></div>
            </div>

            <!-- 数据库性能演示 -->
            <div class="demo-panel">
                <h3>
                    <span id="dbStatus" class="status-indicator status-inactive"></span>
                    数据库性能
                </h3>
                <div class="status-grid">
                    <div class="status-item">
                        <span class="label">查询时间:</span>
                        <span id="queryTime" class="value">0ms</span>
                    </div>
                    <div class="status-item">
                        <span class="label">活跃连接:</span>
                        <span id="dbConnections" class="value">0</span>
                    </div>
                    <div class="status-item">
                        <span class="label">慢查询:</span>
                        <span id="slowQueries" class="value">0</span>
                    </div>
                    <div class="status-item">
                        <span class="label">最后更新:</span>
                        <span id="lastDbUpdate" class="value">从未</span>
                    </div>
                </div>
                <div id="dbLog" class="log-container"></div>
            </div>
        </div>

        <!-- 页面可见性测试 -->
        <div class="demo-panel">
            <h3>📱 页面可见性检测测试</h3>
            <p>切换到其他标签页或最小化窗口来测试页面可见性检测功能。当页面不可见时，自动刷新会暂停；重新可见时会恢复。</p>
            <div class="status-grid">
                <div class="status-item">
                    <span class="label">页面状态:</span>
                    <span id="pageVisibility" class="value">可见</span>
                </div>
                <div class="status-item">
                    <span class="label">刷新状态:</span>
                    <span id="refreshStatus" class="value">运行中</span>
                </div>
                <div class="status-item">
                    <span class="label">暂停次数:</span>
                    <span id="pauseCount" class="value">0</span>
                </div>
                <div class="status-item">
                    <span class="label">恢复次数:</span>
                    <span id="resumeCount" class="value">0</span>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript模块 -->
    <script type="module">
        import { AutoRefreshManager } from './js/modules/auto-refresh-manager.js';
        import { globalRefreshConfigManager } from './js/modules/refresh-config-manager.js';
        import './js/modules/auto-refresh-ui.js';

        // 演示数据生成器
        class DemoDataGenerator {
            constructor() {
                this.pauseCount = 0;
                this.resumeCount = 0;
                this.setupVisibilityTracking();
                this.setupDemoRefreshers();
            }

            setupVisibilityTracking() {
                document.addEventListener('visibilitychange', () => {
                    const visibility = document.hidden ? '隐藏' : '可见';
                    document.getElementById('pageVisibility').textContent = visibility;
                    
                    if (document.hidden) {
                        this.pauseCount++;
                        document.getElementById('pauseCount').textContent = this.pauseCount;
                        document.getElementById('refreshStatus').textContent = '已暂停';
                    } else {
                        this.resumeCount++;
                        document.getElementById('resumeCount').textContent = this.resumeCount;
                        document.getElementById('refreshStatus').textContent = '运行中';
                    }
                });
            }

            setupDemoRefreshers() {
                // 健康检查演示
                this.setupHealthDemo();
                
                // WebSocket监控演示
                this.setupWebSocketDemo();
                
                // 缓存监控演示
                this.setupCacheDemo();
                
                // 数据库性能演示
                this.setupDatabaseDemo();
            }

            setupHealthDemo() {
                const healthManager = new AutoRefreshManager({
                    defaultInterval: 30000,
                    enableVisibilityDetection: true
                });

                healthManager.onRefresh(async () => {
                    const status = document.getElementById('healthStatus');
                    status.className = 'status-indicator status-active';
                    
                    // 模拟API调用
                    const responseTime = Math.random() * 200 + 50;
                    const isHealthy = Math.random() > 0.1; // 90% 成功率
                    
                    document.getElementById('systemStatus').textContent = isHealthy ? '健康' : '异常';
                    document.getElementById('responseTime').textContent = `${responseTime.toFixed(0)}ms`;
                    document.getElementById('successRate').textContent = '90%';
                    document.getElementById('lastHealthCheck').textContent = new Date().toLocaleTimeString();
                    
                    this.addLog('healthLog', isHealthy ? 'success' : 'error', 
                        `健康检查${isHealthy ? '成功' : '失败'} - 响应时间: ${responseTime.toFixed(0)}ms`);
                    
                    setTimeout(() => {
                        status.className = 'status-indicator status-inactive';
                    }, 1000);
                });

                healthManager.start();
            }

            setupWebSocketDemo() {
                const wsManager = new AutoRefreshManager({
                    defaultInterval: 10000,
                    enableVisibilityDetection: true
                });

                wsManager.onRefresh(async () => {
                    const status = document.getElementById('websocketStatus');
                    status.className = 'status-indicator status-active';
                    
                    const connections = Math.floor(Math.random() * 100) + 50;
                    const messages = Math.floor(Math.random() * 1000) + 500;
                    
                    document.getElementById('connectionStatus').textContent = '已连接';
                    document.getElementById('activeConnections').textContent = connections;
                    document.getElementById('messageCount').textContent = messages;
                    document.getElementById('lastWebsocketUpdate').textContent = new Date().toLocaleTimeString();
                    
                    this.addLog('websocketLog', 'info', 
                        `WebSocket状态更新 - 连接数: ${connections}, 消息数: ${messages}`);
                    
                    setTimeout(() => {
                        status.className = 'status-indicator status-inactive';
                    }, 1000);
                });

                wsManager.start();
            }

            setupCacheDemo() {
                const cacheManager = new AutoRefreshManager({
                    defaultInterval: 15000,
                    enableVisibilityDetection: true
                });

                cacheManager.onRefresh(async () => {
                    const status = document.getElementById('cacheStatus');
                    status.className = 'status-indicator status-active';
                    
                    const hitRate = (Math.random() * 30 + 70).toFixed(1); // 70-100%
                    const memory = (Math.random() * 500 + 100).toFixed(0); // 100-600MB
                    const keys = Math.floor(Math.random() * 10000) + 5000;
                    
                    document.getElementById('cacheHitRate').textContent = `${hitRate}%`;
                    document.getElementById('memoryUsage').textContent = `${memory}MB`;
                    document.getElementById('keyCount').textContent = keys;
                    document.getElementById('lastCacheUpdate').textContent = new Date().toLocaleTimeString();
                    
                    this.addLog('cacheLog', 'success', 
                        `缓存统计更新 - 命中率: ${hitRate}%, 内存: ${memory}MB, 键: ${keys}`);
                    
                    setTimeout(() => {
                        status.className = 'status-indicator status-inactive';
                    }, 1000);
                });

                cacheManager.start();
            }

            setupDatabaseDemo() {
                const dbManager = new AutoRefreshManager({
                    defaultInterval: 20000,
                    enableVisibilityDetection: true
                });

                dbManager.onRefresh(async () => {
                    const status = document.getElementById('dbStatus');
                    status.className = 'status-indicator status-active';
                    
                    const queryTime = (Math.random() * 100 + 10).toFixed(0);
                    const connections = Math.floor(Math.random() * 20) + 5;
                    const slowQueries = Math.floor(Math.random() * 5);
                    
                    document.getElementById('queryTime').textContent = `${queryTime}ms`;
                    document.getElementById('dbConnections').textContent = connections;
                    document.getElementById('slowQueries').textContent = slowQueries;
                    document.getElementById('lastDbUpdate').textContent = new Date().toLocaleTimeString();
                    
                    this.addLog('dbLog', slowQueries > 2 ? 'error' : 'success', 
                        `数据库性能更新 - 查询时间: ${queryTime}ms, 连接数: ${connections}, 慢查询: ${slowQueries}`);
                    
                    setTimeout(() => {
                        status.className = 'status-indicator status-inactive';
                    }, 1000);
                });

                dbManager.start();
            }

            addLog(containerId, type, message) {
                const container = document.getElementById(containerId);
                const timestamp = new Date().toLocaleTimeString();
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.innerHTML = `
                    <span class="log-timestamp">[${timestamp}]</span>
                    <span class="log-${type}">${message}</span>
                `;
                
                container.appendChild(logEntry);
                
                // 保持最多20条日志
                while (container.children.length > 20) {
                    container.removeChild(container.firstChild);
                }
                
                // 滚动到底部
                container.scrollTop = container.scrollHeight;
            }
        }

        // 初始化演示
        document.addEventListener('DOMContentLoaded', () => {
            new DemoDataGenerator();
        });
    </script>
</body>
</html>
