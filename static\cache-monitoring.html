<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存监控面板 - Axum企业级应用</title>
    
    <!-- 引入Chart.js用于图表展示 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns@3.0.0/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-align: center;
        }

        .header p {
            color: #7f8c8d;
            text-align: center;
            font-size: 1.1rem;
        }

        /* 控制面板样式 */
        .controls {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        .control-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .btn.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .btn.danger:hover {
            box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-healthy { background-color: #27ae60; }
        .status-warning { background-color: #f39c12; }
        .status-error { background-color: #e74c3c; }

        /* 网格布局 */
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .card-subtitle {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-top: 5px;
        }

        /* 统计卡片样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .stat-trend {
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .trend-up { color: #27ae60; }
        .trend-down { color: #e74c3c; }
        .trend-stable { color: #f39c12; }

        /* 图表容器样式 */
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 15px;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .data-table th,
        .data-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .data-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                justify-content: center;
            }
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 错误和成功消息样式 */
        .message {
            padding: 10px 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }

        .message.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .message.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header">
            <h1>🚀 缓存监控面板</h1>
            <p>实时监控多层缓存系统性能与健康状态</p>
        </div>

        <!-- 控制面板 -->
        <div class="controls">
            <div class="control-group">
                <span class="status-indicator status-healthy" id="connectionStatus"></span>
                <span id="connectionText">连接状态：正常</span>
            </div>
            
            <div class="control-group">
                <button class="btn" id="refreshBtn" onclick="refreshAllData()">
                    <span id="refreshIcon">🔄</span> 刷新数据
                </button>
                <button class="btn danger" id="resetStatsBtn" onclick="resetCacheStats()">
                    🗑️ 重置统计
                </button>
                <select id="refreshInterval" onchange="updateRefreshInterval()">
                    <option value="5000">5秒刷新</option>
                    <option value="10000" selected>10秒刷新</option>
                    <option value="30000">30秒刷新</option>
                    <option value="60000">1分钟刷新</option>
                </select>
            </div>
        </div>

        <!-- 消息显示区域 -->
        <div id="messageContainer"></div>

        <!-- 统计概览卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-value" id="overallHitRate">--</div>
                <div class="stat-label">总体命中率</div>
                <div class="stat-trend trend-stable" id="hitRateTrend">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="totalOperations">--</div>
                <div class="stat-label">总操作数</div>
                <div class="stat-trend trend-up" id="operationsTrend">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="activeConnections">--</div>
                <div class="stat-label">活跃连接</div>
                <div class="stat-trend trend-stable" id="connectionsTrend">--</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="avgResponseTime">--</div>
                <div class="stat-label">平均响应时间(ms)</div>
                <div class="stat-trend trend-down" id="responseTrend">--</div>
            </div>
        </div>

        <!-- 主要监控面板 -->
        <div class="dashboard-grid">
            <!-- 缓存命中率图表 -->
            <div class="card">
                <div class="card-header">
                    <div>
                        <div class="card-title">📊 缓存命中率趋势</div>
                        <div class="card-subtitle">实时监控各层级缓存命中率变化</div>
                    </div>
                    <button class="btn" onclick="exportChartData('hitRateChart')">导出数据</button>
                </div>
                <div class="chart-container">
                    <canvas id="hitRateChart"></canvas>
                </div>
            </div>

            <!-- 操作统计图表 -->
            <div class="card">
                <div class="card-header">
                    <div>
                        <div class="card-title">⚡ 操作统计</div>
                        <div class="card-subtitle">缓存读写操作实时统计</div>
                    </div>
                    <button class="btn" onclick="exportChartData('operationsChart')">导出数据</button>
                </div>
                <div class="chart-container">
                    <canvas id="operationsChart"></canvas>
                </div>
            </div>

            <!-- 连接池状态 -->
            <div class="card">
                <div class="card-header">
                    <div>
                        <div class="card-title">🔗 连接池状态</div>
                        <div class="card-subtitle">缓存连接池实时监控</div>
                    </div>
                    <button class="btn" onclick="refreshPoolStatus()">刷新状态</button>
                </div>
                <div id="poolStatusContent">
                    <div class="loading"></div>
                </div>
            </div>

            <!-- 层级详细统计 -->
            <div class="card">
                <div class="card-header">
                    <div>
                        <div class="card-title">🏗️ 层级统计详情</div>
                        <div class="card-subtitle">热/温/冷三层缓存详细数据</div>
                    </div>
                    <button class="btn" onclick="refreshTierStats()">刷新统计</button>
                </div>
                <table class="data-table" id="tierStatsTable">
                    <thead>
                        <tr>
                            <th>缓存层级</th>
                            <th>读取次数</th>
                            <th>命中次数</th>
                            <th>写入次数</th>
                            <th>命中率</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody id="tierStatsBody">
                        <tr>
                            <td colspan="6" style="text-align: center;">
                                <div class="loading"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 健康状态详情 -->
        <div class="card">
            <div class="card-header">
                <div>
                    <div class="card-title">💚 缓存健康状态</div>
                    <div class="card-subtitle">缓存服务健康检查和性能指标</div>
                </div>
                <button class="btn" onclick="refreshHealthStatus()">健康检查</button>
            </div>
            <div id="healthStatusContent">
                <div class="loading"></div>
            </div>
        </div>
    </div>

    <!-- 引入API模块 -->
    <script type="module" src="/js/modules/api.js"></script>
    <script type="module" src="/js/modules/cache-api.js"></script>

    <!-- 主要JavaScript逻辑 -->
    <script type="module">
        // 导入API模块
        import { cacheAPI } from '/js/modules/cache-api.js';

        // 全局变量
        let hitRateChart = null;
        let operationsChart = null;
        let refreshIntervalId = null;
        let currentRefreshInterval = 10000; // 默认10秒刷新

        // 历史数据存储
        const chartData = {
            hitRate: {
                labels: [],
                datasets: [{
                    label: '总体命中率',
                    data: [],
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: '热数据层',
                    data: [],
                    borderColor: '#e74c3c',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: '温数据层',
                    data: [],
                    borderColor: '#f39c12',
                    backgroundColor: 'rgba(243, 156, 18, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: '冷数据层',
                    data: [],
                    borderColor: '#9b59b6',
                    backgroundColor: 'rgba(155, 89, 182, 0.1)',
                    tension: 0.4,
                    fill: false
                }]
            },
            operations: {
                labels: [],
                datasets: [{
                    label: '总操作数',
                    data: [],
                    borderColor: '#27ae60',
                    backgroundColor: 'rgba(39, 174, 96, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: '命中数',
                    data: [],
                    borderColor: '#2ecc71',
                    backgroundColor: 'rgba(46, 204, 113, 0.1)',
                    tension: 0.4,
                    fill: false
                }, {
                    label: '未命中数',
                    data: [],
                    borderColor: '#e67e22',
                    backgroundColor: 'rgba(230, 126, 34, 0.1)',
                    tension: 0.4,
                    fill: false
                }]
            }
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 缓存监控面板初始化开始');

            try {
                // 初始化图表
                initializeCharts();

                // 加载初始数据
                await refreshAllData();

                // 启动自动刷新
                startAutoRefresh();

                console.log('✅ 缓存监控面板初始化完成');
                showMessage('缓存监控面板已成功加载', 'success');
            } catch (error) {
                console.error('❌ 初始化失败:', error);
                showMessage(`初始化失败: ${error.message}`, 'error');
            }
        });

        // 初始化图表
        function initializeCharts() {
            // 命中率趋势图表
            const hitRateCtx = document.getElementById('hitRateChart').getContext('2d');
            hitRateChart = new Chart(hitRateCtx, {
                type: 'line',
                data: chartData.hitRate,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '缓存命中率趋势 (%)'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        },
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            ticks: {
                                callback: function(value, index) {
                                    const labels = chartData.hitRate.labels;
                                    if (labels[index]) {
                                        return labels[index].toLocaleTimeString();
                                    }
                                    return '';
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            // 操作统计图表
            const operationsCtx = document.getElementById('operationsChart').getContext('2d');
            operationsChart = new Chart(operationsCtx, {
                type: 'line',
                data: chartData.operations,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '缓存操作统计'
                        },
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        },
                        x: {
                            type: 'linear',
                            position: 'bottom',
                            ticks: {
                                callback: function(value, index) {
                                    const labels = chartData.operations.labels;
                                    if (labels[index]) {
                                        return labels[index].toLocaleTimeString();
                                    }
                                    return '';
                                }
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });
        }

        // 刷新所有数据
        window.refreshAllData = async function() {
            const refreshBtn = document.getElementById('refreshBtn');
            const refreshIcon = document.getElementById('refreshIcon');

            try {
                // 显示加载状态
                refreshIcon.textContent = '⏳';
                refreshBtn.disabled = true;

                // 并行获取所有数据
                const [statsData, healthData, poolData] = await Promise.all([
                    fetchCacheStats(),
                    fetchCacheHealth(),
                    fetchCachePoolStatus()
                ]);

                // 更新UI
                updateStatsOverview(statsData);
                updateCharts(statsData);
                updateTierStatsTable(statsData);
                updateHealthStatus(healthData);
                updatePoolStatus(poolData);

                // 更新连接状态
                updateConnectionStatus(true);

                console.log('✅ 所有数据刷新完成');
                showMessage('数据刷新成功', 'success');

            } catch (error) {
                console.error('❌ 数据刷新失败:', error);
                updateConnectionStatus(false);
                showMessage(`数据刷新失败: ${error.message}`, 'error');
            } finally {
                // 恢复按钮状态
                refreshIcon.textContent = '🔄';
                refreshBtn.disabled = false;
            }
        };

        // 获取缓存统计数据
        async function fetchCacheStats() {
            return await cacheAPI.getStats({ useCache: false });
        }

        // 获取缓存健康状态
        async function fetchCacheHealth() {
            return await cacheAPI.getHealth({ useCache: false });
        }

        // 获取连接池状态
        async function fetchCachePoolStatus() {
            return await cacheAPI.getPoolStatus({ useCache: false });
        }

        // 更新统计概览
        function updateStatsOverview(statsData) {
            document.getElementById('overallHitRate').textContent =
                (statsData.hit_rate * 100).toFixed(1) + '%';
            document.getElementById('totalOperations').textContent =
                formatNumber(statsData.total_operations);

            // 更新趋势指示器（这里简化处理，实际应该基于历史数据）
            const hitRateTrend = document.getElementById('hitRateTrend');
            if (statsData.hit_rate > 0.8) {
                hitRateTrend.textContent = '↗️ 优秀';
                hitRateTrend.className = 'stat-trend trend-up';
            } else if (statsData.hit_rate > 0.6) {
                hitRateTrend.textContent = '→ 良好';
                hitRateTrend.className = 'stat-trend trend-stable';
            } else {
                hitRateTrend.textContent = '↘️ 需优化';
                hitRateTrend.className = 'stat-trend trend-down';
            }
        }

        // 更新图表数据
        function updateCharts(statsData) {
            const now = new Date();
            const dataIndex = chartData.hitRate.labels.length;

            // 更新命中率图表
            chartData.hitRate.labels.push(now);
            chartData.hitRate.datasets[0].data.push({
                x: dataIndex,
                y: statsData.hit_rate * 100
            });
            chartData.hitRate.datasets[1].data.push({
                x: dataIndex,
                y: statsData.hot_tier.hit_rate * 100
            });
            chartData.hitRate.datasets[2].data.push({
                x: dataIndex,
                y: statsData.warm_tier.hit_rate * 100
            });
            chartData.hitRate.datasets[3].data.push({
                x: dataIndex,
                y: statsData.cold_tier.hit_rate * 100
            });

            // 更新操作统计图表
            chartData.operations.labels.push(now);
            chartData.operations.datasets[0].data.push({
                x: dataIndex,
                y: statsData.total_operations
            });
            chartData.operations.datasets[1].data.push({
                x: dataIndex,
                y: statsData.total_hits
            });
            chartData.operations.datasets[2].data.push({
                x: dataIndex,
                y: statsData.total_misses
            });

            // 限制数据点数量（保留最近50个点）
            const maxDataPoints = 50;
            if (chartData.hitRate.labels.length > maxDataPoints) {
                chartData.hitRate.labels.shift();
                chartData.hitRate.datasets.forEach(dataset => dataset.data.shift());
                chartData.operations.labels.shift();
                chartData.operations.datasets.forEach(dataset => dataset.data.shift());
            }

            // 更新图表
            hitRateChart.update('none');
            operationsChart.update('none');
        }

        // 更新层级统计表格
        function updateTierStatsTable(statsData) {
            const tbody = document.getElementById('tierStatsBody');
            const tiers = [
                { name: '🔥 热数据层', data: statsData.hot_tier, color: '#e74c3c' },
                { name: '🌡️ 温数据层', data: statsData.warm_tier, color: '#f39c12' },
                { name: '❄️ 冷数据层', data: statsData.cold_tier, color: '#9b59b6' }
            ];

            tbody.innerHTML = tiers.map(tier => `
                <tr>
                    <td style="color: ${tier.color}; font-weight: bold;">${tier.name}</td>
                    <td>${formatNumber(tier.data.reads)}</td>
                    <td>${formatNumber(tier.data.hits)}</td>
                    <td>${formatNumber(tier.data.writes)}</td>
                    <td>
                        <span style="color: ${getHitRateColor(tier.data.hit_rate)}; font-weight: bold;">
                            ${(tier.data.hit_rate * 100).toFixed(1)}%
                        </span>
                    </td>
                    <td>
                        <span class="status-indicator ${getStatusClass(tier.data.hit_rate)}"></span>
                        ${getStatusText(tier.data.hit_rate)}
                    </td>
                </tr>
            `).join('');
        }

        // 更新健康状态
        function updateHealthStatus(healthData) {
            const container = document.getElementById('healthStatusContent');

            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                    <div class="stat-card">
                        <div class="stat-value" style="color: ${healthData.status === 'healthy' ? '#27ae60' : '#e74c3c'}">
                            ${healthData.status === 'healthy' ? '✅' : '❌'}
                        </div>
                        <div class="stat-label">服务状态</div>
                        <div class="stat-trend">${healthData.status}</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${healthData.connection.connected ? '🟢' : '🔴'}</div>
                        <div class="stat-label">连接状态</div>
                        <div class="stat-trend">${healthData.connection.connected ? '已连接' : '未连接'}</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">${healthData.performance.avg_response_time_ms.toFixed(1)}</div>
                        <div class="stat-label">平均响应时间(ms)</div>
                        <div class="stat-trend trend-${healthData.performance.avg_response_time_ms < 5 ? 'up' : 'down'}">
                            ${healthData.performance.avg_response_time_ms < 5 ? '优秀' : '需优化'}
                        </div>
                    </div>
                </div>
                <div style="margin-top: 15px; padding: 15px; background-color: #f8f9fa; border-radius: 8px;">
                    <h4 style="margin-bottom: 10px; color: #2c3e50;">详细信息</h4>
                    <p><strong>连接池大小:</strong> ${healthData.connection.pool_size}</p>
                    <p><strong>活跃连接:</strong> ${healthData.connection.active_connections}</p>
                    <p><strong>空闲连接:</strong> ${healthData.connection.idle_connections}</p>
                    <p><strong>最大响应时间:</strong> ${healthData.performance.max_response_time_ms.toFixed(1)}ms</p>
                    <p><strong>最小响应时间:</strong> ${healthData.performance.min_response_time_ms.toFixed(1)}ms</p>
                    <p><strong>检查时间:</strong> ${new Date(healthData.timestamp).toLocaleString()}</p>
                </div>
            `;
        }

        // 更新连接池状态
        function updatePoolStatus(poolData) {
            const container = document.getElementById('poolStatusContent');

            // 更新活跃连接数统计卡片
            document.getElementById('activeConnections').textContent = poolData.active_connections;

            container.innerHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 15px;">
                    <div class="stat-card">
                        <div class="stat-value">${poolData.total_connections}</div>
                        <div class="stat-label">总连接数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" style="color: #27ae60">${poolData.active_connections}</div>
                        <div class="stat-label">活跃连接</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" style="color: #3498db">${poolData.idle_connections}</div>
                        <div class="stat-label">空闲连接</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value" style="color: ${poolData.success_rate > 0.95 ? '#27ae60' : '#e74c3c'}">
                            ${(poolData.success_rate * 100).toFixed(1)}%
                        </div>
                        <div class="stat-label">成功率</div>
                    </div>
                </div>

                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h4 style="margin-bottom: 10px; color: #2c3e50;">连接池详细信息</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                        <div>
                            <p><strong>最大连接数:</strong> ${poolData.max_connections}</p>
                            <p><strong>最小连接数:</strong> ${poolData.min_connections}</p>
                            <p><strong>获取成功次数:</strong> ${formatNumber(poolData.acquire_success_count)}</p>
                            <p><strong>获取失败次数:</strong> ${formatNumber(poolData.acquire_failure_count)}</p>
                        </div>
                        <div>
                            <p><strong>平均获取时间:</strong> ${poolData.avg_acquire_time_ms.toFixed(2)}ms</p>
                            <p><strong>连接错误次数:</strong> ${formatNumber(poolData.connection_errors)}</p>
                            <p><strong>池利用率:</strong> ${(poolData.pool_utilization * 100).toFixed(1)}%</p>
                            <p><strong>重连尝试:</strong> ${formatNumber(poolData.reconnect_attempts)}</p>
                        </div>
                    </div>
                    <div style="margin-top: 10px; padding: 10px; background-color: ${poolData.is_healthy ? '#d4edda' : '#f8d7da'}; border-radius: 5px;">
                        <strong>健康状态:</strong>
                        <span style="color: ${poolData.is_healthy ? '#155724' : '#721c24'}">
                            ${poolData.is_healthy ? '✅ 健康' : '❌ 异常'}
                        </span>
                        <br>
                        <small>最后检查: ${new Date(poolData.last_health_check).toLocaleString()}</small>
                    </div>
                </div>
            `;
        }

        // 重置缓存统计
        window.resetCacheStats = async function() {
            if (!confirm('确定要重置所有缓存统计信息吗？此操作不可撤销。')) {
                return;
            }

            const resetBtn = document.getElementById('resetStatsBtn');

            try {
                resetBtn.disabled = true;
                resetBtn.textContent = '⏳ 重置中...';

                console.log('🗑️ 重置缓存统计...');
                const response = await cacheAPI.resetStats();

                // 清空图表数据
                chartData.hitRate.labels = [];
                chartData.hitRate.datasets.forEach(dataset => dataset.data = []);
                chartData.operations.labels = [];
                chartData.operations.datasets.forEach(dataset => dataset.data = []);

                hitRateChart.update();
                operationsChart.update();

                // 刷新数据
                await refreshAllData();

                console.log('✅ 缓存统计重置完成');
                showMessage('缓存统计已成功重置', 'success');

            } catch (error) {
                console.error('❌ 重置缓存统计失败:', error);
                showMessage(`重置失败: ${error.message}`, 'error');
            } finally {
                resetBtn.disabled = false;
                resetBtn.textContent = '🗑️ 重置统计';
            }
        };

        // 更新刷新间隔
        window.updateRefreshInterval = function() {
            const select = document.getElementById('refreshInterval');
            currentRefreshInterval = parseInt(select.value);

            // 重启自动刷新
            stopAutoRefresh();
            startAutoRefresh();

            console.log(`🔄 刷新间隔已更新为: ${currentRefreshInterval}ms`);
            showMessage(`刷新间隔已设置为 ${currentRefreshInterval/1000} 秒`, 'info');
        };

        // 启动自动刷新
        function startAutoRefresh() {
            if (refreshIntervalId) {
                clearInterval(refreshIntervalId);
            }

            refreshIntervalId = setInterval(async () => {
                try {
                    await refreshAllData();
                } catch (error) {
                    console.error('自动刷新失败:', error);
                }
            }, currentRefreshInterval);

            console.log(`🔄 自动刷新已启动，间隔: ${currentRefreshInterval}ms`);
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshIntervalId) {
                clearInterval(refreshIntervalId);
                refreshIntervalId = null;
                console.log('🛑 自动刷新已停止');
            }
        }

        // 工具函数
        function updateConnectionStatus(isConnected) {
            const indicator = document.getElementById('connectionStatus');
            const text = document.getElementById('connectionText');

            if (isConnected) {
                indicator.className = 'status-indicator status-healthy';
                text.textContent = '连接状态：正常';
            } else {
                indicator.className = 'status-indicator status-error';
                text.textContent = '连接状态：异常';
            }
        }

        function showMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.textContent = message;

            container.appendChild(messageDiv);

            // 3秒后自动移除消息
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }

        function formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }

        function getHitRateColor(rate) {
            if (rate > 0.8) return '#27ae60';
            if (rate > 0.6) return '#f39c12';
            return '#e74c3c';
        }

        function getStatusClass(rate) {
            if (rate > 0.8) return 'status-healthy';
            if (rate > 0.6) return 'status-warning';
            return 'status-error';
        }

        function getStatusText(rate) {
            if (rate > 0.8) return '优秀';
            if (rate > 0.6) return '良好';
            return '需优化';
        }

        // 导出图表数据
        window.exportChartData = function(chartId) {
            let data, filename;

            if (chartId === 'hitRateChart') {
                data = chartData.hitRate;
                filename = 'cache-hit-rate-data.json';
            } else if (chartId === 'operationsChart') {
                data = chartData.operations;
                filename = 'cache-operations-data.json';
            } else {
                showMessage('未知的图表类型', 'error');
                return;
            }

            const exportData = {
                timestamp: new Date().toISOString(),
                chart: chartId,
                data: data
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                type: 'application/json'
            });

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            showMessage(`图表数据已导出: ${filename}`, 'success');
        };

        // 刷新特定组件的函数
        window.refreshPoolStatus = async function() {
            try {
                const poolData = await fetchCachePoolStatus();
                updatePoolStatus(poolData);
                showMessage('连接池状态已刷新', 'success');
            } catch (error) {
                showMessage(`刷新连接池状态失败: ${error.message}`, 'error');
            }
        };

        window.refreshTierStats = async function() {
            try {
                const statsData = await fetchCacheStats();
                updateTierStatsTable(statsData);
                showMessage('层级统计已刷新', 'success');
            } catch (error) {
                showMessage(`刷新层级统计失败: ${error.message}`, 'error');
            }
        };

        window.refreshHealthStatus = async function() {
            try {
                const healthData = await fetchCacheHealth();
                updateHealthStatus(healthData);
                showMessage('健康状态已刷新', 'success');
            } catch (error) {
                showMessage(`刷新健康状态失败: ${error.message}`, 'error');
            }
        };

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            stopAutoRefresh();
            if (hitRateChart) hitRateChart.destroy();
            if (operationsChart) operationsChart.destroy();
        });

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopAutoRefresh();
                console.log('🛑 页面隐藏，停止自动刷新');
            } else {
                startAutoRefresh();
                console.log('🔄 页面可见，恢复自动刷新');
            }
        });

    </script>
</body>
</html>
