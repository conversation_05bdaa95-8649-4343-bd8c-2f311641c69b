/**
 * 任务21：自动刷新UI样式
 * 为自动刷新管理器提供美观的界面样式
 */

/* 自动刷新面板 */
.auto-refresh-panel {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
    overflow: hidden;
}

.panel-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
}

.global-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
}

.panel-content {
    padding: 20px;
}

/* 设置区域 */
.settings-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-section h4 {
    margin: 0 0 16px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    gap: 12px;
}

.setting-item label {
    min-width: 120px;
    color: #555;
    font-weight: 500;
}

.setting-item select,
.setting-item input[type="number"] {
    padding: 6px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    min-width: 120px;
}

.setting-item input[type="checkbox"] {
    margin-right: 8px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #667eea;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 模块设置 */
.module-setting {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 12px;
}

.module-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.module-name {
    font-weight: 600;
    color: #333;
    flex: 1;
}

.module-priority {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.priority-high {
    background: #fee2e2;
    color: #dc2626;
}

.priority-medium {
    background: #fef3c7;
    color: #d97706;
}

.priority-low {
    background: #dcfce7;
    color: #16a34a;
}

.module-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: 62px;
}

.module-controls label {
    color: #666;
    font-size: 14px;
}

.module-interval {
    padding: 4px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

/* 状态网格 */
.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #667eea;
}

.status-item .label {
    color: #666;
    font-weight: 500;
}

.status-item .value {
    font-weight: 600;
    color: #333;
}

.status-item .value.status-started,
.status-item .value.status-resumed,
.status-item .value.status-success {
    color: #16a34a;
}

.status-item .value.status-stopped,
.status-item .value.status-paused {
    color: #d97706;
}

.status-item .value.status-error {
    color: #dc2626;
}

.status-item .value.status-refreshing {
    color: #2563eb;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #5a67d8;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
    background: #cbd5e0;
    transform: translateY(-1px);
}

/* 自动刷新指示器 */
.auto-refresh-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.auto-refresh-indicator.status-started,
.auto-refresh-indicator.status-resumed {
    background: #dcfce7;
    color: #16a34a;
}

.auto-refresh-indicator.status-stopped {
    background: #f3f4f6;
    color: #6b7280;
}

.auto-refresh-indicator.status-paused {
    background: #fef3c7;
    color: #d97706;
}

.auto-refresh-indicator.status-refreshing {
    background: #dbeafe;
    color: #2563eb;
}

.auto-refresh-indicator.status-success {
    background: #dcfce7;
    color: #16a34a;
}

.auto-refresh-indicator.status-error {
    background: #fee2e2;
    color: #dc2626;
}

/* 动画效果 */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.status-refreshing {
    animation: pulse 1.5s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .panel-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        justify-content: center;
    }
    
    .module-controls {
        margin-left: 0;
        flex-direction: column;
        align-items: flex-start;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .setting-item label {
        min-width: auto;
    }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
    .auto-refresh-panel {
        background: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }
    
    .settings-section {
        border-bottom-color: #374151;
    }
    
    .settings-section h4 {
        color: #f9fafb;
    }
    
    .setting-item label {
        color: #d1d5db;
    }
    
    .setting-item select,
    .setting-item input[type="number"] {
        background: #374151;
        border-color: #4b5563;
        color: #f9fafb;
    }
    
    .module-setting {
        background: #374151;
        border-color: #4b5563;
    }
    
    .module-name {
        color: #f9fafb;
    }
    
    .status-item {
        background: #374151;
    }
    
    .status-item .label {
        color: #d1d5db;
    }
    
    .status-item .value {
        color: #f9fafb;
    }
}
