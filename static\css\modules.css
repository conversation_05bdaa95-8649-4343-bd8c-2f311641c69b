/**
 * ES6模块化UI样式
 * 支持模块化重构后的前端界面
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

/* ==== 导入在线用户样式 ==== */
@import url('online-users.css');

/* ==== 认证区域样式增强 ==== */
.auth-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

#onlineUsersBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

#onlineUsersBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

#onlineUsersBtn:active {
    transform: translateY(0);
}

#onlineUsersCount {
    background: rgba(255, 255, 255, 0.2);
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
}

/* ==== 通知系统样式 ==== */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
}

.notification {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    margin-bottom: 10px;
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    opacity: 1;
    transition: opacity 0.3s ease;
    border-left: 4px solid #007bff;
}

.notification-success {
    border-left-color: #28a745;
    background: #f8fff9;
}

.notification-error {
    border-left-color: #dc3545;
    background: #fff8f8;
}

.notification-warning {
    border-left-color: #ffc107;
    background: #fffef8;
}

.notification-info {
    border-left-color: #17a2b8;
    background: #f8feff;
}

.notification-message {
    flex: 1;
    margin-right: 12px;
    font-size: 14px;
    line-height: 1.4;
}

.notification-close {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.notification-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

/* ==== 任务编辑状态样式 ==== */
.task-item.editing {
    background-color: #f8f9fa;
    border: 2px solid #007bff;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.2);
}

.task-item .edit-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    margin-bottom: 8px;
    transition: border-color 0.2s ease;
}

.task-item .edit-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.task-item textarea.edit-input {
    resize: vertical;
    min-height: 60px;
}

/* ==== 任务优先级样式 ==== */
.task-priority {
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.priority-low {
    background-color: #e3f2fd;
    color: #1976d2;
}

.priority-medium {
    background-color: #fff3e0;
    color: #f57c00;
}

.priority-high {
    background-color: #fce4ec;
    color: #c2185b;
}

.priority-urgent {
    background-color: #ffebee;
    color: #d32f2f;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* ==== 连接状态样式 ==== */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.authenticated {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.unauthenticated {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.connected {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.status-badge.disconnected {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-badge.reconnecting {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* ==== 消息样式增强 ==== */
.message {
    margin-bottom: 12px;
    padding: 12px;
    border-radius: 8px;
    border-left: 4px solid #007bff;
    background: #f8f9fa;
    transition: all 0.2s ease;
}

.message:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.message.text {
    border-left-color: #007bff;
    background: #f8f9fa;
}

.message.system {
    border-left-color: #6c757d;
    background: #f1f3f4;
}

.message.user-joined {
    border-left-color: #28a745;
    background: #f8fff9;
}

.message.user-left {
    border-left-color: #ffc107;
    background: #fffef8;
}

.message.own {
    border-left-color: #17a2b8;
    background: #f0f8ff;
    margin-left: 20px;
}

.message-header {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
    font-weight: 500;
}

.message-content {
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
}

/* ==== 原始消息日志样式 ==== */
.raw-message {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    padding: 8px;
    margin-bottom: 4px;
    border-radius: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
    border-left: 3px solid #6c757d;
}

.raw-message.success {
    background-color: #f8fff9;
    border-left-color: #28a745;
}

.raw-message.error {
    background-color: #fff8f8;
    border-left-color: #dc3545;
}

.raw-message.warning {
    background-color: #fffef8;
    border-left-color: #ffc107;
}

.raw-message.info {
    background-color: #f8feff;
    border-left-color: #17a2b8;
}

.raw-message.system {
    background-color: #f1f3f4;
    border-left-color: #6c757d;
}

/* ==== 用户列表样式 ==== */
.user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid #e9ecef;
    transition: background-color 0.2s ease;
}

.user-item:hover {
    background-color: #f8f9fa;
}

.user-item:last-child {
    border-bottom: none;
}

.user-name {
    font-weight: 500;
    color: #495057;
}

.user-status {
    font-size: 12px;
    color: #28a745;
    font-weight: 500;
}

/* ==== 加载状态样式 ==== */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==== 响应式设计增强 ==== */
@media (max-width: 768px) {
    .notification-container {
        left: 10px;
        right: 10px;
        top: 10px;
        max-width: none;
    }
    
    .notification {
        padding: 12px;
    }
    
    .task-item .edit-input {
        font-size: 16px; /* 防止iOS缩放 */
    }
}

/* ==== 无障碍访问增强 ==== */
@media (prefers-reduced-motion: reduce) {
    .notification,
    .task-item,
    .message,
    .raw-message,
    .user-item {
        transition: none;
    }
    
    .priority-urgent,
    .status-badge.reconnecting {
        animation: none;
    }
}

/* ==== 高对比度模式支持 ==== */
@media (prefers-contrast: high) {
    .notification {
        border: 2px solid;
    }
    
    .task-item.editing {
        border-width: 3px;
    }
    
    .message {
        border-left-width: 6px;
    }
}
