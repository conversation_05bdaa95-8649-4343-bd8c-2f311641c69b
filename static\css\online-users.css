/**
 * 在线用户列表样式
 * 基于现代CSS设计，支持响应式布局
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-25
 */

/* ==== 在线用户容器 ==== */
.online-users-container {
    background: #ffffff;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 280px;
    max-height: 400px;
    display: flex;
    flex-direction: column;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    transition: all 0.3s ease;
}

.online-users-container.hidden {
    display: none;
}

.online-users-container.visible {
    display: flex;
}

/* ==== 头部区域 ==== */
.online-users-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #e1e5e9;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.online-users-header h3 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 4px;
}

#online-users-count {
    color: #3498db;
    font-weight: 700;
}

.refresh-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    font-size: 14px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.refresh-btn:hover {
    background-color: #e9ecef;
}

.refresh-btn:active {
    transform: scale(0.95);
}

/* ==== 用户列表区域 ==== */
.online-users-list {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;
    max-height: 320px;
}

.online-users-list::-webkit-scrollbar {
    width: 6px;
}

.online-users-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.online-users-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.online-users-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ==== 用户项 ==== */
.user-item {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    transition: background-color 0.2s ease;
    cursor: pointer;
    border-bottom: 1px solid #f8f9fa;
}

.user-item:hover {
    background-color: #f8f9fa;
}

.user-item:last-child {
    border-bottom: none;
}

/* ==== 用户头像 ==== */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    flex-shrink: 0;
}

.avatar-text {
    color: white;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
}

/* ==== 用户信息 ==== */
.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    font-size: 14px;
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 11px;
    color: #6c757d;
}

.session-type {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    text-transform: uppercase;
}

/* 不同会话类型的颜色 */
.session-chat .session-type {
    background: #d4edda;
    color: #155724;
}

.session-task_realtime .session-type {
    background: #d1ecf1;
    color: #0c5460;
}

.session-monitoring .session-type {
    background: #fff3cd;
    color: #856404;
}

.session-admin .session-type {
    background: #f8d7da;
    color: #721c24;
}

.connected-time {
    font-size: 10px;
    color: #adb5bd;
}

/* ==== 用户操作按钮 ==== */
.user-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.user-item:hover .user-actions {
    opacity: 1;
}

.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    font-size: 12px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.action-btn:hover {
    background-color: #e9ecef;
}

.chat-btn:hover {
    background-color: #d4edda;
}

/* ==== 状态消息 ==== */
.loading-message,
.empty-message {
    text-align: center;
    padding: 24px 16px;
    color: #6c757d;
    font-size: 13px;
}

.loading-message {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.loading-message::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-message {
    font-style: italic;
}

/* ==== 响应式设计 ==== */
@media (max-width: 768px) {
    .online-users-container {
        width: 100%;
        max-width: 320px;
        margin: 0 auto;
    }
    
    .user-item {
        padding: 12px 16px;
    }
    
    .user-avatar {
        width: 36px;
        height: 36px;
    }
    
    .avatar-text {
        font-size: 16px;
    }
    
    .user-name {
        font-size: 15px;
    }
    
    .user-actions {
        opacity: 1; /* 在移动设备上始终显示操作按钮 */
    }
}

/* ==== 暗色主题支持 ==== */
@media (prefers-color-scheme: dark) {
    .online-users-container {
        background: #2c3e50;
        border-color: #34495e;
        color: #ecf0f1;
    }
    
    .online-users-header {
        background: #34495e;
        border-bottom-color: #4a5f7a;
    }
    
    .online-users-header h3 {
        color: #ecf0f1;
    }
    
    .refresh-btn:hover {
        background-color: #4a5f7a;
    }
    
    .user-item {
        border-bottom-color: #34495e;
    }
    
    .user-item:hover {
        background-color: #34495e;
    }
    
    .user-name {
        color: #ecf0f1;
    }
    
    .user-status {
        color: #bdc3c7;
    }
    
    .connected-time {
        color: #95a5a6;
    }
    
    .action-btn:hover {
        background-color: #4a5f7a;
    }
    
    .loading-message,
    .empty-message {
        color: #bdc3c7;
    }
}

/* ==== 动画效果 ==== */
.user-item {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ==== 可访问性支持 ==== */
.action-btn:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

.refresh-btn:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .online-users-container {
        border-width: 2px;
    }
    
    .user-item {
        border-bottom-width: 2px;
    }
    
    .session-type {
        border: 1px solid currentColor;
    }
}
