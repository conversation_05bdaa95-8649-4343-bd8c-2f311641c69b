/**
 * 用户界面样式
 * 
 * 包含用户信息展示、加载状态、错误处理等样式
 */

/* 用户资料容器 */
.user-profile {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 24px;
    margin: 16px 0;
    max-width: 600px;
    transition: box-shadow 0.3s ease;
}

.user-profile:hover {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* 用户头部信息 */
.user-header {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e5e7eb;
}

.user-avatar {
    position: relative;
    flex-shrink: 0;
}

.user-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #e5e7eb;
    transition: border-color 0.3s ease;
}

.user-avatar:hover img {
    border-color: #3b82f6;
}

.user-status {
    position: absolute;
    bottom: 0;
    right: 0;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 600;
    text-transform: uppercase;
    color: white;
    border: 2px solid white;
}

.user-status.active {
    background-color: #10b981;
}

.user-status.inactive {
    background-color: #6b7280;
}

.user-status.suspended {
    background-color: #ef4444;
}

.user-basic-info {
    flex: 1;
    min-width: 0;
}

.user-display-name {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    line-height: 1.2;
}

.user-username {
    margin: 0 0 4px 0;
    font-size: 16px;
    color: #6b7280;
    font-weight: 500;
}

.user-email {
    margin: 0;
    font-size: 14px;
    color: #9ca3af;
}

/* 用户详细信息 */
.user-details {
    margin-bottom: 24px;
}

.user-bio {
    margin-bottom: 20px;
}

.user-bio h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #374151;
}

.user-bio p {
    margin: 0;
    font-size: 14px;
    line-height: 1.6;
    color: #6b7280;
}

.user-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.info-label {
    font-size: 12px;
    font-weight: 600;
    color: #9ca3af;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.info-value {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

.info-value a {
    color: #3b82f6;
    text-decoration: none;
    transition: color 0.2s ease;
}

.info-value a:hover {
    color: #1d4ed8;
    text-decoration: underline;
}

/* 用户操作按钮 */
.user-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 36px;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover {
    background-color: #2563eb;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background-color: #e5e7eb;
    border-color: #9ca3af;
}

/* 加载状态 */
.user-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
}

.loading-spinner {
    margin-bottom: 16px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    margin: 0;
    font-size: 14px;
    color: #6b7280;
}

/* 错误状态 */
.user-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    text-align: center;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 12px;
}

.user-error.error-not-found {
    background-color: #fef3c7;
    border-color: #fcd34d;
}

.user-error.error-forbidden {
    background-color: #fef2f2;
    border-color: #fca5a5;
}

.user-error.error-unauthorized {
    background-color: #eff6ff;
    border-color: #93c5fd;
}

.user-error.error-invalid-id {
    background-color: #f3f4f6;
    border-color: #d1d5db;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.error-title {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #374151;
}

.error-message {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #6b7280;
}

.error-details {
    margin: 0 0 20px 0;
    font-size: 12px;
    color: #9ca3af;
    font-family: monospace;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 8px;
    border-radius: 4px;
    max-width: 100%;
    word-break: break-word;
}

.error-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
}

/* 提示消息 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease-out;
}

.toast-success {
    background-color: #10b981;
}

.toast-error {
    background-color: #ef4444;
}

.toast-warning {
    background-color: #f59e0b;
}

.toast-info {
    background-color: #3b82f6;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-profile {
        margin: 8px;
        padding: 16px;
    }
    
    .user-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 16px;
    }
    
    .user-info-grid {
        grid-template-columns: 1fr;
    }
    
    .user-actions {
        justify-content: center;
    }
    
    .toast {
        right: 10px;
        left: 10px;
        top: 10px;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .user-profile {
        background: #1f2937;
        color: #f9fafb;
    }
    
    .user-display-name {
        color: #f9fafb;
    }
    
    .user-username {
        color: #9ca3af;
    }
    
    .user-email {
        color: #6b7280;
    }
    
    .info-value {
        color: #d1d5db;
    }
    
    .btn-secondary {
        background-color: #374151;
        color: #d1d5db;
        border-color: #4b5563;
    }
    
    .btn-secondary:hover {
        background-color: #4b5563;
    }
}
