<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>健康检查仪表板 - Axum企业级应用</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard-container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .dashboard-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .dashboard-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .dashboard-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .dashboard-content {
            padding: 30px;
        }

        .controls-section {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .control-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .status-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            border-left: 4px solid #007bff;
            transition: transform 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
        }

        .status-card.healthy {
            border-left-color: #28a745;
        }

        .status-card.unhealthy {
            border-left-color: #dc3545;
        }

        .status-card.degraded {
            border-left-color: #ffc107;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
        }

        .status-icon {
            font-size: 1.5rem;
        }

        .status-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .status-description {
            color: #666;
            font-size: 0.9rem;
        }

        .health-checks-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .health-check-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .health-check-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .health-check-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }

        .health-check-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .health-details {
            display: grid;
            gap: 12px;
        }

        .health-detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
        }

        .detail-label {
            color: #666;
            font-weight: 500;
        }

        .detail-value {
            font-weight: 600;
            color: #333;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
        }

        .auto-refresh-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 123, 255, 0.9);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: none;
        }

        .timestamp {
            color: #666;
            font-size: 0.85rem;
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .dashboard-title {
                font-size: 2rem;
            }
            
            .controls-section {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                justify-content: center;
            }
            
            .health-checks-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1 class="dashboard-title">🏥 健康检查仪表板</h1>
            <p class="dashboard-subtitle">实时监控系统健康状态 - 7个核心检查接口</p>
        </div>

        <div class="dashboard-content">
            <!-- 控制面板 -->
            <div class="controls-section">
                <div class="control-group">
                    <button id="refreshBtn" class="btn btn-primary">
                        🔄 刷新数据
                    </button>
                    <button id="batchCheckBtn" class="btn btn-success">
                        📊 批量检查
                    </button>
                    <button id="clearCacheBtn" class="btn btn-secondary">
                        🗑️ 清除缓存
                    </button>
                </div>
                <div class="control-group">
                    <label>
                        <input type="checkbox" id="autoRefreshToggle"> 自动刷新 (30秒)
                    </label>
                </div>
            </div>

            <!-- 状态概览 -->
            <div class="status-overview">
                <div class="status-card" id="overallStatusCard">
                    <div class="status-header">
                        <span class="status-title">总体状态</span>
                        <span class="status-icon" id="overallStatusIcon">❓</span>
                    </div>
                    <div class="status-value" id="overallStatusValue">检查中...</div>
                    <div class="status-description" id="overallStatusDesc">正在获取系统状态</div>
                </div>

                <div class="status-card">
                    <div class="status-header">
                        <span class="status-title">成功检查</span>
                        <span class="status-icon">✅</span>
                    </div>
                    <div class="status-value" id="successfulChecks">-</div>
                    <div class="status-description">健康检查通过数量</div>
                </div>

                <div class="status-card">
                    <div class="status-header">
                        <span class="status-title">失败检查</span>
                        <span class="status-icon">❌</span>
                    </div>
                    <div class="status-value" id="failedChecks">-</div>
                    <div class="status-description">健康检查失败数量</div>
                </div>

                <div class="status-card">
                    <div class="status-header">
                        <span class="status-title">响应时间</span>
                        <span class="status-icon">⚡</span>
                    </div>
                    <div class="status-value" id="responseTime">-</div>
                    <div class="status-description">平均响应时间</div>
                </div>
            </div>

            <!-- 加载指示器 -->
            <div id="loadingIndicator" class="loading">
                <div class="loading-spinner"></div>
                <p>正在加载健康检查数据...</p>
            </div>

            <!-- 错误消息 -->
            <div id="errorMessage" class="error-message" style="display: none;"></div>

            <!-- 成功消息 -->
            <div id="successMessage" class="success-message" style="display: none;"></div>

            <!-- 健康检查详情 -->
            <div id="healthChecksContainer" class="health-checks-grid" style="display: none;">
                <!-- 健康检查卡片将通过JavaScript动态生成 -->
            </div>

            <!-- 数据库管理面板 -->
            <div class="database-management-panel" style="margin-top: 30px;">
                <h3 style="color: #2c3e50; margin-bottom: 20px; display: flex; align-items: center; gap: 10px;">
                    🗄️ 数据库管理
                </h3>

                <div class="database-management-grid" style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <!-- 连接池监控 -->
                    <div class="management-card" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <h4 style="color: #667eea; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                            📊 连接池监控
                        </h4>
                        <div id="poolStatusContent" style="min-height: 100px;">
                            <div style="color: #6c757d; text-align: center; padding: 20px;">
                                点击"获取连接池状态"按钮加载数据
                            </div>
                        </div>
                        <button id="getPoolStatusBtn" class="btn btn-info" style="width: 100%; margin-top: 15px;">
                            📈 获取连接池状态
                        </button>
                    </div>

                    <!-- 压力测试 -->
                    <div class="management-card" style="background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <h4 style="color: #e74c3c; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                            🔥 数据库压力测试
                        </h4>

                        <!-- 压力测试配置 -->
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">并发连接数:</label>
                            <input type="number" id="concurrentConnections" value="10" min="1" max="100"
                                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">测试持续时间(秒):</label>
                            <input type="number" id="testDuration" value="30" min="5" max="300"
                                   style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">测试类型:</label>
                            <select id="testType" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                <option value="read">读取测试</option>
                                <option value="write">写入测试</option>
                                <option value="mixed">混合测试</option>
                            </select>
                        </div>

                        <button id="runStressTestBtn" class="btn btn-warning" style="width: 100%;">
                            🚀 运行压力测试
                        </button>
                    </div>
                </div>

                <!-- 压力测试结果展示 -->
                <div id="stressTestResults" class="stress-test-results" style="display: none; background: white; border-radius: 12px; padding: 20px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                    <h4 style="color: #28a745; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                        📋 压力测试结果
                    </h4>
                    <div id="stressTestContent">
                        <!-- 测试结果将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 时间戳 -->
            <div class="timestamp" id="lastUpdateTime">
                最后更新时间: 从未更新
            </div>
        </div>
    </div>

    <!-- 自动刷新指示器 -->
    <div id="autoRefreshIndicator" class="auto-refresh-indicator">
        🔄 自动刷新中...
    </div>

    <script type="module" src="js/modules/health-dashboard.js"></script>
</body>
</html>
