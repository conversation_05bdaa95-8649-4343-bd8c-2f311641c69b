<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Axum 任务管理系统</title>

    <!-- 🚀 性能优化：DNS预解析和预连接 -->
    <link rel="dns-prefetch" href="//127.0.0.1">
    <link rel="preconnect" href="//127.0.0.1:3000" crossorigin>

    <!-- 🚀 性能优化：关键资源预加载 -->
    <link rel="preload" href="./js/modules/api.js" as="script" crossorigin>
    <link rel="preload" href="./js/modules/auth.js" as="script" crossorigin>
    <link rel="preload" href="./js/modules/performance-optimizer.js" as="script" crossorigin>
    <link rel="preload" href="./css/modules.css" as="style">

    <!-- 🚀 性能优化：PWA Manifest -->
    <link rel="manifest" href="./manifest.json">

    <!-- 🚀 性能优化：主题色彩 -->
    <meta name="theme-color" content="#3498db">
    <meta name="msapplication-TileColor" content="#3498db">

    <!-- 添加favicon，使用简单的data URI避免404错误 -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><text y='14' font-size='16'>🚀</text></svg>" type="image/svg+xml">
    <style>
        /* 统一字体和排版规范 - 针对Windows 10 Chrome优化中文文字显示 */
        body {
            /* 针对Windows 10 Chrome优化的字体族，优先使用系统字体 */
            font-family: "Segoe UI", "Microsoft YaHei UI", "Microsoft YaHei", "PingFang SC", "Hiragino Sans GB", -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, Arial, sans-serif;
            max-width: none; /* 移除最大宽度限制，使用Grid布局控制 */
            margin: 0;
            padding: 0;
            /* 针对Windows 10 Chrome优化的中文行高 */
            line-height: 1.5;
            color: #2c3e50;
            /* 统一文字方向和对齐方式 */
            direction: ltr;
            text-align: left;
            /* Windows 10 Chrome中文字间距优化 */
            letter-spacing: 0.01em;
            /* Chrome浏览器文字渲染优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
            /* Chrome浏览器背景优化 */
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        
        /* 统一标题排版规范 - 建立清晰的视觉层次 */
        h1, h2, h3, h4, h5, h6 {
            color: #2c3e50;
            /* 统一标题字体粗细 */
            font-weight: 600;
            /* 优化中文标题行高 */
            line-height: 1.4;
            /* 统一标题对齐方式 */
            text-align: left;
            /* 优化标题字间距 */
            letter-spacing: 0.01em;
            /* 统一标题边距 */
            margin-top: 0;
            margin-bottom: 15px;
        }

        /* 现代化页面标题样式 - Windows 10 Chrome优化 */
        .page-header {
            text-align: center;
            margin: 20px auto 24px auto; /* Chrome浏览器间距优化 */
            padding: 24px 20px; /* 适配Chrome的内边距渲染 */
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.06) 0%, rgba(46, 204, 113, 0.06) 100%);
            border-radius: 16px; /* Chrome圆角渲染优化 */
            border: 1px solid rgba(52, 152, 219, 0.12); /* Chrome边框透明度优化 */
            box-shadow: 0 2px 12px rgba(52, 152, 219, 0.08); /* Chrome阴影渲染优化 */
            max-width: 1400px; /* 与主容器保持一致 */
        }

        .page-header h1 {
            font-size: 2.2em;
            margin: 0;
            padding: 0;
            border: none;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .title-icon {
            font-size: 1.5em;
            animation: pulse 2s infinite;
        }

        .title-main {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 50%, #2ecc71 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
            letter-spacing: -0.02em;
        }

        .title-sub {
            font-size: 0.5em;
            color: #7f8c8d;
            font-weight: 400;
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }

        /* 响应式标题 */
        @media (max-width: 768px) {
            .page-header h1 {
                font-size: 1.8em;
            }

            .title-sub {
                font-size: 0.45em;
            }
        }

        h2 {
            font-size: 1.5em;
            margin-bottom: 18px;
        }

        h3 {
            font-size: 1.3em;
            margin-bottom: 15px;
        }

        h4 {
            font-size: 1.1em;
            margin-bottom: 12px;
        }

        /* 通用文字排版规则 - 确保所有文本元素的一致性 */
        p, span, div, li, label {
            /* 继承统一的字体族 */
            font-family: inherit;
            /* 统一文字方向和对齐 */
            direction: ltr;
            text-align: left;
            /* 优化中文文字间距 */
            letter-spacing: 0.02em;
            /* 确保文字渲染质量 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 段落样式 - 优化段落间距和行高 */
        p {
            line-height: 1.6;
            margin-bottom: 12px;
        }

        /* 列表样式 - 统一列表文字排版 */
        ul, ol {
            line-height: 1.6;
            padding-left: 20px;
        }

        li {
            margin-bottom: 6px;
            line-height: 1.6;
        }

        /* 标签样式 - 统一表单标签排版 */
        label {
            font-weight: 500;
            margin-bottom: 6px;
            display: block;
            line-height: 1.4;
        }
        
        /* 主容器 - 使用CSS Grid实现现代化三栏布局，针对Windows 10 Chrome优化 */
        .main-container {
            display: grid;
            /* 优化三栏布局比例：突出聊天功能区域 - 认证:任务:聊天 = 0.8:1:1.5 */
            grid-template-columns: minmax(280px, 0.8fr) minmax(320px, 1fr) minmax(480px, 1.5fr);
            /* 优化Grid行高设置，确保面板高度一致性 */
            grid-template-rows: minmax(500px, auto);
            gap: 24px; /* 适配Chrome的默认间距渲染 */
            min-height: 85vh; /* 针对Chrome浏览器的视口高度优化 */
            /* 改进垂直对齐：使用stretch确保面板高度一致 */
            align-items: stretch;
            /* 确保Grid容器在滚动时保持稳定对齐 */
            align-content: start;
            margin: 16px auto; /* 优化Chrome浏览器的上下边距 */
            padding: 0 16px; /* 适配Chrome的默认内边距渲染 */
            max-width: 1500px; /* 增加最大宽度以适应更大的聊天区域 */
            /* Chrome浏览器特定优化 */
            -webkit-box-sizing: border-box;
            box-sizing: border-box;
            /* 添加滚动时的布局稳定性 */
            position: relative;
            contain: layout style;
        }

        /* 面板基础样式 - 针对Windows 10 Chrome浏览器优化 */
        .panel {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); /* Chrome渲染优化的渐变背景 */
            padding: 24px; /* 适配Chrome的默认内边距渲染 */
            border-radius: 16px; /* 针对Chrome的圆角渲染优化 */
            box-shadow:
                0 4px 20px rgba(0,0,0,0.08),
                0 1px 6px rgba(0,0,0,0.04); /* 针对Chrome的阴影渲染优化 */
            border: 1px solid rgba(224, 224, 224, 0.8); /* Chrome浏览器边框优化 */
            backdrop-filter: blur(8px); /* 适配Chrome的毛玻璃效果性能 */
            display: flex;
            flex-direction: column;
            /* 优化高度设置：确保面板高度一致性和动态响应 */
            height: 100%; /* 占满Grid容器分配的高度 */
            min-height: 500px; /* 增加最小高度，确保内容充分展示 */
            max-height: calc(100vh - 200px); /* 限制最大高度，避免超出视口 */
            transition: all 0.25s ease-out; /* Chrome浏览器过渡动画优化 */
            position: relative; /* 为伪元素定位做准备 */
            /* 优化溢出处理：确保内容滚动时布局稳定 */
            overflow: visible; /* 允许内容正常显示 */
            /* Chrome浏览器特定优化 */
            -webkit-transform: translateZ(0); /* 启用硬件加速 */
            transform: translateZ(0);
            will-change: transform, box-shadow; /* 优化Chrome渲染性能 */
            /* 添加布局稳定性 */
            contain: layout style;
        }

        /* 面板装饰性背景 */
        .panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px; /* 增加装饰条高度 */
            background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12); /* 顶部彩色装饰条 */
            opacity: 0.9; /* 增加透明度，更明显的装饰效果 */
            border-radius: 18px 18px 0 0; /* 与面板圆角保持一致 */
        }

        /* 面板悬停效果 - 现代化交互反馈 */
        .panel:hover {
            box-shadow:
                0 10px 40px rgba(0,0,0,0.15),
                0 4px 12px rgba(0,0,0,0.1); /* 增强悬停阴影效果，更强的层次感 */
            transform: translateY(-3px) scale(1.01); /* 轻微上浮和放大效果 */
            border-color: rgba(255,255,255,1); /* 增强边框亮度 */
        }

        /* 面板焦点效果 */
        .panel:focus-within {
            box-shadow:
                0 10px 40px rgba(52, 152, 219, 0.2),
                0 4px 12px rgba(52, 152, 219, 0.15); /* 增强蓝色焦点阴影 */
            transform: translateY(-2px) scale(1.005);
        }

        /* 面板标题统一样式 */
        .panel > h2 {
            margin-top: 0;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e8e8e8;
            color: #2c3e50;
            font-weight: 600;
        }

        /* 子标题统一样式 */
        .panel h3, .chat-panel h3, .chat-panel h4 {
            margin-top: 15px;
            margin-bottom: 12px;
            color: #34495e;
            font-weight: 500;
        }

        /* 第一个子标题去除顶部边距 */
        .panel h3:first-of-type, .chat-panel h3:first-of-type, .chat-panel h4:first-of-type {
            margin-top: 0;
        }
        
        form {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        /* Windows 10 Chrome表单元素优化 */
        input, textarea, button {
            padding: 12px 16px; /* Chrome浏览器内边距优化 */
            border: 1px solid #d0d0d0; /* Chrome边框颜色优化 */
            border-radius: 8px; /* Chrome圆角优化 */
            /* Chrome字体优化 */
            font-family: inherit;
            font-size: 14px;
            /* Chrome中文输入体验优化 */
            line-height: 1.4;
            letter-spacing: 0.01em;
            /* 统一文字对齐 */
            text-align: left;
            direction: ltr;
            /* Chrome文字渲染优化 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            /* Chrome浏览器性能优化 */
            -webkit-appearance: none;
            appearance: none;
            transition: all 0.2s ease;
        }
        
        /* Windows 10 Chrome按钮样式优化 */
        button {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); /* Chrome渐变优化 */
            color: white;
            cursor: pointer;
            border: none;
            padding: 12px 18px; /* Chrome内边距优化 */
            border-radius: 6px; /* Chrome圆角优化 */
            font-weight: 500; /* Chrome字体粗细优化 */
            font-size: 14px;
            transition: all 0.2s ease; /* Chrome过渡优化 */
            min-height: 40px; /* Chrome最小高度优化 */
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 1px 4px rgba(52, 152, 219, 0.2); /* Chrome阴影优化 */
            /* Chrome浏览器特定优化 */
            -webkit-user-select: none;
            user-select: none;
        }

        /* 按钮波纹效果 */
        button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;
            transform: translate(-50%, -50%);
        }

        button:active::before {
            width: 300px;
            height: 300px;
            top: 50%;
            left: 50%;
        }

        button:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%); /* 悬停渐变 */
            transform: translateY(-2px) scale(1.02); /* 轻微上浮和放大 */
            box-shadow: 0 4px 16px rgba(52, 152, 219, 0.4); /* 增强阴影 */
        }

        button:active {
            transform: translateY(-1px) scale(0.98); /* 按下效果 */
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        button:disabled {
            background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%); /* 禁用渐变 */
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.6; /* 添加透明度 */
        }

        /* 按钮焦点样式 */
        button:focus {
            outline: none;
            box-shadow:
                0 4px 16px rgba(52, 152, 219, 0.4),
                0 0 0 3px rgba(52, 152, 219, 0.2); /* 焦点环 */
        }
        
        /* 任务列表容器 - Windows 10 Chrome浏览器滚动优化 */
        #taskList {
            list-style-type: none;
            padding: 0;
            /* 优化高度设置：动态响应面板高度变化 */
            flex: 1; /* 占用面板剩余空间 */
            max-height: calc(100% - 280px); /* 根据面板内其他元素动态计算 */
            min-height: 200px; /* 确保最小可用高度 */
            overflow-y: auto; /* 添加垂直滚动条 */
            border: 1px solid rgba(208, 208, 208, 0.7); /* Chrome边框渲染优化 */
            border-radius: 12px; /* Chrome圆角渲染优化 */
            background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%); /* Chrome渐变优化 */
            margin-bottom: 20px; /* Chrome间距优化 */
            /* Chrome浏览器滚动条样式优化 */
            scrollbar-width: thin;
            scrollbar-color: rgba(52, 152, 219, 0.4) rgba(245, 245, 245, 0.8);
            /* Chrome性能优化 */
            -webkit-overflow-scrolling: touch;
            /* 添加滚动时的布局稳定性 */
            contain: layout style;
            /* 确保滚动时不影响其他面板 */
            position: relative;
            z-index: 1;
        }

        /* Chrome浏览器滚动条样式优化 */
        #taskList::-webkit-scrollbar {
            width: 6px; /* Chrome浏览器滚动条宽度优化 */
        }

        #taskList::-webkit-scrollbar-track {
            background: rgba(245, 245, 245, 0.8); /* Chrome滚动条轨道优化 */
            border-radius: 3px;
        }

        #taskList::-webkit-scrollbar-thumb {
            background: rgba(52, 152, 219, 0.4); /* Chrome滚动条拇指优化 */
            border-radius: 3px;
            transition: background 0.2s ease;
        }

        #taskList::-webkit-scrollbar-thumb:hover {
            background: rgba(52, 152, 219, 0.6); /* Chrome悬停效果优化 */
        }

        /* 任务列表为空时的现代化样式 */
        #taskList:empty::before {
            content: "✨ 暂无任务，创建第一个任务开始吧！";
            display: block;
            text-align: center;
            color: #7f8c8d;
            padding: 40px 20px; /* 增加垂直内边距 */
            font-style: italic;
            font-size: 1.1em; /* 稍微增大字体 */
            background: linear-gradient(135deg, rgba(52, 152, 219, 0.05), rgba(46, 204, 113, 0.05)); /* 添加微妙背景 */
            border-radius: 8px;
            margin: 20px;
            border: 2px dashed rgba(52, 152, 219, 0.2); /* 添加虚线边框 */
        }
        
        /* 任务项样式 - 统一任务信息文字排版 */
        .task-item {
            background-color: white;
            padding: 15px;
            margin-bottom: 12px;
            border-radius: 6px;
            border-left: 3px solid #3498db;
            /* 统一任务文字排版 */
            font-family: inherit;
            line-height: 1.6;
            letter-spacing: 0.02em;
            text-align: left;
            direction: ltr;
            /* 优化文字渲染 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .task-item.completed {
            border-left-color: #2ecc71;
        }
        
        .task-controls {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 8px;
        }
        
        .delete-btn {
            background-color: #e74c3c;
        }
        
        .delete-btn:hover {
            background-color: #c0392b;
        }
        
        /* 编辑模式下的输入框样式 */
        .edit-input {
            width: calc(100% - 18px); /* 减去padding和border */
            margin-bottom: 5px;
        }

        /* 编辑模式下的任务项样式 */
        .task-item.editing {
            border-left-color: #f39c12;
            background-color: #fef9e7;
        }

        /* 编辑按钮样式 */
        .edit-btn {
            background-color: #f39c12;
        }

        .edit-btn:hover {
            background-color: #e67e22;
        }

        /* 保存按钮样式 */
        .save-btn {
            background-color: #27ae60;
        }

        .save-btn:hover {
            background-color: #229954;
        }

        /* 取消按钮样式 */
        .cancel-btn {
            background-color: #95a5a6;
        }

        .cancel-btn:hover {
            background-color: #7f8c8d;
        }
        
        #webSocketMessages {
            height: 200px;
            overflow-y: auto;
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            font-family: monospace;
            border-radius: 4px;
        }
        

        
        .timestamp {
            color: #95a5a6;
            font-size: 0.8em;
        }
        
        .response-area {
            max-height: 200px;
            overflow-y: auto;
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        
        /* 新增：筛选按钮样式 */
        .filter-btn {
            background-color: #ecf0f1;
            color: #34495e;
            border: 1px solid #bdc3c7;
        }
        
        .filter-btn:hover {
            background-color: #dce4e6;
        }
        
        .filter-btn.active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }

        /* 认证相关样式 */
        .auth-panel {
            background-color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }

        .auth-status {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .auth-info {
            display: flex;
            gap: 20px;
            align-items: center;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.9em;
        }

        .status-badge.authenticated {
            background-color: #2ecc71;
            color: white;
        }

        .status-badge.unauthenticated {
            background-color: #e74c3c;
            color: white;
        }

        .auth-forms {
            display: flex;
            gap: 20px;
            margin-top: 15px;
        }

        .auth-form {
            flex: 1;
            background-color: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
        }

        .auth-form h4 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .auth-message {
            padding: 8px 12px;
            border-radius: 4px;
            margin: 10px 0;
            font-size: 0.9em;
        }

        .auth-message.success {
            background-color: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
        }

        .auth-message.error {
            background-color: #fadbd8;
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }

        .auth-message.info {
            background-color: #ebf3fd;
            color: #2980b9;
            border: 1px solid #2980b9;
        }

        .hidden {
            display: none !important;
        }

        /* 认证标签样式 */
        .auth-tabs {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .auth-tab-btn {
            padding: 8px 20px;
            border: 1px solid #ddd;
            background-color: #f8f9fa;
            color: #333;
            cursor: pointer;
            border-radius: 4px 4px 0 0;
            font-size: 0.9em;
            transition: all 0.2s ease;
        }

        .auth-tab-btn:hover {
            background-color: #e9ecef;
        }

        .auth-tab-btn.active {
            background-color: #3498db;
            color: white;
            border-color: #3498db;
        }

        /* 聊天消息样式 */
        .chat-message {
            margin-bottom: 8px;
            padding: 6px 10px;
            border-radius: 6px;
            line-height: 1.4;
        }

        .chat-message.own-message {
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .chat-message.other-message {
            background-color: #f5f5f5;
            border-left: 3px solid #4caf50;
        }

        .chat-message.system-message {
            background-color: #fff3e0;
            border-left: 3px solid #ff9800;
            font-style: italic;
        }

        .chat-message .sender {
            font-weight: bold;
            color: #2c3e50;
        }

        .chat-message .content {
            color: #34495e;
        }

        .chat-message .system-text {
            color: #e67e22;
        }

        /* ===== 聊天大厅样式 ===== */

        /* 聊天大厅容器 - 使用Grid布局确保元素位置稳定，防止覆盖问题 */
        .chat-hall-container {
            /* Grid布局在后面的特定优化中定义，这里保留基础样式 */
            flex: 1; /* 占满父容器剩余空间 */
            contain: layout;
        }

        /* 聊天面板样式 - 适应垂直布局，优化空间分配 */
        .chat-panel {
            background-color: white;
            padding: 18px; /* 增加内边距 */
            border-radius: 12px; /* 增加圆角，与主面板保持一致 */
            box-shadow: 0 3px 12px rgba(0,0,0,0.08); /* 增强阴影 */
            display: flex;
            flex-direction: column;
            border: 1px solid rgba(232, 232, 232, 0.8); /* 使用半透明边框 */
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 更流畅的过渡 */
            /* 优化高度设置：确保面板间高度协调 */
            min-height: 120px; /* 减少最小高度，提高空间利用率 */
            flex-shrink: 0; /* 防止面板被过度压缩 */
            /* 添加布局稳定性 */
            contain: layout style;
            /* 修复层级问题 */
            position: relative;
            z-index: 1; /* 基础层级 */
        }

        /* 聊天消息面板特殊样式 - 在Grid布局中占据主要空间 */
        .chat-panel.main-chat {
            /* Grid布局中不需要flex属性，通过grid-row控制 */
            min-height: 300px; /* 增加最小高度 */
            z-index: 5; /* 确保主聊天面板在其他面板之上 */
            /* 确保聊天面板内部使用flex布局 */
            display: flex;
            flex-direction: column;
        }

        /* 连接控制面板样式 - 紧凑布局，Grid布局中的固定高度 */
        .chat-panel.control-panel {
            min-height: 140px; /* 适中的高度 */
            /* Grid布局中通过grid-row控制位置 */
        }

        /* 在线用户面板样式 - 中等高度，Grid布局中的固定高度 */
        .chat-panel.users-panel {
            min-height: 180px; /* 适中的高度 */
            max-height: 250px; /* 限制最大高度 */
            /* Grid布局中通过grid-row控制位置 */
        }

        /* 原始日志面板样式 - 紧凑布局 */
        .chat-panel.log-panel {
            flex-shrink: 0; /* 保持固定高度 */
            min-height: 120px; /* 较小的高度 */
            max-height: 180px; /* 限制最大高度 */
        }

        /* 聊天面板悬停效果 */
        .chat-panel:hover {
            box-shadow: 0 5px 20px rgba(0,0,0,0.12); /* 增强悬停阴影 */
            transform: translateY(-1px); /* 轻微上浮效果 */
        }

        /* 聊天区域样式 */
        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; /* 允许flex子项收缩 */
        }

        /* 消息容器样式 - 统一聊天窗口和原始日志区域的外观 */
        .messages-container {
            flex: 1;
            overflow-y: auto;
            border: 1px solid rgba(221, 221, 221, 0.6); /* 使用半透明边框 */
            border-radius: 10px; /* 增加圆角，与整体设计保持一致 */
            padding: 16px; /* 增加内边距，提升内容呼吸感 */
            background: linear-gradient(135deg, #fafafa 0%, #f8f9fa 100%); /* 添加微妙渐变 */
            margin-bottom: 8px; /* 减少底部间距，为输入区域让出空间 */
            /* 优化高度设置：为聊天区域提供更多空间，确保布局稳定 */
            min-height: 200px; /* 增加最小高度，确保聊天体验 */
            max-height: 400px; /* 设置固定最大高度，防止无限增长影响其他元素 */
            /* 优化滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: rgba(52, 152, 219, 0.3) transparent;
            /* 添加滚动时的布局稳定性 */
            contain: layout style;
            position: relative;
            /* 确保滚动行为平滑 */
            scroll-behavior: smooth;
        }

        /* WebKit浏览器滚动条样式 */
        .messages-container::-webkit-scrollbar {
            width: 6px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: #bdc3c7;
            border-radius: 3px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover {
            background: #95a5a6;
        }

        /* 消息样式 - 统一文字排版和布局 */
        .message {
            margin-bottom: 12px;
            padding: 10px 14px;
            border-radius: 8px;
            max-width: 80%;
            word-wrap: break-word;
            /* 统一消息文字排版 */
            font-family: inherit;
            line-height: 1.5;
            letter-spacing: 0.02em;
            text-align: left;
            direction: ltr;
            /* 优化文字渲染 */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .message.system {
            background-color: #f39c12;
            color: white;
            text-align: center;
            max-width: 100%;
        }

        .message.user-joined {
            background-color: #2ecc71;
            color: white;
            text-align: center;
            max-width: 100%;
        }

        .message.user-left {
            background-color: #e74c3c;
            color: white;
            text-align: center;
            max-width: 100%;
        }

        .message.text {
            background-color: #3498db;
            color: white;
        }

        .message.own {
            background-color: #27ae60;
            margin-left: auto;
        }

        /* 消息头部样式 - 统一时间戳和发送者信息排版 */
        .message-header {
            font-size: 0.85em;
            opacity: 0.85;
            margin-bottom: 6px;
            /* 统一头部文字排版 */
            font-weight: 500;
            line-height: 1.3;
            letter-spacing: 0.01em;
            text-align: left;
        }

        /* 消息内容样式 - 优化正文文字显示 */
        .message-content {
            font-size: 0.95em;
            /* 优化消息内容排版 */
            line-height: 1.6;
            letter-spacing: 0.02em;
            text-align: left;
            word-break: break-word;
            /* 确保长文本正确换行 */
            overflow-wrap: break-word;
        }

        /* 输入区域样式 - 优化聊天输入体验，确保发送按钮始终可见 */
        .input-area {
            display: flex;
            gap: 12px; /* 增加间距 */
            padding: 16px; /* 增加内边距，提升视觉重要性 */
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%); /* 添加背景渐变 */
            border-radius: 12px; /* 增加圆角 */
            border: 1px solid rgba(52, 152, 219, 0.2); /* 添加蓝色边框 */
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.1); /* 添加阴影 */
            margin-top: 12px; /* 与消息容器分离 */
            /* 修复发送按钮被遮挡问题：使用sticky定位确保始终在视口内 */
            position: sticky;
            bottom: 0;
            z-index: 100; /* 提高层级确保输入区域在其他元素之上 */
            /* 确保输入区域不会被其他元素覆盖 */
            flex-shrink: 0; /* 防止被压缩 */
        }

        .input-area input {
            flex: 1;
            padding: 14px 16px; /* 增加内边距 */
            border: 2px solid #e1e8ed; /* 增强边框 */
            border-radius: 8px; /* 增加圆角 */
            font-size: 15px; /* 稍微增大字体 */
            font-family: inherit;
            line-height: 1.4;
            transition: all 0.3s ease; /* 添加过渡效果 */
            background-color: #ffffff;
            /* 优化中文输入体验 */
            letter-spacing: 0.02em;
            -webkit-font-smoothing: antialiased;
        }

        .input-area input:focus {
            border-color: #3498db; /* 聚焦时蓝色边框 */
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.15); /* 聚焦光环 */
            outline: none;
            transform: scale(1.01); /* 轻微放大 */
        }

        .input-area input:disabled {
            background-color: #f8f9fa;
            color: #95a5a6;
            border-color: #e1e8ed;
            cursor: not-allowed;
        }

        .input-area button {
            padding: 14px 20px; /* 增加内边距 */
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); /* 渐变背景 */
            color: white;
            border: none;
            border-radius: 8px; /* 增加圆角 */
            cursor: pointer;
            font-size: 15px; /* 与输入框字体大小一致 */
            font-weight: 600; /* 增加字体粗细 */
            min-width: 80px; /* 最小宽度 */
            transition: all 0.3s ease; /* 添加过渡效果 */
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3); /* 添加阴影 */
            position: relative;
            overflow: hidden;
        }

        /* 发送按钮特定样式 - 确保始终可见且可点击 */
        #sendBtn {
            /* 确保按钮不会被其他元素覆盖 */
            position: relative;
            z-index: 50; /* 高层级确保可见性 */
            /* 防止按钮被压缩或隐藏 */
            flex-shrink: 0;
            /* 确保按钮在视口内 */
            display: inline-block;
        }

        .input-area button:hover:not(:disabled) {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%); /* 悬停渐变 */
            transform: translateY(-2px) scale(1.05); /* 上浮和放大 */
            box-shadow: 0 4px 16px rgba(52, 152, 219, 0.4); /* 增强阴影 */
        }

        .input-area button:active:not(:disabled) {
            transform: translateY(-1px) scale(1.02); /* 按下效果 */
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
        }

        .input-area button:disabled {
            background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%); /* 禁用渐变 */
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
            opacity: 0.7; /* 降低透明度 */
        }

        /* 在线用户列表样式 - 优化布局和视觉效果 */
        .online-users {
            max-height: 320px; /* 稍微增加最大高度 */
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px; /* 与其他元素圆角保持一致 */
            background-color: #fafafa;
            /* 继承消息容器的滚动条样式 */
            scrollbar-width: thin;
            scrollbar-color: #bdc3c7 #f8f9fa;
        }

        /* WebKit浏览器滚动条样式 */
        .online-users::-webkit-scrollbar {
            width: 6px;
        }

        .online-users::-webkit-scrollbar-track {
            background: #f8f9fa;
            border-radius: 3px;
        }

        .online-users::-webkit-scrollbar-thumb {
            background: #bdc3c7;
            border-radius: 3px;
        }

        .online-users::-webkit-scrollbar-thumb:hover {
            background: #95a5a6;
        }

        /* 用户列表项样式 - 统一用户信息文字排版 */
        .user-item {
            padding: 12px 14px; /* 增加内边距 */
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease; /* 添加悬停过渡效果 */
            /* 统一用户信息文字排版 */
            font-family: inherit;
            font-size: 0.9em;
            line-height: 1.4;
            letter-spacing: 0.01em;
            text-align: left;
            direction: ltr;
        }

        .user-item:hover {
            background-color: #f0f0f0; /* 添加悬停效果 */
        }

        .user-item:last-child {
            border-bottom: none;
        }

        /* 连接状态样式 - 优化视觉效果和一致性 */
        .connection-status {
            padding: 12px; /* 增加内边距 */
            border-radius: 6px; /* 与其他元素圆角保持一致 */
            text-align: center;
            margin-bottom: 15px; /* 与其他元素间距保持一致 */
            font-weight: 600; /* 稍微增加字体粗细 */
            font-size: 0.95em;
            transition: all 0.3s ease; /* 添加状态切换过渡效果 */
        }

        .connection-status.connected {
            background-color: #d5f4e6;
            color: #27ae60;
            border: 1px solid #27ae60;
            box-shadow: 0 2px 4px rgba(39, 174, 96, 0.2); /* 添加绿色阴影 */
        }

        .connection-status.disconnected {
            background-color: #fadbd8;
            color: #e74c3c;
            border: 1px solid #e74c3c;
            box-shadow: 0 2px 4px rgba(231, 76, 60, 0.2); /* 添加红色阴影 */
        }

        /* 🔄 重连状态样式 */
        .connection-status.reconnecting {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffc107;
            box-shadow: 0 2px 4px rgba(255, 193, 7, 0.2); /* 添加黄色阴影 */
            animation: pulse 1.5s ease-in-out infinite; /* 添加脉冲动画 */
        }

        /* 重连状态脉冲动画 */
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* 控制按钮样式 - 优化布局和视觉效果 */
        .controls {
            display: flex;
            gap: 12px; /* 增加按钮间距 */
            margin-bottom: 15px; /* 增加底部间距 */
            align-items: stretch; /* 确保按钮高度一致 */
        }

        .controls button {
            flex: 1;
            padding: 10px 12px; /* 增加内边距提升触摸体验 */
            font-size: 0.9em;
            font-weight: 500; /* 增加字体粗细 */
            border: none;
            border-radius: 6px; /* 稍微增加圆角 */
            cursor: pointer;
            color: white;
            transition: all 0.2s ease; /* 添加过渡效果 */
            min-height: 40px; /* 确保最小高度 */
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 心跳按钮样式 - 优化颜色和交互效果 */
        .ping-btn {
            background-color: #f39c12;
            box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3); /* 添加阴影 */
        }

        .ping-btn:hover:not(:disabled) {
            background-color: #e67e22;
            transform: translateY(-1px); /* 轻微上移效果 */
            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.4);
        }

        /* 获取用户按钮样式 - 优化颜色和交互效果 */
        .users-btn {
            background-color: #9b59b6;
            box-shadow: 0 2px 4px rgba(155, 89, 182, 0.3); /* 添加阴影 */
        }

        .users-btn:hover:not(:disabled) {
            background-color: #8e44ad;
            transform: translateY(-1px); /* 轻微上移效果 */
            box-shadow: 0 4px 8px rgba(155, 89, 182, 0.4);
        }

        /* 禁用状态样式 - 统一禁用按钮外观 */
        .ping-btn:disabled, .users-btn:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }

        /* 清除日志按钮样式 - 与其他按钮保持一致 */
        #clearLogBtn {
            width: 100%;
            padding: 10px 12px;
            font-size: 0.9em;
            font-weight: 500;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            background-color: #e74c3c; /* 使用红色表示清除操作 */
            color: white;
            transition: all 0.2s ease;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
        }

        #clearLogBtn:hover {
            background-color: #c0392b;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(231, 76, 60, 0.4);
        }

        #clearLogBtn:active {
            transform: translateY(0);
            box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
        }

        /* ===== 新增：布局稳定性和对齐优化 ===== */

        /* 面板内容区域优化 - 确保内容区域高度协调 */
        .panel > * {
            flex-shrink: 0; /* 防止面板内元素被过度压缩 */
        }

        .panel > #taskList,
        .panel > .chat-hall-container,
        .panel > .messages-container {
            flex-shrink: 1; /* 允许这些容器适当收缩 */
        }

        /* 任务管理面板特定优化 */
        .panel:nth-child(2) {
            /* 确保任务管理面板的内容布局稳定 */
            display: flex;
            flex-direction: column;
        }

        .panel:nth-child(2) > form,
        .panel:nth-child(2) > button,
        .panel:nth-child(2) > div:not(#taskList),
        .panel:nth-child(2) > h2,
        .panel:nth-child(2) > h3 {
            flex-shrink: 0; /* 保持表单和按钮区域固定 */
        }

        /* WebSocket聊天面板特定优化 - 使用Grid布局确保稳定性 */
        .panel:nth-child(3) {
            /* 确保聊天面板的垂直布局稳定 */
            display: flex;
            flex-direction: column;
        }

        /* 聊天大厅容器使用Grid布局确保元素位置稳定 */
        .chat-hall-container {
            display: grid;
            grid-template-rows: auto 1fr auto auto; /* 控制面板 聊天区域 用户面板 日志面板 */
            gap: 16px;
            height: 100%;
            min-height: 0; /* 允许grid子项收缩 */
        }

        /* 滚动时的布局锁定 - 防止滚动影响其他面板 */
        .main-container:has(.panel:hover) .panel:not(:hover) {
            /* 当某个面板被悬停时，其他面板保持稳定 */
            transform: none !important;
        }

        /* 动态内容变化时的过渡优化 */
        #taskList .task-item {
            transition: all 0.3s ease;
        }

        /* 添加任务时的布局稳定性 */
        #taskList:has(.task-item:last-child) {
            /* 当有任务项时，确保滚动区域稳定 */
            scroll-behavior: smooth;
        }

        /* Windows 10 Chrome响应式设计优化 - 移动优先方法 */
        @media (max-width: 768px) {
            body {
                padding: 12px; /* Chrome移动端边距优化 */
            }

            .main-container {
                grid-template-columns: 1fr; /* 移动端单列布局 */
                grid-template-rows: auto auto auto; /* 明确三行布局 */
                gap: 16px; /* Chrome移动端间距优化 */
                min-height: auto;
                margin: 12px auto; /* Chrome移动端上下边距优化 */
                padding: 0 8px; /* Chrome移动端左右内边距优化 */
                max-width: 100%; /* 移动端占满宽度 */
                /* 移动端取消stretch对齐，使用auto高度 */
                align-items: start;
            }

            .panel {
                /* 移动端优化高度设置 */
                height: auto; /* 移动端使用自动高度 */
                min-height: 320px; /* 减少移动端最小高度 */
                max-height: none; /* 移动端取消最大高度限制 */
                padding: 20px; /* 适当减少内边距 */
                border-radius: 12px; /* 适当减少圆角 */
            }

            /* 移动端聊天面板特殊处理 - 确保聊天功能优先 */
            .panel:nth-child(3) {
                /* 聊天面板在移动端占据更多空间 */
                min-height: 500px; /* 增加聊天面板高度 */
                order: 1; /* 将聊天面板移到最前面 */
            }

            .panel:nth-child(1) {
                order: 2; /* 认证面板移到中间 */
            }

            .panel:nth-child(2) {
                order: 3; /* 任务管理面板移到最后 */
            }

            .chat-hall-container {
                gap: 12px; /* 适中的间距 */
            }

            .chat-panel {
                padding: 16px; /* 增加内边距提升触摸体验 */
                min-height: 120px;
                border-radius: 12px; /* 保持一致的圆角 */
            }

            /* 移动端聊天面板高度优化 */
            .chat-panel.main-chat {
                min-height: 250px; /* 移动端聊天区域更大 */
                flex: 3; /* 占据更多空间 */
            }

            .chat-panel.control-panel {
                min-height: 100px; /* 移动端控制面板更紧凑 */
            }

            .chat-panel.users-panel {
                min-height: 120px; /* 移动端用户面板适中 */
                max-height: 180px;
            }

            .chat-panel.log-panel {
                min-height: 80px; /* 移动端日志面板更紧凑 */
                max-height: 120px;
            }

            .controls {
                flex-direction: column;
                gap: 12px; /* 增加按钮间距 */
            }

            .controls button {
                min-height: 48px; /* 增加按钮高度提升触摸体验 */
                font-size: 1em;
                border-radius: 8px; /* 保持圆角一致性 */
            }

            .messages-container {
                min-height: 180px; /* 移动端增加消息容器高度 */
                border-radius: 8px; /* 保持圆角一致性 */
            }

            /* 移动端输入区域优化 */
            .input-area {
                padding: 12px; /* 移动端减少内边距 */
                gap: 10px;
            }

            .input-area input {
                padding: 12px 14px; /* 移动端适中的内边距 */
                font-size: 16px; /* 防止iOS缩放 */
            }

            .input-area button {
                padding: 12px 16px;
                font-size: 16px; /* 防止iOS缩放 */
                min-width: 70px;
            }

            #taskList {
                max-height: 280px; /* 适中的任务列表高度 */
                border-radius: 8px; /* 保持圆角一致性 */
            }

            /* 移动端优化表单元素 */
            input, textarea, button {
                border-radius: 8px; /* 统一圆角 */
                font-size: 16px; /* 防止iOS缩放 */
            }

            /* 移动端优化面板标题 */
            .panel > h2 {
                font-size: 1.3em; /* 适中的标题大小 */
                margin-bottom: 16px;
            }
        }

        /* Windows 10 Chrome平板设备优化 */
        @media (max-width: 1024px) and (min-width: 769px) {
            .main-container {
                /* 平板端优化布局：认证面板上方，下方任务管理和聊天并排，聊天区域更大 */
                grid-template-columns: 0.8fr 1.2fr; /* 平板端两列布局，聊天区域更大 */
                grid-template-rows: auto minmax(450px, auto); /* 优化行高设置 */
                gap: 20px; /* Chrome平板端间距优化 */
                padding: 0 12px; /* Chrome平板端左右内边距优化 */
                /* 平板端使用stretch确保第二行面板高度一致 */
                align-items: stretch;
            }

            .panel:first-child {
                grid-column: 1 / -1; /* 认证面板占满第一行 */
                height: auto; /* 认证面板使用自动高度 */
                min-height: 280px; /* 减少认证面板高度 */
                max-height: none; /* 取消最大高度限制 */
            }

            .panel:nth-child(2) {
                grid-column: 1; /* 任务管理面板占左列 */
                grid-row: 2;
            }

            .panel:nth-child(3) {
                grid-column: 2; /* WebSocket聊天面板占右列，空间更大 */
                grid-row: 2;
            }

            .panel:nth-child(2),
            .panel:nth-child(3) {
                /* 第二行面板使用统一高度设置 */
                height: 100%; /* 占满Grid分配的高度 */
                min-height: 450px; /* 平板端增加最小高度 */
                max-height: calc(100vh - 400px); /* 限制最大高度 */
                padding: 20px; /* 适当减少内边距 */
            }

            #taskList {
                max-height: 350px; /* 平板端适中的任务列表高度 */
            }

            /* 平板端优化聊天面板 */
            .chat-hall-container {
                gap: 14px; /* 平板端适中间距 */
            }

            .chat-panel {
                padding: 16px;
                min-height: 120px;
            }

            /* 平板端聊天面板高度优化 */
            .chat-panel.main-chat {
                min-height: 200px; /* 平板端聊天区域更大 */
                flex: 2.5; /* 占据更多空间 */
            }

            .chat-panel.control-panel {
                min-height: 120px; /* 平板端控制面板适中 */
            }

            .chat-panel.users-panel {
                min-height: 140px; /* 平板端用户面板适中 */
                max-height: 200px;
            }

            .chat-panel.log-panel {
                min-height: 100px; /* 平板端日志面板适中 */
                max-height: 150px;
            }

            /* 平板端输入区域优化 */
            .input-area {
                padding: 14px; /* 平板端适中的内边距 */
            }

            .input-area input {
                padding: 12px 14px;
                font-size: 15px;
            }

            .input-area button {
                padding: 12px 18px;
                font-size: 15px;
                min-width: 75px;
            }

            /* 平板端优化按钮 */
            .controls button {
                min-height: 44px;
                font-size: 0.95em;
            }
        }

        /* 大屏幕优化 */
        @media (min-width: 1400px) {
            .main-container {
                gap: 36px; /* 大屏幕增加间距，更好的视觉分离 */
                padding: 0 32px; /* 增加左右内边距 */
            }

            .panel {
                padding: 32px; /* 大屏幕增加内边距 */
                min-height: 520px; /* 大屏幕增加最小高度，更好利用空间 */
            }

            #taskList {
                max-height: 520px; /* 大屏幕增加任务列表高度 */
            }
        }

        /* 超大屏幕优化 */
        @media (min-width: 1600px) {
            .main-container {
                gap: 40px; /* 超大屏幕进一步增加间距 */
                padding: 0 40px;
                max-width: 1600px; /* 增加最大宽度限制 */
            }

            .panel {
                padding: 36px;
                min-height: 560px;
            }

            #taskList {
                max-height: 560px;
            }
        }

        /* 现代化加载动画 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -200px 0;
            }
            100% {
                background-position: calc(200px + 100%) 0;
            }
        }

        /* 页面加载动画 */
        .main-container {
            animation: fadeInUp 0.6s ease-out;
        }

        .panel {
            animation: fadeInUp 0.6s ease-out;
        }

        .panel:nth-child(1) {
            animation-delay: 0.1s;
        }

        .panel:nth-child(2) {
            animation-delay: 0.2s;
        }

        .panel:nth-child(3) {
            animation-delay: 0.3s;
        }

        /* 加载状态样式 */
        .loading {
            position: relative;
            pointer-events: none;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(
                90deg,
                transparent,
                rgba(255, 255, 255, 0.4),
                transparent
            );
            background-size: 200px 100%;
            animation: shimmer 1.5s infinite;
        }

        /* 现代化滚动行为 */
        html {
            scroll-behavior: smooth;
        }

        /* 现代化选择样式 */
        ::selection {
            background: rgba(52, 152, 219, 0.2);
            color: #2c3e50;
        }

        /* 现代化焦点样式 */
        *:focus {
            outline: none;
        }

        input:focus, textarea:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            transform: scale(1.01);
        }

        /* 现代化过渡效果 */
        * {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        /* 减少动画对于偏好减少动画的用户 */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* ===== Windows 10 Chrome浏览器专用优化 ===== */

        /* Chrome浏览器特定的1920x1080分辨率优化 */
        @media screen and (min-width: 1920px) and (max-width: 1920px) {
            .main-container {
                max-width: 1700px; /* 针对1920px宽度的最佳显示，为聊天区域提供更多空间 */
                gap: 32px; /* 大屏幕增加间距，提升视觉分离 */
                padding: 0 24px;
                /* 1920x1080分辨率下的优化布局比例 */
                grid-template-columns: minmax(320px, 0.75fr) minmax(380px, 1fr) minmax(600px, 1.6fr);
            }

            .panel {
                min-height: 550px; /* 1920x1080分辨率下的最佳高度 */
                padding: 32px; /* 大屏幕增加内边距 */
            }

            /* 1920x1080分辨率下聊天面板优化 */
            .chat-panel.main-chat {
                min-height: 350px; /* 大屏幕聊天区域更大 */
            }

            .chat-panel.control-panel {
                min-height: 160px; /* 大屏幕控制面板适中 */
            }

            .chat-panel.users-panel {
                min-height: 200px; /* 大屏幕用户面板适中 */
                max-height: 280px;
            }

            .chat-panel.log-panel {
                min-height: 140px; /* 大屏幕日志面板适中 */
                max-height: 200px;
            }

            /* 1920x1080分辨率下输入区域优化 */
            .input-area {
                padding: 18px; /* 大屏幕增加内边距 */
                gap: 14px;
            }

            .input-area input {
                padding: 16px 18px; /* 大屏幕增加内边距 */
                font-size: 16px;
            }

            .input-area button {
                padding: 16px 24px; /* 大屏幕增加内边距 */
                font-size: 16px;
                min-width: 90px;
            }

            .messages-container {
                min-height: 250px; /* 大屏幕消息容器更大 */
            }
        }

        /* Chrome浏览器字体渲染优化 */
        @supports (-webkit-appearance: none) {
            body, input, textarea, button {
                font-feature-settings: "kern" 1, "liga" 1; /* Chrome字体特性优化 */
                text-rendering: optimizeLegibility;
            }
        }

        /* Chrome浏览器硬件加速优化 */
        .panel, .main-container, .page-header {
            -webkit-transform: translate3d(0, 0, 0); /* 强制硬件加速 */
            transform: translate3d(0, 0, 0);
        }

        /* Chrome浏览器滚动性能优化 */
        .messages-container, #taskList, .online-users {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
        }

        /* Windows 10 Chrome特定的焦点样式优化 */
        input:focus, textarea:focus, button:focus {
            outline: 2px solid rgba(52, 152, 219, 0.5);
            outline-offset: 2px;
        }

        /* Chrome浏览器Grid布局兼容性优化 */
        @supports (display: grid) {
            .main-container {
                display: grid;
                grid-gap: 24px; /* 使用grid-gap确保兼容性 */
            }
        }

        /* Chrome浏览器Flexbox回退方案 */
        @supports not (display: grid) {
            .main-container {
                display: flex;
                flex-wrap: wrap;
                gap: 24px;
            }

            .panel {
                flex: 1 1 300px;
                min-width: 300px;
            }
        }

        /* 新增：面板特定优化 */
        .panel:first-child {
            /* 认证面板特殊样式 */
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%, rgba(52, 152, 219, 0.02) 100%);
        }

        .panel:nth-child(2) {
            /* 任务管理面板特殊样式 */
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%, rgba(46, 204, 113, 0.02) 100%);
        }

        .panel:nth-child(3) {
            /* WebSocket聊天面板特殊样式 */
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%, rgba(243, 156, 18, 0.02) 100%);
        }

        /* 新增：优化间距和布局细节 */
        .main-container > .panel + .panel {
            /* 面板之间的额外视觉分离 */
            position: relative;
        }

        /* 新增：提升可访问性的焦点指示器 */
        .panel:focus-within::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 2px solid rgba(52, 152, 219, 0.5);
            border-radius: 20px;
            pointer-events: none;
        }

        /* ===== 新增：任务列表动态变化优化 ===== */

        /* 任务项添加/删除动画 */
        .task-item {
            animation: taskItemFadeIn 0.3s ease-out;
            transform-origin: top center;
        }

        @keyframes taskItemFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px) scale(0.95);
                max-height: 0;
                margin-bottom: 0;
                padding-top: 0;
                padding-bottom: 0;
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
                max-height: 200px;
                margin-bottom: 12px;
                padding-top: 15px;
                padding-bottom: 15px;
            }
        }

        /* 任务列表容器高度变化时的平滑过渡 */
        #taskList {
            transition: max-height 0.3s ease, min-height 0.3s ease;
        }

        /* 当任务列表为空时的特殊处理 */
        #taskList:empty {
            min-height: 120px; /* 空列表时的最小高度 */
            transition: min-height 0.3s ease;
        }

        /* 任务列表滚动时的其他面板稳定性 */
        .main-container:has(#taskList:hover) .panel:not(:nth-child(2)) {
            /* 当任务列表被悬停时，其他面板保持稳定 */
            will-change: auto;
        }

        /* 面板高度变化时的协调机制 */
        .panel:has(#taskList) {
            /* 包含任务列表的面板特殊处理 */
            transition: height 0.3s ease, min-height 0.3s ease;
        }

        /* 响应式设计优化 - 移动端和小屏幕适配 */
        @media (max-width: 768px) {
            /* 移动端单列布局 */
            .main-container {
                grid-template-columns: 1fr; /* 单列布局 */
                grid-template-rows: auto auto auto; /* 三行布局 */
                gap: 12px; /* 减小间距 */
            }

            /* 聊天大厅容器在移动端的优化 */
            .chat-hall-container {
                grid-template-rows: auto 1fr auto; /* 简化为三行：控制面板 聊天区域 其他面板 */
                gap: 12px;
            }

            /* 移动端消息容器高度调整 */
            .messages-container {
                max-height: 300px; /* 减小移动端高度 */
                min-height: 150px;
            }

            /* 移动端输入区域优化 */
            .input-area {
                padding: 12px; /* 减小内边距 */
                gap: 8px; /* 减小间距 */
            }

            /* 移动端面板内边距优化 */
            .panel, .chat-panel {
                padding: 12px; /* 减小内边距 */
                margin: 8px 0; /* 减小外边距 */
            }
        }

        /* 平板端适配 */
        @media (min-width: 769px) and (max-width: 1024px) {
            .main-container {
                grid-template-columns: 0.8fr 1.2fr; /* 两列布局 */
            }

            /* 平板端消息容器高度调整 */
            .messages-container {
                max-height: 350px;
            }
        }

        /* 大屏幕优化 */
        @media (min-width: 1920px) {
            .main-container {
                max-width: 1800px; /* 限制最大宽度 */
                margin: 0 auto; /* 居中显示 */
            }

            /* 大屏幕消息容器高度增加 */
            .messages-container {
                max-height: 500px;
            }
        }
    </style>
    <!-- ES6模块化CSS样式 -->
    <link rel="stylesheet" href="css/modules.css">
</head>
<body>
    <!-- 现代化页面主标题 -->
    <header class="page-header">
        <h1>
            <span class="title-icon">🚀</span>
            <span class="title-main">Axum 任务管理系统</span>
            <span class="title-sub">企业级聊天应用演示平台</span>
        </h1>
    </header>

    <!-- 页面主要内容容器，使用 CSS Grid 三栏布局 -->
    <div class="main-container">
        <!-- 左侧面板：用户认证区域 -->
        <div class="panel">
            <h2>用户认证</h2>

            <!-- 认证状态显示 -->
            <div class="auth-status">
                <div class="auth-info">
                    <span class="status-badge unauthenticated" id="authStatus">未认证</span>
                    <span id="currentUser">未登录</span>
                </div>
                <div class="auth-actions">
                    <button id="onlineUsersBtn" class="hidden" title="查看在线用户">
                        👥 在线用户 (<span id="onlineUsersCount">0</span>)
                    </button>
                    <button id="logoutBtn" class="hidden">登出</button>
                </div>
            </div>

            <!-- 认证表单区域 -->
            <div class="auth-forms" id="authForms">
                <!-- 标签切换按钮 -->
                <div class="auth-tabs" style="margin-bottom: 15px;">
                    <button type="button" id="loginTab" class="auth-tab-btn active">登录</button>
                    <button type="button" id="registerTab" class="auth-tab-btn">注册</button>
                </div>

                <!-- 登录表单 -->
                <div class="auth-form" id="loginFormContainer">
                    <h4>用户登录</h4>
                    <form id="loginForm">
                        <input type="text" id="loginUsername" name="username" placeholder="用户名" required>
                        <input type="password" id="loginPassword" name="password" placeholder="密码" required>
                        <button type="submit">登录</button>
                    </form>
                    <div id="loginMessage"></div>
                </div>

                <!-- 注册表单 -->
                <div class="auth-form hidden" id="registerFormContainer">
                    <h4>用户注册</h4>
                    <form id="registerForm">
                        <input type="text" id="registerUsername" name="username" placeholder="用户名" required>
                        <input type="password" id="registerPassword" name="password" placeholder="密码" required>
                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="确认密码" required>
                        <button type="submit">注册</button>
                    </form>
                    <div id="registerMessage"></div>
                </div>
            </div>
        </div>
        <!-- 中间面板：任务管理功能区域 -->
        <div class="panel">
            <h2>任务管理</h2>
            
            <!-- 创建任务的表单区域 -->
            <h3>创建新任务</h3>
            <form id="taskForm">
                <!-- 任务标题输入框 -->
                <input type="text" id="title" name="title" placeholder="任务标题" required>
                <!-- 任务描述文本域 -->
                <textarea id="description" name="description" placeholder="任务描述（可选）"></textarea>
                <!-- 任务完成状态复选框 -->
                <label>
                    <input type="checkbox" id="completed" name="completed">
                    已完成
                </label>
                <!-- 提交表单按钮，用于创建任务 -->
                <button type="submit">创建任务</button>
            </form>
            
            <!-- 刷新任务列表的按钮 -->
            <button id="refreshTasksBtn">刷新任务列表</button>
            
            <!-- 新增：任务筛选按钮 -->
            <div id="filter-buttons" style="margin-top: 10px;">
                <button class="filter-btn active" data-filter="all">所有</button>
                <button class="filter-btn" data-filter="active">未完成</button>
                <button class="filter-btn" data-filter="completed">已完成</button>
            </div>
            
            <!-- 显示任务列表的区域 -->
            <h3>任务列表</h3>
            <ul id="taskList"></ul>
            
            <!-- 显示 API 响应结果的区域 -->
            <h3>API响应</h3>
            <div id="apiResponse" class="response-area">等待API响应...</div>
        </div>
        
        <!-- 右侧面板：WebSocket 聊天大厅功能区域 -->
        <div class="panel">
            <h2>WebSocket 聊天大厅</h2>

            <!-- 聊天大厅容器 - 垂直布局，优化空间分配 -->
            <div class="chat-hall-container">
                <!-- 连接状态区域 - 紧凑布局 -->
                <div class="chat-panel control-panel">
                    <h3>连接状态</h3>
                    <div class="connection-status disconnected" id="connectionStatus">未连接</div>

                    <div class="controls">
                        <!-- 隐藏手动连接按钮，改为自动连接 -->
                        <button id="wsConnectBtn" style="display: none;">连接</button>
                        <button id="wsDisconnectBtn" disabled>断开</button>
                        <button id="pingBtn" class="ping-btn" disabled>发送心跳</button>
                        <button id="getUsersBtn" class="users-btn" disabled>获取在线用户</button>
                    </div>
                </div>

                <!-- 聊天区域 - 主要区域，占据更多空间 -->
                <div class="chat-panel main-chat">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
                        <h3 style="margin: 0;">💬 聊天消息</h3>
                        <button id="openAdvancedSearch" class="btn-search" title="高级消息搜索" style="
                            background: #007bff;
                            color: white;
                            border: none;
                            padding: 8px 12px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 14px;
                            display: flex;
                            align-items: center;
                            gap: 6px;
                            transition: all 0.2s ease;
                        " onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='#007bff'">
                            <span>🔍</span>
                            <span>搜索</span>
                        </button>
                    </div>
                    <div class="chat-area">
                        <div class="messages-container" id="messagesContainer">
                            <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                                🚀 连接到聊天大厅开始聊天...
                            </div>
                        </div>

                        <div class="input-area">
                            <input type="text" id="messageInput" placeholder="输入消息..." disabled>
                            <button id="sendBtn" disabled>发送</button>
                        </div>
                    </div>
                </div>

                <!-- 在线用户区域 - 中等高度 -->
                <div class="chat-panel users-panel">
                    <h4>👥 在线用户 (<span id="userCount">0</span>)</h4>
                    <div class="online-users" id="onlineUsers">
                        <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                            暂无在线用户
                        </div>
                    </div>
                </div>

                <!-- 原始消息日志区域 - 紧凑布局 -->
                <div class="chat-panel log-panel">
                    <h4>📋 原始消息日志</h4>
                    <div class="messages-container" id="rawMessages" style="font-family: monospace; font-size: 0.8em; max-height: 120px;">
                        <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                            等待消息...
                        </div>
                    </div>
                    <button id="clearLogBtn">清除日志</button>
                </div>
            </div>


        </div>
    </div>

    <!-- 高级搜索模态框 -->
    <div id="advancedSearchModal" class="search-modal mobile-responsive" role="dialog" aria-labelledby="searchTitle" aria-modal="true" style="
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    ">
        <div class="search-container" style="
            background: white;
            border-radius: 12px;
            padding: 24px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        ">
            <!-- 搜索头部 -->
            <div class="search-header" style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 24px;
                padding-bottom: 16px;
                border-bottom: 2px solid #e9ecef;
            ">
                <h2 id="searchTitle" style="
                    font-size: 24px;
                    font-weight: 600;
                    color: #2c3e50;
                    margin: 0;
                ">高级消息搜索</h2>
                <button id="closeSearchModal" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #6c757d;
                    padding: 8px;
                    border-radius: 50%;
                    transition: all 0.2s ease;
                " aria-label="关闭搜索">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <!-- 搜索表单 -->
            <form id="advancedSearchForm" role="search" style="display: grid; gap: 20px;">
                <!-- 搜索关键词 -->
                <div style="display: flex; flex-direction: column; gap: 8px;">
                    <label for="searchKeyword" style="font-weight: 500; color: #495057; font-size: 14px;">搜索关键词</label>
                    <input
                        type="text"
                        id="searchKeyword"
                        placeholder="输入要搜索的消息内容..."
                        maxlength="100"
                        required
                        style="
                            padding: 12px 16px;
                            border: 2px solid #e9ecef;
                            border-radius: 8px;
                            font-size: 14px;
                            transition: border-color 0.2s ease;
                        "
                    >
                </div>

                <!-- 过滤条件行 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                    <!-- 消息类型过滤 -->
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <label for="messageTypeFilter" style="font-weight: 500; color: #495057; font-size: 14px;">消息类型</label>
                        <select id="messageTypeFilter" style="
                            padding: 12px 16px;
                            border: 2px solid #e9ecef;
                            border-radius: 8px;
                            font-size: 14px;
                            transition: border-color 0.2s ease;
                        ">
                            <option value="">全部类型</option>
                            <option value="text">文本消息</option>
                            <option value="image">图片消息</option>
                            <option value="file">文件消息</option>
                            <option value="video">视频消息</option>
                        </select>
                    </div>

                    <!-- 发送者过滤 -->
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <label for="senderFilter" style="font-weight: 500; color: #495057; font-size: 14px;">发送者</label>
                        <input
                            type="text"
                            id="senderFilter"
                            placeholder="输入发送者用户名..."
                            style="
                                padding: 12px 16px;
                                border: 2px solid #e9ecef;
                                border-radius: 8px;
                                font-size: 14px;
                                transition: border-color 0.2s ease;
                            "
                        >
                    </div>
                </div>

                <!-- 时间范围选择 -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <label for="startDatePicker" style="font-weight: 500; color: #495057; font-size: 14px;">开始时间</label>
                        <input
                            type="date"
                            id="startDatePicker"
                            style="
                                padding: 12px 16px;
                                border: 2px solid #e9ecef;
                                border-radius: 8px;
                                font-size: 14px;
                                transition: border-color 0.2s ease;
                            "
                        >
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <label for="endDatePicker" style="font-weight: 500; color: #495057; font-size: 14px;">结束时间</label>
                        <input
                            type="date"
                            id="endDatePicker"
                            style="
                                padding: 12px 16px;
                                border: 2px solid #e9ecef;
                                border-radius: 8px;
                                font-size: 14px;
                                transition: border-color 0.2s ease;
                            "
                        >
                    </div>
                </div>

                <!-- 快捷时间选项 -->
                <div id="quickSearchOptions" style="display: flex; flex-direction: column; gap: 8px;">
                    <label style="font-weight: 500; color: #495057; font-size: 14px;">快捷时间范围</label>
                    <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                        <button type="button" data-quick-range="today" style="
                            padding: 6px 12px;
                            background: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 20px;
                            font-size: 12px;
                            cursor: pointer;
                            transition: all 0.2s ease;
                        ">今天</button>
                        <button type="button" data-quick-range="yesterday" style="
                            padding: 6px 12px;
                            background: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 20px;
                            font-size: 12px;
                            cursor: pointer;
                            transition: all 0.2s ease;
                        ">昨天</button>
                        <button type="button" data-quick-range="week" style="
                            padding: 6px 12px;
                            background: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 20px;
                            font-size: 12px;
                            cursor: pointer;
                            transition: all 0.2s ease;
                        ">本周</button>
                        <button type="button" data-quick-range="month" style="
                            padding: 6px 12px;
                            background: #f8f9fa;
                            border: 1px solid #dee2e6;
                            border-radius: 20px;
                            font-size: 12px;
                            cursor: pointer;
                            transition: all 0.2s ease;
                        ">本月</button>
                    </div>
                </div>

                <!-- 错误提示 -->
                <div id="dateRangeError" style="
                    background: #f8d7da;
                    color: #721c24;
                    padding: 12px 16px;
                    border-radius: 8px;
                    margin-top: 16px;
                    display: none;
                " role="alert">
                    结束日期不能早于开始日期
                </div>

                <!-- 搜索操作按钮 -->
                <div style="
                    display: flex;
                    gap: 12px;
                    justify-content: flex-end;
                    margin-top: 24px;
                    padding-top: 16px;
                    border-top: 1px solid #e9ecef;
                ">
                    <button type="button" id="clearFiltersButton" style="
                        padding: 12px 24px;
                        border: 2px solid #dee2e6;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        background: transparent;
                        color: #6c757d;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    ">
                        <span aria-hidden="true">🗑️</span>
                        清除筛选
                    </button>
                    <button type="submit" id="searchButton" style="
                        padding: 12px 24px;
                        border: none;
                        border-radius: 8px;
                        font-size: 14px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        background: #007bff;
                        color: white;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                    ">
                        <span aria-hidden="true">🔍</span>
                        开始搜索
                    </button>
                </div>
            </form>

            <!-- 加载指示器 -->
            <div id="searchLoadingIndicator" style="
                display: none;
                text-align: center;
                padding: 40px;
            ">
                <div style="
                    width: 40px;
                    height: 40px;
                    border: 4px solid #f3f3f3;
                    border-top: 4px solid #007bff;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 16px;
                "></div>
                <p>正在搜索消息...</p>
            </div>

            <!-- 搜索结果区域 -->
            <div id="searchResults" style="margin-top: 24px; display: none;">
                <!-- 结果头部 -->
                <div id="searchResultsHeader" style="
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 16px;
                    padding: 16px;
                    background: #f8f9fa;
                    border-radius: 8px;
                ">
                    <div id="resultsCount" style="font-weight: 500; color: #495057;">
                        找到 0 条匹配的消息
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <label for="sortOptions" style="font-weight: 500; color: #495057; font-size: 14px;">排序方式:</label>
                        <select id="sortOptions" style="
                            padding: 8px 12px;
                            border: 1px solid #dee2e6;
                            border-radius: 4px;
                            font-size: 14px;
                        ">
                            <option value="date_desc">时间倒序</option>
                            <option value="date_asc">时间正序</option>
                            <option value="relevance">相关性</option>
                        </select>
                    </div>
                </div>

                <!-- 结果列表 -->
                <div id="searchResultsList" style="
                    max-height: 400px;
                    overflow-y: auto;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                ">
                    <!-- 搜索结果将通过JavaScript动态填充 -->
                </div>

                <!-- 分页控件 -->
                <div id="searchPagination" style="
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 8px;
                    margin-top: 16px;
                ">
                    <button id="prevPageButton" style="
                        padding: 8px 12px;
                        border: 1px solid #dee2e6;
                        background: white;
                        border-radius: 4px;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    " disabled>上一页</button>
                    <span id="pageInfo" style="color: #6c757d;">第 1 页，共 1 页</span>
                    <button id="nextPageButton" style="
                        padding: 8px 12px;
                        border: 1px solid #dee2e6;
                        background: white;
                        border-radius: 4px;
                        cursor: pointer;
                        transition: all 0.2s ease;
                    " disabled>下一页</button>
                </div>
            </div>

            <!-- 搜索历史 -->
            <div id="searchHistory" style="display: none;">
                <h3>搜索历史</h3>
                <div id="historyList">
                    <!-- 搜索历史将通过JavaScript动态填充 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 添加旋转动画样式 -->
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .search-modal.active {
            display: flex !important;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .search-container {
                width: 95% !important;
                padding: 16px !important;
                margin: 10px !important;
            }

            .search-container div[style*="grid-template-columns"] {
                grid-template-columns: 1fr !important;
            }
        }
    </style>

    <!-- ES6模块化JavaScript代码 -->
    <script type="module" src="js/app.js"></script>

    <!-- 兼容性检查和回退方案 -->
    <script nomodule>
        alert('您的浏览器不支持ES6模块，请升级到现代浏览器以获得最佳体验。');
    </script>

    <!-- 旧版JavaScript代码（作为备份，将被移除） -->
    <script style="display: none;">
        // 注意：此代码块已被ES6模块化重构替代
        // 保留此处仅作为迁移期间的参考
        // 在确认ES6模块正常工作后将完全移除
        /*
        // ==== 旧版代码已迁移到ES6模块 ====
        // 以下代码已重构为模块化架构，分别位于：
        // - static/js/modules/auth.js - 认证管理
        // - static/js/modules/api.js - API客户端
        // - static/js/modules/websocket.js - WebSocket管理
        // - static/js/modules/tasks.js - 任务管理
        // - static/js/modules/ui.js - UI工具
        // - static/js/app.js - 主应用
        // - static/js/handlers/ - 事件处理器

        // ==== 全局变量和状态 ====
        // 已迁移到 static/js/modules/ 目录下的各个模块

        // 认证相关全局变量 (已迁移到ES6模块，保留用于兼容性)
        // let authToken = null; // 已由 auth.js 模块管理
        // let currentUser = null; // 已由 auth.js 模块管理
        let tasks = []; // 任务列表，仍在HTML中使用
        // let socket = null; // 已由 websocket.js 模块管理

        /*
        const API_BASE_URL = '/api';
        const WS_URL = `ws://${window.location.host}/api/ws`;
        let socket = null;
        let tasks = [];
        let currentFilter = 'all';

        // ==== 认证存储管理函数 ====
        /**
         * 保存认证信息到localStorage
         * @param {string} token - JWT令牌
         * @param {Object} user - 用户信息对象
         */
        function saveAuthToStorage(token, user) {
            try {
                const authData = { token, user, timestamp: Date.now() };
                localStorage.setItem('axum_auth', JSON.stringify(authData));
                console.log('认证信息已保存到localStorage');
            } catch (error) {
                console.warn('无法保存认证信息到localStorage:', error);
            }
        }

        /**
         * 从localStorage加载认证信息
         * @returns {Object|null} 认证数据对象或null（如果不存在或已过期）
         */
        function loadAuthFromStorage() {
            try {
                const authData = localStorage.getItem('axum_auth');
                if (!authData) return null;

                const parsed = JSON.parse(authData);
                if (isTokenExpired(parsed.token)) {
                    clearAuthFromStorage();
                    return null;
                }
                return parsed;
            } catch (error) {
                console.warn('加载认证信息失败:', error);
                clearAuthFromStorage();
                return null;
            }
        }

        /**
         * 清除localStorage中的认证信息
         */
        function clearAuthFromStorage() {
            try {
                localStorage.removeItem('axum_auth');
                console.log('认证信息已从localStorage清除');
            } catch (error) {
                console.warn('清除认证信息失败:', error);
            }
        }

        /**
         * 检查JWT令牌是否已过期
         * @param {string} token - JWT令牌
         * @returns {boolean} true表示已过期，false表示未过期
         */
        function isTokenExpired(token) {
            try {
                // 解析JWT payload（第二部分）
                const payload = JSON.parse(atob(token.split('.')[1]));
                // 检查过期时间（exp字段，单位为秒，需要转换为毫秒）
                return payload.exp * 1000 < Date.now();
            } catch (error) {
                // 解析失败视为过期
                return true;
            }
        }

        // ==== DOM元素验证函数 ====
        function validateRequiredElements() {
            const requiredElements = [
                'loginForm',
                'registerForm',
                'logoutBtn',
                'taskForm',
                'refreshTasksBtn',
                'loginTab',
                'registerTab',
                'wsConnectBtn',
                'wsDisconnectBtn'
            ];

            const missingElements = [];

            for (const elementId of requiredElements) {
                const element = document.getElementById(elementId);
                if (!element) {
                    missingElements.push(elementId);
                    console.error(`缺少必需的DOM元素: #${elementId}`);
                }
            }

            if (missingElements.length > 0) {
                console.error('发现缺少的DOM元素:', missingElements);
                return false;
            }

            console.log('所有必需的DOM元素验证通过');
            return true;
        }

        // ==== 安全的事件绑定函数 ====
        function safeAddEventListener(elementId, eventType, handler) {
            const element = document.getElementById(elementId);
            if (element) {
                element.addEventListener(eventType, handler);
                console.log(`已绑定事件: ${elementId} -> ${eventType}`);
            } else {
                console.error(`无法绑定事件，元素不存在: #${elementId}`);
            }
        }

        // ==== 页面加载时执行 ====
        document.addEventListener('DOMContentLoaded', () => {
            // 验证所有必需的DOM元素
            if (!validateRequiredElements()) {
                console.error('DOM元素验证失败，某些功能可能无法正常工作');
                return;
            }

            // 注意：事件处理器已在 app.js 中绑定，这里不再重复绑定
            // 避免双重事件绑定导致的冲突
            console.log('跳过重复的事件绑定，使用 app.js 中的模块化事件处理器');

            // 认证标签切换事件和WebSocket事件也在 app.js 中处理
            console.log('认证标签和WebSocket事件绑定已在 app.js 中完成');
            // WebSocket消息发送现在通过聊天大厅的sendBtn处理

            // 初始化聊天大厅DOM元素和事件监听器
            initializeChatElements();
            initializeChatHallEventListeners();

            // 尝试从localStorage恢复认证状态
            const savedAuth = loadAuthFromStorage();
            if (savedAuth) {
                // 使用模块函数设置认证状态
                if (window.setAuthState) {
                    window.setAuthState(savedAuth.token, savedAuth.user);
                }
                console.log('已从localStorage恢复登录状态:', savedAuth.user.username);
                updateAuthUI();
                // fetchTasks(); // 已移至ES6模块化系统，避免重复调用

                // 🔄 自动连接WebSocket：用户已认证时自动建立连接
                console.log('🔄 检测到用户已认证，准备自动建立WebSocket连接...');
                // 延迟一小段时间确保ES6模块完全初始化
                setTimeout(() => {
                    const currentToken = window.getAuthToken ? window.getAuthToken() : null;
                    if (currentToken && window.wsManager) {
                        console.log('🚀 开始自动建立WebSocket连接');
                        // 使用ES6模块化的WebSocket管理器
                        window.wsManager.connect();
                    }
                }, 200); // 增加延迟确保模块初始化完成
            } else {
                // 初始化UI为未登录状态
                updateAuthUI();
            }

            // WebSocket状态由ES6模块化系统管理，无需手动初始化
            console.log('WebSocket状态将由ES6模块化系统管理');
        });

        // ==== 简化的API调用函数 ====
        async function apiCall(method, url, data = null) {
            // 获取当前认证令牌
            const currentToken = window.getAuthToken ? window.getAuthToken() : null;

            // 检查令牌是否过期
            if (currentToken && isTokenExpired(currentToken)) {
                console.warn('JWT令牌已过期，自动登出');
                handleLogout();
                showAuthMessage('loginMessage', '登录已过期，请重新登录', 'info');
                throw new Error('登录已过期，请重新登录');
            }

            const options = {
                method,
                headers: { 'Content-Type': 'application/json' }
            };

            if (currentToken) {
                options.headers.Authorization = `Bearer ${currentToken}`;
            }

            if (data) {
                options.body = JSON.stringify(data);
            }

            try {
                const response = await fetch(url, options);
                const result = response.status === 204 ? null : await response.json();

                // 更新API响应显示
                updateApiResponse(`${method} ${url}`, response, result);

                // 处理401未授权响应（可能是令牌在服务端过期）
                if (response.status === 401 && currentToken) {
                    console.warn('收到401响应，可能令牌已在服务端过期');
                    handleLogout();
                    showAuthMessage('loginMessage', '认证失效，请重新登录', 'info');
                    throw new Error('认证失效，请重新登录');
                }

                if (!response.ok) {
                    throw new Error(result?.message || `HTTP ${response.status}`);
                }

                return result;
            } catch (error) {
                updateApiResponse(`${method} ${url}`, null, { error: error.message });
                throw error;
            }
        }

        // ==== 认证相关函数 ====
        // handleLogin 函数已移至 app.js 模块化代码中

        // handleRegister 函数已移至 app.js 模块化代码中
        // 避免重复定义导致的函数覆盖问题

        // handleLogout 函数已移至 app.js 模块化代码中

        function updateAuthUI() {
            const statusElement = document.getElementById('authStatus');
            const userElement = document.getElementById('currentUser');
            const logoutBtn = document.getElementById('logoutBtn');
            const authForms = document.getElementById('authForms');
            const wsConnectBtn = document.getElementById('wsConnectBtn');

            // 使用模块导出的函数获取认证状态
            const isAuth = window.isAuthenticated ? window.isAuthenticated() : false;
            const user = window.getCurrentUser ? window.getCurrentUser() : null;

            if (isAuth && user) {

                if (statusElement) {
                    statusElement.textContent = '已认证';
                    statusElement.className = 'status-badge authenticated';
                }
                if (userElement) {
                    userElement.textContent = `欢迎，${user.username}`;
                }
                if (logoutBtn) logoutBtn.classList.remove('hidden');
                if (authForms) authForms.classList.add('hidden');

                // WebSocket连接现在是自动的，不需要手动控制连接按钮
            } else {

                if (statusElement) {
                    statusElement.textContent = '未认证';
                    statusElement.className = 'status-badge unauthenticated';
                }
                if (userElement) {
                    userElement.textContent = '未登录';
                }
                if (logoutBtn) logoutBtn.classList.add('hidden');
                if (authForms) authForms.classList.remove('hidden');

                // 使用ES6模块化的WebSocket管理器断开连接
                if (window.wsManager) {
                    window.wsManager.disconnect();
                    console.log('用户登出，WebSocket连接已断开');
                }
            }
        }

        function showAuthMessage(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="auth-message ${type}">${message}</div>`;
            setTimeout(() => element.innerHTML = '', 3000);
        }

        // ==== 认证标签切换函数 ====
        function switchAuthTab(tab) {
            const loginTab = document.getElementById('loginTab');
            const registerTab = document.getElementById('registerTab');
            const loginContainer = document.getElementById('loginFormContainer');
            const registerContainer = document.getElementById('registerFormContainer');

            if (tab === 'login') {
                loginTab.classList.add('active');
                registerTab.classList.remove('active');
                loginContainer.classList.remove('hidden');
                registerContainer.classList.add('hidden');
            } else if (tab === 'register') {
                loginTab.classList.remove('active');
                registerTab.classList.add('active');
                loginContainer.classList.add('hidden');
                registerContainer.classList.remove('hidden');
            }
        }

        // ==== 任务管理函数 ====
        async function fetchTasks() {
            // 使用ES6模块化的认证状态检查
            if (!window.isAuthenticated || !window.isAuthenticated()) {
                document.getElementById('taskList').innerHTML = '<li>请先登录以查看任务</li>';
                return;
            }

            try {
                // 使用ES6模块化的API调用，避免重复的认证问题
                if (window.loadTasks) {
                    console.log('使用ES6模块化的loadTasks函数');
                    await window.loadTasks();
                } else {
                    // 降级到旧的API调用
                    console.log('降级到旧的API调用');
                    document.getElementById('taskList').innerHTML = '<li>正在加载...</li>';
                    const result = await apiCall('GET', '/api/tasks');
                    tasks = result?.data || [];
                    renderTasks();
                }
            } catch (error) {
                console.error('fetchTasks失败:', error);
                document.getElementById('taskList').innerHTML = `<li>获取任务失败: ${error.message}</li>`;
            }
        }

        async function handleCreateTask(event) {
            event.preventDefault();

            // 使用ES6模块化的认证状态检查
            if (!window.isAuthenticated || !window.isAuthenticated()) {
                alert('请先登录');
                return;
            }

            const title = document.getElementById('title').value.trim();
            const description = document.getElementById('description').value.trim();
            const completed = document.getElementById('completed').checked;

            if (!title) {
                alert('任务标题不能为空');
                return;
            }

            try {
                await apiCall('POST', '/api/tasks', { title, description, completed });
                document.getElementById('taskForm').reset();
                await fetchTasks();
            } catch (error) {
                alert(`创建任务失败: ${error.message}`);
            }
        }

        // 简化的任务操作函数
        async function viewTask(id) {
            // 使用ES6模块化的认证状态检查
            if (!window.isAuthenticated || !window.isAuthenticated()) return;
            await apiCall('GET', `/api/tasks/${id}`);
        }

        /**
         * 更新任务
         * @param {string} id - 任务ID
         * @param {Object} payload - 更新数据
         * @param {string} [payload.title] - 任务标题
         * @param {string} [payload.description] - 任务描述
         * @param {boolean} [payload.completed] - 完成状态
         */
        async function updateTask(id, payload) {
            // 使用ES6模块化的认证状态检查
            if (!window.isAuthenticated || !window.isAuthenticated()) {
                alert('请先登录');
                return;
            }

            if (!id || !payload) {
                console.error('更新任务失败：缺少必要参数', { id, payload });
                alert('更新任务失败：参数错误');
                return;
            }

            try {
                // 调用后端 PUT API
                const updatedTask = await apiCall('PUT', `/api/tasks/${id}`, payload);

                // 刷新任务列表以显示最新数据
                await fetchTasks();

                return updatedTask;
            } catch (error) {
                console.error(`更新任务 ${id} 失败:`, error);
                alert(`更新任务失败: ${error.message}`);
                throw error;
            }
        }

        async function deleteTask(id) {
            // 使用ES6模块化的认证状态检查
            if (!window.isAuthenticated || !window.isAuthenticated()) return;
            try {
                await apiCall('DELETE', `/api/tasks/${id}`);
                await fetchTasks();
            } catch (error) {
                alert(`删除任务失败: ${error.message}`);
            }
        }

        function confirmDelete(id, title) {
            if (confirm(`确定要删除任务 "${title}" 吗？`)) {
                deleteTask(id);
            }
        }

        const setFilter = (filter) => {
            currentFilter = filter;

            // 更新按钮状态 - 使用现代选择器
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            const activeBtn = document.querySelector(`[data-filter="${filter}"]`);
            if (activeBtn) {
                activeBtn.classList.add('active');
            }

            renderTasks();
        };

        function renderTasks() {
            const tasksList = document.getElementById('taskList');

            // 使用ES6模块化的认证状态检查
            if (!window.isAuthenticated || !window.isAuthenticated()) {
                tasksList.innerHTML = '<li>请先登录以查看任务</li>';
                return;
            }

            if (!tasks.length) {
                tasksList.innerHTML = '<li>没有任务</li>';
                return;
            }

            // 简化的筛选逻辑
            const filteredTasks = tasks.filter(task => {
                if (currentFilter === 'completed') return task.completed;
                if (currentFilter === 'active') return !task.completed;
                return true;
            });

            if (!filteredTasks.length) {
                tasksList.innerHTML = '<li>没有符合条件的任务</li>';
                return;
            }

            tasksList.innerHTML = filteredTasks.map(task => `
                <li class="task-item ${task.completed ? 'completed' : ''}" id="task-${task.id}" data-task-id="${task.id}">
                    <div class="task-content" id="content-${task.id}">
                        <h4 class="task-title">${escapeHtml(task.title)}</h4>
                        <p class="task-description">${task.description ? escapeHtml(task.description) : '(无描述)'}</p>
                        <p>状态: <strong>${task.completed ? '已完成' : '未完成'}</strong></p>
                        <p class="timestamp">创建于: ${formatDate(task.created_at)} | 更新于: ${formatDate(task.updated_at)}</p>
                    </div>
                    <div class="task-edit-form" id="edit-${task.id}" style="display: none;">
                        <input type="text" class="edit-input" id="edit-title-${task.id}" value="${escapeHtml(task.title)}" placeholder="任务标题">
                        <textarea class="edit-input" id="edit-description-${task.id}" placeholder="任务描述（可选）">${task.description ? escapeHtml(task.description) : ''}</textarea>
                        <label>
                            <input type="checkbox" id="edit-completed-${task.id}" ${task.completed ? 'checked' : ''}>
                            已完成
                        </label>
                    </div>
                    <div class="task-controls">
                        <div class="normal-controls" id="normal-controls-${task.id}">
                            <button class="view-btn" data-action="view" data-task-id="${task.id}">查看</button>
                            <button class="edit-btn" data-action="edit" data-task-id="${task.id}">编辑</button>
                            <button class="toggle-btn" data-action="toggle" data-task-id="${task.id}" data-current-status="${task.completed ? 'completed' : 'pending'}">
                                ${task.completed ? '标记为未完成' : '标记为已完成'}
                            </button>
                            <button class="delete-btn" data-action="delete" data-task-id="${task.id}" data-task-title="${escapeHtml(task.title)}">删除</button>
                        </div>
                        <div class="edit-controls" id="edit-controls-${task.id}" style="display: none;">
                            <button class="save-btn" data-action="save" data-task-id="${task.id}">保存更改</button>
                            <button class="cancel-btn" data-action="cancel" data-task-id="${task.id}">取消</button>
                        </div>
                    </div>
                </li>
            `).join('');
        }

        // ==== 完整的编辑功能实现 ====

        /**
         * 切换任务的编辑模式
         * @param {string} id - 任务ID
         */
        function toggleEditMode(id) {
            const taskItem = document.getElementById(`task-${id}`);
            const contentDiv = document.getElementById(`content-${id}`);
            const editForm = document.getElementById(`edit-${id}`);
            const normalControls = document.getElementById(`normal-controls-${id}`);
            const editControls = document.getElementById(`edit-controls-${id}`);

            if (!taskItem || !contentDiv || !editForm || !normalControls || !editControls) {
                console.error('编辑模式切换失败：找不到必要的DOM元素', id);
                return;
            }

            // 切换到编辑模式
            taskItem.classList.add('editing');
            contentDiv.style.display = 'none';
            editForm.style.display = 'block';
            normalControls.style.display = 'none';
            editControls.style.display = 'flex';

            // 聚焦到标题输入框
            const titleInput = document.getElementById(`edit-title-${id}`);
            if (titleInput) {
                titleInput.focus();
                titleInput.select();
            }


        }

        /**
         * 取消编辑模式，恢复到查看模式
         * @param {string} id - 任务ID
         */
        function cancelEdit(id) {
            const taskItem = document.getElementById(`task-${id}`);
            const contentDiv = document.getElementById(`content-${id}`);
            const editForm = document.getElementById(`edit-${id}`);
            const normalControls = document.getElementById(`normal-controls-${id}`);
            const editControls = document.getElementById(`edit-controls-${id}`);

            if (!taskItem || !contentDiv || !editForm || !normalControls || !editControls) {
                console.error('取消编辑失败：找不到必要的DOM元素', id);
                return;
            }

            // 恢复原始数据（从当前任务数据重新填充表单）
            const task = tasks.find(t => t.id === id);
            if (task) {
                const titleInput = document.getElementById(`edit-title-${id}`);
                const descriptionInput = document.getElementById(`edit-description-${id}`);
                const completedInput = document.getElementById(`edit-completed-${id}`);

                if (titleInput) titleInput.value = task.title || '';
                if (descriptionInput) descriptionInput.value = task.description || '';
                if (completedInput) completedInput.checked = task.completed || false;
            }

            // 切换回查看模式
            taskItem.classList.remove('editing');
            contentDiv.style.display = 'block';
            editForm.style.display = 'none';
            normalControls.style.display = 'flex';
            editControls.style.display = 'none';


        }

        /**
         * 保存任务编辑
         * @param {string} id - 任务ID
         */
        async function handleSaveTask(id) {
            const titleInput = document.getElementById(`edit-title-${id}`);
            const descriptionInput = document.getElementById(`edit-description-${id}`);
            const completedInput = document.getElementById(`edit-completed-${id}`);

            if (!titleInput || !descriptionInput || !completedInput) {
                console.error('保存任务失败：找不到输入元素', id);
                alert('保存失败：找不到输入元素');
                return;
            }

            // 获取编辑后的数据
            const title = titleInput.value.trim();
            const description = descriptionInput.value.trim();
            const completed = completedInput.checked;

            // 验证必填字段
            if (!title) {
                alert('任务标题不能为空');
                titleInput.focus();
                return;
            }

            // 构建更新数据
            const updateData = {
                title: title,
                description: description || null,
                completed: completed
            };

            try {
                // 调用更新API
                await updateTask(id, updateData);

                // 成功后退出编辑模式
                cancelEdit(id);
            } catch (error) {
                console.error(`保存任务 ${id} 失败:`, error);
                alert(`保存失败: ${error.message}`);
            }
        }


        // ==== 工具函数 ====
        function updateApiResponse(action, response, data) {
            const responseArea = document.getElementById('apiResponse');
            const timestamp = new Date().toLocaleTimeString();
            const statusText = response ? `${response.status} ${response.statusText}` : 'Error';
            const content = JSON.stringify(data, null, 2);

            responseArea.innerHTML = `[${timestamp}] ${action} - ${statusText}\n${content}`;
            responseArea.scrollTop = responseArea.scrollHeight;
        }

        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';
            try {
                return new Date(timestamp).toLocaleString();
            } catch {
                return '无效日期';
            }
        }

        // ==== 简化的WebSocket函数 ====
        // 添加连接状态锁定机制，防止频繁点击导致的问题
        let isConnecting = false;
        let isDisconnecting = false;
        let connectionTimeout = null; // 连接超时定时器

        // 🔄 自动重连机制相关变量
        let isReconnecting = false; // 重连状态标志
        let reconnectAttempts = 0; // 当前重连尝试次数
        let maxReconnectAttempts = 5; // 最大重连尝试次数
        let reconnectDelay = 1000; // 初始重连延迟（毫秒）
        let maxReconnectDelay = 30000; // 最大重连延迟（30秒）
        let reconnectTimer = null; // 重连定时器
        let userDisconnected = false; // 用户主动断开标志

        /**
         * 🔄 自动重连函数
         *
         * 实现指数退避算法的自动重连机制，在连接意外断开时自动尝试重新连接。
         * 包含最大重试次数限制和重连状态显示。
         */
        function attemptReconnect() {
            // 如果用户主动断开连接，不进行自动重连
            if (userDisconnected) {
                console.log('🚫 用户主动断开连接，跳过自动重连');
                return;
            }

            // 如果没有认证信息，不进行重连
            if (!window.isAuthenticated || !window.isAuthenticated()) {
                console.log('🚫 没有认证信息，跳过自动重连');
                return;
            }

            // 如果已经在重连过程中，避免重复重连
            if (isReconnecting) {
                console.log('🔄 重连已在进行中，跳过重复重连');
                return;
            }

            // 检查重连次数限制
            if (reconnectAttempts >= maxReconnectAttempts) {
                console.log(`❌ 已达到最大重连次数 (${maxReconnectAttempts})，停止重连`);
                logWebSocketMessage(`连接失败，已尝试 ${maxReconnectAttempts} 次重连`);
                setWebSocketConnectionState(false, '重连失败，已达到最大尝试次数');
                return;
            }

            isReconnecting = true;
            reconnectAttempts++;

            // 计算当前重连延迟（指数退避算法）
            const currentDelay = Math.min(reconnectDelay * Math.pow(2, reconnectAttempts - 1), maxReconnectDelay);

            console.log(`🔄 准备第 ${reconnectAttempts} 次重连，延迟 ${currentDelay}ms`);
            logWebSocketMessage(`连接断开，${currentDelay/1000}秒后尝试第 ${reconnectAttempts} 次重连...`);

            // 更新连接状态显示重连信息
            if (chatElements && chatElements.connectionStatus) {
                chatElements.connectionStatus.textContent = `重连中... (${reconnectAttempts}/${maxReconnectAttempts})`;
                chatElements.connectionStatus.className = 'connection-status reconnecting';
            }

            // 设置重连定时器
            reconnectTimer = setTimeout(() => {
                console.log(`🚀 开始第 ${reconnectAttempts} 次重连尝试`);
                isReconnecting = false;
                connectWebSocket();
            }, currentDelay);
        }

        /**
         * 🔄 重置重连状态
         *
         * 在连接成功或用户主动断开时重置重连相关的状态变量
         */
        function resetReconnectState() {
            isReconnecting = false;
            reconnectAttempts = 0;
            if (reconnectTimer) {
                clearTimeout(reconnectTimer);
                reconnectTimer = null;
            }
        }

        /**
         * 建立WebSocket连接
         *
         * 这个函数负责创建和管理WebSocket连接，包含完整的状态验证、
         * 错误处理和超时保护机制。连接过程包括：
         * 1. 前置条件检查（登录状态、连接状态等）
         * 2. 创建WebSocket实例并配置事件处理器
         * 3. 设置连接超时保护
         * 4. 处理连接成功/失败的各种情况
         */
        function connectWebSocket() {
            // 前置条件1：检查用户是否已登录
            if (!window.isAuthenticated || !window.isAuthenticated()) {
                console.log('错误: 请先登录后再连接WebSocket');
                return;
            }

            // 前置条件2：防止在断开过程中连接，避免状态冲突
            if (isDisconnecting) {
                logWebSocketMessage('正在断开连接中，请稍候...');
                return;
            }

            // 前置条件3：防止重复连接或在连接过程中再次点击
            if (isConnecting) {
                logWebSocketMessage('正在连接中，请稍候...');
                return;
            }

            // 🔧 修复：强制清理任何现有连接，确保干净的连接状态
            if (socket !== null) {
                logWebSocketMessage('🧹 检测到现有连接，正在清理...');
                try {
                    if (socket.readyState === WebSocket.OPEN || socket.readyState === WebSocket.CONNECTING) {
                        socket.close(1000, '准备重新连接');
                    }
                } catch (e) {
                    console.warn('清理现有连接时出错:', e);
                }
                socket = null;
                setWebSocketConnectionState(false, '连接清理');
            }

            // 前置条件4：最终状态验证，确保isConnected状态与实际情况一致
            if (isConnected) {
                setWebSocketConnectionState(false, '状态重置');
            }

            // 设置连接状态锁
            isConnecting = true;

            // 设置连接超时保护（10秒后自动解锁）
            connectionTimeout = setTimeout(() => {
                if (isConnecting) {
                    console.warn('WebSocket连接超时，自动解锁连接状态');
                    isConnecting = false;
                    if (socket && socket.readyState === WebSocket.CONNECTING) {
                        socket.close();
                        socket = null;
                    }
                    logWebSocketMessage('连接超时，请重试');
                }
            }, 10000);

            try {
                // 在WebSocket URL中添加JWT token作为查询参数
                const currentToken = window.getAuthToken ? window.getAuthToken() : null;
                const wsUrlWithToken = `${WS_URL}?token=${currentToken}`;
                socket = new WebSocket(wsUrlWithToken);

                socket.onopen = () => {
                    // 清除连接状态锁和超时定时器
                    isConnecting = false;
                    if (connectionTimeout) {
                        clearTimeout(connectionTimeout);
                        connectionTimeout = null;
                    }

                    // 🔄 重连成功处理：重置重连状态和用户断开标志
                    const wasReconnecting = reconnectAttempts > 0;
                    resetReconnectState();
                    userDisconnected = false;

                    // 更新连接状态
                    setWebSocketConnectionState(true, 'WebSocket连接成功');

                    // 记录连接成功消息
                    if (wasReconnecting) {
                        logWebSocketMessage('🎉 重连成功！已连接到WebSocket服务器');
                        addRawMessage('🎉 WebSocket 重连成功', 'system');
                    } else {
                        logWebSocketMessage('已连接到WebSocket服务器（已认证）');
                        addRawMessage('WebSocket 连接已建立', 'system');
                    }

                    // 🔄 WebSocket连接成功后自动加载聊天历史消息
                    loadChatHistory();
                };

                socket.onmessage = (event) => {
                    // 统一使用聊天大厅消息处理函数，避免重复处理
                    handleChatHallWebSocketMessage(event.data);
                };

                socket.onclose = (event) => {
                    console.log('🔌 WebSocket onclose事件触发，事件代码:', event.code, '原因:', event.reason);

                    // 清除连接状态锁和超时定时器（确保可以重新连接）
                    isConnecting = false;
                    if (connectionTimeout) {
                        clearTimeout(connectionTimeout);
                        connectionTimeout = null;
                    }

                    // 如果socket已经被设置为null，说明是用户主动断开，已经处理过了
                    if (socket === null) {
                        console.log('socket已为null，跳过onclose处理');
                        return;
                    }

                    // 🔧 修复：根据关闭代码确定详细原因
                    let closeReason = '连接已关闭';
                    let isExpectedClose = false;

                    if (event.code === 1000) {
                        closeReason = '正常关闭';
                        isExpectedClose = true;
                    } else if (event.code === 1001) {
                        closeReason = '页面离开';
                        isExpectedClose = true;
                    } else if (event.code === 1006) {
                        closeReason = '连接异常断开（可能是认证失败或网络问题）';
                    } else if (event.code === 1011) {
                        closeReason = '服务器错误';
                    } else if (event.code === 1015) {
                        closeReason = 'TLS握手失败';
                    } else {
                        closeReason = `连接关闭 (代码: ${event.code})`;
                    }

                    // 添加详细的连接状态日志
                    console.log(`📊 连接关闭详情: 代码=${event.code}, 原因="${event.reason}", 预期关闭=${isExpectedClose}`);

                    // 更新连接状态
                    setWebSocketConnectionState(false, closeReason);
                    logWebSocketMessage(`WebSocket连接已关闭: ${closeReason}`);
                    addRawMessage(`WebSocket 连接已关闭: ${closeReason}`, isExpectedClose ? 'system' : 'error');

                    socket = null;
                    console.log('🧹 socket设置为null，连接状态已清理');

                    // 🔄 自动重连逻辑：只有在非预期关闭且非用户主动断开时才重连
                    if (!isExpectedClose && !userDisconnected) {
                        console.log('🔄 检测到意外断开，准备自动重连...');
                        attemptReconnect();
                    } else if (userDisconnected) {
                        console.log('🚫 用户主动断开，不进行自动重连');
                    } else {
                        console.log('✅ 正常关闭，不需要重连');
                    }
                };

                socket.onerror = (error) => {
                    console.error('WebSocket 错误:', error);
                    logWebSocketMessage(`WebSocket错误: ${error}`);
                    addRawMessage('WebSocket 连接错误', 'error');

                    // 清除连接状态锁和超时定时器
                    isConnecting = false;
                    if (connectionTimeout) {
                        clearTimeout(connectionTimeout);
                        connectionTimeout = null;
                    }

                    // 更新连接状态
                    setWebSocketConnectionState(false, 'WebSocket连接错误');
                };
            } catch (error) {
                // 清除连接状态锁和超时定时器
                isConnecting = false;
                if (connectionTimeout) {
                    clearTimeout(connectionTimeout);
                    connectionTimeout = null;
                }
                logWebSocketMessage(`连接失败: ${error.message}`);
            }
        }

        /**
         * 断开WebSocket连接
         *
         * 安全地关闭WebSocket连接并清理相关资源。包含以下步骤：
         * 1. 状态检查，防止重复操作
         * 2. 清理超时定时器
         * 3. 安全关闭WebSocket连接
         * 4. 更新UI状态
         * 5. 重置所有相关状态变量
         *
         * 使用try-finally确保即使出现异常也能正确清理资源。
         */
        function disconnectWebSocket() {
            // 防止重复断开或在断开过程中再次点击
            if (isDisconnecting || socket === null) {
                return;
            }

            // 设置断开状态锁和用户主动断开标志
            isDisconnecting = true;
            userDisconnected = true; // 🔄 标记为用户主动断开，阻止自动重连

            try {
                // 🔄 清除重连相关的定时器和状态
                resetReconnectState();

                // 清除可能存在的连接超时定时器
                if (connectionTimeout) {
                    clearTimeout(connectionTimeout);
                    connectionTimeout = null;
                }

                // 检查WebSocket状态并安全关闭
                if (socket && socket.readyState !== WebSocket.CLOSED && socket.readyState !== WebSocket.CLOSING) {
                    socket.close(1000, '用户主动断开'); // 使用正常关闭代码
                }

                // 立即更新UI状态，不等待onclose事件
                setWebSocketConnectionState(false, '用户主动断开连接');

                // 记录断开消息
                logWebSocketMessage('WebSocket连接已断开');
                addRawMessage('WebSocket连接已断开', 'system');

            } catch (error) {
                console.error('断开WebSocket连接时发生错误:', error);
                logWebSocketMessage(`断开连接时发生错误: ${error.message}`);
            } finally {
                // 确保资源清理和状态重置
                socket = null;
                isDisconnecting = false;
                isConnecting = false; // 确保连接状态也被重置
            }
        }

        function sendWebSocketMessage(event) {
            event.preventDefault();

            // 使用ES6模块化的WebSocket管理器
            if (!window.wsManager || window.wsManager.getState() !== 1) { // 1 = OPEN
                console.log('未连接到WebSocket服务器');
                return;
            }

            // 获取聊天大厅的消息输入框
            const messageInput = document.getElementById('messageInput');
            if (!messageInput) {
                console.log('错误: 找不到消息输入框');
                return;
            }

            const message = messageInput.value.trim();
            if (!message) return;

            // 使用ES6模块化的WebSocket管理器发送聊天消息
            if (window.wsManager) {
                window.wsManager.sendChatMessage(message);
                messageInput.value = '';
            }
        }



        function logWebSocketMessage(message) {
            // 使用聊天大厅的原始消息日志区域
            const messagesElem = document.getElementById('rawMessages');
            if (messagesElem) {
                const timestamp = new Date().toLocaleTimeString();
                const messageElem = document.createElement('div');
                messageElem.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
                messagesElem.appendChild(messageElem);
                messagesElem.scrollTop = messagesElem.scrollHeight;
            }
        }

        /**
         * 显示聊天消息（格式化显示）- 修复消息显示逻辑
         * @param {Object} message - 聊天消息对象
         */
        function displayChatMessage(message) {
            // 修复：使用正确的聊天消息容器元素
            const messagesElem = document.getElementById('messagesContainer');
            if (!messagesElem) {
                console.error('错误: 找不到聊天消息容器 #messagesContainer');
                return;
            }

            const messageElem = document.createElement('div');
            messageElem.className = 'message'; // 使用现有的CSS类名

            // 根据消息类型设置样式
            if (message.is_own_message) {
                messageElem.classList.add('own');
            } else if (message.message_type === 'System') {
                messageElem.classList.add('system');
            } else if (message.message_type === 'UserJoined') {
                messageElem.classList.add('user-joined');
            } else if (message.message_type === 'UserLeft') {
                messageElem.classList.add('user-left');
            } else {
                messageElem.classList.add('text');
            }

            // 格式化时间戳
            const timestamp = new Date(message.timestamp).toLocaleTimeString();

            // 构建消息内容
            let messageContent = '';
            if (message.message_type === 'System' || message.message_type === 'UserJoined' || message.message_type === 'UserLeft') {
                messageContent = `
                    <div class="message-header">[${timestamp}]</div>
                    <div class="message-content">${message.content}</div>
                `;
            } else {
                const senderName = message.sender ? message.sender.username : '未知用户';
                const ownLabel = message.is_own_message ? ' (我)' : '';
                messageContent = `
                    <div class="message-header">[${timestamp}] ${senderName}${ownLabel}</div>
                    <div class="message-content">${message.content}</div>
                `;
            }

            messageElem.innerHTML = messageContent;
            messagesElem.appendChild(messageElem);
            messagesElem.scrollTop = messagesElem.scrollHeight;
        }

        // ===== 聊天大厅功能 =====

        // 聊天大厅状态变量
        let isConnected = false;

        /**
         * 统一的WebSocket连接状态更新函数
         *
         * 这是WebSocket连接状态管理的唯一入口点，确保状态变化的一致性和可追踪性。
         * 所有需要修改isConnected状态的地方都应该通过此函数进行，避免直接修改全局变量。
         *
         * @param {boolean} connected - 新的连接状态（true=已连接，false=未连接）
         * @param {string} reason - 状态变化的原因，用于日志记录和调试
         */
        function setWebSocketConnectionState(connected, reason = '') {
            const previousState = isConnected;
            isConnected = connected;

            // 只有状态真正发生变化时才更新UI和记录日志，避免不必要的DOM操作
            if (previousState !== connected) {
                console.log(`WebSocket连接状态变化: ${previousState} -> ${connected}${reason ? ` (${reason})` : ''}`);
                updateChatHallConnectionUI();
            }
        }

        // 聊天大厅 DOM 元素（延迟初始化）
        let chatElements = null;

        /**
         * 初始化聊天大厅DOM元素缓存对象
         *
         * 为了提高性能和避免重复的DOM查询，将所有需要频繁访问的DOM元素
         * 预先获取并缓存在chatElements对象中。这样可以：
         * 1. 减少DOM查询次数，提高性能
         * 2. 统一DOM元素管理，便于维护
         * 3. 避免因元素不存在导致的错误
         */
        function initializeChatElements() {
            chatElements = {
                // 连接状态显示元素
                connectionStatus: document.getElementById('connectionStatus'),

                // 聊天相关元素
                messagesContainer: document.getElementById('messagesContainer'),
                messageInput: document.getElementById('messageInput'),
                sendBtn: document.getElementById('sendBtn'),

                // 在线用户相关元素
                onlineUsers: document.getElementById('onlineUsers'),
                userCount: document.getElementById('userCount'),

                // 原始消息日志相关元素
                rawMessages: document.getElementById('rawMessages'),
                clearLogBtn: document.getElementById('clearLogBtn'),

                // 功能按钮
                pingBtn: document.getElementById('pingBtn'),
                getUsersBtn: document.getElementById('getUsersBtn'),

                // WebSocket连接控制按钮
                wsConnectBtn: document.getElementById('wsConnectBtn'),
                wsDisconnectBtn: document.getElementById('wsDisconnectBtn')
            };
        }

        /**
         * 初始化聊天大厅事件监听器
         */
        function initializeChatHallEventListeners() {
            // 聊天大厅按钮事件
            if (chatElements.pingBtn) {
                chatElements.pingBtn.addEventListener('click', sendPing);
            }
            if (chatElements.getUsersBtn) {
                chatElements.getUsersBtn.addEventListener('click', getOnlineUsers);
            }
            if (chatElements.sendBtn) {
                chatElements.sendBtn.addEventListener('click', sendChatMessage);
            }
            if (chatElements.clearLogBtn) {
                chatElements.clearLogBtn.addEventListener('click', clearRawMessages);
            }

            // 回车发送消息
            if (chatElements.messageInput) {
                chatElements.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendChatMessage();
                    }
                });
            }
        }

        /**
         * 更新聊天大厅连接状态UI（统一的UI更新函数）
         *
         * 这是UI状态更新的唯一入口点，根据当前的WebSocket连接状态
         * 统一更新所有相关的UI元素，包括：
         * - 连接状态显示
         * - 连接/断开按钮的启用状态
         * - 聊天功能按钮的启用状态
         * - 在线用户列表的显示
         *
         * 使用缓存的DOM元素对象提高性能，避免重复的DOM查询。
         */
        function updateChatHallConnectionUI() {
            // 确保chatElements已初始化，延迟初始化模式
            if (!chatElements) {
                initializeChatElements();
            }

            if (isConnected) {
                // 已连接状态：更新状态显示为绿色"已连接"
                if (chatElements && chatElements.connectionStatus) {
                    chatElements.connectionStatus.textContent = '已连接';
                    chatElements.connectionStatus.className = 'connection-status connected';
                }

                // 已连接状态：启用断开按钮（连接按钮已隐藏，无需控制）
                if (chatElements && chatElements.wsDisconnectBtn) chatElements.wsDisconnectBtn.disabled = false;

                // 已连接状态：启用所有聊天功能按钮
                if (chatElements && chatElements.pingBtn) chatElements.pingBtn.disabled = false;
                if (chatElements && chatElements.getUsersBtn) chatElements.getUsersBtn.disabled = false;
                if (chatElements && chatElements.messageInput) chatElements.messageInput.disabled = false;
                if (chatElements && chatElements.sendBtn) chatElements.sendBtn.disabled = false;

                // 已连接状态：清空消息容器的提示文本，准备显示实际消息
                if (chatElements && chatElements.messagesContainer) {
                    chatElements.messagesContainer.innerHTML = '';
                }
            } else {
                // 未连接状态：更新状态显示为红色"未连接"
                if (chatElements && chatElements.connectionStatus) {
                    chatElements.connectionStatus.textContent = '未连接';
                    chatElements.connectionStatus.className = 'connection-status disconnected';
                }

                // 未连接状态：禁用断开按钮（连接按钮已隐藏，无需控制）
                if (chatElements && chatElements.wsDisconnectBtn) chatElements.wsDisconnectBtn.disabled = true;

                // 未连接状态：禁用所有聊天功能按钮
                if (chatElements && chatElements.pingBtn) chatElements.pingBtn.disabled = true;
                if (chatElements && chatElements.getUsersBtn) chatElements.getUsersBtn.disabled = true;
                if (chatElements && chatElements.messageInput) chatElements.messageInput.disabled = true;
                if (chatElements && chatElements.sendBtn) chatElements.sendBtn.disabled = true;

                // 未连接状态：清空在线用户列表
                updateOnlineUsers([]);
            }
        }

        /**
         * 处理聊天大厅 WebSocket 消息 - 优化消息处理逻辑
         * @param {string} data - 接收到的消息数据
         */
        function handleChatHallWebSocketMessage(data) {
            try {
                const message = JSON.parse(data);
                console.log('收到WebSocket消息:', message);

                // 🔧 修复：过滤心跳消息，不添加到原始消息日志
                if (message.type === 'ping' || message.type === 'pong' ||
                    message.message_type === 'Ping' || message.message_type === 'Pong') {
                    console.log(`心跳消息已过滤，不显示在日志中: ${message.type || message.message_type}`);
                    return; // 直接返回，不处理心跳消息
                }

                // 添加到原始消息日志（非心跳消息）
                addRawMessage(`收到: ${data}`, 'received');

                // 🔧 方案一修复：使用 chatAPI.displayMessage 显示聊天消息
                if (message.message_type === 'Text' && message.content && typeof message.content === 'string') {
                    // 检查是否是自己发送的消息（避免重复显示）
                    const isOwnMessage = message.sender && currentUser &&
                                       message.sender.username === currentUser.username;

                    // 显示所有文本消息（包括自己的和他人的）
                    if (window.chatAPI && window.chatAPI.displayMessage) {
                        window.chatAPI.displayMessage(message);
                        console.log('✅ 消息已通过 chatAPI.displayMessage 显示');
                    } else {
                        console.error('❌ chatAPI.displayMessage 方法不存在');
                        // 降级到原有的显示方法
                        displayChatHallMessage(message);
                    }
                }

                // 处理特殊消息类型
                if (message.message_type === 'OnlineUsersList') {
                    const usersData = JSON.parse(message.content);
                    updateOnlineUsers(usersData.users);
                } else if (message.message_type === 'Pong' || message.message_type === 'Ping') {
                    // 🔧 修复：心跳消息只记录日志，不显示在聊天窗口
                    console.log(`心跳消息已过滤: ${message.message_type}`, message);
                    // 不调用 displayChatHallMessage，避免心跳消息显示在聊天界面
                }
            } catch (error) {
                console.error('解析WebSocket消息失败:', error);
                // 如果不是JSON格式，当作普通文本消息显示
                const fallbackMessage = {
                    message_type: 'Text',
                    content: data,
                    timestamp: new Date().toISOString(),
                    sender: { username: '系统' }
                };

                if (window.chatAPI && window.chatAPI.displayMessage) {
                    window.chatAPI.displayMessage(fallbackMessage);
                } else {
                    displayChatHallMessage(fallbackMessage);
                }
            }
        }

        /**
         * 在聊天大厅显示消息
         * @param {Object} message - 聊天消息对象
         */
        function displayChatHallMessage(message) {
            if (!chatElements.messagesContainer) return;

            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';

            // 根据消息类型设置样式
            switch (message.message_type) {
                case 'System':
                    messageDiv.classList.add('system');
                    break;
                case 'UserJoined':
                    messageDiv.classList.add('user-joined');
                    break;
                case 'UserLeft':
                    messageDiv.classList.add('user-left');
                    break;
                case 'Text':
                    messageDiv.classList.add('text');
                    // 如果是自己发送的消息，添加特殊样式
                    if (message.sender && currentUser && message.sender.username === currentUser.username) {
                        messageDiv.classList.add('own');
                    }
                    break;
                case 'Pong':
                    messageDiv.classList.add('system');
                    break;
                default:
                    messageDiv.classList.add('text');
            }

            // 构建消息内容
            let headerText = '';
            if (message.sender) {
                headerText = `${message.sender.username} - ${formatTimestamp(message.timestamp)}`;
            } else {
                headerText = formatTimestamp(message.timestamp);
            }

            messageDiv.innerHTML = `
                <div class="message-header">${headerText}</div>
                <div class="message-content">${escapeHtml(message.content)}</div>
            `;

            chatElements.messagesContainer.appendChild(messageDiv);
            chatElements.messagesContainer.scrollTop = chatElements.messagesContainer.scrollHeight;
        }

        /**
         * 发送聊天消息 - 统一使用sendWebSocketMessage函数
         */
        function sendChatMessage() {
            // 创建一个模拟的event对象，因为sendWebSocketMessage期望一个event参数
            const mockEvent = { preventDefault: () => {} };
            sendWebSocketMessage(mockEvent);
        }

        /**
         * 🔄 获取聊天历史消息
         *
         * 从后端API获取全局聊天室的历史消息
         * @param {string} roomId - 聊天室ID，默认为'global'
         * @param {number} limit - 限制返回的消息数量，默认50条
         * @returns {Promise<Array>} 聊天历史消息数组
         */
        async function fetchChatHistory(roomId = 'global', limit = 50) {
            try {
                // 使用固定的全局聊天室UUID（与后端保持一致）
                const globalRoomId = '00000000-0000-0000-0000-000000000000';
                const url = `/api/messages/chat-room/${globalRoomId}?limit=${limit}`;

                console.log(`🔄 开始获取聊天历史: ${url}`);
                logWebSocketMessage(`正在加载聊天历史消息...`);

                const result = await apiCall('GET', url);

                if (result && result.success && result.data && result.data.messages) {
                    const messages = result.data.messages;
                    console.log(`✅ 成功获取 ${messages.length} 条历史消息`);
                    logWebSocketMessage(`成功加载 ${messages.length} 条历史消息`);
                    return messages;
                } else {
                    console.warn('⚠️ 获取聊天历史返回数据格式异常:', result);
                    logWebSocketMessage('获取聊天历史数据格式异常');
                    return [];
                }
            } catch (error) {
                console.error('❌ 获取聊天历史失败:', error);
                logWebSocketMessage(`获取聊天历史失败: ${error.message}`);
                return [];
            }
        }

        /**
         * 🔄 加载并显示聊天历史消息
         *
         * 在WebSocket连接成功后自动调用，加载历史消息并显示在聊天窗口中
         */
        async function loadChatHistory() {
            try {
                // 检查用户是否已认证
                const currentUser = window.getCurrentUser ? window.getCurrentUser() : null;
                if (!window.isAuthenticated || !window.isAuthenticated() || !currentUser) {
                    console.log('⚠️ 用户未认证，跳过加载聊天历史');
                    return;
                }

                console.log('🔄 开始加载聊天历史消息...');

                // 获取聊天历史
                const historyMessages = await fetchChatHistory();

                if (historyMessages.length === 0) {
                    console.log('📝 暂无历史消息');
                    addRawMessage('暂无历史消息', 'system');
                    return;
                }

                // 按时间顺序显示历史消息（最早的消息先显示）
                // 后端返回的消息通常是按时间倒序，所以需要反转
                const sortedMessages = historyMessages.sort((a, b) =>
                    new Date(a.created_at) - new Date(b.created_at)
                );

                console.log(`🔄 开始显示 ${sortedMessages.length} 条历史消息`);

                // 在消息容器中添加历史消息分隔线
                addHistoryDivider();

                // 逐条显示历史消息
                sortedMessages.forEach((message, index) => {
                    // 转换后端消息格式为前端显示格式
                    const displayMessage = convertBackendMessageToDisplay(message);
                    displayChatMessage(displayMessage);

                    // 每10条消息输出一次进度
                    if ((index + 1) % 10 === 0) {
                        console.log(`📝 已显示 ${index + 1}/${sortedMessages.length} 条历史消息`);
                    }
                });

                console.log('✅ 聊天历史加载完成');
                addRawMessage(`✅ 已加载 ${sortedMessages.length} 条历史消息`, 'system');

            } catch (error) {
                console.error('❌ 加载聊天历史失败:', error);
                logWebSocketMessage(`加载聊天历史失败: ${error.message}`);
                addRawMessage('❌ 聊天历史加载失败', 'system');
            }
        }

        /**
         * 🔄 转换后端消息格式为前端显示格式
         *
         * 将后端API返回的消息格式转换为前端displayChatMessage函数期望的格式
         * @param {Object} backendMessage - 后端返回的消息对象
         * @returns {Object} 前端显示格式的消息对象
         */
        function convertBackendMessageToDisplay(backendMessage) {
            return {
                message_type: backendMessage.message_type || 'Text',
                content: backendMessage.content,
                sender: {
                    username: backendMessage.sender_username || 'Unknown',
                    user_id: backendMessage.sender_id
                },
                timestamp: backendMessage.created_at,
                // 标记为历史消息，避免与实时消息混淆
                is_history_message: true,
                // 检查是否为当前用户发送的消息
                is_own_message: currentUser && backendMessage.sender_id === currentUser.id
            };
        }

        /**
         * 🔄 在聊天窗口中添加历史消息分隔线
         *
         * 在历史消息和实时消息之间添加视觉分隔线
         */
        function addHistoryDivider() {
            const messagesElem = document.getElementById('messagesContainer');
            if (!messagesElem) {
                console.error('错误: 找不到聊天消息容器 #messagesContainer');
                return;
            }

            // 创建分隔线元素
            const dividerDiv = document.createElement('div');
            dividerDiv.className = 'history-divider';
            dividerDiv.innerHTML = `
                <div class="divider-line"></div>
                <div class="divider-text">历史消息</div>
                <div class="divider-line"></div>
            `;

            // 添加分隔线样式（如果还没有添加）
            if (!document.getElementById('history-divider-styles')) {
                const style = document.createElement('style');
                style.id = 'history-divider-styles';
                style.textContent = `
                    .history-divider {
                        display: flex;
                        align-items: center;
                        margin: 15px 0;
                        opacity: 0.6;
                    }
                    .divider-line {
                        flex: 1;
                        height: 1px;
                        background-color: #ddd;
                    }
                    .divider-text {
                        margin: 0 15px;
                        font-size: 12px;
                        color: #666;
                        white-space: nowrap;
                    }
                `;
                document.head.appendChild(style);
            }

            messagesElem.appendChild(dividerDiv);
        }

        /**
         * 发送心跳消息
         */
        function sendPing() {
            if (!isConnected) return;

            const pingMessage = {
                message_type: 'Ping',
                content: 'ping',
                timestamp: new Date().toISOString()
            };

            sendChatWebSocketMessage(JSON.stringify(pingMessage));
        }

        /**
         * 获取在线用户列表
         */
        function getOnlineUsers() {
            if (!isConnected) return;

            const getUsersMessage = {
                message_type: 'GetOnlineUsers',
                content: '',
                timestamp: new Date().toISOString()
            };

            sendChatWebSocketMessage(JSON.stringify(getUsersMessage));
        }

        /**
         * 发送 WebSocket 消息（聊天大厅专用）
         * @param {string} message - 要发送的消息
         */
        function sendChatWebSocketMessage(message) {
            if (window.wsManager && window.wsManager.getState() === 1) { // 1 = OPEN
                window.wsManager.send(JSON.parse(message));
                console.log(`发送: ${message}`);
            }
        }

        /**
         * 更新在线用户列表
         * @param {Array} users - 在线用户数组
         */
        function updateOnlineUsers(users) {
            if (!chatElements.userCount || !chatElements.onlineUsers) return;

            chatElements.userCount.textContent = users.length;

            if (users.length === 0) {
                chatElements.onlineUsers.innerHTML = `
                    <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                        暂无在线用户
                    </div>
                `;
                return;
            }

            chatElements.onlineUsers.innerHTML = users.map(user => `
                <div class="user-item">
                    <div>
                        <strong>${escapeHtml(user.username)}</strong>
                        <div style="font-size: 0.8em; color: #7f8c8d;">
                            ${formatTimestamp(user.connected_at)}
                        </div>
                    </div>
                    <div style="font-size: 0.8em; color: #3498db;">
                        在线
                    </div>
                </div>
            `).join('');
        }

        /**
         * 添加原始消息到日志
         * @param {string} message - 消息内容
         * @param {string} type - 消息类型 (sent, received, system, error)
         */
        function addRawMessage(message, type) {
            if (!chatElements.rawMessages) return;

            const timestamp = new Date().toLocaleTimeString();
            const messageDiv = document.createElement('div');
            messageDiv.style.marginBottom = '5px';
            messageDiv.style.padding = '4px';
            messageDiv.style.borderRadius = '3px';

            switch (type) {
                case 'sent':
                    messageDiv.style.backgroundColor = '#e8f5e8';
                    messageDiv.style.color = '#2d5a2d';
                    break;
                case 'received':
                    messageDiv.style.backgroundColor = '#e8f4fd';
                    messageDiv.style.color = '#1e4a72';
                    break;
                case 'system':
                    messageDiv.style.backgroundColor = '#fff3cd';
                    messageDiv.style.color = '#856404';
                    break;
                case 'error':
                    messageDiv.style.backgroundColor = '#f8d7da';
                    messageDiv.style.color = '#721c24';
                    break;
                default:
                    messageDiv.style.backgroundColor = '#f8f9fa';
                    messageDiv.style.color = '#495057';
            }

            messageDiv.innerHTML = `<span style="color: #6c757d;">[${timestamp}]</span> ${escapeHtml(message)}`;
            chatElements.rawMessages.appendChild(messageDiv);
            chatElements.rawMessages.scrollTop = chatElements.rawMessages.scrollHeight;
        }

        /**
         * 清除原始消息日志
         */
        function clearRawMessages() {
            if (!chatElements.rawMessages) return;

            chatElements.rawMessages.innerHTML = `
                <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                    等待消息...
                </div>
            `;
        }

        /**
         * 格式化时间戳
         * @param {string} timestamp - ISO 时间戳
         * @returns {string} 格式化后的时间字符串
         */
        function formatTimestamp(timestamp) {
            if (!timestamp) return '';
            try {
                const date = new Date(timestamp);
                return date.toLocaleString('zh-CN');
            } catch (error) {
                return timestamp;
            }
        }

        /**
         * HTML 转义函数
         * @param {string} text - 需要转义的文本
         * @returns {string} 转义后的文本
         */
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // ==== 旧版代码结束 ====
    </script>

    <!-- 搜索功能事件绑定 -->
    <script type="module">
        // 等待DOM加载完成后绑定搜索按钮事件
        document.addEventListener('DOMContentLoaded', function() {
            const searchButton = document.getElementById('openAdvancedSearch');
            if (searchButton) {
                searchButton.addEventListener('click', function() {
                    console.log('搜索按钮被点击，导航到搜索页面');
                    window.location.href = '/search-ui.html';
                });
                console.log('搜索按钮事件已绑定');
            } else {
                console.warn('未找到搜索按钮元素');
            }
        });

        // 暴露API到全局作用域供测试使用
        import('/js/modules/api.js').then(apiModule => {
            window.chatAPI = apiModule.chatAPI;
            window.authAPI = apiModule.authAPI;
            window.taskAPI = apiModule.taskAPI;
            console.log('API模块已暴露到全局作用域');
        });

        // 暴露认证模块到全局作用域
        import('/js/modules/auth.js').then(authModule => {
            window.getAuthToken = authModule.getAuthToken;
            window.isAuthenticated = authModule.isAuthenticated;
            window.getCurrentUser = authModule.getCurrentUser;
            window.login = authModule.login;
            window.logout = authModule.logout;
            console.log('认证模块已暴露到全局作用域');
        }).catch(error => {
            console.error('加载认证模块失败:', error);
        });
    </script>

    <!-- 🚀 性能优化：Service Worker注册 -->
    <script>
        // Service Worker注册和管理
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', async () => {
                try {
                    console.log('🔧 注册Service Worker...');

                    const registration = await navigator.serviceWorker.register('./sw.js', {
                        scope: './'
                    });

                    console.log('✅ Service Worker注册成功:', registration.scope);

                    // 监听Service Worker更新
                    registration.addEventListener('updatefound', () => {
                        const newWorker = registration.installing;
                        console.log('🔄 发现Service Worker更新');

                        newWorker.addEventListener('statechange', () => {
                            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                console.log('🆕 新版本Service Worker已安装，等待激活');

                                // 可以在这里显示更新提示
                                if (confirm('发现新版本，是否立即更新？')) {
                                    newWorker.postMessage({ type: 'SKIP_WAITING' });
                                    window.location.reload();
                                }
                            }
                        });
                    });

                    // 监听Service Worker控制变化
                    navigator.serviceWorker.addEventListener('controllerchange', () => {
                        console.log('🔄 Service Worker控制器已更改');
                        window.location.reload();
                    });

                    // 暴露Service Worker功能到全局作用域
                    window.swRegistration = registration;
                    window.updateServiceWorker = () => {
                        registration.update();
                    };

                } catch (error) {
                    console.error('❌ Service Worker注册失败:', error);
                }
            });
        } else {
            console.warn('⚠️ 浏览器不支持Service Worker');
        }

        // 🚀 性能优化：关键资源预加载
        function preloadCriticalResources() {
            const criticalResources = [
                './js/modules/websocket.js',
                './js/modules/tasks.js',
                './js/modules/ui.js'
            ];

            criticalResources.forEach(resource => {
                const link = document.createElement('link');
                link.rel = 'modulepreload';
                link.href = resource;
                document.head.appendChild(link);
            });
        }

        // 🚀 性能优化：图片懒加载设置
        function setupImageLazyLoading() {
            // 为所有图片添加懒加载属性
            document.querySelectorAll('img').forEach(img => {
                if (!img.hasAttribute('loading')) {
                    img.setAttribute('loading', 'lazy');
                }
            });
        }

        // 🚀 性能优化：网络状态监控
        function setupNetworkMonitoring() {
            if ('connection' in navigator) {
                const connection = navigator.connection;

                function updateConnectionInfo() {
                    console.log(`📶 网络状态: ${connection.effectiveType}, 下行: ${connection.downlink}Mbps`);

                    // 根据网络状况调整性能策略
                    if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                        console.log('🐌 检测到慢速网络，启用节省模式');
                        document.body.classList.add('slow-network');
                    } else {
                        document.body.classList.remove('slow-network');
                    }
                }

                connection.addEventListener('change', updateConnectionInfo);
                updateConnectionInfo();
            }
        }

        // 页面加载完成后执行优化
        window.addEventListener('load', () => {
            preloadCriticalResources();
            setupImageLazyLoading();
            setupNetworkMonitoring();

            // 报告页面加载性能
            if (window.performance && window.performance.timing) {
                const timing = window.performance.timing;
                const loadTime = timing.loadEventEnd - timing.navigationStart;
                console.log(`📊 页面加载时间: ${loadTime}ms`);

                // 发送性能数据到分析服务（如果需要）
                if (window.gtag) {
                    window.gtag('event', 'page_load_time', {
                        event_category: 'Performance',
                        value: loadTime
                    });
                }
            }
        });
    </script>
</body>
</html>