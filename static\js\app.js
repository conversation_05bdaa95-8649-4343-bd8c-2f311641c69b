/**
 * 主应用模块 - Axum企业级任务管理系统
 * 基于ES6模块化设计，遵循Clean Code JavaScript最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

// ==== 模块导入 ====
import {
    initializeAuth,
    onAuthStateChange,
    setAuthState,
    clearAuthState,
    isAuthenticated,
    getCurrentUser
} from './modules/auth.js';

import {
    authAPI,
    taskAPI,
    chatAPI,  // 添加chatAPI导入
    APIError
} from './modules/api.js';

import {
    wsManager,
    MESSAGE_TYPE,
    connect,
    disconnect,
    sendPing,
    onMessage,
    onConnectionStateChange
} from './modules/websocket.js';

import onlineUsersManager, {
    showOnlineUsers,
    hideOnlineUsers,
    toggleOnlineUsers,
    getUserCount,
    onUsersUpdate
} from './modules/onlineUsers.js';

import {
    taskManager,
    TASK_STATUS,
    TASK_PRIORITY,
    onTaskChange
} from './modules/tasks.js';

// ==== 性能优化模块导入 ====
import {
    performanceOptimizer,
    PerformanceMonitor,
    ResourcePreloader,
    LazyLoader,
    CacheManager,
    debounce,
    throttle,
    isSlowConnection,
    preloadRoute
} from './modules/performance-optimizer.js';

import {
    querySelector,
    getElementById,
    createElement,
    addEventListener,
    delegateEvent,
    getFormData,
    resetForm,
    showElement,
    hideElement,
    toggleClass,
    addClass,
    removeClass,
    escapeHtml,
    formatTimestamp,
    scrollToBottom,
    showNotification,
    querySelectorAll
} from './modules/ui.js';

import {
    handleTaskSubmit,
    handleTaskEdit,
    handleTaskSave,
    handleTaskCancel,
    handleTaskDelete,
    handleTaskToggle,
    handleFilterChange,
    loadTasks,
    renderTaskList,
    addTaskToUI,
    updateTaskInUI,
    removeTaskFromUI,
    clearTasksUI
} from './handlers/taskHandlers.js';

import {
    handleSendMessage,
    handleGetUsers,
    handleClearLog,
    displayChatMessage,
    displaySystemMessage,
    displayUserJoined,
    displayUserLeft,
    displayErrorMessage,
    displayRawMessage,
    updateConnectionStatus,
    updateOnlineUsers
} from './handlers/websocketHandlers.js';

// ==== 应用状态 ====
let appState = {
    isInitialized: false,
    currentFilter: 'all',
    isLoading: false
};

// ==== 简化的按钮状态管理器 ====
class SimpleButtonStateManager {
    constructor() {
        this.processingButtons = new Set();
        this.buttonStates = new Map();
    }

    /**
     * 设置按钮为加载状态
     * @param {Element} button - 按钮元素
     * @param {string} loadingText - 加载文本
     * @returns {boolean} 是否成功设置
     */
    setLoading(button, loadingText = '处理中...') {
        if (!button || this.processingButtons.has(button)) {
            return false;
        }

        // 保存原始状态
        if (!this.buttonStates.has(button)) {
            this.buttonStates.set(button, {
                text: button.textContent,
                disabled: button.disabled
            });
        }

        // 设置加载状态
        this.processingButtons.add(button);
        button.disabled = true;
        button.textContent = loadingText;

        return true;
    }

    /**
     * 恢复按钮状态
     * @param {Element} button - 按钮元素
     */
    restore(button) {
        if (!button) return;

        this.processingButtons.delete(button);
        const originalState = this.buttonStates.get(button);

        if (originalState) {
            button.disabled = originalState.disabled;
            button.textContent = originalState.text;
        }
    }

    /**
     * 安全的异步按钮操作
     * @param {Element} button - 按钮元素
     * @param {Function} asyncOperation - 异步操作
     * @param {string} loadingText - 加载文本
     * @returns {Promise} 操作结果
     */
    async safeOperation(button, asyncOperation, loadingText = '处理中...') {
        if (!this.setLoading(button, loadingText)) {
            throw new Error('按钮正在处理中');
        }

        try {
            const result = await asyncOperation();
            this.restore(button);
            return result;
        } catch (error) {
            this.restore(button);
            throw error;
        }
    }
}

// 创建全局实例
const buttonManager = new SimpleButtonStateManager();

/**
 * 设置任务过滤器
 * @param {string} filter - 过滤类型：'all', 'active', 'completed'
 */
const setFilter = (filter) => {
    appState.currentFilter = filter;

    // 更新按钮状态 - 使用现代DOM方法和模板字符串
    querySelectorAll('.filter-btn').forEach(btn => removeClass(btn, 'active'));
    const activeBtn = querySelector(`[data-filter="${filter}"]`);
    if (activeBtn) {
        addClass(activeBtn, 'active');
    }

    // 重新渲染任务列表（如果函数存在）
    if (typeof loadTasks === 'function') {
        loadTasks();
    }
};

// 全局暴露代码已移至 exposeToGlobalScope() 函数中

// ==== DOM元素缓存 ====
let elements = {};

/**
 * 应用初始化
 */
const initializeApp = async () => {
    if (appState.isInitialized) {
        console.log('应用已初始化，跳过重复初始化');
        return;
    }

    try {
        console.log('正在初始化Axum企业级任务管理系统...');

        // 🚀 性能优化：优先初始化性能监控和优化器
        console.log('🚀 初始化性能优化器...');
        await performanceOptimizer.initialize();

        // 立即暴露基本对象到全局作用域（用于调试）
        window.appState = appState;
        console.log('appState 已暴露到全局作用域');

        // 缓存DOM元素
        cacheElements();

        // 初始化认证模块
        initializeAuth();

        // 设置事件监听器
        setupEventListeners();
        
        // 设置认证状态监听
        setupAuthStateListener();
        
        // 设置WebSocket监听
        setupWebSocketListeners();

        // 设置任务变化监听
        setupTaskChangeListeners();

        // 初始化UI状态
        updateUIState();

        // 🔧 修复：延迟同步WebSocket连接状态，确保UI正确显示
        setTimeout(() => {
            syncWebSocketConnectionState();
        }, 300); // 确保所有模块初始化完成后再同步状态
        
        appState.isInitialized = true;
        console.log('应用初始化完成');

        // 暴露函数到全局作用域
        try {
            window.appState = appState;
            window.elements = elements;
            window.wsManager = wsManager; // 暴露WebSocket管理器
            window.isAuthenticated = isAuthenticated; // 暴露认证状态检查函数
            window.getCurrentUser = getCurrentUser; // 暴露当前用户获取函数
            console.log('应用状态已暴露到全局作用域');

            if (typeof handleLogin === 'function') {
                window.handleLogin = handleLogin;
                console.log('handleLogin 已暴露');
            }
            if (typeof handleRegister === 'function') {
                window.handleRegister = handleRegister;
                console.log('handleRegister 已暴露');
            }
            if (typeof handleLogout === 'function') {
                window.handleLogout = handleLogout;
                console.log('handleLogout 已暴露');
            }
        } catch (exposeError) {
            console.error('暴露函数到全局作用域时出错:', exposeError);
        }

        showNotification('系统初始化完成', 'success');
        
    } catch (error) {
        console.error('应用初始化失败:', error);
        showNotification('系统初始化失败，请刷新页面重试', 'error', 0);
    }
};

/**
 * 缓存DOM元素
 */
const cacheElements = () => {
    elements = {
        // 认证相关
        loginForm: getElementById('loginForm'),
        registerForm: getElementById('registerForm'),
        loginTab: getElementById('loginTab'),
        registerTab: getElementById('registerTab'),
        authStatus: getElementById('authStatus'),
        currentUser: getElementById('currentUser'), // 修复：使用正确的元素ID
        logoutBtn: getElementById('logoutBtn'),
        onlineUsersBtn: getElementById('onlineUsersBtn'),
        onlineUsersCount: getElementById('onlineUsersCount'),
        
        // 任务相关
        taskForm: getElementById('taskForm'),
        taskList: getElementById('taskList'),
        filterBtns: querySelector('.filter-buttons'),
        
        // WebSocket相关
        connectBtn: getElementById('connectBtn'),
        disconnectBtn: getElementById('disconnectBtn'),
        pingBtn: getElementById('pingBtn'),
        getUsersBtn: getElementById('getUsersBtn'),
        messageInput: getElementById('messageInput'),
        sendBtn: getElementById('sendBtn'),
        messagesContainer: getElementById('messages'),
        onlineUsers: getElementById('onlineUsers'),
        connectionStatus: getElementById('connectionStatus'),
        rawMessages: getElementById('rawMessages'),
        clearLogBtn: getElementById('clearLogBtn')
    };
    
    console.log('DOM元素缓存完成');
};

/**
 * 初始化按钮原始文本
 */
function initializeButtonTexts() {
    const submitBtns = querySelectorAll('button[type="submit"]');
    submitBtns.forEach(btn => {
        if (!btn.dataset.originalText) {
            btn.dataset.originalText = btn.textContent;
        }
    });
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 初始化按钮原始文本
    initializeButtonTexts();

    // 认证表单事件
    if (elements.loginForm) {
        addEventListener(elements.loginForm, 'submit', handleLogin);
    }

    if (elements.registerForm) {
        addEventListener(elements.registerForm, 'submit', handleRegister);
    }

    if (elements.loginTab) {
        addEventListener(elements.loginTab, 'click', () => switchAuthTab('login'));
    }

    if (elements.registerTab) {
        addEventListener(elements.registerTab, 'click', () => switchAuthTab('register'));
    }

    if (elements.logoutBtn) {
        addEventListener(elements.logoutBtn, 'click', handleLogout);
    }

    if (elements.onlineUsersBtn) {
        addEventListener(elements.onlineUsersBtn, 'click', handleOnlineUsersToggle);
    }
    
    // 任务表单事件
    if (elements.taskForm) {
        addEventListener(elements.taskForm, 'submit', handleTaskSubmit);
    }
    
    // 任务列表委托事件
    if (elements.taskList) {
        delegateEvent(elements.taskList, '.task-item .edit-btn', 'click', handleTaskEdit);
        delegateEvent(elements.taskList, '.task-item .delete-btn', 'click', handleTaskDelete);
        delegateEvent(elements.taskList, '.task-item .save-btn', 'click', handleTaskSave);
        delegateEvent(elements.taskList, '.task-item .cancel-btn', 'click', handleTaskCancel);
        delegateEvent(elements.taskList, '.task-item input[type="checkbox"]', 'change', handleTaskToggle);
    }
    
    // 过滤按钮事件
    if (elements.filterBtns) {
        delegateEvent(elements.filterBtns, 'button', 'click', handleFilterChange);
    }
    
    // WebSocket控制事件
    if (elements.connectBtn) {
        addEventListener(elements.connectBtn, 'click', () => wsManager.connect());
    }
    
    if (elements.disconnectBtn) {
        addEventListener(elements.disconnectBtn, 'click', () => wsManager.disconnect());
    }
    
    if (elements.pingBtn) {
        addEventListener(elements.pingBtn, 'click', () => wsManager.sendPing());
    }
    
    if (elements.getUsersBtn) {
        addEventListener(elements.getUsersBtn, 'click', handleGetUsers);
    }
    
    if (elements.sendBtn) {
        addEventListener(elements.sendBtn, 'click', handleSendMessage);
    }
    
    if (elements.messageInput) {
        addEventListener(elements.messageInput, 'keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage();
            }
        });
    }
    
    if (elements.clearLogBtn) {
        addEventListener(elements.clearLogBtn, 'click', handleClearLog);
    }

    // 现代事件委托 - 过滤器按钮
    const filterButtonsContainer = getElementById('filter-buttons');
    if (filterButtonsContainer) {
        delegateEvent(filterButtonsContainer, 'click', '.filter-btn', (event) => {
            const filter = event.target.dataset.filter;
            if (filter) {
                setFilter(filter);
            }
        });
    }

    console.log('事件监听器设置完成');
}

/**
 * 设置认证状态监听
 */
function setupAuthStateListener() {
    onAuthStateChange((isAuth, user) => {
        console.log('认证状态变化:', isAuth, user);
        updateAuthUI(isAuth, user);
        
        if (isAuth) {
            // 用户已认证，建立WebSocket连接并加载任务数据
            console.log('用户已认证，建立WebSocket连接...');
            connect();
            loadTasks();

            // 延迟请求在线用户列表，确保WebSocket连接已建立
            setTimeout(() => {
                console.log('请求在线用户列表...');
                if (wsManager && wsManager.requestOnlineUsers) {
                    wsManager.requestOnlineUsers();
                }
            }, 1000);
        } else {
            // 用户未认证，断开WebSocket连接并清空任务数据
            console.log('用户未认证，断开WebSocket连接...');
            disconnect();
            clearTasksUI();
        }
    });
}

/**
 * 设置WebSocket监听
 */
function setupWebSocketListeners() {
    // 连接状态变化
    onConnectionStateChange((state) => {
        updateConnectionStatus(state);
    });

    // 聊天消息
    onMessage(MESSAGE_TYPE.CHAT, (message) => {
        displayChatMessage(message);
    });

    // 系统消息
    onMessage(MESSAGE_TYPE.SYSTEM, (message) => {
        displaySystemMessage(message);
    });

    // 用户加入/离开
    onMessage(MESSAGE_TYPE.USER_JOINED, (message) => {
        displayUserJoined(message);
        updateOnlineUsers();
    });

    onMessage(MESSAGE_TYPE.USER_LEFT, (message) => {
        displayUserLeft(message);
        updateOnlineUsers();
    });

    // 错误消息
    onMessage(MESSAGE_TYPE.ERROR, (message) => {
        displayErrorMessage(message);
    });

    // 设置在线用户数量更新回调
    try {
        onUsersUpdate((users, count) => {
            updateOnlineUsersCount(count);
        });
    } catch (error) {
        console.warn('在线用户更新回调设置失败:', error);
        // 延迟设置回调，确保在线用户管理器已完全初始化
        setTimeout(() => {
            try {
                onUsersUpdate((users, count) => {
                    updateOnlineUsersCount(count);
                });
                console.log('在线用户更新回调延迟设置成功');
            } catch (retryError) {
                console.error('在线用户更新回调延迟设置仍然失败:', retryError);
            }
        }, 1000);
    }

    // 所有消息的原始日志
    onMessage('*', (message) => {
        displayRawMessage(message);
    });
}

/**
 * 设置任务变化监听
 */
function setupTaskChangeListeners() {
    onTaskChange((action, data) => {
        console.log('任务变化:', action, data);

        switch (action) {
            case 'fetch':
            case 'filter':
                // 确保清除任何错误状态
                const taskList = getElementById('taskList');
                if (taskList && taskList.innerHTML.includes('认证失败')) {
                    console.log('清除旧的认证失败错误显示');
                }
                renderTaskList(data);
                break;
            case 'create':
                // 修复：从API响应中提取任务数据
                const createdTask = data && data.data ? data.data : data;
                if (createdTask && createdTask.id) {
                    addTaskToUI(createdTask);
                    showNotification('任务创建成功', 'success');
                } else {
                    console.error('任务创建响应数据格式错误:', data);
                    showNotification('任务创建失败：响应数据格式错误', 'error');
                }
                break;
            case 'update':
                // 修复：从API响应中提取任务数据
                const updatedTask = data && data.data ? data.data : data;
                if (updatedTask && updatedTask.id) {
                    updateTaskInUI(updatedTask);
                    showNotification('任务更新成功', 'success');
                } else {
                    console.error('任务更新响应数据格式错误:', data);
                    showNotification('任务更新失败：响应数据格式错误', 'error');
                }
                break;
            case 'delete':
                // 删除操作传入的通常是任务对象本身
                removeTaskFromUI(data);
                showNotification('任务删除成功', 'success');
                break;
        }
    });
}

/**
 * 更新UI状态
 */
function updateUIState() {
    const isAuth = isAuthenticated();
    const user = getCurrentUser();

    updateAuthUI(isAuth, user);
    updateConnectionStatus(wsManager.getState());

    if (isAuth) {
        loadTasks();
    }
}

/**
 * 🔧 修复：同步WebSocket连接状态到UI
 * 解决页面刷新后连接状态显示不一致的问题
 */
function syncWebSocketConnectionState() {
    try {
        if (wsManager && wsManager.isConnected()) {
            console.log('🔧 同步WebSocket连接状态: 已连接');
            updateConnectionStatus('connected');
            // 手动触发连接状态通知，确保所有处理器都能收到
            wsManager.notifyConnectionState('connected');
        } else if (wsManager) {
            const currentState = wsManager.getState();
            console.log('🔧 同步WebSocket连接状态:', currentState);

            // 根据WebSocket状态映射到UI状态
            let uiState = 'disconnected';
            switch (currentState) {
                case WS_STATE.CONNECTING:
                    uiState = 'connecting';
                    break;
                case WS_STATE.OPEN:
                    uiState = 'connected';
                    break;
                case WS_STATE.CLOSING:
                case WS_STATE.CLOSED:
                default:
                    uiState = 'disconnected';
                    break;
            }

            updateConnectionStatus(uiState);
        }
    } catch (error) {
        console.error('同步WebSocket连接状态失败:', error);
    }
}

// ==== 认证事件处理 ====

/**
 * 处理用户登录
 * @param {Event} event - 表单提交事件
 */
async function handleLogin(event) {
    event.preventDefault();

    const formData = getFormData(elements.loginForm);
    const { username, password } = formData;

    if (!username || !password) {
        showNotification('请输入用户名和密码', 'error');
        return;
    }

    // 获取登录按钮
    const loginButton = elements.loginForm.querySelector('button[type="submit"]');

    // 使用安全的按钮操作包装器
    try {
        const response = await buttonManager.safeOperation(
            loginButton,
            () => authAPI.login(username, password),
            '登录中...'
        );

        // 检查响应格式：支持多种响应格式
        let authToken = null;
        let userInfo = null;

        // 方式1：ApiResponse包装器 + access_token
        if (response.success && response.data && response.data.access_token && response.data.user) {
            authToken = response.data.access_token;
            userInfo = response.data.user;
        }
        // 方式2：ApiResponse包装器 + token
        else if (response.success && response.data && response.data.token && response.data.user) {
            authToken = response.data.token;
            userInfo = response.data.user;
        }
        // 方式3：直接格式 + token
        else if (response.token && response.user) {
            authToken = response.token;
            userInfo = response.user;
        }
        // 方式4：直接格式 + access_token
        else if (response.access_token && response.user) {
            authToken = response.access_token;
            userInfo = response.user;
        }
        // 方式5：尝试查找各种可能的token字段
        else {
            const possibleTokenFields = ['access_token', 'token', 'authToken', 'jwt', 'accessToken'];
            for (const field of possibleTokenFields) {
                if (response[field]) {
                    authToken = response[field];
                    userInfo = response.user || response.data?.user;
                    break;
                }
                if (response.data && response.data[field]) {
                    authToken = response.data[field];
                    userInfo = response.data.user || response.user;
                    break;
                }
            }
        }

        if (authToken && userInfo) {
            setAuthState(authToken, userInfo);
            resetForm(elements.loginForm);
            showNotification(`欢迎回来，${userInfo.username}！`, 'success');
        } else {
            console.error('登录响应格式错误:', response);
            throw new Error('登录响应格式错误：未找到有效的token或用户信息');
        }

    } catch (error) {
        console.error('登录失败:', error);
        const message = error instanceof APIError ? error.message : '登录失败，请检查用户名和密码';
        showNotification(message, 'error');

        // 错误已经由safeButtonOperation处理，这里不需要额外处理按钮状态
        throw error; // 重新抛出让safeButtonOperation处理
    }
}

/**
 * 处理用户注册
 * @param {Event} event - 表单提交事件
 */
async function handleRegister(event) {
    event.preventDefault();

    const formData = getFormData(elements.registerForm);
    const { username, password, confirmPassword } = formData;

    if (!username || !password) {
        showNotification('请输入用户名和密码', 'error');
        return;
    }

    if (password !== confirmPassword) {
        showNotification('两次输入的密码不一致', 'error');
        return;
    }

    if (password.length < 6) {
        showNotification('密码长度至少6位', 'error');
        return;
    }

    // 获取注册按钮
    const registerButton = elements.registerForm.querySelector('button[type="submit"]');

    // 使用安全的按钮操作包装器
    try {
        const response = await buttonManager.safeOperation(
            registerButton,
            () => authAPI.register(username, password, confirmPassword),
            '注册中...'
        );

        // 调试：打印完整响应
        console.log('注册API响应:', response);
        console.log('response.success:', response.success);
        console.log('response.data:', response.data);
        console.log('response.data?.access_token:', response.data?.access_token);
        console.log('response.data?.user:', response.data?.user);

        // 检查响应格式：支持多种响应格式
        let authToken = null;
        let userInfo = null;

        // 方式1：ApiResponse包装器 + access_token
        if (response.success && response.data && response.data.access_token && response.data.user) {
            authToken = response.data.access_token;
            userInfo = response.data.user;
            console.log('使用方式1：ApiResponse包装器 + access_token');
        }
        // 方式2：ApiResponse包装器 + token
        else if (response.success && response.data && response.data.token && response.data.user) {
            authToken = response.data.token;
            userInfo = response.data.user;
        }
        // 方式3：直接格式 + token
        else if (response.token && response.user) {
            authToken = response.token;
            userInfo = response.user;
        }
        // 方式4：直接格式 + access_token
        else if (response.access_token && response.user) {
            authToken = response.access_token;
            userInfo = response.user;
        }
        // 方式5：尝试查找各种可能的token字段
        else {
            const possibleTokenFields = ['access_token', 'token', 'authToken', 'jwt', 'accessToken'];
            for (const field of possibleTokenFields) {
                if (response[field]) {
                    authToken = response[field];
                    userInfo = response.user || response.data?.user;
                    break;
                }
                if (response.data && response.data[field]) {
                    authToken = response.data[field];
                    userInfo = response.data.user || response.user;
                    break;
                }
            }
        }

        if (authToken && userInfo) {
            setAuthState(authToken, userInfo);
            resetForm(elements.registerForm);
            showNotification(`注册成功，欢迎 ${userInfo.username}！`, 'success');
        } else {
            console.error('注册响应格式错误:', response);
            throw new Error('注册响应格式错误：未找到有效的token或用户信息');
        }

    } catch (error) {
        console.error('注册失败:', error);
        const message = error instanceof APIError ? error.message : '注册失败，请稍后重试';
        showNotification(message, 'error');

        // 错误已经由safeButtonOperation处理，这里不需要额外处理按钮状态
        throw error; // 重新抛出让safeButtonOperation处理
    }
}

/**
 * 处理在线用户按钮点击
 */
function handleOnlineUsersToggle() {
    console.log('切换在线用户列表显示状态');
    toggleOnlineUsers();
}

/**
 * 更新在线用户数量显示
 * @param {number} count - 在线用户数量
 */
function updateOnlineUsersCount(count) {
    const countElement = elements.onlineUsersCount;
    if (countElement) {
        countElement.textContent = count;
        console.log('在线用户数量已更新:', count);
    }
}

/**
 * 处理用户登出
 */
async function handleLogout() {
    try {
        await authAPI.logout();
    } catch (error) {
        console.warn('登出API调用失败:', error);
    } finally {
        clearAuthState();
        showNotification('已成功登出', 'info');
    }
}

/**
 * 切换认证标签
 * @param {string} tab - 标签类型 ('login' | 'register')
 */
function switchAuthTab(tab) {
    const loginForm = getElementById('loginFormContainer');
    const registerForm = getElementById('registerFormContainer');

    if (tab === 'login') {
        addClass(elements.loginTab, 'active');
        removeClass(elements.registerTab, 'active');
        showElement(loginForm);
        hideElement(registerForm);
    } else {
        addClass(elements.registerTab, 'active');
        removeClass(elements.loginTab, 'active');
        showElement(registerForm);
        hideElement(loginForm);
    }
}

/**
 * 更新认证UI
 * @param {boolean} isAuth - 是否已认证
 * @param {Object} user - 用户信息
 */
function updateAuthUI(isAuth, user) {
    // 检查正确的DOM元素
    if (!elements.authStatus || !elements.currentUser) {
        console.warn('认证UI元素未找到，使用直接DOM访问');

        // 使用直接获取DOM元素作为备用方案
        const authStatus = document.getElementById('authStatus');
        const currentUser = document.getElementById('currentUser');

        if (authStatus && currentUser) {
            updateAuthUIDirectly(isAuth, user, authStatus, currentUser);
        }
        return;
    }

    if (isAuth && user) {
        // 更新认证状态
        elements.authStatus.textContent = '已认证';
        elements.authStatus.className = 'status-badge authenticated';

        // 更新用户信息显示
        elements.currentUser.textContent = `用户: ${escapeHtml(user.username)}`;

        // 隐藏认证表单，显示登出按钮和在线用户按钮
        const authForms = querySelector('.auth-forms');
        if (authForms) hideElement(authForms);
        if (elements.logoutBtn) showElement(elements.logoutBtn);
        if (elements.onlineUsersBtn) showElement(elements.onlineUsersBtn);

    } else {
        // 更新为未认证状态
        elements.authStatus.textContent = '未认证';
        elements.authStatus.className = 'status-badge unauthenticated';
        elements.currentUser.textContent = '未登录';

        // 显示认证表单，隐藏登出按钮和在线用户按钮
        const authForms = querySelector('.auth-forms');
        if (authForms) showElement(authForms);
        if (elements.logoutBtn) hideElement(elements.logoutBtn);
        if (elements.onlineUsersBtn) hideElement(elements.onlineUsersBtn);
    }
}

/**
 * 直接更新认证UI（备用方案）
 * @param {boolean} isAuth - 是否已认证
 * @param {Object} user - 用户信息
 * @param {Element} authStatus - 认证状态元素
 * @param {Element} currentUser - 当前用户元素
 */
function updateAuthUIDirectly(isAuth, user, authStatus, currentUser) {
    if (isAuth && user) {
        // 更新认证状态
        authStatus.textContent = '已认证';
        authStatus.className = 'status-badge authenticated';

        // 更新用户信息显示
        currentUser.textContent = `用户: ${escapeHtml(user.username)}`;

        // 隐藏认证表单，显示登出按钮和在线用户按钮
        const authForms = document.querySelector('.auth-forms');
        const logoutBtn = document.getElementById('logoutBtn');
        const onlineUsersBtn = document.getElementById('onlineUsersBtn');

        if (authForms) hideElement(authForms);
        if (logoutBtn) showElement(logoutBtn);
        if (onlineUsersBtn) showElement(onlineUsersBtn);

    } else {
        // 更新为未认证状态
        authStatus.textContent = '未认证';
        authStatus.className = 'status-badge unauthenticated';
        currentUser.textContent = '未登录';

        // 显示认证表单，隐藏登出按钮和在线用户按钮
        const authForms = document.querySelector('.auth-forms');
        const logoutBtn = document.getElementById('logoutBtn');
        const onlineUsersBtn = document.getElementById('onlineUsersBtn');

        if (authForms) showElement(authForms);
        if (logoutBtn) hideElement(logoutBtn);
        if (onlineUsersBtn) hideElement(onlineUsersBtn);
    }
}

/**
 * 更新加载状态（已弃用，使用buttonStateManager替代）
 * @deprecated 请使用 buttonStateManager 或 safeButtonOperation
 * @param {boolean} isLoading - 是否正在加载
 */
function updateLoadingState(isLoading) {
    console.warn('updateLoadingState 已弃用，请使用 buttonStateManager 或 safeButtonOperation');

    // 为了向后兼容，暂时保留此函数，但建议迁移到新的按钮状态管理器
    const submitBtns = querySelectorAll('button[type="submit"]');
    submitBtns.forEach(btn => {
        if (isLoading) {
            setButtonLoading(btn, '处理中...');
        } else {
            restoreButton(btn);
        }
    });
}

// ==== 页面加载时初始化 ====
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        initializeApp();
        // 初始化完成后暴露函数到全局作用域
        exposeToGlobalScope();
    });
} else {
    initializeApp();
    // 初始化完成后暴露函数到全局作用域
    exposeToGlobalScope();
}

/**
 * 暴露函数到全局作用域
 */
function exposeToGlobalScope() {
    // 将函数暴露到全局作用域以供HTML内联事件使用
    window.setFilter = setFilter;

    // 暴露认证处理函数到全局作用域
    window.handleLogin = handleLogin;
    window.handleRegister = handleRegister;
    window.handleLogout = handleLogout;

    // 暴露应用状态到全局作用域（用于调试）
    window.appState = appState;
    window.elements = elements;

    // 暴露认证相关函数到全局作用域
    window.isAuthenticated = isAuthenticated;
    window.getCurrentUser = getCurrentUser;

    // 暴露API对象到全局作用域（用于测试和调试）
    window.authAPI = authAPI;
    window.taskAPI = taskAPI;
    window.chatAPI = chatAPI;

    // 暴露WebSocket函数到全局作用域
    window.connectWebSocket = connect;
    window.disconnectWebSocket = disconnect;
    window.sendPing = sendPing;
    window.getOnlineUsers = () => {
        console.log('获取在线用户功能暂未实现');
    };

    // 🔧 修复：暴露在线用户管理器到全局作用域
    window.onlineUsersManager = onlineUsersManager;
    window.wsManager = wsManager;

    // 🚀 暴露性能优化相关功能到全局作用域（用于调试和监控）
    window.performanceOptimizer = performanceOptimizer;
    window.isSlowConnection = isSlowConnection;
    window.preloadRoute = preloadRoute;
    window.debounce = debounce;
    window.throttle = throttle;

    console.log('函数已暴露到全局作用域');
}

// ==== 使用导入的WebSocket处理器函数 ====
// 这些函数已经从 './handlers/websocketHandlers.js' 导入
// updateConnectionStatus 和 updateOnlineUsers 函数可以直接使用

// ==== 导出主要功能供测试使用 ====
export {
    initializeApp,
    appState,
    elements
};
