/**
 * 任务事件处理器模块
 * 基于ES6模块化设计，遵循Clean Code JavaScript最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { task<PERSON>anager, TASK_STATUS, TASK_PRIORITY } from '../modules/tasks.js';
import {
    getFormData,
    resetForm,
    createElement,
    addClass,
    removeClass,
    toggleClass,
    escapeHtml,
    formatTimestamp,
    showNotification,
    querySelector,
    getElementById
} from '../modules/ui.js';

// 简化的按钮状态管理函数
function setButtonLoading(button, text = '处理中...') {
    if (!button || button.disabled) return false;

    if (!button.dataset.originalText) {
        button.dataset.originalText = button.textContent;
    }

    button.disabled = true;
    button.textContent = text;
    return true;
}

function restoreButton(button) {
    if (!button) return;

    button.disabled = false;
    button.textContent = button.dataset.originalText || '提交';
}

// ==== 任务事件处理函数 ====

/**
 * 处理任务表单提交
 * @param {Event} event - 表单提交事件
 */
export async function handleTaskSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = getFormData(form);
    
    if (!formData.title || formData.title.trim().length === 0) {
        showNotification('请输入任务标题', 'error');
        return;
    }
    
    try {
        const taskData = {
            title: formData.title.trim(),
            description: formData.description ? formData.description.trim() : '',
            priority: formData.priority || TASK_PRIORITY.MEDIUM,
            status: TASK_STATUS.PENDING
        };
        
        await taskManager.createTask(taskData);
        resetForm(form);
        
    } catch (error) {
        console.error('创建任务失败:', error);
        showNotification(error.message || '创建任务失败', 'error');
    }
}

/**
 * 处理任务编辑
 * @param {Event} event - 点击事件
 */
export function handleTaskEdit(event) {
    const taskItem = event.target.closest('.task-item');
    if (!taskItem) return;

    // 修复：直接使用UUID字符串，不进行parseInt转换
    const taskId = taskItem.dataset.taskId;
    const titleEl = taskItem.querySelector('.task-title');
    const descEl = taskItem.querySelector('.task-description');
    const controlsEl = taskItem.querySelector('.task-controls');

    if (!titleEl || !controlsEl || !taskId) {
        console.error('编辑任务失败：缺少必要的DOM元素或任务ID', { taskId, titleEl: !!titleEl, controlsEl: !!controlsEl });
        return;
    }
    
    // 保存原始内容
    const originalTitle = titleEl.textContent;
    const originalDesc = descEl ? descEl.textContent : '';
    
    // 创建编辑输入框
    const titleInput = createElement('input', {
        type: 'text',
        className: 'edit-input',
        value: originalTitle
    });
    
    const descInput = createElement('textarea', {
        className: 'edit-input',
        rows: '2',
        placeholder: '任务描述（可选）'
    });
    descInput.value = originalDesc;
    
    // 替换显示元素
    titleEl.replaceWith(titleInput);
    if (descEl) {
        descEl.replaceWith(descInput);
    } else {
        taskItem.insertBefore(descInput, controlsEl);
    }
    
    // 更新控制按钮
    controlsEl.innerHTML = `
        <button class="save-btn" data-original-title="${escapeHtml(originalTitle)}" data-original-desc="${escapeHtml(originalDesc)}">保存</button>
        <button class="cancel-btn">取消</button>
    `;
    
    // 添加编辑样式
    addClass(taskItem, 'editing');
    
    // 聚焦到标题输入框
    titleInput.focus();
    titleInput.select();
}

/**
 * 处理任务保存
 * @param {Event} event - 点击事件
 */
export async function handleTaskSave(event) {
    const taskItem = event.target.closest('.task-item');
    if (!taskItem) return;

    // 修复：直接使用UUID字符串，不进行parseInt转换
    const taskId = taskItem.dataset.taskId;
    const titleInput = taskItem.querySelector('input.edit-input');
    const descInput = taskItem.querySelector('textarea.edit-input');

    if (!titleInput || !taskId) {
        console.error('保存任务失败：缺少必要的输入元素或任务ID', { taskId, titleInput: !!titleInput });
        return;
    }
    
    const newTitle = titleInput.value.trim();
    const newDesc = descInput ? descInput.value.trim() : '';
    
    if (!newTitle) {
        showNotification('任务标题不能为空', 'error');
        titleInput.focus();
        return;
    }
    
    // 获取保存按钮
    const saveBtn = taskItem.querySelector('.save-btn');

    try {
        // 设置按钮加载状态
        setButtonLoading(saveBtn, '保存中...');

        const updatedTask = await taskManager.updateTask(taskId, {
            title: newTitle,
            description: newDesc
        });

        // 恢复显示模式
        restoreTaskDisplay(taskItem, updatedTask);

        // 显示成功消息
        showNotification(`任务"${newTitle}"已成功更新`, 'success');

    } catch (error) {
        console.error('保存任务失败:', error);

        // 显示详细错误信息
        let errorMessage = '保存任务失败';
        if (error.message) {
            if (error.message.includes('任务ID无效')) {
                errorMessage = '任务ID无效，请刷新页面重试';
            } else if (error.message.includes('网络')) {
                errorMessage = '网络连接失败，请检查网络后重试';
            } else if (error.message.includes('权限')) {
                errorMessage = '没有权限修改此任务';
            } else {
                errorMessage = `保存失败：${error.message}`;
            }
        }

        showNotification(errorMessage, 'error');
    } finally {
        // 恢复按钮状态
        restoreButton(saveBtn);
    }
}

/**
 * 处理任务取消编辑
 * @param {Event} event - 点击事件
 */
export function handleTaskCancel(event) {
    const taskItem = event.target.closest('.task-item');
    if (!taskItem) return;
    
    const saveBtn = taskItem.querySelector('.save-btn');
    if (!saveBtn) return;
    
    const originalTitle = saveBtn.dataset.originalTitle || '';
    const originalDesc = saveBtn.dataset.originalDesc || '';
    
    // 恢复原始显示
    const task = {
        title: originalTitle,
        description: originalDesc
    };
    
    restoreTaskDisplay(taskItem, task);
}

/**
 * 处理任务删除
 * @param {Event} event - 点击事件
 */
export async function handleTaskDelete(event) {
    const taskItem = event.target.closest('.task-item');
    if (!taskItem) return;

    // 修复：直接使用UUID字符串，不进行parseInt转换
    const taskId = taskItem.dataset.taskId;
    const taskTitle = taskItem.querySelector('.task-title')?.textContent || '未知任务';

    if (!taskId) {
        console.error('删除任务失败：无法获取任务ID');
        showNotification('删除任务失败：无法获取任务ID', 'error');
        return;
    }

    if (!confirm(`确定要删除任务"${taskTitle}"吗？此操作不可撤销。`)) {
        return;
    }
    
    // 获取删除按钮
    const deleteBtn = taskItem.querySelector('.delete-btn');

    try {
        // 设置按钮加载状态
        setButtonLoading(deleteBtn, '删除中...');

        await taskManager.deleteTask(taskId);

        // 显示成功消息
        showNotification(`任务"${taskTitle}"已成功删除`, 'success');

        // 删除成功，任务项将被移除，不需要恢复按钮状态

    } catch (error) {
        console.error('删除任务失败:', error);

        // 恢复按钮状态（只在失败时恢复）
        restoreButton(deleteBtn);

        // 显示详细错误信息
        let errorMessage = '删除任务失败';
        if (error.message) {
            if (error.message.includes('任务ID无效')) {
                errorMessage = '任务ID无效，请刷新页面重试';
            } else if (error.message.includes('网络')) {
                errorMessage = '网络连接失败，请检查网络后重试';
            } else if (error.message.includes('权限')) {
                errorMessage = '没有权限删除此任务';
            } else if (error.message.includes('不存在')) {
                errorMessage = '任务不存在或已被删除';
            } else {
                errorMessage = `删除失败：${error.message}`;
            }
        }

        showNotification(errorMessage, 'error');
    }
}

/**
 * 处理任务状态切换
 * @param {Event} event - 复选框变化事件
 */
export async function handleTaskToggle(event) {
    const checkbox = event.target;
    const taskItem = checkbox.closest('.task-item');
    if (!taskItem) return;

    // 修复：直接使用UUID字符串，不进行parseInt转换
    const taskId = taskItem.dataset.taskId;

    if (!taskId) {
        console.error('切换任务状态失败：无法获取任务ID');
        checkbox.checked = !checkbox.checked;
        showNotification('切换任务状态失败：无法获取任务ID', 'error');
        return;
    }

    // 检查复选框是否已在处理中（防止重复点击）
    if (checkbox.disabled) {
        console.warn('任务状态切换已在处理中，忽略重复操作');
        checkbox.checked = !checkbox.checked; // 恢复复选框状态
        return;
    }

    try {
        // 禁用复选框防止重复点击
        checkbox.disabled = true;

        await taskManager.toggleTaskCompletion(taskId);

        // 显示成功消息
        const taskTitle = taskItem.querySelector('.task-title')?.textContent || '任务';
        const isCompleted = checkbox.checked;
        showNotification(`${taskTitle}已标记为${isCompleted ? '已完成' : '未完成'}`, 'success');

    } catch (error) {
        console.error('切换任务状态失败:', error);

        // 恢复复选框状态
        checkbox.checked = !checkbox.checked;

        // 显示详细错误信息
        let errorMessage = '切换任务状态失败';
        if (error.message) {
            if (error.message.includes('任务ID无效')) {
                errorMessage = '任务ID无效，请刷新页面重试';
            } else if (error.message.includes('网络')) {
                errorMessage = '网络连接失败，请检查网络后重试';
            } else if (error.message.includes('权限')) {
                errorMessage = '没有权限修改此任务';
            } else if (error.message.includes('不存在')) {
                errorMessage = '任务不存在或已被删除';
            } else {
                errorMessage = `状态切换失败：${error.message}`;
            }
        }

        showNotification(errorMessage, 'error');
    } finally {
        // 重新启用复选框
        checkbox.disabled = false;
    }
}

/**
 * 处理过滤器变化
 * @param {Event} event - 点击事件
 */
export function handleFilterChange(event) {
    const button = event.target;
    if (!button.dataset.filter) return;
    
    const filter = button.dataset.filter;
    
    // 更新按钮状态
    const filterBtns = button.parentElement.querySelectorAll('button');
    filterBtns.forEach(btn => removeClass(btn, 'active'));
    addClass(button, 'active');
    
    // 应用过滤器
    taskManager.setFilter(filter);
}

// ==== 任务UI渲染函数 ====

/**
 * 加载任务列表
 */
export async function loadTasks() {
    try {
        // 清除之前的错误显示
        const taskList = document.getElementById('taskList');
        if (taskList) {
            taskList.innerHTML = '<li>正在加载任务...</li>';
        }

        await taskManager.fetchAllTasks();
    } catch (error) {
        console.error('加载任务失败:', error);

        // 检查是否是认证错误
        if (error.status === 401 || error.message.includes('认证失败')) {
            showNotification('认证已过期，请重新登录', 'error');
            // 触发重新登录
            if (window.clearAuthState) {
                window.clearAuthState();
            }
        } else {
            showNotification('加载任务失败，请刷新页面重试', 'error');
        }

        // 更新任务列表显示错误信息
        const taskList = document.getElementById('taskList');
        if (taskList) {
            taskList.innerHTML = `<li>加载失败: ${error.message}</li>`;
        }
    }
}

/**
 * 渲染任务列表
 * @param {Array} tasks - 任务列表
 */
export function renderTaskList(tasks) {
    console.log('renderTaskList 被调用，任务数据:', tasks);
    const taskList = getElementById('taskList');
    if (!taskList) {
        console.warn('taskList 元素未找到');
        return;
    }

    if (!Array.isArray(tasks) || tasks.length === 0) {
        console.log('任务列表为空，显示默认消息');
        taskList.innerHTML = '<li>✨ 暂无任务，创建第一个任务开始吧！</li>';
        return;
    }

    console.log(`正在渲染 ${tasks.length} 个任务`);
    const tasksHTML = tasks.map(task => createTaskHTML(task)).join('');
    taskList.innerHTML = tasksHTML;
    console.log('任务列表渲染完成');
}

/**
 * 添加任务到UI
 * @param {Object} task - 任务对象
 */
export function addTaskToUI(task) {
    const taskList = getElementById('taskList');
    if (!taskList) return;
    
    const taskHTML = createTaskHTML(task);
    taskList.insertAdjacentHTML('beforeend', taskHTML);
}

/**
 * 更新UI中的任务
 * @param {Object} task - 任务对象
 */
export function updateTaskInUI(task) {
    const taskItem = querySelector(`[data-task-id="${task.id}"]`);
    if (!taskItem) return;
    
    const newTaskHTML = createTaskHTML(task);
    taskItem.outerHTML = newTaskHTML;
}

/**
 * 从UI中移除任务
 * @param {Object} task - 任务对象
 */
export function removeTaskFromUI(task) {
    const taskItem = querySelector(`[data-task-id="${task.id}"]`);
    if (taskItem) {
        taskItem.remove();
    }
}

/**
 * 清空任务UI
 */
export function clearTasksUI() {
    const taskList = getElementById('taskList');
    if (taskList) {
        taskList.innerHTML = '';
    }
}

/**
 * 创建任务HTML
 * @param {Object} task - 任务对象
 * @returns {string} 任务HTML字符串
 */
function createTaskHTML(task) {
    const isCompleted = task.status === TASK_STATUS.COMPLETED;
    const priorityClass = `priority-${task.priority}`;
    const completedClass = isCompleted ? 'completed' : '';
    
    return `
        <div class="task-item ${completedClass} ${priorityClass}" data-task-id="${task.id}">
            <div class="task-header">
                <label class="task-checkbox">
                    <input type="checkbox" ${isCompleted ? 'checked' : ''}>
                    <span class="checkmark"></span>
                </label>
                <div class="task-priority priority-${task.priority}">
                    ${getPriorityText(task.priority)}
                </div>
            </div>
            <div class="task-content">
                <div class="task-title">${escapeHtml(task.title)}</div>
                ${task.description ? `<div class="task-description">${escapeHtml(task.description)}</div>` : ''}
                <div class="task-meta">
                    <span class="task-status">状态: ${getStatusText(task.status)}</span>
                    ${task.created_at ? `<span class="task-date">创建: ${formatTimestamp(task.created_at)}</span>` : ''}
                </div>
            </div>
            <div class="task-controls">
                <button class="edit-btn">编辑</button>
                <button class="delete-btn">删除</button>
            </div>
        </div>
    `;
}

/**
 * 恢复任务显示模式
 * @param {Element} taskItem - 任务元素
 * @param {Object} task - 任务对象
 */
function restoreTaskDisplay(taskItem, task) {
    const titleInput = taskItem.querySelector('input.edit-input');
    const descInput = taskItem.querySelector('textarea.edit-input');
    const controlsEl = taskItem.querySelector('.task-controls');
    
    if (!titleInput || !controlsEl) return;
    
    // 创建显示元素
    const titleEl = createElement('div', { className: 'task-title' }, task.title);
    const descEl = task.description ? 
        createElement('div', { className: 'task-description' }, task.description) : null;
    
    // 替换输入框
    titleInput.replaceWith(titleEl);
    if (descInput) {
        if (descEl) {
            descInput.replaceWith(descEl);
        } else {
            descInput.remove();
        }
    } else if (descEl) {
        taskItem.insertBefore(descEl, controlsEl);
    }
    
    // 恢复控制按钮
    controlsEl.innerHTML = `
        <button class="edit-btn">编辑</button>
        <button class="delete-btn">删除</button>
    `;
    
    // 移除编辑样式
    removeClass(taskItem, 'editing');
}

/**
 * 获取优先级文本
 * @param {string} priority - 优先级
 * @returns {string} 优先级文本
 */
function getPriorityText(priority) {
    const priorityMap = {
        [TASK_PRIORITY.LOW]: '低',
        [TASK_PRIORITY.MEDIUM]: '中',
        [TASK_PRIORITY.HIGH]: '高',
        [TASK_PRIORITY.URGENT]: '紧急'
    };
    return priorityMap[priority] || '中';
}

/**
 * 获取状态文本
 * @param {string} status - 状态
 * @returns {string} 状态文本
 */
function getStatusText(status) {
    const statusMap = {
        [TASK_STATUS.PENDING]: '待处理',
        [TASK_STATUS.IN_PROGRESS]: '进行中',
        [TASK_STATUS.COMPLETED]: '已完成',
        [TASK_STATUS.CANCELLED]: '已取消'
    };
    return statusMap[status] || '未知';
}
