/**
 * WebSocket事件处理器模块
 * 基于ES6模块化设计，遵循Clean Code JavaScript最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { wsManager, MESSAGE_TYPE } from '../modules/websocket.js';
import { 
    createElement, 
    addClass, 
    removeClass, 
    escapeHtml, 
    formatTimestamp, 
    scrollToBottom,
    showNotification,
    getElementById
} from '../modules/ui.js';

// ==== WebSocket事件处理函数 ====

/**
 * 处理发送消息
 */
export function handleSendMessage() {
    const messageInput = getElementById('messageInput');
    if (!messageInput) return;
    
    const message = messageInput.value.trim();
    if (!message) {
        showNotification('请输入消息内容', 'warning');
        return;
    }
    
    if (message.length > 500) {
        showNotification('消息长度不能超过500个字符', 'error');
        return;
    }
    
    const success = wsManager.sendChatMessage(message);
    if (success) {
        messageInput.value = '';
        messageInput.focus();
    } else {
        showNotification('发送失败，请检查连接状态', 'error');
    }
}

/**
 * 处理获取在线用户
 */
export function handleGetUsers() {
    const success = wsManager.send({
        type: 'get_users',
        timestamp: new Date().toISOString()
    });
    
    if (!success) {
        showNotification('获取用户列表失败，请检查连接状态', 'error');
    }
}

/**
 * 处理清除日志
 */
export function handleClearLog() {
    const rawMessages = getElementById('rawMessages');
    if (rawMessages) {
        rawMessages.innerHTML = `
            <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                等待消息...
            </div>
        `;
    }
    
    const messagesContainer = getElementById('messages');
    if (messagesContainer) {
        messagesContainer.innerHTML = `
            <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                聊天记录已清空
            </div>
        `;
    }
    
    showNotification('日志已清空', 'info');
}

// ==== WebSocket消息处理函数 ====

/**
 * 显示聊天消息 - 方案一修复：使用正确的容器ID和chatAPI.displayMessage
 * @param {Object} message - 消息对象
 */
export function displayChatMessage(message) {
    // 🔧 修复：过滤心跳消息，检查消息内容是否为心跳JSON
    if (message.content) {
        try {
            const parsedContent = JSON.parse(message.content);
            if (parsedContent.type === 'ping' || parsedContent.type === 'pong') {
                console.log(`心跳消息已过滤，不显示在聊天界面: ${parsedContent.type}`);
                return; // 不显示心跳消息
            }
        } catch (e) {
            // 如果不是JSON格式，继续正常处理
        }
    }

    // 🔧 方案一修复：使用正确的聊天消息容器ID
    const messagesContainer = getElementById('messagesContainer');
    if (!messagesContainer) {
        console.error('❌ 找不到聊天消息容器 #messagesContainer');
        return;
    }

    // 🔧 方案一修复：优先使用 chatAPI.displayMessage 方法
    if (window.chatAPI && window.chatAPI.displayMessage) {
        console.log('✅ 使用 chatAPI.displayMessage 显示消息');

        // 转换消息格式以适配 chatAPI.displayMessage
        const chatMessage = {
            content: message.content || '',
            sender: {
                username: message.sender || '匿名用户'
            },
            timestamp: message.timestamp || new Date().toISOString(),
            message_id: message.message_id || 'unknown',
            message_type: 'Text'
        };

        window.chatAPI.displayMessage(chatMessage);
        console.log('✅ 消息已通过 chatAPI.displayMessage 显示');
        return;
    }

    // 🔧 降级处理：如果 chatAPI.displayMessage 不可用，使用原有逻辑
    console.warn('⚠️ chatAPI.displayMessage 不可用，使用降级显示方法');

    // 清除初始提示信息
    clearInitialMessage(messagesContainer);

    const messageEl = createElement('div', {
        className: 'message text'
    });

    const headerEl = createElement('div', {
        className: 'message-header'
    }, `${escapeHtml(message.sender || '匿名用户')} - ${formatTimestamp(message.timestamp)}`);

    const contentEl = createElement('div', {
        className: 'message-content'
    }, escapeHtml(message.content || ''));

    messageEl.appendChild(headerEl);
    messageEl.appendChild(contentEl);

    // 如果是当前用户发送的消息，添加特殊样式
    if (message.is_own) {
        addClass(messageEl, 'own');
    }

    messagesContainer.appendChild(messageEl);
    scrollToBottom(messagesContainer);
}

/**
 * 显示系统消息
 * @param {Object} message - 消息对象
 */
export function displaySystemMessage(message) {
    const messagesContainer = getElementById('messages');
    if (!messagesContainer) return;
    
    clearInitialMessage(messagesContainer);
    
    const messageEl = createElement('div', {
        className: 'message system'
    });
    
    const contentEl = createElement('div', {
        className: 'message-content'
    }, `🔔 ${escapeHtml(message.content || message.message || '')}`);
    
    messageEl.appendChild(contentEl);
    messagesContainer.appendChild(messageEl);
    scrollToBottom(messagesContainer);
}

/**
 * 显示用户加入消息
 * @param {Object} message - 消息对象
 */
export function displayUserJoined(message) {
    const messagesContainer = getElementById('messages');
    if (!messagesContainer) return;
    
    clearInitialMessage(messagesContainer);
    
    const messageEl = createElement('div', {
        className: 'message user-joined'
    });
    
    const contentEl = createElement('div', {
        className: 'message-content'
    }, `👋 ${escapeHtml(message.username || '用户')} 加入了聊天`);
    
    messageEl.appendChild(contentEl);
    messagesContainer.appendChild(messageEl);
    scrollToBottom(messagesContainer);
}

/**
 * 显示用户离开消息
 * @param {Object} message - 消息对象
 */
export function displayUserLeft(message) {
    const messagesContainer = getElementById('messages');
    if (!messagesContainer) return;
    
    clearInitialMessage(messagesContainer);
    
    const messageEl = createElement('div', {
        className: 'message user-left'
    });
    
    const contentEl = createElement('div', {
        className: 'message-content'
    }, `👋 ${escapeHtml(message.username || '用户')} 离开了聊天`);
    
    messageEl.appendChild(contentEl);
    messagesContainer.appendChild(messageEl);
    scrollToBottom(messagesContainer);
}

/**
 * 显示错误消息
 * @param {Object} message - 消息对象
 */
export function displayErrorMessage(message) {
    const messagesContainer = getElementById('messages');
    if (!messagesContainer) return;
    
    clearInitialMessage(messagesContainer);
    
    const messageEl = createElement('div', {
        className: 'message system'
    });
    
    const contentEl = createElement('div', {
        className: 'message-content'
    }, `❌ 错误: ${escapeHtml(message.error || message.message || '未知错误')}`);
    
    messageEl.appendChild(contentEl);
    messagesContainer.appendChild(messageEl);
    scrollToBottom(messagesContainer);
    
    // 同时显示通知
    showNotification(message.error || message.message || '发生未知错误', 'error');
}

/**
 * 显示原始消息日志
 * @param {Object} message - 消息对象
 */
export function displayRawMessage(message) {
    const rawMessages = getElementById('rawMessages');
    if (!rawMessages) return;

    // 🔧 修复：过滤心跳消息，只在控制台记录，不在原始日志中显示
    const messageType = message.type || 'unknown';
    if (messageType === MESSAGE_TYPE.PING || messageType === MESSAGE_TYPE.PONG) {
        console.log(`心跳消息已过滤: ${messageType}`, message);
        return; // 不显示心跳消息在原始日志中
    }

    // 🔧 修复：检查消息内容是否为心跳JSON（针对包装在Text消息中的心跳）
    if (message.content && typeof message.content === 'string') {
        try {
            const parsedContent = JSON.parse(message.content);
            if (parsedContent.type === 'ping' || parsedContent.type === 'pong') {
                console.log(`包装的心跳消息已过滤: ${parsedContent.type}`, message);
                return; // 不显示包装的心跳消息
            }
        } catch (e) {
            // 如果不是JSON格式，继续正常处理
        }
    }

    // 清除初始提示信息
    clearInitialMessage(rawMessages);

    const timestamp = new Date().toLocaleTimeString('zh-CN');

    const messageEl = createElement('div', {
        className: 'raw-message'
    });

    // 根据消息类型设置样式
    let typeClass = 'info';
    switch (messageType) {
        case MESSAGE_TYPE.ERROR:
            typeClass = 'error';
            break;
        case MESSAGE_TYPE.SYSTEM:
            typeClass = 'system';
            break;
        case MESSAGE_TYPE.CHAT:
            typeClass = 'success';
            break;
        case MESSAGE_TYPE.USER_JOINED:
        case MESSAGE_TYPE.USER_LEFT:
            typeClass = 'info';
            break;
    }

    addClass(messageEl, typeClass);

    messageEl.innerHTML = `
        <span style="color: #6c757d;">[${timestamp}]</span>
        <span style="font-weight: bold;">[${messageType.toUpperCase()}]</span>
        ${escapeHtml(JSON.stringify(message, null, 2))}
    `;

    rawMessages.appendChild(messageEl);
    scrollToBottom(rawMessages);

    // 限制日志条数，避免内存泄漏
    const maxMessages = 100;
    const messages = rawMessages.children;
    if (messages.length > maxMessages) {
        for (let i = 0; i < messages.length - maxMessages; i++) {
            messages[i].remove();
        }
    }
}

/**
 * 更新连接状态
 * @param {string} state - 连接状态
 */
export function updateConnectionStatus(state) {
    const connectionStatus = getElementById('connectionStatus');
    if (!connectionStatus) return;
    
    // 移除所有状态类
    removeClass(connectionStatus, 'connected', 'disconnected', 'reconnecting');
    
    let statusText = '';
    let statusClass = '';
    
    switch (state) {
        case 'connecting':
            statusText = '🔄 正在连接...';
            statusClass = 'reconnecting';
            break;
        case 'connected':
            statusText = '✅ 已连接';
            statusClass = 'connected';
            break;
        case 'reconnecting':
            statusText = '🔄 重新连接中...';
            statusClass = 'reconnecting';
            break;
        case 'disconnected':
        case 'error':
        default:
            statusText = '❌ 未连接';
            statusClass = 'disconnected';
            break;
    }
    
    connectionStatus.textContent = statusText;
    addClass(connectionStatus, statusClass);
    
    // 更新控制按钮状态
    updateControlButtons(state);
}

/**
 * 更新在线用户列表
 * @param {Array} users - 用户列表
 */
export function updateOnlineUsers(users = []) {
    const onlineUsers = getElementById('onlineUsers');
    if (!onlineUsers) return;
    
    if (!Array.isArray(users) || users.length === 0) {
        onlineUsers.innerHTML = `
            <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                暂无在线用户
            </div>
        `;
        return;
    }
    
    const usersHTML = users.map(user => `
        <div class="user-item">
            <span class="user-name">${escapeHtml(user.username || user.name || '匿名用户')}</span>
            <span class="user-status">${user.status || '在线'}</span>
        </div>
    `).join('');
    
    onlineUsers.innerHTML = usersHTML;
}

/**
 * 更新控制按钮状态
 * @param {string} state - 连接状态
 */
function updateControlButtons(state) {
    const connectBtn = getElementById('connectBtn');
    const disconnectBtn = getElementById('disconnectBtn');
    const pingBtn = getElementById('pingBtn');
    const getUsersBtn = getElementById('getUsersBtn');
    const sendBtn = getElementById('sendBtn');
    const messageInput = getElementById('messageInput');
    
    const isConnected = state === 'connected';
    
    if (connectBtn) connectBtn.disabled = isConnected;
    if (disconnectBtn) disconnectBtn.disabled = !isConnected;
    if (pingBtn) pingBtn.disabled = !isConnected;
    if (getUsersBtn) getUsersBtn.disabled = !isConnected;
    if (sendBtn) sendBtn.disabled = !isConnected;
    if (messageInput) messageInput.disabled = !isConnected;
}

/**
 * 清除初始提示消息
 * @param {Element} container - 容器元素
 */
function clearInitialMessage(container) {
    if (!container) return;
    
    // 如果容器只有一个子元素且包含提示文本，则清除
    if (container.children.length === 1) {
        const firstChild = container.firstElementChild;
        if (firstChild && (
            firstChild.textContent.includes('等待消息') ||
            firstChild.textContent.includes('聊天记录已清空')
        )) {
            container.innerHTML = '';
        }
    }
}
