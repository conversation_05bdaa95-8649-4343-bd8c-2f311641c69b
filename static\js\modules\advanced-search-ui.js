/**
 * 高级搜索UI控制器模块
 * 
 * 功能特性：
 * - 搜索界面管理
 * - 时间范围选择器
 * - 多条件过滤
 * - 搜索结果展示
 * - 键盘导航支持
 * - 移动端适配
 * - 搜索历史管理
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { chatAPI } from './api.js';
import { isAuthenticated } from './auth.js';

/**
 * 高级搜索UI控制器类
 */
export class AdvancedSearchUI {
    constructor() {
        this.modal = null;
        this.searchForm = null;
        this.resultsContainer = null;
        this.loadingIndicator = null;
        this.currentPage = 1;
        this.totalPages = 1;
        this.currentSearchParams = null;
        this.searchHistory = this.loadSearchHistory();
        
        this.init();
    }

    /**
     * 初始化搜索UI
     */
    init() {
        this.bindElements();
        this.bindEvents();
        this.setupKeyboardNavigation();
        this.setupMobileAdaptation();
        
        console.log('高级搜索UI控制器已初始化');
    }

    /**
     * 绑定DOM元素
     */
    bindElements() {
        this.modal = document.getElementById('advancedSearchModal');
        this.searchForm = document.getElementById('advancedSearchForm');
        this.resultsContainer = document.getElementById('searchResults');
        this.loadingIndicator = document.getElementById('searchLoadingIndicator');
        
        // 表单元素
        this.keywordInput = document.getElementById('searchKeyword');
        this.messageTypeSelect = document.getElementById('messageTypeFilter');
        this.senderInput = document.getElementById('senderFilter');
        this.startDateInput = document.getElementById('startDatePicker');
        this.endDateInput = document.getElementById('endDatePicker');
        
        // 结果元素
        this.resultsList = document.getElementById('searchResultsList');
        this.resultsCount = document.getElementById('resultsCount');
        this.pagination = document.getElementById('searchPagination');
        this.sortSelect = document.getElementById('sortOptions');
        
        // 错误提示
        this.errorMessage = document.getElementById('dateRangeError');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 打开搜索模态框
        const openButton = document.getElementById('openAdvancedSearch');
        if (openButton) {
            openButton.addEventListener('click', () => this.openModal());
        }

        // 关闭搜索模态框
        const closeButton = document.getElementById('closeSearchModal');
        if (closeButton) {
            closeButton.addEventListener('click', () => this.closeModal());
        }

        // 模态框背景点击关闭
        if (this.modal) {
            this.modal.addEventListener('click', (e) => {
                if (e.target === this.modal) {
                    this.closeModal();
                }
            });
        }

        // 搜索表单提交
        if (this.searchForm) {
            this.searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }

        // 清除筛选按钮
        const clearButton = document.getElementById('clearFiltersButton');
        if (clearButton) {
            clearButton.addEventListener('click', () => this.clearFilters());
        }

        // 快捷时间范围选择
        const quickOptions = document.querySelectorAll('.quick-option');
        quickOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                const range = e.target.dataset.quickRange;
                this.setQuickTimeRange(range);
            });
        });

        // 日期验证
        if (this.startDateInput && this.endDateInput) {
            this.startDateInput.addEventListener('change', () => this.validateDateRange());
            this.endDateInput.addEventListener('change', () => this.validateDateRange());
        }

        // 排序选择
        if (this.sortSelect) {
            this.sortSelect.addEventListener('change', () => {
                if (this.currentSearchParams) {
                    this.performSearch();
                }
            });
        }

        // 分页按钮
        const prevButton = document.getElementById('prevPageButton');
        const nextButton = document.getElementById('nextPageButton');
        
        if (prevButton) {
            prevButton.addEventListener('click', () => this.goToPreviousPage());
        }
        
        if (nextButton) {
            nextButton.addEventListener('click', () => this.goToNextPage());
        }
    }

    /**
     * 设置键盘导航支持
     */
    setupKeyboardNavigation() {
        // Escape键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isModalOpen()) {
                this.closeModal();
            }
        });

        // Enter键执行搜索
        if (this.keywordInput) {
            this.keywordInput.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.performSearch();
                }
            });
        }

        // Tab键循环导航
        if (this.modal) {
            this.modal.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    this.handleTabNavigation(e);
                }
            });
        }
    }

    /**
     * 设置移动端适配
     */
    setupMobileAdaptation() {
        // 检测移动设备
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            // 添加移动端样式类
            if (this.modal) {
                this.modal.classList.add('mobile-responsive');
            }
            
            // 调整输入框行为
            if (this.keywordInput) {
                this.keywordInput.setAttribute('autocomplete', 'off');
                this.keywordInput.setAttribute('autocorrect', 'off');
                this.keywordInput.setAttribute('autocapitalize', 'off');
            }
        }

        // 监听屏幕方向变化
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.adjustModalSize();
            }, 100);
        });
    }

    /**
     * 打开搜索模态框
     */
    openModal() {
        if (!isAuthenticated()) {
            alert('请先登录后再使用搜索功能');
            return;
        }

        if (this.modal) {
            this.modal.classList.add('active');
            this.modal.setAttribute('aria-hidden', 'false');
            
            // 聚焦到搜索输入框
            if (this.keywordInput) {
                setTimeout(() => {
                    this.keywordInput.focus();
                }, 100);
            }
            
            // 防止背景滚动
            document.body.style.overflow = 'hidden';
            
            console.log('高级搜索模态框已打开');
        }
    }

    /**
     * 关闭搜索模态框
     */
    closeModal() {
        if (this.modal) {
            this.modal.classList.remove('active');
            this.modal.setAttribute('aria-hidden', 'true');
            
            // 恢复背景滚动
            document.body.style.overflow = '';
            
            console.log('高级搜索模态框已关闭');
        }
    }

    /**
     * 检查模态框是否打开
     */
    isModalOpen() {
        return this.modal && this.modal.classList.contains('active');
    }

    /**
     * 设置快捷时间范围
     */
    setQuickTimeRange(range) {
        const today = new Date();
        let startDate, endDate;

        switch (range) {
            case 'today':
                startDate = endDate = this.formatDate(today);
                break;
            case 'yesterday':
                const yesterday = new Date(today);
                yesterday.setDate(yesterday.getDate() - 1);
                startDate = endDate = this.formatDate(yesterday);
                break;
            case 'week':
                const weekStart = new Date(today);
                weekStart.setDate(today.getDate() - today.getDay());
                startDate = this.formatDate(weekStart);
                endDate = this.formatDate(today);
                break;
            case 'month':
                const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
                startDate = this.formatDate(monthStart);
                endDate = this.formatDate(today);
                break;
            default:
                return;
        }

        if (this.startDateInput) this.startDateInput.value = startDate;
        if (this.endDateInput) this.endDateInput.value = endDate;

        // 移除错误提示
        this.hideError();

        console.log(`设置快捷时间范围: ${range}, ${startDate} - ${endDate}`);
    }

    /**
     * 格式化日期为YYYY-MM-DD格式
     */
    formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    /**
     * 验证日期范围
     */
    validateDateRange() {
        const startDate = this.startDateInput?.value;
        const endDate = this.endDateInput?.value;

        if (startDate && endDate && startDate > endDate) {
            this.showError('结束日期不能早于开始日期');
            return false;
        }

        this.hideError();
        return true;
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        if (this.errorMessage) {
            this.errorMessage.textContent = message;
            this.errorMessage.classList.add('active');
        }
    }

    /**
     * 隐藏错误信息
     */
    hideError() {
        if (this.errorMessage) {
            this.errorMessage.classList.remove('active');
        }
    }

    /**
     * 清除所有筛选条件
     */
    clearFilters() {
        if (this.keywordInput) this.keywordInput.value = '';
        if (this.messageTypeSelect) this.messageTypeSelect.value = '';
        if (this.senderInput) this.senderInput.value = '';
        if (this.startDateInput) this.startDateInput.value = '';
        if (this.endDateInput) this.endDateInput.value = '';
        
        this.hideError();
        this.hideResults();
        
        console.log('已清除所有筛选条件');
    }

    /**
     * 执行搜索
     */
    async performSearch() {
        // 验证表单
        if (!this.validateForm()) {
            return;
        }

        // 构建搜索参数
        const searchParams = this.buildSearchParams();
        this.currentSearchParams = searchParams;

        // 保存到搜索历史
        this.saveToHistory(searchParams);

        try {
            // 显示加载指示器
            this.showLoading();

            console.log('开始执行搜索:', searchParams);

            // 调用搜索API
            const response = await chatAPI.searchMessages(searchParams);

            // 显示搜索结果
            this.displayResults(response);

            console.log('搜索完成:', response);

        } catch (error) {
            console.error('搜索失败:', error);

            // 根据错误类型提供更友好的错误信息
            let errorMessage = '搜索失败';
            if (error.name === 'AbortError') {
                errorMessage = '搜索请求超时，请稍后重试';
            } else if (error.status === 503) {
                errorMessage = '搜索服务暂时不可用，请稍后重试';
            } else if (error.message) {
                errorMessage = `搜索失败: ${error.message}`;
            }

            this.showError(errorMessage);
        } finally {
            // 隐藏加载指示器
            this.hideLoading();
        }
    }

    /**
     * 验证搜索表单
     */
    validateForm() {
        // 检查关键词
        const keyword = this.keywordInput?.value?.trim();
        if (!keyword) {
            this.showError('请输入搜索关键词');
            this.keywordInput?.focus();
            return false;
        }

        // 验证日期范围
        if (!this.validateDateRange()) {
            return false;
        }

        return true;
    }

    /**
     * 构建搜索参数
     */
    buildSearchParams() {
        const params = {
            keyword: this.keywordInput?.value?.trim(),
            page: this.currentPage,
            limit: 20
        };

        // 可选参数
        if (this.messageTypeSelect?.value) {
            params.message_type = this.messageTypeSelect.value;
        }

        if (this.senderInput?.value?.trim()) {
            params.sender = this.senderInput.value.trim();
        }

        if (this.startDateInput?.value) {
            params.start_date = this.startDateInput.value + 'T00:00:00Z';
        }

        if (this.endDateInput?.value) {
            params.end_date = this.endDateInput.value + 'T23:59:59Z';
        }

        return params;
    }

    /**
     * 显示加载指示器
     */
    showLoading() {
        if (this.loadingIndicator) {
            this.loadingIndicator.classList.add('active');
        }
        this.hideResults();
    }

    /**
     * 隐藏加载指示器
     */
    hideLoading() {
        if (this.loadingIndicator) {
            this.loadingIndicator.classList.remove('active');
        }
    }

    /**
     * 显示搜索结果
     */
    displayResults(response) {
        if (!response || !response.data) {
            this.showError('搜索响应格式错误');
            return;
        }

        const { messages, total_count, page, limit } = response.data;

        // 更新结果计数
        if (this.resultsCount) {
            this.resultsCount.textContent = `找到 ${total_count} 条匹配的消息`;
        }

        // 渲染结果列表
        this.renderResultsList(messages);

        // 更新分页信息
        this.updatePagination(page, Math.ceil(total_count / limit));

        // 显示结果容器
        this.showResults();
    }

    /**
     * 渲染搜索结果列表
     */
    renderResultsList(messages) {
        if (!this.resultsList) return;

        if (!messages || messages.length === 0) {
            this.resultsList.innerHTML = `
                <div class="no-results" style="text-align: center; padding: 40px; color: #6c757d;">
                    <p>没有找到匹配的消息</p>
                    <p>请尝试调整搜索条件</p>
                </div>
            `;
            return;
        }

        const resultsHTML = messages.map(message => `
            <div class="result-item" data-message-id="${message.id}">
                <div class="result-content">${this.escapeHtml(message.content)}</div>
                <div class="result-meta">
                    <span class="sender">发送者: ${this.escapeHtml(message.sender_username)}</span>
                    <span class="room">聊天室: ${this.escapeHtml(message.room_name)}</span>
                    <span class="time">${this.formatDateTime(message.created_at)}</span>
                    <span class="type">类型: ${message.message_type}</span>
                </div>
            </div>
        `).join('');

        this.resultsList.innerHTML = resultsHTML;
    }

    /**
     * 转义HTML内容
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 格式化日期时间
     */
    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * 更新分页信息
     */
    updatePagination(currentPage, totalPages) {
        this.currentPage = currentPage;
        this.totalPages = totalPages;

        const pageInfo = document.getElementById('pageInfo');
        const prevButton = document.getElementById('prevPageButton');
        const nextButton = document.getElementById('nextPageButton');

        if (pageInfo) {
            pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;
        }

        if (prevButton) {
            prevButton.disabled = currentPage <= 1;
        }

        if (nextButton) {
            nextButton.disabled = currentPage >= totalPages;
        }
    }

    /**
     * 显示搜索结果
     */
    showResults() {
        if (this.resultsContainer) {
            this.resultsContainer.classList.add('active');
        }
    }

    /**
     * 隐藏搜索结果
     */
    hideResults() {
        if (this.resultsContainer) {
            this.resultsContainer.classList.remove('active');
        }
    }

    /**
     * 上一页
     */
    goToPreviousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.performSearch();
        }
    }

    /**
     * 下一页
     */
    goToNextPage() {
        if (this.currentPage < this.totalPages) {
            this.currentPage++;
            this.performSearch();
        }
    }

    /**
     * 处理Tab键导航
     */
    handleTabNavigation(e) {
        const focusableElements = this.modal.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );
        
        const firstElement = focusableElements[0];
        const lastElement = focusableElements[focusableElements.length - 1];

        if (e.shiftKey) {
            if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
            }
        } else {
            if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
            }
        }
    }

    /**
     * 调整模态框大小（移动端）
     */
    adjustModalSize() {
        if (window.innerWidth <= 768 && this.modal) {
            const container = this.modal.querySelector('.search-container');
            if (container) {
                container.style.maxHeight = `${window.innerHeight * 0.9}px`;
            }
        }
    }

    /**
     * 保存搜索到历史记录
     */
    saveToHistory(searchParams) {
        const historyItem = {
            keyword: searchParams.keyword,
            timestamp: new Date().toISOString(),
            params: searchParams
        };

        this.searchHistory.unshift(historyItem);
        
        // 限制历史记录数量
        if (this.searchHistory.length > 10) {
            this.searchHistory = this.searchHistory.slice(0, 10);
        }

        // 保存到本地存储
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }

    /**
     * 加载搜索历史
     */
    loadSearchHistory() {
        try {
            const history = localStorage.getItem('searchHistory');
            return history ? JSON.parse(history) : [];
        } catch (error) {
            console.warn('加载搜索历史失败:', error);
            return [];
        }
    }
}

// 创建全局实例
let advancedSearchUI = null;

/**
 * 初始化高级搜索UI
 */
export function initAdvancedSearchUI() {
    if (!advancedSearchUI) {
        advancedSearchUI = new AdvancedSearchUI();
        
        // 暴露到全局作用域供测试使用
        if (typeof window !== 'undefined') {
            window.advancedSearchUI = advancedSearchUI;
        }
    }
    return advancedSearchUI;
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    initAdvancedSearchUI();
});

export default AdvancedSearchUI;
