/**
 * API客户端模块 - 统一处理所有后端API通信
 * 基于ES6模块化设计，遵循Clean Code JavaScript最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { getAuthToken, isAuthenticated } from './auth.js';

// ==== API配置常量 ====
const API_BASE_URL = '/api';
const DEFAULT_TIMEOUT = 25000; // 25秒超时（小于服务器30秒）
const RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 2000; // 2秒重试延迟（给服务器更多恢复时间）

// ==== HTTP状态码常量 ====
const HTTP_STATUS = {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    INTERNAL_SERVER_ERROR: 500
};

/**
 * API错误类
 */
export class APIError extends Error {
    constructor(message, status, response) {
        super(message);
        this.name = 'APIError';
        this.status = status;
        this.response = response;
    }
}

/**
 * 创建带有认证头的请求配置
 * @param {Object} options - 请求选项
 * @returns {Object} 完整的请求配置
 */
function createRequestConfig(options = {}) {
    const config = {
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        },
        ...options
    };

    // 添加认证头
    if (isAuthenticated()) {
        const token = getAuthToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
    }

    return config;
}

/**
 * 处理API响应
 * @param {Response} response - Fetch响应对象
 * @returns {Promise<any>} 解析后的响应数据
 */
async function handleResponse(response) {
    const contentType = response.headers.get('content-type');
    
    let data;
    if (contentType && contentType.includes('application/json')) {
        data = await response.json();
    } else {
        data = await response.text();
    }

    if (!response.ok) {
        const errorMessage = data.message || data.error || `HTTP ${response.status}: ${response.statusText}`;
        throw new APIError(errorMessage, response.status, data);
    }

    return data;
}

/**
 * 延迟函数
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise<void>}
 */
function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 带重试机制的fetch请求
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @param {number} retryCount - 当前重试次数
 * @returns {Promise<any>} 响应数据
 */
async function fetchWithRetry(url, options, retryCount = 0) {
    let timeoutId;
    try {
        const controller = new AbortController();
        const timeout = options.timeout || DEFAULT_TIMEOUT;
        timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(url, {
            ...options,
            signal: controller.signal
        });

        clearTimeout(timeoutId);
        return await handleResponse(response);
    } catch (error) {
        // 清理超时定时器
        if (timeoutId) {
            clearTimeout(timeoutId);
        }

        // 详细错误日志记录
        console.warn(`请求错误详情:`, {
            name: error.name,
            message: error.message,
            url: url,
            retryCount: retryCount,
            timestamp: new Date().toISOString()
        });

        // 如果是网络错误、超时错误或服务器错误且还有重试次数，则重试
        if (retryCount < RETRY_ATTEMPTS &&
            (error.name === 'TypeError' ||
             error.name === 'AbortError' ||
             (error.status >= 500 && error.status < 600))) {
            console.warn(`请求失败，${RETRY_DELAY}ms后进行第${retryCount + 1}次重试:`, error.message);
            await delay(RETRY_DELAY);
            return fetchWithRetry(url, options, retryCount + 1);
        }
        throw error;
    }
}

/**
 * 通用API请求方法
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @returns {Promise<any>} 响应数据
 */
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = createRequestConfig(options);
    
    console.log(`API请求: ${options.method || 'GET'} ${url}`);
    
    try {
        const data = await fetchWithRetry(url, config);
        console.log(`API响应成功: ${options.method || 'GET'} ${url}`);
        return data;
    } catch (error) {
        console.error(`API请求失败: ${options.method || 'GET'} ${url}`, error);
        throw error;
    }
}

// ==== HTTP方法封装 ====

/**
 * GET请求
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @returns {Promise<any>} 响应数据
 */
export function get(endpoint, options = {}) {
    return apiRequest(endpoint, {
        method: 'GET',
        ...options
    });
}

/**
 * POST请求
 * @param {string} endpoint - API端点
 * @param {any} data - 请求数据
 * @param {Object} options - 请求选项
 * @returns {Promise<any>} 响应数据
 */
export function post(endpoint, data = null, options = {}) {
    return apiRequest(endpoint, {
        method: 'POST',
        body: data ? JSON.stringify(data) : null,
        ...options
    });
}

/**
 * PUT请求
 * @param {string} endpoint - API端点
 * @param {any} data - 请求数据
 * @param {Object} options - 请求选项
 * @returns {Promise<any>} 响应数据
 */
export function put(endpoint, data = null, options = {}) {
    return apiRequest(endpoint, {
        method: 'PUT',
        body: data ? JSON.stringify(data) : null,
        ...options
    });
}

/**
 * DELETE请求
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @returns {Promise<any>} 响应数据
 */
export function deleteRequest(endpoint, options = {}) {
    return apiRequest(endpoint, {
        method: 'DELETE',
        ...options
    });
}

/**
 * PATCH请求
 * @param {string} endpoint - API端点
 * @param {any} data - 请求数据
 * @param {Object} options - 请求选项
 * @returns {Promise<any>} 响应数据
 */
export function patch(endpoint, data = null, options = {}) {
    return apiRequest(endpoint, {
        method: 'PATCH',
        body: data ? JSON.stringify(data) : null,
        ...options
    });
}

// ==== 特定API端点方法 ====

/**
 * 用户认证API
 */
export const authAPI = {
    /**
     * 用户登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise<Object>} 登录响应
     */
    login: (username, password) => post('/auth/login', { username, password }),
    
    /**
     * 用户注册
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @param {string} confirmPassword - 确认密码
     * @returns {Promise<Object>} 注册响应
     */
    register: (username, password, confirmPassword) => post('/auth/register', {
        username,
        password,
        confirm_password: confirmPassword || password  // 如果没有提供确认密码，使用原密码
    }),
    
    /**
     * 用户登出
     * @returns {Promise<Object>} 登出响应
     */
    logout: () => post('/auth/logout'),
    
    /**
     * 刷新令牌
     * @returns {Promise<Object>} 刷新响应
     */
    refresh: () => post('/auth/refresh')
};

/**
 * 用户管理API
 */
export const userAPI = {
    /**
     * 根据ID获取用户信息
     * @param {string} id - 用户ID (UUID格式)
     * @param {Object} options - 请求选项
     * @param {boolean} options.useCache - 是否使用缓存 (默认: true)
     * @param {number} options.retryCount - 重试次数 (默认: 3)
     * @param {number} options.retryDelay - 重试延迟毫秒数 (默认: 1000)
     * @returns {Promise<Object>} 用户详情
     */
    async getUser(id, options = {}) {
        // 参数验证
        if (!id) {
            throw new Error('用户ID不能为空');
        }

        // UUID格式验证
        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
        if (!uuidRegex.test(id)) {
            throw new Error('用户ID格式无效，必须是有效的UUID');
        }

        const {
            useCache = true,
            retryCount = 3,
            retryDelay = 1000
        } = options;

        // 缓存键
        const cacheKey = `user_${id}`;

        // 检查缓存
        if (useCache && this._cache && this._cache.has(cacheKey)) {
            const cached = this._cache.get(cacheKey);
            if (Date.now() - cached.timestamp < this._cacheTimeout) {
                console.log(`从缓存获取用户信息: ${id}`);
                return cached.data;
            }
        }

        // 重试逻辑
        let lastError;
        for (let attempt = 0; attempt <= retryCount; attempt++) {
            try {
                console.log(`获取用户信息 (尝试 ${attempt + 1}/${retryCount + 1}): ${id}`);

                const response = await get(`/users/${id}`);

                // 缓存成功的响应
                if (useCache && this._cache) {
                    this._cache.set(cacheKey, {
                        data: response,
                        timestamp: Date.now()
                    });
                }

                return response;

            } catch (error) {
                lastError = error;

                // 对于404和403错误，不进行重试
                if (error.status === 404 || error.status === 403) {
                    throw error;
                }

                // 最后一次尝试失败，抛出错误
                if (attempt === retryCount) {
                    break;
                }

                // 等待后重试
                console.warn(`获取用户信息失败，${retryDelay}ms后重试: ${error.message}`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }

        throw lastError;
    },

    /**
     * 获取当前用户信息
     * @returns {Promise<Object>} 当前用户详情
     */
    getCurrentUser: () => get('/users/me'),

    /**
     * 检查用户名是否可用
     * @param {string} username - 用户名
     * @returns {Promise<Object>} 可用性检查结果
     */
    checkUsername: (username) => get(`/users/check-username?username=${encodeURIComponent(username)}`),

    /**
     * 获取用户个人资料
     * @returns {Promise<Object>} 用户个人资料
     */
    getProfile: () => get('/users/profile'),

    /**
     * 更新用户个人资料
     * @param {Object} profileData - 个人资料数据
     * @returns {Promise<Object>} 更新结果
     */
    updateProfile: (profileData) => put('/users/profile', profileData),

    /**
     * 获取在线用户列表
     * @returns {Promise<Array>} 在线用户列表
     */
    getOnlineUsers: () => get('/online-users'),

    // 内部缓存实现
    _cache: new Map(),
    _cacheTimeout: 5 * 60 * 1000, // 5分钟缓存超时

    /**
     * 清除用户缓存
     * @param {string} id - 用户ID，如果不提供则清除所有缓存
     */
    clearCache(id = null) {
        if (id) {
            const cacheKey = `user_${id}`;
            this._cache.delete(cacheKey);
            console.log(`清除用户缓存: ${id}`);
        } else {
            this._cache.clear();
            console.log('清除所有用户缓存');
        }
    }
};

/**
 * 任务管理API
 */
export const taskAPI = {
    /**
     * 获取所有任务
     * @returns {Promise<Array>} 任务列表
     */
    fetchAll: () => get('/tasks'),

    /**
     * 获取单个任务
     * @param {string} id - 任务ID (UUID字符串)
     * @returns {Promise<Object>} 任务详情
     */
    fetchById: (id) => get(`/tasks/${id}`),

    /**
     * 创建新任务
     * @param {Object} taskData - 任务数据
     * @returns {Promise<Object>} 创建的任务
     */
    create: (taskData) => post('/tasks', taskData),

    /**
     * 更新任务
     * @param {string} id - 任务ID (UUID字符串)
     * @param {Object} taskData - 更新的任务数据
     * @returns {Promise<Object>} 更新的任务
     */
    update: (id, taskData) => put(`/tasks/${id}`, taskData),

    /**
     * 删除任务
     * @param {string} id - 任务ID (UUID字符串)
     * @returns {Promise<void>} 删除响应
     */
    delete: (id) => deleteRequest(`/tasks/${id}`)
};

/**
 * 聊天消息API
 *
 * 功能特性：
 * - 消息验证
 * - 消息队列管理
 * - 发送状态指示
 * - 失败重试机制
 * - 发送速率限制
 */
export const chatAPI = {
    // 消息队列
    _messageQueue: [],
    _isProcessingQueue: false,

    // 速率限制器
    _rateLimiter: {
        tokens: 10, // 令牌桶容量
        maxTokens: 10,
        refillRate: 2, // 每秒补充2个令牌
        lastRefill: Date.now(),

        // 检查是否可以发送消息
        canSend() {
            this.refill();
            if (this.tokens > 0) {
                this.tokens--;
                return true;
            }
            return false;
        },

        // 补充令牌
        refill() {
            const now = Date.now();
            const timePassed = (now - this.lastRefill) / 1000;
            const tokensToAdd = Math.floor(timePassed * this.refillRate);

            if (tokensToAdd > 0) {
                this.tokens = Math.min(this.maxTokens, this.tokens + tokensToAdd);
                this.lastRefill = now;
            }
        },

        // 重置速率限制器（用于测试）
        reset() {
            this.tokens = this.maxTokens;
            this.lastRefill = Date.now();
        }
    },

    // 搜索缓存
    _searchCache: new Map(),

    /**
     * 搜索消息
     * @param {Object} searchParams - 搜索参数
     * @param {string} searchParams.keyword - 搜索关键词
     * @param {string} [searchParams.message_type] - 消息类型过滤
     * @param {string} [searchParams.sender] - 发送者ID过滤
     * @param {string} [searchParams.room_id] - 聊天室ID过滤
     * @param {string} [searchParams.start_date] - 开始时间过滤（RFC3339格式）
     * @param {string} [searchParams.end_date] - 结束时间过滤（RFC3339格式）
     * @param {number} [searchParams.page=1] - 页码
     * @param {number} [searchParams.limit=20] - 每页限制数量
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @param {number} [options.cacheTimeout=300000] - 缓存超时时间（毫秒，默认5分钟）
     * @returns {Promise<Object>} 搜索结果
     */
    async searchMessages(searchParams, options = {}) {
        // 搜索操作使用更长的超时时间
        const searchTimeout = options.timeout || 30000; // 30秒搜索超时

        // 参数验证
        if (!searchParams || !searchParams.keyword) {
            throw new Error('搜索关键词不能为空');
        }

        if (searchParams.keyword.trim().length === 0) {
            throw new Error('搜索关键词不能为空白字符');
        }

        if (searchParams.keyword.length > 100) {
            throw new Error('搜索关键词长度不能超过100个字符');
        }

        const {
            useCache = true,
            cacheTimeout = 300000 // 5分钟缓存
        } = options;

        // 构建查询参数
        const queryParams = new URLSearchParams();

        // 必需参数
        queryParams.append('keyword', searchParams.keyword.trim());

        // 可选参数
        if (searchParams.message_type) {
            queryParams.append('message_type', searchParams.message_type);
        }
        if (searchParams.sender) {
            queryParams.append('sender', searchParams.sender);
        }
        if (searchParams.room_id) {
            queryParams.append('room_id', searchParams.room_id);
        }
        if (searchParams.start_date) {
            queryParams.append('start_date', searchParams.start_date);
        }
        if (searchParams.end_date) {
            queryParams.append('end_date', searchParams.end_date);
        }

        // 分页参数
        queryParams.append('page', (searchParams.page || 1).toString());
        queryParams.append('limit', (searchParams.limit || 20).toString());

        // 生成缓存键
        const cacheKey = `search_${queryParams.toString()}`;

        // 检查缓存
        if (useCache && this._searchCache.has(cacheKey)) {
            const cached = this._searchCache.get(cacheKey);
            if (Date.now() - cached.timestamp < cacheTimeout) {
                console.log('使用缓存的搜索结果:', cacheKey);
                return cached.data;
            } else {
                // 缓存过期，删除
                this._searchCache.delete(cacheKey);
            }
        }

        try {
            console.log('执行消息搜索:', searchParams);

            const response = await get(`/messages/search?${queryParams.toString()}`, {
                timeout: searchTimeout
            });

            // 缓存成功的响应
            if (useCache) {
                this._searchCache.set(cacheKey, {
                    data: response,
                    timestamp: Date.now()
                });

                // 清理过期缓存（简单的LRU策略）
                if (this._searchCache.size > 100) {
                    const oldestKey = this._searchCache.keys().next().value;
                    this._searchCache.delete(oldestKey);
                }
            }

            return response;

        } catch (error) {
            console.error('消息搜索失败:', error);
            throw error;
        }
    },

    /**
     * 清理搜索缓存
     * @param {string} [pattern] - 可选的缓存键模式，如果提供则只清理匹配的缓存
     */
    clearSearchCache(pattern = null) {
        if (pattern) {
            // 清理匹配模式的缓存
            for (const [key] of this._searchCache) {
                if (key.includes(pattern)) {
                    this._searchCache.delete(key);
                }
            }
            console.log(`已清理匹配模式 "${pattern}" 的搜索缓存`);
        } else {
            // 清理所有缓存
            this._searchCache.clear();
            console.log('已清理所有搜索缓存');
        }
    },

    /**
     * 发送聊天消息
     * @param {Object} messageData - 消息数据
     * @param {string} messageData.content - 消息内容
     * @param {Object} messageData.sender - 发送者信息
     * @param {string} messageData.sender.user_id - 发送者用户ID
     * @param {string} messageData.sender.username - 发送者用户名
     * @param {Object} options - 发送选项
     * @param {Function} options.onStatusChange - 状态变化回调函数
     * @param {number} options.retryCount - 重试次数 (默认: 3)
     * @param {number} options.retryDelay - 重试延迟毫秒数 (默认: 1000)
     * @param {boolean} options.useQueue - 是否使用消息队列 (默认: true)
     * @returns {Promise<Object>} 发送结果
     */
    async sendMessage(messageData, options = {}) {
        // 参数验证
        this._validateMessage(messageData);

        const {
            onStatusChange = () => {},
            retryCount = 3,
            retryDelay = 1000,
            useQueue = true
        } = options;

        // 速率限制检查
        if (!this._rateLimiter.canSend()) {
            const error = new Error('发送频率过快，请稍后再试');
            error.code = 'RATE_LIMITED';
            onStatusChange('rate_limited');
            throw error;
        }

        // 创建消息任务
        const messageTask = {
            id: Date.now() + Math.random(),
            messageData,
            options: { onStatusChange, retryCount, retryDelay },
            attempts: 0,
            maxAttempts: retryCount + 1
        };

        if (useQueue) {
            // 添加到消息队列
            this._messageQueue.push(messageTask);
            this._processQueue();

            // 返回Promise，等待队列处理完成
            return new Promise((resolve, reject) => {
                messageTask.resolve = resolve;
                messageTask.reject = reject;
            });
        } else {
            // 直接发送
            return this._sendMessageDirect(messageTask);
        }
    },

    /**
     * 验证消息数据
     * @param {Object} messageData - 消息数据
     * @private
     */
    _validateMessage(messageData) {
        if (!messageData) {
            throw new Error('消息数据不能为空');
        }

        // 验证消息内容
        if (!messageData.content || typeof messageData.content !== 'string') {
            throw new Error('消息内容不能为空');
        }

        if (messageData.content.trim().length === 0) {
            throw new Error('消息内容不能为空');
        }

        if (messageData.content.length > 10000) {
            throw new Error('消息内容不能超过10000个字符');
        }

        // 验证发送者信息
        if (!messageData.sender) {
            throw new Error('发送者信息不能为空');
        }

        if (!messageData.sender.user_id || typeof messageData.sender.user_id !== 'string') {
            throw new Error('发送者用户ID不能为空');
        }

        if (!messageData.sender.username || typeof messageData.sender.username !== 'string') {
            throw new Error('发送者用户名不能为空');
        }
    },

    /**
     * 处理消息队列
     * @private
     */
    async _processQueue() {
        if (this._isProcessingQueue || this._messageQueue.length === 0) {
            return;
        }

        this._isProcessingQueue = true;

        while (this._messageQueue.length > 0) {
            const messageTask = this._messageQueue.shift();

            try {
                const result = await this._sendMessageDirect(messageTask);
                if (messageTask.resolve) {
                    messageTask.resolve(result);
                }
            } catch (error) {
                if (messageTask.reject) {
                    messageTask.reject(error);
                }
            }
        }

        this._isProcessingQueue = false;
    },

    /**
     * 直接发送消息（不使用队列）
     * @param {Object} messageTask - 消息任务
     * @returns {Promise<Object>} 发送结果
     * @private
     */
    async _sendMessageDirect(messageTask) {
        const { messageData, options } = messageTask;
        const { onStatusChange, retryDelay } = options;

        let lastError;

        for (let attempt = 0; attempt < messageTask.maxAttempts; attempt++) {
            try {
                // 更新发送状态
                onStatusChange('sending');

                console.log(`发送消息 (尝试 ${attempt + 1}/${messageTask.maxAttempts}): ${messageData.content.substring(0, 50)}...`);

                // 转换为服务器期望的格式
                const serverMessageData = {
                    message_type: 'Text',
                    content: messageData.content,
                    sender: {
                        username: messageData.sender.username,
                        user_id: messageData.sender.user_id
                    },
                    timestamp: new Date().toISOString()
                };

                // 使用带认证的请求
                const response = await apiRequest('/chat/send', {
                    method: 'POST',
                    body: JSON.stringify(serverMessageData)
                });

                // 发送成功
                onStatusChange('sent');
                console.log('消息发送成功:', response);

                return response;

            } catch (error) {
                lastError = error;
                messageTask.attempts = attempt + 1;

                console.warn(`消息发送失败 (尝试 ${attempt + 1}/${messageTask.maxAttempts}): ${error.message}`);

                // 对于某些错误类型，不进行重试
                if (error.status === 400 || error.status === 401 || error.status === 403) {
                    onStatusChange('failed');
                    throw error;
                }

                // 最后一次尝试失败
                if (attempt === messageTask.maxAttempts - 1) {
                    onStatusChange('failed');
                    break;
                }

                // 等待后重试
                if (retryDelay > 0) {
                    await new Promise(resolve => {
                        // 使用原生setTimeout避免测试环境的mock问题
                        const timeoutFn = global.originalSetTimeout || setTimeout;
                        timeoutFn(resolve, retryDelay);
                    });
                }
            }
        }

        throw lastError;
    },

    /**
     * 获取消息队列状态
     * @returns {Object} 队列状态信息
     */
    getQueueStatus() {
        return {
            queueLength: this._messageQueue.length,
            isProcessing: this._isProcessingQueue,
            rateLimiterTokens: this._rateLimiter.tokens
        };
    },

    /**
     * 清空消息队列
     */
    clearQueue() {
        this._messageQueue.length = 0;
        this._isProcessingQueue = false;
        console.log('消息队列已清空');
    },

    /**
     * 在聊天界面显示消息 - 方案一快速修复
     * @param {Object} message - 消息对象
     * @param {string} message.content - 消息内容
     * @param {Object} message.sender - 发送者信息
     * @param {string} message.sender.username - 发送者用户名
     * @param {string} message.timestamp - 消息时间戳
     * @param {string} message.message_id - 消息ID
     */
    displayMessage(message) {
        const messagesContainer = document.getElementById('messagesContainer');
        if (!messagesContainer) {
            console.error('错误: 找不到聊天消息容器 #messagesContainer');
            return;
        }

        // 清除占位符文本（如果存在）
        if (messagesContainer.innerHTML.includes('🚀 连接到聊天大厅开始聊天...')) {
            messagesContainer.innerHTML = '';
        }

        // 创建消息元素
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';
        messageElement.dataset.messageId = message.message_id || 'unknown';

        // 格式化时间戳
        const timestamp = message.timestamp ? new Date(message.timestamp) : new Date();
        const timeString = timestamp.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        // 转义HTML内容防止XSS攻击
        const escapeHtml = (text) => {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        };

        // 构建消息HTML结构
        messageElement.innerHTML = `
            <div class="message-header">
                <span class="sender">${escapeHtml(message.sender?.username || '匿名用户')}</span>
                <span class="timestamp">${timeString}</span>
            </div>
            <div class="message-content">${escapeHtml(message.content || '')}</div>
        `;

        // 应用样式
        messageElement.style.cssText = `
            margin-bottom: 12px;
            padding: 8px 12px;
            background: #ffffff;
            border-radius: 8px;
            border-left: 3px solid #007bff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        `;

        // 添加到容器
        messagesContainer.appendChild(messageElement);

        // 滚动到底部
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        console.log('消息已显示在聊天界面:', message.content?.substring(0, 50) + '...');
    }
};

/**
 * 健康检查API
 *
 * 功能特性：
 * - 7个健康检查接口集成
 * - 批量健康检查
 * - 状态分类和图标系统
 * - 详细指标展示
 * - 缓存和重试机制
 */
export const healthAPI = {
    // 健康检查缓存
    _healthCache: new Map(),
    _cacheTimeout: 30000, // 30秒缓存超时

    /**
     * 基础健康检查
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @returns {Promise<Object>} 健康状态响应
     */
    async fetchBasicHealth(options = {}) {
        const { useCache = true } = options;
        const cacheKey = 'basic_health';

        // 检查缓存
        if (useCache && this._healthCache.has(cacheKey)) {
            const cached = this._healthCache.get(cacheKey);
            if (Date.now() - cached.timestamp < this._cacheTimeout) {
                console.log('使用缓存的基础健康检查结果');
                return cached.data;
            }
        }

        try {
            console.log('获取基础健康检查状态');
            const response = await get('/health');

            // 缓存响应
            if (useCache) {
                this._healthCache.set(cacheKey, {
                    data: response,
                    timestamp: Date.now()
                });
            }

            return response;
        } catch (error) {
            console.error('基础健康检查失败:', error);
            throw error;
        }
    },

    /**
     * 深度健康检查
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @returns {Promise<Object>} 深度健康状态响应
     */
    async fetchDeepHealth(options = {}) {
        const { useCache = true } = options;
        const cacheKey = 'deep_health';

        // 检查缓存
        if (useCache && this._healthCache.has(cacheKey)) {
            const cached = this._healthCache.get(cacheKey);
            if (Date.now() - cached.timestamp < this._cacheTimeout) {
                console.log('使用缓存的深度健康检查结果');
                return cached.data;
            }
        }

        try {
            console.log('获取深度健康检查状态');
            const response = await get('/health/deep');

            // 缓存响应
            if (useCache) {
                this._healthCache.set(cacheKey, {
                    data: response,
                    timestamp: Date.now()
                });
            }

            return response;
        } catch (error) {
            console.error('深度健康检查失败:', error);
            throw error;
        }
    },

    /**
     * 数据库健康检查
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @returns {Promise<Object>} 数据库健康状态响应
     */
    async fetchDatabaseHealth(options = {}) {
        const { useCache = true } = options;
        const cacheKey = 'database_health';

        // 检查缓存
        if (useCache && this._healthCache.has(cacheKey)) {
            const cached = this._healthCache.get(cacheKey);
            if (Date.now() - cached.timestamp < this._cacheTimeout) {
                console.log('使用缓存的数据库健康检查结果');
                return cached.data;
            }
        }

        try {
            console.log('获取数据库健康检查状态');
            const response = await get('/health/database');

            // 缓存响应
            if (useCache) {
                this._healthCache.set(cacheKey, {
                    data: response,
                    timestamp: Date.now()
                });
            }

            return response;
        } catch (error) {
            console.error('数据库健康检查失败:', error);
            throw error;
        }
    },

    /**
     * 数据库配置信息
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @returns {Promise<Object>} 数据库配置响应
     */
    async fetchDatabaseConfig(options = {}) {
        const { useCache = true } = options;
        const cacheKey = 'database_config';

        // 检查缓存
        if (useCache && this._healthCache.has(cacheKey)) {
            const cached = this._healthCache.get(cacheKey);
            if (Date.now() - cached.timestamp < this._cacheTimeout) {
                console.log('使用缓存的数据库配置信息');
                return cached.data;
            }
        }

        try {
            console.log('获取数据库配置信息');
            const response = await get('/health/database/config');

            // 缓存响应
            if (useCache) {
                this._healthCache.set(cacheKey, {
                    data: response,
                    timestamp: Date.now()
                });
            }

            return response;
        } catch (error) {
            console.error('获取数据库配置失败:', error);
            throw error;
        }
    },

    /**
     * 性能健康检查
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @returns {Promise<Object>} 性能健康状态响应
     */
    async fetchPerformanceHealth(options = {}) {
        const { useCache = true } = options;
        const cacheKey = 'performance_health';

        // 检查缓存
        if (useCache && this._healthCache.has(cacheKey)) {
            const cached = this._healthCache.get(cacheKey);
            if (Date.now() - cached.timestamp < this._cacheTimeout) {
                console.log('使用缓存的性能健康检查结果');
                return cached.data;
            }
        }

        try {
            console.log('获取性能健康检查状态');
            const response = await get('/performance/health');

            // 缓存响应
            if (useCache) {
                this._healthCache.set(cacheKey, {
                    data: response,
                    timestamp: Date.now()
                });
            }

            return response;
        } catch (error) {
            console.error('性能健康检查失败:', error);
            throw error;
        }
    },

    /**
     * 就绪检查
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=false] - 是否使用缓存（就绪检查通常不缓存）
     * @returns {Promise<Object>} 就绪状态响应
     */
    async fetchReadinessCheck(options = {}) {
        const { useCache = false } = options;
        const cacheKey = 'readiness_check';

        // 检查缓存
        if (useCache && this._healthCache.has(cacheKey)) {
            const cached = this._healthCache.get(cacheKey);
            if (Date.now() - cached.timestamp < this._cacheTimeout) {
                console.log('使用缓存的就绪检查结果');
                return cached.data;
            }
        }

        try {
            console.log('执行就绪检查');
            const response = await get('/performance/ready');

            // 缓存响应
            if (useCache) {
                this._healthCache.set(cacheKey, {
                    data: response,
                    timestamp: Date.now()
                });
            }

            return response;
        } catch (error) {
            console.error('就绪检查失败:', error);
            throw error;
        }
    },

    /**
     * 存活检查
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=false] - 是否使用缓存（存活检查通常不缓存）
     * @returns {Promise<Object>} 存活状态响应
     */
    async fetchLivenessCheck(options = {}) {
        const { useCache = false } = options;
        const cacheKey = 'liveness_check';

        // 检查缓存
        if (useCache && this._healthCache.has(cacheKey)) {
            const cached = this._healthCache.get(cacheKey);
            if (Date.now() - cached.timestamp < this._cacheTimeout) {
                console.log('使用缓存的存活检查结果');
                return cached.data;
            }
        }

        try {
            console.log('执行存活检查');
            const response = await get('/performance/live');

            // 缓存响应
            if (useCache) {
                this._healthCache.set(cacheKey, {
                    data: response,
                    timestamp: Date.now()
                });
            }

            return response;
        } catch (error) {
            console.error('存活检查失败:', error);
            throw error;
        }
    },

    /**
     * 批量健康检查
     * 同时执行所有7个健康检查接口
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @param {boolean} [options.parallel=true] - 是否并行执行
     * @returns {Promise<Object>} 批量健康检查结果
     */
    async fetchBatchHealthCheck(options = {}) {
        const { useCache = true, parallel = true } = options;

        console.log('开始批量健康检查');

        const healthChecks = [
            { name: 'basic', method: () => this.fetchBasicHealth({ useCache }) },
            { name: 'deep', method: () => this.fetchDeepHealth({ useCache }) },
            { name: 'database', method: () => this.fetchDatabaseHealth({ useCache }) },
            { name: 'database_config', method: () => this.fetchDatabaseConfig({ useCache }) },
            { name: 'performance', method: () => this.fetchPerformanceHealth({ useCache }) },
            { name: 'readiness', method: () => this.fetchReadinessCheck({ useCache }) },
            { name: 'liveness', method: () => this.fetchLivenessCheck({ useCache }) }
        ];

        const results = {
            timestamp: new Date().toISOString(),
            total_checks: healthChecks.length,
            successful_checks: 0,
            failed_checks: 0,
            checks: {},
            overall_status: 'unknown',
            execution_time_ms: 0
        };

        const startTime = Date.now();

        try {
            if (parallel) {
                // 并行执行所有检查
                const promises = healthChecks.map(async (check) => {
                    try {
                        const result = await check.method();
                        return { name: check.name, success: true, data: result, error: null };
                    } catch (error) {
                        return { name: check.name, success: false, data: null, error: error.message };
                    }
                });

                const checkResults = await Promise.all(promises);

                // 处理结果
                for (const result of checkResults) {
                    results.checks[result.name] = {
                        success: result.success,
                        data: result.data,
                        error: result.error,
                        status: result.success ? 'healthy' : 'unhealthy'
                    };

                    if (result.success) {
                        results.successful_checks++;
                    } else {
                        results.failed_checks++;
                    }
                }
            } else {
                // 串行执行检查
                for (const check of healthChecks) {
                    try {
                        const result = await check.method();
                        results.checks[check.name] = {
                            success: true,
                            data: result,
                            error: null,
                            status: 'healthy'
                        };
                        results.successful_checks++;
                    } catch (error) {
                        results.checks[check.name] = {
                            success: false,
                            data: null,
                            error: error.message,
                            status: 'unhealthy'
                        };
                        results.failed_checks++;
                    }
                }
            }

            // 计算总体状态
            const successRate = results.successful_checks / results.total_checks;
            if (successRate === 1.0) {
                results.overall_status = 'healthy';
            } else if (successRate >= 0.7) {
                results.overall_status = 'degraded';
            } else {
                results.overall_status = 'unhealthy';
            }

            results.execution_time_ms = Date.now() - startTime;

            console.log(`批量健康检查完成: ${results.successful_checks}/${results.total_checks} 成功, 总体状态: ${results.overall_status}`);
            return results;

        } catch (error) {
            results.execution_time_ms = Date.now() - startTime;
            results.overall_status = 'error';
            console.error('批量健康检查失败:', error);
            throw error;
        }
    },

    /**
     * 获取健康状态图标
     * @param {string} status - 健康状态 ('healthy', 'unhealthy', 'degraded', 'unknown')
     * @returns {Object} 图标信息
     */
    getStatusIcon(status) {
        const icons = {
            healthy: {
                icon: '✅',
                color: '#28a745',
                text: '健康',
                class: 'status-healthy'
            },
            unhealthy: {
                icon: '❌',
                color: '#dc3545',
                text: '不健康',
                class: 'status-unhealthy'
            },
            degraded: {
                icon: '⚠️',
                color: '#ffc107',
                text: '降级',
                class: 'status-degraded'
            },
            unknown: {
                icon: '❓',
                color: '#6c757d',
                text: '未知',
                class: 'status-unknown'
            },
            error: {
                icon: '💥',
                color: '#dc3545',
                text: '错误',
                class: 'status-error'
            }
        };

        return icons[status] || icons.unknown;
    },

    /**
     * 分类健康检查结果
     * @param {Object} healthData - 健康检查数据
     * @returns {Object} 分类结果
     */
    classifyHealthStatus(healthData) {
        if (!healthData || typeof healthData !== 'object') {
            return {
                category: 'unknown',
                severity: 'unknown',
                issues: ['无效的健康检查数据']
            };
        }

        const issues = [];
        let severity = 'healthy';

        // 检查基本状态
        if (healthData.status) {
            if (healthData.status === 'unhealthy') {
                severity = 'unhealthy';
                issues.push('系统状态不健康');
            } else if (healthData.status === 'degraded') {
                severity = 'degraded';
                issues.push('系统性能降级');
            }
        }

        // 检查服务状态
        if (healthData.services) {
            for (const [serviceName, serviceStatus] of Object.entries(healthData.services)) {
                if (typeof serviceStatus === 'string') {
                    if (serviceStatus === 'unhealthy') {
                        severity = 'unhealthy';
                        issues.push(`${serviceName}服务不健康`);
                    } else if (serviceStatus === 'degraded') {
                        if (severity === 'healthy') severity = 'degraded';
                        issues.push(`${serviceName}服务性能降级`);
                    }
                } else if (typeof serviceStatus === 'object' && serviceStatus.status) {
                    if (serviceStatus.status === 'unhealthy') {
                        severity = 'unhealthy';
                        issues.push(`${serviceName}服务不健康`);
                    } else if (serviceStatus.status === 'degraded') {
                        if (severity === 'healthy') severity = 'degraded';
                        issues.push(`${serviceName}服务性能降级`);
                    }
                }
            }
        }

        // 检查系统指标
        if (healthData.system) {
            const { memory_usage, cpu_usage, disk_usage } = healthData.system;

            if (memory_usage && memory_usage > 0.9) {
                severity = 'unhealthy';
                issues.push(`内存使用率过高: ${(memory_usage * 100).toFixed(1)}%`);
            } else if (memory_usage && memory_usage > 0.8) {
                if (severity === 'healthy') severity = 'degraded';
                issues.push(`内存使用率较高: ${(memory_usage * 100).toFixed(1)}%`);
            }

            if (cpu_usage && cpu_usage > 0.9) {
                severity = 'unhealthy';
                issues.push(`CPU使用率过高: ${(cpu_usage * 100).toFixed(1)}%`);
            } else if (cpu_usage && cpu_usage > 0.8) {
                if (severity === 'healthy') severity = 'degraded';
                issues.push(`CPU使用率较高: ${(cpu_usage * 100).toFixed(1)}%`);
            }

            if (disk_usage && disk_usage > 0.95) {
                severity = 'unhealthy';
                issues.push(`磁盘使用率过高: ${(disk_usage * 100).toFixed(1)}%`);
            } else if (disk_usage && disk_usage > 0.85) {
                if (severity === 'healthy') severity = 'degraded';
                issues.push(`磁盘使用率较高: ${(disk_usage * 100).toFixed(1)}%`);
            }
        }

        return {
            category: severity,
            severity: severity,
            issues: issues.length > 0 ? issues : ['系统运行正常'],
            icon: this.getStatusIcon(severity)
        };
    },

    /**
     * 清除健康检查缓存
     * @param {string} [pattern] - 可选的缓存键模式，如果提供则只清理匹配的缓存
     */
    clearHealthCache(pattern = null) {
        if (pattern) {
            // 清理匹配模式的缓存
            for (const [key] of this._healthCache) {
                if (key.includes(pattern)) {
                    this._healthCache.delete(key);
                }
            }
            console.log(`已清理匹配模式 "${pattern}" 的健康检查缓存`);
        } else {
            // 清理所有缓存
            this._healthCache.clear();
            console.log('已清理所有健康检查缓存');
        }
    },

    /**
     * 获取缓存状态
     * @returns {Object} 缓存状态信息
     */
    getCacheStatus() {
        const cacheEntries = Array.from(this._healthCache.entries()).map(([key, value]) => ({
            key,
            timestamp: value.timestamp,
            age: Date.now() - value.timestamp,
            expired: Date.now() - value.timestamp > this._cacheTimeout
        }));

        return {
            total_entries: this._healthCache.size,
            cache_timeout: this._cacheTimeout,
            entries: cacheEntries
        };
    },

    /**
     * 设置缓存超时时间
     * @param {number} timeout - 超时时间（毫秒）
     */
    setCacheTimeout(timeout) {
        if (typeof timeout !== 'number' || timeout < 0) {
            throw new Error('缓存超时时间必须是非负数');
        }
        this._cacheTimeout = timeout;
        console.log(`健康检查缓存超时时间已设置为: ${timeout}ms`);
    },

    /**
     * 格式化健康检查数据用于显示
     * @param {Object} healthData - 健康检查数据
     * @returns {Object} 格式化后的显示数据
     */
    formatHealthDataForDisplay(healthData) {
        if (!healthData) {
            return {
                status: '未知',
                timestamp: '无数据',
                details: []
            };
        }

        const classification = this.classifyHealthStatus(healthData);
        const details = [];

        // 基本信息
        if (healthData.version) {
            details.push({ label: '版本', value: healthData.version });
        }

        if (healthData.uptime) {
            const uptimeHours = Math.floor(healthData.uptime / 3600);
            const uptimeMinutes = Math.floor((healthData.uptime % 3600) / 60);
            details.push({
                label: '运行时间',
                value: `${uptimeHours}小时${uptimeMinutes}分钟`
            });
        }

        // 服务状态
        if (healthData.services) {
            for (const [serviceName, serviceStatus] of Object.entries(healthData.services)) {
                const status = typeof serviceStatus === 'string'
                    ? serviceStatus
                    : serviceStatus.status || '未知';
                const icon = this.getStatusIcon(status);

                details.push({
                    label: `${serviceName}服务`,
                    value: `${icon.icon} ${icon.text}`,
                    status: status
                });
            }
        }

        // 系统指标
        if (healthData.system) {
            const { memory_usage, cpu_usage, disk_usage } = healthData.system;

            if (memory_usage !== undefined) {
                details.push({
                    label: '内存使用率',
                    value: `${(memory_usage * 100).toFixed(1)}%`,
                    status: memory_usage > 0.8 ? 'warning' : 'normal'
                });
            }

            if (cpu_usage !== undefined) {
                details.push({
                    label: 'CPU使用率',
                    value: `${(cpu_usage * 100).toFixed(1)}%`,
                    status: cpu_usage > 0.8 ? 'warning' : 'normal'
                });
            }

            if (disk_usage !== undefined) {
                details.push({
                    label: '磁盘使用率',
                    value: `${(disk_usage * 100).toFixed(1)}%`,
                    status: disk_usage > 0.85 ? 'warning' : 'normal'
                });
            }
        }

        // 性能指标
        if (healthData.metrics) {
            const { requests_per_minute, error_rate, active_connections } = healthData.metrics;

            if (requests_per_minute !== undefined) {
                details.push({
                    label: '每分钟请求数',
                    value: requests_per_minute.toString()
                });
            }

            if (error_rate !== undefined) {
                details.push({
                    label: '错误率',
                    value: `${(error_rate * 100).toFixed(2)}%`,
                    status: error_rate > 0.05 ? 'warning' : 'normal'
                });
            }

            if (active_connections !== undefined) {
                details.push({
                    label: '活跃连接数',
                    value: active_connections.toString()
                });
            }
        }

        return {
            status: classification.severity,
            icon: classification.icon,
            timestamp: healthData.timestamp || new Date().toISOString(),
            issues: classification.issues,
            details: details
        };
    }
};

// ==== 数据库管理API ====
/**
 * 数据库管理API客户端
 * 提供数据库压力测试和连接池监控功能
 */
export const databaseManagementAPI = {
    /**
     * 运行数据库压力测试
     * @param {Object} config - 压力测试配置
     * @param {number} [config.concurrent_connections=10] - 并发连接数
     * @param {number} [config.test_duration=30] - 测试持续时间(秒)
     * @param {string} [config.test_type='read'] - 测试类型(read/write/mixed)
     * @returns {Promise<Object>} 压力测试结果
     */
    runStressTest: async (config = {}) => {
        console.log('🔥 开始数据库压力测试');

        // 设置默认配置
        const testConfig = {
            concurrent_connections: 10,
            test_duration: 30,
            test_type: 'read',
            ...config
        };

        // 验证配置参数
        if (testConfig.concurrent_connections < 1 || testConfig.concurrent_connections > 100) {
            throw new APIError('并发连接数必须在1-100之间', 400);
        }

        if (testConfig.test_duration < 5 || testConfig.test_duration > 300) {
            throw new APIError('测试持续时间必须在5-300秒之间', 400);
        }

        if (!['read', 'write', 'mixed'].includes(testConfig.test_type)) {
            throw new APIError('测试类型必须是read、write或mixed', 400);
        }

        try {
            const result = await post('/health/database/stress-test', testConfig);
            console.log('✅ 数据库压力测试完成');
            return result;
        } catch (error) {
            console.error('❌ 数据库压力测试失败:', error);
            throw error;
        }
    },

    /**
     * 获取数据库连接池状态
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 连接池状态信息
     */
    getPoolStatus: async (options = {}) => {
        console.log('📊 获取数据库连接池状态');

        try {
            const result = await get('/db/pool', options);
            console.log('✅ 数据库连接池状态获取成功');
            return result;
        } catch (error) {
            console.error('❌ 获取数据库连接池状态失败:', error);
            throw error;
        }
    },

    /**
     * 获取数据库连接池详细监控数据
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 详细监控数据
     */
    getDetailedPoolMonitoring: async (options = {}) => {
        console.log('📈 获取数据库连接池详细监控数据');

        try {
            // 并行获取多个相关数据
            const [poolStatus, healthStatus] = await Promise.all([
                get('/db/pool', options),
                get('/health/database', options)
            ]);

            const result = {
                pool_status: poolStatus,
                health_status: healthStatus,
                timestamp: new Date().toISOString(),
                monitoring_type: 'detailed'
            };

            console.log('✅ 数据库连接池详细监控数据获取成功');
            return result;
        } catch (error) {
            console.error('❌ 获取数据库连接池详细监控数据失败:', error);
            throw error;
        }
    }
};

// ==== 导出HTTP状态码常量 ====
export { HTTP_STATUS };
