/**
 * 认证模块 - 处理用户认证相关功能
 * 基于ES6模块化设计，遵循Clean Code JavaScript最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

// ==== 认证相关常量 ====
const AUTH_STORAGE_KEY = 'axum_auth';
const TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5分钟缓冲时间

// ==== 认证状态管理 ====
let authToken = null;
let currentUser = null;

/**
 * 保存认证信息到localStorage
 * @param {string} token - JWT令牌
 * @param {Object} user - 用户信息对象
 * @returns {boolean} 保存是否成功
 */
export function saveAuthToStorage(token, user) {
    try {
        const authData = { 
            token, 
            user, 
            timestamp: Date.now() 
        };
        localStorage.setItem(AUTH_STORAGE_KEY, JSON.stringify(authData));
        console.log('认证信息已保存到localStorage');
        return true;
    } catch (error) {
        console.warn('无法保存认证信息到localStorage:', error);
        return false;
    }
}

/**
 * 从localStorage加载认证信息
 * @returns {Object|null} 认证数据对象或null（如果不存在或已过期）
 */
export function loadAuthFromStorage() {
    try {
        const authData = localStorage.getItem(AUTH_STORAGE_KEY);
        if (!authData) return null;

        const parsed = JSON.parse(authData);
        if (isTokenExpired(parsed.token)) {
            clearAuthFromStorage();
            return null;
        }
        return parsed;
    } catch (error) {
        console.warn('加载认证信息失败:', error);
        clearAuthFromStorage();
        return null;
    }
}

/**
 * 清除localStorage中的认证信息
 */
export function clearAuthFromStorage() {
    try {
        localStorage.removeItem(AUTH_STORAGE_KEY);
        console.log('认证信息已从localStorage清除');
    } catch (error) {
        console.warn('清除认证信息失败:', error);
    }
}

/**
 * 检查JWT令牌是否过期
 * @param {string} token - JWT令牌
 * @returns {boolean} 令牌是否过期
 */
export function isTokenExpired(token) {
    if (!token) return true;
    
    try {
        const payload = JSON.parse(atob(token.split('.')[1]));
        const currentTime = Math.floor(Date.now() / 1000);
        const expiryTime = payload.exp - TOKEN_EXPIRY_BUFFER / 1000;
        
        return currentTime >= expiryTime;
    } catch (error) {
        console.warn('解析JWT令牌失败:', error);
        return true;
    }
}

/**
 * 设置当前认证状态
 * @param {string} token - JWT令牌
 * @param {Object} user - 用户信息
 */
export function setAuthState(token, user) {
    authToken = token;
    currentUser = user;
    
    // 保存到localStorage
    saveAuthToStorage(token, user);
    
    // 触发认证状态变化事件
    dispatchAuthStateChange(true, user);
}

/**
 * 清除当前认证状态
 */
export function clearAuthState() {
    authToken = null;
    currentUser = null;
    
    // 清除localStorage
    clearAuthFromStorage();
    
    // 触发认证状态变化事件
    dispatchAuthStateChange(false, null);
}

/**
 * 获取当前认证令牌
 * @returns {string|null} 当前认证令牌
 */
export function getAuthToken() {
    return authToken;
}

/**
 * 获取当前用户信息
 * @returns {Object|null} 当前用户信息
 */
export function getCurrentUser() {
    return currentUser;
}

/**
 * 检查用户是否已认证
 * @returns {boolean} 用户是否已认证
 */
export function isAuthenticated() {
    // 检查基本条件
    if (!authToken || !currentUser) {
        return false;
    }

    // 检查Token是否过期
    try {
        const isExpired = isTokenExpired(authToken);
        return !isExpired;
    } catch (error) {
        console.warn('检查Token过期状态时出错:', error);
        return false;
    }
}

/**
 * 初始化认证模块
 * 从localStorage恢复认证状态
 */
export function initializeAuth() {
    const savedAuth = loadAuthFromStorage();
    if (savedAuth && !isTokenExpired(savedAuth.token)) {
        authToken = savedAuth.token;
        currentUser = savedAuth.user;
        console.log('认证状态已从localStorage恢复');
        
        // 触发认证状态变化事件
        dispatchAuthStateChange(true, savedAuth.user);
    } else {
        console.log('无有效的保存认证状态');
    }
}

/**
 * 触发认证状态变化事件
 * @param {boolean} isAuth - 是否已认证
 * @param {Object|null} user - 用户信息
 */
function dispatchAuthStateChange(isAuth, user) {
    const event = new CustomEvent('authStateChange', {
        detail: { isAuthenticated: isAuth, user }
    });
    window.dispatchEvent(event);
}

/**
 * 监听认证状态变化
 * @param {Function} callback - 回调函数
 */
export function onAuthStateChange(callback) {
    window.addEventListener('authStateChange', (event) => {
        callback(event.detail.isAuthenticated, event.detail.user);
    });
}

/**
 * 移除认证状态变化监听器
 * @param {Function} callback - 回调函数
 */
export function offAuthStateChange(callback) {
    window.removeEventListener('authStateChange', callback);
}

// ==== 模块初始化 ====
// 页面加载时自动初始化认证状态
if (typeof window !== 'undefined') {
    // 确保DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeAuth);
    } else {
        initializeAuth();
    }
}
