/**
 * 任务21：统一的自动刷新管理器
 * 实现可配置的定时刷新机制，支持页面可见性检测、错误处理和性能优化
 * 
 * 功能特性：
 * - 可配置的刷新间隔（5秒、10秒、30秒、1分钟）
 * - 页面可见性检测（页面未激活时暂停刷新）
 * - 刷新状态指示器
 * - 错误处理和重试机制
 * - 权限控制集成
 * - 性能优化
 */

import { apiClient } from './api.js';

/**
 * 自动刷新管理器类
 */
export class AutoRefreshManager {
    constructor(options = {}) {
        // 基础配置
        this.options = {
            defaultInterval: options.defaultInterval || 10000, // 默认10秒
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 2000,
            enableVisibilityDetection: options.enableVisibilityDetection !== false,
            enablePermissionCheck: options.enablePermissionCheck !== false,
            ...options
        };

        // 状态管理
        this.state = {
            isActive: false,
            isPaused: false,
            currentInterval: this.options.defaultInterval,
            timer: null,
            retryCount: 0,
            lastUpdateTime: null,
            refreshCallbacks: new Set(),
            errorCallbacks: new Set(),
            statusCallbacks: new Set()
        };

        // 性能监控
        this.performance = {
            refreshCount: 0,
            totalRefreshTime: 0,
            averageRefreshTime: 0,
            errorCount: 0,
            lastRefreshDuration: 0
        };

        // 初始化
        this.initializeVisibilityHandling();
        this.initializeCleanup();
        this.initializeStatusIndicator();
        
        console.log('自动刷新管理器已初始化', this.options);
    }

    /**
     * 初始化页面可见性检测
     */
    initializeVisibilityHandling() {
        if (!this.options.enableVisibilityDetection) {
            return;
        }

        // 检查浏览器支持
        if (typeof document.visibilityState === 'undefined') {
            console.warn('浏览器不支持Page Visibility API，跳过可见性检测');
            return;
        }

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pauseForVisibility();
            } else {
                this.resumeFromVisibility();
            }
        });

        console.log('页面可见性检测已启用');
    }

    /**
     * 初始化清理机制
     */
    initializeCleanup() {
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            this.destroy();
        });

        // 页面隐藏时暂停（移动端优化）
        window.addEventListener('pagehide', () => {
            this.pauseForVisibility();
        });

        // 页面显示时恢复（移动端优化）
        window.addEventListener('pageshow', () => {
            this.resumeFromVisibility();
        });
    }

    /**
     * 初始化状态指示器
     */
    initializeStatusIndicator() {
        this.statusElement = document.getElementById('autoRefreshStatus');
        this.lastUpdateElement = document.getElementById('lastUpdateTime');
        this.updateStatus('idle');
    }

    /**
     * 启动自动刷新
     */
    start() {
        if (this.state.isActive) {
            console.log('自动刷新已在运行中');
            return;
        }

        this.state.isActive = true;
        this.state.retryCount = 0;
        this.scheduleNextRefresh();
        this.updateStatus('active');
        
        console.log(`自动刷新已启动，间隔: ${this.state.currentInterval}ms`);
        this.notifyStatusChange('started');
    }

    /**
     * 停止自动刷新
     */
    stop() {
        this.state.isActive = false;
        this.clearTimer();
        this.updateStatus('stopped');
        
        console.log('自动刷新已停止');
        this.notifyStatusChange('stopped');
    }

    /**
     * 因页面可见性暂停
     */
    pauseForVisibility() {
        if (!this.state.isActive) {
            return;
        }

        this.state.isPaused = true;
        this.clearTimer();
        this.updateStatus('paused');
        
        console.log('页面不可见，自动刷新已暂停');
        this.notifyStatusChange('paused');
    }

    /**
     * 从页面可见性恢复
     */
    resumeFromVisibility() {
        if (!this.state.isActive || !this.state.isPaused) {
            return;
        }

        this.state.isPaused = false;
        this.scheduleNextRefresh();
        this.updateStatus('active');
        
        console.log('页面重新可见，自动刷新已恢复');
        this.notifyStatusChange('resumed');
    }

    /**
     * 设置刷新间隔
     */
    setInterval(interval) {
        const validIntervals = [5000, 10000, 30000, 60000]; // 5秒、10秒、30秒、1分钟
        
        if (!validIntervals.includes(interval)) {
            console.error('无效的刷新间隔:', interval);
            return false;
        }

        this.state.currentInterval = interval;
        
        // 如果正在运行，重新调度
        if (this.state.isActive && !this.state.isPaused) {
            this.clearTimer();
            this.scheduleNextRefresh();
        }
        
        console.log(`刷新间隔已更改为: ${interval}ms`);
        return true;
    }

    /**
     * 添加刷新回调
     */
    onRefresh(callback) {
        if (typeof callback === 'function') {
            this.state.refreshCallbacks.add(callback);
        }
    }

    /**
     * 移除刷新回调
     */
    offRefresh(callback) {
        this.state.refreshCallbacks.delete(callback);
    }

    /**
     * 添加错误回调
     */
    onError(callback) {
        if (typeof callback === 'function') {
            this.state.errorCallbacks.add(callback);
        }
    }

    /**
     * 添加状态变化回调
     */
    onStatusChange(callback) {
        if (typeof callback === 'function') {
            this.state.statusCallbacks.add(callback);
        }
    }

    /**
     * 立即执行一次刷新
     */
    async refreshNow() {
        if (this.state.isPaused) {
            console.log('刷新已暂停，跳过立即刷新');
            return;
        }

        await this.executeRefresh();
    }

    /**
     * 调度下一次刷新
     */
    scheduleNextRefresh() {
        this.clearTimer();
        
        this.state.timer = setTimeout(() => {
            this.executeRefresh();
        }, this.state.currentInterval);
    }

    /**
     * 执行刷新操作
     */
    async executeRefresh() {
        if (!this.state.isActive || this.state.isPaused) {
            return;
        }

        // 权限检查
        if (this.options.enablePermissionCheck && !await this.checkPermission()) {
            console.log('权限检查失败，跳过刷新');
            this.scheduleNextRefresh();
            return;
        }

        const startTime = performance.now();
        this.updateStatus('refreshing');

        try {
            // 执行所有刷新回调
            const refreshPromises = Array.from(this.state.refreshCallbacks).map(callback => {
                return Promise.resolve(callback());
            });

            await Promise.all(refreshPromises);

            // 更新性能统计
            const duration = performance.now() - startTime;
            this.updatePerformanceStats(duration, true);

            // 重置重试计数
            this.state.retryCount = 0;
            this.state.lastUpdateTime = new Date();
            
            this.updateStatus('success');
            this.updateLastUpdateTime();
            
            console.log(`刷新成功，耗时: ${duration.toFixed(2)}ms`);

        } catch (error) {
            console.error('刷新失败:', error);
            
            const duration = performance.now() - startTime;
            this.updatePerformanceStats(duration, false);
            
            await this.handleRefreshError(error);
        }

        // 调度下一次刷新
        if (this.state.isActive && !this.state.isPaused) {
            this.scheduleNextRefresh();
        }
    }

    /**
     * 处理刷新错误
     */
    async handleRefreshError(error) {
        this.state.retryCount++;
        this.updateStatus('error');

        // 通知错误回调
        this.state.errorCallbacks.forEach(callback => {
            try {
                callback(error, this.state.retryCount);
            } catch (callbackError) {
                console.error('错误回调执行失败:', callbackError);
            }
        });

        // 检查是否需要重试
        if (this.state.retryCount >= this.options.maxRetries) {
            console.error(`达到最大重试次数 (${this.options.maxRetries})，停止自动刷新`);
            this.stop();
            return;
        }

        // 延迟重试
        console.log(`${this.options.retryDelay}ms后进行第${this.state.retryCount}次重试`);
        await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));
    }

    /**
     * 权限检查
     */
    async checkPermission() {
        try {
            // 这里可以集成JWT权限验证
            const token = localStorage.getItem('authToken');
            if (!token) {
                return false;
            }

            // 可以添加更复杂的权限检查逻辑
            return true;
        } catch (error) {
            console.error('权限检查失败:', error);
            return false;
        }
    }

    /**
     * 更新性能统计
     */
    updatePerformanceStats(duration, success) {
        this.performance.refreshCount++;
        this.performance.lastRefreshDuration = duration;
        
        if (success) {
            this.performance.totalRefreshTime += duration;
            this.performance.averageRefreshTime = 
                this.performance.totalRefreshTime / (this.performance.refreshCount - this.performance.errorCount);
        } else {
            this.performance.errorCount++;
        }
    }

    /**
     * 更新状态显示
     */
    updateStatus(status) {
        const statusTexts = {
            idle: '空闲',
            active: '运行中',
            paused: '已暂停',
            stopped: '已停止',
            refreshing: '刷新中...',
            success: '刷新成功',
            error: '刷新失败'
        };

        if (this.statusElement) {
            this.statusElement.textContent = statusTexts[status] || status;
            this.statusElement.className = `refresh-status status-${status}`;
        }
    }

    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        if (this.lastUpdateElement && this.state.lastUpdateTime) {
            this.lastUpdateElement.textContent = 
                `最后更新: ${this.state.lastUpdateTime.toLocaleTimeString()}`;
        }
    }

    /**
     * 通知状态变化
     */
    notifyStatusChange(status) {
        this.state.statusCallbacks.forEach(callback => {
            try {
                callback(status, this.getStatus());
            } catch (error) {
                console.error('状态回调执行失败:', error);
            }
        });
    }

    /**
     * 清理定时器
     */
    clearTimer() {
        if (this.state.timer) {
            clearTimeout(this.state.timer);
            this.state.timer = null;
        }
    }

    /**
     * 获取当前状态
     */
    getStatus() {
        return {
            isActive: this.state.isActive,
            isPaused: this.state.isPaused,
            currentInterval: this.state.currentInterval,
            retryCount: this.state.retryCount,
            lastUpdateTime: this.state.lastUpdateTime,
            performance: { ...this.performance }
        };
    }

    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        return { ...this.performance };
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.stop();
        this.state.refreshCallbacks.clear();
        this.state.errorCallbacks.clear();
        this.state.statusCallbacks.clear();
        
        console.log('自动刷新管理器已销毁');
    }
}

// 创建全局实例
export const globalAutoRefreshManager = new AutoRefreshManager();

// 导出默认实例
export default globalAutoRefreshManager;
