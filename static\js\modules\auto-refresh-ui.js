/**
 * 任务21：自动刷新UI组件
 * 提供统一的自动刷新配置和控制界面
 */

import { globalRefreshConfigManager } from './refresh-config-manager.js';
import { globalAutoRefreshManager } from './auto-refresh-manager.js';

/**
 * 自动刷新UI管理器类
 */
export class AutoRefreshUI {
    constructor(containerId = 'autoRefreshControls') {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        
        if (!this.container) {
            console.warn(`自动刷新UI容器 ${containerId} 不存在`);
            return;
        }

        this.initializeUI();
        this.bindEvents();
        this.loadCurrentSettings();
        
        console.log('自动刷新UI已初始化');
    }

    /**
     * 初始化UI界面
     */
    initializeUI() {
        this.container.innerHTML = `
            <div class="auto-refresh-panel">
                <div class="panel-header">
                    <h3>自动刷新设置</h3>
                    <div class="global-toggle">
                        <label class="switch">
                            <input type="checkbox" id="globalAutoRefreshToggle">
                            <span class="slider"></span>
                        </label>
                        <span>启用自动刷新</span>
                    </div>
                </div>

                <div class="panel-content">
                    <!-- 全局设置 -->
                    <div class="settings-section">
                        <h4>全局设置</h4>
                        <div class="setting-item">
                            <label for="defaultInterval">默认刷新间隔:</label>
                            <select id="defaultInterval">
                                <option value="5000">5秒</option>
                                <option value="10000">10秒</option>
                                <option value="30000">30秒</option>
                                <option value="60000">1分钟</option>
                            </select>
                        </div>
                        <div class="setting-item">
                            <label for="maxRetries">最大重试次数:</label>
                            <input type="number" id="maxRetries" min="1" max="10" value="3">
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="enableVisibilityDetection" checked>
                                启用页面可见性检测
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="enablePermissionCheck" checked>
                                启用权限检查
                            </label>
                        </div>
                    </div>

                    <!-- 模块设置 -->
                    <div class="settings-section">
                        <h4>模块设置</h4>
                        <div id="moduleSettings"></div>
                    </div>

                    <!-- 状态显示 -->
                    <div class="settings-section">
                        <h4>状态信息</h4>
                        <div class="status-grid">
                            <div class="status-item">
                                <span class="label">当前状态:</span>
                                <span id="currentStatus" class="value">空闲</span>
                            </div>
                            <div class="status-item">
                                <span class="label">最后更新:</span>
                                <span id="lastUpdateDisplay" class="value">从未</span>
                            </div>
                            <div class="status-item">
                                <span class="label">刷新次数:</span>
                                <span id="refreshCount" class="value">0</span>
                            </div>
                            <div class="status-item">
                                <span class="label">平均耗时:</span>
                                <span id="averageTime" class="value">0ms</span>
                            </div>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="settings-section">
                        <div class="action-buttons">
                            <button id="refreshNowBtn" class="btn btn-primary">立即刷新</button>
                            <button id="resetConfigBtn" class="btn btn-secondary">重置配置</button>
                            <button id="exportConfigBtn" class="btn btn-secondary">导出配置</button>
                            <button id="importConfigBtn" class="btn btn-secondary">导入配置</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.initializeModuleSettings();
    }

    /**
     * 初始化模块设置
     */
    initializeModuleSettings() {
        const moduleSettingsContainer = document.getElementById('moduleSettings');
        const moduleConfigs = globalRefreshConfigManager.getAllModuleConfigs();

        let moduleHTML = '';
        for (const [moduleName, config] of Object.entries(moduleConfigs)) {
            const displayName = this.getModuleDisplayName(moduleName);
            moduleHTML += `
                <div class="module-setting" data-module="${moduleName}">
                    <div class="module-header">
                        <label class="switch">
                            <input type="checkbox" class="module-toggle" 
                                   data-module="${moduleName}" 
                                   ${config.enabled ? 'checked' : ''}>
                            <span class="slider"></span>
                        </label>
                        <span class="module-name">${displayName}</span>
                        <span class="module-priority priority-${config.priority}">${config.priority}</span>
                    </div>
                    <div class="module-controls">
                        <label>刷新间隔:</label>
                        <select class="module-interval" data-module="${moduleName}">
                            <option value="5000" ${config.interval === 5000 ? 'selected' : ''}>5秒</option>
                            <option value="10000" ${config.interval === 10000 ? 'selected' : ''}>10秒</option>
                            <option value="15000" ${config.interval === 15000 ? 'selected' : ''}>15秒</option>
                            <option value="20000" ${config.interval === 20000 ? 'selected' : ''}>20秒</option>
                            <option value="30000" ${config.interval === 30000 ? 'selected' : ''}>30秒</option>
                            <option value="60000" ${config.interval === 60000 ? 'selected' : ''}>1分钟</option>
                        </select>
                    </div>
                </div>
            `;
        }

        moduleSettingsContainer.innerHTML = moduleHTML;
    }

    /**
     * 获取模块显示名称
     */
    getModuleDisplayName(moduleName) {
        const displayNames = {
            healthDashboard: '健康检查仪表板',
            websocketMonitoring: 'WebSocket监控',
            cacheMonitoring: '缓存监控',
            databasePerformance: '数据库性能',
            userActivity: '用户活动'
        };
        return displayNames[moduleName] || moduleName;
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 全局开关
        const globalToggle = document.getElementById('globalAutoRefreshToggle');
        globalToggle?.addEventListener('change', (e) => {
            this.toggleGlobalAutoRefresh(e.target.checked);
        });

        // 默认间隔
        const defaultInterval = document.getElementById('defaultInterval');
        defaultInterval?.addEventListener('change', (e) => {
            this.updateDefaultInterval(parseInt(e.target.value));
        });

        // 最大重试次数
        const maxRetries = document.getElementById('maxRetries');
        maxRetries?.addEventListener('change', (e) => {
            this.updateMaxRetries(parseInt(e.target.value));
        });

        // 可见性检测
        const visibilityDetection = document.getElementById('enableVisibilityDetection');
        visibilityDetection?.addEventListener('change', (e) => {
            this.updateVisibilityDetection(e.target.checked);
        });

        // 权限检查
        const permissionCheck = document.getElementById('enablePermissionCheck');
        permissionCheck?.addEventListener('change', (e) => {
            this.updatePermissionCheck(e.target.checked);
        });

        // 模块开关
        this.container.addEventListener('change', (e) => {
            if (e.target.classList.contains('module-toggle')) {
                const moduleName = e.target.dataset.module;
                this.toggleModule(moduleName, e.target.checked);
            }
        });

        // 模块间隔
        this.container.addEventListener('change', (e) => {
            if (e.target.classList.contains('module-interval')) {
                const moduleName = e.target.dataset.module;
                const interval = parseInt(e.target.value);
                this.updateModuleInterval(moduleName, interval);
            }
        });

        // 操作按钮
        document.getElementById('refreshNowBtn')?.addEventListener('click', () => {
            this.refreshNow();
        });

        document.getElementById('resetConfigBtn')?.addEventListener('click', () => {
            this.resetConfig();
        });

        document.getElementById('exportConfigBtn')?.addEventListener('click', () => {
            this.exportConfig();
        });

        document.getElementById('importConfigBtn')?.addEventListener('click', () => {
            this.importConfig();
        });

        // 监听配置变化
        globalRefreshConfigManager.onConfigChange((changeEvent) => {
            this.handleConfigChange(changeEvent);
        });

        // 监听自动刷新状态变化
        globalAutoRefreshManager.onStatusChange((status, details) => {
            this.updateStatusDisplay(status, details);
        });
    }

    /**
     * 加载当前设置
     */
    loadCurrentSettings() {
        const globalConfig = globalRefreshConfigManager.getGlobalConfig();
        
        // 设置全局开关
        const globalToggle = document.getElementById('globalAutoRefreshToggle');
        if (globalToggle) {
            globalToggle.checked = globalConfig.globalEnabled;
        }

        // 设置默认间隔
        const defaultInterval = document.getElementById('defaultInterval');
        if (defaultInterval) {
            defaultInterval.value = globalConfig.defaultInterval;
        }

        // 设置最大重试次数
        const maxRetries = document.getElementById('maxRetries');
        if (maxRetries) {
            maxRetries.value = globalConfig.maxRetries;
        }

        // 设置可见性检测
        const visibilityDetection = document.getElementById('enableVisibilityDetection');
        if (visibilityDetection) {
            visibilityDetection.checked = globalConfig.enableVisibilityDetection;
        }

        // 设置权限检查
        const permissionCheck = document.getElementById('enablePermissionCheck');
        if (permissionCheck) {
            permissionCheck.checked = globalConfig.enablePermissionCheck;
        }
    }

    /**
     * 切换全局自动刷新
     */
    toggleGlobalAutoRefresh(enabled) {
        globalRefreshConfigManager.updateGlobalConfig({ globalEnabled: enabled });
        
        if (enabled) {
            globalAutoRefreshManager.start();
        } else {
            globalAutoRefreshManager.stop();
        }
    }

    /**
     * 更新默认间隔
     */
    updateDefaultInterval(interval) {
        globalRefreshConfigManager.updateGlobalConfig({ defaultInterval: interval });
        globalAutoRefreshManager.setInterval(interval);
    }

    /**
     * 更新最大重试次数
     */
    updateMaxRetries(maxRetries) {
        globalRefreshConfigManager.updateGlobalConfig({ maxRetries });
    }

    /**
     * 更新可见性检测设置
     */
    updateVisibilityDetection(enabled) {
        globalRefreshConfigManager.updateGlobalConfig({ enableVisibilityDetection: enabled });
    }

    /**
     * 更新权限检查设置
     */
    updatePermissionCheck(enabled) {
        globalRefreshConfigManager.updateGlobalConfig({ enablePermissionCheck: enabled });
    }

    /**
     * 切换模块
     */
    toggleModule(moduleName, enabled) {
        globalRefreshConfigManager.toggleModule(moduleName, enabled);
    }

    /**
     * 更新模块间隔
     */
    updateModuleInterval(moduleName, interval) {
        globalRefreshConfigManager.setModuleInterval(moduleName, interval);
    }

    /**
     * 立即刷新
     */
    async refreshNow() {
        const refreshBtn = document.getElementById('refreshNowBtn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.textContent = '刷新中...';
        }

        try {
            await globalAutoRefreshManager.refreshNow();
        } finally {
            if (refreshBtn) {
                refreshBtn.disabled = false;
                refreshBtn.textContent = '立即刷新';
            }
        }
    }

    /**
     * 重置配置
     */
    resetConfig() {
        if (confirm('确定要重置所有配置到默认值吗？')) {
            globalRefreshConfigManager.resetToDefaults();
            this.loadCurrentSettings();
            this.initializeModuleSettings();
        }
    }

    /**
     * 导出配置
     */
    exportConfig() {
        const config = globalRefreshConfigManager.exportConfig();
        const blob = new Blob([config], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = 'auto-refresh-config.json';
        a.click();
        
        URL.revokeObjectURL(url);
    }

    /**
     * 导入配置
     */
    importConfig() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const configJson = e.target.result;
                        if (globalRefreshConfigManager.importConfig(configJson)) {
                            this.loadCurrentSettings();
                            this.initializeModuleSettings();
                            alert('配置导入成功');
                        } else {
                            alert('配置导入失败');
                        }
                    } catch (error) {
                        alert('配置文件格式错误');
                    }
                };
                reader.readAsText(file);
            }
        };
        
        input.click();
    }

    /**
     * 处理配置变化
     */
    handleConfigChange(changeEvent) {
        console.log('配置已更新:', changeEvent);
        
        // 根据变化类型更新UI
        if (changeEvent.type === 'global') {
            this.loadCurrentSettings();
        } else if (changeEvent.type === 'module') {
            this.updateModuleDisplay(changeEvent.moduleName);
        }
    }

    /**
     * 更新模块显示
     */
    updateModuleDisplay(moduleName) {
        const moduleConfig = globalRefreshConfigManager.getModuleConfig(moduleName);
        const moduleElement = this.container.querySelector(`[data-module="${moduleName}"]`);
        
        if (moduleElement && moduleConfig) {
            const toggle = moduleElement.querySelector('.module-toggle');
            const interval = moduleElement.querySelector('.module-interval');
            
            if (toggle) toggle.checked = moduleConfig.enabled;
            if (interval) interval.value = moduleConfig.interval;
        }
    }

    /**
     * 更新状态显示
     */
    updateStatusDisplay(status, details) {
        const statusElement = document.getElementById('currentStatus');
        const lastUpdateElement = document.getElementById('lastUpdateDisplay');
        const refreshCountElement = document.getElementById('refreshCount');
        const averageTimeElement = document.getElementById('averageTime');

        if (statusElement) {
            statusElement.textContent = this.getStatusText(status);
            statusElement.className = `value status-${status}`;
        }

        if (details.lastUpdateTime && lastUpdateElement) {
            lastUpdateElement.textContent = details.lastUpdateTime.toLocaleTimeString();
        }

        if (details.performance) {
            if (refreshCountElement) {
                refreshCountElement.textContent = details.performance.refreshCount;
            }
            if (averageTimeElement) {
                averageTimeElement.textContent = `${details.performance.averageRefreshTime.toFixed(1)}ms`;
            }
        }
    }

    /**
     * 获取状态文本
     */
    getStatusText(status) {
        const statusTexts = {
            started: '运行中',
            stopped: '已停止',
            paused: '已暂停',
            resumed: '已恢复',
            refreshing: '刷新中',
            success: '刷新成功',
            error: '刷新失败'
        };
        return statusTexts[status] || status;
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    // 检查是否存在自动刷新控制容器
    if (document.getElementById('autoRefreshControls')) {
        window.autoRefreshUI = new AutoRefreshUI();
    }
});

export default AutoRefreshUI;
