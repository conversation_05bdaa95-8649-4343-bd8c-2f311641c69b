/**
 * 缓存监控API模块
 * 
 * 提供缓存统计、健康检查、连接池状态等API调用功能
 * 支持数据缓存、错误处理、重试机制
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-29
 */

import { get, post, APIError } from './api.js';

/**
 * 缓存监控API类
 * 
 * 功能特性：
 * - 缓存统计数据获取
 * - 健康状态监控
 * - 连接池状态查询
 * - 数据缓存和重试机制
 * - 错误处理和日志记录
 */
export class CacheMonitoringAPI {
    constructor() {
        // 内部缓存
        this._cache = new Map();
        this._cacheTimeout = 30 * 1000; // 30秒缓存超时
        
        // 重试配置
        this._retryConfig = {
            maxRetries: 3,
            retryDelay: 1000, // 1秒
            backoffMultiplier: 2
        };
        
        console.log('🚀 缓存监控API模块已初始化');
    }

    /**
     * 获取缓存统计信息
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @param {boolean} [options.enableRetry=true] - 是否启用重试
     * @returns {Promise<Object>} 缓存统计数据
     */
    async getCacheStats(options = {}) {
        const { useCache = true, enableRetry = true } = options;
        const cacheKey = 'cache_stats';
        
        // 检查缓存
        if (useCache && this._hasValidCache(cacheKey)) {
            console.log('📊 使用缓存的统计数据');
            return this._getFromCache(cacheKey);
        }
        
        try {
            console.log('📊 获取缓存统计数据...');
            const response = await this._requestWithRetry(
                () => get('/cache/stats'),
                enableRetry
            );
            
            const statsData = response.data;
            
            // 数据验证
            this._validateStatsData(statsData);
            
            // 缓存响应
            if (useCache) {
                this._setCache(cacheKey, statsData);
            }
            
            console.log('✅ 缓存统计数据获取成功');
            return statsData;
            
        } catch (error) {
            console.error('❌ 获取缓存统计失败:', error);
            throw new APIError('获取缓存统计数据失败', error);
        }
    }

    /**
     * 获取缓存健康状态
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @param {boolean} [options.enableRetry=true] - 是否启用重试
     * @returns {Promise<Object>} 健康状态数据
     */
    async getCacheHealth(options = {}) {
        const { useCache = true, enableRetry = true } = options;
        const cacheKey = 'cache_health';
        
        // 检查缓存
        if (useCache && this._hasValidCache(cacheKey)) {
            console.log('💚 使用缓存的健康状态数据');
            return this._getFromCache(cacheKey);
        }
        
        try {
            console.log('💚 获取缓存健康状态...');
            const response = await this._requestWithRetry(
                () => get('/cache/health'),
                enableRetry
            );
            
            const healthData = response.data;
            
            // 数据验证
            this._validateHealthData(healthData);
            
            // 缓存响应
            if (useCache) {
                this._setCache(cacheKey, healthData);
            }
            
            console.log('✅ 缓存健康状态获取成功');
            return healthData;
            
        } catch (error) {
            console.error('❌ 获取缓存健康状态失败:', error);
            throw new APIError('获取缓存健康状态失败', error);
        }
    }

    /**
     * 获取连接池状态
     * @param {Object} options - 请求选项
     * @param {boolean} [options.useCache=true] - 是否使用缓存
     * @param {boolean} [options.enableRetry=true] - 是否启用重试
     * @returns {Promise<Object>} 连接池状态数据
     */
    async getPoolStatus(options = {}) {
        const { useCache = true, enableRetry = true } = options;
        const cacheKey = 'pool_status';
        
        // 检查缓存
        if (useCache && this._hasValidCache(cacheKey)) {
            console.log('🔗 使用缓存的连接池状态数据');
            return this._getFromCache(cacheKey);
        }
        
        try {
            console.log('🔗 获取连接池状态...');
            const response = await this._requestWithRetry(
                () => get('/cache/pool'),
                enableRetry
            );
            
            const poolData = response.data;
            
            // 数据验证
            this._validatePoolData(poolData);
            
            // 缓存响应
            if (useCache) {
                this._setCache(cacheKey, poolData);
            }
            
            console.log('✅ 连接池状态获取成功');
            return poolData;
            
        } catch (error) {
            console.error('❌ 获取连接池状态失败:', error);
            throw new APIError('获取连接池状态失败', error);
        }
    }

    /**
     * 重置缓存统计
     * @param {Object} options - 请求选项
     * @param {boolean} [options.enableRetry=true] - 是否启用重试
     * @returns {Promise<Object>} 重置响应数据
     */
    async resetCacheStats(options = {}) {
        const { enableRetry = true } = options;
        
        try {
            console.log('🗑️ 重置缓存统计...');
            const response = await this._requestWithRetry(
                () => post('/cache/stats/reset'),
                enableRetry
            );
            
            // 清除相关缓存
            this._clearCache(['cache_stats']);
            
            console.log('✅ 缓存统计重置成功');
            return response.data;
            
        } catch (error) {
            console.error('❌ 重置缓存统计失败:', error);
            throw new APIError('重置缓存统计失败', error);
        }
    }

    /**
     * 批量获取所有监控数据
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 包含所有监控数据的对象
     */
    async getAllMonitoringData(options = {}) {
        try {
            console.log('📊 批量获取所有监控数据...');
            
            // 并行获取所有数据
            const [statsData, healthData, poolData] = await Promise.all([
                this.getCacheStats(options),
                this.getCacheHealth(options),
                this.getPoolStatus(options)
            ]);
            
            const allData = {
                stats: statsData,
                health: healthData,
                pool: poolData,
                timestamp: new Date().toISOString()
            };
            
            console.log('✅ 所有监控数据获取成功');
            return allData;
            
        } catch (error) {
            console.error('❌ 批量获取监控数据失败:', error);
            throw new APIError('批量获取监控数据失败', error);
        }
    }

    /**
     * 清除所有缓存
     */
    clearAllCache() {
        this._cache.clear();
        console.log('🗑️ 所有缓存已清除');
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 缓存使用统计
     */
    getCacheInfo() {
        return {
            size: this._cache.size,
            timeout: this._cacheTimeout,
            keys: Array.from(this._cache.keys())
        };
    }

    // ========== 私有方法 ==========

    /**
     * 带重试的请求方法
     * @private
     */
    async _requestWithRetry(requestFn, enableRetry = true) {
        if (!enableRetry) {
            return await requestFn();
        }
        
        let lastError;
        let delay = this._retryConfig.retryDelay;
        
        for (let attempt = 0; attempt <= this._retryConfig.maxRetries; attempt++) {
            try {
                return await requestFn();
            } catch (error) {
                lastError = error;
                
                if (attempt === this._retryConfig.maxRetries) {
                    break;
                }
                
                console.warn(`请求失败，${delay}ms后重试 (${attempt + 1}/${this._retryConfig.maxRetries}):`, error.message);
                await this._sleep(delay);
                delay *= this._retryConfig.backoffMultiplier;
            }
        }
        
        throw lastError;
    }

    /**
     * 睡眠函数
     * @private
     */
    _sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * 检查缓存是否有效
     * @private
     */
    _hasValidCache(key) {
        if (!this._cache.has(key)) {
            return false;
        }
        
        const cached = this._cache.get(key);
        return Date.now() - cached.timestamp < this._cacheTimeout;
    }

    /**
     * 从缓存获取数据
     * @private
     */
    _getFromCache(key) {
        return this._cache.get(key).data;
    }

    /**
     * 设置缓存
     * @private
     */
    _setCache(key, data) {
        this._cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
    }

    /**
     * 清除指定缓存
     * @private
     */
    _clearCache(keys) {
        keys.forEach(key => this._cache.delete(key));
    }

    /**
     * 验证统计数据格式
     * @private
     */
    _validateStatsData(data) {
        const requiredFields = ['hit_rate', 'total_operations', 'total_hits', 'total_misses', 'hot_tier', 'warm_tier', 'cold_tier'];

        for (const field of requiredFields) {
            if (!(field in data)) {
                throw new Error(`缺少必需字段: ${field}`);
            }
        }

        // 验证层级数据
        const tiers = ['hot_tier', 'warm_tier', 'cold_tier'];
        for (const tier of tiers) {
            const tierData = data[tier];
            const tierFields = ['reads', 'hits', 'writes', 'hit_rate'];

            for (const field of tierFields) {
                if (!(field in tierData)) {
                    throw new Error(`${tier}缺少必需字段: ${field}`);
                }
            }
        }
    }

    /**
     * 验证健康数据格式
     * @private
     */
    _validateHealthData(data) {
        const requiredFields = ['status', 'connection', 'performance', 'timestamp'];

        for (const field of requiredFields) {
            if (!(field in data)) {
                throw new Error(`健康数据缺少必需字段: ${field}`);
            }
        }

        // 验证连接信息
        const connectionFields = ['connected', 'pool_size', 'active_connections', 'idle_connections'];
        for (const field of connectionFields) {
            if (!(field in data.connection)) {
                throw new Error(`连接信息缺少必需字段: ${field}`);
            }
        }

        // 验证性能信息
        const performanceFields = ['avg_response_time_ms', 'max_response_time_ms', 'min_response_time_ms'];
        for (const field of performanceFields) {
            if (!(field in data.performance)) {
                throw new Error(`性能信息缺少必需字段: ${field}`);
            }
        }
    }

    /**
     * 验证连接池数据格式
     * @private
     */
    _validatePoolData(data) {
        const requiredFields = [
            'total_connections', 'active_connections', 'idle_connections',
            'max_connections', 'min_connections', 'acquire_success_count',
            'acquire_failure_count', 'avg_acquire_time_ms', 'success_rate',
            'is_healthy', 'last_health_check', 'connection_errors',
            'pool_utilization', 'reconnect_attempts'
        ];

        for (const field of requiredFields) {
            if (!(field in data)) {
                throw new Error(`连接池数据缺少必需字段: ${field}`);
            }
        }
    }
}

// 创建单例实例
const cacheMonitoringAPI = new CacheMonitoringAPI();

// 导出API方法
export const cacheAPI = {
    /**
     * 获取缓存统计信息
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 缓存统计数据
     */
    getStats: (options) => cacheMonitoringAPI.getCacheStats(options),

    /**
     * 获取缓存健康状态
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 健康状态数据
     */
    getHealth: (options) => cacheMonitoringAPI.getCacheHealth(options),

    /**
     * 获取连接池状态
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 连接池状态数据
     */
    getPoolStatus: (options) => cacheMonitoringAPI.getPoolStatus(options),

    /**
     * 重置缓存统计
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 重置响应数据
     */
    resetStats: (options) => cacheMonitoringAPI.resetCacheStats(options),

    /**
     * 批量获取所有监控数据
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 所有监控数据
     */
    getAllData: (options) => cacheMonitoringAPI.getAllMonitoringData(options),

    /**
     * 清除所有缓存
     */
    clearCache: () => cacheMonitoringAPI.clearAllCache(),

    /**
     * 获取缓存信息
     * @returns {Object} 缓存统计信息
     */
    getCacheInfo: () => cacheMonitoringAPI.getCacheInfo()
};

// 默认导出
export default cacheAPI;
