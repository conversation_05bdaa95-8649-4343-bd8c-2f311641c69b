/**
 * 健康检查仪表板模块
 * 任务36：集成健康检查API的前端界面
 * 任务21：集成统一的自动刷新管理器
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-25
 */

import { healthAPI, databaseManagementAPI } from './api.js';
import { AutoRefreshManager } from './auto-refresh-manager.js';
import { globalRefreshConfigManager } from './refresh-config-manager.js';

/**
 * 健康检查仪表板类
 */
class HealthDashboard {
    constructor() {
        // 移除旧的自动刷新相关属性，使用新的管理器
        this.lastUpdateTime = null;
        this.healthData = null;

        // 初始化自动刷新管理器
        this.initializeAutoRefreshManager();

        this.initializeElements();
        this.bindEvents();
        this.initializeDatabaseManagement();
        this.loadInitialData();
    }

    /**
     * 初始化自动刷新管理器
     */
    initializeAutoRefreshManager() {
        // 获取模块配置
        const moduleConfig = globalRefreshConfigManager.getModuleConfig('healthDashboard');

        // 创建专用的自动刷新管理器
        this.autoRefreshManager = new AutoRefreshManager({
            defaultInterval: moduleConfig?.interval || 30000,
            maxRetries: moduleConfig?.maxRetries || 3,
            enableVisibilityDetection: moduleConfig?.enableVisibilityDetection !== false,
            enablePermissionCheck: moduleConfig?.enablePermissionCheck !== false
        });

        // 注册刷新回调
        this.autoRefreshManager.onRefresh(async () => {
            await this.refreshData();
        });

        // 注册错误回调
        this.autoRefreshManager.onError((error, retryCount) => {
            console.error(`健康检查刷新失败 (重试 ${retryCount}):`, error);
            this.showError(`数据刷新失败: ${error.message}`);
        });

        // 注册状态变化回调
        this.autoRefreshManager.onStatusChange((status, details) => {
            this.updateAutoRefreshIndicator(status, details);
        });

        console.log('健康检查仪表板自动刷新管理器已初始化');
    }

    /**
     * 初始化DOM元素引用
     */
    initializeElements() {
        this.elements = {
            // 控制按钮
            refreshBtn: document.getElementById('refreshBtn'),
            batchCheckBtn: document.getElementById('batchCheckBtn'),
            clearCacheBtn: document.getElementById('clearCacheBtn'),
            autoRefreshToggle: document.getElementById('autoRefreshToggle'),
            
            // 状态卡片
            overallStatusCard: document.getElementById('overallStatusCard'),
            overallStatusIcon: document.getElementById('overallStatusIcon'),
            overallStatusValue: document.getElementById('overallStatusValue'),
            overallStatusDesc: document.getElementById('overallStatusDesc'),
            successfulChecks: document.getElementById('successfulChecks'),
            failedChecks: document.getElementById('failedChecks'),
            responseTime: document.getElementById('responseTime'),
            
            // 容器和指示器
            loadingIndicator: document.getElementById('loadingIndicator'),
            errorMessage: document.getElementById('errorMessage'),
            successMessage: document.getElementById('successMessage'),
            healthChecksContainer: document.getElementById('healthChecksContainer'),
            lastUpdateTime: document.getElementById('lastUpdateTime'),
            autoRefreshIndicator: document.getElementById('autoRefreshIndicator')
        };
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        this.elements.refreshBtn.addEventListener('click', () => this.refreshData());
        this.elements.batchCheckBtn.addEventListener('click', () => this.performBatchCheck());
        this.elements.clearCacheBtn.addEventListener('click', () => this.clearCache());

        // 更新自动刷新切换逻辑，使用新的管理器
        this.elements.autoRefreshToggle.addEventListener('change', (e) => {
            this.toggleAutoRefresh(e.target.checked);
        });

        // 添加刷新间隔选择器事件监听（如果存在）
        const intervalSelector = document.getElementById('refreshIntervalSelector');
        if (intervalSelector) {
            intervalSelector.addEventListener('change', (e) => {
                const interval = parseInt(e.target.value);
                this.setRefreshInterval(interval);
            });
        }
    }

    /**
     * 初始化数据库管理功能
     */
    initializeDatabaseManagement() {
        console.log('🗄️ 初始化数据库管理功能');

        // 获取数据库管理相关元素
        this.databaseElements = {
            getPoolStatusBtn: document.getElementById('getPoolStatusBtn'),
            runStressTestBtn: document.getElementById('runStressTestBtn'),
            poolStatusContent: document.getElementById('poolStatusContent'),
            stressTestResults: document.getElementById('stressTestResults'),
            stressTestContent: document.getElementById('stressTestContent'),
            concurrentConnections: document.getElementById('concurrentConnections'),
            testDuration: document.getElementById('testDuration'),
            testType: document.getElementById('testType')
        };

        // 绑定数据库管理事件
        if (this.databaseElements.getPoolStatusBtn) {
            this.databaseElements.getPoolStatusBtn.addEventListener('click', () => this.handleGetPoolStatus());
        }

        if (this.databaseElements.runStressTestBtn) {
            this.databaseElements.runStressTestBtn.addEventListener('click', () => this.handleRunStressTest());
        }

        console.log('✅ 数据库管理功能初始化完成');
    }

    /**
     * 处理获取连接池状态
     */
    async handleGetPoolStatus() {
        console.log('📊 获取数据库连接池状态');

        const btn = this.databaseElements.getPoolStatusBtn;
        const content = this.databaseElements.poolStatusContent;

        btn.disabled = true;
        btn.textContent = '🔄 获取中...';

        try {
            const poolData = await databaseManagementAPI.getDetailedPoolMonitoring();
            this.displayPoolStatus(poolData, content);
            this.showSuccess('数据库连接池状态获取成功');

        } catch (error) {
            console.error('❌ 获取连接池状态失败:', error);
            content.innerHTML = `<div style="color: #e74c3c; text-align: center; padding: 20px;">获取失败: ${error.message}</div>`;
            this.showError(`获取连接池状态失败: ${error.message}`);
        } finally {
            btn.disabled = false;
            btn.textContent = '📈 获取连接池状态';
        }
    }

    /**
     * 处理运行压力测试
     */
    async handleRunStressTest() {
        console.log('🔥 运行数据库压力测试');

        const btn = this.databaseElements.runStressTestBtn;
        const resultsContainer = this.databaseElements.stressTestResults;
        const resultsContent = this.databaseElements.stressTestContent;

        // 获取测试配置
        const config = {
            concurrent_connections: parseInt(this.databaseElements.concurrentConnections.value),
            test_duration: parseInt(this.databaseElements.testDuration.value),
            test_type: this.databaseElements.testType.value
        };

        // 验证配置
        if (config.concurrent_connections < 1 || config.concurrent_connections > 100) {
            this.showError('并发连接数必须在1-100之间');
            return;
        }

        if (config.test_duration < 5 || config.test_duration > 300) {
            this.showError('测试持续时间必须在5-300秒之间');
            return;
        }

        btn.disabled = true;
        btn.textContent = '🔄 测试中...';
        resultsContainer.style.display = 'none';

        try {
            const testResult = await databaseManagementAPI.runStressTest(config);
            this.displayStressTestResult(testResult, resultsContent);
            resultsContainer.style.display = 'block';
            this.showSuccess(`数据库压力测试完成！测试时长: ${config.test_duration}秒`);

        } catch (error) {
            console.error('❌ 数据库压力测试失败:', error);
            resultsContent.innerHTML = `<div style="color: #e74c3c; text-align: center; padding: 20px;">测试失败: ${error.message}</div>`;
            resultsContainer.style.display = 'block';
            this.showError(`数据库压力测试失败: ${error.message}`);
        } finally {
            btn.disabled = false;
            btn.textContent = '🚀 运行压力测试';
        }
    }

    /**
     * 显示连接池状态
     */
    displayPoolStatus(poolData, container) {
        const poolStatus = poolData.pool_status || {};
        const healthStatus = poolData.health_status || {};

        const html = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h5 style="color: #667eea; margin-bottom: 10px;">连接池信息</h5>
                    <div style="font-size: 14px; line-height: 1.6;">
                        <div><strong>活跃连接:</strong> ${poolStatus.active_connections || 'N/A'}</div>
                        <div><strong>最大连接:</strong> ${poolStatus.max_connections || 'N/A'}</div>
                        <div><strong>空闲连接:</strong> ${poolStatus.idle_connections || 'N/A'}</div>
                        <div><strong>等待连接:</strong> ${poolStatus.waiting_connections || 'N/A'}</div>
                    </div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h5 style="color: #28a745; margin-bottom: 10px;">健康状态</h5>
                    <div style="font-size: 14px; line-height: 1.6;">
                        <div><strong>状态:</strong> <span style="color: ${healthStatus.status === 'healthy' ? '#28a745' : '#e74c3c'}">${healthStatus.status || 'N/A'}</span></div>
                        <div><strong>响应时间:</strong> ${healthStatus.response_time_ms || 'N/A'}ms</div>
                        <div><strong>连接测试:</strong> ${healthStatus.connection_test ? '✅ 通过' : '❌ 失败'}</div>
                        <div><strong>更新时间:</strong> ${new Date(poolData.timestamp).toLocaleString()}</div>
                    </div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    /**
     * 显示压力测试结果
     */
    displayStressTestResult(testResult, container) {
        const result = testResult.result || {};
        const config = testResult.config || {};

        const html = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h5 style="color: #667eea; margin-bottom: 10px;">测试配置</h5>
                    <div style="font-size: 14px; line-height: 1.6;">
                        <div><strong>并发连接数:</strong> ${config.concurrent_connections}</div>
                        <div><strong>测试持续时间:</strong> ${config.test_duration}秒</div>
                        <div><strong>测试类型:</strong> ${config.test_type}</div>
                        <div><strong>开始时间:</strong> ${new Date(result.start_time).toLocaleString()}</div>
                    </div>
                </div>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                    <h5 style="color: #28a745; margin-bottom: 10px;">测试结果</h5>
                    <div style="font-size: 14px; line-height: 1.6;">
                        <div><strong>总请求数:</strong> ${result.total_requests || 'N/A'}</div>
                        <div><strong>成功请求:</strong> ${result.successful_requests || 'N/A'}</div>
                        <div><strong>失败请求:</strong> ${result.failed_requests || 'N/A'}</div>
                        <div><strong>成功率:</strong> ${result.success_rate ? (result.success_rate * 100).toFixed(2) + '%' : 'N/A'}</div>
                    </div>
                </div>
            </div>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                <h5 style="color: #e74c3c; margin-bottom: 10px;">性能指标</h5>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; font-size: 14px;">
                    <div><strong>平均响应时间:</strong> ${result.avg_response_time_ms || 'N/A'}ms</div>
                    <div><strong>最小响应时间:</strong> ${result.min_response_time_ms || 'N/A'}ms</div>
                    <div><strong>最大响应时间:</strong> ${result.max_response_time_ms || 'N/A'}ms</div>
                    <div><strong>QPS:</strong> ${result.queries_per_second || 'N/A'}</div>
                    <div><strong>并发性能:</strong> ${result.concurrent_performance || 'N/A'}</div>
                    <div><strong>错误率:</strong> ${result.error_rate ? (result.error_rate * 100).toFixed(2) + '%' : 'N/A'}</div>
                </div>
            </div>
        `;

        container.innerHTML = html;
    }

    /**
     * 加载初始数据
     */
    async loadInitialData() {
        await this.refreshData();
    }

    /**
     * 刷新健康检查数据
     */
    async refreshData() {
        this.showLoading();
        this.hideMessages();

        try {
            console.log('开始刷新健康检查数据');
            
            // 执行批量健康检查
            const batchResult = await healthAPI.fetchBatchHealthCheck({
                useCache: false,
                parallel: true
            });

            this.healthData = batchResult;
            this.updateUI(batchResult);
            this.showSuccess('健康检查数据刷新成功');
            
            console.log('健康检查数据刷新完成', batchResult);

        } catch (error) {
            console.error('刷新健康检查数据失败:', error);
            this.showError(`刷新失败: ${error.message}`);
        } finally {
            this.hideLoading();
            this.updateLastUpdateTime();
        }
    }

    /**
     * 执行批量检查
     */
    async performBatchCheck() {
        this.elements.batchCheckBtn.disabled = true;
        this.elements.batchCheckBtn.textContent = '🔄 检查中...';

        try {
            const batchResult = await healthAPI.fetchBatchHealthCheck({
                useCache: false,
                parallel: true
            });

            this.healthData = batchResult;
            this.updateUI(batchResult);
            this.showSuccess(`批量检查完成: ${batchResult.successful_checks}/${batchResult.total_checks} 成功`);

        } catch (error) {
            console.error('批量检查失败:', error);
            this.showError(`批量检查失败: ${error.message}`);
        } finally {
            this.elements.batchCheckBtn.disabled = false;
            this.elements.batchCheckBtn.textContent = '📊 批量检查';
        }
    }

    /**
     * 清除缓存
     */
    clearCache() {
        try {
            healthAPI.clearHealthCache();
            this.showSuccess('缓存已清除');
            console.log('健康检查缓存已清除');
        } catch (error) {
            console.error('清除缓存失败:', error);
            this.showError(`清除缓存失败: ${error.message}`);
        }
    }

    /**
     * 切换自动刷新
     */
    toggleAutoRefresh(enabled) {
        if (enabled) {
            this.autoRefreshManager.start();
            this.elements.autoRefreshIndicator.style.display = 'block';
            console.log('健康检查自动刷新已启用');
        } else {
            this.autoRefreshManager.stop();
            this.elements.autoRefreshIndicator.style.display = 'none';
            console.log('健康检查自动刷新已禁用');
        }
    }

    /**
     * 设置刷新间隔
     */
    setRefreshInterval(interval) {
        if (this.autoRefreshManager.setInterval(interval)) {
            console.log(`健康检查刷新间隔已更改为: ${interval}ms`);

            // 更新配置管理器
            globalRefreshConfigManager.updateModuleConfig('healthDashboard', { interval });
        }
    }

    /**
     * 更新自动刷新指示器
     */
    updateAutoRefreshIndicator(status, details) {
        if (!this.elements.autoRefreshIndicator) {
            return;
        }

        const indicator = this.elements.autoRefreshIndicator;
        const statusTexts = {
            started: '自动刷新运行中',
            stopped: '自动刷新已停止',
            paused: '自动刷新已暂停',
            resumed: '自动刷新已恢复',
            refreshing: '正在刷新...',
            success: '刷新成功',
            error: '刷新失败'
        };

        indicator.textContent = statusTexts[status] || status;
        indicator.className = `auto-refresh-indicator status-${status}`;

        // 更新最后更新时间
        if (details.lastUpdateTime) {
            this.lastUpdateTime = details.lastUpdateTime;
            this.updateLastUpdateTime();
        }
    }

    /**
     * 更新UI界面
     */
    updateUI(batchResult) {
        this.updateStatusOverview(batchResult);
        this.updateHealthChecksGrid(batchResult);
    }

    /**
     * 更新状态概览
     */
    updateStatusOverview(batchResult) {
        // 更新总体状态
        const statusIcon = healthAPI.getStatusIcon(batchResult.overall_status);
        this.elements.overallStatusIcon.textContent = statusIcon.icon;
        this.elements.overallStatusValue.textContent = statusIcon.text;
        this.elements.overallStatusDesc.textContent = this.getStatusDescription(batchResult.overall_status);
        
        // 更新状态卡片样式
        this.elements.overallStatusCard.className = `status-card ${batchResult.overall_status}`;

        // 更新统计数据
        this.elements.successfulChecks.textContent = batchResult.successful_checks;
        this.elements.failedChecks.textContent = batchResult.failed_checks;
        this.elements.responseTime.textContent = `${batchResult.execution_time_ms}ms`;
    }

    /**
     * 更新健康检查网格
     */
    updateHealthChecksGrid(batchResult) {
        const container = this.elements.healthChecksContainer;
        container.innerHTML = '';

        // 健康检查项配置
        const checkConfigs = [
            { key: 'basic', title: '基础健康检查', description: '系统基本状态检查' },
            { key: 'deep', title: '深度健康检查', description: '详细系统诊断' },
            { key: 'database', title: '数据库健康检查', description: '数据库连接状态' },
            { key: 'database_config', title: '数据库配置', description: '数据库配置信息' },
            { key: 'performance', title: '性能健康检查', description: '系统性能指标' },
            { key: 'readiness', title: '就绪检查', description: '服务就绪状态' },
            { key: 'liveness', title: '存活检查', description: '服务存活状态' }
        ];

        checkConfigs.forEach(config => {
            const checkData = batchResult.checks[config.key];
            if (checkData) {
                const card = this.createHealthCheckCard(config, checkData);
                container.appendChild(card);
            }
        });

        container.style.display = 'grid';
    }

    /**
     * 创建健康检查卡片
     */
    createHealthCheckCard(config, checkData) {
        const card = document.createElement('div');
        card.className = 'health-check-card';

        const statusIcon = healthAPI.getStatusIcon(checkData.status);
        
        card.innerHTML = `
            <div class="health-check-header">
                <h3 class="health-check-title">${config.title}</h3>
                <div class="health-check-status" style="color: ${statusIcon.color}">
                    <span>${statusIcon.icon}</span>
                    <span>${statusIcon.text}</span>
                </div>
            </div>
            <div class="health-details">
                <div class="health-detail-item">
                    <span class="detail-label">状态</span>
                    <span class="detail-value">${checkData.success ? '成功' : '失败'}</span>
                </div>
                <div class="health-detail-item">
                    <span class="detail-label">描述</span>
                    <span class="detail-value">${config.description}</span>
                </div>
                ${checkData.error ? `
                <div class="health-detail-item">
                    <span class="detail-label">错误</span>
                    <span class="detail-value" style="color: #dc3545">${checkData.error}</span>
                </div>
                ` : ''}
                ${checkData.data && checkData.data.timestamp ? `
                <div class="health-detail-item">
                    <span class="detail-label">检查时间</span>
                    <span class="detail-value">${new Date(checkData.data.timestamp).toLocaleString('zh-CN')}</span>
                </div>
                ` : ''}
            </div>
        `;

        return card;
    }

    /**
     * 获取状态描述
     */
    getStatusDescription(status) {
        const descriptions = {
            healthy: '所有系统组件运行正常',
            degraded: '部分系统组件性能降级',
            unhealthy: '系统存在严重问题',
            unknown: '系统状态未知',
            error: '检查过程中发生错误'
        };
        return descriptions[status] || descriptions.unknown;
    }

    /**
     * 显示加载状态
     */
    showLoading() {
        this.elements.loadingIndicator.style.display = 'block';
        this.elements.healthChecksContainer.style.display = 'none';
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        this.elements.loadingIndicator.style.display = 'none';
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        this.elements.errorMessage.textContent = message;
        this.elements.errorMessage.style.display = 'block';
        setTimeout(() => this.hideMessages(), 5000);
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.elements.successMessage.textContent = message;
        this.elements.successMessage.style.display = 'block';
        setTimeout(() => this.hideMessages(), 3000);
    }

    /**
     * 隐藏消息
     */
    hideMessages() {
        this.elements.errorMessage.style.display = 'none';
        this.elements.successMessage.style.display = 'none';
    }

    /**
     * 更新最后更新时间
     */
    updateLastUpdateTime() {
        this.lastUpdateTime = new Date();
        this.elements.lastUpdateTime.textContent = 
            `最后更新时间: ${this.lastUpdateTime.toLocaleString('zh-CN')}`;
    }

    /**
     * 销毁仪表板
     */
    destroy() {
        // 销毁自动刷新管理器
        if (this.autoRefreshManager) {
            this.autoRefreshManager.destroy();
        }

        // 移除事件监听器等清理工作
        console.log('健康检查仪表板已销毁');
    }
}

// 页面加载完成后初始化仪表板
document.addEventListener('DOMContentLoaded', () => {
    console.log('初始化健康检查仪表板');
    window.healthDashboard = new HealthDashboard();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.healthDashboard) {
        window.healthDashboard.destroy();
    }
});
