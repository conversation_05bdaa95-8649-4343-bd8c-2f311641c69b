/**
 * 在线用户管理模块
 * 基于ES6模块化设计，遵循Clean Code JavaScript最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-25
 */

import { wsManager, onMessage, MESSAGE_TYPE } from './websocket.js';
import { createElement, addClass, removeClass, escapeHtml, formatTimestamp } from './ui.js';

// ==== 在线用户状态管理 ====
class OnlineUsersManager {
    constructor() {
        this.users = new Map(); // 用户ID -> 用户信息
        this.container = null;
        this.isVisible = false;
        this.updateCallbacks = new Set();
        
        this.init();
    }

    /**
     * 初始化在线用户管理器
     */
    init() {
        console.log('初始化在线用户管理器');

        // 🔧 修复：延迟注册消息监听器，确保WebSocket管理器已完全初始化
        this.registerMessageHandlers();

        // 创建UI容器
        this.createContainer();
    }

    /**
     * 注册WebSocket消息处理器
     */
    registerMessageHandlers() {
        // 延迟注册，确保WebSocket管理器已初始化
        const registerHandlers = () => {
            try {
                console.log('注册在线用户消息处理器');

                // 监听用户列表响应消息
                onMessage(MESSAGE_TYPE.USERS_LIST, (message) => {
                    console.log('在线用户管理器收到USERS_LIST消息');
                    this.handleUsersListMessage(message);
                });

                // 监听用户加入/离开消息
                onMessage(MESSAGE_TYPE.USER_JOINED, (message) => {
                    this.handleUserJoined(message);
                });

                onMessage(MESSAGE_TYPE.USER_LEFT, (message) => {
                    this.handleUserLeft(message);
                });

                console.log('在线用户消息处理器注册完成');
            } catch (error) {
                console.error('注册在线用户消息处理器失败:', error);
                // 如果失败，再次延迟尝试
                setTimeout(registerHandlers, 1000);
            }
        };

        // 立即尝试注册，如果失败则延迟重试
        registerHandlers();
    }

    /**
     * 创建在线用户列表容器
     */
    createContainer() {
        // 查找或创建容器
        this.container = document.getElementById('online-users-container');
        
        if (!this.container) {
            this.container = createElement('div', {
                id: 'online-users-container',
                className: 'online-users-container'
            });

            // 添加到页面中（假设有一个侧边栏容器）
            const sidebar = document.querySelector('.sidebar') || document.body;
            sidebar.appendChild(this.container);
        }

        // 创建标题和用户列表
        this.container.innerHTML = `
            <div class="online-users-header">
                <h3>在线用户 (<span id="online-users-count">0</span>)</h3>
                <button id="refresh-users-btn" class="refresh-btn" title="刷新用户列表">
                    🔄
                </button>
            </div>
            <div class="online-users-list" id="online-users-list">
                <div class="loading-message">正在加载用户列表...</div>
            </div>
        `;

        // 绑定刷新按钮事件
        const refreshBtn = document.getElementById('refresh-users-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                this.requestUsersList();
            });
        }

        console.log('在线用户容器已创建');
    }

    /**
     * 处理用户列表消息
     * @param {Object} message - 用户列表消息
     */
    handleUsersListMessage(message) {
        console.log('收到用户列表消息:', message);
        console.log('消息数据结构:', JSON.stringify(message, null, 2));

        try {
            // 解析用户列表数据
            let usersData;
            if (message.data && message.data.users) {
                usersData = message.data.users;
                console.log('从message.data.users提取用户数据:', usersData);
            } else if (message.users) {
                usersData = message.users;
                console.log('从message.users提取用户数据:', usersData);
            } else if (message.data && Array.isArray(message.data)) {
                usersData = message.data;
                console.log('从message.data提取用户数组:', usersData);
            } else {
                console.warn('用户列表消息格式不正确:', message);
                console.warn('尝试使用空数组作为默认值');
                usersData = [];
            }

            // 更新用户列表
            this.updateUsersList(usersData);

            // 🔧 修复：同时更新聊天大厅中的在线用户显示
            this.updateChatHallOnlineUsers(usersData);

        } catch (error) {
            console.error('处理用户列表消息失败:', error);
        }
    }

    /**
     * 处理用户加入消息
     * @param {Object} message - 用户加入消息
     */
    handleUserJoined(message) {
        console.log('用户加入:', message);
        
        if (message.user_id && message.username) {
            const userInfo = {
                user_id: message.user_id,
                username: message.username,
                connected_at: new Date().toISOString(),
                session_type: message.session_type || 'chat'
            };
            
            this.addUser(userInfo);
        }
    }

    /**
     * 处理用户离开消息
     * @param {Object} message - 用户离开消息
     */
    handleUserLeft(message) {
        console.log('用户离开:', message);
        
        if (message.user_id) {
            this.removeUser(message.user_id);
        }
    }

    /**
     * 更新用户列表
     * @param {Array} usersData - 用户数据数组
     */
    updateUsersList(usersData) {
        console.log('更新用户列表，用户数量:', usersData.length);
        
        // 清空当前用户列表
        this.users.clear();
        
        // 添加新用户
        usersData.forEach(user => {
            this.users.set(user.user_id, user);
        });
        
        // 重新渲染UI
        this.renderUsersList();
        
        // 通知回调函数
        this.notifyUpdateCallbacks();
    }

    /**
     * 添加用户
     * @param {Object} userInfo - 用户信息
     */
    addUser(userInfo) {
        this.users.set(userInfo.user_id, userInfo);
        this.renderUsersList();
        this.notifyUpdateCallbacks();
    }

    /**
     * 移除用户
     * @param {string} userId - 用户ID
     */
    removeUser(userId) {
        if (this.users.delete(userId)) {
            this.renderUsersList();
            this.notifyUpdateCallbacks();
        }
    }

    /**
     * 渲染用户列表UI
     */
    renderUsersList() {
        const listContainer = document.getElementById('online-users-list');
        const countElement = document.getElementById('online-users-count');

        if (!listContainer) return;

        const userCount = this.users.size;

        // 更新用户数量
        if (countElement) {
            countElement.textContent = userCount;
        }

        // 如果没有用户，显示空状态
        if (userCount === 0) {
            listContainer.innerHTML = '<div class="empty-message">暂无在线用户</div>';
            return;
        }

        // 生成用户列表HTML
        const usersArray = Array.from(this.users.values());
        const usersHtml = usersArray.map(user => this.createUserItemHtml(user)).join('');

        listContainer.innerHTML = usersHtml;

        console.log('用户列表UI已更新，显示', userCount, '个用户');
    }

    /**
     * 更新聊天大厅中的在线用户显示
     * @param {Array} usersData - 用户数据数组
     */
    updateChatHallOnlineUsers(usersData) {
        console.log('更新聊天大厅在线用户显示，用户数量:', usersData.length);

        // 更新聊天大厅中的用户计数器
        const chatUserCount = document.getElementById('userCount');
        if (chatUserCount) {
            chatUserCount.textContent = usersData.length;
            console.log('聊天大厅用户计数器已更新:', usersData.length);
        }

        // 更新认证区域的在线用户计数器
        const authUserCount = document.getElementById('onlineUsersCount');
        if (authUserCount) {
            authUserCount.textContent = usersData.length;
            console.log('认证区域用户计数器已更新:', usersData.length);
        }

        // 更新聊天大厅的在线用户列表
        const onlineUsersContainer = document.getElementById('onlineUsers');
        if (onlineUsersContainer) {
            if (usersData.length === 0) {
                onlineUsersContainer.innerHTML = `
                    <div style="text-align: center; color: #7f8c8d; padding: 20px;">
                        暂无在线用户
                    </div>
                `;
            } else {
                const usersHtml = usersData.map(user => `
                    <div class="user-item">
                        <div>
                            <strong>${this.escapeHtml(user.username)}</strong>
                            <div style="font-size: 0.8em; color: #7f8c8d;">
                                ${this.formatTimestamp(user.connected_at)}
                            </div>
                        </div>
                    </div>
                `).join('');

                onlineUsersContainer.innerHTML = usersHtml;
            }
            console.log('聊天大厅在线用户列表已更新');
        }
    }

    /**
     * 创建用户项HTML
     * @param {Object} user - 用户信息
     * @returns {string} 用户项HTML
     */
    createUserItemHtml(user) {
        const connectedTime = formatTimestamp(user.connected_at);
        const sessionTypeClass = `session-${user.session_type}`;
        
        return `
            <div class="user-item ${sessionTypeClass}" data-user-id="${escapeHtml(user.user_id)}">
                <div class="user-avatar">
                    <span class="avatar-text">${escapeHtml(user.username.charAt(0).toUpperCase())}</span>
                </div>
                <div class="user-info">
                    <div class="user-name">${escapeHtml(user.username)}</div>
                    <div class="user-status">
                        <span class="session-type">${escapeHtml(user.session_type)}</span>
                        <span class="connected-time" title="连接时间: ${connectedTime}">${connectedTime}</span>
                    </div>
                </div>
                <div class="user-actions">
                    <button class="action-btn chat-btn" title="发起私聊" data-action="chat" data-user-id="${escapeHtml(user.user_id)}">
                        💬
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 请求用户列表
     */
    requestUsersList() {
        console.log('请求获取在线用户列表');
        
        // 显示加载状态
        const listContainer = document.getElementById('online-users-list');
        if (listContainer) {
            listContainer.innerHTML = '<div class="loading-message">正在刷新用户列表...</div>';
        }
        
        // 发送请求
        if (wsManager && wsManager.requestOnlineUsers) {
            wsManager.requestOnlineUsers();
        }
    }

    /**
     * 显示/隐藏在线用户列表
     */
    toggle() {
        this.isVisible = !this.isVisible;
        
        if (this.container) {
            if (this.isVisible) {
                removeClass(this.container, 'hidden');
                addClass(this.container, 'visible');
                // 显示时自动请求最新用户列表
                this.requestUsersList();
            } else {
                removeClass(this.container, 'visible');
                addClass(this.container, 'hidden');
            }
        }
    }

    /**
     * 显示在线用户列表
     */
    show() {
        if (!this.isVisible) {
            this.toggle();
        }
    }

    /**
     * 隐藏在线用户列表
     */
    hide() {
        if (this.isVisible) {
            this.toggle();
        }
    }

    /**
     * 获取当前在线用户数量
     * @returns {number} 在线用户数量
     */
    getUserCount() {
        return this.users.size;
    }

    /**
     * 获取所有在线用户
     * @returns {Array} 用户信息数组
     */
    getAllUsers() {
        return Array.from(this.users.values());
    }

    /**
     * 根据用户ID获取用户信息
     * @param {string} userId - 用户ID
     * @returns {Object|null} 用户信息或null
     */
    getUser(userId) {
        return this.users.get(userId) || null;
    }

    /**
     * 注册更新回调函数
     * @param {Function} callback - 回调函数
     */
    onUpdate(callback) {
        if (typeof callback === 'function') {
            this.updateCallbacks.add(callback);
        }
    }

    /**
     * 移除更新回调函数
     * @param {Function} callback - 回调函数
     */
    offUpdate(callback) {
        this.updateCallbacks.delete(callback);
    }

    /**
     * 通知所有更新回调函数
     */
    notifyUpdateCallbacks() {
        this.updateCallbacks.forEach(callback => {
            try {
                callback(this.getAllUsers(), this.getUserCount());
            } catch (error) {
                console.error('在线用户更新回调执行失败:', error);
            }
        });
    }

    /**
     * HTML转义函数
     * @param {string} text - 需要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 格式化时间戳
     * @param {string} timestamp - ISO时间戳
     * @returns {string} 格式化后的时间
     */
    formatTimestamp(timestamp) {
        try {
            const date = new Date(timestamp);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            console.warn('时间戳格式化失败:', error);
            return '未知时间';
        }
    }
}

// 创建全局实例
const onlineUsersManager = new OnlineUsersManager();

// ==== 导出API ====
export default onlineUsersManager;

// 🔧 修复：正确导出方法，避免解构赋值导致的undefined问题
export const showOnlineUsers = () => onlineUsersManager.show();
export const hideOnlineUsers = () => onlineUsersManager.hide();
export const toggleOnlineUsers = () => onlineUsersManager.toggle();
export const requestUsersList = () => onlineUsersManager.requestUsersList();
export const getUserCount = () => onlineUsersManager.getUserCount();
export const getAllUsers = () => onlineUsersManager.getAllUsers();
export const getUser = (userId) => onlineUsersManager.getUser(userId);
export const onUsersUpdate = (callback) => onlineUsersManager.onUpdate(callback);
export const offUsersUpdate = (callback) => onlineUsersManager.offUpdate(callback);
