/**
 * 前端性能优化模块
 * 基于2025年最新Web性能优化最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

// ==== 性能优化配置 ====
const PERFORMANCE_CONFIG = {
    // 资源预加载配置
    preload: {
        critical: ['./modules/api.js', './modules/auth.js'],
        fonts: [],
        images: []
    },
    
    // 懒加载配置
    lazyLoad: {
        images: true,
        modules: true,
        threshold: '50px'
    },
    
    // 缓存配置
    cache: {
        version: '1.0.0',
        staticAssets: 86400000, // 24小时
        apiResponses: 300000,   // 5分钟
        images: 604800000       // 7天
    },
    
    // 压缩配置
    compression: {
        enabled: true,
        threshold: 1024 // 1KB以上的资源进行压缩
    }
};

// ==== 性能监控类 ====
export class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.observers = new Map();
        this.isSupported = this.checkSupport();
        
        if (this.isSupported) {
            this.initializeObservers();
        }
    }
    
    /**
     * 检查浏览器支持情况
     */
    checkSupport() {
        return !!(
            window.PerformanceObserver &&
            window.performance &&
            window.performance.mark &&
            window.performance.measure
        );
    }
    
    /**
     * 初始化性能观察器
     */
    initializeObservers() {
        try {
            // 观察LCP (Largest Contentful Paint)
            const lcpObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                const lastEntry = entries[entries.length - 1];
                this.metrics.set('lcp', lastEntry.startTime);
                this.reportMetric('LCP', lastEntry.startTime);
            });
            lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
            this.observers.set('lcp', lcpObserver);
            
            // 观察FID (First Input Delay)
            const fidObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    const fid = entry.processingStart - entry.startTime;
                    this.metrics.set('fid', fid);
                    this.reportMetric('FID', fid);
                });
            });
            fidObserver.observe({ entryTypes: ['first-input'] });
            this.observers.set('fid', fidObserver);
            
            // 观察CLS (Cumulative Layout Shift)
            let clsValue = 0;
            const clsObserver = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                        this.metrics.set('cls', clsValue);
                        this.reportMetric('CLS', clsValue);
                    }
                });
            });
            clsObserver.observe({ entryTypes: ['layout-shift'] });
            this.observers.set('cls', clsObserver);
            
        } catch (error) {
            console.warn('性能监控初始化失败:', error);
        }
    }
    
    /**
     * 报告性能指标
     */
    reportMetric(name, value) {
        console.log(`📊 性能指标 ${name}: ${Math.round(value)}${name === 'CLS' ? '' : 'ms'}`);
        
        // 发送到分析服务（如果需要）
        if (window.gtag) {
            window.gtag('event', 'web_vitals', {
                event_category: 'Performance',
                event_label: name,
                value: Math.round(value)
            });
        }
    }
    
    /**
     * 获取所有性能指标
     */
    getMetrics() {
        return Object.fromEntries(this.metrics);
    }
    
    /**
     * 清理观察器
     */
    cleanup() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
    }
}

// ==== 资源预加载管理器 ====
export class ResourcePreloader {
    constructor() {
        this.preloadedResources = new Set();
    }
    
    /**
     * 预加载关键资源
     */
    preloadCriticalResources() {
        // 预加载关键CSS
        this.preloadCSS('./css/modules.css');
        
        // 预加载关键JavaScript模块
        PERFORMANCE_CONFIG.preload.critical.forEach(module => {
            this.preloadModule(module);
        });
        
        // 预加载字体
        PERFORMANCE_CONFIG.preload.fonts.forEach(font => {
            this.preloadFont(font);
        });
    }
    
    /**
     * 预加载CSS文件
     */
    preloadCSS(href) {
        if (this.preloadedResources.has(href)) return;
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        link.onload = () => {
            link.rel = 'stylesheet';
        };
        document.head.appendChild(link);
        
        this.preloadedResources.add(href);
    }
    
    /**
     * 预加载JavaScript模块
     */
    preloadModule(src) {
        if (this.preloadedResources.has(src)) return;
        
        const link = document.createElement('link');
        link.rel = 'modulepreload';
        link.href = src;
        document.head.appendChild(link);
        
        this.preloadedResources.add(src);
    }
    
    /**
     * 预加载字体
     */
    preloadFont(href) {
        if (this.preloadedResources.has(href)) return;
        
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'font';
        link.type = 'font/woff2';
        link.crossOrigin = 'anonymous';
        link.href = href;
        document.head.appendChild(link);
        
        this.preloadedResources.add(href);
    }
}

// ==== 懒加载管理器 ====
export class LazyLoader {
    constructor() {
        this.imageObserver = null;
        this.moduleCache = new Map();
        this.initializeImageLazyLoading();
    }
    
    /**
     * 初始化图片懒加载
     */
    initializeImageLazyLoading() {
        if (!('IntersectionObserver' in window)) {
            // 降级处理：立即加载所有图片
            this.loadAllImages();
            return;
        }
        
        this.imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    this.loadImage(img);
                    this.imageObserver.unobserve(img);
                }
            });
        }, {
            rootMargin: PERFORMANCE_CONFIG.lazyLoad.threshold
        });
        
        // 观察所有懒加载图片
        this.observeImages();
    }
    
    /**
     * 观察图片元素
     */
    observeImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => {
            this.imageObserver.observe(img);
        });
    }
    
    /**
     * 加载图片
     */
    loadImage(img) {
        const src = img.dataset.src;
        if (src) {
            img.src = src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
        }
    }
    
    /**
     * 降级处理：加载所有图片
     */
    loadAllImages() {
        const lazyImages = document.querySelectorAll('img[data-src]');
        lazyImages.forEach(img => this.loadImage(img));
    }
    
    /**
     * 动态导入模块（带缓存）
     */
    async loadModule(modulePath) {
        if (this.moduleCache.has(modulePath)) {
            return this.moduleCache.get(modulePath);
        }
        
        try {
            const module = await import(modulePath);
            this.moduleCache.set(modulePath, module);
            return module;
        } catch (error) {
            console.error(`模块加载失败: ${modulePath}`, error);
            throw error;
        }
    }
}

// ==== 缓存管理器 ====
export class CacheManager {
    constructor() {
        this.cacheName = `axum-app-v${PERFORMANCE_CONFIG.cache.version}`;
        this.isSupported = 'caches' in window;
    }
    
    /**
     * 初始化缓存
     */
    async initialize() {
        if (!this.isSupported) return;
        
        try {
            const cache = await caches.open(this.cacheName);
            
            // 缓存关键资源
            const criticalResources = [
                '/',
                './css/modules.css',
                './js/app.js',
                './js/modules/api.js',
                './js/modules/auth.js'
            ];
            
            await cache.addAll(criticalResources);
            console.log('✅ 关键资源已缓存');
            
        } catch (error) {
            console.warn('缓存初始化失败:', error);
        }
    }
    
    /**
     * 缓存API响应
     */
    async cacheResponse(request, response) {
        if (!this.isSupported) return;
        
        try {
            const cache = await caches.open(this.cacheName);
            await cache.put(request, response.clone());
        } catch (error) {
            console.warn('响应缓存失败:', error);
        }
    }
    
    /**
     * 获取缓存响应
     */
    async getCachedResponse(request) {
        if (!this.isSupported) return null;
        
        try {
            const cache = await caches.open(this.cacheName);
            return await cache.match(request);
        } catch (error) {
            console.warn('缓存获取失败:', error);
            return null;
        }
    }
    
    /**
     * 清理过期缓存
     */
    async cleanupOldCaches() {
        if (!this.isSupported) return;
        
        try {
            const cacheNames = await caches.keys();
            const oldCaches = cacheNames.filter(name => 
                name.startsWith('axum-app-') && name !== this.cacheName
            );
            
            await Promise.all(
                oldCaches.map(name => caches.delete(name))
            );
            
            if (oldCaches.length > 0) {
                console.log(`🗑️ 已清理 ${oldCaches.length} 个过期缓存`);
            }
        } catch (error) {
            console.warn('缓存清理失败:', error);
        }
    }
}

// ==== 性能优化器主类 ====
export class PerformanceOptimizer {
    constructor() {
        this.monitor = new PerformanceMonitor();
        this.preloader = new ResourcePreloader();
        this.lazyLoader = new LazyLoader();
        this.cacheManager = new CacheManager();
        
        this.initialized = false;
    }
    
    /**
     * 初始化性能优化
     */
    async initialize() {
        if (this.initialized) return;
        
        console.log('🚀 初始化性能优化器...');
        
        try {
            // 预加载关键资源
            this.preloader.preloadCriticalResources();
            
            // 初始化缓存
            await this.cacheManager.initialize();
            
            // 清理过期缓存
            await this.cacheManager.cleanupOldCaches();
            
            // 设置性能监控
            this.setupPerformanceMonitoring();
            
            // 优化字体加载
            this.optimizeFontLoading();
            
            // 优化图片加载
            this.optimizeImageLoading();
            
            this.initialized = true;
            console.log('✅ 性能优化器初始化完成');
            
        } catch (error) {
            console.error('性能优化器初始化失败:', error);
        }
    }
    
    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        // 监控页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'hidden') {
                this.reportPerformanceMetrics();
            }
        });
        
        // 监控页面卸载
        window.addEventListener('beforeunload', () => {
            this.reportPerformanceMetrics();
        });
    }
    
    /**
     * 优化字体加载
     */
    optimizeFontLoading() {
        // 使用font-display: swap优化字体加载
        const style = document.createElement('style');
        style.textContent = `
            @font-face {
                font-family: 'system-font';
                font-display: swap;
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 优化图片加载
     */
    optimizeImageLoading() {
        // 为新添加的图片设置懒加载
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const images = node.querySelectorAll ? 
                            node.querySelectorAll('img[data-src]') : 
                            (node.tagName === 'IMG' && node.dataset.src ? [node] : []);
                        
                        images.forEach(img => {
                            this.lazyLoader.imageObserver?.observe(img);
                        });
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    /**
     * 报告性能指标
     */
    reportPerformanceMetrics() {
        const metrics = this.monitor.getMetrics();
        console.log('📊 性能指标报告:', metrics);
        
        // 发送到分析服务
        if (window.gtag && Object.keys(metrics).length > 0) {
            window.gtag('event', 'performance_report', {
                event_category: 'Performance',
                custom_map: metrics
            });
        }
    }
    
    /**
     * 清理资源
     */
    cleanup() {
        this.monitor.cleanup();
    }
}

// ==== 导出单例实例 ====
export const performanceOptimizer = new PerformanceOptimizer();

// ==== 工具函数 ====

/**
 * 防抖函数
 */
export function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
export function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 检查是否为慢速连接
 */
export function isSlowConnection() {
    if ('connection' in navigator) {
        const connection = navigator.connection;
        return connection.effectiveType === 'slow-2g' || 
               connection.effectiveType === '2g' ||
               connection.saveData;
    }
    return false;
}

/**
 * 预加载关键路由
 */
export function preloadRoute(path) {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = path;
    document.head.appendChild(link);
}

// 自动初始化（如果在浏览器环境中）
if (typeof window !== 'undefined') {
    // 等待DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            performanceOptimizer.initialize();
        });
    } else {
        performanceOptimizer.initialize();
    }
}
