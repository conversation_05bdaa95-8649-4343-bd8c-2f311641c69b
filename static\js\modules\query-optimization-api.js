/**
 * 查询优化API模块
 * 
 * 功能：
 * - 单个查询优化分析
 * - 批量查询优化处理
 * - 数据库统计信息获取
 * - 索引推荐建议
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-29
 */

import { getAuthToken } from './auth.js';

// ==== API配置常量 ====
const API_BASE_URL = '/api';
const DEFAULT_TIMEOUT = 15000; // 查询优化可能需要更长时间
const RETRY_ATTEMPTS = 2; // 查询优化重试次数较少
const RETRY_DELAY = 2000; // 2秒重试延迟

// ==== 缓存配置 ====
const CACHE_DURATION = 60000; // 1分钟缓存
const cache = new Map();

/**
 * 查询优化API错误类
 */
class QueryOptimizationAPIError extends Error {
    constructor(message, status, response) {
        super(message);
        this.name = 'QueryOptimizationAPIError';
        this.status = status;
        this.response = response;
    }
}

/**
 * 创建带有认证头的请求配置
 * @param {Object} options - 请求选项
 * @returns {Object} 完整的请求配置
 */
function createRequestConfig(options = {}) {
    const config = {
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        },
        timeout: options.timeout || DEFAULT_TIMEOUT,
        ...options
    };

    // 添加JWT认证头
    const token = getAuthToken();
    if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
    }

    return config;
}

/**
 * 执行HTTP请求的通用方法
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} API响应数据
 */
async function makeRequest(url, options = {}) {
    const config = createRequestConfig(options);
    let lastError;

    for (let attempt = 1; attempt <= RETRY_ATTEMPTS; attempt++) {
        try {
            console.log(`🔍 查询优化API请求 (尝试 ${attempt}/${RETRY_ATTEMPTS}): ${url}`);
            
            const response = await fetch(url, config);
            
            if (!response.ok) {
                const errorText = await response.text();
                throw new QueryOptimizationAPIError(
                    `查询优化API请求失败: ${response.status} ${response.statusText}`,
                    response.status,
                    errorText
                );
            }

            const data = await response.json();
            console.log(`✅ 查询优化API请求成功: ${url}`);
            return data;

        } catch (error) {
            lastError = error;
            console.error(`❌ 查询优化API请求失败 (尝试 ${attempt}/${RETRY_ATTEMPTS}):`, error);
            
            if (attempt < RETRY_ATTEMPTS) {
                console.log(`⏳ ${RETRY_DELAY}ms后重试...`);
                await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
            }
        }
    }

    throw lastError;
}

/**
 * GET请求封装
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} API响应数据
 */
async function get(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    
    // 检查缓存
    if (options.useCache !== false) {
        const cacheKey = `GET:${url}`;
        const cached = cache.get(cacheKey);
        if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
            console.log(`📦 使用缓存数据: ${url}`);
            return cached.data;
        }
    }

    const data = await makeRequest(url, { method: 'GET', ...options });
    
    // 缓存响应数据
    if (options.useCache !== false) {
        const cacheKey = `GET:${url}`;
        cache.set(cacheKey, { data, timestamp: Date.now() });
    }

    return data;
}

/**
 * POST请求封装
 * @param {string} endpoint - API端点
 * @param {Object} body - 请求体数据
 * @param {Object} options - 请求选项
 * @returns {Promise<Object>} API响应数据
 */
async function post(endpoint, body, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    return makeRequest(url, {
        method: 'POST',
        body: JSON.stringify(body),
        ...options
    });
}

/**
 * 数据验证工具函数
 */
const validators = {
    /**
     * 验证SQL查询字符串
     * @param {string} sql - SQL查询语句
     * @returns {boolean} 验证结果
     */
    validateSQL(sql) {
        if (!sql || typeof sql !== 'string') {
            return false;
        }
        // 基本SQL语句验证（可以根据需要扩展）
        const sqlPattern = /^\s*(SELECT|INSERT|UPDATE|DELETE|WITH)\s+/i;
        return sqlPattern.test(sql.trim());
    },

    /**
     * 验证查询优化请求参数
     * @param {Object} queryData - 查询数据
     * @returns {Object} 验证结果
     */
    validateOptimizeQuery(queryData) {
        const errors = [];
        
        if (!queryData) {
            errors.push('查询数据不能为空');
            return { valid: false, errors };
        }

        if (!this.validateSQL(queryData.query_sql)) {
            errors.push('无效的SQL查询语句');
        }

        if (queryData.apply_recommendations !== undefined && 
            typeof queryData.apply_recommendations !== 'boolean') {
            errors.push('apply_recommendations必须是布尔值');
        }

        return { valid: errors.length === 0, errors };
    },

    /**
     * 验证批量查询数据
     * @param {Array} queries - 查询数组
     * @returns {Object} 验证结果
     */
    validateBatchQueries(queries) {
        const errors = [];
        
        if (!Array.isArray(queries)) {
            errors.push('查询数据必须是数组');
            return { valid: false, errors };
        }

        if (queries.length === 0) {
            errors.push('查询数组不能为空');
            return { valid: false, errors };
        }

        if (queries.length > 10) {
            errors.push('批量查询数量不能超过10个');
        }

        queries.forEach((query, index) => {
            // 对于批量查询，直接验证SQL字符串
            if (!this.validateSQL(query)) {
                errors.push(`查询${index + 1}: 无效的SQL查询语句`);
            }
        });

        return { valid: errors.length === 0, errors };
    }
};

/**
 * 查询优化API客户端类
 */
class QueryOptimizationAPI {
    /**
     * 优化单个查询
     * @param {Object} queryData - 查询数据
     * @param {string} queryData.query_sql - SQL查询语句
     * @param {boolean} [queryData.apply_recommendations=false] - 是否应用推荐
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 优化结果
     */
    async optimizeQuery(queryData, options = {}) {
        console.log('🔍 开始单个查询优化分析');
        
        // 数据验证
        const validation = validators.validateOptimizeQuery(queryData);
        if (!validation.valid) {
            throw new QueryOptimizationAPIError(
                `查询数据验证失败: ${validation.errors.join(', ')}`,
                400
            );
        }

        try {
            const result = await post('/query/optimize', queryData, options);
            console.log('✅ 单个查询优化完成');
            return result;
        } catch (error) {
            console.error('❌ 单个查询优化失败:', error);
            throw error;
        }
    }

    /**
     * 批量优化查询
     * @param {Array} queries - 查询数组
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 批量优化结果
     */
    async batchOptimizeQueries(queries, options = {}) {
        console.log(`🔍 开始批量查询优化分析 (${queries.length}个查询)`);
        
        // 数据验证
        const validation = validators.validateBatchQueries(queries);
        if (!validation.valid) {
            throw new QueryOptimizationAPIError(
                `批量查询数据验证失败: ${validation.errors.join(', ')}`,
                400
            );
        }

        try {
            const result = await post('/query/batch-optimize', { queries }, options);
            console.log('✅ 批量查询优化完成');
            return result;
        } catch (error) {
            console.error('❌ 批量查询优化失败:', error);
            throw error;
        }
    }

    /**
     * 获取数据库统计信息
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 数据库统计信息
     */
    async getDatabaseStats(options = {}) {
        console.log('📊 获取数据库统计信息');
        
        try {
            const result = await get('/query/database-stats', options);
            console.log('✅ 数据库统计信息获取成功');
            return result;
        } catch (error) {
            console.error('❌ 获取数据库统计信息失败:', error);
            throw error;
        }
    }

    /**
     * 获取索引推荐建议
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 索引推荐建议
     */
    async getIndexRecommendations(options = {}) {
        console.log('💡 获取索引推荐建议');
        
        try {
            const result = await get('/query/index-recommendations', options);
            console.log('✅ 索引推荐建议获取成功');
            return result;
        } catch (error) {
            console.error('❌ 获取索引推荐建议失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有查询优化相关数据
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 综合数据
     */
    async getAllOptimizationData(options = {}) {
        console.log('📈 获取所有查询优化数据');
        
        try {
            const [databaseStats, indexRecommendations] = await Promise.all([
                this.getDatabaseStats(options),
                this.getIndexRecommendations(options)
            ]);

            const result = {
                database_stats: databaseStats,
                index_recommendations: indexRecommendations,
                timestamp: new Date().toISOString()
            };

            console.log('✅ 所有查询优化数据获取成功');
            return result;
        } catch (error) {
            console.error('❌ 获取查询优化数据失败:', error);
            throw error;
        }
    }

    /**
     * 清除缓存
     */
    clearCache() {
        cache.clear();
        console.log('🗑️ 查询优化API缓存已清除');
    }
}

// 创建单例实例
const queryOptimizationAPIInstance = new QueryOptimizationAPI();

// 导出API接口
export const queryOptimizationAPI = {
    optimizeQuery: (queryData, options) => queryOptimizationAPIInstance.optimizeQuery(queryData, options),
    batchOptimize: (queries, options) => queryOptimizationAPIInstance.batchOptimizeQueries(queries, options),
    getDatabaseStats: (options) => queryOptimizationAPIInstance.getDatabaseStats(options),
    getIndexRecommendations: (options) => queryOptimizationAPIInstance.getIndexRecommendations(options),
    getAllData: (options) => queryOptimizationAPIInstance.getAllOptimizationData(options),
    clearCache: () => queryOptimizationAPIInstance.clearCache()
};

// 导出错误类和验证工具
export { QueryOptimizationAPIError, validators };

// 默认导出
export default queryOptimizationAPI;
