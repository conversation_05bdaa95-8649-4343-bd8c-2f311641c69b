/**
 * 任务21：刷新配置管理器
 * 统一管理所有模块的刷新配置和设置
 */

/**
 * 刷新配置管理器类
 */
export class RefreshConfigManager {
    constructor() {
        // 默认配置
        this.defaultConfig = {
            // 全局设置
            globalEnabled: true,
            defaultInterval: 10000, // 10秒
            maxRetries: 3,
            retryDelay: 2000,
            enableVisibilityDetection: true,
            enablePermissionCheck: true,
            
            // 模块特定配置
            modules: {
                healthDashboard: {
                    enabled: true,
                    interval: 30000, // 30秒
                    priority: 'high',
                    endpoints: ['/api/health/check', '/api/health/status']
                },
                websocketMonitoring: {
                    enabled: true,
                    interval: 10000, // 10秒
                    priority: 'high',
                    endpoints: ['/api/websocket/stats', '/api/websocket/connections']
                },
                cacheMonitoring: {
                    enabled: true,
                    interval: 15000, // 15秒
                    priority: 'medium',
                    endpoints: ['/api/cache/stats', '/api/cache/performance']
                },
                databasePerformance: {
                    enabled: true,
                    interval: 20000, // 20秒
                    priority: 'medium',
                    endpoints: ['/api/database/performance', '/api/database/metrics']
                },
                userActivity: {
                    enabled: true,
                    interval: 60000, // 1分钟
                    priority: 'low',
                    endpoints: ['/api/users/online', '/api/users/activity']
                }
            },
            
            // 性能优化设置
            performance: {
                batchRequests: true,
                requestTimeout: 5000,
                maxConcurrentRequests: 3,
                enableRequestDeduplication: true,
                enableResponseCaching: true,
                cacheTimeout: 5000
            },
            
            // 错误处理设置
            errorHandling: {
                enableRetry: true,
                exponentialBackoff: true,
                maxBackoffDelay: 30000,
                enableErrorReporting: true,
                silentFailureThreshold: 5
            }
        };

        // 当前配置
        this.currentConfig = this.loadConfig();
        
        // 配置变化监听器
        this.configChangeListeners = new Set();
        
        console.log('刷新配置管理器已初始化', this.currentConfig);
    }

    /**
     * 加载配置
     */
    loadConfig() {
        try {
            const savedConfig = localStorage.getItem('autoRefreshConfig');
            if (savedConfig) {
                const parsed = JSON.parse(savedConfig);
                return this.mergeConfig(this.defaultConfig, parsed);
            }
        } catch (error) {
            console.error('加载配置失败，使用默认配置:', error);
        }
        
        return { ...this.defaultConfig };
    }

    /**
     * 保存配置
     */
    saveConfig() {
        try {
            localStorage.setItem('autoRefreshConfig', JSON.stringify(this.currentConfig));
            console.log('配置已保存');
            return true;
        } catch (error) {
            console.error('保存配置失败:', error);
            return false;
        }
    }

    /**
     * 合并配置
     */
    mergeConfig(defaultConfig, userConfig) {
        const merged = { ...defaultConfig };
        
        // 递归合并对象
        const deepMerge = (target, source) => {
            for (const key in source) {
                if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                    target[key] = target[key] || {};
                    deepMerge(target[key], source[key]);
                } else {
                    target[key] = source[key];
                }
            }
        };
        
        deepMerge(merged, userConfig);
        return merged;
    }

    /**
     * 获取全局配置
     */
    getGlobalConfig() {
        return {
            globalEnabled: this.currentConfig.globalEnabled,
            defaultInterval: this.currentConfig.defaultInterval,
            maxRetries: this.currentConfig.maxRetries,
            retryDelay: this.currentConfig.retryDelay,
            enableVisibilityDetection: this.currentConfig.enableVisibilityDetection,
            enablePermissionCheck: this.currentConfig.enablePermissionCheck
        };
    }

    /**
     * 获取模块配置
     */
    getModuleConfig(moduleName) {
        const moduleConfig = this.currentConfig.modules[moduleName];
        if (!moduleConfig) {
            console.warn(`模块 ${moduleName} 的配置不存在`);
            return null;
        }
        
        return {
            ...moduleConfig,
            // 继承全局设置
            globalEnabled: this.currentConfig.globalEnabled,
            maxRetries: this.currentConfig.maxRetries,
            retryDelay: this.currentConfig.retryDelay,
            enableVisibilityDetection: this.currentConfig.enableVisibilityDetection,
            enablePermissionCheck: this.currentConfig.enablePermissionCheck
        };
    }

    /**
     * 获取所有模块配置
     */
    getAllModuleConfigs() {
        const configs = {};
        for (const moduleName in this.currentConfig.modules) {
            configs[moduleName] = this.getModuleConfig(moduleName);
        }
        return configs;
    }

    /**
     * 更新全局配置
     */
    updateGlobalConfig(updates) {
        const oldConfig = { ...this.currentConfig };
        
        Object.assign(this.currentConfig, updates);
        
        if (this.saveConfig()) {
            this.notifyConfigChange('global', oldConfig, this.currentConfig);
            return true;
        }
        
        return false;
    }

    /**
     * 更新模块配置
     */
    updateModuleConfig(moduleName, updates) {
        if (!this.currentConfig.modules[moduleName]) {
            console.error(`模块 ${moduleName} 不存在`);
            return false;
        }
        
        const oldConfig = { ...this.currentConfig.modules[moduleName] };
        
        Object.assign(this.currentConfig.modules[moduleName], updates);
        
        if (this.saveConfig()) {
            this.notifyConfigChange('module', oldConfig, this.currentConfig.modules[moduleName], moduleName);
            return true;
        }
        
        return false;
    }

    /**
     * 启用/禁用模块
     */
    toggleModule(moduleName, enabled) {
        return this.updateModuleConfig(moduleName, { enabled });
    }

    /**
     * 设置模块刷新间隔
     */
    setModuleInterval(moduleName, interval) {
        const validIntervals = [5000, 10000, 15000, 20000, 30000, 60000];
        
        if (!validIntervals.includes(interval)) {
            console.error(`无效的刷新间隔: ${interval}`);
            return false;
        }
        
        return this.updateModuleConfig(moduleName, { interval });
    }

    /**
     * 获取性能配置
     */
    getPerformanceConfig() {
        return { ...this.currentConfig.performance };
    }

    /**
     * 更新性能配置
     */
    updatePerformanceConfig(updates) {
        const oldConfig = { ...this.currentConfig.performance };
        
        Object.assign(this.currentConfig.performance, updates);
        
        if (this.saveConfig()) {
            this.notifyConfigChange('performance', oldConfig, this.currentConfig.performance);
            return true;
        }
        
        return false;
    }

    /**
     * 获取错误处理配置
     */
    getErrorHandlingConfig() {
        return { ...this.currentConfig.errorHandling };
    }

    /**
     * 更新错误处理配置
     */
    updateErrorHandlingConfig(updates) {
        const oldConfig = { ...this.currentConfig.errorHandling };
        
        Object.assign(this.currentConfig.errorHandling, updates);
        
        if (this.saveConfig()) {
            this.notifyConfigChange('errorHandling', oldConfig, this.currentConfig.errorHandling);
            return true;
        }
        
        return false;
    }

    /**
     * 重置配置到默认值
     */
    resetToDefaults() {
        const oldConfig = { ...this.currentConfig };
        this.currentConfig = { ...this.defaultConfig };
        
        if (this.saveConfig()) {
            this.notifyConfigChange('reset', oldConfig, this.currentConfig);
            return true;
        }
        
        return false;
    }

    /**
     * 导出配置
     */
    exportConfig() {
        return JSON.stringify(this.currentConfig, null, 2);
    }

    /**
     * 导入配置
     */
    importConfig(configJson) {
        try {
            const importedConfig = JSON.parse(configJson);
            const oldConfig = { ...this.currentConfig };
            
            this.currentConfig = this.mergeConfig(this.defaultConfig, importedConfig);
            
            if (this.saveConfig()) {
                this.notifyConfigChange('import', oldConfig, this.currentConfig);
                return true;
            }
            
            return false;
        } catch (error) {
            console.error('导入配置失败:', error);
            return false;
        }
    }

    /**
     * 验证配置
     */
    validateConfig(config = this.currentConfig) {
        const errors = [];
        
        // 验证全局配置
        if (typeof config.globalEnabled !== 'boolean') {
            errors.push('globalEnabled 必须是布尔值');
        }
        
        if (!Number.isInteger(config.defaultInterval) || config.defaultInterval < 1000) {
            errors.push('defaultInterval 必须是大于等于1000的整数');
        }
        
        if (!Number.isInteger(config.maxRetries) || config.maxRetries < 0) {
            errors.push('maxRetries 必须是非负整数');
        }
        
        // 验证模块配置
        for (const moduleName in config.modules) {
            const moduleConfig = config.modules[moduleName];
            
            if (typeof moduleConfig.enabled !== 'boolean') {
                errors.push(`模块 ${moduleName} 的 enabled 必须是布尔值`);
            }
            
            if (!Number.isInteger(moduleConfig.interval) || moduleConfig.interval < 1000) {
                errors.push(`模块 ${moduleName} 的 interval 必须是大于等于1000的整数`);
            }
            
            if (!Array.isArray(moduleConfig.endpoints)) {
                errors.push(`模块 ${moduleName} 的 endpoints 必须是数组`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 添加配置变化监听器
     */
    onConfigChange(listener) {
        if (typeof listener === 'function') {
            this.configChangeListeners.add(listener);
        }
    }

    /**
     * 移除配置变化监听器
     */
    offConfigChange(listener) {
        this.configChangeListeners.delete(listener);
    }

    /**
     * 通知配置变化
     */
    notifyConfigChange(type, oldConfig, newConfig, moduleName = null) {
        const changeEvent = {
            type,
            oldConfig,
            newConfig,
            moduleName,
            timestamp: new Date()
        };
        
        this.configChangeListeners.forEach(listener => {
            try {
                listener(changeEvent);
            } catch (error) {
                console.error('配置变化监听器执行失败:', error);
            }
        });
        
        console.log('配置已更新:', changeEvent);
    }

    /**
     * 获取配置摘要
     */
    getConfigSummary() {
        const enabledModules = Object.keys(this.currentConfig.modules)
            .filter(name => this.currentConfig.modules[name].enabled);
        
        return {
            globalEnabled: this.currentConfig.globalEnabled,
            enabledModulesCount: enabledModules.length,
            totalModulesCount: Object.keys(this.currentConfig.modules).length,
            enabledModules,
            averageInterval: this.calculateAverageInterval(),
            performanceOptimized: this.currentConfig.performance.batchRequests,
            errorHandlingEnabled: this.currentConfig.errorHandling.enableRetry
        };
    }

    /**
     * 计算平均刷新间隔
     */
    calculateAverageInterval() {
        const enabledModules = Object.values(this.currentConfig.modules)
            .filter(config => config.enabled);
        
        if (enabledModules.length === 0) {
            return 0;
        }
        
        const totalInterval = enabledModules.reduce((sum, config) => sum + config.interval, 0);
        return Math.round(totalInterval / enabledModules.length);
    }
}

// 创建全局实例
export const globalRefreshConfigManager = new RefreshConfigManager();

// 导出默认实例
export default globalRefreshConfigManager;
