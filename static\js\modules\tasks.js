/**
 * 任务管理模块 - 处理任务相关功能
 * 基于ES6模块化设计，遵循Clean Code JavaScript最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { taskAPI } from './api.js';
import { onMessage } from './websocket.js';

// ==== 任务状态常量 ====
export const TASK_STATUS = {
    PENDING: 'pending',
    IN_PROGRESS: 'in_progress',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled'
};

// ==== 任务优先级常量 ====
export const TASK_PRIORITY = {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent'
};

// ==== 任务状态管理 ====
let tasks = [];
let currentFilter = 'all';
let taskChangeHandlers = new Set();

/**
 * 任务数据验证器
 */
class TaskValidator {
    /**
     * 验证任务数据
     * @param {Object} taskData - 任务数据
     * @returns {Object} 验证结果
     */
    static validate(taskData) {
        const errors = [];
        
        if (!taskData.title || taskData.title.trim().length === 0) {
            errors.push('任务标题不能为空');
        }
        
        if (taskData.title && taskData.title.length > 200) {
            errors.push('任务标题不能超过200个字符');
        }
        
        if (taskData.description && taskData.description.length > 1000) {
            errors.push('任务描述不能超过1000个字符');
        }
        
        if (taskData.priority && !Object.values(TASK_PRIORITY).includes(taskData.priority)) {
            errors.push('无效的任务优先级');
        }
        
        if (taskData.status && !Object.values(TASK_STATUS).includes(taskData.status)) {
            errors.push('无效的任务状态');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

/**
 * 任务管理器类
 */
class TaskManager {
    /**
     * 获取所有任务
     * @returns {Promise<Array>} 任务列表
     */
    async fetchAllTasks() {
        try {
            console.log('正在获取所有任务...');
            const response = await taskAPI.fetchAll();

            // 修复：从API响应中提取实际的任务数据
            const fetchedTasks = response && response.data ? response.data : response;
            tasks = Array.isArray(fetchedTasks) ? fetchedTasks : [];

            this.notifyTaskChange('fetch', tasks);
            console.log(`成功获取${tasks.length}个任务`);
            return tasks;
        } catch (error) {
            console.error('获取任务失败:', error);
            throw error;
        }
    }

    /**
     * 获取单个任务
     * @param {string} id - 任务ID (UUID字符串)
     * @returns {Promise<Object>} 任务详情
     */
    async fetchTaskById(id) {
        // 验证任务ID
        if (!id || typeof id !== 'string') {
            throw new Error('任务ID无效：必须是有效的UUID字符串');
        }

        try {
            console.log(`正在获取任务 ${id}...`);
            const task = await taskAPI.fetchById(id);
            console.log(`成功获取任务 ${id}`);
            return task;
        } catch (error) {
            console.error(`获取任务 ${id} 失败:`, error);
            throw error;
        }
    }

    /**
     * 创建新任务
     * @param {Object} taskData - 任务数据
     * @returns {Promise<Object>} 创建的任务
     */
    async createTask(taskData) {
        // 验证任务数据
        const validation = TaskValidator.validate(taskData);
        if (!validation.isValid) {
            throw new Error(`任务数据验证失败: ${validation.errors.join(', ')}`);
        }

        try {
            console.log('正在创建新任务...', taskData);
            const response = await taskAPI.create(taskData);

            // 修复：从API响应中提取实际的任务数据
            const newTask = response && response.data ? response.data : response;

            if (!newTask || !newTask.id) {
                throw new Error('任务创建响应数据格式错误：缺少任务ID');
            }

            // 更新本地任务列表
            tasks.push(newTask);
            this.notifyTaskChange('create', response); // 传递完整响应给UI处理

            console.log('任务创建成功:', response);
            return response; // 返回完整响应以保持API一致性
        } catch (error) {
            console.error('创建任务失败:', error);
            throw error;
        }
    }

    /**
     * 更新任务
     * @param {string} id - 任务ID (UUID字符串)
     * @param {Object} taskData - 更新的任务数据
     * @returns {Promise<Object>} 更新的任务
     */
    async updateTask(id, taskData) {
        // 验证任务ID
        if (!id || typeof id !== 'string') {
            throw new Error('任务ID无效：必须是有效的UUID字符串');
        }

        // 验证任务数据
        const validation = TaskValidator.validate(taskData);
        if (!validation.isValid) {
            throw new Error(`任务数据验证失败: ${validation.errors.join(', ')}`);
        }

        try {
            console.log(`正在更新任务 ${id}...`, taskData);
            const updatedTask = await this.retryOperation(() => taskAPI.update(id, taskData), 'update');

            // 更新本地任务列表
            const index = tasks.findIndex(task => task.id === id);
            if (index !== -1) {
                tasks[index] = updatedTask;
                this.notifyTaskChange('update', updatedTask);
            }

            console.log('任务更新成功:', updatedTask);
            return updatedTask;
        } catch (error) {
            console.error(`更新任务 ${id} 失败:`, error);
            throw this.enhanceError(error, 'update', id);
        }
    }

    /**
     * 删除任务
     * @param {string} id - 任务ID (UUID字符串)
     * @returns {Promise<void>}
     */
    async deleteTask(id) {
        // 验证任务ID
        if (!id || typeof id !== 'string') {
            throw new Error('任务ID无效：必须是有效的UUID字符串');
        }

        try {
            console.log(`正在删除任务 ${id}...`);
            await this.retryOperation(() => taskAPI.delete(id), 'delete');

            // 从本地任务列表中移除
            const index = tasks.findIndex(task => task.id === id);
            if (index !== -1) {
                const deletedTask = tasks.splice(index, 1)[0];
                this.notifyTaskChange('delete', deletedTask);
            }

            console.log(`任务 ${id} 删除成功`);
        } catch (error) {
            console.error(`删除任务 ${id} 失败:`, error);
            throw this.enhanceError(error, 'delete', id);
        }
    }

    /**
     * 切换任务完成状态
     * @param {string} id - 任务ID (UUID字符串)
     * @returns {Promise<Object>} 更新的任务
     */
    async toggleTaskCompletion(id) {
        // 验证任务ID
        if (!id || typeof id !== 'string') {
            throw new Error('任务ID无效：必须是有效的UUID字符串');
        }

        const task = tasks.find(t => t.id === id);
        if (!task) {
            throw new Error(`任务 ${id} 不存在`);
        }

        const newStatus = task.status === TASK_STATUS.COMPLETED
            ? TASK_STATUS.PENDING
            : TASK_STATUS.COMPLETED;

        return this.updateTask(id, { ...task, status: newStatus });
    }

    /**
     * 获取过滤后的任务列表
     * @param {string} filter - 过滤条件
     * @returns {Array} 过滤后的任务列表
     */
    getFilteredTasks(filter = currentFilter) {
        switch (filter) {
            case 'completed':
                return tasks.filter(task => task.status === TASK_STATUS.COMPLETED);
            case 'pending':
                return tasks.filter(task => task.status === TASK_STATUS.PENDING);
            case 'in_progress':
                return tasks.filter(task => task.status === TASK_STATUS.IN_PROGRESS);
            case 'all':
            default:
                return [...tasks];
        }
    }

    /**
     * 设置任务过滤器
     * @param {string} filter - 过滤条件
     */
    setFilter(filter) {
        currentFilter = filter;
        this.notifyTaskChange('filter', this.getFilteredTasks(filter));
    }

    /**
     * 获取当前过滤器
     * @returns {string} 当前过滤器
     */
    getCurrentFilter() {
        return currentFilter;
    }

    /**
     * 获取任务统计信息
     * @returns {Object} 任务统计
     */
    getTaskStats() {
        const total = tasks.length;
        const completed = tasks.filter(task => task.status === TASK_STATUS.COMPLETED).length;
        const pending = tasks.filter(task => task.status === TASK_STATUS.PENDING).length;
        const inProgress = tasks.filter(task => task.status === TASK_STATUS.IN_PROGRESS).length;
        const cancelled = tasks.filter(task => task.status === TASK_STATUS.CANCELLED).length;

        return {
            total,
            completed,
            pending,
            inProgress,
            cancelled,
            completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
        };
    }

    /**
     * 搜索任务
     * @param {string} query - 搜索关键词
     * @returns {Array} 搜索结果
     */
    searchTasks(query) {
        if (!query || query.trim().length === 0) {
            return this.getFilteredTasks();
        }

        const searchTerm = query.toLowerCase().trim();
        return tasks.filter(task => 
            task.title.toLowerCase().includes(searchTerm) ||
            (task.description && task.description.toLowerCase().includes(searchTerm))
        );
    }

    /**
     * 按优先级排序任务
     * @param {Array} taskList - 任务列表
     * @param {boolean} ascending - 是否升序
     * @returns {Array} 排序后的任务列表
     */
    sortTasksByPriority(taskList = tasks, ascending = false) {
        const priorityOrder = {
            [TASK_PRIORITY.URGENT]: 4,
            [TASK_PRIORITY.HIGH]: 3,
            [TASK_PRIORITY.MEDIUM]: 2,
            [TASK_PRIORITY.LOW]: 1
        };

        return [...taskList].sort((a, b) => {
            const priorityA = priorityOrder[a.priority] || 0;
            const priorityB = priorityOrder[b.priority] || 0;
            return ascending ? priorityA - priorityB : priorityB - priorityA;
        });
    }

    /**
     * 注册任务变化处理器
     * @param {Function} handler - 处理器函数
     */
    onTaskChange(handler) {
        taskChangeHandlers.add(handler);
    }

    /**
     * 移除任务变化处理器
     * @param {Function} handler - 处理器函数
     */
    offTaskChange(handler) {
        taskChangeHandlers.delete(handler);
    }

    /**
     * 通知任务变化
     * @param {string} action - 操作类型
     * @param {any} data - 相关数据
     */
    notifyTaskChange(action, data) {
        taskChangeHandlers.forEach(handler => {
            try {
                handler(action, data);
            } catch (error) {
                console.error('任务变化处理器执行失败:', error);
            }
        });
    }

    /**
     * 获取所有任务（只读）
     * @returns {Array} 任务列表的副本
     */
    getAllTasks() {
        return [...tasks];
    }

    /**
     * 重试操作机制
     * @param {Function} operation - 要重试的操作
     * @param {string} operationType - 操作类型（用于日志）
     * @param {number} maxRetries - 最大重试次数
     * @param {number} delay - 重试延迟（毫秒）
     * @returns {Promise<any>} 操作结果
     */
    async retryOperation(operation, operationType, maxRetries = 2, delay = 1000) {
        let lastError;

        for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                return await operation();
            } catch (error) {
                lastError = error;
                console.warn(`${operationType}操作第${attempt}次尝试失败:`, error.message);

                // 如果是最后一次尝试，直接抛出错误
                if (attempt > maxRetries) {
                    break;
                }

                // 检查是否是可重试的错误
                if (!this.isRetryableError(error)) {
                    console.log(`${operationType}操作遇到不可重试错误，停止重试`);
                    break;
                }

                // 等待后重试
                console.log(`${operationType}操作将在${delay}ms后重试...`);
                await new Promise(resolve => setTimeout(resolve, delay));
                delay *= 1.5; // 指数退避
            }
        }

        throw lastError;
    }

    /**
     * 判断错误是否可重试
     * @param {Error} error - 错误对象
     * @returns {boolean} 是否可重试
     */
    isRetryableError(error) {
        // 网络错误通常可重试
        if (error.message.includes('网络') || error.message.includes('Network')) {
            return true;
        }

        // 超时错误可重试
        if (error.message.includes('timeout') || error.message.includes('超时')) {
            return true;
        }

        // 5xx服务器错误可重试
        if (error.status >= 500 && error.status < 600) {
            return true;
        }

        // 4xx客户端错误通常不可重试（除了429限流）
        if (error.status === 429) {
            return true;
        }

        return false;
    }

    /**
     * 增强错误信息
     * @param {Error} error - 原始错误
     * @param {string} operation - 操作类型
     * @param {string} taskId - 任务ID
     * @returns {Error} 增强后的错误
     */
    enhanceError(error, operation, taskId) {
        let enhancedMessage = error.message || `${operation}操作失败`;

        // 根据HTTP状态码提供更友好的错误信息
        if (error.status) {
            switch (error.status) {
                case 400:
                    enhancedMessage = '请求参数无效，请检查输入内容';
                    break;
                case 401:
                    enhancedMessage = '登录已过期，请重新登录';
                    break;
                case 403:
                    enhancedMessage = '没有权限执行此操作';
                    break;
                case 404:
                    enhancedMessage = '任务不存在或已被删除';
                    break;
                case 409:
                    enhancedMessage = '操作冲突，请刷新页面后重试';
                    break;
                case 429:
                    enhancedMessage = '操作过于频繁，请稍后重试';
                    break;
                case 500:
                    enhancedMessage = '服务器内部错误，请稍后重试';
                    break;
                case 502:
                case 503:
                case 504:
                    enhancedMessage = '服务暂时不可用，请稍后重试';
                    break;
                default:
                    if (error.status >= 500) {
                        enhancedMessage = '服务器错误，请稍后重试';
                    }
            }
        }

        // 创建新的错误对象，保留原始错误信息
        const enhancedError = new Error(enhancedMessage);
        enhancedError.originalError = error;
        enhancedError.operation = operation;
        enhancedError.taskId = taskId;
        enhancedError.status = error.status;

        return enhancedError;
    }
}

// ==== 导出单例实例 ====
export const taskManager = new TaskManager();

// ==== 便捷方法导出 ====
export const {
    fetchAllTasks,
    fetchTaskById,
    createTask,
    updateTask,
    deleteTask,
    toggleTaskCompletion,
    getFilteredTasks,
    setFilter,
    getCurrentFilter,
    getTaskStats,
    searchTasks,
    sortTasksByPriority,
    onTaskChange,
    offTaskChange,
    getAllTasks
} = taskManager;

// ==== WebSocket实时更新处理器 ====

/**
 * 处理任务创建的实时更新
 * @param {Object} message - WebSocket消息
 */
function handleTaskCreated(message) {
    console.log('收到任务创建实时更新:', message);

    if (message.data && message.data.id) {
        // 将新任务添加到本地任务列表
        const newTask = message.data;
        tasks.push(newTask);

        // 通知所有监听器
        taskChangeHandlers.forEach(handler => {
            try {
                handler('created', newTask);
            } catch (error) {
                console.error('任务变更处理器执行失败:', error);
            }
        });

        // 显示通知
        if (window.showNotification) {
            window.showNotification(`新任务已创建: ${newTask.title}`, 'success');
        }

        console.log('任务创建实时更新处理完成');
    }
}

/**
 * 处理任务更新的实时更新
 * @param {Object} message - WebSocket消息
 */
function handleTaskUpdated(message) {
    console.log('收到任务更新实时更新:', message);

    if (message.data && message.data.id) {
        const updatedTask = message.data;
        const taskIndex = tasks.findIndex(task => task.id === updatedTask.id);

        if (taskIndex !== -1) {
            // 更新本地任务数据
            tasks[taskIndex] = { ...tasks[taskIndex], ...updatedTask };

            // 通知所有监听器
            taskChangeHandlers.forEach(handler => {
                try {
                    handler('updated', tasks[taskIndex]);
                } catch (error) {
                    console.error('任务变更处理器执行失败:', error);
                }
            });

            // 显示通知
            if (window.showNotification) {
                window.showNotification(`任务已更新: ${updatedTask.title}`, 'info');
            }

            console.log('任务更新实时更新处理完成');
        }
    }
}

/**
 * 处理任务删除的实时更新
 * @param {Object} message - WebSocket消息
 */
function handleTaskDeleted(message) {
    console.log('收到任务删除实时更新:', message);

    if (message.data && message.data.id) {
        const deletedTaskId = message.data.id;
        const taskIndex = tasks.findIndex(task => task.id === deletedTaskId);

        if (taskIndex !== -1) {
            const deletedTask = tasks[taskIndex];

            // 从本地任务列表中移除
            tasks.splice(taskIndex, 1);

            // 通知所有监听器
            taskChangeHandlers.forEach(handler => {
                try {
                    handler('deleted', deletedTask);
                } catch (error) {
                    console.error('任务变更处理器执行失败:', error);
                }
            });

            // 显示通知
            if (window.showNotification) {
                window.showNotification(`任务已删除: ${deletedTask.title}`, 'warning');
            }

            console.log('任务删除实时更新处理完成');
        }
    }
}

/**
 * 处理任务状态变更的实时更新
 * @param {Object} message - WebSocket消息
 */
function handleTaskStatusChanged(message) {
    console.log('收到任务状态变更实时更新:', message);

    // 任务状态变更通常包含在任务更新中，所以直接调用更新处理器
    handleTaskUpdated(message);
}

/**
 * 处理任务列表刷新的实时更新
 * @param {Object} message - WebSocket消息
 */
function handleTaskListRefresh(message) {
    console.log('收到任务列表刷新实时更新:', message);

    // 重新获取任务列表
    taskManager.fetchAllTasks().then(() => {
        console.log('任务列表已刷新');

        // 显示通知
        if (window.showNotification) {
            window.showNotification('任务列表已刷新', 'info');
        }
    }).catch(error => {
        console.error('刷新任务列表失败:', error);
    });
}

// 注册WebSocket消息处理器
onMessage('task_created', handleTaskCreated);
onMessage('task_updated', handleTaskUpdated);
onMessage('task_deleted', handleTaskDeleted);
onMessage('task_status_changed', handleTaskStatusChanged);
onMessage('task_list_refresh', handleTaskListRefresh);

console.log('任务WebSocket实时更新处理器已注册');
