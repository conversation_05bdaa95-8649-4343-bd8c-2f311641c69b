/**
 * UI工具模块 - 提供通用UI操作和DOM工具函数
 * 基于ES6模块化设计，遵循Clean Code JavaScript最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

// ==== DOM选择器工具 ====

/**
 * 安全的DOM元素选择器
 * @param {string} selector - CSS选择器
 * @param {Element} context - 查找上下文，默认为document
 * @returns {Element|null} DOM元素或null
 */
export function querySelector(selector, context = document) {
    try {
        return context.querySelector(selector);
    } catch (error) {
        console.error(`无效的选择器: ${selector}`, error);
        return null;
    }
}

/**
 * 安全的DOM元素列表选择器
 * @param {string} selector - CSS选择器
 * @param {Element} context - 查找上下文，默认为document
 * @returns {NodeList} DOM元素列表
 */
export function querySelectorAll(selector, context = document) {
    try {
        return context.querySelectorAll(selector);
    } catch (error) {
        console.error(`无效的选择器: ${selector}`, error);
        return [];
    }
}

/**
 * 通过ID获取元素
 * @param {string} id - 元素ID
 * @returns {Element|null} DOM元素或null
 */
export function getElementById(id) {
    return document.getElementById(id);
}

// ==== DOM操作工具 ====

/**
 * 创建DOM元素
 * @param {string} tagName - 标签名
 * @param {Object} attributes - 属性对象
 * @param {string} textContent - 文本内容
 * @returns {Element} 创建的DOM元素
 */
export function createElement(tagName, attributes = {}, textContent = '') {
    const element = document.createElement(tagName);
    
    // 设置属性
    Object.entries(attributes).forEach(([key, value]) => {
        if (key === 'className') {
            element.className = value;
        } else if (key === 'dataset') {
            Object.entries(value).forEach(([dataKey, dataValue]) => {
                element.dataset[dataKey] = dataValue;
            });
        } else {
            element.setAttribute(key, value);
        }
    });
    
    // 设置文本内容
    if (textContent) {
        element.textContent = textContent;
    }
    
    return element;
}

/**
 * 安全地移除DOM元素
 * @param {Element} element - 要移除的元素
 */
export function removeElement(element) {
    if (element && element.parentNode) {
        element.parentNode.removeChild(element);
    }
}

/**
 * 清空元素内容
 * @param {Element} element - 要清空的元素
 */
export function clearElement(element) {
    if (element) {
        element.innerHTML = '';
    }
}

/**
 * 切换元素的CSS类
 * @param {Element} element - 目标元素
 * @param {string} className - CSS类名
 * @param {boolean} force - 强制添加或移除
 */
export function toggleClass(element, className, force) {
    if (element && element.classList) {
        element.classList.toggle(className, force);
    }
}

/**
 * 添加CSS类
 * @param {Element} element - 目标元素
 * @param {...string} classNames - CSS类名
 */
export function addClass(element, ...classNames) {
    if (element && element.classList) {
        element.classList.add(...classNames);
    }
}

/**
 * 移除CSS类
 * @param {Element} element - 目标元素
 * @param {...string} classNames - CSS类名
 */
export function removeClass(element, ...classNames) {
    if (element && element.classList) {
        element.classList.remove(...classNames);
    }
}

/**
 * 检查元素是否包含指定CSS类
 * @param {Element} element - 目标元素
 * @param {string} className - CSS类名
 * @returns {boolean} 是否包含指定类
 */
export function hasClass(element, className) {
    return element && element.classList && element.classList.contains(className);
}

// ==== 事件处理工具 ====

/**
 * 安全的事件监听器添加
 * @param {Element} element - 目标元素
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理器
 * @param {Object} options - 事件选项
 */
export function addEventListener(element, event, handler, options = {}) {
    if (element && typeof handler === 'function') {
        element.addEventListener(event, handler, options);
    }
}

/**
 * 安全的事件监听器移除
 * @param {Element} element - 目标元素
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理器
 * @param {Object} options - 事件选项
 */
export function removeEventListener(element, event, handler, options = {}) {
    if (element && typeof handler === 'function') {
        element.removeEventListener(event, handler, options);
    }
}

/**
 * 委托事件处理
 * @param {Element} container - 容器元素
 * @param {string} selector - 目标选择器
 * @param {string} event - 事件类型
 * @param {Function} handler - 事件处理器
 */
export function delegateEvent(container, selector, event, handler) {
    if (!container || typeof handler !== 'function') return;
    
    container.addEventListener(event, (e) => {
        const target = e.target.closest(selector);
        if (target) {
            handler.call(target, e);
        }
    });
}

// ==== 表单工具 ====

/**
 * 获取表单数据
 * @param {HTMLFormElement} form - 表单元素
 * @returns {Object} 表单数据对象
 */
export function getFormData(form) {
    if (!form) return {};
    
    const formData = new FormData(form);
    const data = {};
    
    for (const [key, value] of formData.entries()) {
        if (data[key]) {
            // 处理多值字段
            if (Array.isArray(data[key])) {
                data[key].push(value);
            } else {
                data[key] = [data[key], value];
            }
        } else {
            data[key] = value;
        }
    }
    
    return data;
}

/**
 * 设置表单数据
 * @param {HTMLFormElement} form - 表单元素
 * @param {Object} data - 数据对象
 */
export function setFormData(form, data) {
    if (!form || !data) return;
    
    Object.entries(data).forEach(([key, value]) => {
        const field = form.elements[key];
        if (field) {
            if (field.type === 'checkbox' || field.type === 'radio') {
                field.checked = Boolean(value);
            } else {
                field.value = value;
            }
        }
    });
}

/**
 * 重置表单
 * @param {HTMLFormElement} form - 表单元素
 */
export function resetForm(form) {
    if (form && typeof form.reset === 'function') {
        form.reset();
    }
}

/**
 * 验证表单
 * @param {HTMLFormElement} form - 表单元素
 * @returns {boolean} 表单是否有效
 */
export function validateForm(form) {
    if (!form) return false;
    
    return form.checkValidity();
}

// ==== 显示/隐藏工具 ====

/**
 * 显示元素
 * @param {Element} element - 目标元素
 * @param {string} display - 显示类型，默认为'block'
 */
export function showElement(element, display = 'block') {
    if (element) {
        element.classList.remove('hidden');
        // 如果元素没有其他显示样式，设置内联样式
        if (!element.style.display || element.style.display === 'none') {
            element.style.display = display;
        }
    }
}

/**
 * 隐藏元素
 * @param {Element} element - 目标元素
 */
export function hideElement(element) {
    if (element) {
        element.classList.add('hidden');
    }
}

/**
 * 切换元素显示状态
 * @param {Element} element - 目标元素
 * @param {string} display - 显示类型，默认为'block'
 */
export function toggleElement(element, display = 'block') {
    if (!element) return;
    
    if (element.style.display === 'none') {
        showElement(element, display);
    } else {
        hideElement(element);
    }
}

// ==== 文本处理工具 ====

/**
 * HTML转义
 * @param {string} text - 要转义的文本
 * @returns {string} 转义后的文本
 */
export function escapeHtml(text) {
    if (!text) return '';
    
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 格式化时间戳
 * @param {string|Date} timestamp - 时间戳
 * @param {Object} options - 格式化选项
 * @returns {string} 格式化后的时间字符串
 */
export function formatTimestamp(timestamp, options = {}) {
    if (!timestamp) return '';
    
    try {
        const date = new Date(timestamp);
        const defaultOptions = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            ...options
        };
        
        return date.toLocaleString('zh-CN', defaultOptions);
    } catch (error) {
        console.error('时间格式化失败:', error);
        return String(timestamp);
    }
}

// ==== 滚动工具 ====

/**
 * 滚动到元素
 * @param {Element} element - 目标元素
 * @param {Object} options - 滚动选项
 */
export function scrollToElement(element, options = {}) {
    if (element && element.scrollIntoView) {
        const defaultOptions = {
            behavior: 'smooth',
            block: 'start',
            ...options
        };
        element.scrollIntoView(defaultOptions);
    }
}

/**
 * 滚动到顶部
 * @param {Element} container - 容器元素，默认为window
 * @param {Object} options - 滚动选项
 */
export function scrollToTop(container = window, options = {}) {
    const defaultOptions = {
        top: 0,
        behavior: 'smooth',
        ...options
    };
    
    if (container === window) {
        window.scrollTo(defaultOptions);
    } else if (container && container.scrollTo) {
        container.scrollTo(defaultOptions);
    }
}

/**
 * 滚动到底部
 * @param {Element} container - 容器元素
 * @param {Object} options - 滚动选项
 */
export function scrollToBottom(container, options = {}) {
    if (!container) return;

    const defaultOptions = {
        top: container.scrollHeight,
        behavior: 'smooth',
        ...options
    };

    if (container.scrollTo) {
        container.scrollTo(defaultOptions);
    } else {
        container.scrollTop = container.scrollHeight;
    }
}

// ==== 通知工具 ====

/**
 * 显示通知消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 (success, error, warning, info)
 * @param {number} duration - 显示时长（毫秒），0表示不自动关闭
 */
export function showNotification(message, type = 'info', duration = 3000) {
    // 创建通知容器（如果不存在）
    let container = getElementById('notification-container');
    if (!container) {
        container = createElement('div', {
            id: 'notification-container',
            className: 'notification-container'
        });
        document.body.appendChild(container);
    }

    // 创建通知元素
    const notification = createElement('div', {
        className: `notification notification-${type}`
    });

    const messageEl = createElement('span', {
        className: 'notification-message'
    }, message);

    const closeBtn = createElement('button', {
        className: 'notification-close',
        type: 'button'
    }, '×');

    notification.appendChild(messageEl);
    notification.appendChild(closeBtn);

    // 添加关闭事件
    closeBtn.addEventListener('click', () => {
        removeNotification(notification);
    });

    // 添加到容器
    container.appendChild(notification);

    // 自动关闭
    if (duration > 0) {
        setTimeout(() => {
            removeNotification(notification);
        }, duration);
    }

    return notification;
}

/**
 * 移除通知
 * @param {Element} notification - 通知元素
 */
function removeNotification(notification) {
    if (notification && notification.parentNode) {
        notification.style.opacity = '0';
        setTimeout(() => {
            removeElement(notification);
        }, 300);
    }
}

// ==== 按钮状态管理工具 ====

/**
 * 按钮状态管理器
 * 统一管理所有按钮的加载状态，防止状态冲突和卡住问题
 */
class ButtonStateManager {
    constructor() {
        // 存储按钮的原始状态
        this.buttonStates = new Map();
        // 存储当前正在处理的按钮
        this.processingButtons = new Set();
        // 操作计数器，用于调试
        this.operationCounter = 0;
    }

    /**
     * 初始化按钮状态
     * @param {Element} button - 按钮元素
     */
    initializeButton(button) {
        if (!button || this.buttonStates.has(button)) return;

        const originalState = {
            text: button.textContent || button.innerText || '',
            disabled: button.disabled,
            className: button.className
        };

        this.buttonStates.set(button, originalState);

        // 设置data属性作为备份
        if (!button.dataset.originalText) {
            button.dataset.originalText = originalState.text;
        }
        if (!button.dataset.originalDisabled) {
            button.dataset.originalDisabled = originalState.disabled.toString();
        }

        console.log('ButtonStateManager: 初始化按钮状态', {
            text: originalState.text,
            disabled: originalState.disabled
        });
    }

    /**
     * 设置按钮为加载状态
     * @param {Element} button - 按钮元素
     * @param {string} loadingText - 加载时显示的文本，默认为"处理中..."
     * @returns {boolean} 是否成功设置（如果按钮已在处理中则返回false）
     */
    setLoading(button, loadingText = '处理中...') {
        if (!button) {
            console.warn('ButtonStateManager.setLoading: 按钮元素为空');
            return false;
        }

        // 如果按钮已在处理中，拒绝重复设置
        if (this.processingButtons.has(button)) {
            console.warn('ButtonStateManager.setLoading: 按钮已在处理中，拒绝重复设置', {
                currentText: button.textContent,
                requestedText: loadingText
            });
            return false;
        }

        // 初始化按钮状态（如果还没有初始化）
        this.initializeButton(button);

        // 标记为处理中
        this.processingButtons.add(button);
        this.operationCounter++;

        // 设置加载状态
        button.disabled = true;
        button.textContent = loadingText;

        // 添加加载样式类
        if (!button.classList.contains('loading')) {
            button.classList.add('loading');
        }

        console.log('ButtonStateManager: 设置按钮加载状态', {
            text: loadingText,
            operationId: this.operationCounter,
            processingCount: this.processingButtons.size
        });

        return true;
    }

    /**
     * 恢复按钮到原始状态
     * @param {Element} button - 按钮元素
     * @param {string} customText - 自定义恢复文本（可选）
     */
    restore(button, customText = null) {
        if (!button) {
            console.warn('ButtonStateManager.restore: 按钮元素为空');
            return;
        }

        // 从处理中集合移除
        const wasProcessing = this.processingButtons.has(button);
        this.processingButtons.delete(button);

        // 获取原始状态
        const originalState = this.buttonStates.get(button);

        if (originalState) {
            // 恢复原始状态
            button.disabled = originalState.disabled;
            button.textContent = customText || originalState.text;
            button.className = originalState.className;
        } else {
            // 备用方案：使用data属性恢复
            button.disabled = button.dataset.originalDisabled === 'true';
            button.textContent = customText || button.dataset.originalText || '提交';

            // 移除加载样式类
            button.classList.remove('loading');
        }

        console.log('ButtonStateManager: 恢复按钮状态', {
            wasProcessing,
            restoredText: button.textContent,
            processingCount: this.processingButtons.size
        });
    }

    /**
     * 检查按钮是否正在处理中
     * @param {Element} button - 按钮元素
     * @returns {boolean} 是否正在处理中
     */
    isProcessing(button) {
        return this.processingButtons.has(button);
    }

    /**
     * 强制清理所有按钮状态（紧急情况使用）
     */
    clearAllStates() {
        console.warn('ButtonStateManager.clearAllStates: 强制清理所有按钮状态', {
            processingCount: this.processingButtons.size
        });

        // 恢复所有正在处理的按钮
        for (const button of this.processingButtons) {
            this.restore(button);
        }

        // 清空集合
        this.processingButtons.clear();
    }

    /**
     * 获取当前处理中的按钮数量（用于调试）
     * @returns {number} 处理中的按钮数量
     */
    getProcessingCount() {
        return this.processingButtons.size;
    }

    /**
     * 获取调试信息
     * @returns {Object} 调试信息
     */
    getDebugInfo() {
        return {
            totalButtons: this.buttonStates.size,
            processingButtons: this.processingButtons.size,
            operationCounter: this.operationCounter
        };
    }
}

// 创建全局按钮状态管理器实例
export const buttonStateManager = new ButtonStateManager();

/**
 * 便捷函数：设置按钮加载状态
 * @param {Element} button - 按钮元素
 * @param {string} loadingText - 加载文本
 * @returns {boolean} 是否成功设置
 */
export function setButtonLoading(button, loadingText = '处理中...') {
    return buttonStateManager.setLoading(button, loadingText);
}

/**
 * 便捷函数：恢复按钮状态
 * @param {Element} button - 按钮元素
 * @param {string} customText - 自定义文本
 */
export function restoreButton(button, customText = null) {
    buttonStateManager.restore(button, customText);
}

/**
 * 便捷函数：检查按钮是否正在处理
 * @param {Element} button - 按钮元素
 * @returns {boolean} 是否正在处理中
 */
export function isButtonProcessing(button) {
    return buttonStateManager.isProcessing(button);
}

/**
 * 便捷函数：安全的异步按钮操作包装器
 * 自动管理按钮状态，确保操作完成后恢复状态
 * @param {Element} button - 按钮元素
 * @param {Function} asyncOperation - 异步操作函数
 * @param {string} loadingText - 加载时显示的文本
 * @param {string} successText - 成功后显示的文本（可选）
 * @param {string} errorText - 失败后显示的文本（可选）
 * @returns {Promise} 操作结果
 */
export async function safeButtonOperation(button, asyncOperation, loadingText = '处理中...', successText = null, errorText = null) {
    // 检查参数
    if (!button || typeof asyncOperation !== 'function') {
        console.error('safeButtonOperation: 无效的参数', { button: !!button, asyncOperation: typeof asyncOperation });
        return Promise.reject(new Error('无效的参数'));
    }

    // 设置加载状态
    const success = setButtonLoading(button, loadingText);
    if (!success) {
        console.warn('safeButtonOperation: 无法设置按钮加载状态，可能已在处理中');
        return Promise.reject(new Error('按钮正在处理中'));
    }

    try {
        // 执行异步操作
        const result = await asyncOperation();

        // 操作成功，恢复按钮状态
        restoreButton(button, successText);

        return result;
    } catch (error) {
        // 操作失败，恢复按钮状态并显示错误文本
        restoreButton(button, errorText);

        // 重新抛出错误供调用者处理
        throw error;
    }
}
