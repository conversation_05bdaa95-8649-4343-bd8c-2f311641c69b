/**
 * 用户界面模块
 * 
 * 提供用户信息展示、加载状态管理、错误处理等功能
 */

import { userAPI } from './api.js';

/**
 * 用户界面管理器
 */
export class UserUI {
    constructor() {
        this.loadingStates = new Map(); // 跟踪加载状态
        this.errorRetryCallbacks = new Map(); // 错误重试回调
    }

    /**
     * 显示用户信息
     * @param {string} userId - 用户ID
     * @param {HTMLElement} container - 容器元素
     * @param {Object} options - 显示选项
     */
    async displayUser(userId, container, options = {}) {
        const {
            showLoadingSpinner = true,
            enableRetry = true,
            cacheEnabled = true
        } = options;

        try {
            // 显示加载状态
            if (showLoadingSpinner) {
                this.showLoadingState(container, userId);
            }

            // 获取用户信息
            const response = await userAPI.getUser(userId, {
                useCache: cacheEnabled,
                retryCount: enableRetry ? 3 : 0
            });

            // 显示用户信息
            this.renderUserInfo(container, response.data, userId);
            
            // 清除加载状态
            this.clearLoadingState(userId);

        } catch (error) {
            // 处理错误
            this.handleUserError(container, error, userId, () => {
                this.displayUser(userId, container, options);
            });
        }
    }

    /**
     * 显示加载状态
     * @param {HTMLElement} container - 容器元素
     * @param {string} userId - 用户ID
     */
    showLoadingState(container, userId) {
        this.loadingStates.set(userId, true);
        
        container.innerHTML = `
            <div class="user-loading" data-user-id="${userId}">
                <div class="loading-spinner">
                    <div class="spinner"></div>
                </div>
                <p class="loading-text">正在加载用户信息...</p>
            </div>
        `;
    }

    /**
     * 清除加载状态
     * @param {string} userId - 用户ID
     */
    clearLoadingState(userId) {
        this.loadingStates.delete(userId);
    }

    /**
     * 渲染用户信息
     * @param {HTMLElement} container - 容器元素
     * @param {Object} user - 用户数据
     * @param {string} userId - 用户ID
     */
    renderUserInfo(container, user, userId) {
        const avatarUrl = user.avatar_url || '/static/images/default-avatar.png';
        const displayName = user.display_name || user.username;
        const joinDate = new Date(user.created_at).toLocaleDateString('zh-CN');
        const lastLogin = user.last_login_at 
            ? new Date(user.last_login_at).toLocaleString('zh-CN')
            : '从未登录';

        container.innerHTML = `
            <div class="user-profile" data-user-id="${userId}">
                <div class="user-header">
                    <div class="user-avatar">
                        <img src="${avatarUrl}" alt="${displayName}的头像" 
                             onerror="this.src='/static/images/default-avatar.png'">
                        <span class="user-status ${user.status}">${this.getStatusText(user.status)}</span>
                    </div>
                    <div class="user-basic-info">
                        <h2 class="user-display-name">${this.escapeHtml(displayName)}</h2>
                        <p class="user-username">@${this.escapeHtml(user.username)}</p>
                        <p class="user-email">${this.escapeHtml(user.email || '未设置邮箱')}</p>
                    </div>
                </div>
                
                <div class="user-details">
                    ${user.bio ? `<div class="user-bio">
                        <h3>个人简介</h3>
                        <p>${this.escapeHtml(user.bio)}</p>
                    </div>` : ''}
                    
                    <div class="user-info-grid">
                        ${user.location ? `<div class="info-item">
                            <span class="info-label">位置</span>
                            <span class="info-value">${this.escapeHtml(user.location)}</span>
                        </div>` : ''}
                        
                        ${user.website ? `<div class="info-item">
                            <span class="info-label">网站</span>
                            <a href="${this.escapeHtml(user.website)}" target="_blank" class="info-value">
                                ${this.escapeHtml(user.website)}
                            </a>
                        </div>` : ''}
                        
                        <div class="info-item">
                            <span class="info-label">加入时间</span>
                            <span class="info-value">${joinDate}</span>
                        </div>
                        
                        <div class="info-item">
                            <span class="info-label">最后登录</span>
                            <span class="info-value">${lastLogin}</span>
                        </div>
                    </div>
                </div>
                
                <div class="user-actions">
                    <button class="btn btn-primary" onclick="userUI.refreshUser('${userId}')">
                        刷新信息
                    </button>
                    <button class="btn btn-secondary" onclick="userUI.clearUserCache('${userId}')">
                        清除缓存
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 处理用户获取错误
     * @param {HTMLElement} container - 容器元素
     * @param {Error} error - 错误对象
     * @param {string} userId - 用户ID
     * @param {Function} retryCallback - 重试回调函数
     */
    handleUserError(container, error, userId, retryCallback) {
        this.clearLoadingState(userId);
        this.errorRetryCallbacks.set(userId, retryCallback);

        let errorMessage = '获取用户信息失败';
        let errorClass = 'error-general';
        let showRetry = true;

        // 根据错误类型显示不同的消息
        if (error.status === 404) {
            errorMessage = '用户不存在';
            errorClass = 'error-not-found';
            showRetry = false;
        } else if (error.status === 403) {
            errorMessage = '没有权限查看此用户信息';
            errorClass = 'error-forbidden';
            showRetry = false;
        } else if (error.status === 401) {
            errorMessage = '请先登录';
            errorClass = 'error-unauthorized';
            showRetry = false;
        } else if (error.message && error.message.includes('UUID')) {
            errorMessage = '用户ID格式无效';
            errorClass = 'error-invalid-id';
            showRetry = false;
        }

        container.innerHTML = `
            <div class="user-error ${errorClass}" data-user-id="${userId}">
                <div class="error-icon">⚠️</div>
                <h3 class="error-title">加载失败</h3>
                <p class="error-message">${errorMessage}</p>
                ${error.message ? `<p class="error-details">${this.escapeHtml(error.message)}</p>` : ''}
                
                <div class="error-actions">
                    ${showRetry ? `
                        <button class="btn btn-primary" onclick="userUI.retryLoadUser('${userId}')">
                            重试
                        </button>
                    ` : ''}
                    <button class="btn btn-secondary" onclick="userUI.clearUserCache('${userId}')">
                        清除缓存
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 重试加载用户信息
     * @param {string} userId - 用户ID
     */
    retryLoadUser(userId) {
        const retryCallback = this.errorRetryCallbacks.get(userId);
        if (retryCallback) {
            retryCallback();
        }
    }

    /**
     * 刷新用户信息
     * @param {string} userId - 用户ID
     */
    async refreshUser(userId) {
        // 清除缓存
        userAPI.clearCache(userId);
        
        // 重新加载
        const container = document.querySelector(`[data-user-id="${userId}"]`)?.closest('.user-container');
        if (container) {
            await this.displayUser(userId, container);
        }
    }

    /**
     * 清除用户缓存
     * @param {string} userId - 用户ID
     */
    clearUserCache(userId) {
        userAPI.clearCache(userId);
        
        // 显示成功消息
        this.showToast('缓存已清除', 'success');
    }

    /**
     * 获取状态文本
     * @param {string} status - 状态值
     * @returns {string} 状态文本
     */
    getStatusText(status) {
        const statusMap = {
            'active': '活跃',
            'inactive': '非活跃',
            'suspended': '已暂停'
        };
        return statusMap[status] || status;
    }

    /**
     * HTML转义
     * @param {string} text - 要转义的文本
     * @returns {string} 转义后的文本
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示提示消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 (success, error, warning, info)
     */
    showToast(message, type = 'info') {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        // 添加到页面
        document.body.appendChild(toast);
        
        // 自动移除
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }

    /**
     * 检查用户是否正在加载
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否正在加载
     */
    isUserLoading(userId) {
        return this.loadingStates.has(userId);
    }
}

// 创建全局实例
export const userUI = new UserUI();

// 将实例添加到全局作用域，以便在HTML中使用
window.userUI = userUI;
