/**
 * WebSocket实时监控模块
 * 
 * 功能：
 * - 实时获取WebSocket统计数据
 * - 自动刷新监控面板
 * - 图表展示和数据可视化
 * - 连接质量监控
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { getAuthToken } from './auth.js';

// ==== 监控配置常量 ====
const API_ENDPOINTS = {
    STATS: '/api/websocket/stats',
    CONNECTIONS: '/api/websocket/connections',
    METRICS: '/api/websocket/metrics',
    STABILITY: '/api/websocket/stability'
};

const WEBSOCKET_ENDPOINTS = {
    MONITORING: '/api/websocket/monitoring'
};

const DEFAULT_REFRESH_INTERVAL = 10000; // 10秒
const MAX_RETRY_ATTEMPTS = 3;
const RETRY_DELAY = 2000; // 2秒
const WEBSOCKET_RECONNECT_DELAY = 5000; // WebSocket重连延迟5秒

// ==== 全局状态管理 ====
class MonitoringState {
    constructor() {
        this.isAutoRefreshEnabled = false;
        this.refreshInterval = DEFAULT_REFRESH_INTERVAL;
        this.refreshTimer = null;
        this.retryCount = 0;
        this.lastUpdateTime = null;
        this.performanceHistory = [];
        this.maxHistoryLength = 50;

        // WebSocket实时连接相关
        this.websocket = null;
        this.isWebSocketConnected = false;
        this.websocketReconnectTimer = null;
        this.websocketReconnectAttempts = 0;
        this.maxWebSocketReconnectAttempts = 5;

        // 图表实例
        this.charts = {
            connectionType: null,
            messageType: null,
            performance: null
        };
    }

    startAutoRefresh() {
        this.isAutoRefreshEnabled = true;
        this.refreshTimer = setInterval(() => {
            this.refreshAllData();
        }, this.refreshInterval);
        console.log(`自动刷新已启动，间隔: ${this.refreshInterval}ms`);
    }

    stopAutoRefresh() {
        this.isAutoRefreshEnabled = false;
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
        }
        console.log('自动刷新已停止');
    }

    setRefreshInterval(interval) {
        this.refreshInterval = interval;
        if (this.isAutoRefreshEnabled) {
            this.stopAutoRefresh();
            this.startAutoRefresh();
        }
    }

    // ==== WebSocket实时连接管理 ====

    /**
     * 建立WebSocket连接用于实时监控
     */
    connectWebSocket() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            console.log('WebSocket已连接，跳过重复连接');
            return;
        }

        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}${WEBSOCKET_ENDPOINTS.MONITORING}`;

            console.log('正在连接WebSocket监控服务:', wsUrl);
            this.websocket = new WebSocket(wsUrl);

            this.websocket.onopen = () => {
                console.log('WebSocket监控连接已建立');
                this.isWebSocketConnected = true;
                this.websocketReconnectAttempts = 0;
                this.updateConnectionStatus('connected');

                // 发送认证信息
                const token = getAuthToken();
                if (token) {
                    this.websocket.send(JSON.stringify({
                        type: 'auth',
                        token: token
                    }));
                }
            };

            this.websocket.onmessage = (event) => {
                try {
                    const data = JSON.parse(event.data);
                    this.handleWebSocketMessage(data);
                } catch (error) {
                    console.error('解析WebSocket消息失败:', error);
                }
            };

            this.websocket.onclose = (event) => {
                console.log('WebSocket连接已关闭:', event.code, event.reason);
                this.isWebSocketConnected = false;
                this.updateConnectionStatus('disconnected');
                this.scheduleWebSocketReconnect();
            };

            this.websocket.onerror = (error) => {
                console.error('WebSocket连接错误:', error);
                this.isWebSocketConnected = false;
                this.updateConnectionStatus('error');
            };

        } catch (error) {
            console.error('创建WebSocket连接失败:', error);
            this.scheduleWebSocketReconnect();
        }
    }

    /**
     * 处理WebSocket实时消息
     */
    handleWebSocketMessage(data) {
        console.log('收到WebSocket监控数据:', data);

        switch (data.type) {
            case 'monitoring_update':
                // 新的统一监控数据格式
                this.updateAllDisplaysFromMonitoringData(data.data);
                break;
            case 'stats_update':
                this.updateStatsDisplay(data.payload);
                break;
            case 'connections_update':
                this.updateConnectionsDisplay(data.payload);
                break;
            case 'metrics_update':
                this.updateMetricsDisplay(data.payload);
                this.addToPerformanceHistory(data.payload);
                break;
            case 'stability_update':
                this.updateStabilityDisplay(data.payload);
                break;
            case 'heartbeat':
                // 心跳响应，保持连接活跃
                break;
            default:
                console.warn('未知的WebSocket消息类型:', data.type);
        }

        this.updateLastRefreshTime();
    }

    /**
     * 从统一监控数据更新所有显示
     */
    updateAllDisplaysFromMonitoringData(monitoringData) {
        console.log('更新所有监控显示:', monitoringData);

        // 更新连接统计
        if (monitoringData.connection_stats) {
            this.updateStatsDisplay({
                connection_stats: monitoringData.connection_stats,
                message_stats: monitoringData.message_stats
            });
        }

        // 更新性能指标
        if (monitoringData.performance) {
            this.updateMetricsDisplay({
                performance: monitoringData.performance
            });
            this.addToPerformanceHistory(monitoringData.performance);
        }

        // 更新稳定性指标
        if (monitoringData.stability) {
            this.updateStabilityDisplay({
                stability: monitoringData.stability
            });
        }
    }

    /**
     * 安排WebSocket重连
     */
    scheduleWebSocketReconnect() {
        if (this.websocketReconnectAttempts >= this.maxWebSocketReconnectAttempts) {
            console.error('WebSocket重连次数已达上限，停止重连');
            this.updateConnectionStatus('failed');
            return;
        }

        this.websocketReconnectAttempts++;
        console.log(`安排WebSocket重连 (尝试 ${this.websocketReconnectAttempts}/${this.maxWebSocketReconnectAttempts})`);

        this.websocketReconnectTimer = setTimeout(() => {
            this.connectWebSocket();
        }, WEBSOCKET_RECONNECT_DELAY);
    }

    /**
     * 断开WebSocket连接
     */
    disconnectWebSocket() {
        if (this.websocketReconnectTimer) {
            clearTimeout(this.websocketReconnectTimer);
            this.websocketReconnectTimer = null;
        }

        if (this.websocket) {
            this.websocket.close();
            this.websocket = null;
        }

        this.isWebSocketConnected = false;
        this.updateConnectionStatus('disconnected');
    }

    /**
     * 更新连接状态显示
     */
    updateConnectionStatus(status) {
        const statusElement = document.getElementById('connectionStatus');
        if (!statusElement) return;

        const statusConfig = {
            connected: { text: '实时连接', class: 'status-online' },
            disconnected: { text: '已断开', class: 'status-warning' },
            error: { text: '连接错误', class: 'status-offline' },
            failed: { text: '连接失败', class: 'status-offline' }
        };

        const config = statusConfig[status] || statusConfig.disconnected;
        statusElement.textContent = config.text;
        statusElement.className = `status-indicator ${config.class}`;
    }

    async refreshAllData() {
        try {
            await Promise.all([
                this.fetchStats(),
                this.fetchConnections(),
                this.fetchMetrics(),
                this.fetchStability()
            ]);
            this.retryCount = 0;
            this.updateLastRefreshTime();
            this.hideError();
        } catch (error) {
            console.error('刷新数据失败:', error);
            this.handleRefreshError(error);
        }
    }

    async fetchStats() {
        const data = await this.makeApiRequest(API_ENDPOINTS.STATS);
        this.updateStatsDisplay(data);
        return data;
    }

    async fetchConnections() {
        const data = await this.makeApiRequest(API_ENDPOINTS.CONNECTIONS);
        this.updateConnectionsDisplay(data);
        return data;
    }

    async fetchMetrics() {
        const data = await this.makeApiRequest(API_ENDPOINTS.METRICS);
        this.updateMetricsDisplay(data);
        this.addToPerformanceHistory(data);
        return data;
    }

    async fetchStability() {
        const data = await this.makeApiRequest(API_ENDPOINTS.STABILITY);
        this.updateStabilityDisplay(data);
        return data;
    }

    async makeApiRequest(endpoint) {
        const token = getAuthToken();
        const headers = {
            'Content-Type': 'application/json'
        };

        if (token) {
            headers['Authorization'] = `Bearer ${token}`;
        }

        const response = await fetch(endpoint, {
            method: 'GET',
            headers: headers
        });

        if (!response.ok) {
            throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    updateStatsDisplay(data) {
        const connectionStats = data.connection_stats || {};
        const messageStats = data.message_stats || {};

        // 更新统计卡片
        this.updateElement('activeConnections', connectionStats.active_connections || 0);
        this.updateElement('totalConnections', connectionStats.total_connections || 0);
        this.updateElement('uniqueUsers', connectionStats.unique_users || 0);

        // 更新连接成功率
        const successRate = connectionStats.connection_success_rate || 0;
        this.updateElement('connectionSuccessRate', `${(successRate * 100).toFixed(1)}%`);

        console.log('统计数据已更新:', connectionStats);
    }

    updateConnectionsDisplay(data) {
        // 更新活跃连接列表
        const connectionsList = document.getElementById('activeConnectionsList');
        if (!connectionsList) return;

        const connectionsHtml = this.generateConnectionsHtml(data);
        connectionsList.innerHTML = connectionsHtml;

        // 更新连接类型图表数据
        this.updateConnectionTypeChart(data.connections_by_type || {});
    }

    updateMetricsDisplay(data) {
        const performance = data.performance || {};
        const messageStats = data.message_stats || {};

        // 更新性能指标
        this.updateElement('messageThroughput', performance.message_throughput_per_second || 0);
        this.updateElement('averageLatency', `${performance.average_latency_ms || 0}ms`);

        // 更新消息类型图表
        if (messageStats.messages_by_type) {
            this.updateMessageTypeChart(messageStats.messages_by_type);
        }

        console.log('性能指标已更新:', performance);
    }

    updateStabilityDisplay(data) {
        const stability = data.stability || {};
        
        // 可以在这里添加稳定性指标的显示逻辑
        console.log('稳定性数据已更新:', stability);
    }

    generateConnectionsHtml(data) {
        if (!data.active_connections || data.active_connections === 0) {
            return '<div class="connection-item">当前没有活跃连接</div>';
        }

        // 模拟连接详情（实际应用中应从API获取）
        let html = '';
        const connectionTypes = data.connections_by_type || {};
        
        Object.entries(connectionTypes).forEach(([type, count]) => {
            for (let i = 0; i < Math.min(count, 5); i++) { // 最多显示5个连接
                html += `
                    <div class="connection-item">
                        <div class="connection-info">
                            <div class="connection-user">用户_${type}_${i + 1}</div>
                            <div class="connection-details">
                                类型: ${type} | 连接时长: ${Math.floor(Math.random() * 300)}秒
                            </div>
                        </div>
                        <span class="status-indicator status-online"></span>
                    </div>
                `;
            }
        });

        return html || '<div class="connection-item">无连接详情</div>';
    }

    updateConnectionTypeChart(connectionsByType) {
        if (!connectionsByType || typeof Chart === 'undefined') return;

        // 销毁现有图表
        if (this.charts.connectionType) {
            this.charts.connectionType.destroy();
        }

        const ctx = document.getElementById('connectionTypeChart');
        if (!ctx) return;

        const labels = Object.keys(connectionsByType);
        const data = Object.values(connectionsByType);
        const colors = [
            '#667eea', '#764ba2', '#f093fb', '#f5576c',
            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
        ];

        this.charts.connectionType = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors.slice(0, labels.length),
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * 更新消息类型统计图表
     */
    updateMessageTypeChart(messagesByType) {
        if (!messagesByType || typeof Chart === 'undefined') return;

        // 销毁现有图表
        if (this.charts.messageType) {
            this.charts.messageType.destroy();
        }

        const ctx = document.getElementById('messageTypeChart');
        if (!ctx) return;

        const labels = Object.keys(messagesByType);
        const data = Object.values(messagesByType);
        const colors = [
            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
            '#667eea', '#764ba2', '#f093fb', '#f5576c'
        ];

        this.charts.messageType = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '消息数量',
                    data: data,
                    backgroundColor: colors.slice(0, labels.length),
                    borderColor: colors.slice(0, labels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.parsed.y} 条消息`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '消息数量'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '消息类型'
                        }
                    }
                }
            }
        });
    }

    addToPerformanceHistory(metricsData) {
        const performance = metricsData.performance || {};
        const timestamp = new Date().toLocaleTimeString();
        
        this.performanceHistory.push({
            timestamp,
            throughput: performance.message_throughput_per_second || 0,
            latency: performance.average_latency_ms || 0
        });

        // 保持历史记录长度限制
        if (this.performanceHistory.length > this.maxHistoryLength) {
            this.performanceHistory.shift();
        }

        this.updatePerformanceChart();
    }

    updatePerformanceChart() {
        if (this.performanceHistory.length === 0 || typeof Chart === 'undefined') return;

        // 销毁现有图表
        if (this.charts.performance) {
            this.charts.performance.destroy();
        }

        const ctx = document.getElementById('performanceChart');
        if (!ctx) return;

        const recentData = this.performanceHistory.slice(-20); // 显示最近20条记录
        const labels = recentData.map(d => d.timestamp);
        const throughputData = recentData.map(d => d.throughput);
        const latencyData = recentData.map(d => d.latency);

        this.charts.performance = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [
                    {
                        label: '吞吐量 (消息/秒)',
                        data: throughputData,
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    },
                    {
                        label: '延迟 (毫秒)',
                        data: latencyData,
                        borderColor: '#f5576c',
                        backgroundColor: 'rgba(245, 87, 108, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }
                ]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '时间'
                        },
                        ticks: {
                            maxTicksLimit: 10
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '吞吐量 (消息/秒)'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '延迟 (毫秒)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false,
                    }
                }
            }
        });
    }

    updateElement(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = value;
        }
    }

    updateLastRefreshTime() {
        this.lastUpdateTime = new Date();
        const timeString = this.lastUpdateTime.toLocaleString();
        this.updateElement('lastUpdated', `最后更新: ${timeString}`);
    }

    handleRefreshError(error) {
        this.retryCount++;
        console.error(`数据刷新失败 (尝试 ${this.retryCount}/${MAX_RETRY_ATTEMPTS}):`, error);
        
        if (this.retryCount >= MAX_RETRY_ATTEMPTS) {
            this.showError(`数据获取失败: ${error.message}。请检查网络连接或稍后重试。`);
            this.stopAutoRefresh();
        } else {
            // 延迟重试
            setTimeout(() => {
                this.refreshAllData();
            }, RETRY_DELAY);
        }
    }

    showError(message) {
        const errorElement = document.getElementById('errorMessage');
        if (errorElement) {
            errorElement.textContent = message;
            errorElement.style.display = 'block';
        }
    }

    hideError() {
        const errorElement = document.getElementById('errorMessage');
        if (errorElement) {
            errorElement.style.display = 'none';
        }
    }

    showLoading() {
        const loadingElement = document.getElementById('loadingIndicator');
        if (loadingElement) {
            loadingElement.style.display = 'block';
        }
    }

    hideLoading() {
        const loadingElement = document.getElementById('loadingIndicator');
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }
    }
}

// ==== 初始化监控面板 ====
const monitoringState = new MonitoringState();

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', async () => {
    console.log('WebSocket监控面板初始化中...');

    // 绑定事件监听器
    setupEventListeners();

    // 初始数据加载
    try {
        monitoringState.showLoading();
        await monitoringState.refreshAllData();
        monitoringState.hideLoading();

        // 建立WebSocket实时连接
        monitoringState.connectWebSocket();

        console.log('监控面板初始化完成');
    } catch (error) {
        console.error('初始化失败:', error);
        monitoringState.hideLoading();
        monitoringState.showError('初始化失败，请刷新页面重试');
    }
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    monitoringState.disconnectWebSocket();
    monitoringState.stopAutoRefresh();
});

function setupEventListeners() {
    // 立即刷新按钮
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', async () => {
            refreshBtn.disabled = true;
            refreshBtn.textContent = '刷新中...';
            
            try {
                await monitoringState.refreshAllData();
            } finally {
                refreshBtn.disabled = false;
                refreshBtn.textContent = '立即刷新';
            }
        });
    }

    // 自动刷新切换按钮
    const autoRefreshBtn = document.getElementById('autoRefreshBtn');
    if (autoRefreshBtn) {
        autoRefreshBtn.addEventListener('click', () => {
            if (monitoringState.isAutoRefreshEnabled) {
                monitoringState.stopAutoRefresh();
                autoRefreshBtn.textContent = '开启自动刷新';
                autoRefreshBtn.style.background = '#667eea';
            } else {
                monitoringState.startAutoRefresh();
                autoRefreshBtn.textContent = '停止自动刷新';
                autoRefreshBtn.style.background = '#dc3545';
            }
        });
    }

    // 刷新间隔选择
    const refreshInterval = document.getElementById('refreshInterval');
    if (refreshInterval) {
        refreshInterval.addEventListener('change', (e) => {
            const interval = parseInt(e.target.value);
            monitoringState.setRefreshInterval(interval);
            console.log(`刷新间隔已更改为: ${interval}ms`);
        });
    }
}

// 导出监控状态对象供其他模块使用
export { monitoringState };
