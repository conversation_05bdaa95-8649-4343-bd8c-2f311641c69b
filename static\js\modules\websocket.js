/**
 * WebSocket模块 - 处理实时通信功能
 * 基于ES6模块化设计，遵循Clean Code JavaScript最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-23
 */

import { getAuthToken, isAuthenticated, onAuthStateChange } from './auth.js';

// ==== WebSocket配置常量 ====
const WS_URL = `ws://${window.location.host}/api/ws`;
const RECONNECT_INTERVAL = 3000; // 3秒重连间隔
const MAX_RECONNECT_ATTEMPTS = 10;
const HEARTBEAT_INTERVAL = 30000; // 30秒心跳间隔

// ==== WebSocket状态枚举 ====
export const WS_STATE = {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
};

// ==== 消息类型常量 ====
export const MESSAGE_TYPE = {
    CHAT: 'chat',
    SYSTEM: 'system',
    USER_JOINED: 'user_joined',
    USER_LEFT: 'user_left',
    PING: 'ping',
    PONG: 'pong',
    ERROR: 'error',
    GET_USERS: 'get_users',
    USERS_LIST: 'users_list'
};

/**
 * WebSocket管理器类
 */
class WebSocketManager {
    constructor() {
        this.socket = null;
        this.reconnectAttempts = 0;
        this.reconnectTimer = null;
        this.heartbeatTimer = null;
        this.messageHandlers = new Map();
        this.connectionStateHandlers = new Set();
        this.isManualClose = false;
        
        // 监听认证状态变化
        onAuthStateChange((isAuth) => {
            if (isAuth) {
                this.connect();
            } else {
                this.disconnect();
            }
        });
    }

    /**
     * 连接WebSocket
     */
    connect() {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            console.log('WebSocket已连接，无需重复连接');
            return;
        }

        if (!isAuthenticated()) {
            console.warn('用户未认证，无法建立WebSocket连接');
            return;
        }

        try {
            console.log('正在连接WebSocket...');
            this.isManualClose = false;
            
            // 创建WebSocket连接，包含认证令牌和会话类型
            const token = getAuthToken();
            const sessionType = 'task_realtime'; // 设置为任务实时更新会话类型
            let wsUrl = WS_URL;

            if (token) {
                wsUrl += `?token=${encodeURIComponent(token)}&session_type=${encodeURIComponent(sessionType)}`;
            } else {
                wsUrl += `?session_type=${encodeURIComponent(sessionType)}`;
            }
            
            this.socket = new WebSocket(wsUrl);
            this.setupEventHandlers();

            console.log('WebSocket连接已创建，会话类型: task_realtime');
            
            this.notifyConnectionState('connecting');
        } catch (error) {
            console.error('WebSocket连接失败:', error);
            this.scheduleReconnect();
        }
    }

    /**
     * 断开WebSocket连接
     */
    disconnect() {
        this.isManualClose = true;
        this.clearTimers();
        
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        
        this.notifyConnectionState('disconnected');
        console.log('WebSocket连接已断开');
    }

    /**
     * 设置WebSocket事件处理器
     */
    setupEventHandlers() {
        if (!this.socket) return;

        this.socket.onopen = () => {
            console.log('WebSocket连接已建立');
            this.reconnectAttempts = 0;
            this.startHeartbeat();
            this.notifyConnectionState('connected');
        };

        this.socket.onmessage = (event) => {
            this.handleMessage(event);
        };

        this.socket.onclose = (event) => {
            console.log('WebSocket连接已关闭:', event.code, event.reason);
            this.clearTimers();
            this.notifyConnectionState('disconnected');
            
            if (!this.isManualClose && this.reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
                this.scheduleReconnect();
            }
        };

        this.socket.onerror = (error) => {
            console.error('WebSocket错误:', error);
            this.notifyConnectionState('error');
        };
    }

    /**
     * 处理接收到的消息
     * @param {MessageEvent} event - WebSocket消息事件
     */
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            console.log('收到WebSocket消息:', message);

            // 🔧 方案一修复：消息格式适配 - 将服务器格式转换为客户端期望格式
            let adaptedMessage = { ...message };

            // 适配服务器消息格式到客户端格式
            if (message.message_type === 'Text' && !message.type) {
                adaptedMessage.type = MESSAGE_TYPE.CHAT;
                console.log('✅ 消息格式已适配: message_type="Text" -> type="chat"');
            } else if (message.message_type && !message.type) {
                // 处理其他消息类型的适配
                const typeMapping = {
                    'Text': MESSAGE_TYPE.CHAT,
                    'System': MESSAGE_TYPE.SYSTEM,
                    'UserJoined': MESSAGE_TYPE.USER_JOINED,
                    'UserLeft': MESSAGE_TYPE.USER_LEFT,
                    'Ping': MESSAGE_TYPE.PING,
                    'Pong': MESSAGE_TYPE.PONG,
                    'GetUsers': MESSAGE_TYPE.GET_USERS,
                    'UsersList': MESSAGE_TYPE.USERS_LIST
                };
                adaptedMessage.type = typeMapping[message.message_type] || message.message_type.toLowerCase();
                console.log(`✅ 消息格式已适配: message_type="${message.message_type}" -> type="${adaptedMessage.type}"`);
            }

            // 适配发送者格式 - 修复：正确处理发送者对象到字符串的转换
            if (message.sender && message.sender.username && typeof adaptedMessage.sender === 'object') {
                adaptedMessage.sender = message.sender.username;
                console.log('✅ 发送者格式已适配:', message.sender.username);
            }

            // 🔧 修复：过滤心跳消息，防止显示在聊天界面中
            if (adaptedMessage.type === MESSAGE_TYPE.PING) {
                console.log('收到心跳请求，仅记录日志');
                return; // 不传递给消息处理器，避免在聊天界面显示
            }

            if (adaptedMessage.type === MESSAGE_TYPE.PONG) {
                console.log('收到心跳响应，仅记录日志');
                return; // 不传递给消息处理器，避免在聊天界面显示
            }

            // 分发消息给注册的处理器
            const handlers = this.messageHandlers.get(adaptedMessage.type) || [];
            handlers.forEach(handler => {
                try {
                    handler(adaptedMessage);
                } catch (error) {
                    console.error('消息处理器执行失败:', error);
                }
            });

            // 分发给通用消息处理器
            const allHandlers = this.messageHandlers.get('*') || [];
            allHandlers.forEach(handler => {
                try {
                    handler(adaptedMessage);
                } catch (error) {
                    console.error('通用消息处理器执行失败:', error);
                }
            });
            
        } catch (error) {
            console.error('解析WebSocket消息失败:', error);
        }
    }

    /**
     * 发送消息
     * @param {Object} message - 要发送的消息对象
     * @returns {boolean} 发送是否成功
     */
    send(message) {
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
            console.warn('WebSocket未连接，无法发送消息');
            return false;
        }

        try {
            const messageStr = JSON.stringify(message);
            this.socket.send(messageStr);
            console.log('发送WebSocket消息:', message);
            return true;
        } catch (error) {
            console.error('发送WebSocket消息失败:', error);
            return false;
        }
    }

    /**
     * 发送聊天消息
     * @param {string} content - 消息内容
     * @returns {boolean} 发送是否成功
     */
    sendChatMessage(content) {
        return this.send({
            type: MESSAGE_TYPE.CHAT,
            content: content,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 发送心跳消息
     * @returns {boolean} 发送是否成功
     */
    sendPing() {
        return this.send({
            type: MESSAGE_TYPE.PING,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 请求获取在线用户列表
     * @returns {boolean} 发送是否成功
     */
    requestOnlineUsers() {
        console.log('请求获取在线用户列表');
        return this.send({
            type: MESSAGE_TYPE.GET_USERS,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 注册消息处理器
     * @param {string} messageType - 消息类型，使用'*'监听所有消息
     * @param {Function} handler - 处理器函数
     */
    onMessage(messageType, handler) {
        if (!this.messageHandlers.has(messageType)) {
            this.messageHandlers.set(messageType, []);
        }
        this.messageHandlers.get(messageType).push(handler);
    }

    /**
     * 移除消息处理器
     * @param {string} messageType - 消息类型
     * @param {Function} handler - 处理器函数
     */
    offMessage(messageType, handler) {
        const handlers = this.messageHandlers.get(messageType);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * 注册连接状态变化处理器
     * @param {Function} handler - 处理器函数
     */
    onConnectionStateChange(handler) {
        this.connectionStateHandlers.add(handler);
    }

    /**
     * 移除连接状态变化处理器
     * @param {Function} handler - 处理器函数
     */
    offConnectionStateChange(handler) {
        this.connectionStateHandlers.delete(handler);
    }

    /**
     * 通知连接状态变化
     * @param {string} state - 连接状态
     */
    notifyConnectionState(state) {
        this.connectionStateHandlers.forEach(handler => {
            try {
                handler(state);
            } catch (error) {
                console.error('连接状态处理器执行失败:', error);
            }
        });
    }

    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.isManualClose) return;
        
        this.reconnectAttempts++;
        console.log(`${RECONNECT_INTERVAL}ms后进行第${this.reconnectAttempts}次重连...`);
        
        this.notifyConnectionState('reconnecting');
        
        this.reconnectTimer = setTimeout(() => {
            this.connect();
        }, RECONNECT_INTERVAL);
    }

    /**
     * 开始心跳
     */
    startHeartbeat() {
        this.heartbeatTimer = setInterval(() => {
            this.sendPing();
        }, HEARTBEAT_INTERVAL);
    }

    /**
     * 清除定时器
     */
    clearTimers() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
        
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
        }
    }

    /**
     * 获取当前连接状态
     * @returns {number} WebSocket状态
     */
    getState() {
        return this.socket ? this.socket.readyState : WS_STATE.CLOSED;
    }

    /**
     * 检查是否已连接
     * @returns {boolean} 是否已连接
     */
    isConnected() {
        return this.socket && this.socket.readyState === WebSocket.OPEN;
    }
}

// ==== 导出单例实例 ====
export const wsManager = new WebSocketManager();

// ==== 便捷方法导出 ====
export const connect = () => wsManager.connect();
export const disconnect = () => wsManager.disconnect();
export const send = (message) => wsManager.send(message);
export const sendChatMessage = (content) => wsManager.sendChatMessage(content);
export const sendPing = () => wsManager.sendPing();
export const requestOnlineUsers = () => wsManager.requestOnlineUsers();
export const onMessage = (messageType, handler) => wsManager.onMessage(messageType, handler);
export const offMessage = (messageType, handler) => wsManager.offMessage(messageType, handler);
export const onConnectionStateChange = (handler) => wsManager.onConnectionStateChange(handler);
export const offConnectionStateChange = (handler) => wsManager.offConnectionStateChange(handler);
export const getState = () => wsManager.getState();
export const isConnected = () => wsManager.isConnected();
