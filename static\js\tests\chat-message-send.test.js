/**
 * 聊天消息发送功能测试
 * 
 * 测试范围：
 * 1. 消息验证
 * 2. 消息发送成功场景
 * 3. 消息发送失败场景
 * 4. 重试机制
 * 5. 速率限制
 * 6. 发送状态指示
 */

import { chatAPI } from '../modules/api.js';

describe('聊天消息发送API测试', () => {
    let originalFetch;
    
    beforeEach(() => {
        // 模拟fetch函数
        originalFetch = global.fetch;
        global.fetch = jest.fn();
        
        // 重置速率限制器
        if (chatAPI._rateLimiter) {
            chatAPI._rateLimiter.reset();
        }
    });
    
    afterEach(() => {
        global.fetch = originalFetch;
        jest.clearAllMocks();
    });

    describe('消息验证测试', () => {
        test('应该拒绝空消息内容', async () => {
            await expect(chatAPI.sendMessage({
                content: '',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            })).rejects.toThrow('消息内容不能为空');
        });

        test('应该拒绝过长的消息内容', async () => {
            const longContent = 'a'.repeat(10001); // 超过10000字符限制
            
            await expect(chatAPI.sendMessage({
                content: longContent,
                sender: { user_id: 'test-user-id', username: 'testuser' }
            })).rejects.toThrow('消息内容不能超过10000个字符');
        });

        test('应该拒绝无效的发送者信息', async () => {
            await expect(chatAPI.sendMessage({
                content: '测试消息',
                sender: null
            })).rejects.toThrow('发送者信息不能为空');

            await expect(chatAPI.sendMessage({
                content: '测试消息',
                sender: { user_id: '', username: 'testuser' }
            })).rejects.toThrow('发送者用户ID不能为空');

            await expect(chatAPI.sendMessage({
                content: '测试消息',
                sender: { user_id: 'test-user-id', username: '' }
            })).rejects.toThrow('发送者用户名不能为空');
        });
    });

    describe('消息发送成功场景测试', () => {
        test('应该成功发送有效消息', async () => {
            const mockResponse = {
                success: true,
                data: {
                    message: '消息已广播到所有连接的客户端',
                    content: '测试消息',
                    sent_count: 5
                }
            };

            global.fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            const messageData = {
                content: '测试消息',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            };

            const result = await chatAPI.sendMessage(messageData);

            expect(global.fetch).toHaveBeenCalledWith(
                expect.stringContaining('/api/chat/send'),
                expect.objectContaining({
                    method: 'POST',
                    headers: expect.objectContaining({
                        'Content-Type': 'application/json'
                    }),
                    body: JSON.stringify(messageData)
                })
            );

            expect(result).toEqual(mockResponse);
        });

        test('应该正确处理发送状态回调', async () => {
            const mockResponse = {
                success: true,
                data: { sent_count: 3 }
            };

            global.fetch.mockResolvedValueOnce({
                ok: true,
                status: 200,
                json: async () => mockResponse
            });

            const statusCallback = jest.fn();
            
            await chatAPI.sendMessage({
                content: '测试消息',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            }, { onStatusChange: statusCallback });

            // 验证状态回调被正确调用
            expect(statusCallback).toHaveBeenCalledWith('sending');
            expect(statusCallback).toHaveBeenCalledWith('sent');
        });
    });

    describe('消息发送失败场景测试', () => {
        test('应该处理网络错误', async () => {
            global.fetch.mockRejectedValueOnce(new Error('网络连接失败'));

            const statusCallback = jest.fn();

            await expect(chatAPI.sendMessage({
                content: '测试消息',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            }, { onStatusChange: statusCallback, retryDelay: 0 })).rejects.toThrow('网络连接失败');

            expect(statusCallback).toHaveBeenCalledWith('sending');
            expect(statusCallback).toHaveBeenCalledWith('failed');
        });

        test('应该处理服务器错误响应', async () => {
            global.fetch.mockResolvedValueOnce({
                ok: false,
                status: 500,
                statusText: 'Internal Server Error',
                json: async () => ({ error: '服务器内部错误' })
            });

            const statusCallback = jest.fn();

            await expect(chatAPI.sendMessage({
                content: '测试消息',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            }, { onStatusChange: statusCallback, retryDelay: 0 })).rejects.toThrow('HTTP 500: Internal Server Error');

            expect(statusCallback).toHaveBeenCalledWith('failed');
        });
    });

    describe('重试机制测试', () => {
        test('应该在失败时自动重试', async () => {
            // 前两次失败，第三次成功
            global.fetch
                .mockRejectedValueOnce(new Error('网络错误'))
                .mockRejectedValueOnce(new Error('网络错误'))
                .mockResolvedValueOnce({
                    ok: true,
                    status: 200,
                    json: async () => ({ success: true, data: { sent_count: 1 } })
                });

            const result = await chatAPI.sendMessage({
                content: '测试消息',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            }, { retryCount: 3, retryDelay: 0 }); // 设置retryDelay为0避免setTimeout问题

            expect(global.fetch).toHaveBeenCalledTimes(3);
            expect(result.success).toBe(true);
        });

        test('应该在达到最大重试次数后失败', async () => {
            global.fetch.mockRejectedValue(new Error('持续网络错误'));

            await expect(chatAPI.sendMessage({
                content: '测试消息',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            }, { retryCount: 2, retryDelay: 0 })).rejects.toThrow('持续网络错误');

            expect(global.fetch).toHaveBeenCalledTimes(3); // 初始尝试 + 2次重试
        });
    });

    describe('速率限制测试', () => {
        test('应该限制发送频率', async () => {
            global.fetch.mockResolvedValue({
                ok: true,
                status: 200,
                json: async () => ({ success: true, data: { sent_count: 1 } })
            });

            const messageData = {
                content: '测试消息',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            };

            // 快速连续发送多条消息
            const promises = [];
            for (let i = 0; i < 5; i++) {
                promises.push(chatAPI.sendMessage(messageData));
            }

            // 应该有一些请求被速率限制拒绝
            const results = await Promise.allSettled(promises);
            const rejectedCount = results.filter(r => r.status === 'rejected').length;
            
            expect(rejectedCount).toBeGreaterThan(0);
        });
    });

    describe('消息队列测试', () => {
        test('应该将消息加入队列并按顺序处理', async () => {
            let callOrder = [];

            global.fetch.mockImplementation(async (url, options) => {
                const body = JSON.parse(options.body);
                callOrder.push(body.content);

                return {
                    ok: true,
                    status: 200,
                    json: async () => ({ success: true, data: { sent_count: 1 } })
                };
            });

            const messageData1 = {
                content: '消息1',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            };

            const messageData2 = {
                content: '消息2',
                sender: { user_id: 'test-user-id', username: 'testuser' }
            };

            // 同时发送两条消息
            await Promise.all([
                chatAPI.sendMessage(messageData1, { retryDelay: 0 }),
                chatAPI.sendMessage(messageData2, { retryDelay: 0 })
            ]);

            // 验证消息按顺序处理
            expect(callOrder).toEqual(['消息1', '消息2']);
        });
    });
});
