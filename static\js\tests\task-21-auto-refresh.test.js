/**
 * 任务21：定时刷新机制前端测试
 * 测试前端定时刷新功能的完整性和正确性
 */

// 模拟DOM环境
const { JSDOM } = require('jsdom');
const dom = new JSDOM('<!DOCTYPE html><html><body></body></html>');
global.window = dom.window;
global.document = dom.window.document;
global.navigator = dom.window.navigator;

// 模拟Page Visibility API
Object.defineProperty(document, 'visibilityState', {
    writable: true,
    value: 'visible'
});

Object.defineProperty(document, 'hidden', {
    writable: true,
    value: false
});

// 模拟事件监听器
document.addEventListener = jest.fn();

describe('任务21：定时刷新机制测试', () => {
    let autoRefreshManager;
    let mockApiClient;

    beforeEach(() => {
        // 重置模拟
        jest.clearAllMocks();
        jest.clearAllTimers();
        jest.useFakeTimers();

        // 模拟API客户端
        mockApiClient = {
            get: jest.fn().mockResolvedValue({ data: 'test' }),
            post: jest.fn().mockResolvedValue({ data: 'test' })
        };

        // 创建测试用的DOM元素
        document.body.innerHTML = `
            <div id="autoRefreshControls">
                <button id="autoRefreshToggle">开启自动刷新</button>
                <select id="refreshInterval">
                    <option value="5000">5秒</option>
                    <option value="10000">10秒</option>
                    <option value="30000">30秒</option>
                    <option value="60000">1分钟</option>
                </select>
                <div id="refreshStatus">空闲</div>
                <div id="lastUpdateTime"></div>
            </div>
        `;

        // 初始化自动刷新管理器
        autoRefreshManager = new AutoRefreshManager({
            apiClient: mockApiClient,
            defaultInterval: 10000
        });
    });

    afterEach(() => {
        jest.useRealTimers();
        if (autoRefreshManager) {
            autoRefreshManager.destroy();
        }
    });

    describe('基本功能测试', () => {
        test('应该能够启动和停止自动刷新', () => {
            // 启动自动刷新
            autoRefreshManager.start();
            expect(autoRefreshManager.isRunning()).toBe(true);

            // 停止自动刷新
            autoRefreshManager.stop();
            expect(autoRefreshManager.isRunning()).toBe(false);
        });

        test('应该支持可配置的刷新间隔', () => {
            const intervals = [5000, 10000, 30000, 60000];
            
            intervals.forEach(interval => {
                autoRefreshManager.setInterval(interval);
                expect(autoRefreshManager.getInterval()).toBe(interval);
            });
        });

        test('应该在指定间隔执行刷新', () => {
            const refreshCallback = jest.fn();
            autoRefreshManager.onRefresh(refreshCallback);
            
            autoRefreshManager.start();
            
            // 快进10秒
            jest.advanceTimersByTime(10000);
            expect(refreshCallback).toHaveBeenCalledTimes(1);
            
            // 再快进10秒
            jest.advanceTimersByTime(10000);
            expect(refreshCallback).toHaveBeenCalledTimes(2);
        });
    });

    describe('页面可见性检测测试', () => {
        test('页面隐藏时应该暂停刷新', () => {
            const refreshCallback = jest.fn();
            autoRefreshManager.onRefresh(refreshCallback);
            autoRefreshManager.start();

            // 模拟页面变为隐藏
            document.visibilityState = 'hidden';
            document.hidden = true;
            
            // 触发visibilitychange事件
            const visibilityChangeEvent = new Event('visibilitychange');
            document.dispatchEvent(visibilityChangeEvent);

            // 快进时间，应该不会执行刷新
            jest.advanceTimersByTime(20000);
            expect(refreshCallback).not.toHaveBeenCalled();
        });

        test('页面重新可见时应该恢复刷新', () => {
            const refreshCallback = jest.fn();
            autoRefreshManager.onRefresh(refreshCallback);
            autoRefreshManager.start();

            // 先隐藏页面
            document.visibilityState = 'hidden';
            document.hidden = true;
            document.dispatchEvent(new Event('visibilitychange'));

            // 然后重新显示页面
            document.visibilityState = 'visible';
            document.hidden = false;
            document.dispatchEvent(new Event('visibilitychange'));

            // 快进时间，应该恢复刷新
            jest.advanceTimersByTime(10000);
            expect(refreshCallback).toHaveBeenCalledTimes(1);
        });
    });

    describe('状态指示器测试', () => {
        test('应该正确显示刷新状态', () => {
            const statusElement = document.getElementById('refreshStatus');
            
            // 测试空闲状态
            autoRefreshManager.setStatus('idle');
            expect(statusElement.textContent).toBe('空闲');

            // 测试刷新中状态
            autoRefreshManager.setStatus('refreshing');
            expect(statusElement.textContent).toBe('刷新中...');

            // 测试成功状态
            autoRefreshManager.setStatus('success');
            expect(statusElement.textContent).toBe('刷新成功');

            // 测试错误状态
            autoRefreshManager.setStatus('error');
            expect(statusElement.textContent).toBe('刷新失败');
        });

        test('应该更新最后更新时间', () => {
            const lastUpdateElement = document.getElementById('lastUpdateTime');
            const testTime = new Date('2025-01-01 12:00:00');
            
            autoRefreshManager.updateLastUpdateTime(testTime);
            expect(lastUpdateElement.textContent).toContain('最后更新');
            expect(lastUpdateElement.textContent).toContain('12:00:00');
        });
    });

    describe('错误处理和重试机制测试', () => {
        test('网络错误时应该进行重试', async () => {
            const refreshCallback = jest.fn()
                .mockRejectedValueOnce(new Error('网络错误'))
                .mockRejectedValueOnce(new Error('网络错误'))
                .mockResolvedValueOnce('成功');

            autoRefreshManager.onRefresh(refreshCallback);
            autoRefreshManager.start();

            // 触发第一次刷新（失败）
            jest.advanceTimersByTime(10000);
            await Promise.resolve(); // 等待异步操作

            // 应该进行重试
            expect(autoRefreshManager.getRetryCount()).toBe(1);

            // 快进重试延迟时间
            jest.advanceTimersByTime(2000);
            await Promise.resolve();

            expect(autoRefreshManager.getRetryCount()).toBe(2);
        });

        test('达到最大重试次数后应该停止重试', async () => {
            const refreshCallback = jest.fn().mockRejectedValue(new Error('持续错误'));
            autoRefreshManager.onRefresh(refreshCallback);
            autoRefreshManager.setMaxRetries(3);
            autoRefreshManager.start();

            // 触发多次失败
            for (let i = 0; i < 5; i++) {
                jest.advanceTimersByTime(10000);
                await Promise.resolve();
                jest.advanceTimersByTime(2000);
                await Promise.resolve();
            }

            // 应该停止在最大重试次数
            expect(autoRefreshManager.getRetryCount()).toBeLessThanOrEqual(3);
            expect(autoRefreshManager.isRunning()).toBe(false);
        });
    });

    describe('性能优化测试', () => {
        test('应该避免重复的定时器', () => {
            const setIntervalSpy = jest.spyOn(global, 'setInterval');
            
            autoRefreshManager.start();
            autoRefreshManager.start(); // 重复启动
            
            // 应该只创建一个定时器
            expect(setIntervalSpy).toHaveBeenCalledTimes(1);
        });

        test('应该正确清理定时器', () => {
            const clearIntervalSpy = jest.spyOn(global, 'clearInterval');
            
            autoRefreshManager.start();
            autoRefreshManager.stop();
            
            expect(clearIntervalSpy).toHaveBeenCalled();
        });

        test('应该在页面卸载时清理资源', () => {
            autoRefreshManager.start();
            
            // 模拟页面卸载
            const beforeUnloadEvent = new Event('beforeunload');
            window.dispatchEvent(beforeUnloadEvent);
            
            expect(autoRefreshManager.isRunning()).toBe(false);
        });
    });

    describe('与现有模块集成测试', () => {
        test('应该与权限控制集成', () => {
            // 模拟权限检查
            const mockPermissionCheck = jest.fn().mockReturnValue(true);
            autoRefreshManager.setPermissionCheck(mockPermissionCheck);
            
            autoRefreshManager.start();
            jest.advanceTimersByTime(10000);
            
            expect(mockPermissionCheck).toHaveBeenCalled();
        });

        test('应该与缓存监控模块集成', () => {
            const mockCacheMonitor = {
                updateData: jest.fn()
            };
            
            autoRefreshManager.setCacheMonitor(mockCacheMonitor);
            autoRefreshManager.start();
            
            jest.advanceTimersByTime(10000);
            
            expect(mockCacheMonitor.updateData).toHaveBeenCalled();
        });
    });
});

/**
 * 自动刷新管理器类（用于测试）
 */
class AutoRefreshManager {
    constructor(options = {}) {
        this.apiClient = options.apiClient;
        this.interval = options.defaultInterval || 10000;
        this.timer = null;
        this.isActive = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.refreshCallback = null;
        this.permissionCheck = null;
        this.cacheMonitor = null;
        
        this.initializeVisibilityHandling();
        this.initializeCleanup();
    }

    initializeVisibilityHandling() {
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.pause();
            } else {
                this.resume();
            }
        });
    }

    initializeCleanup() {
        window.addEventListener('beforeunload', () => {
            this.destroy();
        });
    }

    start() {
        if (this.timer) {
            return; // 避免重复启动
        }
        
        this.isActive = true;
        this.timer = setInterval(() => {
            this.executeRefresh();
        }, this.interval);
    }

    stop() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
        this.isActive = false;
    }

    pause() {
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    }

    resume() {
        if (this.isActive && !this.timer) {
            this.start();
        }
    }

    async executeRefresh() {
        if (this.permissionCheck && !this.permissionCheck()) {
            return;
        }

        try {
            this.setStatus('refreshing');
            
            if (this.refreshCallback) {
                await this.refreshCallback();
            }
            
            if (this.cacheMonitor) {
                this.cacheMonitor.updateData();
            }
            
            this.setStatus('success');
            this.retryCount = 0;
            this.updateLastUpdateTime(new Date());
        } catch (error) {
            this.handleError(error);
        }
    }

    handleError(error) {
        this.retryCount++;
        this.setStatus('error');
        
        if (this.retryCount >= this.maxRetries) {
            this.stop();
        }
    }

    setStatus(status) {
        const statusElement = document.getElementById('refreshStatus');
        if (statusElement) {
            const statusTexts = {
                idle: '空闲',
                refreshing: '刷新中...',
                success: '刷新成功',
                error: '刷新失败'
            };
            statusElement.textContent = statusTexts[status] || status;
        }
    }

    updateLastUpdateTime(time) {
        const element = document.getElementById('lastUpdateTime');
        if (element) {
            element.textContent = `最后更新: ${time.toLocaleTimeString()}`;
        }
    }

    // Getter和Setter方法
    isRunning() { return this.isActive && this.timer !== null; }
    setInterval(interval) { this.interval = interval; }
    getInterval() { return this.interval; }
    getRetryCount() { return this.retryCount; }
    setMaxRetries(max) { this.maxRetries = max; }
    onRefresh(callback) { this.refreshCallback = callback; }
    setPermissionCheck(check) { this.permissionCheck = check; }
    setCacheMonitor(monitor) { this.cacheMonitor = monitor; }

    destroy() {
        this.stop();
        // 清理其他资源
    }
}
