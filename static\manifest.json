{"name": "Axum企业级任务管理系统", "short_name": "Axum任务", "description": "基于Rust Axum框架的企业级任务管理和实时聊天系统", "version": "1.0.0", "start_url": "/", "scope": "/", "display": "standalone", "orientation": "portrait-primary", "theme_color": "#3498db", "background_color": "#f8f9fa", "lang": "zh-CN", "dir": "ltr", "categories": ["productivity", "business", "utilities"], "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 192 192'><rect width='192' height='192' fill='%233498db' rx='24'/><text x='96' y='120' font-size='96' text-anchor='middle' fill='white'>🚀</text></svg>", "sizes": "192x192", "type": "image/svg+xml", "purpose": "any maskable"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 512 512'><rect width='512' height='512' fill='%233498db' rx='64'/><text x='256' y='320' font-size='256' text-anchor='middle' fill='white'>🚀</text></svg>", "sizes": "512x512", "type": "image/svg+xml", "purpose": "any maskable"}], "screenshots": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1280 720'><rect width='1280' height='720' fill='%23f8f9fa'/><rect x='40' y='40' width='1200' height='640' fill='white' stroke='%23e1e8ed' stroke-width='2' rx='8'/><text x='640' y='360' font-size='48' text-anchor='middle' fill='%232c3e50'>Axum任务管理系统</text></svg>", "sizes": "1280x720", "type": "image/svg+xml", "form_factor": "wide", "label": "桌面版主界面"}, {"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 390 844'><rect width='390' height='844' fill='%23f8f9fa'/><rect x='20' y='60' width='350' height='724' fill='white' stroke='%23e1e8ed' stroke-width='2' rx='8'/><text x='195' y='422' font-size='24' text-anchor='middle' fill='%232c3e50'>移动版界面</text></svg>", "sizes": "390x844", "type": "image/svg+xml", "form_factor": "narrow", "label": "移动版主界面"}], "shortcuts": [{"name": "创建任务", "short_name": "新任务", "description": "快速创建新任务", "url": "/?action=create-task", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><rect width='96' height='96' fill='%232ecc71' rx='12'/><text x='48' y='60' font-size='48' text-anchor='middle' fill='white'>+</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "聊天室", "short_name": "聊天", "description": "进入实时聊天室", "url": "/?action=open-chat", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><rect width='96' height='96' fill='%23f39c12' rx='12'/><text x='48' y='60' font-size='48' text-anchor='middle' fill='white'>💬</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}, {"name": "系统状态", "short_name": "状态", "description": "查看系统运行状态", "url": "/?action=system-status", "icons": [{"src": "data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 96 96'><rect width='96' height='96' fill='%239b59b6' rx='12'/><text x='48' y='60' font-size='48' text-anchor='middle' fill='white'>📊</text></svg>", "sizes": "96x96", "type": "image/svg+xml"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "focus-existing"}, "protocol_handlers": [{"protocol": "web+axum-task", "url": "/?task=%s"}], "file_handlers": [{"action": "/", "accept": {"application/json": [".json"], "text/csv": [".csv"]}}]}