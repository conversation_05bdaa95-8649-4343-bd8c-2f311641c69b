<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>查询优化管理 - Axum企业级应用</title>
    
    <!-- DNS预解析和资源预加载 -->
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="/css/modules.css">
    
    <style>
        /* 查询优化专用样式 */
        .query-optimization-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }

        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .panel {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
        }

        .panel-title {
            font-size: 1.4em;
            font-weight: 600;
            margin-bottom: 20px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .query-input-section {
            grid-column: 1 / -1;
        }

        .sql-textarea {
            width: 100%;
            min-height: 150px;
            padding: 15px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .sql-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .button-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .results-section {
            grid-column: 1 / -1;
            margin-top: 30px;
        }

        .results-tabs {
            display: flex;
            border-bottom: 2px solid #e1e5e9;
            margin-bottom: 20px;
        }

        .tab-button {
            padding: 12px 24px;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .optimization-result {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .metric-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }

        .metric-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .metric-value {
            font-size: 1.2em;
            color: #667eea;
            font-weight: 500;
        }

        .recommendation-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #28a745;
        }

        .recommendation-title {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .recommendation-description {
            color: #6c757d;
            line-height: 1.5;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid transparent;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .alert-info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-grid {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                justify-content: center;
            }
            
            .results-tabs {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="query-optimization-container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🔍 查询优化管理</h1>
            <p>智能SQL查询分析与性能优化建议系统</p>
        </div>

        <!-- 主要内容网格 -->
        <div class="main-grid">
            <!-- SQL查询输入区域 -->
            <div class="panel query-input-section">
                <div class="panel-title">
                    📝 SQL查询输入
                </div>
                
                <textarea
                    id="sqlQueryInput"
                    class="sql-textarea"
                    placeholder="请输入您要优化的SQL查询语句...&#10;&#10;示例：&#10;SELECT u.id, u.username, p.title &#10;FROM users u &#10;JOIN posts p ON u.id = p.user_id &#10;WHERE u.created_at > '2024-01-01' &#10;ORDER BY p.created_at DESC;"
                >SELECT * FROM tasks WHERE completed = false ORDER BY created_at DESC LIMIT 10</textarea>
                
                <div class="button-group">
                    <button id="optimizeQueryBtn" class="btn btn-primary">
                        🚀 优化查询
                    </button>
                    <button id="batchOptimizeBtn" class="btn btn-secondary">
                        📊 批量优化
                    </button>
                    <button id="clearQueryBtn" class="btn btn-secondary">
                        🗑️ 清空
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据库统计和索引推荐 -->
        <div class="main-grid">
            <div class="panel">
                <div class="panel-title">
                    📊 数据库统计
                </div>
                <div id="databaseStatsContent">
                    <div class="alert alert-info">
                        点击"获取统计信息"按钮加载数据库性能统计
                    </div>
                </div>
                <div class="button-group">
                    <button id="getDatabaseStatsBtn" class="btn btn-success">
                        📈 获取统计信息
                    </button>
                </div>
            </div>

            <div class="panel">
                <div class="panel-title">
                    💡 索引推荐
                </div>
                <div id="indexRecommendationsContent">
                    <div class="alert alert-info">
                        点击"获取推荐"按钮加载索引优化建议
                    </div>
                </div>
                <div class="button-group">
                    <button id="getIndexRecommendationsBtn" class="btn btn-success">
                        🔍 获取推荐
                    </button>
                </div>
            </div>
        </div>

        <!-- 结果展示区域 -->
        <div class="panel results-section">
            <div class="panel-title">
                📋 优化结果
            </div>
            
            <div class="results-tabs">
                <button class="tab-button active" data-tab="optimization">优化分析</button>
                <button class="tab-button" data-tab="performance">性能指标</button>
                <button class="tab-button" data-tab="recommendations">优化建议</button>
            </div>

            <div id="optimizationTab" class="tab-content active">
                <div class="alert alert-info">
                    请输入SQL查询并点击"优化查询"按钮开始分析
                </div>
            </div>

            <div id="performanceTab" class="tab-content">
                <div class="alert alert-info">
                    优化完成后，这里将显示详细的性能指标对比
                </div>
            </div>

            <div id="recommendationsTab" class="tab-content">
                <div class="alert alert-info">
                    优化完成后，这里将显示具体的优化建议和实施方案
                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <div id="loadingText">正在分析查询，请稍候...</div>
        </div>
    </div>

    <!-- 引入JavaScript模块 -->
    <script type="module">
        // 导入查询优化API模块
        import { queryOptimizationAPI } from '/js/modules/query-optimization-api.js';
        import { isAuthenticated } from '/js/modules/auth.js';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('🚀 查询优化管理页面初始化');
            
            // 检查用户认证状态
            if (!isAuthenticated()) {
                console.warn('⚠️ 用户未登录，重定向到登录页面');
                window.location.href = '/login.html';
                return;
            }

            // 初始化页面功能
            initializeQueryOptimization();
            
            // 暴露API到全局作用域供测试使用
            window.queryOptimizationAPI = queryOptimizationAPI;
            console.log('✅ 查询优化API已暴露到全局作用域');
        });

        // 初始化查询优化功能
        function initializeQueryOptimization() {
            // 获取DOM元素
            const elements = {
                sqlQueryInput: document.getElementById('sqlQueryInput'),
                optimizeQueryBtn: document.getElementById('optimizeQueryBtn'),
                batchOptimizeBtn: document.getElementById('batchOptimizeBtn'),
                clearQueryBtn: document.getElementById('clearQueryBtn'),
                getDatabaseStatsBtn: document.getElementById('getDatabaseStatsBtn'),
                getIndexRecommendationsBtn: document.getElementById('getIndexRecommendationsBtn'),
                loadingOverlay: document.getElementById('loadingOverlay'),
                loadingText: document.getElementById('loadingText'),
                databaseStatsContent: document.getElementById('databaseStatsContent'),
                indexRecommendationsContent: document.getElementById('indexRecommendationsContent'),
                optimizationTab: document.getElementById('optimizationTab'),
                performanceTab: document.getElementById('performanceTab'),
                recommendationsTab: document.getElementById('recommendationsTab')
            };

            // 绑定事件监听器
            elements.optimizeQueryBtn.addEventListener('click', () => handleOptimizeQuery(elements));
            elements.batchOptimizeBtn.addEventListener('click', () => handleBatchOptimize(elements));
            elements.clearQueryBtn.addEventListener('click', () => handleClearQuery(elements));
            elements.getDatabaseStatsBtn.addEventListener('click', () => handleGetDatabaseStats(elements));
            elements.getIndexRecommendationsBtn.addEventListener('click', () => handleGetIndexRecommendations(elements));

            // 标签页切换
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', (e) => {
                    const tabName = e.target.dataset.tab;
                    switchTab(tabName);
                });
            });

            console.log('✅ 查询优化功能初始化完成');
        }

        // 处理单个查询优化
        async function handleOptimizeQuery(elements) {
            const sqlQuery = elements.sqlQueryInput.value.trim();
            
            if (!sqlQuery) {
                showAlert('请输入SQL查询语句', 'error');
                return;
            }

            showLoading('正在分析查询，请稍候...');
            elements.optimizeQueryBtn.disabled = true;

            try {
                const result = await queryOptimizationAPI.optimizeQuery({
                    query_sql: sqlQuery,
                    apply_recommendations: false
                });

                displayOptimizationResult(result);
                showAlert('查询优化分析完成！', 'success');
                
            } catch (error) {
                console.error('❌ 查询优化失败:', error);
                showAlert(`查询优化失败: ${error.message}`, 'error');
            } finally {
                hideLoading();
                elements.optimizeQueryBtn.disabled = false;
            }
        }

        // 处理批量查询优化
        async function handleBatchOptimize(elements) {
            const sqlQuery = elements.sqlQueryInput.value.trim();
            
            if (!sqlQuery) {
                showAlert('请输入SQL查询语句', 'error');
                return;
            }

            // 将输入的查询按分号分割为多个查询
            const queries = sqlQuery.split(';')
                .map(q => q.trim())
                .filter(q => q.length > 0);

            if (queries.length === 0) {
                showAlert('未找到有效的SQL查询语句', 'error');
                return;
            }

            showLoading(`正在批量分析${queries.length}个查询，请稍候...`);
            elements.batchOptimizeBtn.disabled = true;

            try {
                const result = await queryOptimizationAPI.batchOptimize(queries);
                displayBatchOptimizationResult(result);
                showAlert(`批量查询优化完成！处理了${queries.length}个查询`, 'success');
                
            } catch (error) {
                console.error('❌ 批量查询优化失败:', error);
                showAlert(`批量查询优化失败: ${error.message}`, 'error');
            } finally {
                hideLoading();
                elements.batchOptimizeBtn.disabled = false;
            }
        }

        // 处理清空查询
        function handleClearQuery(elements) {
            elements.sqlQueryInput.value = '';
            elements.optimizationTab.innerHTML = '<div class="alert alert-info">请输入SQL查询并点击"优化查询"按钮开始分析</div>';
            elements.performanceTab.innerHTML = '<div class="alert alert-info">优化完成后，这里将显示详细的性能指标对比</div>';
            elements.recommendationsTab.innerHTML = '<div class="alert alert-info">优化完成后，这里将显示具体的优化建议和实施方案</div>';
            showAlert('查询内容已清空', 'info');
        }

        // 处理获取数据库统计信息
        async function handleGetDatabaseStats(elements) {
            showLoading('正在获取数据库统计信息...');
            elements.getDatabaseStatsBtn.disabled = true;

            try {
                const result = await queryOptimizationAPI.getDatabaseStats();
                displayDatabaseStats(result, elements.databaseStatsContent);
                showAlert('数据库统计信息获取成功！', 'success');
                
            } catch (error) {
                console.error('❌ 获取数据库统计信息失败:', error);
                elements.databaseStatsContent.innerHTML = `<div class="alert alert-error">获取失败: ${error.message}</div>`;
                showAlert(`获取数据库统计信息失败: ${error.message}`, 'error');
            } finally {
                hideLoading();
                elements.getDatabaseStatsBtn.disabled = false;
            }
        }

        // 处理获取索引推荐
        async function handleGetIndexRecommendations(elements) {
            showLoading('正在获取索引推荐建议...');
            elements.getIndexRecommendationsBtn.disabled = true;

            try {
                const result = await queryOptimizationAPI.getIndexRecommendations();
                displayIndexRecommendations(result, elements.indexRecommendationsContent);
                showAlert('索引推荐建议获取成功！', 'success');
                
            } catch (error) {
                console.error('❌ 获取索引推荐失败:', error);
                elements.indexRecommendationsContent.innerHTML = `<div class="alert alert-error">获取失败: ${error.message}</div>`;
                showAlert(`获取索引推荐失败: ${error.message}`, 'error');
            } finally {
                hideLoading();
                elements.getIndexRecommendationsBtn.disabled = false;
            }
        }

        // 显示优化结果
        function displayOptimizationResult(result) {
            console.log('📊 显示优化结果:', result);

            const optimizationTab = document.getElementById('optimizationTab');
            const performanceTab = document.getElementById('performanceTab');
            const recommendationsTab = document.getElementById('recommendationsTab');

            if (!optimizationTab || !performanceTab || !recommendationsTab) {
                console.error('❌ 找不到结果容器元素');
                return;
            }

            // 构建结果HTML
            let html = '<div class="optimization-result-content">';

            // 显示摘要信息
            if (result.summary) {
                html += `
                    <div class="result-section">
                        <h4>📋 优化摘要</h4>
                        <div class="summary-content">${result.summary}</div>
                    </div>
                `;
            }

            // 显示性能指标
            if (result.performance_metrics) {
                const metrics = result.performance_metrics;
                html += `
                    <div class="result-section">
                        <h4>📊 性能指标</h4>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <span class="metric-label">执行时间:</span>
                                <span class="metric-value">${metrics.execution_time_ms || 'N/A'}ms</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">扫描行数:</span>
                                <span class="metric-value">${metrics.rows_examined || 'N/A'}</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">返回行数:</span>
                                <span class="metric-value">${metrics.rows_returned || 'N/A'}</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">索引使用:</span>
                                <span class="metric-value ${metrics.index_used ? 'success' : 'warning'}">
                                    ${metrics.index_used ? '✅ 是' : '⚠️ 否'}
                                </span>
                            </div>
                        </div>
                    </div>
                `;
            }

            // 显示优化策略
            if (result.optimization_strategies && result.optimization_strategies.length > 0) {
                html += `
                    <div class="result-section">
                        <h4>💡 优化建议</h4>
                        <div class="strategies-list">
                `;

                result.optimization_strategies.forEach((strategy, index) => {
                    html += `
                        <div class="strategy-item">
                            <div class="strategy-header">
                                <span class="strategy-type">${strategy.strategy_type || '通用优化'}</span>
                                <span class="strategy-impact ${strategy.impact_level || 'medium'}">${strategy.impact_level || 'medium'}</span>
                            </div>
                            <div class="strategy-description">${strategy.description || '暂无描述'}</div>
                            ${strategy.suggested_sql ? `<div class="strategy-sql"><strong>建议SQL:</strong><pre><code>${strategy.suggested_sql}</code></pre></div>` : ''}
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            html += '</div>';

            // 更新优化分析标签页
            optimizationTab.innerHTML = html;
        }

        // 显示批量优化结果
        function displayBatchOptimizationResult(result) {
            console.log('📊 显示批量优化结果:', result);

            const optimizationTab = document.getElementById('optimizationTab');
            if (!optimizationTab) {
                console.error('❌ 找不到结果容器元素');
                return;
            }

            // 构建批量结果HTML
            let html = '<div class="batch-optimization-result-content">';

            // 显示批量摘要
            if (result.summary) {
                html += `
                    <div class="result-section">
                        <h4>📋 批量优化摘要</h4>
                        <div class="summary-content">${result.summary}</div>
                    </div>
                `;
            }

            // 显示每个查询的结果
            if (result.results && result.results.length > 0) {
                html += `
                    <div class="result-section">
                        <h4>📊 查询优化结果 (${result.results.length}个查询)</h4>
                        <div class="batch-results-list">
                `;

                result.results.forEach((queryResult, index) => {
                    const metrics = queryResult.performance_metrics || {};
                    html += `
                        <div class="batch-query-item">
                            <div class="query-header">
                                <h5>查询 ${index + 1}</h5>
                                <span class="query-status ${queryResult.success ? 'success' : 'error'}">
                                    ${queryResult.success ? '✅ 成功' : '❌ 失败'}
                                </span>
                            </div>
                            <div class="query-sql">
                                <strong>SQL:</strong>
                                <pre><code>${queryResult.query_sql || '未知查询'}</code></pre>
                            </div>
                            <div class="query-metrics">
                                <div class="metric-item">
                                    <span class="metric-label">执行时间:</span>
                                    <span class="metric-value">${metrics.execution_time_ms || 'N/A'}ms</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">扫描行数:</span>
                                    <span class="metric-value">${metrics.rows_examined || 'N/A'}</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">索引使用:</span>
                                    <span class="metric-value ${metrics.index_used ? 'success' : 'warning'}">
                                        ${metrics.index_used ? '✅ 是' : '⚠️ 否'}
                                    </span>
                                </div>
                            </div>
                            ${queryResult.optimization_strategies && queryResult.optimization_strategies.length > 0 ? `
                                <div class="query-strategies">
                                    <strong>优化建议:</strong>
                                    <ul>
                                        ${queryResult.optimization_strategies.map(strategy =>
                                            `<li>${strategy.description || '暂无描述'}</li>`
                                        ).join('')}
                                    </ul>
                                </div>
                            ` : ''}
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            html += '</div>';

            // 更新优化分析标签页
            optimizationTab.innerHTML = html;
        }

        // 格式化字节大小的辅助函数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 计算慢查询比例的辅助函数
        function getSlowQueryRatio(stats) {
            if (!stats.total_queries || stats.total_queries === 0) return 0;
            return ((stats.slow_queries || 0) / stats.total_queries) * 100;
        }

        // 格式化详细信息键名的辅助函数
        function formatDetailKey(key) {
            const keyMap = {
                'cache_efficiency': '缓存效率',
                'query_performance': '查询性能'
            };
            return keyMap[key] || key;
        }

        // 格式化详细信息值的辅助函数
        function formatDetailValue(value) {
            if (typeof value === 'object') {
                return JSON.stringify(value, null, 2);
            }
            return String(value);
        }

        // 将辅助函数暴露到全局作用域（用于调试和兼容性）
        window.formatBytes = formatBytes;
        window.getSlowQueryRatio = getSlowQueryRatio;
        window.formatDetailKey = formatDetailKey;
        window.formatDetailValue = formatDetailValue;

        // 显示数据库统计信息
        function displayDatabaseStats(response, container) {
            console.log('📊 显示数据库统计响应:', response);

            if (!container) {
                console.error('❌ 找不到数据库统计容器元素');
                return;
            }

            // 从响应中提取stats数据
            const stats = response.stats || response;
            console.log('📊 解析后的统计数据:', stats);

            // 构建统计信息HTML（不需要额外的wrapper div）
            let html = '';

            // 显示基础数据库信息
            html += `
                <div class="stats-section">
                    <h5>💾 数据库基础信息</h5>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">数据库大小:</span>
                            <span class="stat-value">${formatBytes(stats.database_size_bytes || 0)}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">活跃连接数:</span>
                            <span class="stat-value">${stats.active_connections || 0}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">缓存命中率:</span>
                            <span class="stat-value success">${(stats.cache_hit_ratio || 0).toFixed(2)}%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">统计时间:</span>
                            <span class="stat-value">${response.collected_at ? new Date(response.collected_at).toLocaleString() : '未知'}</span>
                        </div>
                    </div>
                </div>
            `;

            // 显示查询统计
            html += `
                <div class="stats-section">
                    <h5>📊 查询统计</h5>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">总查询数:</span>
                            <span class="stat-value">${stats.total_queries || 0}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">慢查询数:</span>
                            <span class="stat-value ${(stats.slow_queries || 0) > 0 ? 'warning' : ''}">${stats.slow_queries || 0}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">慢查询比例:</span>
                            <span class="stat-value ${getSlowQueryRatio(stats) > 10 ? 'warning' : ''}">${getSlowQueryRatio(stats).toFixed(2)}%</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">优化策略数:</span>
                            <span class="stat-value">${stats.slow_queries || 0}</span>
                        </div>
                    </div>
                </div>
            `;

            // 显示详细信息（如果有）
            if (response.details) {
                html += `
                    <div class="stats-section">
                        <h5>🔍 详细信息</h5>
                        <div class="details-content">
                `;

                Object.entries(response.details).forEach(([key, value]) => {
                    html += `
                        <div class="detail-item">
                            <strong>${formatDetailKey(key)}:</strong>
                            <span>${formatDetailValue(value)}</span>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            }

            // 更新容器内容
            container.innerHTML = html;
        }

        // 将displayDatabaseStats函数暴露到全局作用域（用于调试和兼容性）
        window.displayDatabaseStats = displayDatabaseStats;

        // 显示索引推荐
        function displayIndexRecommendations(recommendations, container) {
            console.log('💡 显示索引推荐:', recommendations);

            if (!container) {
                console.error('❌ 找不到索引推荐容器元素');
                return;
            }

            // 构建推荐HTML
            let html = '<div class="index-recommendations-content">';

            if (recommendations && recommendations.length > 0) {
                html += `
                    <div class="recommendations-section">
                        <h5>💡 索引优化建议 (${recommendations.length}个)</h5>
                        <div class="recommendations-list">
                `;

                recommendations.forEach((recommendation, index) => {
                    html += `
                        <div class="recommendation-item">
                            <div class="recommendation-header">
                                <span class="recommendation-priority ${recommendation.priority || 'medium'}">
                                    ${recommendation.priority === 'high' ? '🔴 高优先级' :
                                      recommendation.priority === 'medium' ? '🟡 中优先级' : '🟢 低优先级'}
                                </span>
                                <span class="recommendation-type">${recommendation.index_type || '复合索引'}</span>
                            </div>
                            <div class="recommendation-details">
                                <div class="recommendation-table">
                                    <strong>表名:</strong> ${recommendation.table_name || '未知表'}
                                </div>
                                <div class="recommendation-columns">
                                    <strong>建议索引列:</strong> ${recommendation.columns ? recommendation.columns.join(', ') : '未指定'}
                                </div>
                                <div class="recommendation-reason">
                                    <strong>原因:</strong> ${recommendation.reason || '提升查询性能'}
                                </div>
                                ${recommendation.estimated_improvement ? `
                                    <div class="recommendation-improvement">
                                        <strong>预期提升:</strong>
                                        <span class="improvement-value">${recommendation.estimated_improvement}</span>
                                    </div>
                                ` : ''}
                                ${recommendation.create_sql ? `
                                    <div class="recommendation-sql">
                                        <strong>创建SQL:</strong>
                                        <pre><code>${recommendation.create_sql}</code></pre>
                                        <button class="copy-sql-btn" onclick="copyToClipboard('${recommendation.create_sql.replace(/'/g, "\\'")}')">
                                            📋 复制SQL
                                        </button>
                                    </div>
                                ` : ''}
                            </div>
                        </div>
                    `;
                });

                html += `
                        </div>
                    </div>
                `;
            } else {
                html += `
                    <div class="no-recommendations">
                        <div class="no-recommendations-icon">✅</div>
                        <div class="no-recommendations-text">
                            <h5>当前无索引优化建议</h5>
                            <p>您的数据库索引配置良好，暂时无需优化。</p>
                        </div>
                    </div>
                `;
            }

            html += '</div>';

            // 更新容器内容
            container.innerHTML = html;
        }

        // 复制SQL到剪贴板的工具函数
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showAlert('SQL已复制到剪贴板！', 'success');
            }).catch(err => {
                console.error('复制失败:', err);
                showAlert('复制失败，请手动复制', 'error');
            });
        }

        // 工具函数
        function showLoading(text) {
            document.getElementById('loadingText').textContent = text;
            document.getElementById('loadingOverlay').style.display = 'flex';
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }

        function showAlert(message, type) {
            // 简单的提示实现，可以根据需要扩展
            console.log(`${type.toUpperCase()}: ${message}`);
        }

        function switchTab(tabName) {
            // 切换标签页
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(`${tabName}Tab`).classList.add('active');
        }
    </script>
</body>
</html>
