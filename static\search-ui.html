<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级消息搜索 - Axum企业级聊天应用</title>
    <link rel="stylesheet" href="/css/modules.css">
    <style>
        /* 高级搜索界面专用样式 */
        .search-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .search-modal.active {
            display: flex;
        }

        .search-container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .search-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 16px;
            border-bottom: 2px solid #e9ecef;
        }

        .search-title {
            font-size: 24px;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }

        .close-button {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #6c757d;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .close-button:hover {
            background: #f8f9fa;
            color: #dc3545;
        }

        .search-form {
            display: grid;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }

        .form-label {
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }

        .form-input, .form-select {
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s ease;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .quick-options {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
            margin-top: 8px;
        }

        .quick-option {
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .quick-option:hover {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .search-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            padding-top: 16px;
            border-top: 1px solid #e9ecef;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-outline {
            background: transparent;
            color: #6c757d;
            border: 2px solid #dee2e6;
        }

        .btn-outline:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .search-results {
            margin-top: 24px;
            display: none;
        }

        .search-results.active {
            display: block;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .results-count {
            font-weight: 500;
            color: #495057;
        }

        .sort-options {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .results-list {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }

        .result-item {
            padding: 16px;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.2s ease;
        }

        .result-item:hover {
            background: #f8f9fa;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .result-content {
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .result-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #6c757d;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 16px;
        }

        .page-btn {
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .page-btn:hover {
            background: #f8f9fa;
        }

        .page-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .loading-indicator {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .loading-indicator.active {
            display: block;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 12px 16px;
            border-radius: 8px;
            margin-top: 16px;
            display: none;
        }

        .error-message.active {
            display: block;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .search-container {
                width: 95%;
                padding: 16px;
                margin: 10px;
            }

            .search-title {
                font-size: 20px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .search-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .results-header {
                flex-direction: column;
                gap: 12px;
                align-items: stretch;
            }

            .sort-options {
                justify-content: center;
            }
        }

        /* 无障碍访问支持 */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* 键盘导航支持 */
        .form-input:focus,
        .form-select:focus,
        .btn:focus,
        .quick-option:focus {
            outline: 2px solid #007bff;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <!-- 高级搜索模态框 -->
    <div id="advancedSearchModal" class="search-modal mobile-responsive" role="dialog" aria-labelledby="searchTitle" aria-modal="true">
        <div class="search-container">
            <!-- 搜索头部 -->
            <div class="search-header">
                <h2 id="searchTitle" class="search-title">高级消息搜索</h2>
                <button id="closeSearchModal" class="close-button" aria-label="关闭搜索">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <!-- 搜索表单 -->
            <form id="advancedSearchForm" class="search-form" role="search">
                <!-- 搜索关键词 -->
                <div class="form-group">
                    <label for="searchKeyword" class="form-label">搜索关键词</label>
                    <input 
                        type="text" 
                        id="searchKeyword" 
                        class="form-input" 
                        placeholder="输入要搜索的消息内容..."
                        maxlength="100"
                        required
                        aria-describedby="keywordHelp"
                    >
                    <small id="keywordHelp" class="form-text">支持模糊搜索，最多100个字符</small>
                </div>

                <!-- 过滤条件行 -->
                <div class="form-row">
                    <!-- 消息类型过滤 -->
                    <div class="form-group">
                        <label for="messageTypeFilter" class="form-label">消息类型</label>
                        <select id="messageTypeFilter" class="form-select" aria-describedby="typeHelp">
                            <option value="">全部类型</option>
                            <option value="text">文本消息</option>
                            <option value="image">图片消息</option>
                            <option value="file">文件消息</option>
                            <option value="video">视频消息</option>
                        </select>
                        <small id="typeHelp" class="form-text">按消息类型筛选结果</small>
                    </div>

                    <!-- 发送者过滤 -->
                    <div class="form-group">
                        <label for="senderFilter" class="form-label">发送者</label>
                        <input 
                            type="text" 
                            id="senderFilter" 
                            class="form-input" 
                            placeholder="输入发送者用户名..."
                            aria-describedby="senderHelp"
                        >
                        <small id="senderHelp" class="form-text">按发送者筛选消息</small>
                    </div>
                </div>

                <!-- 时间范围选择 -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="startDatePicker" class="form-label">开始时间</label>
                        <input 
                            type="date" 
                            id="startDatePicker" 
                            class="form-input"
                            aria-describedby="startDateHelp"
                        >
                        <small id="startDateHelp" class="form-text">搜索范围的开始时间</small>
                    </div>

                    <div class="form-group">
                        <label for="endDatePicker" class="form-label">结束时间</label>
                        <input 
                            type="date" 
                            id="endDatePicker" 
                            class="form-input"
                            aria-describedby="endDateHelp"
                        >
                        <small id="endDateHelp" class="form-text">搜索范围的结束时间</small>
                    </div>
                </div>

                <!-- 快捷时间选项 -->
                <div id="quickSearchOptions" class="form-group">
                    <label class="form-label">快捷时间范围</label>
                    <div class="quick-options">
                        <button type="button" class="quick-option" data-quick-range="today" tabindex="0">今天</button>
                        <button type="button" class="quick-option" data-quick-range="yesterday" tabindex="0">昨天</button>
                        <button type="button" class="quick-option" data-quick-range="week" tabindex="0">本周</button>
                        <button type="button" class="quick-option" data-quick-range="month" tabindex="0">本月</button>
                    </div>
                </div>

                <!-- 错误提示 -->
                <div id="dateRangeError" class="error-message" role="alert">
                    结束日期不能早于开始日期
                </div>

                <!-- 搜索操作按钮 -->
                <div class="search-actions">
                    <button type="button" id="clearFiltersButton" class="btn btn-outline">
                        <span aria-hidden="true">🗑️</span>
                        清除筛选
                    </button>
                    <button type="submit" id="searchButton" class="btn btn-primary">
                        <span aria-hidden="true">🔍</span>
                        开始搜索
                    </button>
                </div>
            </form>

            <!-- 加载指示器 -->
            <div id="searchLoadingIndicator" class="loading-indicator">
                <div class="spinner" aria-hidden="true"></div>
                <p>正在搜索消息...</p>
            </div>

            <!-- 搜索结果区域 -->
            <div id="searchResults" class="search-results">
                <!-- 结果头部 -->
                <div id="searchResultsHeader" class="results-header">
                    <div id="resultsCount" class="results-count">
                        找到 0 条匹配的消息
                    </div>
                    <div class="sort-options">
                        <label for="sortOptions" class="form-label">排序方式:</label>
                        <select id="sortOptions" class="form-select">
                            <option value="date_desc">时间倒序</option>
                            <option value="date_asc">时间正序</option>
                            <option value="relevance">相关性</option>
                        </select>
                    </div>
                </div>

                <!-- 结果列表 -->
                <div id="searchResultsList" class="results-list">
                    <!-- 搜索结果将通过JavaScript动态填充 -->
                </div>

                <!-- 分页控件 -->
                <div id="searchPagination" class="pagination">
                    <button id="prevPageButton" class="page-btn" disabled>上一页</button>
                    <span id="pageInfo" class="page-info">第 1 页，共 1 页</span>
                    <button id="nextPageButton" class="page-btn" disabled>下一页</button>
                </div>
            </div>

            <!-- 搜索历史 -->
            <div id="searchHistory" class="search-history" style="display: none;">
                <h3>搜索历史</h3>
                <div id="historyList" class="history-list">
                    <!-- 搜索历史将通过JavaScript动态填充 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 主页面触发按钮（用于测试） -->
    <div style="padding: 20px;">
        <button id="openAdvancedSearch" class="btn btn-primary">
            打开高级搜索
        </button>
        <div id="authStatus" style="margin-top: 10px; color: #28a745;">
            已登录: testuser456
        </div>
    </div>

    <!-- 引入JavaScript模块 -->
    <script type="module" src="/js/modules/api.js"></script>
    <script type="module" src="/js/modules/auth.js"></script>
    <script type="module" src="/js/modules/advanced-search-ui.js"></script>
    <script type="module">
        // 初始化高级搜索UI
        import { initAdvancedSearchUI } from '/js/modules/advanced-search-ui.js';

        document.addEventListener('DOMContentLoaded', async () => {
            const searchUI = initAdvancedSearchUI();
            console.log('高级搜索UI已初始化:', searchUI);

            // 暴露API到全局作用域供测试使用
            window.chatAPI = (await import('/js/modules/api.js')).chatAPI;
            window.authAPI = (await import('/js/modules/api.js')).authAPI;
            window.taskAPI = (await import('/js/modules/api.js')).taskAPI;
        });
    </script>
</body>
</html>
