/**
 * Service Worker - 前端性能优化缓存策略
 * 基于2025年最新PWA和缓存最佳实践
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025-07-28
 */

// ==== 缓存配置 ====
const CACHE_CONFIG = {
    version: '1.0.0',
    caches: {
        static: 'axum-static-v1.0.0',
        dynamic: 'axum-dynamic-v1.0.0',
        api: 'axum-api-v1.0.0',
        images: 'axum-images-v1.0.0'
    },
    maxEntries: {
        dynamic: 50,
        api: 100,
        images: 30
    },
    maxAge: {
        static: 30 * 24 * 60 * 60 * 1000,    // 30天
        dynamic: 7 * 24 * 60 * 60 * 1000,    // 7天
        api: 5 * 60 * 1000,                  // 5分钟
        images: 30 * 24 * 60 * 60 * 1000     // 30天
    }
};

// ==== 静态资源列表 ====
const STATIC_ASSETS = [
    '/',
    '/index.html',
    '/css/modules.css',
    '/js/app.js',
    '/js/modules/api.js',
    '/js/modules/auth.js',
    '/js/modules/websocket.js',
    '/js/modules/tasks.js',
    '/js/modules/ui.js',
    '/js/modules/performance-optimizer.js'
];

// ==== API路径模式 ====
const API_PATTERNS = [
    /^\/api\//,
    /^\/health/,
    /^\/metrics/
];

// ==== 图片文件模式 ====
const IMAGE_PATTERNS = [
    /\.(?:png|jpg|jpeg|svg|gif|webp|avif)$/i
];

// ==== Service Worker事件处理 ====

/**
 * 安装事件 - 预缓存静态资源
 */
self.addEventListener('install', event => {
    console.log('🔧 Service Worker 安装中...');
    
    event.waitUntil(
        caches.open(CACHE_CONFIG.caches.static)
            .then(cache => {
                console.log('📦 预缓存静态资源...');
                return cache.addAll(STATIC_ASSETS);
            })
            .then(() => {
                console.log('✅ 静态资源预缓存完成');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ 静态资源预缓存失败:', error);
            })
    );
});

/**
 * 激活事件 - 清理旧缓存
 */
self.addEventListener('activate', event => {
    console.log('🚀 Service Worker 激活中...');
    
    event.waitUntil(
        Promise.all([
            // 清理旧版本缓存
            cleanupOldCaches(),
            // 立即控制所有客户端
            self.clients.claim()
        ]).then(() => {
            console.log('✅ Service Worker 激活完成');
        })
    );
});

/**
 * 网络请求拦截 - 实现缓存策略
 */
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // 只处理同源请求
    if (url.origin !== location.origin) {
        return;
    }
    
    // 根据请求类型选择缓存策略
    if (isStaticAsset(request)) {
        event.respondWith(handleStaticAsset(request));
    } else if (isAPIRequest(request)) {
        event.respondWith(handleAPIRequest(request));
    } else if (isImageRequest(request)) {
        event.respondWith(handleImageRequest(request));
    } else {
        event.respondWith(handleDynamicRequest(request));
    }
});

/**
 * 消息事件 - 处理来自主线程的消息
 */
self.addEventListener('message', event => {
    const { type, payload } = event.data;
    
    switch (type) {
        case 'SKIP_WAITING':
            self.skipWaiting();
            break;
            
        case 'CACHE_URLS':
            cacheUrls(payload.urls);
            break;
            
        case 'CLEAR_CACHE':
            clearCache(payload.cacheName);
            break;
            
        case 'GET_CACHE_INFO':
            getCacheInfo().then(info => {
                event.ports[0].postMessage(info);
            });
            break;
            
        default:
            console.warn('未知消息类型:', type);
    }
});

// ==== 缓存策略实现 ====

/**
 * 处理静态资源 - Cache First策略
 */
async function handleStaticAsset(request) {
    try {
        const cache = await caches.open(CACHE_CONFIG.caches.static);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            // 后台更新缓存
            updateCacheInBackground(request, cache);
            return cachedResponse;
        }
        
        // 缓存未命中，从网络获取
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.error('静态资源处理失败:', error);
        return new Response('资源不可用', { status: 503 });
    }
}

/**
 * 处理API请求 - Network First策略
 */
async function handleAPIRequest(request) {
    try {
        const cache = await caches.open(CACHE_CONFIG.caches.api);
        
        // 尝试网络请求
        try {
            const networkResponse = await fetch(request);
            
            if (networkResponse.ok) {
                // 只缓存GET请求的成功响应
                if (request.method === 'GET') {
                    const responseToCache = networkResponse.clone();
                    await cache.put(request, responseToCache);
                }
            }
            
            return networkResponse;
            
        } catch (networkError) {
            // 网络失败，尝试从缓存获取
            const cachedResponse = await cache.match(request);
            
            if (cachedResponse) {
                console.log('🔄 使用缓存的API响应:', request.url);
                return cachedResponse;
            }
            
            throw networkError;
        }
        
    } catch (error) {
        console.error('API请求处理失败:', error);
        return new Response(
            JSON.stringify({ error: 'API不可用' }), 
            { 
                status: 503,
                headers: { 'Content-Type': 'application/json' }
            }
        );
    }
}

/**
 * 处理图片请求 - Cache First策略
 */
async function handleImageRequest(request) {
    try {
        const cache = await caches.open(CACHE_CONFIG.caches.images);
        const cachedResponse = await cache.match(request);
        
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            // 限制缓存大小
            await limitCacheSize(cache, CACHE_CONFIG.maxEntries.images);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
        
    } catch (error) {
        console.error('图片请求处理失败:', error);
        // 返回占位图片或透明图片
        return new Response(
            new Uint8Array([
                0x47, 0x49, 0x46, 0x38, 0x39, 0x61, 0x01, 0x00,
                0x01, 0x00, 0x80, 0x00, 0x00, 0xFF, 0xFF, 0xFF,
                0x00, 0x00, 0x00, 0x21, 0xF9, 0x04, 0x01, 0x00,
                0x00, 0x00, 0x00, 0x2C, 0x00, 0x00, 0x00, 0x00,
                0x01, 0x00, 0x01, 0x00, 0x00, 0x02, 0x02, 0x04,
                0x01, 0x00, 0x3B
            ]),
            { headers: { 'Content-Type': 'image/gif' } }
        );
    }
}

/**
 * 处理动态请求 - Stale While Revalidate策略
 */
async function handleDynamicRequest(request) {
    try {
        const cache = await caches.open(CACHE_CONFIG.caches.dynamic);
        const cachedResponse = await cache.match(request);
        
        // 后台更新
        const fetchPromise = fetch(request).then(response => {
            if (response.ok) {
                cache.put(request, response.clone());
            }
            return response;
        });
        
        // 如果有缓存，立即返回缓存，同时后台更新
        if (cachedResponse) {
            fetchPromise.catch(() => {}); // 忽略后台更新错误
            return cachedResponse;
        }
        
        // 没有缓存，等待网络响应
        return await fetchPromise;
        
    } catch (error) {
        console.error('动态请求处理失败:', error);
        return new Response('页面不可用', { status: 503 });
    }
}

// ==== 工具函数 ====

/**
 * 判断是否为静态资源
 */
function isStaticAsset(request) {
    const url = new URL(request.url);
    return STATIC_ASSETS.some(asset => url.pathname === asset) ||
           url.pathname.match(/\.(js|css|html)$/);
}

/**
 * 判断是否为API请求
 */
function isAPIRequest(request) {
    const url = new URL(request.url);
    return API_PATTERNS.some(pattern => pattern.test(url.pathname));
}

/**
 * 判断是否为图片请求
 */
function isImageRequest(request) {
    const url = new URL(request.url);
    return IMAGE_PATTERNS.some(pattern => pattern.test(url.pathname));
}

/**
 * 后台更新缓存
 */
async function updateCacheInBackground(request, cache) {
    try {
        const response = await fetch(request);
        if (response.ok) {
            await cache.put(request, response);
        }
    } catch (error) {
        console.warn('后台缓存更新失败:', error);
    }
}

/**
 * 限制缓存大小
 */
async function limitCacheSize(cache, maxEntries) {
    const keys = await cache.keys();
    
    if (keys.length >= maxEntries) {
        // 删除最旧的条目
        const entriesToDelete = keys.slice(0, keys.length - maxEntries + 1);
        await Promise.all(entriesToDelete.map(key => cache.delete(key)));
    }
}

/**
 * 清理旧版本缓存
 */
async function cleanupOldCaches() {
    const cacheNames = await caches.keys();
    const currentCaches = Object.values(CACHE_CONFIG.caches);
    
    const oldCaches = cacheNames.filter(name => 
        name.startsWith('axum-') && !currentCaches.includes(name)
    );
    
    await Promise.all(oldCaches.map(name => caches.delete(name)));
    
    if (oldCaches.length > 0) {
        console.log(`🗑️ 已清理 ${oldCaches.length} 个旧缓存`);
    }
}

/**
 * 缓存指定URL列表
 */
async function cacheUrls(urls) {
    try {
        const cache = await caches.open(CACHE_CONFIG.caches.dynamic);
        await cache.addAll(urls);
        console.log(`📦 已缓存 ${urls.length} 个URL`);
    } catch (error) {
        console.error('URL缓存失败:', error);
    }
}

/**
 * 清理指定缓存
 */
async function clearCache(cacheName) {
    try {
        const deleted = await caches.delete(cacheName);
        console.log(`🗑️ 缓存清理${deleted ? '成功' : '失败'}: ${cacheName}`);
    } catch (error) {
        console.error('缓存清理失败:', error);
    }
}

/**
 * 获取缓存信息
 */
async function getCacheInfo() {
    try {
        const cacheNames = await caches.keys();
        const info = {};
        
        for (const name of cacheNames) {
            const cache = await caches.open(name);
            const keys = await cache.keys();
            info[name] = {
                count: keys.length,
                urls: keys.map(request => request.url)
            };
        }
        
        return info;
    } catch (error) {
        console.error('获取缓存信息失败:', error);
        return {};
    }
}

// ==== 性能监控 ====

/**
 * 监控缓存性能
 */
function monitorCachePerformance() {
    // 定期清理过期缓存
    setInterval(async () => {
        try {
            await cleanupExpiredCache();
        } catch (error) {
            console.error('定期缓存清理失败:', error);
        }
    }, 60 * 60 * 1000); // 每小时执行一次
}

/**
 * 清理过期缓存
 */
async function cleanupExpiredCache() {
    const now = Date.now();
    
    for (const [cacheType, cacheName] of Object.entries(CACHE_CONFIG.caches)) {
        try {
            const cache = await caches.open(cacheName);
            const keys = await cache.keys();
            
            for (const request of keys) {
                const response = await cache.match(request);
                if (response) {
                    const dateHeader = response.headers.get('date');
                    if (dateHeader) {
                        const cacheDate = new Date(dateHeader).getTime();
                        const maxAge = CACHE_CONFIG.maxAge[cacheType];
                        
                        if (now - cacheDate > maxAge) {
                            await cache.delete(request);
                        }
                    }
                }
            }
        } catch (error) {
            console.error(`清理缓存失败 ${cacheName}:`, error);
        }
    }
}

// 启动性能监控
monitorCachePerformance();
