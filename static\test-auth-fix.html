<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>认证API修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 认证API修复测试</h1>
        <p>测试用户注册和登录功能，验证前端与后端的响应格式兼容性。</p>

        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="testuser_fix" placeholder="输入用户名">
        </div>

        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="password123" placeholder="输入密码">
        </div>

        <div class="form-group">
            <button onclick="testRegister()">🆕 测试注册</button>
            <button onclick="testLogin()">🔑 测试登录</button>
            <button onclick="testAuthStatus()">📊 检查认证状态</button>
            <button onclick="clearResults()">🗑️ 清除结果</button>
        </div>

        <div id="results"></div>
    </div>

    <script type="module">
        import { authAPI } from './js/modules/api.js';
        import { 
            setAuthState, 
            clearAuthState, 
            isAuthenticated, 
            getCurrentUser, 
            getAuthToken 
        } from './js/modules/auth.js';

        // 全局函数供按钮调用
        window.testRegister = async function() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'error');
                return;
            }

            try {
                showResult('正在注册用户...', 'info');
                
                const response = await authAPI.register(username, password);
                
                showResult(`注册成功！\n响应数据:\n${JSON.stringify(response, null, 2)}`, 'success');
                
                // 尝试提取token和用户信息
                let authToken = null;
                let userInfo = null;

                if (response.success && response.data && response.data.access_token && response.data.user) {
                    authToken = response.data.access_token;
                    userInfo = response.data.user;
                } else if (response.success && response.data && response.data.token && response.data.user) {
                    authToken = response.data.token;
                    userInfo = response.data.user;
                } else if (response.token && response.user) {
                    authToken = response.token;
                    userInfo = response.user;
                } else if (response.access_token && response.user) {
                    authToken = response.access_token;
                    userInfo = response.user;
                }

                if (authToken && userInfo) {
                    setAuthState(authToken, userInfo);
                    showResult(`认证状态已设置\nToken: ${authToken.substring(0, 20)}...\n用户: ${userInfo.username}`, 'success');
                } else {
                    showResult('警告：无法从响应中提取token或用户信息', 'error');
                }

            } catch (error) {
                showResult(`注册失败：${error.message}\n详细信息：${JSON.stringify(error, null, 2)}`, 'error');
            }
        };

        window.testLogin = async function() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showResult('请输入用户名和密码', 'error');
                return;
            }

            try {
                showResult('正在登录...', 'info');
                
                const response = await authAPI.login(username, password);
                
                showResult(`登录成功！\n响应数据:\n${JSON.stringify(response, null, 2)}`, 'success');
                
                // 尝试提取token和用户信息
                let authToken = null;
                let userInfo = null;

                if (response.success && response.data && response.data.access_token && response.data.user) {
                    authToken = response.data.access_token;
                    userInfo = response.data.user;
                } else if (response.success && response.data && response.data.token && response.data.user) {
                    authToken = response.data.token;
                    userInfo = response.data.user;
                } else if (response.token && response.user) {
                    authToken = response.token;
                    userInfo = response.user;
                } else if (response.access_token && response.user) {
                    authToken = response.access_token;
                    userInfo = response.user;
                }

                if (authToken && userInfo) {
                    setAuthState(authToken, userInfo);
                    showResult(`认证状态已设置\nToken: ${authToken.substring(0, 20)}...\n用户: ${userInfo.username}`, 'success');
                } else {
                    showResult('警告：无法从响应中提取token或用户信息', 'error');
                }

            } catch (error) {
                showResult(`登录失败：${error.message}\n详细信息：${JSON.stringify(error, null, 2)}`, 'error');
            }
        };

        window.testAuthStatus = function() {
            const isAuth = isAuthenticated();
            const user = getCurrentUser();
            const token = getAuthToken();
            
            const status = {
                isAuthenticated: isAuth,
                currentUser: user,
                authToken: token ? `${token.substring(0, 20)}...` : null,
                tokenLength: token ? token.length : 0
            };
            
            showResult(`认证状态:\n${JSON.stringify(status, null, 2)}`, isAuth ? 'success' : 'info');
        };

        window.clearResults = function() {
            document.getElementById('results').innerHTML = '';
        };

        function showResult(message, type) {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // 页面加载时显示当前认证状态
        window.addEventListener('load', () => {
            window.testAuthStatus();
        });
    </script>
</body>
</html>
