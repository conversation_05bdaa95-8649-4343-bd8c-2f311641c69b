<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>按钮状态管理测试 - Axum企业级任务管理系统</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        
        button {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            margin: 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        button:hover:not(:disabled) {
            background: linear-gradient(135deg, #2980b9 0%, #1f5f8b 100%);
            transform: translateY(-2px);
        }
        
        button:disabled {
            background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
            cursor: not-allowed;
            transform: none;
            opacity: 0.7;
        }
        
        button.loading {
            position: relative;
            color: transparent;
        }
        
        button.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
        
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }
        
        .form-test {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .form-test input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>🔧 按钮状态管理测试页面</h1>
    <p>此页面用于测试修复后的按钮状态管理功能，确保按钮不会卡在"处理中..."状态。</p>

    <!-- 基础按钮状态测试 -->
    <div class="test-section">
        <h2>1. 基础按钮状态测试</h2>
        <p>测试按钮状态管理器的基本功能：设置加载状态、恢复状态、防止重复操作。</p>
        
        <div class="test-buttons">
            <button id="testBtn1">测试按钮 1</button>
            <button id="testBtn2">测试按钮 2</button>
            <button id="testBtn3">测试按钮 3</button>
        </div>
        
        <div class="test-buttons">
            <button onclick="testBasicLoading()">设置加载状态</button>
            <button onclick="testRestore()">恢复状态</button>
            <button onclick="testDuplicateOperation()">测试重复操作</button>
            <button onclick="clearAllStates()">清除所有状态</button>
        </div>
        
        <div id="basicTestStatus" class="status info">等待测试...</div>
    </div>

    <!-- 异步操作测试 -->
    <div class="test-section">
        <h2>2. 异步操作测试</h2>
        <p>测试safeButtonOperation包装器，模拟真实的异步操作场景。</p>
        
        <div class="test-buttons">
            <button id="asyncBtn1">异步成功测试</button>
            <button id="asyncBtn2">异步失败测试</button>
            <button id="asyncBtn3">长时间操作测试</button>
        </div>
        
        <div class="test-buttons">
            <button onclick="testAsyncSuccess()">测试异步成功</button>
            <button onclick="testAsyncFailure()">测试异步失败</button>
            <button onclick="testLongOperation()">测试长时间操作</button>
        </div>
        
        <div id="asyncTestStatus" class="status info">等待测试...</div>
    </div>

    <!-- 表单提交测试 -->
    <div class="test-section">
        <h2>3. 表单提交测试</h2>
        <p>测试表单提交时的按钮状态管理，模拟登录/注册场景。</p>
        
        <div class="form-test">
            <form id="testForm">
                <input type="text" placeholder="用户名" required>
                <input type="password" placeholder="密码" required>
                <button type="submit">提交表单</button>
            </form>
        </div>
        
        <div id="formTestStatus" class="status info">等待测试...</div>
    </div>

    <!-- 调试信息 -->
    <div class="test-section">
        <h2>4. 调试信息</h2>
        <p>实时显示按钮状态管理器的内部状态。</p>
        
        <button onclick="updateDebugInfo()">刷新调试信息</button>
        <button onclick="setInterval(updateDebugInfo, 1000)">自动刷新</button>
        
        <div id="debugInfo" class="debug-info">点击"刷新调试信息"查看状态...</div>
    </div>

    <!-- 导入模块和测试脚本 -->
    <script type="module">
        import { 
            buttonStateManager, 
            setButtonLoading, 
            restoreButton, 
            isButtonProcessing, 
            safeButtonOperation 
        } from './js/modules/ui.js';

        // 将函数暴露到全局作用域供HTML调用
        window.buttonStateManager = buttonStateManager;
        window.setButtonLoading = setButtonLoading;
        window.restoreButton = restoreButton;
        window.isButtonProcessing = isButtonProcessing;
        window.safeButtonOperation = safeButtonOperation;

        // 测试函数
        window.testBasicLoading = function() {
            const btn1 = document.getElementById('testBtn1');
            const btn2 = document.getElementById('testBtn2');
            const btn3 = document.getElementById('testBtn3');
            
            const success1 = setButtonLoading(btn1, '加载中...');
            const success2 = setButtonLoading(btn2, '处理中...');
            const success3 = setButtonLoading(btn3, '等待中...');
            
            document.getElementById('basicTestStatus').innerHTML = 
                `<span class="status ${success1 && success2 && success3 ? 'success' : 'error'}">
                    按钮1: ${success1 ? '成功' : '失败'}, 
                    按钮2: ${success2 ? '成功' : '失败'}, 
                    按钮3: ${success3 ? '成功' : '失败'}
                </span>`;
        };

        window.testRestore = function() {
            const btn1 = document.getElementById('testBtn1');
            const btn2 = document.getElementById('testBtn2');
            const btn3 = document.getElementById('testBtn3');
            
            restoreButton(btn1);
            restoreButton(btn2, '自定义文本');
            restoreButton(btn3);
            
            document.getElementById('basicTestStatus').innerHTML = 
                '<span class="status success">所有按钮状态已恢复</span>';
        };

        window.testDuplicateOperation = function() {
            const btn1 = document.getElementById('testBtn1');
            
            const first = setButtonLoading(btn1, '第一次设置');
            const second = setButtonLoading(btn1, '第二次设置');
            
            document.getElementById('basicTestStatus').innerHTML = 
                `<span class="status ${!second ? 'success' : 'error'}">
                    第一次设置: ${first ? '成功' : '失败'}, 
                    第二次设置: ${second ? '成功(错误!)' : '被拒绝(正确!)'}
                </span>`;
        };

        window.clearAllStates = function() {
            buttonStateManager.clearAllStates();
            document.getElementById('basicTestStatus').innerHTML = 
                '<span class="status info">所有按钮状态已强制清除</span>';
        };

        window.testAsyncSuccess = async function() {
            const btn = document.getElementById('asyncBtn1');
            
            try {
                await safeButtonOperation(
                    btn,
                    () => new Promise(resolve => setTimeout(() => resolve('成功'), 2000)),
                    '异步处理中...',
                    '操作成功',
                    null
                );
                
                document.getElementById('asyncTestStatus').innerHTML = 
                    '<span class="status success">异步操作成功完成</span>';
            } catch (error) {
                document.getElementById('asyncTestStatus').innerHTML = 
                    `<span class="status error">异步操作失败: ${error.message}</span>`;
            }
        };

        window.testAsyncFailure = async function() {
            const btn = document.getElementById('asyncBtn2');
            
            try {
                await safeButtonOperation(
                    btn,
                    () => new Promise((_, reject) => setTimeout(() => reject(new Error('模拟错误')), 1500)),
                    '异步处理中...',
                    null,
                    '操作失败'
                );
            } catch (error) {
                document.getElementById('asyncTestStatus').innerHTML = 
                    `<span class="status success">异步错误处理正确: ${error.message}</span>`;
            }
        };

        window.testLongOperation = async function() {
            const btn = document.getElementById('asyncBtn3');
            
            try {
                await safeButtonOperation(
                    btn,
                    () => new Promise(resolve => setTimeout(() => resolve('长时间操作完成'), 5000)),
                    '长时间处理中...',
                    '完成',
                    null
                );
                
                document.getElementById('asyncTestStatus').innerHTML = 
                    '<span class="status success">长时间操作成功完成</span>';
            } catch (error) {
                document.getElementById('asyncTestStatus').innerHTML = 
                    `<span class="status error">长时间操作失败: ${error.message}</span>`;
            }
        };

        window.updateDebugInfo = function() {
            const debugInfo = buttonStateManager.getDebugInfo();
            document.getElementById('debugInfo').innerHTML = `
                <strong>按钮状态管理器调试信息:</strong><br>
                总按钮数: ${debugInfo.totalButtons}<br>
                处理中按钮数: ${debugInfo.processingButtons}<br>
                操作计数器: ${debugInfo.operationCounter}<br>
                更新时间: ${new Date().toLocaleTimeString()}
            `;
        };

        // 表单提交测试
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = this.querySelector('button[type="submit"]');
            
            try {
                await safeButtonOperation(
                    submitBtn,
                    () => new Promise(resolve => setTimeout(() => resolve('表单提交成功'), 2000)),
                    '提交中...',
                    '提交表单',
                    null
                );
                
                document.getElementById('formTestStatus').innerHTML = 
                    '<span class="status success">表单提交测试成功</span>';
            } catch (error) {
                document.getElementById('formTestStatus').innerHTML = 
                    `<span class="status error">表单提交测试失败: ${error.message}</span>`;
            }
        });

        // 页面加载完成后显示初始调试信息
        window.addEventListener('load', function() {
            updateDebugInfo();
        });
    </script>
</body>
</html>
