<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户API测试 - GET /api/users/{id}</title>
    <link rel="stylesheet" href="/css/user-ui.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9fafb;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .test-controls {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            color: #374151;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .test-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-top: 16px;
        }
        
        .status-bar {
            background: #f3f4f6;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-family: monospace;
            font-size: 12px;
        }
        
        .user-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            min-height: 200px;
        }
        
        .test-scenarios {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .scenario-card {
            background: white;
            padding: 16px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .scenario-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
        }
        
        .scenario-description {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 12px;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .test-buttons {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>用户API测试 - GET /api/users/{id}</h1>
            <p>测试用户详情获取接口的完整功能，包括参数验证、缓存机制、错误处理和重试逻辑。</p>
        </div>
        
        <div class="test-controls">
            <h2>测试控制面板</h2>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="userId">用户ID (UUID格式)</label>
                    <input type="text" id="userId" placeholder="例如: 123e4567-e89b-12d3-a456-************">
                </div>
                
                <div class="form-group">
                    <label for="authToken">认证令牌</label>
                    <input type="text" id="authToken" placeholder="JWT令牌 (可选，用于测试认证)">
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="cacheEnabled">缓存设置</label>
                    <select id="cacheEnabled">
                        <option value="true">启用缓存</option>
                        <option value="false">禁用缓存</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="retryEnabled">重试设置</label>
                    <select id="retryEnabled">
                        <option value="true">启用重试</option>
                        <option value="false">禁用重试</option>
                    </select>
                </div>
            </div>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="testGetUser()">获取用户信息</button>
                <button class="btn btn-secondary" onclick="clearUserCache()">清除缓存</button>
                <button class="btn btn-secondary" onclick="clearResults()">清除结果</button>
                <button class="btn btn-secondary" onclick="loginTestUser()">登录测试用户</button>
            </div>
        </div>
        
        <div class="status-bar" id="statusBar">
            状态: 就绪
        </div>
        
        <div class="user-container" id="userContainer">
            <div style="padding: 40px; text-align: center; color: #6b7280;">
                请输入用户ID并点击"获取用户信息"开始测试
            </div>
        </div>
        
        <div class="test-scenarios">
            <div class="scenario-card">
                <div class="scenario-title">测试场景 1: 正常用户</div>
                <div class="scenario-description">使用有效的用户ID获取用户信息</div>
                <button class="btn btn-primary" onclick="testValidUser()">测试</button>
            </div>
            
            <div class="scenario-card">
                <div class="scenario-title">测试场景 2: 用户不存在</div>
                <div class="scenario-description">使用不存在的用户ID测试404错误处理</div>
                <button class="btn btn-primary" onclick="testNonExistentUser()">测试</button>
            </div>
            
            <div class="scenario-card">
                <div class="scenario-title">测试场景 3: 无效UUID</div>
                <div class="scenario-description">使用无效的UUID格式测试参数验证</div>
                <button class="btn btn-primary" onclick="testInvalidUUID()">测试</button>
            </div>
            
            <div class="scenario-card">
                <div class="scenario-title">测试场景 4: 未认证访问</div>
                <div class="scenario-description">不提供认证令牌测试401错误处理</div>
                <button class="btn btn-primary" onclick="testUnauthorized()">测试</button>
            </div>
            
            <div class="scenario-card">
                <div class="scenario-title">测试场景 5: 缓存机制</div>
                <div class="scenario-description">测试缓存的存储和读取功能</div>
                <button class="btn btn-primary" onclick="testCaching()">测试</button>
            </div>
            
            <div class="scenario-card">
                <div class="scenario-title">测试场景 6: 并发请求</div>
                <div class="scenario-description">同时发送多个请求测试并发处理</div>
                <button class="btn btn-primary" onclick="testConcurrentRequests()">测试</button>
            </div>
        </div>
    </div>

    <script type="module">
        import { userAPI } from '/js/modules/api.js';
        import { userUI } from '/js/modules/user-ui.js';
        
        // 将函数添加到全局作用域
        window.userAPI = userAPI;
        window.userUI = userUI;
        
        // 测试函数
        window.testGetUser = async function() {
            const userId = document.getElementById('userId').value.trim();
            const authToken = document.getElementById('authToken').value.trim();
            const cacheEnabled = document.getElementById('cacheEnabled').value === 'true';
            const retryEnabled = document.getElementById('retryEnabled').value === 'true';
            
            if (!userId) {
                updateStatus('错误: 请输入用户ID');
                return;
            }
            
            updateStatus(`正在获取用户信息: ${userId}`);
            
            // 设置认证令牌
            if (authToken) {
                localStorage.setItem('authToken', authToken);
            }
            
            const container = document.getElementById('userContainer');
            await userUI.displayUser(userId, container, {
                showLoadingSpinner: true,
                enableRetry: retryEnabled,
                cacheEnabled: cacheEnabled
            });
            
            updateStatus('请求完成');
        };
        
        window.clearUserCache = function() {
            userAPI.clearCache();
            updateStatus('缓存已清除');
        };
        
        window.clearResults = function() {
            const container = document.getElementById('userContainer');
            container.innerHTML = `
                <div style="padding: 40px; text-align: center; color: #6b7280;">
                    请输入用户ID并点击"获取用户信息"开始测试
                </div>
            `;
            updateStatus('结果已清除');
        };
        
        window.loginTestUser = async function() {
            updateStatus('正在登录测试用户...');
            try {
                // 这里应该调用实际的登录API
                // 暂时模拟一个令牌
                const mockToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.test.token';
                document.getElementById('authToken').value = mockToken;
                localStorage.setItem('authToken', mockToken);
                updateStatus('测试用户登录成功');
            } catch (error) {
                updateStatus(`登录失败: ${error.message}`);
            }
        };
        
        // 测试场景函数
        window.testValidUser = function() {
            // 使用一个示例UUID
            document.getElementById('userId').value = '123e4567-e89b-12d3-a456-************';
            testGetUser();
        };
        
        window.testNonExistentUser = function() {
            // 使用一个不存在的UUID
            document.getElementById('userId').value = '00000000-0000-0000-0000-000000000000';
            testGetUser();
        };
        
        window.testInvalidUUID = function() {
            document.getElementById('userId').value = 'invalid-uuid-format';
            testGetUser();
        };
        
        window.testUnauthorized = function() {
            // 清除认证令牌
            document.getElementById('authToken').value = '';
            localStorage.removeItem('authToken');
            document.getElementById('userId').value = '123e4567-e89b-12d3-a456-************';
            testGetUser();
        };
        
        window.testCaching = async function() {
            const userId = '123e4567-e89b-12d3-a456-************';
            document.getElementById('userId').value = userId;
            document.getElementById('cacheEnabled').value = 'true';
            
            updateStatus('测试缓存机制 - 第一次请求');
            await testGetUser();
            
            setTimeout(async () => {
                updateStatus('测试缓存机制 - 第二次请求 (应该从缓存读取)');
                await testGetUser();
            }, 1000);
        };
        
        window.testConcurrentRequests = async function() {
            const userId = '123e4567-e89b-12d3-a456-************';
            document.getElementById('userId').value = userId;
            
            updateStatus('发送5个并发请求...');
            
            const promises = [];
            for (let i = 0; i < 5; i++) {
                promises.push(testGetUser());
            }
            
            try {
                await Promise.all(promises);
                updateStatus('所有并发请求完成');
            } catch (error) {
                updateStatus(`并发请求失败: ${error.message}`);
            }
        };
        
        function updateStatus(message) {
            const statusBar = document.getElementById('statusBar');
            const timestamp = new Date().toLocaleTimeString();
            statusBar.textContent = `[${timestamp}] ${message}`;
        }
        
        // 页面加载完成后的初始化
        updateStatus('页面加载完成，准备就绪');
    </script>
</body>
</html>
