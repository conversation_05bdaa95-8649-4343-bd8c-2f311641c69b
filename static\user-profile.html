<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户详情 - Axum企业级应用</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="/static/css/modules.css">
    <link rel="stylesheet" href="/static/css/user-ui.css">
    
    <!-- 图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* 页面特定样式 */
        .page-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8fafc;
            min-height: 100vh;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0 0 10px 0;
        }
        
        .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 300px;
            gap: 30px;
            align-items: start;
        }
        
        .main-content {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .user-container {
            padding: 0;
        }
        
        .quick-actions {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .quick-actions h3 {
            margin: 0 0 15px 0;
            font-size: 1.2rem;
            color: #374151;
        }
        
        .action-button {
            display: flex;
            align-items: center;
            gap: 10px;
            width: 100%;
            padding: 12px 16px;
            margin-bottom: 8px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s ease;
            cursor: pointer;
            font-size: 14px;
        }
        
        .action-button:hover {
            background: #3b82f6;
            color: white;
            border-color: #3b82f6;
            transform: translateY(-1px);
        }
        
        .action-button i {
            width: 16px;
            text-align: center;
        }
        
        .user-stats {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .user-stats h3 {
            margin: 0 0 15px 0;
            font-size: 1.2rem;
            color: #374151;
        }
        
        .stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .stat-item:last-child {
            border-bottom: none;
        }
        
        .stat-label {
            font-size: 14px;
            color: #6b7280;
        }
        
        .stat-value {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .loading-content {
            background: white;
            padding: 40px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .page-header {
                padding: 30px 15px;
            }
            
            .page-title {
                font-size: 2rem;
            }
            
            .sidebar {
                order: -1;
            }
        }
        
        /* 在线状态指示器增强 */
        .user-status.online {
            background-color: #10b981;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        /* 权限控制显示 */
        .permission-restricted {
            opacity: 0.5;
            pointer-events: none;
        }
        
        .permission-message {
            background: #fef3c7;
            border: 1px solid #fcd34d;
            color: #92400e;
            padding: 12px 16px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <!-- 页面容器 -->
    <div class="page-container">
        <!-- 页面头部 -->
        <header class="page-header">
            <h1 class="page-title">
                <i class="fas fa-user-circle"></i>
                用户详情页面
            </h1>
            <p class="page-subtitle">查看和管理用户信息</p>
        </header>
        
        <!-- 主要内容区域 -->
        <div class="content-grid">
            <!-- 主内容 -->
            <main class="main-content">
                <!-- 用户信息容器 -->
                <div id="user-container" class="user-container">
                    <!-- 用户信息将通过JavaScript动态加载 -->
                </div>
            </main>
            
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <!-- 快速操作 -->
                <div class="quick-actions">
                    <h3><i class="fas fa-bolt"></i> 快速操作</h3>
                    
                    <button class="action-button" onclick="refreshUserInfo()">
                        <i class="fas fa-sync-alt"></i>
                        刷新用户信息
                    </button>
                    
                    <button class="action-button" onclick="clearUserCache()">
                        <i class="fas fa-trash-alt"></i>
                        清除缓存
                    </button>
                    
                    <button class="action-button" onclick="exportUserData()" id="export-btn">
                        <i class="fas fa-download"></i>
                        导出用户数据
                    </button>
                    
                    <a href="/static/index.html" class="action-button">
                        <i class="fas fa-home"></i>
                        返回首页
                    </a>
                </div>
                
                <!-- 用户统计 -->
                <div class="user-stats">
                    <h3><i class="fas fa-chart-bar"></i> 用户统计</h3>
                    
                    <div class="stat-item">
                        <span class="stat-label">页面访问次数</span>
                        <span class="stat-value" id="visit-count">-</span>
                    </div>
                    
                    <div class="stat-item">
                        <span class="stat-label">最后访问时间</span>
                        <span class="stat-value" id="last-visit">-</span>
                    </div>
                    
                    <div class="stat-item">
                        <span class="stat-label">数据加载时间</span>
                        <span class="stat-value" id="load-time">-</span>
                    </div>
                    
                    <div class="stat-item">
                        <span class="stat-label">缓存状态</span>
                        <span class="stat-value" id="cache-status">-</span>
                    </div>
                </div>
            </aside>
        </div>
    </div>
    
    <!-- 加载遮罩 -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
            <p class="loading-text">正在加载用户信息...</p>
        </div>
    </div>
    
    <!-- JavaScript模块 -->
    <script type="module">
        // 导入必要的模块
        import { userAPI } from '/static/js/modules/api.js';
        import { userUI } from '/static/js/modules/user-ui.js';
        import { isAuthenticated, getAuthToken } from '/static/js/modules/auth.js';
        
        // 全局变量
        let currentUserId = null;
        let visitCount = 0;
        let loadStartTime = null;
        
        // 页面初始化
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('用户详情页面初始化开始');
            
            // 检查认证状态
            if (!isAuthenticated()) {
                showPermissionMessage('请先登录以查看用户详情');
                return;
            }
            
            // 从URL参数获取用户ID
            const urlParams = new URLSearchParams(window.location.search);
            const userId = urlParams.get('id');
            
            if (!userId) {
                showError('未指定用户ID');
                return;
            }
            
            // 设置当前用户ID
            currentUserId = userId;
            
            // 更新访问统计
            updateVisitStats();
            
            // 加载用户信息
            await loadUserProfile(userId);
        });
        
        // 加载用户资料
        async function loadUserProfile(userId) {
            const container = document.getElementById('user-container');
            const loadingOverlay = document.getElementById('loading-overlay');
            
            try {
                // 显示加载状态
                loadStartTime = Date.now();
                loadingOverlay.style.display = 'flex';
                
                // 使用userUI模块加载用户信息
                await userUI.displayUser(userId, container, {
                    showLoadingSpinner: false, // 我们使用自定义的加载遮罩
                    enableRetry: true,
                    cacheEnabled: true
                });
                
                // 更新加载时间统计
                const loadTime = Date.now() - loadStartTime;
                document.getElementById('load-time').textContent = `${loadTime}ms`;
                document.getElementById('cache-status').textContent = '已启用';
                
                console.log(`用户信息加载完成，耗时: ${loadTime}ms`);
                
            } catch (error) {
                console.error('加载用户信息失败:', error);
                showError('加载用户信息失败: ' + error.message);
            } finally {
                // 隐藏加载遮罩
                loadingOverlay.style.display = 'none';
            }
        }
        
        // 显示权限消息
        function showPermissionMessage(message) {
            const container = document.getElementById('user-container');
            container.innerHTML = `
                <div class="permission-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    ${message}
                </div>
            `;
            
            // 禁用需要权限的操作
            document.getElementById('export-btn').classList.add('permission-restricted');
        }
        
        // 显示错误信息
        function showError(message) {
            const container = document.getElementById('user-container');
            container.innerHTML = `
                <div class="user-error error-general">
                    <div class="error-icon">⚠️</div>
                    <h3 class="error-title">加载失败</h3>
                    <p class="error-message">${message}</p>
                    <div class="error-actions">
                        <button class="btn btn-primary" onclick="location.reload()">
                            重新加载
                        </button>
                        <a href="/static/index.html" class="btn btn-secondary">
                            返回首页
                        </a>
                    </div>
                </div>
            `;
        }
        
        // 更新访问统计
        function updateVisitStats() {
            visitCount++;
            document.getElementById('visit-count').textContent = visitCount;
            document.getElementById('last-visit').textContent = new Date().toLocaleString('zh-CN');
        }
        
        // 全局函数：刷新用户信息
        window.refreshUserInfo = async function() {
            if (!currentUserId) return;
            
            console.log('刷新用户信息');
            await loadUserProfile(currentUserId);
            updateVisitStats();
        };
        
        // 全局函数：清除用户缓存
        window.clearUserCache = function() {
            if (!currentUserId) return;
            
            userUI.clearUserCache(currentUserId);
            document.getElementById('cache-status').textContent = '已清除';
            console.log('用户缓存已清除');
        };
        
        // 全局函数：导出用户数据
        window.exportUserData = async function() {
            if (!currentUserId) return;
            
            try {
                const response = await userAPI.getUser(currentUserId);
                const userData = response.data;
                
                // 创建导出数据
                const exportData = {
                    exportTime: new Date().toISOString(),
                    userId: currentUserId,
                    userInfo: userData,
                    pageStats: {
                        visitCount: visitCount,
                        lastVisit: document.getElementById('last-visit').textContent,
                        loadTime: document.getElementById('load-time').textContent
                    }
                };
                
                // 下载JSON文件
                const blob = new Blob([JSON.stringify(exportData, null, 2)], {
                    type: 'application/json'
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `user-${currentUserId}-${Date.now()}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                userUI.showToast('用户数据导出成功', 'success');
                console.log('用户数据导出完成');
                
            } catch (error) {
                console.error('导出用户数据失败:', error);
                userUI.showToast('导出失败: ' + error.message, 'error');
            }
        };
    </script>
</body>
</html>
