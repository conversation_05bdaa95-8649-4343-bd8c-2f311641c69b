<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket实时监控面板 - Axum企业级应用</title>
    <link rel="stylesheet" href="/static/css/modules.css">
    <style>
        /* 监控面板专用样式 */
        .monitoring-dashboard {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .dashboard-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .dashboard-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }

        .dashboard-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .stat-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.2em;
            font-weight: 600;
        }

        .stat-value {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin: 10px 0;
        }

        .stat-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .chart-container {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .chart-placeholder {
            height: 300px;
            background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%),
                        linear-gradient(-45deg, #f8f9fa 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f8f9fa 75%),
                        linear-gradient(-45deg, transparent 75%, #f8f9fa 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 1.1em;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-offline { background-color: #dc3545; }

        .connection-list {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 5px;
        }

        .connection-item {
            padding: 10px 15px;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .connection-item:last-child {
            border-bottom: none;
        }

        .connection-info {
            flex: 1;
        }

        .connection-user {
            font-weight: 600;
            color: #333;
        }

        .connection-details {
            font-size: 0.85em;
            color: #666;
            margin-top: 2px;
        }

        .refresh-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: background-color 0.2s ease;
            margin: 10px 5px;
        }

        .refresh-button:hover {
            background: #5a6fd8;
        }

        .refresh-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .auto-refresh-controls {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .last-updated {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 10px;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border: 1px solid #f5c6cb;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .loading::after {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .monitoring-dashboard {
                padding: 10px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .dashboard-header h1 {
                font-size: 2em;
            }
            
            .stat-value {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="monitoring-dashboard">
        <!-- 面板标题 -->
        <div class="dashboard-header">
            <h1>WebSocket实时监控面板</h1>
            <p>实时监控WebSocket连接状态、消息统计和性能指标</p>
            <div style="margin-top: 15px;">
                <span id="connectionStatus" class="status-indicator status-warning">正在连接...</span>
                <span style="margin-left: 10px; color: rgba(255,255,255,0.8);">实时数据连接状态</span>
            </div>
        </div>

        <!-- 自动刷新控制 -->
        <div class="auto-refresh-controls">
            <button id="refreshBtn" class="refresh-button">立即刷新</button>
            <button id="autoRefreshBtn" class="refresh-button">开启自动刷新</button>
            <select id="refreshInterval">
                <option value="5000">5秒</option>
                <option value="10000" selected>10秒</option>
                <option value="30000">30秒</option>
                <option value="60000">1分钟</option>
            </select>
            <div class="last-updated" id="lastUpdated">最后更新: 从未</div>
        </div>

        <!-- 错误消息显示区域 -->
        <div id="errorMessage" class="error-message" style="display: none;"></div>

        <!-- 加载指示器 -->
        <div id="loadingIndicator" class="loading" style="display: none;">正在加载数据...</div>

        <!-- 统计卡片网格 -->
        <div class="stats-grid">
            <!-- 活跃连接数 -->
            <div class="stat-card">
                <h3><span class="status-indicator status-online"></span>活跃连接</h3>
                <div class="stat-value" id="activeConnections">-</div>
                <div class="stat-label">当前在线用户</div>
            </div>

            <!-- 总连接数 -->
            <div class="stat-card">
                <h3>总连接数</h3>
                <div class="stat-value" id="totalConnections">-</div>
                <div class="stat-label">累计连接次数</div>
            </div>

            <!-- 唯一用户数 -->
            <div class="stat-card">
                <h3>唯一用户</h3>
                <div class="stat-value" id="uniqueUsers">-</div>
                <div class="stat-label">不同用户数量</div>
            </div>

            <!-- 消息吞吐量 -->
            <div class="stat-card">
                <h3>消息吞吐量</h3>
                <div class="stat-value" id="messageThroughput">-</div>
                <div class="stat-label">消息/秒</div>
            </div>

            <!-- 平均延迟 -->
            <div class="stat-card">
                <h3>平均延迟</h3>
                <div class="stat-value" id="averageLatency">-</div>
                <div class="stat-label">毫秒</div>
            </div>

            <!-- 连接成功率 -->
            <div class="stat-card">
                <h3>连接成功率</h3>
                <div class="stat-value" id="connectionSuccessRate">-</div>
                <div class="stat-label">百分比</div>
            </div>
        </div>

        <!-- 图表容器 -->
        <div class="chart-container">
            <div class="chart-title">连接类型分布</div>
            <canvas id="connectionTypeChart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
            <div class="chart-title">消息类型统计</div>
            <canvas id="messageTypeChart" width="400" height="200"></canvas>
        </div>

        <div class="chart-container">
            <div class="chart-title">性能指标历史</div>
            <canvas id="performanceChart" width="400" height="200"></canvas>
        </div>

        <!-- 活跃连接列表 -->
        <div class="chart-container">
            <div class="chart-title">活跃连接详情</div>
            <div class="connection-list" id="activeConnectionsList">
                <div class="loading">正在加载连接信息...</div>
            </div>
        </div>
    </div>

    <!-- 引入Chart.js图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js"></script>

    <!-- 引入WebSocket监控JavaScript模块 -->
    <script type="module" src="/static/js/modules/websocket-monitoring.js"></script>
</body>
</html>
